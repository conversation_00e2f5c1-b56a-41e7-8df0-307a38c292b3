// GCodeEditor.cpp
#include "GCodeEditor.h"

#include <QDebug>
#include <QFile>
#include <QTextStream>

GCodeEditor::GCodeEditor(QWidget *parent) : QsciScintilla(parent) {
    m_lexer = new GCodeLexer(this);
    m_apis = new GCodeAPIs(m_lexer);

    setupEditor();
    setupAutoCompletion();
    setupCallTips();
}

void GCodeEditor::setupEditor() {
    setLexer(m_lexer);
    setUtf8(true);
    setAutoIndent(true);
    setIndentationGuides(false);
    setTabWidth(4);
    setEdgeColumn(80);
    setEdgeMode(QsciScintilla::EdgeLine);
    setMarginLineNumbers(0, true);
    setMarginWidth(0, 40);

    // G代码通常不需要折行
    setWrapMode(QsciScintilla::WrapNone);

    // 设置字体
    QFont font("Courier New", 10);
    setFont(font);
}

void GCodeEditor::setupAutoCompletion() {
    setAutoCompletionSource(QsciScintilla::AcsAPIs);
    setAutoCompletionCaseSensitivity(false);
    setAutoCompletionReplaceWord(true);
    setAutoCompletionShowSingle(true);
    setAutoCompletionThreshold(1);
    setAutoCompletionFillupsEnabled(true);
}

void GCodeEditor::setupCallTips() {
    setCallTipsStyle(QsciScintilla::CallTipsNoContext);
    setCallTipsVisible(0);

    connect(this, &QsciScintilla::charAdded, [this](int ch) {
        if (ch == ' ' || ch == '(') {
            int line, index;
            getCursorPosition(&line, &index);

            // 获取当前行的文本
            QString lineText = text(line);
            int startPos = qMax(0, index - 10);  // 向前查找10个字符
            QString context = lineText.mid(startPos, index - startPos);

            // 检查是否是G或M命令
            QRegularExpression commandRegex("[GM][0-9]+");
            QRegularExpressionMatch match = commandRegex.match(context);
            if (match.hasMatch()) {
                QString command = match.captured();
                QStringList tips =
                    m_apis->callTips(QStringList() << command, 0, QsciScintilla::CallTipsNoContext, QList<int>());
                if (!tips.isEmpty()) {
                    this->callTipShow(index, tips.first());
                }
            }
        } else if (ch == ')') {
            callTipCancel();
        }
    });
}

void GCodeEditor::loadGCodeFile(const QString &filename) {
    QFile file(filename);
    if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QTextStream in(&file);
        setText(in.readAll());
        file.close();
    }
}

void GCodeEditor::saveGCodeFile(const QString &filename) {
    QFile file(filename);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        out << text();
        file.close();
    }
}

//查找
void GCodeEditor::findNextTxt(QString findText) {
    bool ret = findFirst(findText, false, false, false, true);
    if (!ret) qDebug() << "无法找到文本" << findText;
}
