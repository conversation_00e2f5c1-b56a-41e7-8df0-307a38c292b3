// GCodeLexer.cpp
#include "GCodeLexer.h"
#include <QRegularExpression>

GCodeLexer::GCodeLexer(QObject *parent) 
    : QsciLexerCustom(parent) {
    // 初始化G代码命令
    m_gCommands = {
        "G00", "G01", "G02", "G03", "G04", "G17", "G18", "G19",
        "G20", "G21", "G28", "G40", "G41", "G42", "G43", "G49",
        "G54", "G55", "G56", "G57", "G58", "G59", "G80", "G81",
        "G90", "G91", "G94", "G98", "G99"
    };

    // 初始化M代码命令
    m_mCommands = {
        "M00", "M01", "M02", "M03", "M04", "M05", "M06", "M08",
        "M09", "M30", "M98", "M99"
    };

    m_mMacroCommands = {
        "IF", "WHILE", "DO", "END", "EQ", "NE", "GT", "GE", "LT", "LE"
    };

    // 轴运动字母
    m_axisLetters = {"X", "Y", "Z", "A", "B", "C", "U", "V", "W"};

    // 设置样式颜色
    setColor(QColor(0, 0, 255), 1);    // G命令 - 蓝色
    setColor(QColor(128, 0, 128), 2);  // M命令 - 紫色
    setColor(QColor(0, 128, 0), 3);    // 轴命令 - 绿色
    setColor(QColor(255, 0, 0), 4);    // 注释 - 红色
    setColor(QColor(0, 0, 0), 5);      // 数字 - 黑色
    setColor(QColor(255, 255, 0), 6);   // Macro命令 - 黄色
    setColor(QColor(170, 85, 0), 7);   // 其他字母 - 棕色
}

const char *GCodeLexer::language() const {
    return "G-Code";
}

QString GCodeLexer::description(int style) const {
    switch (style) {
        case 1: return "G Command";
        case 2: return "M Command";
        case 3: return "Axis Command";
        case 4: return "Comment";
        case 5: return "Number";
        case 6: return "Other Letter";
        case 7: return "Macro Command";
        default: return "Default";
    }
}

void GCodeLexer::styleText(int start, int end) {
    if (!editor()) return;

    QString text = editor()->text(start, end);
    QStringList lines = text.split('\n');

    int lineStart = start;
    for (const QString &line : lines) {
        styleLine(line, lineStart);
        lineStart += line.length() + 1;
    }
}

void GCodeLexer::styleLine(const QString &line, int startPos) {
    if (line.isEmpty()) return;

    static QRegularExpression commentRegex("\\(.*\\)|;.*$");
    static QRegularExpression wordRegex("[GM][0-9]+|\\b[A-Z]\\b|[-+]?\\d*\\.?\\d+");

    int pos = 0;
    startStyling(startPos);

    // 处理整行注释
    if (line.trimmed().startsWith("%") || line.trimmed().isEmpty()) {
        setStyling(line.length(), 0);
        return;
    }

    // 处理行内注释
    QRegularExpressionMatch commentMatch = commentRegex.match(line);
    if (commentMatch.hasMatch()) {
        int commentStart = commentMatch.capturedStart();
        if (pos < commentStart) {
            setStyling(commentStart - pos, 0);
            pos = commentStart;
        }
        setStyling(commentMatch.capturedLength(), 4);
        pos += commentMatch.capturedLength();
    }


    // 处理剩余部分
    QRegularExpressionMatchIterator wordIter = wordRegex.globalMatch(line, pos);
    while (wordIter.hasNext()) {
        QRegularExpressionMatch match = wordIter.next();

        // 设置匹配前的默认样式
        if (pos < match.capturedStart()) {
            setStyling(match.capturedStart() - pos, 0);
        }

        QString word = match.captured();
        int style = 0;

        if (isGCommand(word)) {
            style = 1;
        } else if (isMCommand(word)) {
            style = 2;
        } else if (isAxisCommand(word)) {
            style = 3;
        } else if (isNumber(word)) {
            style = 5;
        } else if (word.length() == 1 && word[0].isLetter()) {
            style = 6;
        } else if (isMacroCommand(word)) {
            style = 7;
        }

        if (macroCommands.contains(word)) {
            setStyling(match.capturedLength(), 8);  // 宏命令样式
            pos = match.capturedEnd();
            continue;
        }

        // 在GCodeLexer.cpp的styleLine函数中添加
        if (word.startsWith("O") && word.length() > 1 && word.mid(1).toInt() > 0) {
            setStyling(match.capturedLength(), 7);  // 程序号样式
            pos = match.capturedEnd();
            continue;
        }

        setStyling(match.capturedLength(), style);
        pos = match.capturedEnd();
    }

    // 设置行尾剩余部分
    if (pos < line.length()) {
        setStyling(line.length() - pos, 0);
    }
}

bool GCodeLexer::isGCommand(const QString &word) const {
    return m_gCommands.contains(word);
}

bool GCodeLexer::isMCommand(const QString &word) const {
    return m_mCommands.contains(word);
}

bool GCodeLexer::isAxisCommand(const QString &word) const {
    return word.length() == 1 && m_axisLetters.contains(word);
}

bool GCodeLexer::isMacroCommand(const QString &word) const {
    return m_mMacroCommands.contains(word);
}

bool GCodeLexer::isNumber(const QString &word) const {
    bool ok;
    word.toDouble(&ok);
    return ok;
}
