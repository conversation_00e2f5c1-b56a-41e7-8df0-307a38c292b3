// GCodeAPIs.h
#pragma once
#include <Qsci/qsciapis.h>
#include "GCodeLexer.h"

class GCodeAPIs : public QsciAPIs {
    Q_OBJECT
public:
    explicit GCodeAPIs(QsciLexer *lexer);

    void updateAutoCompletionList(const QStringList &context, QStringList &list) override;
    QStringList callTips(const QStringList &context, int commas, QsciScintilla::CallTipsStyle style, QList<int> &shifts) override;

private:
    void loadStandardCommands();
    //void loadMachineSpecificCommands(const QString &machineType);

    QMap<QString, QString> m_gCommands;
    QMap<QString, QString> m_mCommands;
    QMap<QString, QString> m_parameters;
};

