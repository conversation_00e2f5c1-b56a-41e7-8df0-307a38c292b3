// GCodeEditor.h
#pragma once
#include <Qsci/qsciscintilla.h>

#include "GCodeAPIs.h"
#include "GCodeLexer.h"

class GCodeEditor : public QsciScintilla {
    Q_OBJECT
   public:
    explicit GCodeEditor(QWidget *parent = nullptr);

    void loadGCodeFile(const QString &filename);
    void saveGCodeFile(const QString &filename);

    void findNextTxt();

   private:
    GCodeLexer *m_lexer;
    GCodeAPIs *m_apis;

    void setupEditor();
    void setupAutoCompletion();
    void setupCallTips();
};
