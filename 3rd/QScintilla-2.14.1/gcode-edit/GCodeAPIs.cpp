// GCodeAPIs.cpp
#include "GCodeAPIs.h"

GCodeAPIs::GCodeAPIs(QsciLexer *lexer)
    : QsciAPIs(lexer) {
    loadStandardCommands();
    QString type = "SIEMENS";
    //loadMachineSpecificCommands(type);
    prepare();
}

//void GCodeAPIs::loadMachineSpecificCommands(const QString &machineType) {
//    if (machineType == "FANUC") {
//        m_gCommands.insert("G10", "可编程数据输入");
//        m_gCommands.insert("G52", "局部坐标系");
//        m_mCommands.insert("M198", "子程序调用");
//    } else if (machineType == "SIEMENS") {
//        m_gCommands.insert("G110", "极坐标编程");
//        m_mCommands.insert("M17", "子程序结束");
//    }
//    // 其他机床品牌...
//}

void GCodeAPIs::loadStandardCommands() {
    // 标准G命令
    m_gCommands = {
        {"G00", "快速定位 (X Y Z)"},
        {"G01", "直线插补 (X Y Z F)"},
        {"G02", "顺时针圆弧插补 (X Y Z I J K R F)"},
        {"G03", "逆时针圆弧插补 (X Y Z I J K R F)"},
        {"G04", "暂停 (P|X)"},
        {"G17", "XY平面选择"},
        {"G18", "ZX平面选择"},
        {"G19", "YZ平面选择"},
        {"G20", "英制单位"},
        {"G21", "公制单位"},
        {"G28", "返回参考点"},
        {"G40", "取消刀具半径补偿"},
        {"G41", "左侧刀具半径补偿 (D)"},
        {"G42", "右侧刀具半径补偿 (D)"},
        {"G43", "刀具长度正补偿 (H)"},
        {"G49", "取消刀具长度补偿"},
        {"G54", "工件坐标系1"},
        {"G90", "绝对编程"},
        {"G91", "增量编程"}
    };

    // 标准M命令
    m_mCommands = {
        {"M00", "程序暂停"},
        {"M01", "选择性暂停"},
        {"M02", "程序结束"},
        {"M03", "主轴正转 (S)"},
        {"M04", "主轴反转 (S)"},
        {"M05", "主轴停止"},
        {"M06", "换刀 (T)"},
        {"M08", "冷却液开"},
        {"M09", "冷却液关"},
        {"M30", "程序结束并返回"}

    };
    
    // 参数说明
    m_parameters = {
        {"X", "X轴坐标"},
        {"Y", "Y轴坐标"},
        {"Z", "Z轴坐标"},
        {"I", "圆弧中心X向增量"},
        {"J", "圆弧中心Y向增量"},
        {"K", "圆弧中心Z向增量"},
        {"F", "进给速度"},
        {"S", "主轴转速"},
        {"T", "刀具号"},
        {"D", "刀具半径补偿号"},
        {"H", "刀具长度补偿号"},
        {"P", "暂停时间(毫秒)"},
        {"R", "圆弧半径"}
    };

    // 添加到自动完成
    for (auto it = m_gCommands.begin(); it != m_gCommands.end(); ++it) {
        add(it.key() + "?1" + it.value());
    }

    for (auto it = m_mCommands.begin(); it != m_mCommands.end(); ++it) {
        add(it.key() + "?2" + it.value());
    }

    for (auto it = m_parameters.begin(); it != m_parameters.end(); ++it) {
        add(it.key() + "?3" + it.value());
    }

    // 在GCodeAPIs.cpp的loadStandardCommands中添加
    m_mCommands.insert("M98", "调用子程序 (P=程序号 L=调用次数)");
    m_mCommands.insert("M99", "子程序结束/返回");
}

void GCodeAPIs::updateAutoCompletionList(const QStringList &context, QStringList &list) {
    QsciAPIs::updateAutoCompletionList(context, list);
    
    if (!context.isEmpty()) {
        QString lastWord = context.last();
        QStringList filtered;
        
        for (const QString &item : list) {
            if (item.startsWith(lastWord, Qt::CaseInsensitive)) {
                filtered.append(item.split('?').first());
            }
        }
        
        if (!filtered.isEmpty()) {
            list = filtered;
        }
    }
}

QStringList GCodeAPIs::callTips(const QStringList &context, int commas, QsciScintilla::CallTipsStyle style, QList<int> &shifts) {
    if (context.isEmpty()) return QStringList();
    
    QString lastWord = context.last();
    if (m_gCommands.contains(lastWord)) {
        return QStringList() << m_gCommands[lastWord];
    } else if (m_mCommands.contains(lastWord)) {
        return QStringList() << m_mCommands[lastWord];
    }
    
    return QsciAPIs::callTips(context, commas, style, shifts);
}
