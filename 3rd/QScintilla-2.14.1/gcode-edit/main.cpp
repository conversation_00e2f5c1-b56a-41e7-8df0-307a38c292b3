// main.cpp
#include <QApplication>
#include "GCodeEditor.h"

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);

    GCodeEditor editor;
    editor.resize(800, 600);
    editor.show();
    editor.setText(
        "%\n"
        "O1000 (SAMPLE PROGRAM)\n"
        "G21 G40 G49 G80 G90 (SAFETY BLOCK)\n"
        "G28 G91 Z0 (RETURN TO Z HOME)\n"
        "T1 M06 (TOOL CHANGE)\n"
        "G54 G00 X0 Y0 (WORK OFFSET AND POSITION)\n"
        "G43 H1 Z50.0 (TOOL LENGTH OFFSET)\n"
        "M03 S1200 (SPINDLE ON CW 1200RPM)\n"
        "G00 Z5.0 (RAPID TO CLEARANCE)\n"
        "G01 Z-2.0 F200 (PLUNGE CUT)\n"
        "X50.0 Y25.0 (LINEAR CUT)\n"
        "G02 X75.0 Y0 R25.0 (CLOCKWISE ARC)\n"
        "G01 X0 (BACK TO START)\n"
        "G00 Z50.0 (RAPID RETRACT)\n"
        "M05 (SPINDLE STOP)\n"
        "M30 (PROGRAM END)\n"
        "%\n"
    );

    return app.exec();
}
