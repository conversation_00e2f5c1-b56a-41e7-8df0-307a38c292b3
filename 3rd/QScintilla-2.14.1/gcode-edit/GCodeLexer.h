// GCodeLexer.h
#pragma once
#include <Qsci/qscilexercustom.h>
#include <QSet>       // 添加QSet头文件
#include <QString>    // 添加QString头文件

class GCodeLexer : public QsciLexerCustom {
    Q_OBJECT
public:
    explicit GCodeLexer(QObject *parent = nullptr);

    const char *language() const override;
    QString description(int style) const override;
    void styleText(int start, int end) override;

private:
    void styleLine(const QString &line, int startPos);
    bool isGCommand(const QString &word) const;
    bool isMCommand(const QString &word) const;
    bool isAxisCommand(const QString &word) const;
    bool isNumber(const QString &word) const;
    bool isMacroCommand(const QString &word) const;

    QSet<QString> m_gCommands;
    QSet<QString> m_mCommands;
    QSet<QString> m_mMacroCommands;
    QSet<QString> m_axisLetters;
};
