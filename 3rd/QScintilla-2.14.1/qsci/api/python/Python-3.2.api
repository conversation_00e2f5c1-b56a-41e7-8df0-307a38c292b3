ArithmeticError(??)
AssertionError(??)
AttributeError(??)
BaseException(??)
BufferError(??)
BytesWarning(??)
DeprecationWarning(??)
EOFError(??)
Ellipsis(??)
EnvironmentError(??)
Exception(??)
False(??)
FloatingPointError(??)
FutureWarning(??)
GeneratorExit(??)
IOError(??)
ImportError(??)
ImportWarning(??)
IndentationError(??)
IndexError(??)
KeyError(??)
KeyboardInterrupt(??)
LookupError(??)
MemoryError(??)
NameError(??)
None(??)
NotImplemented(??)
NotImplementedError(??)
OSError(??)
OverflowError(??)
PendingDeprecationWarning(??)
ReferenceError(??)
ResourceWarning(??)
RuntimeError(??)
RuntimeWarning(??)
StopIteration(??)
SyntaxError(??)
SyntaxWarning(??)
SystemError(??)
SystemExit(??)
TabError(??)
True(??)
TypeError(??)
UnboundLocalError(??)
UnicodeDecodeError(??)
UnicodeEncodeError(??)
UnicodeError(??)
UnicodeTranslateError(??)
UnicodeWarning(??)
UserWarning(??)
ValueError(??)
Warning(??)
ZeroDivisionError(??)
abc.ABCMeta(??)
abc.WeakSet(??)
abc.abstractclassmethod(??)
abc.abstractmethod(??)
abc.abstractproperty(??)
abc.abstractstaticmethod(??)
abs(number) -> number
aifc.Aifc_read(??)
aifc.Aifc_write(??)
aifc.Chunk(??)
aifc.Error(??)
aifc.builtins(??)
aifc.open(??)
aifc.openfp(??)
aifc.struct(??)
all(iterable) -> bool
any(iterable) -> bool
argparse.Action(??)
argparse.ArgumentDefaultsHelpFormatter(??)
argparse.ArgumentError(??)
argparse.ArgumentParser(??)
argparse.ArgumentTypeError(??)
argparse.FileType(??)
argparse.HelpFormatter(??)
argparse.Namespace(??)
argparse.ONE_OR_MORE
argparse.OPTIONAL
argparse.PARSER
argparse.REMAINDER
argparse.RawDescriptionHelpFormatter(??)
argparse.RawTextHelpFormatter(??)
argparse.SUPPRESS
argparse.ZERO_OR_MORE
argparse.ngettext(??)
ascii(object) -> string
ast.AST(??)
ast.Add(??)
ast.And(??)
ast.Assert(??)
ast.Assign(??)
ast.Attribute(??)
ast.AugAssign(??)
ast.AugLoad(??)
ast.AugStore(??)
ast.BinOp(??)
ast.BitAnd(??)
ast.BitOr(??)
ast.BitXor(??)
ast.BoolOp(??)
ast.Break(??)
ast.Bytes(??)
ast.Call(??)
ast.ClassDef(??)
ast.Compare(??)
ast.Continue(??)
ast.Del(??)
ast.Delete(??)
ast.Dict(??)
ast.DictComp(??)
ast.Div(??)
ast.Ellipsis(??)
ast.Eq(??)
ast.ExceptHandler(??)
ast.Expr(??)
ast.Expression(??)
ast.ExtSlice(??)
ast.FloorDiv(??)
ast.For(??)
ast.FunctionDef(??)
ast.GeneratorExp(??)
ast.Global(??)
ast.Gt(??)
ast.GtE(??)
ast.If(??)
ast.IfExp(??)
ast.Import(??)
ast.ImportFrom(??)
ast.In(??)
ast.Index(??)
ast.Interactive(??)
ast.Invert(??)
ast.Is(??)
ast.IsNot(??)
ast.LShift(??)
ast.Lambda(??)
ast.List(??)
ast.ListComp(??)
ast.Load(??)
ast.Lt(??)
ast.LtE(??)
ast.Mod(??)
ast.Module(??)
ast.Mult(??)
ast.Name(??)
ast.NodeTransformer(??)
ast.NodeVisitor(??)
ast.Nonlocal(??)
ast.Not(??)
ast.NotEq(??)
ast.NotIn(??)
ast.Num(??)
ast.Or(??)
ast.Param(??)
ast.Pass(??)
ast.Pow(??)
ast.PyCF_ONLY_AST
ast.RShift(??)
ast.Raise(??)
ast.Return(??)
ast.Set(??)
ast.SetComp(??)
ast.Slice(??)
ast.Starred(??)
ast.Store(??)
ast.Str(??)
ast.Sub(??)
ast.Subscript(??)
ast.Suite(??)
ast.TryExcept(??)
ast.TryFinally(??)
ast.Tuple(??)
ast.UAdd(??)
ast.USub(??)
ast.UnaryOp(??)
ast.While(??)
ast.With(??)
ast.Yield(??)
ast.alias(??)
ast.arg(??)
ast.arguments(??)
ast.boolop(??)
ast.cmpop(??)
ast.comprehension(??)
ast.copy_location(??)
ast.dump(??)
ast.excepthandler(??)
ast.expr(??)
ast.expr_context(??)
ast.fix_missing_locations(??)
ast.get_docstring(??)
ast.increment_lineno(??)
ast.iter_child_nodes(??)
ast.iter_fields(??)
ast.keyword(??)
ast.literal_eval(??)
ast.mod(??)
ast.operator(??)
ast.parse(??)
ast.slice(??)
ast.stmt(??)
ast.unaryop(??)
ast.walk(??)
asynchat.async_chat(??)
asynchat.asyncore(??)
asynchat.buffer(??)
asynchat.deque(iterable[, maxlen]) --> deque object
asynchat.fifo(??)
asynchat.find_prefix_at_end(??)
asynchat.simple_producer(??)
asynchat.socket(??)
asyncore.EALREADY
asyncore.EBADF
asyncore.ECONNABORTED
asyncore.ECONNRESET
asyncore.EINPROGRESS
asyncore.EINTR
asyncore.EINVAL
asyncore.EISCONN
asyncore.ENOTCONN
asyncore.ESHUTDOWN
asyncore.EWOULDBLOCK
asyncore.ExitNow(??)
asyncore.close_all(??)
asyncore.compact_traceback(??)
asyncore.dispatcher(??)
asyncore.dispatcher_with_send(??)
asyncore.errorcode(??)
asyncore.fcntl(??)
asyncore.file_dispatcher(??)
asyncore.file_wrapper(??)
asyncore.loop(??)
asyncore.os(??)
asyncore.poll(??)
asyncore.poll2(??)
asyncore.poll3(??)
asyncore.read(??)
asyncore.readwrite(??)
asyncore.select(??)
asyncore.socket(??)
asyncore.socket_map(??)
asyncore.sys(??)
asyncore.time(??)
asyncore.warnings(??)
asyncore.write(??)
base64.MAXBINSIZE
base64.MAXLINESIZE
base64.b16decode(??)
base64.b16encode(??)
base64.b32decode(??)
base64.b32encode(??)
base64.b64decode(??)
base64.b64encode(??)
base64.binascii(??)
base64.bytes_types(??)
base64.decode(??)
base64.decodebytes(??)
base64.decodestring(??)
base64.encode(??)
base64.encodebytes(??)
base64.encodestring(??)
base64.main(??)
base64.re(??)
base64.standard_b64decode(??)
base64.standard_b64encode(??)
base64.struct(??)
base64.test(??)
base64.urlsafe_b64decode(??)
base64.urlsafe_b64encode(??)
bdb.Bdb(??)
bdb.BdbQuit(??)
bdb.Breakpoint(??)
bdb.Tdb(??)
bdb.bar(??)
bdb.checkfuncname(??)
bdb.effective(??)
bdb.fnmatch(??)
bdb.foo(??)
bdb.os(??)
bdb.set_trace(??)
bdb.sys(??)
bdb.test(??)
bin(number) -> string
binhex.BinHex(??)
binhex.Error(??)
binhex.FInfo(??)
binhex.HexBin(??)
binhex.LINELEN
binhex.REASONABLY_LARGE
binhex.RUNCHAR
binhex.binascii(??)
binhex.binhex(infilename, outfilename): create binhex-encoded copy of a file
binhex.getfileinfo(??)
binhex.hexbin(infilename, outfilename) - Decode binhexed file
binhex.io(??)
binhex.openrsrc(??)
binhex.os(??)
binhex.struct(??)
binhex.sys(??)
bisect.bisect(??)
bisect.bisect_left(a, x[, lo[, hi]]) -> index
bisect.bisect_right(a, x[, lo[, hi]]) -> index
bisect.insort(??)
bisect.insort_left(a, x[, lo[, hi]])
bisect.insort_right(a, x[, lo[, hi]])
bool(x) -> bool
builtins.ArithmeticError(??)
builtins.AssertionError(??)
builtins.AttributeError(??)
builtins.BaseException(??)
builtins.BufferError(??)
builtins.BytesWarning(??)
builtins.DeprecationWarning(??)
builtins.EOFError(??)
builtins.Ellipsis(??)
builtins.EnvironmentError(??)
builtins.Exception(??)
builtins.False
builtins.FloatingPointError(??)
builtins.FutureWarning(??)
builtins.GeneratorExit(??)
builtins.IOError(??)
builtins.ImportError(??)
builtins.ImportWarning(??)
builtins.IndentationError(??)
builtins.IndexError(??)
builtins.KeyError(??)
builtins.KeyboardInterrupt(??)
builtins.LookupError(??)
builtins.MemoryError(??)
builtins.NameError(??)
builtins.None
builtins.NotImplemented(??)
builtins.NotImplementedError(??)
builtins.OSError(??)
builtins.OverflowError(??)
builtins.PendingDeprecationWarning(??)
builtins.ReferenceError(??)
builtins.ResourceWarning(??)
builtins.RuntimeError(??)
builtins.RuntimeWarning(??)
builtins.StopIteration(??)
builtins.SyntaxError(??)
builtins.SyntaxWarning(??)
builtins.SystemError(??)
builtins.SystemExit(??)
builtins.TabError(??)
builtins.True
builtins.TypeError(??)
builtins.UnboundLocalError(??)
builtins.UnicodeDecodeError(??)
builtins.UnicodeEncodeError(??)
builtins.UnicodeError(??)
builtins.UnicodeTranslateError(??)
builtins.UnicodeWarning(??)
builtins.UserWarning(??)
builtins.ValueError(??)
builtins.Warning(??)
builtins.ZeroDivisionError(??)
builtins.abs(number) -> number
builtins.all(iterable) -> bool
builtins.any(iterable) -> bool
builtins.ascii(object) -> string
builtins.bin(number) -> string
builtins.bool(x) -> bool
builtins.bytearray(iterable_of_ints) -> bytearray
builtins.bytes(iterable_of_ints) -> bytes
builtins.callable(object) -> bool
builtins.chr(i) -> Unicode character
builtins.classmethod(function) -> method
builtins.compile(source, filename, mode[, flags[, dont_inherit]]) -> code object
builtins.complex(real[, imag]) -> complex number
builtins.copyright(??)
builtins.credits(??)
builtins.delattr(object, name)
builtins.dict() -> new empty dictionary
builtins.dir([object]) -> list of strings
builtins.divmod(x, y) -> (div, mod)
builtins.enumerate(iterable[, start]) -> iterator for index, value of iterable
builtins.eval(source[, globals[, locals]]) -> value
builtins.exec(object[, globals[, locals]])
builtins.exit(??)
builtins.filter(function or None, iterable) --> filter object
builtins.float(x) -> floating point number
builtins.format(value[, format_spec]) -> string
builtins.frozenset() -> empty frozenset object
builtins.getattr(object, name[, default]) -> value
builtins.globals() -> dictionary
builtins.hasattr(object, name) -> bool
builtins.hash(object) -> integer
builtins.help(??)
builtins.hex(number) -> string
builtins.id(object) -> integer
builtins.input([prompt]) -> string
builtins.int(x[, base]) -> integer
builtins.isinstance(object, class-or-type-or-tuple) -> bool
builtins.issubclass(C, B) -> bool
builtins.iter(iterable) -> iterator
builtins.len(object) -> integer
builtins.license(??)
builtins.list() -> new empty list
builtins.locals() -> dictionary
builtins.map(func, *iterables) --> map object
builtins.max(iterable[, key=func]) -> value
builtins.memoryview(object)
builtins.min(iterable[, key=func]) -> value
builtins.next(iterator[, default])
builtins.object(??)
builtins.oct(number) -> string
builtins.open(??)
builtins.ord(c) -> integer
builtins.pow(x, y[, z]) -> number
builtins.print(value, ..., sep=' ', end='\n', file=sys.stdout)
builtins.property(fget=None, fset=None, fdel=None, doc=None) -> property attribute
builtins.quit(??)
builtins.range([start,] stop[, step]) -> range object
builtins.repr(object) -> string
builtins.reversed(sequence) -> reverse iterator over values of the sequence
builtins.round(number[, ndigits]) -> number
builtins.set() -> new empty set object
builtins.setattr(object, name, value)
builtins.slice([start,] stop[, step])
builtins.sorted(iterable, key=None, reverse=False) --> new sorted list
builtins.staticmethod(function) -> method
builtins.str(string[, encoding[, errors]]) -> str
builtins.sum(iterable[, start]) -> value
builtins.super() -> same as super(__class__, <first argument>)
builtins.tuple() -> empty tuple
builtins.type(object) -> the object's type
builtins.vars([object]) -> dictionary
builtins.zip(iter1 [,iter2 [...]]) --> zip object
bytearray(iterable_of_ints) -> bytearray
bytes(iterable_of_ints) -> bytes
cProfile.Profile(custom_timer=None, time_unit=None, subcalls=True, builtins=True)
cProfile.label(??)
cProfile.main(??)
cProfile.run(??)
cProfile.runctx(??)
calendar.Calendar(??)
calendar.EPOCH
calendar.FRIDAY
calendar.February
calendar.HTMLCalendar(??)
calendar.IllegalMonthError(??)
calendar.IllegalWeekdayError(??)
calendar.January
calendar.LocaleHTMLCalendar(??)
calendar.LocaleTextCalendar(??)
calendar.MONDAY
calendar.SATURDAY
calendar.SUNDAY
calendar.THURSDAY
calendar.TUESDAY
calendar.TextCalendar(??)
calendar.WEDNESDAY
calendar.c(??)
calendar.calendar(??)
calendar.datetime(??)
calendar.day_abbr(??)
calendar.day_name(??)
calendar.different_locale(??)
calendar.error(??)
calendar.firstweekday(??)
calendar.format(??)
calendar.formatstring(??)
calendar.isleap(??)
calendar.leapdays(??)
calendar.main(??)
calendar.mdays(??)
calendar.month(??)
calendar.month_abbr(??)
calendar.month_name(??)
calendar.monthcalendar(??)
calendar.monthrange(??)
calendar.prcal(??)
calendar.prmonth(??)
calendar.prweek(??)
calendar.setfirstweekday(??)
calendar.sys(??)
calendar.timegm(??)
calendar.week(??)
calendar.weekday(??)
calendar.weekheader(??)
callable(object) -> bool
cgi.BytesIO([buffer]) -> object
cgi.FeedParser(??)
cgi.FieldStorage(??)
cgi.MiniFieldStorage(??)
cgi.StringIO(??)
cgi.TextIOWrapper(??)
cgi.dolog(??)
cgi.escape(??)
cgi.html(??)
cgi.initlog(??)
cgi.locale(??)
cgi.log(??)
cgi.logfile
cgi.logfp(??)
cgi.maxlen
cgi.nolog(??)
cgi.os(??)
cgi.parse(??)
cgi.parse_header(??)
cgi.parse_multipart(??)
cgi.parse_qs(??)
cgi.parse_qsl(??)
cgi.print_arguments(??)
cgi.print_directory(??)
cgi.print_environ(??)
cgi.print_environ_usage(??)
cgi.print_exception(??)
cgi.print_form(??)
cgi.sys(??)
cgi.tempfile(??)
cgi.test(??)
cgi.urllib(??)
cgi.valid_boundary(??)
cgi.warn(??)
cgitb.Hook(??)
cgitb.enable(??)
cgitb.grey(??)
cgitb.handler(??)
cgitb.html(??)
cgitb.inspect(??)
cgitb.keyword(??)
cgitb.linecache(??)
cgitb.lookup(??)
cgitb.os(??)
cgitb.pydoc(??)
cgitb.reset(??)
cgitb.scanvars(??)
cgitb.small(??)
cgitb.strong(??)
cgitb.sys(??)
cgitb.tempfile(??)
cgitb.text(??)
cgitb.time(??)
cgitb.tokenize(??)
cgitb.traceback(??)
cgitb.types(??)
chr(i) -> Unicode character
chunk.Chunk(??)
classmethod(function) -> method
cmd.Cmd(??)
cmd.IDENTCHARS
cmd.PROMPT
cmd.string(??)
cmd.sys(??)
code.CommandCompiler(??)
code.InteractiveConsole(??)
code.InteractiveInterpreter(??)
code.compile_command(??)
code.interact(??)
code.sys(??)
code.traceback(??)
codecs.BOM
codecs.BOM32_BE
codecs.BOM32_LE
codecs.BOM64_BE
codecs.BOM64_LE
codecs.BOM_BE
codecs.BOM_LE
codecs.BOM_UTF16
codecs.BOM_UTF16_BE
codecs.BOM_UTF16_LE
codecs.BOM_UTF32
codecs.BOM_UTF32_BE
codecs.BOM_UTF32_LE
codecs.BOM_UTF8
codecs.BufferedIncrementalDecoder(??)
codecs.BufferedIncrementalEncoder(??)
codecs.Codec(??)
codecs.CodecInfo(??)
codecs.EncodedFile(??)
codecs.IncrementalDecoder(??)
codecs.IncrementalEncoder(??)
codecs.StreamReader(??)
codecs.StreamReaderWriter(??)
codecs.StreamRecoder(??)
codecs.StreamWriter(??)
codecs.ascii_decode(??)
codecs.ascii_encode(??)
codecs.backslashreplace_errors(??)
codecs.builtins(??)
codecs.charmap_build(??)
codecs.charmap_decode(??)
codecs.charmap_encode(??)
codecs.decode(obj, [encoding[,errors]]) -> object
codecs.encode(obj, [encoding[,errors]]) -> object
codecs.escape_decode(??)
codecs.escape_encode(??)
codecs.getdecoder(??)
codecs.getencoder(??)
codecs.getincrementaldecoder(??)
codecs.getincrementalencoder(??)
codecs.getreader(??)
codecs.getwriter(??)
codecs.ignore_errors(??)
codecs.iterdecode(??)
codecs.iterencode(??)
codecs.latin_1_decode(??)
codecs.latin_1_encode(??)
codecs.lookup(encoding) -> CodecInfo
codecs.lookup_error(errors) -> handler
codecs.make_encoding_map(??)
codecs.make_identity_dict(rng) -> dict
codecs.open(??)
codecs.raw_unicode_escape_decode(??)
codecs.raw_unicode_escape_encode(??)
codecs.readbuffer_encode(??)
codecs.register(search_function)
codecs.register_error(errors, handler)
codecs.replace_errors(??)
codecs.strict_errors(??)
codecs.sys(??)
codecs.unicode_escape_decode(??)
codecs.unicode_escape_encode(??)
codecs.unicode_internal_decode(??)
codecs.unicode_internal_encode(??)
codecs.utf_16_be_decode(??)
codecs.utf_16_be_encode(??)
codecs.utf_16_decode(??)
codecs.utf_16_encode(??)
codecs.utf_16_ex_decode(??)
codecs.utf_16_le_decode(??)
codecs.utf_16_le_encode(??)
codecs.utf_32_be_decode(??)
codecs.utf_32_be_encode(??)
codecs.utf_32_decode(??)
codecs.utf_32_encode(??)
codecs.utf_32_ex_decode(??)
codecs.utf_32_le_decode(??)
codecs.utf_32_le_encode(??)
codecs.utf_7_decode(??)
codecs.utf_7_encode(??)
codecs.utf_8_decode(??)
codecs.utf_8_encode(??)
codecs.xmlcharrefreplace_errors(??)
codeop.CommandCompiler(??)
codeop.Compile(??)
codeop.PyCF_DONT_IMPLY_DEDENT
codeop.compile_command(??)
collections.ByteString(??)
collections.Callable(??)
collections.Container(??)
collections.Counter(??)
collections.Hashable(??)
collections.ItemsView(??)
collections.Iterable(??)
collections.Iterator(??)
collections.KeysView(??)
collections.Mapping(??)
collections.MappingView(??)
collections.MutableMapping(??)
collections.MutableSequence(??)
collections.MutableSet(??)
collections.OrderedDict(??)
collections.Sequence(??)
collections.Set(??)
collections.Sized(??)
collections.UserDict(??)
collections.UserList(??)
collections.UserString(??)
collections.ValuesView(??)
collections.defaultdict(default_factory) --> dict with default factory
collections.deque(iterable[, maxlen]) --> deque object
collections.namedtuple(??)
colorsys.ONE_SIXTH
colorsys.ONE_THIRD
colorsys.TWO_THIRD
colorsys.hls_to_rgb(??)
colorsys.hsv_to_rgb(??)
colorsys.rgb_to_hls(??)
colorsys.rgb_to_hsv(??)
colorsys.rgb_to_yiq(??)
colorsys.yiq_to_rgb(??)
compile(source, filename, mode[, flags[, dont_inherit]]) -> code object
compileall.compile_dir(??)
compileall.compile_file(??)
compileall.compile_path(??)
compileall.errno(??)
compileall.imp(??)
compileall.main(??)
compileall.os(??)
compileall.py_compile(??)
compileall.struct(??)
compileall.sys(??)
complex(real[, imag]) -> complex number
concurrent.futures(??)
concurrent.futures.ALL_COMPLETED
concurrent.futures.CancelledError(??)
concurrent.futures.Executor(??)
concurrent.futures.FIRST_COMPLETED
concurrent.futures.FIRST_EXCEPTION
concurrent.futures.Future(??)
concurrent.futures.ProcessPoolExecutor(??)
concurrent.futures.ThreadPoolExecutor(??)
concurrent.futures.TimeoutError(??)
concurrent.futures.as_completed(??)
concurrent.futures.process(??)
concurrent.futures.process.EXTRA_QUEUED_CALLS
concurrent.futures.process.ProcessPoolExecutor(??)
concurrent.futures.process.atexit(??)
concurrent.futures.process.multiprocessing(??)
concurrent.futures.process.queue(??)
concurrent.futures.process.threading(??)
concurrent.futures.process.weakref(??)
concurrent.futures.thread(??)
concurrent.futures.thread.ThreadPoolExecutor(??)
concurrent.futures.thread.atexit(??)
concurrent.futures.thread.queue(??)
concurrent.futures.thread.threading(??)
concurrent.futures.thread.weakref(??)
concurrent.futures.wait(??)
configparser.BasicInterpolation(??)
configparser.ConfigParser(??)
configparser.DEFAULTSECT
configparser.DuplicateOptionError(??)
configparser.DuplicateSectionError(??)
configparser.Error(??)
configparser.ExtendedInterpolation(??)
configparser.Interpolation(??)
configparser.InterpolationDepthError(??)
configparser.InterpolationError(??)
configparser.InterpolationMissingOptionError(??)
configparser.InterpolationSyntaxError(??)
configparser.LegacyInterpolation(??)
configparser.MAX_INTERPOLATION_DEPTH
configparser.MissingSectionHeaderError(??)
configparser.MutableMapping(??)
configparser.NoOptionError(??)
configparser.NoSectionError(??)
configparser.ParsingError(??)
configparser.RawConfigParser(??)
configparser.SafeConfigParser(??)
configparser.SectionProxy(??)
configparser.functools(??)
configparser.io(??)
configparser.itertools(??)
configparser.re(??)
configparser.sys(??)
configparser.warnings(??)
contextlib.ContextDecorator(??)
contextlib.closing(??)
contextlib.contextmanager(??)
contextlib.sys(??)
contextlib.warn(??)
contextlib.wraps(??)
copy.Error(??)
copy.PyStringMap(??)
copy.builtins(??)
copy.copy(??)
copy.deepcopy(??)
copy.dispatch_table(??)
copy.error(??)
copy.name
copy.t(??)
copy.weakref(??)
copyreg.add_extension(??)
copyreg.clear_extension_cache(??)
copyreg.constructor(??)
copyreg.dispatch_table(??)
copyreg.pickle(??)
copyreg.pickle_complex(??)
copyreg.remove_extension(??)
copyright(??)
credits(??)
csv.Dialect(??)
csv.DictReader(??)
csv.DictWriter(??)
csv.Error(??)
csv.QUOTE_ALL
csv.QUOTE_MINIMAL
csv.QUOTE_NONE
csv.QUOTE_NONNUMERIC
csv.Sniffer(??)
csv.StringIO(??)
csv.excel(??)
csv.excel_tab(??)
csv.field_size_limit(??)
csv.get_dialect(??)
csv.list_dialects(??)
csv.re(??)
csv.reader(??)
csv.register_dialect(??)
csv.unix_dialect(??)
csv.unregister_dialect(??)
csv.writer(??)
ctypes.ARRAY(??)
ctypes.ArgumentError(??)
ctypes.Array(??)
ctypes.BigEndianStructure(??)
ctypes.CDLL(??)
ctypes.CFUNCTYPE(??)
ctypes.DEFAULT_MODE
ctypes.LibraryLoader(??)
ctypes.LittleEndianStructure(??)
ctypes.POINTER(??)
ctypes.PYFUNCTYPE(??)
ctypes.PyDLL(??)
ctypes.RTLD_GLOBAL
ctypes.RTLD_LOCAL
ctypes.SetPointerType(??)
ctypes.Structure(??)
ctypes.Union(??)
ctypes.addressof(C instance) -> integer
ctypes.alignment(C type) -> integer
ctypes.byref(C instance[, offset=0]) -> byref-object
ctypes.c_bool(??)
ctypes.c_buffer(??)
ctypes.c_byte(??)
ctypes.c_char(??)
ctypes.c_char_p(??)
ctypes.c_double(??)
ctypes.c_float(??)
ctypes.c_int(??)
ctypes.c_int16(??)
ctypes.c_int32(??)
ctypes.c_int64(??)
ctypes.c_int8(??)
ctypes.c_long(??)
ctypes.c_longdouble(??)
ctypes.c_longlong(??)
ctypes.c_short(??)
ctypes.c_size_t(??)
ctypes.c_ssize_t(??)
ctypes.c_ubyte(??)
ctypes.c_uint(??)
ctypes.c_uint16(??)
ctypes.c_uint32(??)
ctypes.c_uint64(??)
ctypes.c_uint8(??)
ctypes.c_ulong(??)
ctypes.c_ulonglong(??)
ctypes.c_ushort(??)
ctypes.c_void_p(??)
ctypes.c_voidp(??)
ctypes.c_wchar(??)
ctypes.c_wchar_p(??)
ctypes.cast(??)
ctypes.cdll(??)
ctypes.create_string_buffer(aBytes) -> character array
ctypes.create_unicode_buffer(aString) -> character array
ctypes.get_errno(??)
ctypes.macholib(??)
ctypes.macholib.dyld(??)
ctypes.macholib.dyld.DEFAULT_FRAMEWORK_FALLBACK(??)
ctypes.macholib.dyld.DEFAULT_LIBRARY_FALLBACK(??)
ctypes.macholib.dyld.accumulate(iterable) --> accumulate object
ctypes.macholib.dyld.chain(*iterables) --> chain object
ctypes.macholib.dyld.combinations(iterable, r) --> combinations object
ctypes.macholib.dyld.combinations_with_replacement(iterable, r) --> combinations_with_replacement object
ctypes.macholib.dyld.compress(data, selectors) --> iterator over selected data
ctypes.macholib.dyld.count(start=0, step=1) --> count object
ctypes.macholib.dyld.cycle(iterable) --> cycle object
ctypes.macholib.dyld.dropwhile(predicate, iterable) --> dropwhile object
ctypes.macholib.dyld.dyld_default_search(??)
ctypes.macholib.dyld.dyld_env(??)
ctypes.macholib.dyld.dyld_executable_path_search(??)
ctypes.macholib.dyld.dyld_fallback_framework_path(??)
ctypes.macholib.dyld.dyld_fallback_library_path(??)
ctypes.macholib.dyld.dyld_find(??)
ctypes.macholib.dyld.dyld_framework_path(??)
ctypes.macholib.dyld.dyld_image_suffix(??)
ctypes.macholib.dyld.dyld_image_suffix_search(??)
ctypes.macholib.dyld.dyld_library_path(??)
ctypes.macholib.dyld.dyld_override_search(??)
ctypes.macholib.dyld.dylib_info(??)
ctypes.macholib.dyld.filterfalse(function or None, sequence) --> filterfalse object
ctypes.macholib.dyld.framework_find(??)
ctypes.macholib.dyld.framework_info(??)
ctypes.macholib.dyld.groupby(iterable[, keyfunc]) -> create an iterator which returns
ctypes.macholib.dyld.islice(iterable, [start,] stop [, step]) --> islice object
ctypes.macholib.dyld.os(??)
ctypes.macholib.dyld.permutations(iterable[, r]) --> permutations object
ctypes.macholib.dyld.product(*iterables) --> product object
ctypes.macholib.dyld.repeat(object [,times]) -> create an iterator which returns the object
ctypes.macholib.dyld.starmap(function, sequence) --> starmap object
ctypes.macholib.dyld.takewhile(predicate, iterable) --> takewhile object
ctypes.macholib.dyld.tee(iterable, n=2) --> tuple of n independent iterators.
ctypes.macholib.dyld.test_dyld_find(??)
ctypes.macholib.dyld.zip_longest(iter1 [,iter2 [...]], [fillvalue=None]) --> zip_longest object
ctypes.macholib.dylib(??)
ctypes.macholib.dylib.DYLIB_RE(??)
ctypes.macholib.dylib.dylib_info(??)
ctypes.macholib.dylib.re(??)
ctypes.macholib.dylib.test_dylib_info(??)
ctypes.macholib.framework(??)
ctypes.macholib.framework.STRICT_FRAMEWORK_RE(??)
ctypes.macholib.framework.framework_info(??)
ctypes.macholib.framework.re(??)
ctypes.macholib.framework.test_framework_info(??)
ctypes.memmove(??)
ctypes.memset(??)
ctypes.pointer(??)
ctypes.py_object(??)
ctypes.pydll(??)
ctypes.pythonapi(??)
ctypes.resize(??)
ctypes.set_errno(??)
ctypes.sizeof(C type) -> integer
ctypes.string_at(addr[, size]) -> string
ctypes.util(??)
ctypes.util.contextlib(??)
ctypes.util.errno(??)
ctypes.util.find_library(??)
ctypes.util.os(??)
ctypes.util.re(??)
ctypes.util.sys(??)
ctypes.util.tempfile(??)
ctypes.util.test(??)
ctypes.wstring_at(addr[, size]) -> string
datetime.MAXYEAR
datetime.MINYEAR
datetime.date(year, month, day) --> date object
datetime.datetime(year, month, day[, hour[, minute[, second[, microsecond[,tzinfo]]]]])
datetime.datetime_CAPI(??)
datetime.time([hour[, minute[, second[, microsecond[, tzinfo]]]]]) --> a time object
datetime.timedelta(??)
datetime.timezone(??)
datetime.tzinfo(??)
dbm.dumb.collections(??)
dbm.dumb.error(??)
dbm.dumb.open(??)
dbm.error(??)
dbm.io(??)
dbm.open(??)
dbm.os(??)
dbm.struct(??)
dbm.sys(??)
dbm.whichdb(??)
decimal.BasicContext(??)
decimal.Clamped(??)
decimal.Context(??)
decimal.ConversionSyntax(??)
decimal.Decimal(??)
decimal.DecimalException(??)
decimal.DecimalTuple(sign, digits, exponent)
decimal.DefaultContext(??)
decimal.DivisionByZero(??)
decimal.DivisionImpossible(??)
decimal.DivisionUndefined(??)
decimal.ExtendedContext(??)
decimal.Inexact(??)
decimal.InvalidContext(??)
decimal.InvalidOperation(??)
decimal.Overflow(??)
decimal.ROUND_05UP
decimal.ROUND_CEILING
decimal.ROUND_DOWN
decimal.ROUND_FLOOR
decimal.ROUND_HALF_DOWN
decimal.ROUND_HALF_EVEN
decimal.ROUND_HALF_UP
decimal.ROUND_UP
decimal.Rounded(??)
decimal.Subnormal(??)
decimal.Underflow(??)
decimal.getcontext(??)
decimal.localcontext(??)
decimal.setcontext(??)
delattr(object, name)
dict() -> new empty dictionary
difflib.Differ(??)
difflib.HtmlDiff(??)
difflib.IS_CHARACTER_JUNK(??)
difflib.IS_LINE_JUNK(??)
difflib.Match(a, b, size)
difflib.SequenceMatcher(??)
difflib.context_diff(??)
difflib.get_close_matches(??)
difflib.heapq(??)
difflib.ndiff(??)
difflib.restore(??)
difflib.unified_diff(??)
difflib.warnings(??)
dir([object]) -> list of strings
dis.COMPILER_FLAG_NAMES(??)
dis.EXTENDED_ARG
dis.HAVE_ARGUMENT
dis.cmp_op(??)
dis.code_info(??)
dis.dis(??)
dis.disassemble(??)
dis.disco(??)
dis.distb(??)
dis.findlabels(??)
dis.findlinestarts(??)
dis.hascompare(??)
dis.hasconst(??)
dis.hasfree(??)
dis.hasjabs(??)
dis.hasjrel(??)
dis.haslocal(??)
dis.hasname(??)
dis.opmap(??)
dis.opname(??)
dis.pretty_flags(??)
dis.show_code(??)
dis.sys(??)
dis.types(??)
divmod(x, y) -> (div, mod)
doctest.BLANKLINE_MARKER
doctest.COMPARISON_FLAGS
doctest.DONT_ACCEPT_BLANKLINE
doctest.DONT_ACCEPT_TRUE_FOR_1
doctest.DebugRunner(??)
doctest.DocFileCase(??)
doctest.DocFileSuite(??)
doctest.DocFileTest(??)
doctest.DocTest(??)
doctest.DocTestCase(??)
doctest.DocTestFailure(??)
doctest.DocTestFinder(??)
doctest.DocTestParser(??)
doctest.DocTestRunner(??)
doctest.DocTestSuite(??)
doctest.ELLIPSIS
doctest.ELLIPSIS_MARKER
doctest.Example(??)
doctest.IGNORE_EXCEPTION_DETAIL
doctest.NORMALIZE_WHITESPACE
doctest.OPTIONFLAGS_BY_NAME(??)
doctest.OutputChecker(??)
doctest.REPORTING_FLAGS
doctest.REPORT_CDIFF
doctest.REPORT_NDIFF
doctest.REPORT_ONLY_FIRST_FAILURE
doctest.REPORT_UDIFF
doctest.SKIP
doctest.SkipDocTestCase(??)
doctest.StringIO(??)
doctest.TestResults(failed, attempted)
doctest.UnexpectedException(??)
doctest.debug(??)
doctest.debug_script(??)
doctest.debug_src(??)
doctest.difflib(??)
doctest.inspect(??)
doctest.linecache(??)
doctest.master(??)
doctest.namedtuple(??)
doctest.os(??)
doctest.pdb(??)
doctest.re(??)
doctest.register_optionflag(??)
doctest.run_docstring_examples(??)
doctest.script_from_examples(??)
doctest.set_unittest_reportflags(??)
doctest.sys(??)
doctest.tempfile(??)
doctest.testfile(??)
doctest.testmod(??)
doctest.testsource(??)
doctest.traceback(??)
doctest.unittest(??)
doctest.warnings(??)
dummy_threading.Barrier(??)
dummy_threading.BoundedSemaphore(??)
dummy_threading.Condition(??)
dummy_threading.Event(??)
dummy_threading.Lock(??)
dummy_threading.RLock(??)
dummy_threading.Semaphore(??)
dummy_threading.Thread(??)
dummy_threading.Timer(??)
dummy_threading.active_count(??)
dummy_threading.current_thread(??)
dummy_threading.enumerate(??)
dummy_threading.local(??)
dummy_threading.setprofile(??)
dummy_threading.settrace(??)
dummy_threading.stack_size(??)
dummy_threading.threading(??)
email.base64mime(??)
email.base64mime.CRLF
email.base64mime.EMPTYSTRING
email.base64mime.MISC_LEN
email.base64mime.NL
email.base64mime.a2b_base64(??)
email.base64mime.b2a_base64(??)
email.base64mime.b64encode(??)
email.base64mime.body_decode(??)
email.base64mime.body_encode(??)
email.base64mime.decode(??)
email.base64mime.decodestring(??)
email.base64mime.header_encode(??)
email.base64mime.header_length(??)
email.charset(??)
email.charset.ALIASES(??)
email.charset.BASE64
email.charset.CHARSETS(??)
email.charset.CODEC_MAP(??)
email.charset.Charset(??)
email.charset.DEFAULT_CHARSET
email.charset.EMPTYSTRING
email.charset.QP
email.charset.RFC2047_CHROME_LEN
email.charset.SHORTEST
email.charset.UNKNOWN8BIT
email.charset.add_alias(??)
email.charset.add_charset(??)
email.charset.add_codec(??)
email.charset.email(??)
email.charset.encode_7or8bit(??)
email.charset.errors(??)
email.charset.partial(func, *args, **keywords) - new function with partial application
email.encoders(??)
email.encoders.encode_7or8bit(??)
email.encoders.encode_base64(??)
email.encoders.encode_noop(??)
email.encoders.encode_quopri(??)
email.errors(??)
email.errors.BoundaryError(??)
email.errors.CharsetError(??)
email.errors.FirstHeaderLineIsContinuationDefect(??)
email.errors.HeaderParseError(??)
email.errors.MalformedHeaderDefect(??)
email.errors.MessageDefect(??)
email.errors.MessageError(??)
email.errors.MessageParseError(??)
email.errors.MisplacedEnvelopeHeaderDefect(??)
email.errors.MultipartConversionError(??)
email.errors.MultipartInvariantViolationDefect(??)
email.errors.NoBoundaryInMultipartDefect(??)
email.errors.StartBoundaryNotFoundDefect(??)
email.feedparser(??)
email.feedparser.BufferedSubFile(??)
email.feedparser.BytesFeedParser(??)
email.feedparser.EMPTYSTRING
email.feedparser.FeedParser(??)
email.feedparser.NL
email.feedparser.NLCRE(??)
email.feedparser.NLCRE_bol(??)
email.feedparser.NLCRE_crack(??)
email.feedparser.NLCRE_eol(??)
email.feedparser.NeedMoreData(??)
email.feedparser.errors(??)
email.feedparser.headerRE(??)
email.feedparser.message(??)
email.feedparser.re(??)
email.generator(??)
email.generator.BytesGenerator(??)
email.generator.BytesIO([buffer]) -> object
email.generator.DecodedGenerator(??)
email.generator.Generator(??)
email.generator.Header(??)
email.generator.NL
email.generator.StringIO(??)
email.generator.UNDERSCORE
email.generator.fcre(??)
email.generator.random(??)
email.generator.re(??)
email.generator.sys(??)
email.generator.time(??)
email.generator.warnings(??)
email.header(??)
email.header.BSPACE
email.header.Charset(??)
email.header.EMPTYSTRING
email.header.Header(??)
email.header.HeaderParseError(??)
email.header.MAXLINELEN
email.header.NL
email.header.SPACE
email.header.SPACE8
email.header.USASCII(??)
email.header.UTF8(??)
email.header.binascii(??)
email.header.decode_header(??)
email.header.ecre(??)
email.header.email(??)
email.header.fcre(??)
email.header.make_header(??)
email.header.re(??)
email.iterators(??)
email.iterators.StringIO(??)
email.iterators.body_line_iterator(??)
email.iterators.sys(??)
email.iterators.typed_subpart_iterator(??)
email.iterators.walk(??)
email.message(??)
email.message.BytesIO([buffer]) -> object
email.message.Charset(??)
email.message.Message(??)
email.message.SEMISPACE
email.message.StringIO(??)
email.message.base64(??)
email.message.binascii(??)
email.message.errors(??)
email.message.header(??)
email.message.re(??)
email.message.tspecials(??)
email.message.utils(??)
email.message.uu(??)
email.message.warnings(??)
email.message_from_binary_file(??)
email.message_from_bytes(??)
email.message_from_file(??)
email.message_from_string(??)
email.mime(??)
email.mime.application(??)
email.mime.application.MIMEApplication(??)
email.mime.application.MIMENonMultipart(??)
email.mime.application.encoders(??)
email.mime.audio(??)
email.mime.audio.BytesIO([buffer]) -> object
email.mime.audio.MIMEAudio(??)
email.mime.audio.MIMENonMultipart(??)
email.mime.audio.encoders(??)
email.mime.audio.sndhdr(??)
email.mime.base(??)
email.mime.base.MIMEBase(??)
email.mime.base.message(??)
email.mime.image.MIMEImage(??)
email.mime.image.MIMENonMultipart(??)
email.mime.image.encoders(??)
email.mime.image.imghdr(??)
email.mime.message(??)
email.mime.message.MIMEMessage(??)
email.mime.message.MIMENonMultipart(??)
email.mime.message.message(??)
email.mime.multipart(??)
email.mime.multipart.MIMEBase(??)
email.mime.multipart.MIMEMultipart(??)
email.mime.nonmultipart(??)
email.mime.nonmultipart.MIMEBase(??)
email.mime.nonmultipart.MIMENonMultipart(??)
email.mime.nonmultipart.errors(??)
email.mime.text(??)
email.mime.text.MIMENonMultipart(??)
email.mime.text.MIMEText(??)
email.mime.text.encode_7or8bit(??)
email.parser(??)
email.parser.BytesParser(??)
email.parser.FeedParser(??)
email.parser.HeaderParser(??)
email.parser.Message(??)
email.parser.Parser(??)
email.parser.StringIO(??)
email.parser.TextIOWrapper(??)
email.parser.warnings(??)
email.quoprimime(??)
email.quoprimime.CRLF
email.quoprimime.EMPTYSTRING
email.quoprimime.NL
email.quoprimime.ascii_letters
email.quoprimime.body_check(??)
email.quoprimime.body_decode(??)
email.quoprimime.body_encode(??)
email.quoprimime.body_length(??)
email.quoprimime.c
email.quoprimime.decode(??)
email.quoprimime.decodestring(??)
email.quoprimime.digits
email.quoprimime.header_check(??)
email.quoprimime.header_decode(??)
email.quoprimime.header_encode(??)
email.quoprimime.header_length(??)
email.quoprimime.hexdigits
email.quoprimime.quote(??)
email.quoprimime.re(??)
email.quoprimime.unquote(??)
email.utils(??)
email.utils.COMMASPACE
email.utils.CRLF
email.utils.EMPTYSTRING
email.utils.StringIO(??)
email.utils.TICK
email.utils.UEMPTYSTRING
email.utils.base64(??)
email.utils.collapse_rfc2231_value(??)
email.utils.decode_params(??)
email.utils.decode_rfc2231(??)
email.utils.ecre(??)
email.utils.encode_rfc2231(??)
email.utils.escapesre(??)
email.utils.formataddr(??)
email.utils.formatdate(??)
email.utils.getaddresses(??)
email.utils.make_msgid(??)
email.utils.mktime_tz(??)
email.utils.os(??)
email.utils.parseaddr(??)
email.utils.parsedate(??)
email.utils.parsedate_tz(??)
email.utils.quote(??)
email.utils.random(??)
email.utils.re(??)
email.utils.rfc2231_continuation(??)
email.utils.socket(??)
email.utils.specialsre(??)
email.utils.time(??)
email.utils.unquote(??)
email.utils.urllib(??)
email.utils.warnings(??)
enumerate(iterable[, start]) -> iterator for index, value of iterable
errno.E2BIG
errno.EACCES
errno.EADDRINUSE
errno.EADDRNOTAVAIL
errno.EADV
errno.EAFNOSUPPORT
errno.EAGAIN
errno.EALREADY
errno.EBADE
errno.EBADF
errno.EBADFD
errno.EBADMSG
errno.EBADR
errno.EBADRQC
errno.EBADSLT
errno.EBFONT
errno.EBUSY
errno.ECANCELED
errno.ECHILD
errno.ECHRNG
errno.ECOMM
errno.ECONNABORTED
errno.ECONNREFUSED
errno.ECONNRESET
errno.EDEADLK
errno.EDEADLOCK
errno.EDESTADDRREQ
errno.EDOM
errno.EDOTDOT
errno.EDQUOT
errno.EEXIST
errno.EFAULT
errno.EFBIG
errno.EHOSTDOWN
errno.EHOSTUNREACH
errno.EIDRM
errno.EILSEQ
errno.EINPROGRESS
errno.EINTR
errno.EINVAL
errno.EIO
errno.EISCONN
errno.EISDIR
errno.EISNAM
errno.EKEYEXPIRED
errno.EKEYREJECTED
errno.EKEYREVOKED
errno.EL2HLT
errno.EL2NSYNC
errno.EL3HLT
errno.EL3RST
errno.ELIBACC
errno.ELIBBAD
errno.ELIBEXEC
errno.ELIBMAX
errno.ELIBSCN
errno.ELNRNG
errno.ELOOP
errno.EMEDIUMTYPE
errno.EMFILE
errno.EMLINK
errno.EMSGSIZE
errno.EMULTIHOP
errno.ENAMETOOLONG
errno.ENAVAIL
errno.ENETDOWN
errno.ENETRESET
errno.ENETUNREACH
errno.ENFILE
errno.ENOANO
errno.ENOBUFS
errno.ENOCSI
errno.ENODATA
errno.ENODEV
errno.ENOENT
errno.ENOEXEC
errno.ENOKEY
errno.ENOLCK
errno.ENOLINK
errno.ENOMEDIUM
errno.ENOMEM
errno.ENOMSG
errno.ENONET
errno.ENOPKG
errno.ENOPROTOOPT
errno.ENOSPC
errno.ENOSR
errno.ENOSTR
errno.ENOSYS
errno.ENOTBLK
errno.ENOTCONN
errno.ENOTDIR
errno.ENOTEMPTY
errno.ENOTNAM
errno.ENOTRECOVERABLE
errno.ENOTSOCK
errno.ENOTSUP
errno.ENOTTY
errno.ENOTUNIQ
errno.ENXIO
errno.EOPNOTSUPP
errno.EOVERFLOW
errno.EOWNERDEAD
errno.EPERM
errno.EPFNOSUPPORT
errno.EPIPE
errno.EPROTO
errno.EPROTONOSUPPORT
errno.EPROTOTYPE
errno.ERANGE
errno.EREMCHG
errno.EREMOTE
errno.EREMOTEIO
errno.ERESTART
errno.ERFKILL
errno.EROFS
errno.ESHUTDOWN
errno.ESOCKTNOSUPPORT
errno.ESPIPE
errno.ESRCH
errno.ESRMNT
errno.ESTALE
errno.ESTRPIPE
errno.ETIME
errno.ETIMEDOUT
errno.ETOOMANYREFS
errno.ETXTBSY
errno.EUCLEAN
errno.EUNATCH
errno.EUSERS
errno.EWOULDBLOCK
errno.EXDEV
errno.EXFULL
errno.errorcode(??)
eval(source[, globals[, locals]]) -> value
exec(object[, globals[, locals]])
exit(??)
filecmp.BUFSIZE
filecmp.cmp(??)
filecmp.cmpfiles(??)
filecmp.demo(??)
filecmp.dircmp(??)
filecmp.filterfalse(function or None, sequence) --> filterfalse object
filecmp.os(??)
filecmp.stat(??)
fileinput.DEFAULT_BUFSIZE
fileinput.FileInput(??)
fileinput.close(??)
fileinput.filelineno(??)
fileinput.filename(??)
fileinput.fileno(??)
fileinput.hook_compressed(??)
fileinput.hook_encoded(??)
fileinput.input(files=None, inplace=False, backup="", bufsize=0, mode="r", openhook=None)
fileinput.isfirstline(??)
fileinput.isstdin(??)
fileinput.lineno(??)
fileinput.nextfile(??)
fileinput.os(??)
fileinput.sys(??)
filter(function or None, iterable) --> filter object
float(x) -> floating point number
fnmatch.filter(??)
fnmatch.fnmatch(??)
fnmatch.fnmatchcase(??)
fnmatch.functools(??)
fnmatch.os(??)
fnmatch.posixpath(??)
fnmatch.re(??)
fnmatch.translate(??)
format(value[, format_spec]) -> string
formatter.AS_IS(??)
formatter.AbstractFormatter(??)
formatter.AbstractWriter(??)
formatter.DumbWriter(??)
formatter.NullFormatter(??)
formatter.NullWriter(??)
formatter.sys(??)
formatter.test(??)
fractions.Decimal(??)
fractions.Fraction(??)
fractions.gcd(??)
fractions.math(??)
fractions.numbers(??)
fractions.operator(??)
fractions.re(??)
fractions.sys(??)
frozenset() -> empty frozenset object
ftplib.B_CRLF
ftplib.CRLF
ftplib.Error(??)
ftplib.FTP(??)
ftplib.FTP_PORT
ftplib.FTP_TLS(??)
ftplib.MSG_OOB
ftplib.Netrc(??)
ftplib.all_errors(??)
ftplib.error_perm(??)
ftplib.error_proto(??)
ftplib.error_reply(??)
ftplib.error_temp(??)
ftplib.ftpcp(??)
ftplib.os(??)
ftplib.parse150(??)
ftplib.parse227(??)
ftplib.parse229(??)
ftplib.parse257(??)
ftplib.print_line(??)
ftplib.socket(??)
ftplib.ssl(??)
ftplib.sys(??)
ftplib.test(??)
functools.Lock(??)
functools.OrderedDict(??)
functools.WRAPPER_ASSIGNMENTS(??)
functools.WRAPPER_UPDATES(??)
functools.cmp_to_key(??)
functools.lru_cache(??)
functools.namedtuple(??)
functools.partial(func, *args, **keywords) - new function with partial application
functools.reduce(function, sequence[, initial]) -> value
functools.total_ordering(??)
functools.update_wrapper(??)
functools.wraps(??)
gc.DEBUG_COLLECTABLE
gc.DEBUG_LEAK
gc.DEBUG_SAVEALL
gc.DEBUG_STATS
gc.DEBUG_UNCOLLECTABLE
gc.collect([generation]) -> n
gc.disable() -> None
gc.enable() -> None
gc.garbage(??)
gc.get_count() -> (count0, count1, count2)
gc.get_debug() -> flags
gc.get_objects() -> [...]
gc.get_referents(*objs) -> list
gc.get_referrers(*objs) -> list
gc.get_threshold() -> (threshold0, threshold1, threshold2)
gc.is_tracked(obj) -> bool
gc.isenabled() -> status
gc.set_debug(flags) -> None
gc.set_threshold(threshold0, [threshold1, threshold2]) -> None
genericpath.commonprefix(??)
genericpath.exists(??)
genericpath.getatime(??)
genericpath.getctime(??)
genericpath.getmtime(??)
genericpath.getsize(??)
genericpath.isdir(??)
genericpath.isfile(??)
genericpath.os(??)
genericpath.stat(??)
getattr(object, name[, default]) -> value
getopt.GetoptError(??)
getopt.do_longs(??)
getopt.do_shorts(??)
getopt.error(??)
getopt.getopt(args, options[, long_options]) -> opts, args
getopt.gnu_getopt(??)
getopt.long_has_args(??)
getopt.os(??)
getopt.short_has_arg(??)
getpass.GetPassWarning(??)
getpass.fallback_getpass(??)
getpass.getpass(??)
getpass.getuser(??)
getpass.os(??)
getpass.sys(??)
getpass.termios(??)
getpass.unix_getpass(??)
getpass.warnings(??)
getpass.win_getpass(??)
gettext.Catalog(??)
gettext.ENOENT
gettext.GNUTranslations(??)
gettext.NullTranslations(??)
gettext.bind_textdomain_codeset(??)
gettext.bindtextdomain(??)
gettext.c2py(??)
gettext.copy(??)
gettext.dgettext(??)
gettext.dngettext(??)
gettext.find(??)
gettext.gettext(??)
gettext.install(??)
gettext.io(??)
gettext.ldgettext(??)
gettext.ldngettext(??)
gettext.lgettext(??)
gettext.lngettext(??)
gettext.locale(??)
gettext.ngettext(??)
gettext.os(??)
gettext.re(??)
gettext.struct(??)
gettext.sys(??)
gettext.textdomain(??)
gettext.translation(??)
glob.fnmatch(??)
glob.glob(??)
glob.glob0(??)
glob.glob1(??)
glob.has_magic(??)
glob.iglob(??)
glob.magic_check(??)
glob.magic_check_bytes(??)
glob.os(??)
glob.re(??)
glob.sys(??)
globals() -> dictionary
gzip.FCOMMENT
gzip.FEXTRA
gzip.FHCRC
gzip.FNAME
gzip.FTEXT
gzip.GzipFile(??)
gzip.LOWU32(??)
gzip.READ
gzip.U32(??)
gzip.WRITE
gzip.builtins(??)
gzip.compress(??)
gzip.decompress(??)
gzip.io(??)
gzip.open(??)
gzip.os(??)
gzip.read32(??)
gzip.struct(??)
gzip.sys(??)
gzip.time(??)
gzip.write32u(??)
gzip.zlib(??)
hasattr(object, name) -> bool
hash(object) -> integer
hashlib.algorithms_available(??)
hashlib.algorithms_guaranteed(??)
hashlib.md5(??)
hashlib.new(name, data=b'') - Return a new hashing object using the named algorithm;
hashlib.sha1(??)
hashlib.sha224(??)
hashlib.sha256(??)
hashlib.sha384(??)
hashlib.sha512(??)
heapq.bisect(??)
heapq.chain(*iterables) --> chain object
heapq.count(start=0, step=1) --> count object
heapq.heapify(??)
heapq.heappop(??)
heapq.heappush(??)
heapq.heappushpop(??)
heapq.heapreplace(??)
heapq.islice(iterable, [start,] stop [, step]) --> islice object
heapq.merge(??)
heapq.nlargest(??)
heapq.nsmallest(??)
heapq.repeat(object [,times]) -> create an iterator which returns the object
heapq.tee(iterable, n=2) --> tuple of n independent iterators.
help(??)
hex(number) -> string
hmac.HMAC(??)
hmac.digest_size(??)
hmac.new(??)
hmac.trans_36
hmac.trans_5C
html.entities(??)
html.entities.codepoint2name(??)
html.entities.entitydefs(??)
html.entities.name2codepoint(??)
html.escape(??)
html.parser(??)
html.parser.HTMLParseError(??)
html.parser.HTMLParser(??)
html.parser.attrfind(??)
html.parser.attrfind_tolerant(??)
html.parser.charref(??)
html.parser.commentclose(??)
html.parser.endendtag(??)
html.parser.endtagfind(??)
html.parser.entityref(??)
html.parser.incomplete(??)
html.parser.interesting_cdata(??)
html.parser.interesting_normal(??)
html.parser.locatestarttagend(??)
html.parser.locatestarttagend_tolerant(??)
html.parser.piclose(??)
html.parser.re(??)
html.parser.starttagopen(??)
html.parser.tagfind(??)
http.client(??)
http.client.ACCEPTED
http.client.BAD_GATEWAY
http.client.BAD_REQUEST
http.client.BadStatusLine(??)
http.client.CONFLICT
http.client.CONTINUE
http.client.CREATED
http.client.CannotSendHeader(??)
http.client.CannotSendRequest(??)
http.client.EXPECTATION_FAILED
http.client.FAILED_DEPENDENCY
http.client.FORBIDDEN
http.client.FOUND
http.client.GATEWAY_TIMEOUT
http.client.GONE
http.client.HTTPConnection(??)
http.client.HTTPException(??)
http.client.HTTPMessage(??)
http.client.HTTPResponse(??)
http.client.HTTPSConnection(??)
http.client.HTTPS_PORT
http.client.HTTP_PORT
http.client.HTTP_VERSION_NOT_SUPPORTED
http.client.IM_USED
http.client.INSUFFICIENT_STORAGE
http.client.INTERNAL_SERVER_ERROR
http.client.ImproperConnectionState(??)
http.client.IncompleteRead(??)
http.client.InvalidURL(??)
http.client.LENGTH_REQUIRED
http.client.LOCKED
http.client.LineTooLong(??)
http.client.MAXAMOUNT
http.client.METHOD_NOT_ALLOWED
http.client.MOVED_PERMANENTLY
http.client.MULTIPLE_CHOICES
http.client.MULTI_STATUS
http.client.NON_AUTHORITATIVE_INFORMATION
http.client.NOT_ACCEPTABLE
http.client.NOT_EXTENDED
http.client.NOT_FOUND
http.client.NOT_IMPLEMENTED
http.client.NOT_MODIFIED
http.client.NO_CONTENT
http.client.NotConnected(??)
http.client.OK
http.client.PARTIAL_CONTENT
http.client.PAYMENT_REQUIRED
http.client.PRECONDITION_FAILED
http.client.PROCESSING
http.client.PROXY_AUTHENTICATION_REQUIRED
http.client.REQUESTED_RANGE_NOT_SATISFIABLE
http.client.REQUEST_ENTITY_TOO_LARGE
http.client.REQUEST_TIMEOUT
http.client.REQUEST_URI_TOO_LONG
http.client.RESET_CONTENT
http.client.ResponseNotReady(??)
http.client.SEE_OTHER
http.client.SERVICE_UNAVAILABLE
http.client.SWITCHING_PROTOCOLS
http.client.TEMPORARY_REDIRECT
http.client.UNAUTHORIZED
http.client.UNPROCESSABLE_ENTITY
http.client.UNSUPPORTED_MEDIA_TYPE
http.client.UPGRADE_REQUIRED
http.client.USE_PROXY
http.client.UnimplementedFileMode(??)
http.client.UnknownProtocol(??)
http.client.UnknownTransferEncoding(??)
http.client.collections(??)
http.client.email(??)
http.client.error(??)
http.client.io(??)
http.client.os(??)
http.client.parse_headers(??)
http.client.responses(??)
http.client.socket(??)
http.client.ssl(??)
http.client.urlsplit(??)
http.client.warnings(??)
http.cookiejar(??)
http.cookiejar.Absent(??)
http.cookiejar.Cookie(??)
http.cookiejar.CookieJar(??)
http.cookiejar.CookiePolicy(??)
http.cookiejar.DAYS(??)
http.cookiejar.DEFAULT_HTTP_PORT
http.cookiejar.DefaultCookiePolicy(??)
http.cookiejar.EPOCH_YEAR
http.cookiejar.ESCAPED_CHAR_RE(??)
http.cookiejar.FileCookieJar(??)
http.cookiejar.HEADER_ESCAPE_RE(??)
http.cookiejar.HEADER_JOIN_ESCAPE_RE(??)
http.cookiejar.HEADER_QUOTED_VALUE_RE(??)
http.cookiejar.HEADER_TOKEN_RE(??)
http.cookiejar.HEADER_VALUE_RE(??)
http.cookiejar.HTTP_PATH_SAFE
http.cookiejar.IPV4_RE(??)
http.cookiejar.ISO_DATE_RE(??)
http.cookiejar.LOOSE_HTTP_DATE_RE(??)
http.cookiejar.LWPCookieJar(??)
http.cookiejar.LoadError(??)
http.cookiejar.MISSING_FILENAME_TEXT
http.cookiejar.MONTHS(??)
http.cookiejar.MONTHS_LOWER(??)
http.cookiejar.MozillaCookieJar(??)
http.cookiejar.STRICT_DATE_RE(??)
http.cookiejar.TIMEZONE_RE(??)
http.cookiejar.UTC_ZONES(??)
http.cookiejar.WEEKDAY_RE(??)
http.cookiejar.copy(??)
http.cookiejar.cut_port_re(??)
http.cookiejar.debug(??)
http.cookiejar.deepvalues(??)
http.cookiejar.domain_match(??)
http.cookiejar.eff_request_host(??)
http.cookiejar.escape_path(??)
http.cookiejar.http(??)
http.cookiejar.http2time(??)
http.cookiejar.is_HDN(??)
http.cookiejar.is_third_party(??)
http.cookiejar.iso2time(??)
http.cookiejar.join_header_words(??)
http.cookiejar.liberal_is_HDN(??)
http.cookiejar.logger(??)
http.cookiejar.lwp_cookie_str(??)
http.cookiejar.month
http.cookiejar.offset_from_tz_string(??)
http.cookiejar.parse_ns_headers(??)
http.cookiejar.re(??)
http.cookiejar.reach(??)
http.cookiejar.request_host(??)
http.cookiejar.request_path(??)
http.cookiejar.request_port(??)
http.cookiejar.split_header_words(??)
http.cookiejar.strip_quotes(??)
http.cookiejar.time(??)
http.cookiejar.time2isoz(??)
http.cookiejar.time2netscape(??)
http.cookiejar.timegm(??)
http.cookiejar.unmatched(??)
http.cookiejar.uppercase_escaped_char(??)
http.cookiejar.urllib(??)
http.cookiejar.user_domain_match(??)
http.cookiejar.vals_sorted_by_key(??)
http.cookies(??)
http.cookies.BaseCookie(??)
http.cookies.CookieError(??)
http.cookies.Morsel(??)
http.cookies.SimpleCookie(??)
http.cookies.re(??)
http.cookies.string(??)
http.server(??)
http.server.BaseHTTPRequestHandler(??)
http.server.CGIHTTPRequestHandler(??)
http.server.DEFAULT_ERROR_CONTENT_TYPE
http.server.DEFAULT_ERROR_MESSAGE
http.server.HTTPServer(??)
http.server.SimpleHTTPRequestHandler(??)
http.server.copy(??)
http.server.email(??)
http.server.executable(??)
http.server.html(??)
http.server.http(??)
http.server.io(??)
http.server.mimetypes(??)
http.server.nobody(??)
http.server.nobody_uid(??)
http.server.os(??)
http.server.posixpath(??)
http.server.select(??)
http.server.shutil(??)
http.server.socket(??)
http.server.socketserver(??)
http.server.sys(??)
http.server.test(??)
http.server.time(??)
http.server.urllib(??)
id(object) -> integer
imaplib.AllowedVersions(??)
imaplib.CRLF
imaplib.Commands(??)
imaplib.Continuation(??)
imaplib.Debug
imaplib.Flags(??)
imaplib.HAVE_SSL(??)
imaplib.IMAP4(??)
imaplib.IMAP4_PORT
imaplib.IMAP4_SSL(??)
imaplib.IMAP4_SSL_PORT
imaplib.IMAP4_stream(??)
imaplib.Int2AP(??)
imaplib.InternalDate(??)
imaplib.Internaldate2tuple(??)
imaplib.Literal(??)
imaplib.MapCRLF(??)
imaplib.Mon2num(??)
imaplib.ParseFlags(??)
imaplib.Response_code(??)
imaplib.Time2Internaldate(??)
imaplib.Untagged_response(??)
imaplib.Untagged_status(??)
imaplib.binascii(??)
imaplib.errno(??)
imaplib.random(??)
imaplib.re(??)
imaplib.socket(??)
imaplib.ssl(??)
imaplib.subprocess(??)
imaplib.sys(??)
imaplib.time(??)
imghdr.test(??)
imghdr.test_bmp(??)
imghdr.test_gif(??)
imghdr.test_jpeg(??)
imghdr.test_pbm(??)
imghdr.test_pgm(??)
imghdr.test_png(??)
imghdr.test_ppm(??)
imghdr.test_rast(??)
imghdr.test_rgb(??)
imghdr.test_tiff(??)
imghdr.test_xbm(??)
imghdr.testall(??)
imghdr.tests(??)
imghdr.what(??)
imp.C_BUILTIN
imp.C_EXTENSION
imp.IMP_HOOK
imp.NullImporter(??)
imp.PKG_DIRECTORY
imp.PY_CODERESOURCE
imp.PY_COMPILED
imp.PY_FROZEN
imp.PY_RESOURCE
imp.PY_SOURCE
imp.SEARCH_ERROR
imp.acquire_lock() -> None
imp.cache_from_source(??)
imp.find_module(name, [path]) -> (file, filename, (suffix, mode, type))
imp.get_frozen_object(??)
imp.get_magic() -> string
imp.get_suffixes() -> [(suffix, mode, type), ...]
imp.get_tag() -> string
imp.init_builtin(??)
imp.init_frozen(??)
imp.is_builtin(??)
imp.is_frozen(??)
imp.is_frozen_package(??)
imp.load_compiled(??)
imp.load_dynamic(??)
imp.load_module(name, file, filename, (suffix, mode, type)) -> module
imp.load_package(??)
imp.load_source(??)
imp.lock_held() -> boolean
imp.new_module(name) -> module
imp.release_lock() -> None
imp.reload(module) -> module
imp.source_from_cache(??)
importlib.abc.ExecutionLoader(??)
importlib.abc.Finder(??)
importlib.abc.InspectLoader(??)
importlib.abc.Loader(??)
importlib.abc.PyLoader(??)
importlib.abc.PyPycLoader(??)
importlib.abc.ResourceLoader(??)
importlib.abc.SourceLoader(??)
importlib.abc.abc(??)
importlib.abc.imp(??)
importlib.abc.io(??)
importlib.abc.machinery(??)
importlib.abc.marshal(??)
importlib.abc.os(??)
importlib.abc.sys(??)
importlib.abc.tokenize(??)
importlib.abc.types(??)
importlib.abc.util(??)
importlib.abc.warnings(??)
importlib.errno(??)
importlib.imp(??)
importlib.import_module(??)
importlib.machinery(??)
importlib.machinery.BuiltinImporter(??)
importlib.machinery.FrozenImporter(??)
importlib.machinery.PathFinder(??)
importlib.marshal(??)
importlib.os(??)
importlib.re(??)
importlib.sep
importlib.sys(??)
importlib.tokenize(??)
importlib.util(??)
importlib.util.module_for_loader(??)
importlib.util.set_loader(??)
importlib.util.set_package(??)
input([prompt]) -> string
inspect.ArgInfo(args, varargs, keywords, locals)
inspect.ArgSpec(args, varargs, keywords, defaults)
inspect.Arguments(args, varargs, varkw)
inspect.Attribute(name, kind, defining_class, object)
inspect.BlockFinder(??)
inspect.CO_GENERATOR
inspect.CO_NESTED
inspect.CO_NEWLOCALS
inspect.CO_NOFREE
inspect.CO_OPTIMIZED
inspect.CO_VARARGS
inspect.CO_VARKEYWORDS
inspect.EndOfBlock(??)
inspect.FullArgSpec(args, varargs, varkw, defaults, kwonlyargs, kwonlydefaults, annotations)
inspect.GEN_CLOSED
inspect.GEN_CREATED
inspect.GEN_RUNNING
inspect.GEN_SUSPENDED
inspect.ModuleInfo(name, suffix, mode, module_type)
inspect.TPFLAGS_IS_ABSTRACT
inspect.Traceback(filename, lineno, function, code_context, index)
inspect.attrgetter(attr, ...) --> attrgetter object
inspect.classify_class_attrs(??)
inspect.cleandoc(??)
inspect.currentframe(??)
inspect.findsource(??)
inspect.formatannotation(??)
inspect.formatannotationrelativeto(??)
inspect.formatargspec(??)
inspect.formatargvalues(??)
inspect.getabsfile(??)
inspect.getargs(??)
inspect.getargspec(??)
inspect.getargvalues(??)
inspect.getattr_static(??)
inspect.getblock(??)
inspect.getcallargs(??)
inspect.getclasstree(??)
inspect.getcomments(??)
inspect.getdoc(??)
inspect.getfile(??)
inspect.getframeinfo(??)
inspect.getfullargspec(??)
inspect.getgeneratorstate(??)
inspect.getinnerframes(??)
inspect.getlineno(??)
inspect.getmembers(??)
inspect.getmodule(??)
inspect.getmoduleinfo(??)
inspect.getmodulename(??)
inspect.getmro(??)
inspect.getouterframes(??)
inspect.getsource(??)
inspect.getsourcefile(??)
inspect.getsourcelines(??)
inspect.imp(??)
inspect.indentsize(??)
inspect.isabstract(??)
inspect.isbuiltin(??)
inspect.isclass(??)
inspect.iscode(??)
inspect.isdatadescriptor(??)
inspect.isframe(??)
inspect.isfunction(??)
inspect.isgenerator(??)
inspect.isgeneratorfunction(??)
inspect.isgetsetdescriptor(??)
inspect.ismemberdescriptor(??)
inspect.ismethod(??)
inspect.ismethoddescriptor(??)
inspect.ismodule(??)
inspect.isroutine(??)
inspect.istraceback(??)
inspect.itertools(??)
inspect.k
inspect.linecache(??)
inspect.mod_dict(??)
inspect.modulesbyfile(??)
inspect.namedtuple(??)
inspect.os(??)
inspect.re(??)
inspect.stack(??)
inspect.string(??)
inspect.sys(??)
inspect.tokenize(??)
inspect.trace(??)
inspect.types(??)
inspect.v
inspect.walktree(??)
int(x[, base]) -> integer
io.BlockingIOError(??)
io.BufferedIOBase(??)
io.BufferedRWPair(??)
io.BufferedRandom(??)
io.BufferedReader(??)
io.BufferedWriter(??)
io.BytesIO([buffer]) -> object
io.DEFAULT_BUFFER_SIZE
io.FileIO(??)
io.IOBase(??)
io.IncrementalNewlineDecoder(??)
io.OpenWrapper(??)
io.RawIOBase(??)
io.SEEK_CUR
io.SEEK_END
io.SEEK_SET
io.StringIO(??)
io.TextIOBase(??)
io.TextIOWrapper(??)
io.UnsupportedOperation(??)
io.abc(??)
io.open(??)
isinstance(object, class-or-type-or-tuple) -> bool
issubclass(C, B) -> bool
iter(iterable) -> iterator
itertools.accumulate(iterable) --> accumulate object
itertools.chain(*iterables) --> chain object
itertools.combinations(iterable, r) --> combinations object
itertools.combinations_with_replacement(iterable, r) --> combinations_with_replacement object
itertools.compress(data, selectors) --> iterator over selected data
itertools.count(start=0, step=1) --> count object
itertools.cycle(iterable) --> cycle object
itertools.dropwhile(predicate, iterable) --> dropwhile object
itertools.filterfalse(function or None, sequence) --> filterfalse object
itertools.groupby(iterable[, keyfunc]) -> create an iterator which returns
itertools.islice(iterable, [start,] stop [, step]) --> islice object
itertools.permutations(iterable[, r]) --> permutations object
itertools.product(*iterables) --> product object
itertools.repeat(object [,times]) -> create an iterator which returns the object
itertools.starmap(function, sequence) --> starmap object
itertools.takewhile(predicate, iterable) --> takewhile object
itertools.tee(iterable, n=2) --> tuple of n independent iterators.
itertools.zip_longest(iter1 [,iter2 [...]], [fillvalue=None]) --> zip_longest object
json.JSONDecoder(??)
json.JSONEncoder(??)
json.decoder(??)
json.decoder.BACKSLASH(??)
json.decoder.FLAGS
json.decoder.JSONArray(??)
json.decoder.JSONDecoder(??)
json.decoder.JSONObject(??)
json.decoder.NaN
json.decoder.NegInf
json.decoder.PosInf
json.decoder.STRINGCHUNK(??)
json.decoder.WHITESPACE(??)
json.decoder.WHITESPACE_STR
json.decoder.binascii(??)
json.decoder.c_scanstring(??)
json.decoder.errmsg(??)
json.decoder.linecol(??)
json.decoder.make_scanner(??)
json.decoder.py_scanstring(??)
json.decoder.re(??)
json.decoder.scanstring(string, end, strict=True) -> (string, end)
json.decoder.struct(??)
json.decoder.sys(??)
json.dump(??)
json.dumps(??)
json.encoder(??)
json.encoder.ESCAPE(??)
json.encoder.ESCAPE_ASCII(??)
json.encoder.ESCAPE_DCT(??)
json.encoder.FLOAT_REPR(??)
json.encoder.HAS_UTF8(??)
json.encoder.INFINITY
json.encoder.JSONEncoder(??)
json.encoder.c_encode_basestring_ascii(??)
json.encoder.c_make_encoder(??)
json.encoder.encode_basestring(??)
json.encoder.encode_basestring_ascii(string) -> string
json.encoder.i
json.encoder.py_encode_basestring_ascii(??)
json.encoder.re(??)
json.load(??)
json.loads(??)
json.scanner(??)
json.scanner.NUMBER_RE(??)
json.scanner.c_make_scanner(??)
json.scanner.make_scanner(??)
json.scanner.py_make_scanner(??)
json.scanner.re(??)
json.tool(??)
json.tool.json(??)
json.tool.main(??)
json.tool.sys(??)
keyword.iskeyword(??)
keyword.kwlist(??)
keyword.main(??)
len(object) -> integer
license(??)
linecache.cache(??)
linecache.checkcache(??)
linecache.clearcache(??)
linecache.getline(??)
linecache.getlines(??)
linecache.os(??)
linecache.sys(??)
linecache.tokenize(??)
linecache.updatecache(??)
list() -> new empty list
locale.ABDAY_1
locale.ABDAY_2
locale.ABDAY_3
locale.ABDAY_4
locale.ABDAY_5
locale.ABDAY_6
locale.ABDAY_7
locale.ABMON_1
locale.ABMON_10
locale.ABMON_11
locale.ABMON_12
locale.ABMON_2
locale.ABMON_3
locale.ABMON_4
locale.ABMON_5
locale.ABMON_6
locale.ABMON_7
locale.ABMON_8
locale.ABMON_9
locale.ALT_DIGITS
locale.AM_STR
locale.CHAR_MAX
locale.CODESET
locale.CRNCYSTR
locale.DAY_1
locale.DAY_2
locale.DAY_3
locale.DAY_4
locale.DAY_5
locale.DAY_6
locale.DAY_7
locale.D_FMT
locale.D_T_FMT
locale.ERA
locale.ERA_D_FMT
locale.ERA_D_T_FMT
locale.ERA_T_FMT
locale.Error(??)
locale.LC_ALL
locale.LC_COLLATE
locale.LC_CTYPE
locale.LC_MESSAGES
locale.LC_MONETARY
locale.LC_NUMERIC
locale.LC_TIME
locale.MON_1
locale.MON_10
locale.MON_11
locale.MON_12
locale.MON_2
locale.MON_3
locale.MON_4
locale.MON_5
locale.MON_6
locale.MON_7
locale.MON_8
locale.MON_9
locale.NOEXPR
locale.PM_STR
locale.RADIXCHAR
locale.THOUSEP
locale.T_FMT
locale.T_FMT_AMPM
locale.YESEXPR
locale.atof(??)
locale.atoi(??)
locale.bind_textdomain_codeset(domain, codeset) -> string
locale.bindtextdomain(domain, dir) -> string
locale.collections(??)
locale.currency(??)
locale.dcgettext(domain, msg, category) -> string
locale.dgettext(domain, msg) -> string
locale.encodings(??)
locale.format(??)
locale.format_string(??)
locale.functools(??)
locale.getdefaultlocale(??)
locale.getlocale(??)
locale.getpreferredencoding(??)
locale.gettext(msg) -> string
locale.locale_alias(??)
locale.locale_encoding_alias(??)
locale.localeconv(??)
locale.nl_langinfo(key) -> string
locale.normalize(??)
locale.re(??)
locale.resetlocale(??)
locale.setlocale(??)
locale.str(??)
locale.strcoll(??)
locale.strxfrm(string) -> string.
locale.sys(??)
locale.textdomain(domain) -> string
locale.windows_locale(??)
locals() -> dictionary
logging.BASIC_FORMAT
logging.BufferingFormatter(??)
logging.CRITICAL
logging.DEBUG
logging.ERROR
logging.FATAL
logging.FileHandler(??)
logging.Filter(??)
logging.Filterer(??)
logging.Formatter(??)
logging.Handler(??)
logging.INFO
logging.LogRecord(??)
logging.Logger(??)
logging.LoggerAdapter(??)
logging.Manager(??)
logging.NOTSET
logging.NullHandler(??)
logging.PercentStyle(??)
logging.PlaceHolder(??)
logging.RootLogger(??)
logging.StrFormatStyle(??)
logging.StreamHandler(??)
logging.StringTemplateStyle(??)
logging.Template(??)
logging.WARN
logging.WARNING
logging.addLevelName(??)
logging.atexit(??)
logging.basicConfig(??)
logging.captureWarnings(??)
logging.codecs(??)
logging.config(??)
logging.config.BaseConfigurator(??)
logging.config.ConvertingDict(??)
logging.config.ConvertingList(??)
logging.config.ConvertingTuple(??)
logging.config.DEFAULT_LOGGING_CONFIG_PORT
logging.config.DictConfigurator(??)
logging.config.IDENTIFIER(??)
logging.config.RESET_ERROR
logging.config.StreamRequestHandler(??)
logging.config.ThreadingTCPServer(??)
logging.config.dictConfig(??)
logging.config.dictConfigClass(??)
logging.config.fileConfig(??)
logging.config.io(??)
logging.config.listen(??)
logging.config.logging(??)
logging.config.os(??)
logging.config.re(??)
logging.config.socket(??)
logging.config.stopListening(??)
logging.config.struct(??)
logging.config.sys(??)
logging.config.thread(??)
logging.config.threading(??)
logging.config.traceback(??)
logging.config.types(??)
logging.config.valid_ident(??)
logging.critical(??)
logging.currentframe(??)
logging.debug(??)
logging.disable(??)
logging.error(??)
logging.exception(??)
logging.fatal(??)
logging.getLevelName(??)
logging.getLogRecordFactory(??)
logging.getLogger(??)
logging.getLoggerClass(??)
logging.handlers(??)
logging.handlers.BaseRotatingHandler(??)
logging.handlers.BufferingHandler(??)
logging.handlers.DEFAULT_HTTP_LOGGING_PORT
logging.handlers.DEFAULT_SOAP_LOGGING_PORT
logging.handlers.DEFAULT_TCP_LOGGING_PORT
logging.handlers.DEFAULT_UDP_LOGGING_PORT
logging.handlers.DatagramHandler(??)
logging.handlers.HTTPHandler(??)
logging.handlers.MemoryHandler(??)
logging.handlers.NTEventLogHandler(??)
logging.handlers.QueueHandler(??)
logging.handlers.QueueListener(??)
logging.handlers.RotatingFileHandler(??)
logging.handlers.SMTPHandler(??)
logging.handlers.ST_DEV
logging.handlers.ST_INO
logging.handlers.ST_MTIME
logging.handlers.SYSLOG_TCP_PORT
logging.handlers.SYSLOG_UDP_PORT
logging.handlers.SocketHandler(??)
logging.handlers.SysLogHandler(??)
logging.handlers.TimedRotatingFileHandler(??)
logging.handlers.WatchedFileHandler(??)
logging.handlers.codecs(??)
logging.handlers.logging(??)
logging.handlers.os(??)
logging.handlers.pickle(??)
logging.handlers.re(??)
logging.handlers.socket(??)
logging.handlers.struct(??)
logging.handlers.time(??)
logging.info(??)
logging.io(??)
logging.lastResort(??)
logging.log(??)
logging.logMultiprocessing
logging.logProcesses
logging.logThreads
logging.makeLogRecord(??)
logging.os(??)
logging.raiseExceptions
logging.root(??)
logging.setLogRecordFactory(??)
logging.setLoggerClass(??)
logging.shutdown(??)
logging.sys(??)
logging.thread(??)
logging.threading(??)
logging.time(??)
logging.traceback(??)
logging.warn(??)
logging.warning(??)
logging.warnings(??)
logging.weakref(??)
macpath.SF_APPEND
macpath.SF_ARCHIVED
macpath.SF_IMMUTABLE
macpath.SF_NOUNLINK
macpath.SF_SNAPSHOT
macpath.ST_ATIME
macpath.ST_CTIME
macpath.ST_DEV
macpath.ST_GID
macpath.ST_INO
macpath.ST_MODE
macpath.ST_MTIME
macpath.ST_NLINK
macpath.ST_SIZE
macpath.ST_UID
macpath.S_ENFMT
macpath.S_IEXEC
macpath.S_IFBLK
macpath.S_IFCHR
macpath.S_IFDIR
macpath.S_IFIFO
macpath.S_IFLNK
macpath.S_IFMT(??)
macpath.S_IFREG
macpath.S_IFSOCK
macpath.S_IMODE(??)
macpath.S_IREAD
macpath.S_IRGRP
macpath.S_IROTH
macpath.S_IRUSR
macpath.S_IRWXG
macpath.S_IRWXO
macpath.S_IRWXU
macpath.S_ISBLK(??)
macpath.S_ISCHR(??)
macpath.S_ISDIR(??)
macpath.S_ISFIFO(??)
macpath.S_ISGID
macpath.S_ISLNK(??)
macpath.S_ISREG(??)
macpath.S_ISSOCK(??)
macpath.S_ISUID
macpath.S_ISVTX
macpath.S_IWGRP
macpath.S_IWOTH
macpath.S_IWRITE
macpath.S_IWUSR
macpath.S_IXGRP
macpath.S_IXOTH
macpath.S_IXUSR
macpath.UF_APPEND
macpath.UF_IMMUTABLE
macpath.UF_NODUMP
macpath.UF_NOUNLINK
macpath.UF_OPAQUE
macpath.abspath(??)
macpath.altsep(??)
macpath.basename(??)
macpath.commonprefix(??)
macpath.curdir
macpath.defpath
macpath.devnull
macpath.dirname(??)
macpath.exists(??)
macpath.expanduser(??)
macpath.expandvars(??)
macpath.extsep
macpath.genericpath(??)
macpath.getatime(??)
macpath.getctime(??)
macpath.getmtime(??)
macpath.getsize(??)
macpath.isabs(??)
macpath.isdir(??)
macpath.isfile(??)
macpath.islink(??)
macpath.ismount(??)
macpath.join(??)
macpath.lexists(??)
macpath.norm_error(??)
macpath.normcase(??)
macpath.normpath(??)
macpath.os(??)
macpath.pardir
macpath.pathsep
macpath.realpath(??)
macpath.sep
macpath.split(??)
macpath.splitdrive(??)
macpath.splitext(??)
macpath.supports_unicode_filenames(??)
macurl2path.os(??)
macurl2path.pathname2url(??)
macurl2path.test(??)
macurl2path.url2pathname(??)
macurl2path.urllib(??)
mailbox.Babyl(??)
mailbox.BabylMessage(??)
mailbox.Error(??)
mailbox.ExternalClashError(??)
mailbox.FormatError(??)
mailbox.MH(??)
mailbox.MHMessage(??)
mailbox.MMDF(??)
mailbox.MMDFMessage(??)
mailbox.Mailbox(??)
mailbox.Maildir(??)
mailbox.MaildirMessage(??)
mailbox.Message(??)
mailbox.NoSuchMailboxError(??)
mailbox.NotEmptyError(??)
mailbox.calendar(??)
mailbox.copy(??)
mailbox.email(??)
mailbox.errno(??)
mailbox.fcntl(??)
mailbox.io(??)
mailbox.linesep
mailbox.mbox(??)
mailbox.mboxMessage(??)
mailbox.os(??)
mailbox.socket(??)
mailbox.sys(??)
mailbox.time(??)
mailbox.warnings(??)
mailcap.findmatch(??)
mailcap.findparam(??)
mailcap.getcaps(??)
mailcap.listmailcapfiles(??)
mailcap.lookup(??)
mailcap.os(??)
mailcap.parsefield(??)
mailcap.parseline(??)
mailcap.readmailcapfile(??)
mailcap.show(??)
mailcap.subst(??)
mailcap.test(??)
map(func, *iterables) --> map object
marshal.dump(value, file[, version])
marshal.dumps(value[, version])
marshal.load(file)
marshal.loads(string)
marshal.version
max(iterable[, key=func]) -> value
memoryview(object)
mimetypes.MimeTypes(??)
mimetypes.add_type(??)
mimetypes.common_types(??)
mimetypes.encodings_map(??)
mimetypes.guess_all_extensions(??)
mimetypes.guess_extension(??)
mimetypes.guess_type(??)
mimetypes.init(??)
mimetypes.inited(??)
mimetypes.knownfiles(??)
mimetypes.os(??)
mimetypes.posixpath(??)
mimetypes.read_mime_types(??)
mimetypes.suffix_map(??)
mimetypes.sys(??)
mimetypes.types_map(??)
mimetypes.urllib(??)
min(iterable[, key=func]) -> value
modulefinder.AddPackagePath(??)
modulefinder.HAVE_ARGUMENT
modulefinder.IMPORT_NAME
modulefinder.LOAD_CONST
modulefinder.Module(??)
modulefinder.ModuleFinder(??)
modulefinder.READ_MODE
modulefinder.ReplacePackage(??)
modulefinder.STORE_GLOBAL
modulefinder.STORE_NAME
modulefinder.STORE_OPS(??)
modulefinder.dis(??)
modulefinder.generators(??)
modulefinder.imp(??)
modulefinder.marshal(??)
modulefinder.os(??)
modulefinder.packagePathMap(??)
modulefinder.replacePackageMap(??)
modulefinder.struct(??)
modulefinder.sys(??)
modulefinder.test(??)
modulefinder.types(??)
multiprocessing.Array(??)
multiprocessing.AuthenticationError(??)
multiprocessing.BoundedSemaphore(??)
multiprocessing.BufferTooShort(??)
multiprocessing.Condition(??)
multiprocessing.Event(??)
multiprocessing.JoinableQueue(??)
multiprocessing.Lock(??)
multiprocessing.Manager(??)
multiprocessing.Pipe(??)
multiprocessing.Pool(??)
multiprocessing.Process(??)
multiprocessing.ProcessError(??)
multiprocessing.Queue(??)
multiprocessing.RLock(??)
multiprocessing.RawArray(??)
multiprocessing.RawValue(??)
multiprocessing.SUBDEBUG
multiprocessing.SUBWARNING
multiprocessing.Semaphore(??)
multiprocessing.TimeoutError(??)
multiprocessing.Value(??)
multiprocessing.active_children(??)
multiprocessing.allow_connection_pickling(??)
multiprocessing.connection(??)
multiprocessing.connection.AuthenticationError(??)
multiprocessing.connection.BUFSIZE
multiprocessing.connection.CHALLENGE
multiprocessing.connection.CONNECTION_TIMEOUT
multiprocessing.connection.Client(??)
multiprocessing.connection.ConnectionWrapper(??)
multiprocessing.connection.FAILURE
multiprocessing.connection.Finalize(??)
multiprocessing.connection.Listener(??)
multiprocessing.connection.MESSAGE_LENGTH
multiprocessing.connection.Pipe(??)
multiprocessing.connection.SocketClient(??)
multiprocessing.connection.SocketListener(??)
multiprocessing.connection.WELCOME
multiprocessing.connection.XmlClient(??)
multiprocessing.connection.XmlListener(??)
multiprocessing.connection.address_type(??)
multiprocessing.connection.answer_challenge(??)
multiprocessing.connection.arbitrary_address(??)
multiprocessing.connection.close(fd)
multiprocessing.connection.current_process(??)
multiprocessing.connection.debug(??)
multiprocessing.connection.default_family
multiprocessing.connection.deliver_challenge(??)
multiprocessing.connection.duplicate(??)
multiprocessing.connection.errno(??)
multiprocessing.connection.families(??)
multiprocessing.connection.get_temp_dir(??)
multiprocessing.connection.itertools(??)
multiprocessing.connection.os(??)
multiprocessing.connection.socket(??)
multiprocessing.connection.sub_debug(??)
multiprocessing.connection.sys(??)
multiprocessing.connection.tempfile(??)
multiprocessing.connection.time(??)
multiprocessing.cpu_count(??)
multiprocessing.current_process(??)
multiprocessing.dummy(??)
multiprocessing.dummy.Array(??)
multiprocessing.dummy.BoundedSemaphore(??)
multiprocessing.dummy.Condition(??)
multiprocessing.dummy.DummyProcess(??)
multiprocessing.dummy.Event(??)
multiprocessing.dummy.JoinableQueue(??)
multiprocessing.dummy.Lock(??)
multiprocessing.dummy.Manager(??)
multiprocessing.dummy.Namespace(??)
multiprocessing.dummy.Pipe(??)
multiprocessing.dummy.Pool(??)
multiprocessing.dummy.Process(??)
multiprocessing.dummy.Queue(??)
multiprocessing.dummy.RLock(??)
multiprocessing.dummy.Semaphore(??)
multiprocessing.dummy.TimeoutError(??)
multiprocessing.dummy.Value(??)
multiprocessing.dummy.active_children(??)
multiprocessing.dummy.array(??)
multiprocessing.dummy.connection(??)
multiprocessing.dummy.connection.Client(??)
multiprocessing.dummy.connection.Connection(??)
multiprocessing.dummy.connection.Listener(??)
multiprocessing.dummy.connection.Pipe(??)
multiprocessing.dummy.connection.Queue(??)
multiprocessing.dummy.connection.families(??)
multiprocessing.dummy.cpu_count(??)
multiprocessing.dummy.current_process(??)
multiprocessing.dummy.dict() -> new empty dictionary
multiprocessing.dummy.freeze_support(??)
multiprocessing.dummy.itertools(??)
multiprocessing.dummy.list() -> new empty list
multiprocessing.dummy.shutdown(??)
multiprocessing.dummy.sys(??)
multiprocessing.dummy.threading(??)
multiprocessing.dummy.weakref(??)
multiprocessing.forking(??)
multiprocessing.forking.ForkingPickler(??)
multiprocessing.forking.Pickler(??)
multiprocessing.forking.Popen(??)
multiprocessing.forking.assert_spawning(??)
multiprocessing.forking.close(fd)
multiprocessing.forking.duplicate(??)
multiprocessing.forking.exit(??)
multiprocessing.forking.old_main_modules(??)
multiprocessing.forking.os(??)
multiprocessing.forking.partial(func, *args, **keywords) - new function with partial application
multiprocessing.forking.prepare(??)
multiprocessing.forking.process(??)
multiprocessing.forking.signal(??)
multiprocessing.forking.sys(??)
multiprocessing.forking.time(??)
multiprocessing.forking.util(??)
multiprocessing.freeze_support(??)
multiprocessing.get_logger(??)
multiprocessing.heap(??)
multiprocessing.heap.Arena(??)
multiprocessing.heap.BufferWrapper(??)
multiprocessing.heap.Finalize(??)
multiprocessing.heap.Heap(??)
multiprocessing.heap.assert_spawning(??)
multiprocessing.heap.bisect(??)
multiprocessing.heap.info(??)
multiprocessing.heap.itertools(??)
multiprocessing.heap.mmap(??)
multiprocessing.heap.os(??)
multiprocessing.heap.sys(??)
multiprocessing.heap.tempfile(??)
multiprocessing.heap.threading(??)
multiprocessing.log_to_stderr(??)
multiprocessing.managers(??)
multiprocessing.managers.AcquirerProxy(??)
multiprocessing.managers.Array(??)
multiprocessing.managers.ArrayProxy(??)
multiprocessing.managers.AuthenticationString(??)
multiprocessing.managers.AutoProxy(??)
multiprocessing.managers.BaseListProxy(??)
multiprocessing.managers.BaseManager(??)
multiprocessing.managers.BaseProxy(??)
multiprocessing.managers.ConditionProxy(??)
multiprocessing.managers.DictProxy(??)
multiprocessing.managers.EventProxy(??)
multiprocessing.managers.Finalize(??)
multiprocessing.managers.ForkingPickler(??)
multiprocessing.managers.IteratorProxy(??)
multiprocessing.managers.ListProxy(??)
multiprocessing.managers.MakeProxyType(??)
multiprocessing.managers.Namespace(??)
multiprocessing.managers.NamespaceProxy(??)
multiprocessing.managers.PicklingError(??)
multiprocessing.managers.Pool(??)
multiprocessing.managers.PoolProxy(??)
multiprocessing.managers.Popen(??)
multiprocessing.managers.Process(??)
multiprocessing.managers.ProcessLocalSet(??)
multiprocessing.managers.RebuildProxy(??)
multiprocessing.managers.RemoteError(??)
multiprocessing.managers.Server(??)
multiprocessing.managers.State(??)
multiprocessing.managers.SyncManager(??)
multiprocessing.managers.Token(??)
multiprocessing.managers.Value(??)
multiprocessing.managers.ValueProxy(??)
multiprocessing.managers.active_children(??)
multiprocessing.managers.all_methods(??)
multiprocessing.managers.array(??)
multiprocessing.managers.assert_spawning(??)
multiprocessing.managers.connection(??)
multiprocessing.managers.convert_to_error(??)
multiprocessing.managers.copyreg(??)
multiprocessing.managers.current_process(??)
multiprocessing.managers.dispatch(??)
multiprocessing.managers.exit(??)
multiprocessing.managers.format_exc(??)
multiprocessing.managers.info(??)
multiprocessing.managers.listener_client(??)
multiprocessing.managers.os(??)
multiprocessing.managers.public_methods(??)
multiprocessing.managers.queue(??)
multiprocessing.managers.rebuild_as_list(??)
multiprocessing.managers.reduce_array(??)
multiprocessing.managers.sys(??)
multiprocessing.managers.threading(??)
multiprocessing.managers.util(??)
multiprocessing.managers.view_type(??)
multiprocessing.managers.view_types(??)
multiprocessing.managers.weakref(??)
multiprocessing.os(??)
multiprocessing.pool(??)
multiprocessing.pool.ApplyResult(??)
multiprocessing.pool.CLOSE
multiprocessing.pool.Finalize(??)
multiprocessing.pool.IMapIterator(??)
multiprocessing.pool.IMapUnorderedIterator(??)
multiprocessing.pool.MapResult(??)
multiprocessing.pool.MaybeEncodingError(??)
multiprocessing.pool.Pool(??)
multiprocessing.pool.Process(??)
multiprocessing.pool.RUN
multiprocessing.pool.TERMINATE
multiprocessing.pool.ThreadPool(??)
multiprocessing.pool.TimeoutError(??)
multiprocessing.pool.collections(??)
multiprocessing.pool.cpu_count(??)
multiprocessing.pool.debug(??)
multiprocessing.pool.itertools(??)
multiprocessing.pool.job_counter(??)
multiprocessing.pool.mapstar(??)
multiprocessing.pool.queue(??)
multiprocessing.pool.threading(??)
multiprocessing.pool.time(??)
multiprocessing.pool.worker(??)
multiprocessing.process(??)
multiprocessing.process.AuthenticationString(??)
multiprocessing.process.ORIGINAL_DIR
multiprocessing.process.Process(??)
multiprocessing.process.active_children(??)
multiprocessing.process.current_process(??)
multiprocessing.process.itertools(??)
multiprocessing.process.name
multiprocessing.process.os(??)
multiprocessing.process.signal(??)
multiprocessing.process.signum(??)
multiprocessing.process.sys(??)
multiprocessing.queues(??)
multiprocessing.queues.BoundedSemaphore(??)
multiprocessing.queues.Condition(??)
multiprocessing.queues.Empty(??)
multiprocessing.queues.Finalize(??)
multiprocessing.queues.Full(??)
multiprocessing.queues.JoinableQueue(??)
multiprocessing.queues.Lock(??)
multiprocessing.queues.Pipe(??)
multiprocessing.queues.Queue(??)
multiprocessing.queues.Semaphore(??)
multiprocessing.queues.SimpleQueue(??)
multiprocessing.queues.assert_spawning(??)
multiprocessing.queues.atexit(??)
multiprocessing.queues.collections(??)
multiprocessing.queues.debug(??)
multiprocessing.queues.info(??)
multiprocessing.queues.os(??)
multiprocessing.queues.register_after_fork(??)
multiprocessing.queues.sys(??)
multiprocessing.queues.threading(??)
multiprocessing.queues.time(??)
multiprocessing.queues.weakref(??)
multiprocessing.reduction(??)
multiprocessing.reduction.Client(??)
multiprocessing.reduction.ForkingPickler(??)
multiprocessing.reduction.Listener(??)
multiprocessing.reduction.Popen(??)
multiprocessing.reduction.close(fd)
multiprocessing.reduction.current_process(??)
multiprocessing.reduction.debug(??)
multiprocessing.reduction.duplicate(??)
multiprocessing.reduction.fromfd(??)
multiprocessing.reduction.os(??)
multiprocessing.reduction.rebuild_connection(??)
multiprocessing.reduction.rebuild_handle(??)
multiprocessing.reduction.rebuild_socket(??)
multiprocessing.reduction.recv_handle(??)
multiprocessing.reduction.reduce_connection(??)
multiprocessing.reduction.reduce_handle(??)
multiprocessing.reduction.reduce_socket(??)
multiprocessing.reduction.register_after_fork(??)
multiprocessing.reduction.send_handle(??)
multiprocessing.reduction.socket(??)
multiprocessing.reduction.sub_debug(??)
multiprocessing.reduction.sys(??)
multiprocessing.reduction.threading(??)
multiprocessing.sharedctypes(??)
multiprocessing.sharedctypes.Array(??)
multiprocessing.sharedctypes.ForkingPickler(??)
multiprocessing.sharedctypes.RLock(??)
multiprocessing.sharedctypes.RawArray(??)
multiprocessing.sharedctypes.RawValue(??)
multiprocessing.sharedctypes.Synchronized(??)
multiprocessing.sharedctypes.SynchronizedArray(??)
multiprocessing.sharedctypes.SynchronizedBase(??)
multiprocessing.sharedctypes.SynchronizedString(??)
multiprocessing.sharedctypes.Value(??)
multiprocessing.sharedctypes.assert_spawning(??)
multiprocessing.sharedctypes.class_cache(??)
multiprocessing.sharedctypes.copy(??)
multiprocessing.sharedctypes.ctypes(??)
multiprocessing.sharedctypes.heap(??)
multiprocessing.sharedctypes.make_property(??)
multiprocessing.sharedctypes.prop_cache(??)
multiprocessing.sharedctypes.rebuild_ctype(??)
multiprocessing.sharedctypes.reduce_ctype(??)
multiprocessing.sharedctypes.synchronized(??)
multiprocessing.sharedctypes.sys(??)
multiprocessing.sharedctypes.template
multiprocessing.sharedctypes.typecode_to_type(??)
multiprocessing.sharedctypes.weakref(??)
multiprocessing.synchronize(??)
multiprocessing.synchronize.BoundedSemaphore(??)
multiprocessing.synchronize.Condition(??)
multiprocessing.synchronize.Event(??)
multiprocessing.synchronize.Finalize(??)
multiprocessing.synchronize.Lock(??)
multiprocessing.synchronize.Popen(??)
multiprocessing.synchronize.RECURSIVE_MUTEX
multiprocessing.synchronize.RLock(??)
multiprocessing.synchronize.SEMAPHORE
multiprocessing.synchronize.SEM_VALUE_MAX
multiprocessing.synchronize.SemLock(??)
multiprocessing.synchronize.Semaphore(??)
multiprocessing.synchronize.assert_spawning(??)
multiprocessing.synchronize.current_process(??)
multiprocessing.synchronize.debug(??)
multiprocessing.synchronize.os(??)
multiprocessing.synchronize.register_after_fork(??)
multiprocessing.synchronize.sys(??)
multiprocessing.synchronize.threading(??)
multiprocessing.sys(??)
multiprocessing.util(??)
multiprocessing.util.DEBUG
multiprocessing.util.DEFAULT_LOGGING_FORMAT
multiprocessing.util.Finalize(??)
multiprocessing.util.ForkAwareLocal(??)
multiprocessing.util.ForkAwareThreadLock(??)
multiprocessing.util.INFO
multiprocessing.util.LOGGER_NAME
multiprocessing.util.NOTSET
multiprocessing.util.SUBDEBUG
multiprocessing.util.SUBWARNING
multiprocessing.util.active_children(??)
multiprocessing.util.atexit(??)
multiprocessing.util.current_process(??)
multiprocessing.util.debug(??)
multiprocessing.util.get_logger(??)
multiprocessing.util.get_temp_dir(??)
multiprocessing.util.info(??)
multiprocessing.util.is_exiting(??)
multiprocessing.util.itertools(??)
multiprocessing.util.log_to_stderr(??)
multiprocessing.util.register_after_fork(??)
multiprocessing.util.sub_debug(??)
multiprocessing.util.sub_warning(??)
multiprocessing.util.threading(??)
multiprocessing.util.weakref(??)
netrc.NetrcParseError(??)
netrc.netrc(??)
netrc.os(??)
netrc.shlex(??)
next(iterator[, default])
nntplib.ArticleInfo(number, message_id, lines)
nntplib.GroupInfo(group, last, first, flag)
nntplib.NNTP(??)
nntplib.NNTPDataError(??)
nntplib.NNTPError(??)
nntplib.NNTPPermanentError(??)
nntplib.NNTPProtocolError(??)
nntplib.NNTPReplyError(??)
nntplib.NNTPTemporaryError(??)
nntplib.NNTP_PORT
nntplib.NNTP_SSL(??)
nntplib.NNTP_SSL_PORT
nntplib.collections(??)
nntplib.datetime(??)
nntplib.decode_header(??)
nntplib.re(??)
nntplib.socket(??)
nntplib.ssl(??)
nntplib.warnings(??)
numbers.ABCMeta(??)
numbers.Complex(??)
numbers.Integral(??)
numbers.Number(??)
numbers.Rational(??)
numbers.Real(??)
numbers.abstractmethod(??)
numbers.abstractproperty(??)
object(??)
oct(number) -> string
open(??)
operator.abs(a) -- Same as abs(a).
operator.add(a, b) -- Same as a + b.
operator.and_(a, b) -- Same as a & b.
operator.attrgetter(attr, ...) --> attrgetter object
operator.concat(a, b) -- Same as a + b, for a and b sequences.
operator.contains(a, b) -- Same as b in a (note reversed operands).
operator.countOf(a, b) -- Return the number of times b occurs in a.
operator.delitem(a, b) -- Same as del a[b].
operator.eq(a, b) -- Same as a==b.
operator.floordiv(a, b) -- Same as a // b.
operator.ge(a, b) -- Same as a>=b.
operator.getitem(a, b) -- Same as a[b].
operator.gt(a, b) -- Same as a>b.
operator.iadd(??)
operator.iand(??)
operator.iconcat(??)
operator.ifloordiv(??)
operator.ilshift(??)
operator.imod(??)
operator.imul(??)
operator.index(a) -- Same as a.__index__()
operator.indexOf(a, b) -- Return the first index of b in a.
operator.inv(a) -- Same as ~a.
operator.invert(a) -- Same as ~a.
operator.ior(??)
operator.ipow(??)
operator.irshift(??)
operator.is_(a, b) -- Same as a is b.
operator.is_not(a, b) -- Same as a is not b.
operator.isub(??)
operator.itemgetter(item, ...) --> itemgetter object
operator.itruediv(??)
operator.ixor(??)
operator.le(a, b) -- Same as a<=b.
operator.lshift(a, b) -- Same as a << b.
operator.lt(a, b) -- Same as a<b.
operator.methodcaller(name, ...) --> methodcaller object
operator.mod(a, b) -- Same as a % b.
operator.mul(a, b) -- Same as a * b.
operator.ne(a, b) -- Same as a!=b.
operator.neg(a) -- Same as -a.
operator.not_(a) -- Same as not a.
operator.or_(a, b) -- Same as a | b.
operator.pos(a) -- Same as +a.
operator.pow(a, b) -- Same as a ** b.
operator.rshift(a, b) -- Same as a >> b.
operator.setitem(a, b, c) -- Same as a[b] = c.
operator.sub(a, b) -- Same as a - b.
operator.truediv(a, b) -- Same as a / b.
operator.truth(a) -- Return True if a is true, False otherwise.
operator.xor(a, b) -- Same as a ^ b.
optparse.AmbiguousOptionError(??)
optparse.BadOptionError(??)
optparse.HelpFormatter(??)
optparse.IndentedHelpFormatter(??)
optparse.NO_DEFAULT(??)
optparse.OptParseError(??)
optparse.Option(??)
optparse.OptionConflictError(??)
optparse.OptionContainer(??)
optparse.OptionError(??)
optparse.OptionGroup(??)
optparse.OptionParser(??)
optparse.OptionValueError(??)
optparse.SUPPRESS_HELP
optparse.SUPPRESS_USAGE
optparse.TitledHelpFormatter(??)
optparse.Values(??)
optparse.check_builtin(??)
optparse.check_choice(??)
optparse.gettext(??)
optparse.make_option(??)
optparse.os(??)
optparse.sys(??)
optparse.textwrap(??)
ord(c) -> integer
os.EX_CANTCREAT
os.EX_CONFIG
os.EX_DATAERR
os.EX_IOERR
os.EX_NOHOST
os.EX_NOINPUT
os.EX_NOPERM
os.EX_NOUSER
os.EX_OK
os.EX_OSERR
os.EX_OSFILE
os.EX_PROTOCOL
os.EX_SOFTWARE
os.EX_TEMPFAIL
os.EX_UNAVAILABLE
os.EX_USAGE
os.F_OK
os.MutableMapping(??)
os.NGROUPS_MAX
os.O_APPEND
os.O_ASYNC
os.O_CREAT
os.O_DIRECT
os.O_DIRECTORY
os.O_DSYNC
os.O_EXCL
os.O_LARGEFILE
os.O_NDELAY
os.O_NOATIME
os.O_NOCTTY
os.O_NOFOLLOW
os.O_NONBLOCK
os.O_RDONLY
os.O_RDWR
os.O_RSYNC
os.O_SYNC
os.O_TRUNC
os.O_WRONLY
os.P_NOWAIT
os.P_NOWAITO
os.P_WAIT
os.R_OK
os.SEEK_CUR
os.SEEK_END
os.SEEK_SET
os.ST_NOSUID
os.ST_RDONLY
os.TMP_MAX
os.WCONTINUED
os.WCOREDUMP(status) -> bool
os.WEXITSTATUS(status) -> integer
os.WIFCONTINUED(status) -> bool
os.WIFEXITED(status) -> bool
os.WIFSIGNALED(status) -> bool
os.WIFSTOPPED(status) -> bool
os.WNOHANG
os.WSTOPSIG(status) -> integer
os.WTERMSIG(status) -> integer
os.WUNTRACED
os.W_OK
os.X_OK
os.abort() -> does not return!
os.access(path, mode) -> True if granted, False otherwise
os.altsep(??)
os.chdir(path)
os.chmod(path, mode)
os.chown(path, uid, gid)
os.chroot(path)
os.close(fd)
os.closerange(fd_low, fd_high)
os.confstr(name) -> string
os.confstr_names(??)
os.ctermid() -> string
os.curdir
os.defpath
os.device_encoding(fd) -> str
os.devnull
os.dup(fd) -> fd2
os.dup2(old_fd, new_fd)
os.environ(??)
os.environb(??)
os.errno(??)
os.error(??)
os.execl(file, *args)
os.execle(file, *args, env)
os.execlp(file, *args)
os.execlpe(file, *args, env)
os.execv(path, args)
os.execve(path, args, env)
os.execvp(file, args)
os.execvpe(file, args, env)
os.extsep
os.fchdir(fildes)
os.fchmod(fd, mode)
os.fchown(fd, uid, gid)
os.fdatasync(fildes)
os.fdopen(??)
os.fork() -> pid
os.forkpty() -> (pid, master_fd)
os.fpathconf(fd, name) -> integer
os.fsdecode(??)
os.fsencode(??)
os.fstat(fd) -> stat result
os.fstatvfs(fd) -> statvfs result
os.fsync(fildes)
os.ftruncate(fd, length)
os.get_exec_path(??)
os.getcwd() -> path
os.getcwdb() -> path
os.getegid() -> egid
os.getenv(??)
os.getenvb(??)
os.geteuid() -> euid
os.getgid() -> gid
os.getgroups() -> list of group IDs
os.getloadavg() -> (float, float, float)
os.getlogin() -> string
os.getpgid(pid) -> pgid
os.getpgrp() -> pgrp
os.getpid() -> pid
os.getppid() -> ppid
os.getresgid() -> (rgid, egid, sgid)
os.getresuid() -> (ruid, euid, suid)
os.getsid(pid) -> sid
os.getuid() -> uid
os.initgroups(username, gid) -> None
os.isatty(fd) -> bool
os.kill(pid, sig)
os.killpg(pgid, sig)
os.lchown(path, uid, gid)
os.linesep
os.link(src, dst)
os.listdir([path]) -> list_of_strings
os.lseek(fd, pos, how) -> newpos
os.lstat(path) -> stat result
os.major(device) -> major number
os.makedev(major, minor) -> device number
os.makedirs(path [, mode=0o777][, exist_ok=False])
os.minor(device) -> minor number
os.mkdir(path [, mode=0777])
os.mkfifo(filename [, mode=0666])
os.mknod(filename [, mode=0600, device])
os.name
os.nice(inc) -> new_priority
os.open(filename, flag [, mode=0777]) -> fd
os.openpty() -> (master_fd, slave_fd)
os.pardir
os.path(??)
os.pathconf(path, name) -> integer
os.pathconf_names(??)
os.pathsep
os.pipe() -> (read_end, write_end)
os.popen(??)
os.putenv(key, value)
os.read(fd, buffersize) -> string
os.readlink(path) -> path
os.remove(path)
os.removedirs(path)
os.rename(old, new)
os.renames(old, new)
os.rmdir(path)
os.sep
os.setegid(gid)
os.seteuid(uid)
os.setgid(gid)
os.setgroups(list)
os.setpgid(pid, pgrp)
os.setpgrp()
os.setregid(rgid, egid)
os.setresgid(rgid, egid, sgid)
os.setresuid(ruid, euid, suid)
os.setreuid(ruid, euid)
os.setsid()
os.setuid(uid)
os.spawnl(mode, file, *args) -> integer
os.spawnle(mode, file, *args, env) -> integer
os.spawnlp(mode, file, *args) -> integer
os.spawnlpe(mode, file, *args, env) -> integer
os.spawnv(mode, file, args) -> integer
os.spawnve(mode, file, args, env) -> integer
os.spawnvp(mode, file, args) -> integer
os.spawnvpe(mode, file, args, env) -> integer
os.stat(path) -> stat result
os.stat_float_times([newval]) -> oldval
os.stat_result(??)
os.statvfs(path) -> statvfs result
os.statvfs_result(??)
os.strerror(code) -> string
os.supports_bytes_environ(??)
os.symlink(src, dst)
os.sys(??)
os.sysconf(name) -> integer
os.sysconf_names(??)
os.system(command) -> exit_status
os.tcgetpgrp(fd) -> pgid
os.tcsetpgrp(fd, pgid)
os.times() -> (utime, stime, cutime, cstime, elapsed_time)
os.ttyname(fd) -> string
os.umask(new_mask) -> old_mask
os.uname() -> (sysname, nodename, release, version, machine)
os.unlink(path)
os.unsetenv(key)
os.urandom(n) -> str
os.utime(path, (atime, mtime))
os.wait() -> (pid, status)
os.wait3(options) -> (pid, status, rusage)
os.wait4(pid, options) -> (pid, status, rusage)
os.waitpid(pid, options) -> (pid, status)
os.walk(??)
os.write(fd, string) -> byteswritten
pdb.Pdb(??)
pdb.Restart(??)
pdb.TESTCMD
pdb.bdb(??)
pdb.cmd(??)
pdb.code(??)
pdb.dis(??)
pdb.find_function(??)
pdb.getsourcelines(??)
pdb.help(??)
pdb.inspect(??)
pdb.lasti2lineno(??)
pdb.line_prefix
pdb.linecache(??)
pdb.main(??)
pdb.os(??)
pdb.pm(??)
pdb.post_mortem(??)
pdb.pprint(??)
pdb.re(??)
pdb.run(??)
pdb.runcall(??)
pdb.runctx(??)
pdb.runeval(??)
pdb.set_trace(??)
pdb.signal(??)
pdb.sys(??)
pdb.test(??)
pdb.traceback(??)
pickle.APPEND
pickle.APPENDS
pickle.BINBYTES
pickle.BINFLOAT
pickle.BINGET
pickle.BININT
pickle.BININT1
pickle.BININT2
pickle.BINPERSID
pickle.BINPUT
pickle.BINSTRING
pickle.BINUNICODE
pickle.BUILD
pickle.BuiltinFunctionType(??)
pickle.DEFAULT_PROTOCOL
pickle.DICT
pickle.DUP
pickle.EMPTY_DICT
pickle.EMPTY_LIST
pickle.EMPTY_TUPLE
pickle.EXT1
pickle.EXT2
pickle.EXT4
pickle.FALSE
pickle.FLOAT
pickle.FunctionType(??)
pickle.GET
pickle.GLOBAL
pickle.HIGHEST_PROTOCOL
pickle.INST
pickle.INT
pickle.LIST
pickle.LONG
pickle.LONG1
pickle.LONG4
pickle.LONG_BINGET
pickle.LONG_BINPUT
pickle.MARK
pickle.NEWFALSE
pickle.NEWOBJ
pickle.NEWTRUE
pickle.NONE
pickle.OBJ
pickle.PERSID
pickle.POP
pickle.POP_MARK
pickle.PROTO
pickle.PUT
pickle.PickleError(??)
pickle.Pickler(file, protocol=None)
pickle.PicklingError(??)
pickle.PyStringMap(??)
pickle.REDUCE
pickle.SETITEM
pickle.SETITEMS
pickle.SHORT_BINBYTES
pickle.SHORT_BINSTRING
pickle.STOP
pickle.STRING
pickle.TRUE
pickle.TUPLE
pickle.TUPLE1
pickle.TUPLE2
pickle.TUPLE3
pickle.UNICODE
pickle.Unpickler(file, *, encoding='ASCII', errors='strict')
pickle.UnpicklingError(??)
pickle.bytes_types(??)
pickle.classmap(??)
pickle.codecs(??)
pickle.compatible_formats(??)
pickle.decode_long(??)
pickle.dispatch_table(??)
pickle.dump(obj, file, protocol=None, *, fix_imports=True) -> None
pickle.dumps(obj, protocol=None, *, fix_imports=True) -> bytes
pickle.encode_long(??)
pickle.format_version
pickle.io(??)
pickle.load(file, *, fix_imports=True, encoding='ASCII', errors='strict') -> object
pickle.loads(input, *, fix_imports=True, encoding='ASCII', errors='strict') -> object
pickle.marshal(??)
pickle.mloads(??)
pickle.re(??)
pickle.struct(??)
pickle.sys(??)
pickle.whichmodule(??)
pickletools.ArgumentDescriptor(??)
pickletools.OpcodeInfo(??)
pickletools.StackObject(??)
pickletools.TAKEN_FROM_ARGUMENT1
pickletools.TAKEN_FROM_ARGUMENT4
pickletools.UP_TO_NEWLINE
pickletools.anyobject(??)
pickletools.bytes_types(??)
pickletools.code2op(??)
pickletools.codecs(??)
pickletools.decimalnl_long(??)
pickletools.decimalnl_short(??)
pickletools.decode_long(??)
pickletools.dis(??)
pickletools.float8(??)
pickletools.floatnl(??)
pickletools.genops(??)
pickletools.int4(??)
pickletools.long1(??)
pickletools.long4(??)
pickletools.markobject(??)
pickletools.opcodes(??)
pickletools.optimize(??)
pickletools.pickle(??)
pickletools.pybool(??)
pickletools.pybytes(??)
pickletools.pydict(??)
pickletools.pyfloat(??)
pickletools.pyint(??)
pickletools.pyinteger_or_bool(??)
pickletools.pylist(??)
pickletools.pylong(??)
pickletools.pynone(??)
pickletools.pystring(??)
pickletools.pytuple(??)
pickletools.pyunicode(??)
pickletools.re(??)
pickletools.read_decimalnl_long(??)
pickletools.read_decimalnl_short(??)
pickletools.read_float8(??)
pickletools.read_floatnl(??)
pickletools.read_int4(??)
pickletools.read_long1(??)
pickletools.read_long4(??)
pickletools.read_string1(??)
pickletools.read_string4(??)
pickletools.read_stringnl(??)
pickletools.read_stringnl_noescape(??)
pickletools.read_stringnl_noescape_pair(??)
pickletools.read_uint1(??)
pickletools.read_uint2(??)
pickletools.read_unicodestring4(??)
pickletools.read_unicodestringnl(??)
pickletools.stackslice(??)
pickletools.string1(??)
pickletools.string4(??)
pickletools.stringnl(??)
pickletools.stringnl_noescape(??)
pickletools.stringnl_noescape_pair(??)
pickletools.uint1(??)
pickletools.uint2(??)
pickletools.unicodestring4(??)
pickletools.unicodestringnl(??)
pipes.FILEIN_FILEOUT
pipes.FILEIN_STDOUT
pipes.SINK
pipes.SOURCE
pipes.STDIN_FILEOUT
pipes.STDIN_STDOUT
pipes.Template(??)
pipes.makepipeline(??)
pipes.os(??)
pipes.quote(??)
pipes.re(??)
pipes.stepkinds(??)
pipes.string(??)
pipes.tempfile(??)
pkgutil.ImpImporter(??)
pkgutil.ImpLoader(??)
pkgutil.ModuleType(??)
pkgutil.extend_path(??)
pkgutil.find_loader(??)
pkgutil.get_data(??)
pkgutil.get_importer(??)
pkgutil.get_loader(??)
pkgutil.imp(??)
pkgutil.iter_importer_modules(??)
pkgutil.iter_importers(??)
pkgutil.iter_modules(??)
pkgutil.iter_zipimport_modules(??)
pkgutil.os(??)
pkgutil.read_code(??)
pkgutil.simplegeneric(??)
pkgutil.sys(??)
pkgutil.walk_packages(??)
pkgutil.zipimport(??)
pkgutil.zipimporter(archivepath) -> zipimporter object
platform.DEV_NULL
platform.architecture(??)
platform.dist(??)
platform.java_ver(??)
platform.libc_ver(??)
platform.linux_distribution(??)
platform.mac_ver(??)
platform.machine(??)
platform.node(??)
platform.os(??)
platform.platform(??)
platform.popen(??)
platform.processor(??)
platform.python_branch(??)
platform.python_build(??)
platform.python_compiler(??)
platform.python_implementation(??)
platform.python_revision(??)
platform.python_version(??)
platform.python_version_tuple(??)
platform.re(??)
platform.release(??)
platform.sys(??)
platform.system(??)
platform.system_alias(??)
platform.uname(??)
platform.version(??)
platform.win32_ver(??)
plistlib.BytesIO([buffer]) -> object
plistlib.Data(??)
plistlib.Dict(??)
plistlib.DumbXMLWriter(??)
plistlib.PLISTHEADER
plistlib.Plist(??)
plistlib.PlistParser(??)
plistlib.PlistWriter(??)
plistlib.binascii(??)
plistlib.datetime(??)
plistlib.re(??)
plistlib.readPlist(??)
plistlib.readPlistFromBytes(??)
plistlib.writePlist(??)
plistlib.writePlistToBytes(??)
poplib.CR
poplib.CRLF
poplib.LF
poplib.POP3(??)
poplib.POP3_PORT
poplib.POP3_SSL(??)
poplib.POP3_SSL_PORT
poplib.error_proto(??)
poplib.re(??)
poplib.socket(??)
poplib.ssl(??)
posix.EX_CANTCREAT
posix.EX_CONFIG
posix.EX_DATAERR
posix.EX_IOERR
posix.EX_NOHOST
posix.EX_NOINPUT
posix.EX_NOPERM
posix.EX_NOUSER
posix.EX_OK
posix.EX_OSERR
posix.EX_OSFILE
posix.EX_PROTOCOL
posix.EX_SOFTWARE
posix.EX_TEMPFAIL
posix.EX_UNAVAILABLE
posix.EX_USAGE
posix.F_OK
posix.NGROUPS_MAX
posix.O_APPEND
posix.O_ASYNC
posix.O_CREAT
posix.O_DIRECT
posix.O_DIRECTORY
posix.O_DSYNC
posix.O_EXCL
posix.O_LARGEFILE
posix.O_NDELAY
posix.O_NOATIME
posix.O_NOCTTY
posix.O_NOFOLLOW
posix.O_NONBLOCK
posix.O_RDONLY
posix.O_RDWR
posix.O_RSYNC
posix.O_SYNC
posix.O_TRUNC
posix.O_WRONLY
posix.R_OK
posix.ST_NOSUID
posix.ST_RDONLY
posix.TMP_MAX
posix.WCONTINUED
posix.WCOREDUMP(status) -> bool
posix.WEXITSTATUS(status) -> integer
posix.WIFCONTINUED(status) -> bool
posix.WIFEXITED(status) -> bool
posix.WIFSIGNALED(status) -> bool
posix.WIFSTOPPED(status) -> bool
posix.WNOHANG
posix.WSTOPSIG(status) -> integer
posix.WTERMSIG(status) -> integer
posix.WUNTRACED
posix.W_OK
posix.X_OK
posix.abort() -> does not return!
posix.access(path, mode) -> True if granted, False otherwise
posix.chdir(path)
posix.chmod(path, mode)
posix.chown(path, uid, gid)
posix.chroot(path)
posix.close(fd)
posix.closerange(fd_low, fd_high)
posix.confstr(name) -> string
posix.confstr_names(??)
posix.ctermid() -> string
posix.device_encoding(fd) -> str
posix.dup(fd) -> fd2
posix.dup2(old_fd, new_fd)
posix.environ(??)
posix.error(??)
posix.execv(path, args)
posix.execve(path, args, env)
posix.fchdir(fildes)
posix.fchmod(fd, mode)
posix.fchown(fd, uid, gid)
posix.fdatasync(fildes)
posix.fork() -> pid
posix.forkpty() -> (pid, master_fd)
posix.fpathconf(fd, name) -> integer
posix.fstat(fd) -> stat result
posix.fstatvfs(fd) -> statvfs result
posix.fsync(fildes)
posix.ftruncate(fd, length)
posix.getcwd() -> path
posix.getcwdb() -> path
posix.getegid() -> egid
posix.geteuid() -> euid
posix.getgid() -> gid
posix.getgroups() -> list of group IDs
posix.getloadavg() -> (float, float, float)
posix.getlogin() -> string
posix.getpgid(pid) -> pgid
posix.getpgrp() -> pgrp
posix.getpid() -> pid
posix.getppid() -> ppid
posix.getresgid() -> (rgid, egid, sgid)
posix.getresuid() -> (ruid, euid, suid)
posix.getsid(pid) -> sid
posix.getuid() -> uid
posix.initgroups(username, gid) -> None
posix.isatty(fd) -> bool
posix.kill(pid, sig)
posix.killpg(pgid, sig)
posix.lchown(path, uid, gid)
posix.link(src, dst)
posix.listdir([path]) -> list_of_strings
posix.lseek(fd, pos, how) -> newpos
posix.lstat(path) -> stat result
posix.major(device) -> major number
posix.makedev(major, minor) -> device number
posix.minor(device) -> minor number
posix.mkdir(path [, mode=0777])
posix.mkfifo(filename [, mode=0666])
posix.mknod(filename [, mode=0600, device])
posix.nice(inc) -> new_priority
posix.open(filename, flag [, mode=0777]) -> fd
posix.openpty() -> (master_fd, slave_fd)
posix.pathconf(path, name) -> integer
posix.pathconf_names(??)
posix.pipe() -> (read_end, write_end)
posix.putenv(key, value)
posix.read(fd, buffersize) -> string
posix.readlink(path) -> path
posix.remove(path)
posix.rename(old, new)
posix.rmdir(path)
posix.setegid(gid)
posix.seteuid(uid)
posix.setgid(gid)
posix.setgroups(list)
posix.setpgid(pid, pgrp)
posix.setpgrp()
posix.setregid(rgid, egid)
posix.setresgid(rgid, egid, sgid)
posix.setresuid(ruid, euid, suid)
posix.setreuid(ruid, euid)
posix.setsid()
posix.setuid(uid)
posix.stat(path) -> stat result
posix.stat_float_times([newval]) -> oldval
posix.stat_result(??)
posix.statvfs(path) -> statvfs result
posix.statvfs_result(??)
posix.strerror(code) -> string
posix.symlink(src, dst)
posix.sysconf(name) -> integer
posix.sysconf_names(??)
posix.system(command) -> exit_status
posix.tcgetpgrp(fd) -> pgid
posix.tcsetpgrp(fd, pgid)
posix.times() -> (utime, stime, cutime, cstime, elapsed_time)
posix.ttyname(fd) -> string
posix.umask(new_mask) -> old_mask
posix.uname() -> (sysname, nodename, release, version, machine)
posix.unlink(path)
posix.unsetenv(key)
posix.utime(path, (atime, mtime))
posix.wait() -> (pid, status)
posix.wait3(options) -> (pid, status, rusage)
posix.wait4(pid, options) -> (pid, status, rusage)
posix.waitpid(pid, options) -> (pid, status)
posix.write(fd, string) -> byteswritten
pow(x, y[, z]) -> number
pprint.PrettyPrinter(??)
pprint.isreadable(??)
pprint.isrecursive(??)
pprint.pformat(??)
pprint.pprint(??)
pprint.saferepr(??)
print(value, ..., sep=' ', end='\n', file=sys.stdout)
profile.OptionParser(??)
profile.Profile(??)
profile.main(??)
profile.marshal(??)
profile.os(??)
profile.resgetrusage(??)
profile.resource(??)
profile.run(??)
profile.runctx(??)
profile.sys(??)
profile.time(??)
property(fget=None, fset=None, fdel=None, doc=None) -> property attribute
pstats.Stats(??)
pstats.TupleComp(??)
pstats.add_callers(??)
pstats.add_func_stats(??)
pstats.cmp_to_key(??)
pstats.count_calls(??)
pstats.f8(??)
pstats.func_get_function_name(??)
pstats.func_std_string(??)
pstats.func_strip_path(??)
pstats.marshal(??)
pstats.os(??)
pstats.re(??)
pstats.sys(??)
pstats.time(??)
pty.CHILD
pty.STDERR_FILENO
pty.STDIN_FILENO
pty.STDOUT_FILENO
pty.fork() -> (pid, master_fd)
pty.master_open() -> (master_fd, slave_name)
pty.openpty() -> (master_fd, slave_fd)
pty.os(??)
pty.select(rlist, wlist, xlist[, timeout]) -> (rlist, wlist, xlist)
pty.slave_open(tty_name) -> slave_fd
pty.spawn(??)
pty.tty(??)
pwd.getpwall() -> list_of_entries
pwd.getpwnam(name) -> (pw_name,pw_passwd,pw_uid,
pwd.getpwuid(uid) -> (pw_name,pw_passwd,pw_uid,
pwd.struct_passwd(??)
py_compile.MAGIC
py_compile.PyCompileError(??)
py_compile.builtins(??)
py_compile.compile(??)
py_compile.errno(??)
py_compile.imp(??)
py_compile.main(??)
py_compile.marshal(??)
py_compile.os(??)
py_compile.sys(??)
py_compile.tokenize(??)
py_compile.traceback(??)
py_compile.wr_long(??)
pyclbr.Class(??)
pyclbr.DEDENT
pyclbr.Function(??)
pyclbr.NAME
pyclbr.OP
pyclbr.imp(??)
pyclbr.itemgetter(item, ...) --> itemgetter object
pyclbr.readmodule(??)
pyclbr.readmodule_ex(??)
pyclbr.sys(??)
pyclbr.tokenize(??)
pydoc.Doc(??)
pydoc.ErrorDuringImport(??)
pydoc.HTMLDoc(??)
pydoc.HTMLRepr(??)
pydoc.Helper(??)
pydoc.ModuleScanner(??)
pydoc.Repr(??)
pydoc.Scanner(??)
pydoc.TextDoc(??)
pydoc.TextRepr(??)
pydoc.allmethods(??)
pydoc.apropos(??)
pydoc.browse(??)
pydoc.builtins(??)
pydoc.classify_class_attrs(??)
pydoc.classname(??)
pydoc.cli(??)
pydoc.cram(??)
pydoc.deque(iterable[, maxlen]) --> deque object
pydoc.describe(??)
pydoc.doc(??)
pydoc.extract_tb(??)
pydoc.format_exception_only(??)
pydoc.getdoc(??)
pydoc.getpager(??)
pydoc.gui(??)
pydoc.help(??)
pydoc.html(??)
pydoc.imp(??)
pydoc.importfile(??)
pydoc.inspect(??)
pydoc.io(??)
pydoc.isdata(??)
pydoc.ispackage(??)
pydoc.ispath(??)
pydoc.locate(??)
pydoc.os(??)
pydoc.pager(??)
pydoc.pathdirs(??)
pydoc.pipepager(??)
pydoc.pkgutil(??)
pydoc.plain(??)
pydoc.plainpager(??)
pydoc.plaintext(??)
pydoc.platform(??)
pydoc.re(??)
pydoc.render_doc(??)
pydoc.replace(??)
pydoc.resolve(??)
pydoc.safeimport(??)
pydoc.serve(??)
pydoc.source_synopsis(??)
pydoc.splitdoc(??)
pydoc.stripid(??)
pydoc.synopsis(??)
pydoc.sys(??)
pydoc.tempfilepager(??)
pydoc.text(??)
pydoc.time(??)
pydoc.ttypager(??)
pydoc.visiblename(??)
pydoc.warnings(??)
pydoc.writedoc(??)
pydoc.writedocs(??)
pydoc_data.topics(??)
pydoc_data.topics.topics(??)
queue.Empty(??)
queue.Full(??)
queue.LifoQueue(??)
queue.PriorityQueue(??)
queue.Queue(??)
queue.deque(iterable[, maxlen]) --> deque object
queue.heapq(??)
quit(??)
quopri.EMPTYSTRING
quopri.ESCAPE
quopri.HEX
quopri.MAXLINESIZE
quopri.a2b_qp(??)
quopri.b2a_qp(data, quotetabs=0, istext=1, header=0) -> s;
quopri.decode(??)
quopri.decodestring(??)
quopri.encode(??)
quopri.encodestring(??)
quopri.ishex(??)
quopri.main(??)
quopri.needsquoting(??)
quopri.quote(??)
quopri.unhex(??)
random.BPF
random.LOG4
random.NV_MAGICCONST
random.RECIP_BPF
random.Random(??)
random.SG_MAGICCONST
random.SystemRandom(??)
random.TWOPI
random.betavariate(??)
random.choice(??)
random.division(??)
random.expovariate(??)
random.gammavariate(??)
random.gauss(??)
random.getrandbits(k) -> x.  Generates a long int with k random bits.
random.getstate(??)
random.lognormvariate(??)
random.normalvariate(??)
random.paretovariate(??)
random.randint(??)
random.random() -> x in the interval [0, 1).
random.randrange(??)
random.sample(??)
random.seed(??)
random.setstate(??)
random.shuffle(??)
random.triangular(??)
random.uniform(??)
random.vonmisesvariate(??)
random.weibullvariate(??)
range([start,] stop[, step]) -> range object
re.A
re.ASCII
re.DEBUG
re.DOTALL
re.I
re.IGNORECASE
re.L
re.LOCALE
re.M
re.MULTILINE
re.S
re.Scanner(??)
re.T
re.TEMPLATE
re.U
re.UNICODE
re.VERBOSE
re.X
re.compile(??)
re.copyreg(??)
re.error(??)
re.escape(??)
re.findall(??)
re.finditer(??)
re.functools(??)
re.match(??)
re.purge(??)
re.search(??)
re.split(??)
re.sre_compile(??)
re.sre_parse(??)
re.sub(??)
re.subn(??)
re.sys(??)
re.template(??)
repr(object) -> string
reprlib.Repr(??)
reprlib.aRepr(??)
reprlib.builtins(??)
reprlib.get_ident() -> integer
reprlib.islice(iterable, [start,] stop [, step]) --> islice object
reprlib.recursive_repr(??)
reprlib.repr(??)
reversed(sequence) -> reverse iterator over values of the sequence
rlcompleter.Completer(??)
rlcompleter.builtins(??)
rlcompleter.get_class_members(??)
round(number[, ndigits]) -> number
runpy.get_loader(??)
runpy.imp(??)
runpy.read_code(??)
runpy.run_module(??)
runpy.run_path(??)
runpy.sys(??)
sched.Event(??)
sched.heapq(??)
sched.namedtuple(??)
sched.scheduler(??)
set() -> new empty set object
setattr(object, name, value)
shelve.BsdDbShelf(??)
shelve.BytesIO([buffer]) -> object
shelve.DbfilenameShelf(??)
shelve.Pickler(file, protocol=None)
shelve.Shelf(??)
shelve.Unpickler(file, *, encoding='ASCII', errors='strict')
shelve.collections(??)
shelve.open(??)
shlex.StringIO(??)
shlex.deque(iterable[, maxlen]) --> deque object
shlex.os(??)
shlex.shlex(??)
shlex.split(??)
shlex.sys(??)
shutil.Error(??)
shutil.ExecError(??)
shutil.ReadError(??)
shutil.RegistryError(??)
shutil.SpecialFileError(??)
shutil.WindowsError(??)
shutil.abspath(??)
shutil.collections(??)
shutil.copy(??)
shutil.copy2(??)
shutil.copyfile(??)
shutil.copyfileobj(??)
shutil.copymode(??)
shutil.copystat(??)
shutil.copytree(??)
shutil.errno(??)
shutil.fnmatch(??)
shutil.get_archive_formats(??)
shutil.get_unpack_formats(??)
shutil.getgrnam(name) -> tuple
shutil.getpwnam(name) -> (pw_name,pw_passwd,pw_uid,
shutil.ignore_patterns(??)
shutil.make_archive(??)
shutil.move(??)
shutil.os(??)
shutil.register_archive_format(??)
shutil.register_unpack_format(??)
shutil.rmtree(??)
shutil.stat(??)
shutil.sys(??)
shutil.tarfile(??)
shutil.unpack_archive(??)
shutil.unregister_archive_format(??)
shutil.unregister_unpack_format(??)
signal.ITIMER_PROF
signal.ITIMER_REAL
signal.ITIMER_VIRTUAL
signal.ItimerError(??)
signal.NSIG
signal.SIGABRT
signal.SIGALRM
signal.SIGBUS
signal.SIGCHLD
signal.SIGCLD
signal.SIGCONT
signal.SIGFPE
signal.SIGHUP
signal.SIGILL
signal.SIGINT
signal.SIGIO
signal.SIGIOT
signal.SIGKILL
signal.SIGPIPE
signal.SIGPOLL
signal.SIGPROF
signal.SIGPWR
signal.SIGQUIT
signal.SIGRTMAX
signal.SIGRTMIN
signal.SIGSEGV
signal.SIGSTOP
signal.SIGSYS
signal.SIGTERM
signal.SIGTRAP
signal.SIGTSTP
signal.SIGTTIN
signal.SIGTTOU
signal.SIGURG
signal.SIGUSR1
signal.SIGUSR2
signal.SIGVTALRM
signal.SIGWINCH
signal.SIGXCPU
signal.SIGXFSZ
signal.SIG_DFL
signal.SIG_IGN
signal.alarm(seconds)
signal.default_int_handler(...)
signal.getitimer(which)
signal.getsignal(sig) -> action
signal.pause()
signal.set_wakeup_fd(fd) -> fd
signal.setitimer(which, seconds[, interval])
signal.siginterrupt(sig, flag) -> None
signal.signal(sig, action) -> action
site.ENABLE_USER_SITE(??)
site.PREFIXES(??)
site.USER_BASE
site.USER_SITE
site.abs_paths(??)
site.addpackage(??)
site.addsitedir(??)
site.addsitepackages(??)
site.addusersitepackages(??)
site.aliasmbcs(??)
site.builtins(??)
site.check_enableusersite(??)
site.execsitecustomize(??)
site.execusercustomize(??)
site.getsitepackages(??)
site.getuserbase(??)
site.getusersitepackages(??)
site.main(??)
site.makepath(??)
site.os(??)
site.removeduppaths(??)
site.setBEGINLIBPATH(??)
site.setcopyright(??)
site.sethelper(??)
site.setquit(??)
site.sys(??)
site.traceback(??)
slice([start,] stop[, step])
smtpd.COMMASPACE
smtpd.DEBUGSTREAM(??)
smtpd.DebuggingServer(??)
smtpd.Devnull(??)
smtpd.EMPTYSTRING
smtpd.MailmanProxy(??)
smtpd.NEWLINE
smtpd.Options(??)
smtpd.PureProxy(??)
smtpd.SMTPChannel(??)
smtpd.SMTPServer(??)
smtpd.asynchat(??)
smtpd.asyncore(??)
smtpd.errno(??)
smtpd.getopt(??)
smtpd.os(??)
smtpd.parseargs(??)
smtpd.program
smtpd.socket(??)
smtpd.sys(??)
smtpd.time(??)
smtpd.usage(??)
smtpd.warn(??)
smtplib.CRLF
smtplib.LMTP(??)
smtplib.LMTP_PORT
smtplib.OLDSTYLE_AUTH(??)
smtplib.SMTP(??)
smtplib.SMTPAuthenticationError(??)
smtplib.SMTPConnectError(??)
smtplib.SMTPDataError(??)
smtplib.SMTPException(??)
smtplib.SMTPHeloError(??)
smtplib.SMTPRecipientsRefused(??)
smtplib.SMTPResponseException(??)
smtplib.SMTPSenderRefused(??)
smtplib.SMTPServerDisconnected(??)
smtplib.SMTP_PORT
smtplib.SMTP_SSL(??)
smtplib.SMTP_SSL_PORT
smtplib.SSLFakeFile(??)
smtplib.bCRLF
smtplib.base64(??)
smtplib.email(??)
smtplib.encode_base64(??)
smtplib.hmac(??)
smtplib.io(??)
smtplib.quoteaddr(??)
smtplib.quotedata(??)
smtplib.re(??)
smtplib.socket(??)
smtplib.ssl(??)
smtplib.stderr(??)
sndhdr.get_long_be(??)
sndhdr.get_long_le(??)
sndhdr.get_short_be(??)
sndhdr.get_short_le(??)
sndhdr.test(??)
sndhdr.test_8svx(??)
sndhdr.test_aifc(??)
sndhdr.test_au(??)
sndhdr.test_hcom(??)
sndhdr.test_sndr(??)
sndhdr.test_sndt(??)
sndhdr.test_voc(??)
sndhdr.test_wav(??)
sndhdr.testall(??)
sndhdr.tests(??)
sndhdr.what(??)
sndhdr.whathdr(??)
socket.AF_APPLETALK
socket.AF_ASH
socket.AF_ATMPVC
socket.AF_ATMSVC
socket.AF_AX25
socket.AF_BLUETOOTH
socket.AF_BRIDGE
socket.AF_DECnet
socket.AF_ECONET
socket.AF_INET
socket.AF_INET6
socket.AF_IPX
socket.AF_IRDA
socket.AF_KEY
socket.AF_LLC
socket.AF_NETBEUI
socket.AF_NETLINK
socket.AF_NETROM
socket.AF_PACKET
socket.AF_PPPOX
socket.AF_ROSE
socket.AF_ROUTE
socket.AF_SECURITY
socket.AF_SNA
socket.AF_TIPC
socket.AF_UNIX
socket.AF_UNSPEC
socket.AF_WANPIPE
socket.AF_X25
socket.AI_ADDRCONFIG
socket.AI_ALL
socket.AI_CANONNAME
socket.AI_NUMERICHOST
socket.AI_NUMERICSERV
socket.AI_PASSIVE
socket.AI_V4MAPPED
socket.BDADDR_ANY
socket.BDADDR_LOCAL
socket.BTPROTO_HCI
socket.BTPROTO_L2CAP
socket.BTPROTO_RFCOMM
socket.BTPROTO_SCO
socket.CAPI(??)
socket.EAGAIN
socket.EAI_ADDRFAMILY
socket.EAI_AGAIN
socket.EAI_BADFLAGS
socket.EAI_FAIL
socket.EAI_FAMILY
socket.EAI_MEMORY
socket.EAI_NODATA
socket.EAI_NONAME
socket.EAI_OVERFLOW
socket.EAI_SERVICE
socket.EAI_SOCKTYPE
socket.EAI_SYSTEM
socket.EBADF
socket.EINTR
socket.EWOULDBLOCK
socket.HCI_DATA_DIR
socket.HCI_FILTER
socket.HCI_TIME_STAMP
socket.INADDR_ALLHOSTS_GROUP
socket.INADDR_ANY
socket.INADDR_BROADCAST
socket.INADDR_LOOPBACK
socket.INADDR_MAX_LOCAL_GROUP
socket.INADDR_NONE
socket.INADDR_UNSPEC_GROUP
socket.IPPORT_RESERVED
socket.IPPORT_USERRESERVED
socket.IPPROTO_AH
socket.IPPROTO_DSTOPTS
socket.IPPROTO_EGP
socket.IPPROTO_ESP
socket.IPPROTO_FRAGMENT
socket.IPPROTO_GRE
socket.IPPROTO_HOPOPTS
socket.IPPROTO_ICMP
socket.IPPROTO_ICMPV6
socket.IPPROTO_IDP
socket.IPPROTO_IGMP
socket.IPPROTO_IP
socket.IPPROTO_IPIP
socket.IPPROTO_IPV6
socket.IPPROTO_NONE
socket.IPPROTO_PIM
socket.IPPROTO_PUP
socket.IPPROTO_RAW
socket.IPPROTO_ROUTING
socket.IPPROTO_RSVP
socket.IPPROTO_TCP
socket.IPPROTO_TP
socket.IPPROTO_UDP
socket.IPV6_CHECKSUM
socket.IPV6_DSTOPTS
socket.IPV6_HOPLIMIT
socket.IPV6_HOPOPTS
socket.IPV6_JOIN_GROUP
socket.IPV6_LEAVE_GROUP
socket.IPV6_MULTICAST_HOPS
socket.IPV6_MULTICAST_IF
socket.IPV6_MULTICAST_LOOP
socket.IPV6_NEXTHOP
socket.IPV6_PKTINFO
socket.IPV6_RECVDSTOPTS
socket.IPV6_RECVHOPLIMIT
socket.IPV6_RECVHOPOPTS
socket.IPV6_RECVPKTINFO
socket.IPV6_RECVRTHDR
socket.IPV6_RECVTCLASS
socket.IPV6_RTHDR
socket.IPV6_RTHDRDSTOPTS
socket.IPV6_RTHDR_TYPE_0
socket.IPV6_TCLASS
socket.IPV6_UNICAST_HOPS
socket.IPV6_V6ONLY
socket.IP_ADD_MEMBERSHIP
socket.IP_DEFAULT_MULTICAST_LOOP
socket.IP_DEFAULT_MULTICAST_TTL
socket.IP_DROP_MEMBERSHIP
socket.IP_HDRINCL
socket.IP_MAX_MEMBERSHIPS
socket.IP_MULTICAST_IF
socket.IP_MULTICAST_LOOP
socket.IP_MULTICAST_TTL
socket.IP_OPTIONS
socket.IP_RECVOPTS
socket.IP_RECVRETOPTS
socket.IP_RETOPTS
socket.IP_TOS
socket.IP_TTL
socket.MSG_CTRUNC
socket.MSG_DONTROUTE
socket.MSG_DONTWAIT
socket.MSG_EOR
socket.MSG_OOB
socket.MSG_PEEK
socket.MSG_TRUNC
socket.MSG_WAITALL
socket.NETLINK_DNRTMSG
socket.NETLINK_FIREWALL
socket.NETLINK_IP6_FW
socket.NETLINK_NFLOG
socket.NETLINK_ROUTE
socket.NETLINK_USERSOCK
socket.NETLINK_XFRM
socket.NI_DGRAM
socket.NI_MAXHOST
socket.NI_MAXSERV
socket.NI_NAMEREQD
socket.NI_NOFQDN
socket.NI_NUMERICHOST
socket.NI_NUMERICSERV
socket.PACKET_BROADCAST
socket.PACKET_FASTROUTE
socket.PACKET_HOST
socket.PACKET_LOOPBACK
socket.PACKET_MULTICAST
socket.PACKET_OTHERHOST
socket.PACKET_OUTGOING
socket.PF_PACKET
socket.SHUT_RD
socket.SHUT_RDWR
socket.SHUT_WR
socket.SOCK_CLOEXEC
socket.SOCK_DGRAM
socket.SOCK_NONBLOCK
socket.SOCK_RAW
socket.SOCK_RDM
socket.SOCK_SEQPACKET
socket.SOCK_STREAM
socket.SOL_HCI
socket.SOL_IP
socket.SOL_SOCKET
socket.SOL_TCP
socket.SOL_TIPC
socket.SOL_UDP
socket.SOMAXCONN
socket.SO_ACCEPTCONN
socket.SO_BROADCAST
socket.SO_DEBUG
socket.SO_DONTROUTE
socket.SO_ERROR
socket.SO_KEEPALIVE
socket.SO_LINGER
socket.SO_OOBINLINE
socket.SO_RCVBUF
socket.SO_RCVLOWAT
socket.SO_RCVTIMEO
socket.SO_REUSEADDR
socket.SO_SNDBUF
socket.SO_SNDLOWAT
socket.SO_SNDTIMEO
socket.SO_TYPE
socket.SocketIO(??)
socket.SocketType(??)
socket.TCP_CORK
socket.TCP_DEFER_ACCEPT
socket.TCP_INFO
socket.TCP_KEEPCNT
socket.TCP_KEEPIDLE
socket.TCP_KEEPINTVL
socket.TCP_LINGER2
socket.TCP_MAXSEG
socket.TCP_NODELAY
socket.TCP_QUICKACK
socket.TCP_SYNCNT
socket.TCP_WINDOW_CLAMP
socket.TIPC_ADDR_ID
socket.TIPC_ADDR_NAME
socket.TIPC_ADDR_NAMESEQ
socket.TIPC_CFG_SRV
socket.TIPC_CLUSTER_SCOPE
socket.TIPC_CONN_TIMEOUT
socket.TIPC_CRITICAL_IMPORTANCE
socket.TIPC_DEST_DROPPABLE
socket.TIPC_HIGH_IMPORTANCE
socket.TIPC_IMPORTANCE
socket.TIPC_LOW_IMPORTANCE
socket.TIPC_MEDIUM_IMPORTANCE
socket.TIPC_NODE_SCOPE
socket.TIPC_PUBLISHED
socket.TIPC_SRC_DROPPABLE
socket.TIPC_SUBSCR_TIMEOUT
socket.TIPC_SUB_CANCEL
socket.TIPC_SUB_PORTS
socket.TIPC_SUB_SERVICE
socket.TIPC_TOP_SRV
socket.TIPC_WAIT_FOREVER
socket.TIPC_WITHDRAWN
socket.TIPC_ZONE_SCOPE
socket.create_connection(??)
socket.dup(integer) -> integer
socket.errno(??)
socket.error(??)
socket.fromfd(fd, family, type[, proto]) -> socket object
socket.gaierror(??)
socket.getaddrinfo(host, port [, family, socktype, proto, flags])
socket.getdefaulttimeout() -> timeout
socket.getfqdn(??)
socket.gethostbyaddr(host) -> (name, aliaslist, addresslist)
socket.gethostbyname(host) -> address
socket.gethostbyname_ex(host) -> (name, aliaslist, addresslist)
socket.gethostname() -> string
socket.getnameinfo(sockaddr, flags) --> (host, port)
socket.getprotobyname(name) -> integer
socket.getservbyname(servicename[, protocolname]) -> integer
socket.getservbyport(port[, protocolname]) -> string
socket.has_ipv6(??)
socket.herror(??)
socket.htonl(integer) -> integer
socket.htons(integer) -> integer
socket.inet_aton(string) -> bytes giving packed 32-bit IP representation
socket.inet_ntoa(packed_ip) -> ip_address_string
socket.inet_ntop(af, packed_ip) -> string formatted IP address
socket.inet_pton(af, ip) -> packed IP address string
socket.io(??)
socket.ntohl(integer) -> integer
socket.ntohs(integer) -> integer
socket.os(??)
socket.setdefaulttimeout(timeout)
socket.socket(??)
socket.socketpair([family[, type[, proto]]]) -> (socket object, socket object)
socket.sys(??)
socket.timeout(??)
socketserver.BaseRequestHandler(??)
socketserver.BaseServer(??)
socketserver.DatagramRequestHandler(??)
socketserver.ForkingMixIn(??)
socketserver.ForkingTCPServer(??)
socketserver.ForkingUDPServer(??)
socketserver.StreamRequestHandler(??)
socketserver.TCPServer(??)
socketserver.ThreadingMixIn(??)
socketserver.ThreadingTCPServer(??)
socketserver.ThreadingUDPServer(??)
socketserver.ThreadingUnixDatagramServer(??)
socketserver.ThreadingUnixStreamServer(??)
socketserver.UDPServer(??)
socketserver.UnixDatagramServer(??)
socketserver.UnixStreamServer(??)
socketserver.os(??)
socketserver.select(??)
socketserver.socket(??)
socketserver.sys(??)
socketserver.threading(??)
sorted(iterable, key=None, reverse=False) --> new sorted list
ssl.AF_INET
ssl.CERT_NONE
ssl.CERT_OPTIONAL
ssl.CERT_REQUIRED
ssl.CertificateError(??)
ssl.DER_cert_to_PEM_cert(??)
ssl.HAS_SNI(??)
ssl.OPENSSL_VERSION
ssl.OPENSSL_VERSION_INFO(??)
ssl.OPENSSL_VERSION_NUMBER
ssl.OP_ALL
ssl.OP_NO_SSLv2
ssl.OP_NO_SSLv3
ssl.OP_NO_TLSv1
ssl.PEM_FOOTER
ssl.PEM_HEADER
ssl.PEM_cert_to_DER_cert(??)
ssl.PROTOCOL_SSLv2
ssl.PROTOCOL_SSLv23
ssl.PROTOCOL_SSLv3
ssl.PROTOCOL_TLSv1
ssl.RAND_add(string, entropy)
ssl.RAND_egd(path) -> bytes
ssl.RAND_status() -> 0 or 1
ssl.SOCK_STREAM
ssl.SSLContext(??)
ssl.SSLError(??)
ssl.SSLSocket(??)
ssl.SSL_ERROR_EOF
ssl.SSL_ERROR_INVALID_ERROR_CODE
ssl.SSL_ERROR_SSL
ssl.SSL_ERROR_SYSCALL
ssl.SSL_ERROR_WANT_CONNECT
ssl.SSL_ERROR_WANT_READ
ssl.SSL_ERROR_WANT_WRITE
ssl.SSL_ERROR_WANT_X509_LOOKUP
ssl.SSL_ERROR_ZERO_RETURN
ssl.base64(??)
ssl.cert_time_to_seconds(??)
ssl.errno(??)
ssl.get_protocol_name(??)
ssl.get_server_certificate(??)
ssl.match_hostname(??)
ssl.re(??)
ssl.socket(??)
ssl.socket_error(??)
ssl.textwrap(??)
ssl.traceback(??)
ssl.wrap_socket(??)
stat.SF_APPEND
stat.SF_ARCHIVED
stat.SF_IMMUTABLE
stat.SF_NOUNLINK
stat.SF_SNAPSHOT
stat.ST_ATIME
stat.ST_CTIME
stat.ST_DEV
stat.ST_GID
stat.ST_INO
stat.ST_MODE
stat.ST_MTIME
stat.ST_NLINK
stat.ST_SIZE
stat.ST_UID
stat.S_ENFMT
stat.S_IEXEC
stat.S_IFBLK
stat.S_IFCHR
stat.S_IFDIR
stat.S_IFIFO
stat.S_IFLNK
stat.S_IFMT(??)
stat.S_IFREG
stat.S_IFSOCK
stat.S_IMODE(??)
stat.S_IREAD
stat.S_IRGRP
stat.S_IROTH
stat.S_IRUSR
stat.S_IRWXG
stat.S_IRWXO
stat.S_IRWXU
stat.S_ISBLK(??)
stat.S_ISCHR(??)
stat.S_ISDIR(??)
stat.S_ISFIFO(??)
stat.S_ISGID
stat.S_ISLNK(??)
stat.S_ISREG(??)
stat.S_ISSOCK(??)
stat.S_ISUID
stat.S_ISVTX
stat.S_IWGRP
stat.S_IWOTH
stat.S_IWRITE
stat.S_IWUSR
stat.S_IXGRP
stat.S_IXOTH
stat.S_IXUSR
stat.UF_APPEND
stat.UF_IMMUTABLE
stat.UF_NODUMP
stat.UF_NOUNLINK
stat.UF_OPAQUE
staticmethod(function) -> method
str(string[, encoding[, errors]]) -> str
string.Formatter(??)
string.Template(??)
string.ascii_letters
string.ascii_lowercase
string.ascii_uppercase
string.capwords(s [,sep]) -> string
string.digits
string.hexdigits
string.octdigits
string.printable
string.punctuation
string.whitespace
stringprep.b1_set(??)
stringprep.b3_exceptions(??)
stringprep.c22_specials(??)
stringprep.c6_set(??)
stringprep.c7_set(??)
stringprep.c8_set(??)
stringprep.c9_set(??)
stringprep.in_table_a1(??)
stringprep.in_table_b1(??)
stringprep.in_table_c11(??)
stringprep.in_table_c11_c12(??)
stringprep.in_table_c12(??)
stringprep.in_table_c21(??)
stringprep.in_table_c21_c22(??)
stringprep.in_table_c22(??)
stringprep.in_table_c3(??)
stringprep.in_table_c4(??)
stringprep.in_table_c5(??)
stringprep.in_table_c6(??)
stringprep.in_table_c7(??)
stringprep.in_table_c8(??)
stringprep.in_table_c9(??)
stringprep.in_table_d1(??)
stringprep.in_table_d2(??)
stringprep.map_table_b2(??)
stringprep.map_table_b3(??)
stringprep.unicodedata(??)
struct.Struct(fmt) --> compiled struct object
struct.calcsize(fmt) -> integer
struct.error(??)
struct.pack(fmt, v1, v2, ...) -> bytes
struct.pack_into(fmt, buffer, offset, v1, v2, ...)
struct.unpack(fmt, buffer) -> (v1, v2, ...)
struct.unpack_from(fmt, buffer, offset=0) -> (v1, v2, ...)
subprocess.CalledProcessError(??)
subprocess.MAXFD
subprocess.PIPE
subprocess.Popen(??)
subprocess.STDOUT
subprocess.builtins(??)
subprocess.call(??)
subprocess.check_call(??)
subprocess.check_output(??)
subprocess.errno(??)
subprocess.fcntl(??)
subprocess.gc(??)
subprocess.getoutput(??)
subprocess.getstatusoutput(??)
subprocess.io(??)
subprocess.list2cmdline(??)
subprocess.mswindows(??)
subprocess.os(??)
subprocess.pickle(??)
subprocess.select(??)
subprocess.signal(??)
subprocess.sys(??)
subprocess.traceback(??)
subprocess.warnings(??)
sum(iterable[, start]) -> value
sunau.AUDIO_FILE_ENCODING_ADPCM_G721
sunau.AUDIO_FILE_ENCODING_ADPCM_G722
sunau.AUDIO_FILE_ENCODING_ADPCM_G723_3
sunau.AUDIO_FILE_ENCODING_ADPCM_G723_5
sunau.AUDIO_FILE_ENCODING_ALAW_8
sunau.AUDIO_FILE_ENCODING_DOUBLE
sunau.AUDIO_FILE_ENCODING_FLOAT
sunau.AUDIO_FILE_ENCODING_LINEAR_16
sunau.AUDIO_FILE_ENCODING_LINEAR_24
sunau.AUDIO_FILE_ENCODING_LINEAR_32
sunau.AUDIO_FILE_ENCODING_LINEAR_8
sunau.AUDIO_FILE_ENCODING_MULAW_8
sunau.AUDIO_FILE_MAGIC
sunau.AUDIO_UNKNOWN_SIZE
sunau.Au_read(??)
sunau.Au_write(??)
sunau.Error(??)
sunau.open(??)
sunau.openfp(??)
super() -> same as super(__class__, <first argument>)
symbol.and_expr
symbol.and_test
symbol.arglist
symbol.argument
symbol.arith_expr
symbol.assert_stmt
symbol.atom
symbol.augassign
symbol.break_stmt
symbol.classdef
symbol.comp_for
symbol.comp_if
symbol.comp_iter
symbol.comp_op
symbol.comparison
symbol.compound_stmt
symbol.continue_stmt
symbol.decorated
symbol.decorator
symbol.decorators
symbol.del_stmt
symbol.dictorsetmaker
symbol.dotted_as_name
symbol.dotted_as_names
symbol.dotted_name
symbol.encoding_decl
symbol.eval_input
symbol.except_clause
symbol.expr
symbol.expr_stmt
symbol.exprlist
symbol.factor
symbol.file_input
symbol.flow_stmt
symbol.for_stmt
symbol.funcdef
symbol.global_stmt
symbol.if_stmt
symbol.import_as_name
symbol.import_as_names
symbol.import_from
symbol.import_name
symbol.import_stmt
symbol.lambdef
symbol.lambdef_nocond
symbol.main(??)
symbol.nonlocal_stmt
symbol.not_test
symbol.or_test
symbol.parameters
symbol.pass_stmt
symbol.power
symbol.raise_stmt
symbol.return_stmt
symbol.shift_expr
symbol.simple_stmt
symbol.single_input
symbol.sliceop
symbol.small_stmt
symbol.star_expr
symbol.stmt
symbol.subscript
symbol.subscriptlist
symbol.suite
symbol.sym_name(??)
symbol.term
symbol.test
symbol.test_nocond
symbol.testlist
symbol.testlist_comp
symbol.testlist_star_expr
symbol.tfpdef
symbol.trailer
symbol.try_stmt
symbol.typedargslist
symbol.varargslist
symbol.vfpdef
symbol.while_stmt
symbol.with_item
symbol.with_stmt
symbol.xor_expr
symbol.yield_expr
symbol.yield_stmt
symtable.CELL
symtable.Class(??)
symtable.DEF_BOUND
symtable.DEF_GLOBAL
symtable.DEF_IMPORT
symtable.DEF_LOCAL
symtable.DEF_PARAM
symtable.FREE
symtable.Function(??)
symtable.GLOBAL_EXPLICIT
symtable.GLOBAL_IMPLICIT
symtable.LOCAL
symtable.OPT_IMPORT_STAR
symtable.SCOPE_MASK
symtable.SCOPE_OFF
symtable.Symbol(??)
symtable.SymbolTable(??)
symtable.SymbolTableFactory(??)
symtable.USE
symtable.symtable(??)
symtable.weakref(??)
sys.abiflags
sys.api_version
sys.argv(??)
sys.builtin_module_names(??)
sys.byteorder
sys.call_tracing(func, args) -> object
sys.callstats() -> tuple of integers
sys.copyright
sys.displayhook(object) -> None
sys.dont_write_bytecode(??)
sys.exc_info() -> (type, value, traceback)
sys.excepthook(exctype, value, traceback) -> None
sys.exec_prefix
sys.executable
sys.exit([status])
sys.flags(??)
sys.float_info(??)
sys.float_repr_style
sys.getcheckinterval() -> current check interval; see setcheckinterval().
sys.getdefaultencoding() -> string
sys.getdlopenflags() -> int
sys.getfilesystemencoding() -> string
sys.getprofile()
sys.getrecursionlimit()
sys.getrefcount(object) -> integer
sys.getsizeof(object, default) -> int
sys.getswitchinterval() -> current thread switch interval; see setswitchinterval().
sys.gettrace()
sys.hash_info(??)
sys.hexversion
sys.int_info(??)
sys.intern(string) -> string
sys.maxsize
sys.maxunicode
sys.meta_path(??)
sys.modules(??)
sys.path(??)
sys.path_hooks(??)
sys.path_importer_cache(??)
sys.platform
sys.prefix
sys.setcheckinterval(n)
sys.setdlopenflags(n) -> None
sys.setprofile(function)
sys.setrecursionlimit(n)
sys.setswitchinterval(n)
sys.settrace(function)
sys.stderr(??)
sys.stdin(??)
sys.stdout(??)
sys.subversion(??)
sys.version
sys.version_info(??)
sys.warnoptions(??)
sysconfig.get_config_h_filename(??)
sysconfig.get_config_var(??)
sysconfig.get_config_vars(??)
sysconfig.get_makefile_filename(??)
sysconfig.get_path(??)
sysconfig.get_path_names(??)
sysconfig.get_paths(??)
sysconfig.get_platform(??)
sysconfig.get_python_version(??)
sysconfig.get_scheme_names(??)
sysconfig.is_python_build(??)
sysconfig.os(??)
sysconfig.pardir
sysconfig.parse_config_h(??)
sysconfig.realpath(??)
sysconfig.sys(??)
tabnanny.NannyNag(??)
tabnanny.Whitespace(??)
tabnanny.check(file_or_dir)
tabnanny.errprint(??)
tabnanny.filename_only
tabnanny.format_witnesses(??)
tabnanny.getopt(??)
tabnanny.main(??)
tabnanny.os(??)
tabnanny.process_tokens(??)
tabnanny.sys(??)
tabnanny.tokenize(??)
tabnanny.verbose
tarfile.AREGTYPE
tarfile.BLKTYPE
tarfile.BLOCKSIZE
tarfile.CHRTYPE
tarfile.CONTTYPE
tarfile.CompressionError(??)
tarfile.DEFAULT_FORMAT
tarfile.DIRTYPE
tarfile.ENCODING
tarfile.EOFHeaderError(??)
tarfile.EmptyHeaderError(??)
tarfile.ExFileObject(??)
tarfile.ExtractError(??)
tarfile.FIFOTYPE
tarfile.GNUTYPE_LONGLINK
tarfile.GNUTYPE_LONGNAME
tarfile.GNUTYPE_SPARSE
tarfile.GNU_FORMAT
tarfile.GNU_MAGIC
tarfile.GNU_TYPES(??)
tarfile.HeaderError(??)
tarfile.InvalidHeaderError(??)
tarfile.LENGTH_LINK
tarfile.LENGTH_NAME
tarfile.LENGTH_PREFIX
tarfile.LNKTYPE
tarfile.NUL
tarfile.PAX_FIELDS(??)
tarfile.PAX_FORMAT
tarfile.PAX_NAME_FIELDS(??)
tarfile.PAX_NUMBER_FIELDS(??)
tarfile.POSIX_MAGIC
tarfile.RECORDSIZE
tarfile.REGTYPE
tarfile.REGULAR_TYPES(??)
tarfile.ReadError(??)
tarfile.SOLARIS_XHDTYPE
tarfile.SUPPORTED_TYPES(??)
tarfile.SYMTYPE
tarfile.S_IFBLK
tarfile.S_IFCHR
tarfile.S_IFDIR
tarfile.S_IFIFO
tarfile.S_IFLNK
tarfile.S_IFREG
tarfile.StreamError(??)
tarfile.SubsequentHeaderError(??)
tarfile.TGEXEC
tarfile.TGREAD
tarfile.TGWRITE
tarfile.TOEXEC
tarfile.TOREAD
tarfile.TOWRITE
tarfile.TSGID
tarfile.TSUID
tarfile.TSVTX
tarfile.TUEXEC
tarfile.TUREAD
tarfile.TUWRITE
tarfile.TarError(??)
tarfile.TarFile(??)
tarfile.TarInfo(??)
tarfile.TarIter(??)
tarfile.TruncatedHeaderError(??)
tarfile.USTAR_FORMAT
tarfile.XGLTYPE
tarfile.XHDTYPE
tarfile.bltn_open(??)
tarfile.calc_chksums(??)
tarfile.copy(??)
tarfile.copyfileobj(??)
tarfile.errno(??)
tarfile.filemode(??)
tarfile.filemode_table(??)
tarfile.grp(??)
tarfile.is_tarfile(??)
tarfile.itn(??)
tarfile.nti(??)
tarfile.nts(??)
tarfile.open(??)
tarfile.os(??)
tarfile.pwd(??)
tarfile.re(??)
tarfile.shutil(??)
tarfile.stat(??)
tarfile.stn(??)
tarfile.struct(??)
tarfile.symlink_exception(??)
tarfile.sys(??)
tarfile.time(??)
tarfile.version
telnetlib.AO
telnetlib.AUTHENTICATION
telnetlib.AYT
telnetlib.BINARY
telnetlib.BM
telnetlib.BRK
telnetlib.CHARSET
telnetlib.COM_PORT_OPTION
telnetlib.DEBUGLEVEL
telnetlib.DET
telnetlib.DM
telnetlib.DO
telnetlib.DONT
telnetlib.EC
telnetlib.ECHO
telnetlib.EL
telnetlib.ENCRYPT
telnetlib.EOR
telnetlib.EXOPL
telnetlib.FORWARD_X
telnetlib.GA
telnetlib.IAC
telnetlib.IP
telnetlib.KERMIT
telnetlib.LFLOW
telnetlib.LINEMODE
telnetlib.LOGOUT
telnetlib.NAMS
telnetlib.NAOCRD
telnetlib.NAOFFD
telnetlib.NAOHTD
telnetlib.NAOHTS
telnetlib.NAOL
telnetlib.NAOLFD
telnetlib.NAOP
telnetlib.NAOVTD
telnetlib.NAOVTS
telnetlib.NAWS
telnetlib.NEW_ENVIRON
telnetlib.NOOPT
telnetlib.NOP
telnetlib.OLD_ENVIRON
telnetlib.OUTMRK
telnetlib.PRAGMA_HEARTBEAT
telnetlib.PRAGMA_LOGON
telnetlib.RCP
telnetlib.RCTE
telnetlib.RSP
telnetlib.SB
telnetlib.SE
telnetlib.SEND_URL
telnetlib.SGA
telnetlib.SNDLOC
telnetlib.SSPI_LOGON
telnetlib.STATUS
telnetlib.SUPDUP
telnetlib.SUPDUPOUTPUT
telnetlib.SUPPRESS_LOCAL_ECHO
telnetlib.TELNET_PORT
telnetlib.TLS
telnetlib.TM
telnetlib.TN3270E
telnetlib.TSPEED
telnetlib.TTYLOC
telnetlib.TTYPE
telnetlib.TUID
telnetlib.Telnet(??)
telnetlib.VT3270REGIME
telnetlib.WILL
telnetlib.WONT
telnetlib.X3PAD
telnetlib.XASCII
telnetlib.XAUTH
telnetlib.XDISPLOC
telnetlib.select(??)
telnetlib.socket(??)
telnetlib.sys(??)
telnetlib.test(??)
telnetlib.theNULL
tempfile.NamedTemporaryFile(??)
tempfile.SpooledTemporaryFile(??)
tempfile.TMP_MAX
tempfile.TemporaryDirectory(??)
tempfile.TemporaryFile(??)
tempfile.gettempdir(??)
tempfile.gettempprefix(??)
tempfile.mkdtemp(??)
tempfile.mkstemp(??)
tempfile.mktemp(??)
tempfile.tempdir(??)
tempfile.template
textwrap.TextWrapper(??)
textwrap.dedent(??)
textwrap.fill(??)
textwrap.re(??)
textwrap.string(??)
textwrap.wrap(??)
threading.Barrier(??)
threading.BoundedSemaphore(??)
threading.BrokenBarrierError(??)
threading.Condition(??)
threading.Event(??)
threading.Lock(??)
threading.RLock(??)
threading.Semaphore(??)
threading.TIMEOUT_MAX
threading.Thread(??)
threading.ThreadError(??)
threading.Timer(??)
threading.activeCount(??)
threading.active_count(??)
threading.currentThread(??)
threading.current_thread(??)
threading.deque(iterable[, maxlen]) --> deque object
threading.enumerate(??)
threading.local(??)
threading.setprofile(??)
threading.settrace(??)
threading.stack_size([size]) -> size
timeit.Timer(??)
timeit.default_number
timeit.default_repeat
timeit.default_timer(??)
timeit.dummy_src_name
timeit.gc(??)
timeit.itertools(??)
timeit.main(??)
timeit.reindent(??)
timeit.repeat(??)
timeit.sys(??)
timeit.template
timeit.time(??)
timeit.timeit(??)
token.AMPER
token.AMPEREQUAL
token.AT
token.CIRCUMFLEX
token.CIRCUMFLEXEQUAL
token.COLON
token.COMMA
token.DEDENT
token.DOT
token.DOUBLESLASH
token.DOUBLESLASHEQUAL
token.DOUBLESTAR
token.DOUBLESTAREQUAL
token.ELLIPSIS
token.ENDMARKER
token.EQEQUAL
token.EQUAL
token.ERRORTOKEN
token.GREATER
token.GREATEREQUAL
token.INDENT
token.ISEOF(??)
token.ISNONTERMINAL(??)
token.ISTERMINAL(??)
token.LBRACE
token.LEFTSHIFT
token.LEFTSHIFTEQUAL
token.LESS
token.LESSEQUAL
token.LPAR
token.LSQB
token.MINEQUAL
token.MINUS
token.NAME
token.NEWLINE
token.NOTEQUAL
token.NT_OFFSET
token.NUMBER
token.N_TOKENS
token.OP
token.PERCENT
token.PERCENTEQUAL
token.PLUS
token.PLUSEQUAL
token.RARROW
token.RBRACE
token.RIGHTSHIFT
token.RIGHTSHIFTEQUAL
token.RPAR
token.RSQB
token.SEMI
token.SLASH
token.SLASHEQUAL
token.STAR
token.STAREQUAL
token.STRING
token.TILDE
token.VBAR
token.VBAREQUAL
token.tok_name(??)
tokenize.AMPER
tokenize.AMPEREQUAL
tokenize.AT
tokenize.BOM_UTF8
tokenize.Binnumber
tokenize.Bracket
tokenize.CIRCUMFLEX
tokenize.CIRCUMFLEXEQUAL
tokenize.COLON
tokenize.COMMA
tokenize.COMMENT
tokenize.Comment
tokenize.ContStr
tokenize.DEDENT
tokenize.DOT
tokenize.DOUBLESLASH
tokenize.DOUBLESLASHEQUAL
tokenize.DOUBLESTAR
tokenize.DOUBLESTAREQUAL
tokenize.Decnumber
tokenize.Double
tokenize.Double3
tokenize.ELLIPSIS
tokenize.ENCODING
tokenize.ENDMARKER
tokenize.EQEQUAL
tokenize.EQUAL
tokenize.ERRORTOKEN
tokenize.Expfloat
tokenize.Exponent
tokenize.Floatnumber
tokenize.Funny
tokenize.GREATER
tokenize.GREATEREQUAL
tokenize.Hexnumber
tokenize.INDENT
tokenize.ISEOF(??)
tokenize.ISNONTERMINAL(??)
tokenize.ISTERMINAL(??)
tokenize.Ignore
tokenize.Imagnumber
tokenize.Intnumber
tokenize.LBRACE
tokenize.LEFTSHIFT
tokenize.LEFTSHIFTEQUAL
tokenize.LESS
tokenize.LESSEQUAL
tokenize.LPAR
tokenize.LSQB
tokenize.MINEQUAL
tokenize.MINUS
tokenize.NAME
tokenize.NEWLINE
tokenize.NL
tokenize.NOTEQUAL
tokenize.NT_OFFSET
tokenize.NUMBER
tokenize.N_TOKENS
tokenize.Name
tokenize.Number
tokenize.OP
tokenize.Octnumber
tokenize.Operator
tokenize.PERCENT
tokenize.PERCENTEQUAL
tokenize.PLUS
tokenize.PLUSEQUAL
tokenize.PlainToken
tokenize.Pointfloat
tokenize.PseudoExtras
tokenize.PseudoToken
tokenize.RARROW
tokenize.RBRACE
tokenize.RIGHTSHIFT
tokenize.RIGHTSHIFTEQUAL
tokenize.RPAR
tokenize.RSQB
tokenize.SEMI
tokenize.SLASH
tokenize.SLASHEQUAL
tokenize.STAR
tokenize.STAREQUAL
tokenize.STRING
tokenize.Single
tokenize.Single3
tokenize.Special
tokenize.StopTokenizing(??)
tokenize.String
tokenize.TILDE
tokenize.TextIOWrapper(??)
tokenize.Token
tokenize.TokenError(??)
tokenize.TokenInfo(??)
tokenize.Triple
tokenize.Untokenizer(??)
tokenize.VBAR
tokenize.VBAREQUAL
tokenize.Whitespace
tokenize.any(??)
tokenize.collections(??)
tokenize.cookie_re(??)
tokenize.detect_encoding(??)
tokenize.double3prog(??)
tokenize.endprogs(??)
tokenize.generate_tokens(??)
tokenize.group(??)
tokenize.lookup(encoding) -> CodecInfo
tokenize.maybe(??)
tokenize.open(??)
tokenize.pseudoprog(??)
tokenize.re(??)
tokenize.single3prog(??)
tokenize.single_quoted(??)
tokenize.sys(??)
tokenize.t
tokenize.tabsize
tokenize.tok_name(??)
tokenize.tokenize(??)
tokenize.tokenprog(??)
tokenize.triple_quoted(??)
tokenize.untokenize(??)
trace.CoverageResults(??)
trace.Ignore(??)
trace.PRAGMA_NOCOVER
trace.Trace(??)
trace.dis(??)
trace.find_executable_linenos(??)
trace.find_lines(??)
trace.find_lines_from_code(??)
trace.find_strings(??)
trace.fullmodname(??)
trace.gc(??)
trace.inspect(??)
trace.io(??)
trace.linecache(??)
trace.main(??)
trace.modname(??)
trace.os(??)
trace.pickle(??)
trace.re(??)
trace.rx_blank(??)
trace.sys(??)
trace.threading(??)
trace.time(??)
trace.token(??)
trace.tokenize(??)
trace.usage(??)
traceback.extract_stack(??)
traceback.extract_tb(??)
traceback.format_exc(??)
traceback.format_exception(??)
traceback.format_exception_only(??)
traceback.format_list(??)
traceback.format_stack(??)
traceback.format_tb(??)
traceback.linecache(??)
traceback.print_exc(??)
traceback.print_exception(??)
traceback.print_last(??)
traceback.print_list(??)
traceback.print_stack(??)
traceback.print_tb(??)
traceback.sys(??)
tty.B0
tty.********
tty.B110
tty.B115200
tty.********
tty.B1200
tty.B134
tty.B150
tty.********
tty.B1800
tty.B19200
tty.B200
tty.********
tty.B230400
tty.B2400
tty.********
tty.B300
tty.********
tty.********
tty.B38400
tty.********
tty.B460800
tty.B4800
tty.B50
tty.B500000
tty.B57600
tty.B576000
tty.B600
tty.B75
tty.B921600
tty.B9600
tty.BRKINT
tty.BS0
tty.BS1
tty.BSDLY
tty.CBAUD
tty.CBAUDEX
tty.CC
tty.CDSUSP
tty.CEOF
tty.CEOL
tty.CEOT
tty.CERASE
tty.CFLAG
tty.CFLUSH
tty.CIBAUD
tty.CINTR
tty.CKILL
tty.CLNEXT
tty.CLOCAL
tty.CQUIT
tty.CR0
tty.CR1
tty.CR2
tty.CR3
tty.CRDLY
tty.CREAD
tty.CRPRNT
tty.CRTSCTS
tty.CS5
tty.CS6
tty.CS7
tty.CS8
tty.CSIZE
tty.CSTART
tty.CSTOP
tty.CSTOPB
tty.CSUSP
tty.CWERASE
tty.ECHO
tty.ECHOCTL
tty.ECHOE
tty.ECHOK
tty.ECHOKE
tty.ECHONL
tty.ECHOPRT
tty.EXTA
tty.EXTB
tty.FF0
tty.FF1
tty.FFDLY
tty.FIOASYNC
tty.FIOCLEX
tty.FIONBIO
tty.FIONCLEX
tty.FIONREAD
tty.FLUSHO
tty.HUPCL
tty.ICANON
tty.ICRNL
tty.IEXTEN
tty.IFLAG
tty.IGNBRK
tty.IGNCR
tty.IGNPAR
tty.IMAXBEL
tty.INLCR
tty.INPCK
tty.IOCSIZE_MASK
tty.IOCSIZE_SHIFT
tty.ISIG
tty.ISPEED
tty.ISTRIP
tty.IUCLC
tty.IXANY
tty.IXOFF
tty.IXON
tty.LFLAG
tty.NCC
tty.NCCS
tty.NL0
tty.NL1
tty.NLDLY
tty.NOFLSH
tty.N_MOUSE
tty.N_PPP
tty.N_SLIP
tty.N_STRIP
tty.N_TTY
tty.OCRNL
tty.OFDEL
tty.OFILL
tty.OFLAG
tty.OLCUC
tty.ONLCR
tty.ONLRET
tty.ONOCR
tty.OPOST
tty.OSPEED
tty.PARENB
tty.PARMRK
tty.PARODD
tty.PENDIN
tty.TAB0
tty.TAB1
tty.TAB2
tty.TAB3
tty.TABDLY
tty.TCFLSH
tty.TCGETA
tty.TCGETS
tty.TCIFLUSH
tty.TCIOFF
tty.TCIOFLUSH
tty.TCION
tty.TCOFLUSH
tty.TCOOFF
tty.TCOON
tty.TCSADRAIN
tty.TCSAFLUSH
tty.TCSANOW
tty.TCSBRK
tty.TCSBRKP
tty.TCSETA
tty.TCSETAF
tty.TCSETAW
tty.TCSETS
tty.TCSETSF
tty.TCSETSW
tty.TCXONC
tty.TIOCCONS
tty.TIOCEXCL
tty.TIOCGETD
tty.TIOCGICOUNT
tty.TIOCGLCKTRMIOS
tty.TIOCGPGRP
tty.TIOCGSERIAL
tty.TIOCGSOFTCAR
tty.TIOCGWINSZ
tty.TIOCINQ
tty.TIOCLINUX
tty.TIOCMBIC
tty.TIOCMBIS
tty.TIOCMGET
tty.TIOCMIWAIT
tty.TIOCMSET
tty.TIOCM_CAR
tty.TIOCM_CD
tty.TIOCM_CTS
tty.TIOCM_DSR
tty.TIOCM_DTR
tty.TIOCM_LE
tty.TIOCM_RI
tty.TIOCM_RNG
tty.TIOCM_RTS
tty.TIOCM_SR
tty.TIOCM_ST
tty.TIOCNOTTY
tty.TIOCNXCL
tty.TIOCOUTQ
tty.TIOCPKT
tty.TIOCPKT_DATA
tty.TIOCPKT_DOSTOP
tty.TIOCPKT_FLUSHREAD
tty.TIOCPKT_FLUSHWRITE
tty.TIOCPKT_NOSTOP
tty.TIOCPKT_START
tty.TIOCPKT_STOP
tty.TIOCSCTTY
tty.TIOCSERCONFIG
tty.TIOCSERGETLSR
tty.TIOCSERGETMULTI
tty.TIOCSERGSTRUCT
tty.TIOCSERGWILD
tty.TIOCSERSETMULTI
tty.TIOCSERSWILD
tty.TIOCSER_TEMT
tty.TIOCSETD
tty.TIOCSLCKTRMIOS
tty.TIOCSPGRP
tty.TIOCSSERIAL
tty.TIOCSSOFTCAR
tty.TIOCSTI
tty.TIOCSWINSZ
tty.TOSTOP
tty.VDISCARD
tty.VEOF
tty.VEOL
tty.VEOL2
tty.VERASE
tty.VINTR
tty.VKILL
tty.VLNEXT
tty.VMIN
tty.VQUIT
tty.VREPRINT
tty.VSTART
tty.VSTOP
tty.VSUSP
tty.VSWTC
tty.VSWTCH
tty.VT0
tty.VT1
tty.VTDLY
tty.VTIME
tty.VWERASE
tty.XCASE
tty.XTABS
tty.error(??)
tty.setcbreak(??)
tty.setraw(??)
tty.tcdrain(fd) -> None
tty.tcflow(fd, action) -> None
tty.tcflush(fd, queue) -> None
tty.tcgetattr(fd) -> list_of_attrs
tty.tcsendbreak(fd, duration) -> None
tty.tcsetattr(fd, when, attributes) -> None
tuple() -> empty tuple
type(object) -> the object's type
types.BuiltinFunctionType(??)
types.BuiltinMethodType(??)
types.CodeType(??)
types.FrameType(??)
types.FunctionType(??)
types.GeneratorType(??)
types.GetSetDescriptorType(??)
types.LambdaType(??)
types.MemberDescriptorType(??)
types.MethodType(??)
types.ModuleType(??)
types.TracebackType(??)
urllib.error(??)
urllib.error.ContentTooShortError(??)
urllib.error.HTTPError(??)
urllib.error.URLError(??)
urllib.error.urllib(??)
urllib.parse(??)
urllib.parse.DefragResult(??)
urllib.parse.DefragResultBytes(??)
urllib.parse.MAX_CACHE_SIZE
urllib.parse.ParseResult(??)
urllib.parse.ParseResultBytes(??)
urllib.parse.Quoter(??)
urllib.parse.ResultBase(??)
urllib.parse.SplitResult(??)
urllib.parse.SplitResultBytes(??)
urllib.parse.clear_cache(??)
urllib.parse.collections(??)
urllib.parse.namedtuple(??)
urllib.parse.non_hierarchical(??)
urllib.parse.parse_qs(??)
urllib.parse.parse_qsl(??)
urllib.parse.quote('abc def') -> 'abc%20def'
urllib.parse.quote_from_bytes(??)
urllib.parse.quote_plus(??)
urllib.parse.scheme_chars
urllib.parse.splitattr('/path;attr1=value1;attr2=value2;...') ->
urllib.parse.splithost('//host[:port]/path') --> 'host[:port]', '/path'.
urllib.parse.splitnport(??)
urllib.parse.splitpasswd('user:passwd') -> 'user', 'passwd'.
urllib.parse.splitport('host:port') --> 'host', 'port'.
urllib.parse.splitquery('/path?query') --> '/path', 'query'.
urllib.parse.splittag('/path#tag') --> '/path', 'tag'.
urllib.parse.splittype('type:opaquestring') --> 'type', 'opaquestring'.
urllib.parse.splituser('user[:passwd]@host[:port]') --> 'user[:passwd]', 'host[:port]'.
urllib.parse.splitvalue('attr=value') --> 'attr', 'value'.
urllib.parse.sys(??)
urllib.parse.to_bytes(u"URL") --> 'URL'.
urllib.parse.unquote(??)
urllib.parse.unquote_plus(??)
urllib.parse.unquote_to_bytes('abc%20def') -> b'abc def'.
urllib.parse.unwrap('<URL:type://host/path>') --> 'type://host/path'.
urllib.parse.urldefrag(??)
urllib.parse.urlencode(??)
urllib.parse.urljoin(??)
urllib.parse.urlparse(??)
urllib.parse.urlsplit(??)
urllib.parse.urlunparse(??)
urllib.parse.urlunsplit(??)
urllib.parse.uses_fragment(??)
urllib.parse.uses_netloc(??)
urllib.parse.uses_params(??)
urllib.parse.uses_query(??)
urllib.parse.uses_relative(??)
urllib.request(??)
urllib.request.AbstractBasicAuthHandler(??)
urllib.request.AbstractDigestAuthHandler(??)
urllib.request.AbstractHTTPHandler(??)
urllib.request.BaseHandler(??)
urllib.request.CacheFTPHandler(??)
urllib.request.ContentTooShortError(??)
urllib.request.FTPHandler(??)
urllib.request.FancyURLopener(??)
urllib.request.FileHandler(??)
urllib.request.HTTPBasicAuthHandler(??)
urllib.request.HTTPCookieProcessor(??)
urllib.request.HTTPDefaultErrorHandler(??)
urllib.request.HTTPDigestAuthHandler(??)
urllib.request.HTTPError(??)
urllib.request.HTTPErrorProcessor(??)
urllib.request.HTTPHandler(??)
urllib.request.HTTPPasswordMgr(??)
urllib.request.HTTPPasswordMgrWithDefaultRealm(??)
urllib.request.HTTPRedirectHandler(??)
urllib.request.HTTPSHandler(??)
urllib.request.MAXFTPCACHE
urllib.request.OpenerDirector(??)
urllib.request.ProxyBasicAuthHandler(??)
urllib.request.ProxyDigestAuthHandler(??)
urllib.request.ProxyHandler(??)
urllib.request.Request(??)
urllib.request.URLError(??)
urllib.request.URLopener(??)
urllib.request.UnknownHandler(??)
urllib.request.addclosehook(??)
urllib.request.addinfourl(??)
urllib.request.base64(??)
urllib.request.bisect(??)
urllib.request.build_opener(??)
urllib.request.collections(??)
urllib.request.email(??)
urllib.request.ftpcache(??)
urllib.request.ftperrors(??)
urllib.request.ftpwrapper(??)
urllib.request.getproxies(??)
urllib.request.getproxies_environment(??)
urllib.request.hashlib(??)
urllib.request.http(??)
urllib.request.install_opener(??)
urllib.request.io(??)
urllib.request.localhost(??)
urllib.request.noheaders(??)
urllib.request.os(??)
urllib.request.parse_http_list(??)
urllib.request.parse_keqv_list(??)
urllib.request.pathname2url(??)
urllib.request.posixpath(??)
urllib.request.proxy_bypass(??)
urllib.request.proxy_bypass_environment(??)
urllib.request.quote('abc def') -> 'abc%20def'
urllib.request.random(??)
urllib.request.randombytes(??)
urllib.request.re(??)
urllib.request.request_host(??)
urllib.request.socket(??)
urllib.request.splitattr('/path;attr1=value1;attr2=value2;...') ->
urllib.request.splithost('//host[:port]/path') --> 'host[:port]', '/path'.
urllib.request.splitpasswd('user:passwd') -> 'user', 'passwd'.
urllib.request.splitport('host:port') --> 'host', 'port'.
urllib.request.splitquery('/path?query') --> '/path', 'query'.
urllib.request.splittag('/path#tag') --> '/path', 'tag'.
urllib.request.splittype('type:opaquestring') --> 'type', 'opaquestring'.
urllib.request.splituser('user[:passwd]@host[:port]') --> 'user[:passwd]', 'host[:port]'.
urllib.request.splitvalue('attr=value') --> 'attr', 'value'.
urllib.request.ssl(??)
urllib.request.sys(??)
urllib.request.thishost(??)
urllib.request.time(??)
urllib.request.to_bytes(u"URL") --> 'URL'.
urllib.request.unquote(??)
urllib.request.unwrap('<URL:type://host/path>') --> 'type://host/path'.
urllib.request.url2pathname(??)
urllib.request.urlcleanup(??)
urllib.request.urljoin(??)
urllib.request.urlopen(??)
urllib.request.urlparse(??)
urllib.request.urlretrieve(??)
urllib.request.urlsplit(??)
urllib.request.urlunparse(??)
urllib.response(??)
urllib.response.addbase(??)
urllib.response.addclosehook(??)
urllib.response.addinfo(??)
urllib.response.addinfourl(??)
urllib.robotparser(??)
urllib.robotparser.Entry(??)
urllib.robotparser.RobotFileParser(??)
urllib.robotparser.RuleLine(??)
urllib.robotparser.urllib(??)
uu.Error(??)
uu.binascii(??)
uu.decode(??)
uu.encode(??)
uu.os(??)
uu.sys(??)
uu.test(??)
uuid.NAMESPACE_DNS(??)
uuid.NAMESPACE_OID(??)
uuid.NAMESPACE_URL(??)
uuid.NAMESPACE_X500(??)
uuid.RESERVED_FUTURE
uuid.RESERVED_MICROSOFT
uuid.RESERVED_NCS
uuid.RFC_4122
uuid.UUID(??)
uuid.bytes_(??)
uuid.ctypes(??)
uuid.getnode(??)
uuid.int_(??)
uuid.lib(??)
uuid.libname
uuid.sys(??)
uuid.uuid1(??)
uuid.uuid3(??)
uuid.uuid4(??)
uuid.uuid5(??)
vars([object]) -> dictionary
warnings.WarningMessage(??)
warnings.catch_warnings(??)
warnings.defaultaction
warnings.filters(??)
warnings.filterwarnings(??)
warnings.formatwarning(??)
warnings.linecache(??)
warnings.onceregistry(??)
warnings.resetwarnings(??)
warnings.showwarning(??)
warnings.simplefilter(??)
warnings.sys(??)
warnings.warn(??)
warnings.warn_explicit(??)
wave.Chunk(??)
wave.Error(??)
wave.WAVE_FORMAT_PCM
wave.Wave_read(??)
wave.Wave_write(??)
wave.big_endian
wave.builtins(??)
wave.open(??)
wave.openfp(??)
wave.struct(??)
weakref.CallableProxyType(??)
weakref.KeyedRef(??)
weakref.ProxyType(??)
weakref.ProxyTypes(??)
weakref.ReferenceType(??)
weakref.WeakKeyDictionary(??)
weakref.WeakSet(??)
weakref.WeakValueDictionary(??)
weakref.collections(??)
weakref.getweakrefcount(object) -- return the number of weak references
weakref.getweakrefs(object) -- return a list of all weak reference objects
weakref.proxy(object[, callback]) -- create a proxy object that weakly
weakref.ref(??)
webbrowser.BackgroundBrowser(??)
webbrowser.BaseBrowser(??)
webbrowser.Elinks(??)
webbrowser.Error(??)
webbrowser.Galeon(??)
webbrowser.GenericBrowser(??)
webbrowser.Grail(??)
webbrowser.Konqueror(??)
webbrowser.Mozilla(??)
webbrowser.Netscape(??)
webbrowser.Opera(??)
webbrowser.UnixBrowser(??)
webbrowser.get(??)
webbrowser.io(??)
webbrowser.main(??)
webbrowser.open(??)
webbrowser.open_new(??)
webbrowser.open_new_tab(??)
webbrowser.os(??)
webbrowser.register(??)
webbrowser.register_X_browsers(??)
webbrowser.shlex(??)
webbrowser.stat(??)
webbrowser.subprocess(??)
webbrowser.sys(??)
webbrowser.time(??)
wsgiref.handlers(??)
wsgiref.handlers.BaseCGIHandler(??)
wsgiref.handlers.BaseHandler(??)
wsgiref.handlers.CGIHandler(??)
wsgiref.handlers.FileWrapper(??)
wsgiref.handlers.Headers(??)
wsgiref.handlers.IISCGIHandler(??)
wsgiref.handlers.SimpleHandler(??)
wsgiref.handlers.format_date_time(??)
wsgiref.handlers.guess_scheme(??)
wsgiref.handlers.is_hop_by_hop(??)
wsgiref.handlers.os(??)
wsgiref.handlers.read_environ(??)
wsgiref.handlers.sys(??)
wsgiref.handlers.time(??)
wsgiref.headers(??)
wsgiref.headers.Headers(??)
wsgiref.headers.re(??)
wsgiref.headers.tspecials(??)
wsgiref.simple_server(??)
wsgiref.simple_server.BaseHTTPRequestHandler(??)
wsgiref.simple_server.HTTPServer(??)
wsgiref.simple_server.ServerHandler(??)
wsgiref.simple_server.SimpleHandler(??)
wsgiref.simple_server.WSGIRequestHandler(??)
wsgiref.simple_server.WSGIServer(??)
wsgiref.simple_server.demo_app(??)
wsgiref.simple_server.make_server(??)
wsgiref.simple_server.server_version
wsgiref.simple_server.software_version
wsgiref.simple_server.sys(??)
wsgiref.simple_server.sys_version
wsgiref.simple_server.urllib(??)
wsgiref.util(??)
wsgiref.util.FileWrapper(??)
wsgiref.util.application_uri(??)
wsgiref.util.guess_scheme(??)
wsgiref.util.is_hop_by_hop(??)
wsgiref.util.posixpath(??)
wsgiref.util.request_uri(??)
wsgiref.util.setup_testing_defaults(??)
wsgiref.util.shift_path_info(??)
wsgiref.validate(??)
wsgiref.validate.ErrorWrapper(??)
wsgiref.validate.InputWrapper(??)
wsgiref.validate.IteratorWrapper(??)
wsgiref.validate.PartialIteratorWrapper(??)
wsgiref.validate.WSGIWarning(??)
wsgiref.validate.WriteWrapper(??)
wsgiref.validate.assert_(??)
wsgiref.validate.bad_header_value_re(??)
wsgiref.validate.check_content_type(??)
wsgiref.validate.check_environ(??)
wsgiref.validate.check_errors(??)
wsgiref.validate.check_exc_info(??)
wsgiref.validate.check_headers(??)
wsgiref.validate.check_input(??)
wsgiref.validate.check_iterator(??)
wsgiref.validate.check_status(??)
wsgiref.validate.check_string_type(??)
wsgiref.validate.header_re(??)
wsgiref.validate.re(??)
wsgiref.validate.sys(??)
wsgiref.validate.validator(??)
wsgiref.validate.warnings(??)
xdrlib.BytesIO([buffer]) -> object
xdrlib.ConversionError(??)
xdrlib.Error(??)
xdrlib.Packer(??)
xdrlib.Unpacker(??)
xdrlib.struct(??)
xml.dom(??)
xml.dom.DOMException(??)
xml.dom.DOMSTRING_SIZE_ERR
xml.dom.DomstringSizeErr(??)
xml.dom.EMPTY_NAMESPACE(??)
xml.dom.EMPTY_PREFIX(??)
xml.dom.HIERARCHY_REQUEST_ERR
xml.dom.HierarchyRequestErr(??)
xml.dom.INDEX_SIZE_ERR
xml.dom.INUSE_ATTRIBUTE_ERR
xml.dom.INVALID_ACCESS_ERR
xml.dom.INVALID_CHARACTER_ERR
xml.dom.INVALID_MODIFICATION_ERR
xml.dom.INVALID_STATE_ERR
xml.dom.IndexSizeErr(??)
xml.dom.InuseAttributeErr(??)
xml.dom.InvalidAccessErr(??)
xml.dom.InvalidCharacterErr(??)
xml.dom.InvalidModificationErr(??)
xml.dom.InvalidStateErr(??)
xml.dom.NAMESPACE_ERR
xml.dom.NOT_FOUND_ERR
xml.dom.NOT_SUPPORTED_ERR
xml.dom.NO_DATA_ALLOWED_ERR
xml.dom.NO_MODIFICATION_ALLOWED_ERR
xml.dom.NamespaceErr(??)
xml.dom.NoDataAllowedErr(??)
xml.dom.NoModificationAllowedErr(??)
xml.dom.Node(??)
xml.dom.NodeFilter(??)
xml.dom.NodeFilter.NodeFilter(??)
xml.dom.NotFoundErr(??)
xml.dom.NotSupportedErr(??)
xml.dom.SYNTAX_ERR
xml.dom.SyntaxErr(??)
xml.dom.UserDataHandler(??)
xml.dom.VALIDATION_ERR
xml.dom.ValidationErr(??)
xml.dom.WRONG_DOCUMENT_ERR
xml.dom.WrongDocumentErr(??)
xml.dom.XHTML_NAMESPACE
xml.dom.XMLNS_NAMESPACE
xml.dom.XML_NAMESPACE
xml.dom.domreg(??)
xml.dom.domreg.EmptyNodeList(??)
xml.dom.domreg.NodeList(??)
xml.dom.domreg.StringTypes(??)
xml.dom.domreg.defproperty(??)
xml.dom.domreg.getDOMImplementation(name = None, features = ()) -> DOM implementation.
xml.dom.domreg.registerDOMImplementation(name, factory)
xml.dom.domreg.registered(??)
xml.dom.domreg.well_known_implementations(??)
xml.dom.expatbuilder(??)
xml.dom.expatbuilder.CDATA_SECTION_NODE
xml.dom.expatbuilder.DOCUMENT_NODE
xml.dom.expatbuilder.EMPTY_NAMESPACE(??)
xml.dom.expatbuilder.EMPTY_PREFIX(??)
xml.dom.expatbuilder.ElementInfo(??)
xml.dom.expatbuilder.EmptyNodeList(??)
xml.dom.expatbuilder.ExpatBuilder(??)
xml.dom.expatbuilder.ExpatBuilderNS(??)
xml.dom.expatbuilder.FILTER_ACCEPT
xml.dom.expatbuilder.FILTER_INTERRUPT
xml.dom.expatbuilder.FILTER_REJECT
xml.dom.expatbuilder.FILTER_SKIP
xml.dom.expatbuilder.FilterCrutch(??)
xml.dom.expatbuilder.FilterVisibilityController(??)
xml.dom.expatbuilder.FragmentBuilder(??)
xml.dom.expatbuilder.FragmentBuilderNS(??)
xml.dom.expatbuilder.InternalSubsetExtractor(??)
xml.dom.expatbuilder.Namespaces(??)
xml.dom.expatbuilder.Node(??)
xml.dom.expatbuilder.NodeFilter(??)
xml.dom.expatbuilder.NodeList(??)
xml.dom.expatbuilder.ParseEscape(??)
xml.dom.expatbuilder.Rejecter(??)
xml.dom.expatbuilder.Skipper(??)
xml.dom.expatbuilder.StringTypes(??)
xml.dom.expatbuilder.TEXT_NODE
xml.dom.expatbuilder.XMLNS_NAMESPACE
xml.dom.expatbuilder.defproperty(??)
xml.dom.expatbuilder.expat(??)
xml.dom.expatbuilder.makeBuilder(??)
xml.dom.expatbuilder.minidom(??)
xml.dom.expatbuilder.parse(??)
xml.dom.expatbuilder.parseFragment(??)
xml.dom.expatbuilder.parseFragmentString(??)
xml.dom.expatbuilder.parseString(??)
xml.dom.expatbuilder.theDOMImplementation(??)
xml.dom.expatbuilder.xmlbuilder(??)
xml.dom.getDOMImplementation(name = None, features = ()) -> DOM implementation.
xml.dom.minicompat(??)
xml.dom.minicompat.EmptyNodeList(??)
xml.dom.minicompat.NodeList(??)
xml.dom.minicompat.StringTypes(??)
xml.dom.minicompat.defproperty(??)
xml.dom.minicompat.xml(??)
xml.dom.minidom(??)
xml.dom.minidom.Attr(??)
xml.dom.minidom.AttributeList(??)
xml.dom.minidom.CDATASection(??)
xml.dom.minidom.CharacterData(??)
xml.dom.minidom.Childless(??)
xml.dom.minidom.Comment(??)
xml.dom.minidom.DOMImplementation(??)
xml.dom.minidom.DOMImplementationLS(??)
xml.dom.minidom.Document(??)
xml.dom.minidom.DocumentFragment(??)
xml.dom.minidom.DocumentLS(??)
xml.dom.minidom.DocumentType(??)
xml.dom.minidom.EMPTY_NAMESPACE(??)
xml.dom.minidom.EMPTY_PREFIX(??)
xml.dom.minidom.Element(??)
xml.dom.minidom.ElementInfo(??)
xml.dom.minidom.EmptyNodeList(??)
xml.dom.minidom.Entity(??)
xml.dom.minidom.Identified(??)
xml.dom.minidom.NamedNodeMap(??)
xml.dom.minidom.Node(??)
xml.dom.minidom.NodeList(??)
xml.dom.minidom.Notation(??)
xml.dom.minidom.ProcessingInstruction(??)
xml.dom.minidom.ReadOnlySequentialNamedNodeMap(??)
xml.dom.minidom.StringTypes(??)
xml.dom.minidom.Text(??)
xml.dom.minidom.TypeInfo(??)
xml.dom.minidom.XMLNS_NAMESPACE
xml.dom.minidom.codecs(??)
xml.dom.minidom.defproperty(??)
xml.dom.minidom.domreg(??)
xml.dom.minidom.getDOMImplementation(??)
xml.dom.minidom.io(??)
xml.dom.minidom.parse(??)
xml.dom.minidom.parseString(??)
xml.dom.minidom.xml(??)
xml.dom.pulldom(??)
xml.dom.pulldom.CHARACTERS
xml.dom.pulldom.COMMENT
xml.dom.pulldom.DOMEventStream(??)
xml.dom.pulldom.END_DOCUMENT
xml.dom.pulldom.END_ELEMENT
xml.dom.pulldom.ErrorHandler(??)
xml.dom.pulldom.IGNORABLE_WHITESPACE
xml.dom.pulldom.PROCESSING_INSTRUCTION
xml.dom.pulldom.PullDOM(??)
xml.dom.pulldom.SAX2DOM(??)
xml.dom.pulldom.START_DOCUMENT
xml.dom.pulldom.START_ELEMENT
xml.dom.pulldom.default_bufsize
xml.dom.pulldom.parse(??)
xml.dom.pulldom.parseString(??)
xml.dom.pulldom.types(??)
xml.dom.pulldom.xml(??)
xml.dom.registerDOMImplementation(name, factory)
xml.dom.xmlbuilder(??)
xml.dom.xmlbuilder.DOMBuilder(??)
xml.dom.xmlbuilder.DOMBuilderFilter(??)
xml.dom.xmlbuilder.DOMEntityResolver(??)
xml.dom.xmlbuilder.DOMImplementationLS(??)
xml.dom.xmlbuilder.DOMInputSource(??)
xml.dom.xmlbuilder.DocumentLS(??)
xml.dom.xmlbuilder.Options(??)
xml.dom.xmlbuilder.copy(??)
xml.dom.xmlbuilder.xml(??)
xml.etree.ElementInclude(??)
xml.etree.ElementInclude.ElementTree(??)
xml.etree.ElementInclude.FatalIncludeError(??)
xml.etree.ElementInclude.XINCLUDE
xml.etree.ElementInclude.XINCLUDE_FALLBACK
xml.etree.ElementInclude.XINCLUDE_INCLUDE
xml.etree.ElementInclude.copy(??)
xml.etree.ElementInclude.default_loader(??)
xml.etree.ElementInclude.include(??)
xml.etree.ElementPath(??)
xml.etree.ElementPath.find(??)
xml.etree.ElementPath.findall(??)
xml.etree.ElementPath.findtext(??)
xml.etree.ElementPath.get_parent_map(??)
xml.etree.ElementPath.iterfind(??)
xml.etree.ElementPath.ops(??)
xml.etree.ElementPath.prepare_child(??)
xml.etree.ElementPath.prepare_descendant(??)
xml.etree.ElementPath.prepare_parent(??)
xml.etree.ElementPath.prepare_predicate(??)
xml.etree.ElementPath.prepare_self(??)
xml.etree.ElementPath.prepare_star(??)
xml.etree.ElementPath.re(??)
xml.etree.ElementPath.xpath_tokenizer(??)
xml.etree.ElementPath.xpath_tokenizer_re(??)
xml.etree.ElementTree(??)
xml.etree.ElementTree.Comment(??)
xml.etree.ElementTree.Element(??)
xml.etree.ElementTree.ElementPath(??)
xml.etree.ElementTree.ElementTree(??)
xml.etree.ElementTree.HTML_EMPTY(??)
xml.etree.ElementTree.PI(??)
xml.etree.ElementTree.ParseError(??)
xml.etree.ElementTree.ProcessingInstruction(??)
xml.etree.ElementTree.QName(??)
xml.etree.ElementTree.SubElement(??)
xml.etree.ElementTree.TreeBuilder(??)
xml.etree.ElementTree.VERSION
xml.etree.ElementTree.XML(??)
xml.etree.ElementTree.XMLID(??)
xml.etree.ElementTree.XMLParser(??)
xml.etree.ElementTree.XMLTreeBuilder(??)
xml.etree.ElementTree.dump(??)
xml.etree.ElementTree.fromstring(??)
xml.etree.ElementTree.fromstringlist(??)
xml.etree.ElementTree.iselement(??)
xml.etree.ElementTree.iterparse(??)
xml.etree.ElementTree.parse(??)
xml.etree.ElementTree.re(??)
xml.etree.ElementTree.register_namespace(??)
xml.etree.ElementTree.sys(??)
xml.etree.ElementTree.tostring(??)
xml.etree.ElementTree.tostringlist(??)
xml.etree.ElementTree.warnings(??)
xml.etree.cElementTree(??)
xml.etree.cElementTree.Comment(??)
xml.etree.cElementTree.Element(??)
xml.etree.cElementTree.ElementPath(??)
xml.etree.cElementTree.ElementTree(??)
xml.etree.cElementTree.PI(??)
xml.etree.cElementTree.ParseError(??)
xml.etree.cElementTree.ProcessingInstruction(??)
xml.etree.cElementTree.QName(??)
xml.etree.cElementTree.SubElement(??)
xml.etree.cElementTree.TreeBuilder(??)
xml.etree.cElementTree.VERSION
xml.etree.cElementTree.XML(??)
xml.etree.cElementTree.XMLID(??)
xml.etree.cElementTree.XMLParser(??)
xml.etree.cElementTree.XMLTreeBuilder(??)
xml.etree.cElementTree.dump(??)
xml.etree.cElementTree.fromstring(??)
xml.etree.cElementTree.fromstringlist(??)
xml.etree.cElementTree.iselement(??)
xml.etree.cElementTree.iterparse(??)
xml.etree.cElementTree.parse(??)
xml.etree.cElementTree.register_namespace(??)
xml.etree.cElementTree.tostring(??)
xml.etree.cElementTree.tostringlist(??)
xml.parsers(??)
xml.parsers.expat(??)
xml.parsers.expat.EXPAT_VERSION
xml.parsers.expat.ErrorString(errno) -> string
xml.parsers.expat.ExpatError(??)
xml.parsers.expat.ParserCreate([encoding[, namespace_separator]]) -> parser
xml.parsers.expat.XMLParserType(??)
xml.parsers.expat.XML_PARAM_ENTITY_PARSING_ALWAYS
xml.parsers.expat.XML_PARAM_ENTITY_PARSING_NEVER
xml.parsers.expat.XML_PARAM_ENTITY_PARSING_UNLESS_STANDALONE
xml.parsers.expat.error(??)
xml.parsers.expat.errors(??)
xml.parsers.expat.expat_CAPI(??)
xml.parsers.expat.features(??)
xml.parsers.expat.model(??)
xml.parsers.expat.native_encoding
xml.parsers.expat.sys(??)
xml.parsers.expat.version_info(??)
xml.sax(??)
xml.sax.ContentHandler(??)
xml.sax.ErrorHandler(??)
xml.sax.InputSource(??)
xml.sax.SAXException(??)
xml.sax.SAXNotRecognizedException(??)
xml.sax.SAXNotSupportedException(??)
xml.sax.SAXParseException(??)
xml.sax.SAXReaderNotAvailable(??)
xml.sax.default_parser_list(??)
xml.sax.expatreader(??)
xml.sax.expatreader.AttributesImpl(??)
xml.sax.expatreader.AttributesNSImpl(??)
xml.sax.expatreader.ExpatLocator(??)
xml.sax.expatreader.ExpatParser(??)
xml.sax.expatreader.SAXException(??)
xml.sax.expatreader.SAXNotRecognizedException(??)
xml.sax.expatreader.SAXNotSupportedException(??)
xml.sax.expatreader.SAXParseException(??)
xml.sax.expatreader.SAXReaderNotAvailable(??)
xml.sax.expatreader.create_parser(??)
xml.sax.expatreader.expat(??)
xml.sax.expatreader.feature_external_ges
xml.sax.expatreader.feature_external_pes
xml.sax.expatreader.feature_namespace_prefixes
xml.sax.expatreader.feature_namespaces
xml.sax.expatreader.feature_string_interning
xml.sax.expatreader.feature_validation
xml.sax.expatreader.handler(??)
xml.sax.expatreader.property_interning_dict
xml.sax.expatreader.property_xml_string
xml.sax.expatreader.saxutils(??)
xml.sax.expatreader.version
xml.sax.expatreader.xmlreader(??)
xml.sax.handler(??)
xml.sax.handler.ContentHandler(??)
xml.sax.handler.DTDHandler(??)
xml.sax.handler.EntityResolver(??)
xml.sax.handler.ErrorHandler(??)
xml.sax.handler.all_features(??)
xml.sax.handler.all_properties(??)
xml.sax.handler.feature_external_ges
xml.sax.handler.feature_external_pes
xml.sax.handler.feature_namespace_prefixes
xml.sax.handler.feature_namespaces
xml.sax.handler.feature_string_interning
xml.sax.handler.feature_validation
xml.sax.handler.property_declaration_handler
xml.sax.handler.property_dom_node
xml.sax.handler.property_encoding
xml.sax.handler.property_interning_dict
xml.sax.handler.property_lexical_handler
xml.sax.handler.property_xml_string
xml.sax.handler.version
xml.sax.make_parser(??)
xml.sax.parse(??)
xml.sax.parseString(??)
xml.sax.saxutils(??)
xml.sax.saxutils.XMLFilterBase(??)
xml.sax.saxutils.XMLGenerator(??)
xml.sax.saxutils.escape(??)
xml.sax.saxutils.handler(??)
xml.sax.saxutils.os(??)
xml.sax.saxutils.prepare_input_source(??)
xml.sax.saxutils.quoteattr(??)
xml.sax.saxutils.unescape(??)
xml.sax.saxutils.urllib(??)
xml.sax.saxutils.xmlreader(??)
xml.sax.xmlreader(??)
xml.sax.xmlreader.AttributesImpl(??)
xml.sax.xmlreader.AttributesNSImpl(??)
xml.sax.xmlreader.IncrementalParser(??)
xml.sax.xmlreader.InputSource(??)
xml.sax.xmlreader.Locator(??)
xml.sax.xmlreader.SAXNotRecognizedException(??)
xml.sax.xmlreader.SAXNotSupportedException(??)
xml.sax.xmlreader.XMLReader(??)
xml.sax.xmlreader.handler(??)
xmlrpc.client(??)
xmlrpc.client.APPLICATION_ERROR
xmlrpc.client.Binary(??)
xmlrpc.client.Boolean(??)
xmlrpc.client.BytesIO([buffer]) -> object
xmlrpc.client.DateTime(??)
xmlrpc.client.Error(??)
xmlrpc.client.ExpatParser(??)
xmlrpc.client.FastMarshaller(??)
xmlrpc.client.FastParser(??)
xmlrpc.client.FastUnmarshaller(??)
xmlrpc.client.Fault(??)
xmlrpc.client.GzipDecodedResponse(??)
xmlrpc.client.INTERNAL_ERROR
xmlrpc.client.INVALID_ENCODING_CHAR
xmlrpc.client.INVALID_METHOD_PARAMS
xmlrpc.client.INVALID_XMLRPC
xmlrpc.client.MAXINT
xmlrpc.client.METHOD_NOT_FOUND
xmlrpc.client.MININT
xmlrpc.client.Marshaller(??)
xmlrpc.client.MultiCall(??)
xmlrpc.client.MultiCallIterator(??)
xmlrpc.client.NOT_WELLFORMED_ERROR
xmlrpc.client.PARSE_ERROR
xmlrpc.client.ProtocolError(??)
xmlrpc.client.ResponseError(??)
xmlrpc.client.SERVER_ERROR
xmlrpc.client.SYSTEM_ERROR
xmlrpc.client.SafeTransport(??)
xmlrpc.client.Server(??)
xmlrpc.client.ServerProxy(??)
xmlrpc.client.TRANSPORT_ERROR
xmlrpc.client.Transport(??)
xmlrpc.client.UNSUPPORTED_ENCODING
xmlrpc.client.Unmarshaller(??)
xmlrpc.client.WRAPPERS(??)
xmlrpc.client.base64(??)
xmlrpc.client.boolean(??)
xmlrpc.client.datetime(??)
xmlrpc.client.dumps(??)
xmlrpc.client.errno(??)
xmlrpc.client.escape(??)
xmlrpc.client.expat(??)
xmlrpc.client.getparser() -> parser, unmarshaller
xmlrpc.client.gzip(??)
xmlrpc.client.gzip_decode(??)
xmlrpc.client.gzip_encode(??)
xmlrpc.client.http(??)
xmlrpc.client.io(??)
xmlrpc.client.loads(??)
xmlrpc.client.operator(??)
xmlrpc.client.re(??)
xmlrpc.client.socket(??)
xmlrpc.client.time(??)
xmlrpc.server(??)
xmlrpc.server.BaseHTTPRequestHandler(??)
xmlrpc.server.CGIXMLRPCRequestHandler(??)
xmlrpc.server.DocCGIXMLRPCRequestHandler(??)
xmlrpc.server.DocXMLRPCRequestHandler(??)
xmlrpc.server.DocXMLRPCServer(??)
xmlrpc.server.Fault(??)
xmlrpc.server.MultiPathXMLRPCServer(??)
xmlrpc.server.ServerHTMLDoc(??)
xmlrpc.server.SimpleXMLRPCDispatcher(??)
xmlrpc.server.SimpleXMLRPCRequestHandler(??)
xmlrpc.server.SimpleXMLRPCServer(??)
xmlrpc.server.XMLRPCDocGenerator(??)
xmlrpc.server.dumps(??)
xmlrpc.server.fcntl(??)
xmlrpc.server.gzip_decode(??)
xmlrpc.server.gzip_encode(??)
xmlrpc.server.http(??)
xmlrpc.server.inspect(??)
xmlrpc.server.list_public_methods(??)
xmlrpc.server.loads(??)
xmlrpc.server.os(??)
xmlrpc.server.pydoc(??)
xmlrpc.server.re(??)
xmlrpc.server.resolve_dotted_attribute(a, 'b.c.d') => a.b.c.d
xmlrpc.server.socketserver(??)
xmlrpc.server.sys(??)
xmlrpc.server.traceback(??)
zip(iter1 [,iter2 [...]]) --> zip object
zipfile.BadZipFile(??)
zipfile.BadZipfile(??)
zipfile.LargeZipFile(??)
zipfile.PyZipFile(??)
zipfile.ZIP64_LIMIT
zipfile.ZIP_DEFLATED
zipfile.ZIP_FILECOUNT_LIMIT
zipfile.ZIP_MAX_COMMENT
zipfile.ZIP_STORED
zipfile.ZipExtFile(??)
zipfile.ZipFile(??)
zipfile.ZipInfo(??)
zipfile.binascii(??)
zipfile.crc32(string[, start]) -- Compute a CRC-32 checksum of string.
zipfile.error(??)
zipfile.imp(??)
zipfile.io(??)
zipfile.is_zipfile(??)
zipfile.main(??)
zipfile.os(??)
zipfile.re(??)
zipfile.shutil(??)
zipfile.sizeCentralDir
zipfile.sizeEndCentDir
zipfile.sizeEndCentDir64
zipfile.sizeEndCentDir64Locator
zipfile.sizeFileHeader
zipfile.stat(??)
zipfile.stringCentralDir
zipfile.stringEndArchive
zipfile.stringEndArchive64
zipfile.stringEndArchive64Locator
zipfile.stringFileHeader
zipfile.struct(??)
zipfile.structCentralDir
zipfile.structEndArchive
zipfile.structEndArchive64
zipfile.structEndArchive64Locator
zipfile.structFileHeader
zipfile.sys(??)
zipfile.time(??)
zipfile.zlib(??)
zipimport.ZipImportError(??)
zipimport.zipimporter(archivepath) -> zipimporter object
