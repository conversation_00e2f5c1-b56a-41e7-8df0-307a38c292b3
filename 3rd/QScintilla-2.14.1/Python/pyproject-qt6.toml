# Specify the build system.
[build-system]
requires = ["sip >=6.0.2, <7", "PyQt-builder >=1.6, <2"]
build-backend = "sipbuild.api"

# Specify the PEP 566 metadata for the project.
[tool.sip.metadata]
name = "PyQt6-QScintilla"
version = "2.14.1"
summary = "Python bindings for the QScintilla programmers editor widget"
home-page = "https://www.riverbankcomputing.com/software/qscintilla/"
author = "Riverbank Computing Limited"
author-email = "<EMAIL>"
license = "GPL v3"
description-file = "README"
requires-dist = "PyQt6 (>=6.0.3)"
