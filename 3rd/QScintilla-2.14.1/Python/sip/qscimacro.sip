// This is the SIP interface definition for QsciMacro.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of QScintilla.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QsciMacro : QObject
{
%TypeHeaderCode
#include <Qsci/qscimacro.h>
%End

public:
    QsciMacro(QsciScintilla *parent /TransferThis/);
    QsciMacro(const QString &asc, QsciScintilla *parent /TransferThis/);

    virtual ~QsciMacro();

    void clear();
    bool load(const QString &asc);
    QString save() const;

public slots:
    virtual void play();
    virtual void startRecording();
    virtual void endRecording();

private:
    QsciMacro(const QsciMacro &);
};
