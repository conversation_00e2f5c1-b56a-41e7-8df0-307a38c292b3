// This is the SIP interface definition for QsciLexerHex.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of QScintilla.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QsciLexerHex : QsciLexer
{
%TypeHeaderCode
#include <Qsci/qscilexerhex.h>
%End

public:
    enum {
        Default,
        RecordStart,
        RecordType,
        UnknownRecordType,
        ByteCount,
        IncorrectByteCount,
        NoAddress,
        DataAddress,
        RecordCount,
        StartAddress,
        ExtendedAddress,
        OddData,
        EvenData,
        UnknownData,
        Checksum,
        IncorrectChecksum,
        TrailingGarbage,
    };

    QsciLexerHex(QObject *parent /TransferThis/ = 0);
    virtual ~QsciLexerHex();

    QColor defaultColor(int style) const;
    QFont defaultFont(int style) const;
    QColor defaultPaper(int style) const;
    QString description(int style) const;

private:
    QsciLexerHex(const QsciLexerHex &);
};
