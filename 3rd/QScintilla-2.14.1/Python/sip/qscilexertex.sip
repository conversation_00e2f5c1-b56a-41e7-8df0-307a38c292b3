// This is the SIP interface definition for QsciLexerTeX.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of QScintilla.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QsciLexerTeX : QsciLexer
{
%TypeHeaderCode
#include <Qsci/qscilexertex.h>
%End

public:
    enum {
        Default,
        Special,
        Group,
        Symbol,
        Command,
        Text
    };

    QsciLexerTeX(QObject *parent /TransferThis/ = 0);
    virtual ~QsciLexerTeX();

    const char *language() const;
    const char *lexer() const;
    QColor defaultColor(int style) const;
    const char *keywords(int set) const;
    QString description(int style) const;
    const char *wordCharacters() const;

    void refreshProperties();
    void setFoldComments(bool fold);
    bool foldComments() const;
    void setFoldCompact(bool fold);
    bool foldCompact() const;
    void setProcessComments(bool enable);
    bool processComments() const;
    void setProcessIf(bool enable);
    bool processIf() const;

protected:
    bool readProperties(QSettings &qs, const QString &prefix);
    bool writeProperties(QSettings &qs, const QString &prefix) const;

private:
    QsciLexerTeX(const QsciLexerTeX &);
};
