<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciLexerRuby Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="classQsciLexerRuby-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciLexerRuby Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscilexerruby.h&gt;</code></p>

<p>Inherits <a class="el" href="classQsciLexer.html">QsciLexer</a>.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a11f87d89b2ff7aae3066ae57b0addafd"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdaaa0287c846694faece595f55d26fca1c">Default</a> = 0, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda96bb579511d0e7c816783f3740de5aec">Error</a> = 1, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda4d3bcdc4618dd38c999f30ec64a2be94">Comment</a> = 2, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdad72bbb5f1aa4cd77945f45796235e38d">POD</a> = 3, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda9f9666ed92175c304b5733a425562a26">Number</a> = 4, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda64027d287f4c03b3c5c790277e6bbbc4">Keyword</a> = 5, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda29b34bc0b6d300d9eec4e7d4b8352ca6">DoubleQuotedString</a> = 6, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdabb7fbac71a097f21eb72fa0133f5c705">SingleQuotedString</a> = 7, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda73fc696ddb8d12d4a0568b85a690a180">ClassName</a> = 8, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdafc1f84fb3175d37e1a12822cdea2aabf">FunctionMethodName</a> = 9, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda620214bd8d8ed0e2839c4cc0c5143349">Operator</a> = 10, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda1a052fb80029ed8c2990a996b311081d">Identifier</a> = 11, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda939c896299ac5cc057aced1a059250a2">Regex</a> = 12, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdac07c560208a60c08d1fea27a862ce60a">Global</a> = 13, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdaf94cff4b54c0376f5c0e99ab3bf5cbee">Symbol</a> = 14, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda2e66ca91a2f6aa3f873e017b9d794710">ModuleName</a> = 15, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdac62e9eb3fad7c9f5ffd551e37116b2bb">InstanceVariable</a> = 16, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda8d3b74c47f0454a05b12f65ca98f13c1">ClassVariable</a> = 17, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda40dc4d5fdccc6fa1de6189a4e07d4345">Backticks</a> = 18, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda36e45c5ce435eacd1f9f140adf265a78">DataSection</a> = 19, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda1cf86e15ea041fc9e5d0700a56c1a220">HereDocumentDelimiter</a> = 20, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdae3c3ed5000ff843e47b7215bd175c0b6">HereDocument</a> = 21, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda4ddf93050bae629ab5c0f786e92809f6">PercentStringq</a> = 24, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda6c5d3e3f93635ec89652a2ef93763f8f">PercentStringQ</a> = 25, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdadff89867eee2f270b2bbf832a690a70c">PercentStringx</a> = 26, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda02679f74d964ce8130c528a3d75edeae">PercentStringr</a> = 27, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda3df92c38564360c4aa73c65abcad153a">PercentStringw</a> = 28, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda5a7463b193f6ad19397c1feead6b83b6">DemotedKeyword</a> = 29, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdabb88fb8ac7aadad4027a14bfe2aa329b">Stdin</a> = 30, 
<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda3d377471f1eb2d17957d8050ed4fdf6d">Stdout</a> = 31, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdae0c485bc9e3025341d39501600a5221d">Stderr</a> = 40
<br />
 }</td></tr>
<tr class="separator:a11f87d89b2ff7aae3066ae57b0addafd"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aa3bb000261e4752d89e06afe69d665f0"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#aa3bb000261e4752d89e06afe69d665f0">QsciLexerRuby</a> (QObject *parent=0)</td></tr>
<tr class="separator:aa3bb000261e4752d89e06afe69d665f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4fe52167ba709a506391026615d0ef7b"><td class="memItemLeft" align="right" valign="top"><a id="a4fe52167ba709a506391026615d0ef7b"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#a4fe52167ba709a506391026615d0ef7b">~QsciLexerRuby</a> ()</td></tr>
<tr class="separator:a4fe52167ba709a506391026615d0ef7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a700754468352f673157d08d4ff222e79"><td class="memItemLeft" align="right" valign="top"><a id="a700754468352f673157d08d4ff222e79"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#a700754468352f673157d08d4ff222e79">language</a> () const</td></tr>
<tr class="separator:a700754468352f673157d08d4ff222e79"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a14f1638b2f668fb7d98791cda719f8a0"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#a14f1638b2f668fb7d98791cda719f8a0">lexer</a> () const</td></tr>
<tr class="separator:a14f1638b2f668fb7d98791cda719f8a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aabf79a666eb40a912dfb7136d79f80e6"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#aabf79a666eb40a912dfb7136d79f80e6">blockEnd</a> (int *style=0) const</td></tr>
<tr class="separator:aabf79a666eb40a912dfb7136d79f80e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ecc2269f4b7a4956b7209082032245d"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#a7ecc2269f4b7a4956b7209082032245d">blockStart</a> (int *style=0) const</td></tr>
<tr class="separator:a7ecc2269f4b7a4956b7209082032245d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a47eb0ab494fe54b5518b4c8bdcd2968e"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#a47eb0ab494fe54b5518b4c8bdcd2968e">blockStartKeyword</a> (int *style=0) const</td></tr>
<tr class="separator:a47eb0ab494fe54b5518b4c8bdcd2968e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae7a6d23e6e8748210198b4fee3932144"><td class="memItemLeft" align="right" valign="top"><a id="ae7a6d23e6e8748210198b4fee3932144"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#ae7a6d23e6e8748210198b4fee3932144">braceStyle</a> () const</td></tr>
<tr class="separator:ae7a6d23e6e8748210198b4fee3932144"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a55b4fb34deedc4131e4f85fc4f7e01bc"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#a55b4fb34deedc4131e4f85fc4f7e01bc">defaultColor</a> (int style) const</td></tr>
<tr class="separator:a55b4fb34deedc4131e4f85fc4f7e01bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6e85b803ff580acecda16deaa70c758"><td class="memItemLeft" align="right" valign="top"><a id="aa6e85b803ff580acecda16deaa70c758"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#aa6e85b803ff580acecda16deaa70c758">defaultEolFill</a> (int style) const</td></tr>
<tr class="separator:aa6e85b803ff580acecda16deaa70c758"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6a8edb1b3ae833cd5c5a2b56cf1ec3e"><td class="memItemLeft" align="right" valign="top"><a id="ae6a8edb1b3ae833cd5c5a2b56cf1ec3e"></a>
QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#ae6a8edb1b3ae833cd5c5a2b56cf1ec3e">defaultFont</a> (int style) const</td></tr>
<tr class="separator:ae6a8edb1b3ae833cd5c5a2b56cf1ec3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af45a578123a772bdb293d326c29218dc"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#af45a578123a772bdb293d326c29218dc">defaultPaper</a> (int style) const</td></tr>
<tr class="separator:af45a578123a772bdb293d326c29218dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd6f026e6cb154c64c581f6e5f7f2fed"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#abd6f026e6cb154c64c581f6e5f7f2fed">keywords</a> (int set) const</td></tr>
<tr class="separator:abd6f026e6cb154c64c581f6e5f7f2fed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff36eb2ba5df9c4998eb9c8311f14de5"><td class="memItemLeft" align="right" valign="top">QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#aff36eb2ba5df9c4998eb9c8311f14de5">description</a> (int style) const</td></tr>
<tr class="separator:aff36eb2ba5df9c4998eb9c8311f14de5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf07311e229b5ec1370dd8a57873c1b6"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#abf07311e229b5ec1370dd8a57873c1b6">refreshProperties</a> ()</td></tr>
<tr class="separator:abf07311e229b5ec1370dd8a57873c1b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acf9bbfcaf3dfd6004428920e1c6572fd"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#acf9bbfcaf3dfd6004428920e1c6572fd">setFoldComments</a> (bool fold)</td></tr>
<tr class="separator:acf9bbfcaf3dfd6004428920e1c6572fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a846ebeb36f0847cee3599860f787bcde"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#a846ebeb36f0847cee3599860f787bcde">foldComments</a> () const</td></tr>
<tr class="separator:a846ebeb36f0847cee3599860f787bcde"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e83f239ecb3c52bf4930412f32f51f1"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#a0e83f239ecb3c52bf4930412f32f51f1">setFoldCompact</a> (bool fold)</td></tr>
<tr class="separator:a0e83f239ecb3c52bf4930412f32f51f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acbaa96d72ad071768acc25d7d56b6324"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#acbaa96d72ad071768acc25d7d56b6324">foldCompact</a> () const</td></tr>
<tr class="separator:acbaa96d72ad071768acc25d7d56b6324"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a> (QObject *parent=0)</td></tr>
<tr class="separator:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="af6cc5bb9d9421d806e9941d018030068"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a> ()</td></tr>
<tr class="separator:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a> () const</td></tr>
<tr class="separator:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a> () const</td></tr>
<tr class="separator:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a6504a6fff35af16fbfd97889048db2a5"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a> () const</td></tr>
<tr class="separator:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e10650b0e9ad137062ad5c17ad33e76 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QStringList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">autoCompletionWordSeparators</a> () const</td></tr>
<tr class="separator:a4e10650b0e9ad137062ad5c17ad33e76 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a> ()</td></tr>
<tr class="separator:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a> () const</td></tr>
<tr class="separator:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="afccca7eb1aed463f89ac442d99135839"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a> () const</td></tr>
<tr class="separator:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a> (int style) const</td></tr>
<tr class="separator:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a> (int style) const</td></tr>
<tr class="separator:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a> (int style) const</td></tr>
<tr class="separator:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="aff4735542e937c5e35ecb2eb82e8f875"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a> () const</td></tr>
<tr class="separator:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a> () const</td></tr>
<tr class="separator:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a> (int style) const</td></tr>
<tr class="separator:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">defaultColor</a> () const</td></tr>
<tr class="separator:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">defaultFont</a> () const</td></tr>
<tr class="separator:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">defaultPaper</a> () const</td></tr>
<tr class="separator:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciScintilla.html">QsciScintilla</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a> () const</td></tr>
<tr class="separator:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a> (<a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *<a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>)</td></tr>
<tr class="separator:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a> (const QFont &amp;f)</td></tr>
<tr class="separator:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a1e81186b1f8f8bc2a4901a42cbca568a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>setEditor</b> (<a class="el" href="classQsciScintilla.html">QsciScintilla</a> *<a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>)</td></tr>
<tr class="separator:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td></tr>
<tr class="separator:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a> () const</td></tr>
<tr class="separator:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aace68e3dbcef9da1b031fb9cfd843c57 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aace68e3dbcef9da1b031fb9cfd843c57">wordCharacters</a> () const</td></tr>
<tr class="separator:aace68e3dbcef9da1b031fb9cfd843c57 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td></tr>
<tr class="separator:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:afa0b9ecea2700420820e4e9b705cb784"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#afa0b9ecea2700420820e4e9b705cb784">readProperties</a> (QSettings &amp;qs, const QString &amp;prefix)</td></tr>
<tr class="separator:afa0b9ecea2700420820e4e9b705cb784"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af187d6973df01f3f704b181a446ea2f5"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerRuby.html#af187d6973df01f3f704b181a446ea2f5">writeProperties</a> (QSettings &amp;qs, const QString &amp;prefix) const</td></tr>
<tr class="separator:af187d6973df01f3f704b181a446ea2f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pro_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a41d4521504d63ee63d43fd7ed0c003ee"></a>
QByteArray&#160;</td><td class="memItemRight" valign="bottom"><b>textAsBytes</b> (const QString &amp;text) const</td></tr>
<tr class="separator:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a5261dd606c209a5c6a494e608a9a111a"></a>
QString&#160;</td><td class="memItemRight" valign="bottom"><b>bytesAsText</b> (const char *bytes, int size) const</td></tr>
<tr class="separator:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_slots_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_slots_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Slots inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a> (int autoindentstyle)</td></tr>
<tr class="separator:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a> (bool eoffill, int style=-1)</td></tr>
<tr class="separator:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a> (const QFont &amp;f, int style=-1)</td></tr>
<tr class="separator:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header signals_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('signals_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Signals inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a> (bool eolfilled, int style)</td></tr>
<tr class="separator:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a> (const QFont &amp;f, int style)</td></tr>
<tr class="separator:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a> (const char *prop, const char *val)</td></tr>
<tr class="separator:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciLexerRuby.html" title="The QsciLexerRuby class encapsulates the Scintilla Ruby lexer.">QsciLexerRuby</a> class encapsulates the Scintilla Ruby lexer. </p>
</div><h2 class="groupheader">Member Enumeration Documentation</h2>
<a id="a11f87d89b2ff7aae3066ae57b0addafd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a11f87d89b2ff7aae3066ae57b0addafd">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the meanings of the different styles used by the Ruby lexer. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafdaaa0287c846694faece595f55d26fca1c"></a>Default&#160;</td><td class="fielddoc"><p>The default. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda96bb579511d0e7c816783f3740de5aec"></a>Error&#160;</td><td class="fielddoc"><p>An error. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda4d3bcdc4618dd38c999f30ec64a2be94"></a>Comment&#160;</td><td class="fielddoc"><p>A comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafdad72bbb5f1aa4cd77945f45796235e38d"></a>POD&#160;</td><td class="fielddoc"><p>A POD. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda9f9666ed92175c304b5733a425562a26"></a>Number&#160;</td><td class="fielddoc"><p>A number. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda64027d287f4c03b3c5c790277e6bbbc4"></a>Keyword&#160;</td><td class="fielddoc"><p>A keyword. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda29b34bc0b6d300d9eec4e7d4b8352ca6"></a>DoubleQuotedString&#160;</td><td class="fielddoc"><p>A double-quoted string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafdabb7fbac71a097f21eb72fa0133f5c705"></a>SingleQuotedString&#160;</td><td class="fielddoc"><p>A single-quoted string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda73fc696ddb8d12d4a0568b85a690a180"></a>ClassName&#160;</td><td class="fielddoc"><p>The name of a class. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafdafc1f84fb3175d37e1a12822cdea2aabf"></a>FunctionMethodName&#160;</td><td class="fielddoc"><p>The name of a function or method. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda620214bd8d8ed0e2839c4cc0c5143349"></a>Operator&#160;</td><td class="fielddoc"><p>An operator. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda1a052fb80029ed8c2990a996b311081d"></a>Identifier&#160;</td><td class="fielddoc"><p>An identifier. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda939c896299ac5cc057aced1a059250a2"></a>Regex&#160;</td><td class="fielddoc"><p>A regular expression. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafdac07c560208a60c08d1fea27a862ce60a"></a>Global&#160;</td><td class="fielddoc"><p>A global. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafdaf94cff4b54c0376f5c0e99ab3bf5cbee"></a>Symbol&#160;</td><td class="fielddoc"><p>A symbol. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda2e66ca91a2f6aa3f873e017b9d794710"></a>ModuleName&#160;</td><td class="fielddoc"><p>The name of a module. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafdac62e9eb3fad7c9f5ffd551e37116b2bb"></a>InstanceVariable&#160;</td><td class="fielddoc"><p>An instance variable. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda8d3b74c47f0454a05b12f65ca98f13c1"></a>ClassVariable&#160;</td><td class="fielddoc"><p>A class variable. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda40dc4d5fdccc6fa1de6189a4e07d4345"></a>Backticks&#160;</td><td class="fielddoc"><p>Backticks. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda36e45c5ce435eacd1f9f140adf265a78"></a>DataSection&#160;</td><td class="fielddoc"><p>A data section. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda1cf86e15ea041fc9e5d0700a56c1a220"></a>HereDocumentDelimiter&#160;</td><td class="fielddoc"><p>A here document delimiter. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafdae3c3ed5000ff843e47b7215bd175c0b6"></a>HereDocument&#160;</td><td class="fielddoc"><p>A here document. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda4ddf93050bae629ab5c0f786e92809f6"></a>PercentStringq&#160;</td><td class="fielddoc"><p>A q string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda6c5d3e3f93635ec89652a2ef93763f8f"></a>PercentStringQ&#160;</td><td class="fielddoc"><p>A Q string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafdadff89867eee2f270b2bbf832a690a70c"></a>PercentStringx&#160;</td><td class="fielddoc"><p>A x string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda02679f74d964ce8130c528a3d75edeae"></a>PercentStringr&#160;</td><td class="fielddoc"><p>A r string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda3df92c38564360c4aa73c65abcad153a"></a>PercentStringw&#160;</td><td class="fielddoc"><p>A w string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda5a7463b193f6ad19397c1feead6b83b6"></a>DemotedKeyword&#160;</td><td class="fielddoc"><p>A demoted keyword. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafdabb88fb8ac7aadad4027a14bfe2aa329b"></a>Stdin&#160;</td><td class="fielddoc"><p>stdin. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafda3d377471f1eb2d17957d8050ed4fdf6d"></a>Stdout&#160;</td><td class="fielddoc"><p>stdout. </p>
</td></tr>
<tr><td class="fieldname"><a id="a11f87d89b2ff7aae3066ae57b0addafdae0c485bc9e3025341d39501600a5221d"></a>Stderr&#160;</td><td class="fielddoc"><p>stderr. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="aa3bb000261e4752d89e06afe69d665f0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa3bb000261e4752d89e06afe69d665f0">&#9670;&nbsp;</a></span>QsciLexerRuby()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciLexerRuby::QsciLexerRuby </td>
          <td>(</td>
          <td class="paramtype">QObject *&#160;</td>
          <td class="paramname"><em>parent</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Construct a <a class="el" href="classQsciLexerRuby.html" title="The QsciLexerRuby class encapsulates the Scintilla Ruby lexer.">QsciLexerRuby</a> with parent <em>parent</em>. <em>parent</em> is typically the <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> instance. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a14f1638b2f668fb7d98791cda719f8a0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a14f1638b2f668fb7d98791cda719f8a0">&#9670;&nbsp;</a></span>lexer()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerRuby::lexer </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the name of the lexer. Some lexers support a number of languages. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ac7c40b97187e23ab85f6d95113f91b39">QsciLexer</a>.</p>

</div>
</div>
<a id="aabf79a666eb40a912dfb7136d79f80e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aabf79a666eb40a912dfb7136d79f80e6">&#9670;&nbsp;</a></span>blockEnd()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerRuby::blockEnd </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of words or characters in a particular style that define the end of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">QsciLexer</a>.</p>

</div>
</div>
<a id="a7ecc2269f4b7a4956b7209082032245d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7ecc2269f4b7a4956b7209082032245d">&#9670;&nbsp;</a></span>blockStart()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerRuby::blockStart </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of words or characters in a particular style that define the start of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">QsciLexer</a>.</p>

</div>
</div>
<a id="a47eb0ab494fe54b5518b4c8bdcd2968e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a47eb0ab494fe54b5518b4c8bdcd2968e">&#9670;&nbsp;</a></span>blockStartKeyword()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerRuby::blockStartKeyword </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of keywords in a particular style that define the start of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">QsciLexer</a>.</p>

</div>
</div>
<a id="a55b4fb34deedc4131e4f85fc4f7e01bc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a55b4fb34deedc4131e4f85fc4f7e01bc">&#9670;&nbsp;</a></span>defaultColor()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerRuby::defaultColor </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the foreground colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd>defaultpaper() </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#af7508f1b816a2c9446d36141edc9b5ce">QsciLexer</a>.</p>

</div>
</div>
<a id="af45a578123a772bdb293d326c29218dc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af45a578123a772bdb293d326c29218dc">&#9670;&nbsp;</a></span>defaultPaper()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerRuby::defaultPaper </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the background colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerRuby.html#a55b4fb34deedc4131e4f85fc4f7e01bc">defaultColor()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a7e5ab7f541d913760c32abedbdc72963">QsciLexer</a>.</p>

</div>
</div>
<a id="abd6f026e6cb154c64c581f6e5f7f2fed"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abd6f026e6cb154c64c581f6e5f7f2fed">&#9670;&nbsp;</a></span>keywords()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerRuby::keywords </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>set</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the set of keywords for the keyword set <em>set</em> recognised by the lexer as a space separated string. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a013b7c1bf9846e231b97827dfd9540b0">QsciLexer</a>.</p>

</div>
</div>
<a id="aff36eb2ba5df9c4998eb9c8311f14de5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aff36eb2ba5df9c4998eb9c8311f14de5">&#9670;&nbsp;</a></span>description()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QString QsciLexerRuby::description </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the descriptive name for style number <em>style</em>. If the style is invalid for this language then an empty QString is returned. This is intended to be used in user preference dialogs. </p>

<p>Implements <a class="el" href="classQsciLexer.html#add9c20adb43bc38d1a0ca3083ac3e6fa">QsciLexer</a>.</p>

</div>
</div>
<a id="abf07311e229b5ec1370dd8a57873c1b6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abf07311e229b5ec1370dd8a57873c1b6">&#9670;&nbsp;</a></span>refreshProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerRuby::refreshProperties </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Causes all properties to be refreshed by emitting the <a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged()</a> signal as required. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ae508c3ab4ce1f338dfff3ddf5ee7e34c">QsciLexer</a>.</p>

</div>
</div>
<a id="acf9bbfcaf3dfd6004428920e1c6572fd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acf9bbfcaf3dfd6004428920e1c6572fd">&#9670;&nbsp;</a></span>setFoldComments()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerRuby::setFoldComments </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then multi-line comment blocks can be folded. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerRuby.html#a846ebeb36f0847cee3599860f787bcde">foldComments()</a> </dd></dl>

</div>
</div>
<a id="a846ebeb36f0847cee3599860f787bcde"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a846ebeb36f0847cee3599860f787bcde">&#9670;&nbsp;</a></span>foldComments()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerRuby::foldComments </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if multi-line comment blocks can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerRuby.html#acf9bbfcaf3dfd6004428920e1c6572fd">setFoldComments()</a> </dd></dl>

</div>
</div>
<a id="a0e83f239ecb3c52bf4930412f32f51f1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0e83f239ecb3c52bf4930412f32f51f1">&#9670;&nbsp;</a></span>setFoldCompact()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerRuby::setFoldCompact </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then trailing blank lines are included in a fold block. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerRuby.html#acbaa96d72ad071768acc25d7d56b6324">foldCompact()</a> </dd></dl>

</div>
</div>
<a id="acbaa96d72ad071768acc25d7d56b6324"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acbaa96d72ad071768acc25d7d56b6324">&#9670;&nbsp;</a></span>foldCompact()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerRuby::foldCompact </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if trailing blank lines are included in a fold block.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerRuby.html#a0e83f239ecb3c52bf4930412f32f51f1">setFoldCompact()</a> </dd></dl>

</div>
</div>
<a id="afa0b9ecea2700420820e4e9b705cb784"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afa0b9ecea2700420820e4e9b705cb784">&#9670;&nbsp;</a></span>readProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerRuby::readProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are read from the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ad472b16506a4cbc19634f07aa90f1ea6">QsciLexer</a>.</p>

</div>
</div>
<a id="af187d6973df01f3f704b181a446ea2f5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af187d6973df01f3f704b181a446ea2f5">&#9670;&nbsp;</a></span>writeProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerRuby::writeProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are written to the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#abccc4e010b724df1a7b5c5f3bce29501">QsciLexer</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
