<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciLexerPerl Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9aebe8e5c6b96783494e61c8cd03975570">Array</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a0e4df63d7d5714b1bdb71c1975f7f99c">autoCompletionWordSeparators</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9aa75c7ba5ad98b870f0e303c94f0b9375">BacktickHereDocument</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a7fb5b3d78cf402664941ceee7a17d758">BacktickHereDocumentVar</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a53d80aaaa00ca3d47433a05bc93297c8">Backticks</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a1042900000e9c99d0a52724d5c838c94">BackticksVar</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a42133f1b4127c78674f89e3209236a18">blockEnd</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#ae33c3f0e337cfe173c61ea86c5cd3591">blockStart</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a3e90db838034f7404e65b2e284403604">braceStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bytesAsText</b>(const char *bytes, int size) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9af2c29ccb75997807734f024b49998b6a">Comment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a8c119f4794e3dab23aa2a4f739a1e81f">DataSection</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9af5df69c0ed6d8c42bc39e717889aea78">Default</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a3ec3d302e4ad33ca360d3edbe14ac561">defaultColor</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">QsciLexer::defaultColor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a242188212df611073f78d1eff326f5d5">defaultEolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a1deaafed565aeae806e4ea6083baa186">defaultFont</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">QsciLexer::defaultFont</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#afa54795b596b6bc9f3664865b9d76484">defaultPaper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">QsciLexer::defaultPaper</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a386c817d87735b2dd347735cb264d548">description</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a0a82588ab552f48b9caeb05db6d9428f">DoubleQuotedHereDocument</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9ae9c623b599443071c8bb547279c7dd64">DoubleQuotedHereDocumentVar</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9aa14ec9d32c1fa5bbf171a3fb45473bcf">DoubleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a87d5e90f82615a99e0af4ccc4875dc65">DoubleQuotedStringVar</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a>(bool eolfilled, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a99a1c9873cd83852da55023a2420f5a8">Error</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a696abf6da5415e772e5ade8752eac3b2">foldAtElse</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#ae9881257bbcc887cdbe21e74bbb8ea65">foldComments</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a0c3e05e1bbdc4614fc7e76e508178592">foldCompact</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a5ea4eb1e65b2cee23a09f143074790b4">foldPackages</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#ab58e1e9d037d280fc74792ace83936d4">foldPODBlocks</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a>(const QFont &amp;f, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9adbc0e3fbe443a92d03f444a1f66b1d5c">FormatBody</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9af1b0ae17967c8b101eea3d9e4cc173cd">FormatIdentifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9aaa260a4964100f84e24f3797150379ac">Hash</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a1735d83cde8be27eda10acb6f7e2ed98">HereDocumentDelimiter</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a65e52532e4624b84e6f3cd89b37a48b8">Identifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9ae431c41ecbd64bf0f773f25b68a7973a">Keyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a57958c564d4d3127e7ee6148d232bd4b">keywords</a>(int set) const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a16fb82e08452dc260bdda610817c79ea">language</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#aae9e42584c6466a8b859d56218eaf28c">lexer</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a65ab3c30be465884145bee390d038a8f">Number</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a2c43ed725d5edb523abb214f6867a5f4">Operator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a64e30800729f8ef4d273130a90b62704">POD</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9aecb9bf65704610bce3bf8dbfdbce40a4">PODVerbatim</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a>(const char *prop, const char *val)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a36359d3e1cb6037b561f95fccf16881e">QsciLexerPerl</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a4eb6e937e8713d00368651dbeada3b74">QuotedStringQ</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a93f9be9adc1bcc1289155cca445eb860">QuotedStringQQ</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a3a4919b9d5dfefc405bd70d8f6ce780a">QuotedStringQQVar</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a7931aea4826aceb60ba2aab3fd7b5884">QuotedStringQR</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a5dd29ed0e2532a609155a9f2279cda6b">QuotedStringQRVar</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9ad9cf54861772d7a5cf696c4bb4be04cd">QuotedStringQW</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a500ce87c3501f0e8d86db52eefdc7b8e">QuotedStringQX</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a4bd8b77cc8ca06d77281c7c146b7f9be">QuotedStringQXVar</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a47884fcfd8d2b0ab7b8d277cb0325c17">readProperties</a>(QSettings &amp;qs, const QString &amp;prefix)</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#ac9868e2d0efbf3602a22d8bdac12a119">refreshProperties</a>()</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a1ba94b26ebb6f719bfec1e2fc5c180a7">Regex</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a52496f8867a7285b205ef55fb014d84e">RegexVar</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a8fe3c7834c771123699097248a2a97fa">Scalar</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a>(QsciAbstractAPIs *apis)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a>(int autoindentstyle)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a>(const QFont &amp;f)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>setEditor</b>(QsciScintilla *editor) (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a>(bool eoffill, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a14705cac9643949facd57641e0892fb0">setFoldAtElse</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a85aa8e72d81818a7edea1867362db16a">setFoldComments</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a269b1c3c788ae863939fd8b1749a5abf">setFoldCompact</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a5e2cdbcaa57b02f18d65aea89d2faa54">setFoldPackages</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#af0ee6abab37e283e68f527c597c50877">setFoldPODBlocks</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a>(const QFont &amp;f, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a73e0d55813d2d21a060a9e1e59360506">SingleQuotedHereDocument</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a2cf9c05452a47bcde418b4cf691bbcd1">SingleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9aa77b69ca726faae33472a1ff018d54af">SubroutinePrototype</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a3edcaf1beac4277212faf8f30c8271b9">Substitution</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a7c0194dff17baffd0e9592b581944fda">SubstitutionVar</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9adfaa14e55f48f7774f991a73f8a7fadc">SymbolTable</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>textAsBytes</b>(const QString &amp;text) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a84d882c68a32f9eefcfc6ad3ff953c6e">Translation</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a5ffd80ff37350acb6fe03f798f34a912">wordCharacters</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPerl.html#a16841e0262d8200d5ed3a85099d45b37">writeProperties</a>(QSettings &amp;qs, const QString &amp;prefix) const</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPerl.html#a6f87282ec40dbc5e752dc0bc0aec87a0">~QsciLexerPerl</a>()</td><td class="entry"><a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
