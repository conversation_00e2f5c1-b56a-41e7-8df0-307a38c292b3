<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciLexerCSS Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-slots">Public Slots</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="classQsciLexerCSS-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciLexerCSS Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscilexercss.h&gt;</code></p>

<p>Inherits <a class="el" href="classQsciLexer.html">QsciLexer</a>.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a8f38d12d56564b95f6f1f6b1834ca3e0"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0a53e968c600f53a2bae2710db0ef8db1d">Default</a> = 0, 
<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0a1f6473d0fc2f6bddc22eea9d01ea05ad">Tag</a> = 1, 
<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0aafd65314dc8f5e87697c987a8a3d1037">ClassSelector</a> = 2, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0ad9a4a9e2945073685158769a62e16cda">PseudoClass</a> = 3, 
<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0a6ae93503184d2ba66879daeefaeb4b23">UnknownPseudoClass</a> = 4, 
<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0ab7ce96396ee8eff4196b5b9d5bf53afc">Operator</a> = 5, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0a729b64d3f84347da91167d421302a76d">CSS1Property</a> = 6, 
<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0af3110ffc53b23fa5dbe17151dc7a5e75">UnknownProperty</a> = 7, 
<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0acdf1ff5843500ebbfe209bff6c0e4370">Value</a> = 8, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0a3496565bdaf261864ed37cd0909687be">Comment</a> = 9, 
<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0ad59613421106ae8b7a7594812a4091b5">IDSelector</a> = 10, 
<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0a8b419e8c3650bc498dc90610ccda4f1a">Important</a> = 11, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0ab66090eb95d05ecb61cb8a4822ab94d8">AtRule</a> = 12, 
<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0a34b937f20071fd25a7b7aa57620cb5d5">DoubleQuotedString</a> = 13, 
<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0a0e475783c35d0707225bfc28edd36d2e">SingleQuotedString</a> = 14, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0aae08a7b509d7f18df60133b2e204291b">CSS2Property</a> = 15, 
<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0ac1a1825aa643819ef0ed1c3a23ce48ee">Attribute</a> = 16, 
<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0ade6d6fd36b5e81bcca8ce404d915a16b">CSS3Property</a> = 17, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0aaad0b9e8982d95504f3fdeac29c01d68">PseudoElement</a> = 18, 
<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0aea3a8406f16545255905240042c4954b">ExtendedCSSProperty</a> = 19, 
<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0aeee973f3f30ab97b97903de6de2dfca5">ExtendedPseudoClass</a> = 20, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0a220432c41649811f99607624ddb568e6">ExtendedPseudoElement</a> = 21, 
<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0aa791b33014a708120356e7ea9365bfeb">MediaRule</a> = 22, 
<a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0ab7b8d54656b8a5eb94be142ad027f672">Variable</a> = 23
<br />
 }</td></tr>
<tr class="separator:a8f38d12d56564b95f6f1f6b1834ca3e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-slots"></a>
Public Slots</h2></td></tr>
<tr class="memitem:a5f77be4cb83422d47220c5b38d9f0a99"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#a5f77be4cb83422d47220c5b38d9f0a99">setFoldComments</a> (bool fold)</td></tr>
<tr class="separator:a5f77be4cb83422d47220c5b38d9f0a99"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf9762aeea19ed1c8d6766a9e6a52cd3"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#aaf9762aeea19ed1c8d6766a9e6a52cd3">setFoldCompact</a> (bool fold)</td></tr>
<tr class="separator:aaf9762aeea19ed1c8d6766a9e6a52cd3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_slots_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_slots_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Slots inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a> (int autoindentstyle)</td></tr>
<tr class="separator:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a> (bool eoffill, int style=-1)</td></tr>
<tr class="separator:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a> (const QFont &amp;f, int style=-1)</td></tr>
<tr class="separator:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:acbf55e58ad04813101573146ecc43c67"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#acbf55e58ad04813101573146ecc43c67">QsciLexerCSS</a> (QObject *parent=0)</td></tr>
<tr class="separator:acbf55e58ad04813101573146ecc43c67"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a377ab8d8a44c7fff2f355cc8ed45e611"><td class="memItemLeft" align="right" valign="top"><a id="a377ab8d8a44c7fff2f355cc8ed45e611"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#a377ab8d8a44c7fff2f355cc8ed45e611">~QsciLexerCSS</a> ()</td></tr>
<tr class="separator:a377ab8d8a44c7fff2f355cc8ed45e611"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c29f0bbe4d09c159040b5676c8143d2"><td class="memItemLeft" align="right" valign="top"><a id="a2c29f0bbe4d09c159040b5676c8143d2"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#a2c29f0bbe4d09c159040b5676c8143d2">language</a> () const</td></tr>
<tr class="separator:a2c29f0bbe4d09c159040b5676c8143d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1fa70c8e86dd88d34508fc652d30f3f7"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#a1fa70c8e86dd88d34508fc652d30f3f7">lexer</a> () const</td></tr>
<tr class="separator:a1fa70c8e86dd88d34508fc652d30f3f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada48a387b3e1414927bebe2415de75f8"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#ada48a387b3e1414927bebe2415de75f8">blockEnd</a> (int *style=0) const</td></tr>
<tr class="separator:ada48a387b3e1414927bebe2415de75f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aae249ec529d5f7de5fa238de9208058d"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#aae249ec529d5f7de5fa238de9208058d">blockStart</a> (int *style=0) const</td></tr>
<tr class="separator:aae249ec529d5f7de5fa238de9208058d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7cbe39118747739dd557df191c91db0c"><td class="memItemLeft" align="right" valign="top"><a id="a7cbe39118747739dd557df191c91db0c"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#a7cbe39118747739dd557df191c91db0c">wordCharacters</a> () const</td></tr>
<tr class="separator:a7cbe39118747739dd557df191c91db0c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a98140e8245532496f7ed97bcaa8671f3"><td class="memItemLeft" align="right" valign="top"><a id="a98140e8245532496f7ed97bcaa8671f3"></a>
QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#a98140e8245532496f7ed97bcaa8671f3">defaultColor</a> (int style) const</td></tr>
<tr class="separator:a98140e8245532496f7ed97bcaa8671f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a1cd44b041d1d4f4c11c22f91de99c3"><td class="memItemLeft" align="right" valign="top"><a id="a2a1cd44b041d1d4f4c11c22f91de99c3"></a>
QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#a2a1cd44b041d1d4f4c11c22f91de99c3">defaultFont</a> (int style) const</td></tr>
<tr class="separator:a2a1cd44b041d1d4f4c11c22f91de99c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41d04b17da9c84a94289e91323fb5206"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#a41d04b17da9c84a94289e91323fb5206">keywords</a> (int set) const</td></tr>
<tr class="separator:a41d04b17da9c84a94289e91323fb5206"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aca9a53a01d50ef44d9f5ac0fd662bf84"><td class="memItemLeft" align="right" valign="top">QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#aca9a53a01d50ef44d9f5ac0fd662bf84">description</a> (int style) const</td></tr>
<tr class="separator:aca9a53a01d50ef44d9f5ac0fd662bf84"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a78f4690fa92e02c8511074a334c06096"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#a78f4690fa92e02c8511074a334c06096">refreshProperties</a> ()</td></tr>
<tr class="separator:a78f4690fa92e02c8511074a334c06096"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae32591385112bb3d33de8c1c40888190"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#ae32591385112bb3d33de8c1c40888190">foldComments</a> () const</td></tr>
<tr class="separator:ae32591385112bb3d33de8c1c40888190"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aecbd103b6dff15873e60cdf48e634a4a"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#aecbd103b6dff15873e60cdf48e634a4a">foldCompact</a> () const</td></tr>
<tr class="separator:aecbd103b6dff15873e60cdf48e634a4a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9e61fa490e6e6c1480f3de5187ffed02"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#a9e61fa490e6e6c1480f3de5187ffed02">setHSSLanguage</a> (bool enabled)</td></tr>
<tr class="separator:a9e61fa490e6e6c1480f3de5187ffed02"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a95a8b4cc2d6f8437c8feada8f518daff"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#a95a8b4cc2d6f8437c8feada8f518daff">HSSLanguage</a> () const</td></tr>
<tr class="separator:a95a8b4cc2d6f8437c8feada8f518daff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a388e532d847652dbf18207593e236e5e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#a388e532d847652dbf18207593e236e5e">setLessLanguage</a> (bool enabled)</td></tr>
<tr class="separator:a388e532d847652dbf18207593e236e5e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a26df830be43cb0ca067b5e0ad037171a"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#a26df830be43cb0ca067b5e0ad037171a">LessLanguage</a> () const</td></tr>
<tr class="separator:a26df830be43cb0ca067b5e0ad037171a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a2195f681df3657fbadf72c55003863"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#a2a2195f681df3657fbadf72c55003863">setSCSSLanguage</a> (bool enabled)</td></tr>
<tr class="separator:a2a2195f681df3657fbadf72c55003863"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae8630fee6378af65bbd772b8f20fe4c9"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#ae8630fee6378af65bbd772b8f20fe4c9">SCSSLanguage</a> () const</td></tr>
<tr class="separator:ae8630fee6378af65bbd772b8f20fe4c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a> (QObject *parent=0)</td></tr>
<tr class="separator:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="af6cc5bb9d9421d806e9941d018030068"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a> ()</td></tr>
<tr class="separator:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a> () const</td></tr>
<tr class="separator:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a> () const</td></tr>
<tr class="separator:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a6504a6fff35af16fbfd97889048db2a5"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a> () const</td></tr>
<tr class="separator:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e10650b0e9ad137062ad5c17ad33e76 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QStringList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">autoCompletionWordSeparators</a> () const</td></tr>
<tr class="separator:a4e10650b0e9ad137062ad5c17ad33e76 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a> ()</td></tr>
<tr class="separator:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a> () const</td></tr>
<tr class="separator:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf12117a142b6f68479ea425d80a4196 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a> (int *style=0) const</td></tr>
<tr class="separator:abf12117a142b6f68479ea425d80a4196 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:affe136114d62180e9a14caa81f2b7fd5 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="affe136114d62180e9a14caa81f2b7fd5"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#affe136114d62180e9a14caa81f2b7fd5">braceStyle</a> () const</td></tr>
<tr class="separator:affe136114d62180e9a14caa81f2b7fd5 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="afccca7eb1aed463f89ac442d99135839"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a> () const</td></tr>
<tr class="separator:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a> (int style) const</td></tr>
<tr class="separator:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a> (int style) const</td></tr>
<tr class="separator:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a> (int style) const</td></tr>
<tr class="separator:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="aff4735542e937c5e35ecb2eb82e8f875"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a> () const</td></tr>
<tr class="separator:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a> () const</td></tr>
<tr class="separator:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a> (int style) const</td></tr>
<tr class="separator:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">defaultColor</a> () const</td></tr>
<tr class="separator:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a06228b73f8df699a211be872f54d8501 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a06228b73f8df699a211be872f54d8501">defaultEolFill</a> (int style) const</td></tr>
<tr class="separator:a06228b73f8df699a211be872f54d8501 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">defaultFont</a> () const</td></tr>
<tr class="separator:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">defaultPaper</a> () const</td></tr>
<tr class="separator:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7e5ab7f541d913760c32abedbdc72963 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a7e5ab7f541d913760c32abedbdc72963"></a>
virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a7e5ab7f541d913760c32abedbdc72963">defaultPaper</a> (int style) const</td></tr>
<tr class="separator:a7e5ab7f541d913760c32abedbdc72963 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciScintilla.html">QsciScintilla</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a> () const</td></tr>
<tr class="separator:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a> (<a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *<a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>)</td></tr>
<tr class="separator:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a> (const QFont &amp;f)</td></tr>
<tr class="separator:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a1e81186b1f8f8bc2a4901a42cbca568a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>setEditor</b> (<a class="el" href="classQsciScintilla.html">QsciScintilla</a> *<a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>)</td></tr>
<tr class="separator:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td></tr>
<tr class="separator:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a> () const</td></tr>
<tr class="separator:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td></tr>
<tr class="separator:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:a7bfdaea964c9e2c51568f63f379b6108"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#a7bfdaea964c9e2c51568f63f379b6108">readProperties</a> (QSettings &amp;qs, const QString &amp;prefix)</td></tr>
<tr class="separator:a7bfdaea964c9e2c51568f63f379b6108"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac70e03bceba5de91104b85edd00e1a68"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCSS.html#ac70e03bceba5de91104b85edd00e1a68">writeProperties</a> (QSettings &amp;qs, const QString &amp;prefix) const</td></tr>
<tr class="separator:ac70e03bceba5de91104b85edd00e1a68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pro_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a41d4521504d63ee63d43fd7ed0c003ee"></a>
QByteArray&#160;</td><td class="memItemRight" valign="bottom"><b>textAsBytes</b> (const QString &amp;text) const</td></tr>
<tr class="separator:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a5261dd606c209a5c6a494e608a9a111a"></a>
QString&#160;</td><td class="memItemRight" valign="bottom"><b>bytesAsText</b> (const char *bytes, int size) const</td></tr>
<tr class="separator:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header signals_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('signals_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Signals inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a> (bool eolfilled, int style)</td></tr>
<tr class="separator:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a> (const QFont &amp;f, int style)</td></tr>
<tr class="separator:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a> (const char *prop, const char *val)</td></tr>
<tr class="separator:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciLexerCSS.html" title="The QsciLexerCSS class encapsulates the Scintilla CSS lexer.">QsciLexerCSS</a> class encapsulates the Scintilla CSS lexer. </p>
</div><h2 class="groupheader">Member Enumeration Documentation</h2>
<a id="a8f38d12d56564b95f6f1f6b1834ca3e0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8f38d12d56564b95f6f1f6b1834ca3e0">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the meanings of the different styles used by the CSS lexer. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0a53e968c600f53a2bae2710db0ef8db1d"></a>Default&#160;</td><td class="fielddoc"><p>The default. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0a1f6473d0fc2f6bddc22eea9d01ea05ad"></a>Tag&#160;</td><td class="fielddoc"><p>A tag. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0aafd65314dc8f5e87697c987a8a3d1037"></a>ClassSelector&#160;</td><td class="fielddoc"><p>A class selector. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0ad9a4a9e2945073685158769a62e16cda"></a>PseudoClass&#160;</td><td class="fielddoc"><p>A pseudo class. The list of pseudo classes is defined by keyword set 2. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0a6ae93503184d2ba66879daeefaeb4b23"></a>UnknownPseudoClass&#160;</td><td class="fielddoc"><p>An unknown pseudo class. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0ab7ce96396ee8eff4196b5b9d5bf53afc"></a>Operator&#160;</td><td class="fielddoc"><p>An operator. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0a729b64d3f84347da91167d421302a76d"></a>CSS1Property&#160;</td><td class="fielddoc"><p>A CSS1 property. The list of CSS1 properties is defined by keyword set 1. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0af3110ffc53b23fa5dbe17151dc7a5e75"></a>UnknownProperty&#160;</td><td class="fielddoc"><p>An unknown property. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0acdf1ff5843500ebbfe209bff6c0e4370"></a>Value&#160;</td><td class="fielddoc"><p>A value. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0a3496565bdaf261864ed37cd0909687be"></a>Comment&#160;</td><td class="fielddoc"><p>A comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0ad59613421106ae8b7a7594812a4091b5"></a>IDSelector&#160;</td><td class="fielddoc"><p>An ID selector. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0a8b419e8c3650bc498dc90610ccda4f1a"></a>Important&#160;</td><td class="fielddoc"><p>An important value. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0ab66090eb95d05ecb61cb8a4822ab94d8"></a>AtRule&#160;</td><td class="fielddoc"><p>An -rule. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0a34b937f20071fd25a7b7aa57620cb5d5"></a>DoubleQuotedString&#160;</td><td class="fielddoc"><p>A double-quoted string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0a0e475783c35d0707225bfc28edd36d2e"></a>SingleQuotedString&#160;</td><td class="fielddoc"><p>A single-quoted string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0aae08a7b509d7f18df60133b2e204291b"></a>CSS2Property&#160;</td><td class="fielddoc"><p>A CSS2 property. The list of CSS2 properties is defined by keyword set 3. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0ac1a1825aa643819ef0ed1c3a23ce48ee"></a>Attribute&#160;</td><td class="fielddoc"><p>An attribute. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0ade6d6fd36b5e81bcca8ce404d915a16b"></a>CSS3Property&#160;</td><td class="fielddoc"><p>A CSS3 property. The list of CSS3 properties is defined by keyword set 4. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0aaad0b9e8982d95504f3fdeac29c01d68"></a>PseudoElement&#160;</td><td class="fielddoc"><p>A pseudo element. The list of pseudo elements is defined by keyword set 5. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0aea3a8406f16545255905240042c4954b"></a>ExtendedCSSProperty&#160;</td><td class="fielddoc"><p>An extended (browser specific) CSS property. The list of extended CSS properties is defined by keyword set 6. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0aeee973f3f30ab97b97903de6de2dfca5"></a>ExtendedPseudoClass&#160;</td><td class="fielddoc"><p>An extended (browser specific) pseudo class. The list of extended pseudo classes is defined by keyword set 7. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0a220432c41649811f99607624ddb568e6"></a>ExtendedPseudoElement&#160;</td><td class="fielddoc"><p>An extended (browser specific) pseudo element. The list of extended pseudo elements is defined by keyword set 8. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0aa791b33014a708120356e7ea9365bfeb"></a>MediaRule&#160;</td><td class="fielddoc"><p>A media rule. </p>
</td></tr>
<tr><td class="fieldname"><a id="a8f38d12d56564b95f6f1f6b1834ca3e0ab7b8d54656b8a5eb94be142ad027f672"></a>Variable&#160;</td><td class="fielddoc"><p>A variable. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="acbf55e58ad04813101573146ecc43c67"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acbf55e58ad04813101573146ecc43c67">&#9670;&nbsp;</a></span>QsciLexerCSS()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciLexerCSS::QsciLexerCSS </td>
          <td>(</td>
          <td class="paramtype">QObject *&#160;</td>
          <td class="paramname"><em>parent</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Construct a <a class="el" href="classQsciLexerCSS.html" title="The QsciLexerCSS class encapsulates the Scintilla CSS lexer.">QsciLexerCSS</a> with parent <em>parent</em>. <em>parent</em> is typically the <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> instance. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a1fa70c8e86dd88d34508fc652d30f3f7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1fa70c8e86dd88d34508fc652d30f3f7">&#9670;&nbsp;</a></span>lexer()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerCSS::lexer </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the name of the lexer. Some lexers support a number of languages. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ac7c40b97187e23ab85f6d95113f91b39">QsciLexer</a>.</p>

</div>
</div>
<a id="ada48a387b3e1414927bebe2415de75f8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ada48a387b3e1414927bebe2415de75f8">&#9670;&nbsp;</a></span>blockEnd()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerCSS::blockEnd </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of words or characters in a particular style that define the end of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">QsciLexer</a>.</p>

</div>
</div>
<a id="aae249ec529d5f7de5fa238de9208058d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aae249ec529d5f7de5fa238de9208058d">&#9670;&nbsp;</a></span>blockStart()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerCSS::blockStart </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of words or characters in a particular style that define the start of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">QsciLexer</a>.</p>

</div>
</div>
<a id="a41d04b17da9c84a94289e91323fb5206"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a41d04b17da9c84a94289e91323fb5206">&#9670;&nbsp;</a></span>keywords()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerCSS::keywords </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>set</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the set of keywords for the keyword set <em>set</em> recognised by the lexer as a space separated string. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a013b7c1bf9846e231b97827dfd9540b0">QsciLexer</a>.</p>

</div>
</div>
<a id="aca9a53a01d50ef44d9f5ac0fd662bf84"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aca9a53a01d50ef44d9f5ac0fd662bf84">&#9670;&nbsp;</a></span>description()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QString QsciLexerCSS::description </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the descriptive name for style number <em>style</em>. If the style is invalid for this language then an empty QString is returned. This is intended to be used in user preference dialogs. </p>

<p>Implements <a class="el" href="classQsciLexer.html#add9c20adb43bc38d1a0ca3083ac3e6fa">QsciLexer</a>.</p>

</div>
</div>
<a id="a78f4690fa92e02c8511074a334c06096"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a78f4690fa92e02c8511074a334c06096">&#9670;&nbsp;</a></span>refreshProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerCSS::refreshProperties </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Causes all properties to be refreshed by emitting the <a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged()</a> signal as required. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ae508c3ab4ce1f338dfff3ddf5ee7e34c">QsciLexer</a>.</p>

</div>
</div>
<a id="ae32591385112bb3d33de8c1c40888190"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae32591385112bb3d33de8c1c40888190">&#9670;&nbsp;</a></span>foldComments()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerCSS::foldComments </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if multi-line comment blocks can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCSS.html#a5f77be4cb83422d47220c5b38d9f0a99">setFoldComments()</a> </dd></dl>

</div>
</div>
<a id="aecbd103b6dff15873e60cdf48e634a4a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aecbd103b6dff15873e60cdf48e634a4a">&#9670;&nbsp;</a></span>foldCompact()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerCSS::foldCompact </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if trailing blank lines are included in a fold block.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCSS.html#aaf9762aeea19ed1c8d6766a9e6a52cd3">setFoldCompact()</a> </dd></dl>

</div>
</div>
<a id="a9e61fa490e6e6c1480f3de5187ffed02"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9e61fa490e6e6c1480f3de5187ffed02">&#9670;&nbsp;</a></span>setHSSLanguage()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerCSS::setHSSLanguage </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>enabled</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>enabled</em> is true then support for HSS is enabled. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCSS.html#a95a8b4cc2d6f8437c8feada8f518daff">HSSLanguage()</a> </dd></dl>

</div>
</div>
<a id="a95a8b4cc2d6f8437c8feada8f518daff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a95a8b4cc2d6f8437c8feada8f518daff">&#9670;&nbsp;</a></span>HSSLanguage()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerCSS::HSSLanguage </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if support for HSS is enabled.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCSS.html#a9e61fa490e6e6c1480f3de5187ffed02">setHSSLanguage()</a> </dd></dl>

</div>
</div>
<a id="a388e532d847652dbf18207593e236e5e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a388e532d847652dbf18207593e236e5e">&#9670;&nbsp;</a></span>setLessLanguage()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerCSS::setLessLanguage </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>enabled</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>enabled</em> is true then support for Less CSS is enabled. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCSS.html#a26df830be43cb0ca067b5e0ad037171a">LessLanguage()</a> </dd></dl>

</div>
</div>
<a id="a26df830be43cb0ca067b5e0ad037171a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a26df830be43cb0ca067b5e0ad037171a">&#9670;&nbsp;</a></span>LessLanguage()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerCSS::LessLanguage </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if support for Less CSS is enabled.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCSS.html#a388e532d847652dbf18207593e236e5e">setLessLanguage()</a> </dd></dl>

</div>
</div>
<a id="a2a2195f681df3657fbadf72c55003863"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2a2195f681df3657fbadf72c55003863">&#9670;&nbsp;</a></span>setSCSSLanguage()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerCSS::setSCSSLanguage </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>enabled</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>enabled</em> is true then support for Sassy CSS is enabled. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCSS.html#ae8630fee6378af65bbd772b8f20fe4c9">SCSSLanguage()</a> </dd></dl>

</div>
</div>
<a id="ae8630fee6378af65bbd772b8f20fe4c9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae8630fee6378af65bbd772b8f20fe4c9">&#9670;&nbsp;</a></span>SCSSLanguage()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerCSS::SCSSLanguage </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if support for Sassy CSS is enabled.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCSS.html#a2a2195f681df3657fbadf72c55003863">setSCSSLanguage()</a> </dd></dl>

</div>
</div>
<a id="a5f77be4cb83422d47220c5b38d9f0a99"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5f77be4cb83422d47220c5b38d9f0a99">&#9670;&nbsp;</a></span>setFoldComments</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerCSS::setFoldComments </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then multi-line comment blocks can be folded. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCSS.html#ae32591385112bb3d33de8c1c40888190">foldComments()</a> </dd></dl>

</div>
</div>
<a id="aaf9762aeea19ed1c8d6766a9e6a52cd3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaf9762aeea19ed1c8d6766a9e6a52cd3">&#9670;&nbsp;</a></span>setFoldCompact</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerCSS::setFoldCompact </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then trailing blank lines are included in a fold block. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCSS.html#aecbd103b6dff15873e60cdf48e634a4a">foldCompact()</a> </dd></dl>

</div>
</div>
<a id="a7bfdaea964c9e2c51568f63f379b6108"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7bfdaea964c9e2c51568f63f379b6108">&#9670;&nbsp;</a></span>readProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerCSS::readProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are read from the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ad472b16506a4cbc19634f07aa90f1ea6">QsciLexer</a>.</p>

</div>
</div>
<a id="ac70e03bceba5de91104b85edd00e1a68"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac70e03bceba5de91104b85edd00e1a68">&#9670;&nbsp;</a></span>writeProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerCSS::writeProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are written to the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#abccc4e010b724df1a7b5c5f3bce29501">QsciLexer</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
