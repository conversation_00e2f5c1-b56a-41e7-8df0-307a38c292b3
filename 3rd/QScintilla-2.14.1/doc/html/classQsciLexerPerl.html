<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciLexerPerl Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-slots">Public Slots</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="classQsciLexerPerl-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciLexerPerl Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscilexerperl.h&gt;</code></p>

<p>Inherits <a class="el" href="classQsciLexer.html">QsciLexer</a>.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a69516e9f701fceec0231cc3050b41da9"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9af5df69c0ed6d8c42bc39e717889aea78">Default</a> = 0, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a99a1c9873cd83852da55023a2420f5a8">Error</a> = 1, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9af2c29ccb75997807734f024b49998b6a">Comment</a> = 2, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a64e30800729f8ef4d273130a90b62704">POD</a> = 3, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a65ab3c30be465884145bee390d038a8f">Number</a> = 4, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9ae431c41ecbd64bf0f773f25b68a7973a">Keyword</a> = 5, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9aa14ec9d32c1fa5bbf171a3fb45473bcf">DoubleQuotedString</a> = 6, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a2cf9c05452a47bcde418b4cf691bbcd1">SingleQuotedString</a> = 7, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a2c43ed725d5edb523abb214f6867a5f4">Operator</a> = 10, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a65e52532e4624b84e6f3cd89b37a48b8">Identifier</a> = 11, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a8fe3c7834c771123699097248a2a97fa">Scalar</a> = 12, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9aebe8e5c6b96783494e61c8cd03975570">Array</a> = 13, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9aaa260a4964100f84e24f3797150379ac">Hash</a> = 14, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9adfaa14e55f48f7774f991a73f8a7fadc">SymbolTable</a> = 15, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a1ba94b26ebb6f719bfec1e2fc5c180a7">Regex</a> = 17, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a3edcaf1beac4277212faf8f30c8271b9">Substitution</a> = 18, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a53d80aaaa00ca3d47433a05bc93297c8">Backticks</a> = 20, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a8c119f4794e3dab23aa2a4f739a1e81f">DataSection</a> = 21, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a1735d83cde8be27eda10acb6f7e2ed98">HereDocumentDelimiter</a> = 22, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a73e0d55813d2d21a060a9e1e59360506">SingleQuotedHereDocument</a> = 23, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a0a82588ab552f48b9caeb05db6d9428f">DoubleQuotedHereDocument</a> = 24, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9aa75c7ba5ad98b870f0e303c94f0b9375">BacktickHereDocument</a> = 25, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a4eb6e937e8713d00368651dbeada3b74">QuotedStringQ</a> = 26, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a93f9be9adc1bcc1289155cca445eb860">QuotedStringQQ</a> = 27, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a500ce87c3501f0e8d86db52eefdc7b8e">QuotedStringQX</a> = 28, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a7931aea4826aceb60ba2aab3fd7b5884">QuotedStringQR</a> = 29, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9ad9cf54861772d7a5cf696c4bb4be04cd">QuotedStringQW</a> = 30, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9aecb9bf65704610bce3bf8dbfdbce40a4">PODVerbatim</a> = 31, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9aa77b69ca726faae33472a1ff018d54af">SubroutinePrototype</a> = 40, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9af1b0ae17967c8b101eea3d9e4cc173cd">FormatIdentifier</a> = 41, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9adbc0e3fbe443a92d03f444a1f66b1d5c">FormatBody</a> = 42, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a87d5e90f82615a99e0af4ccc4875dc65">DoubleQuotedStringVar</a> = 43, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a84d882c68a32f9eefcfc6ad3ff953c6e">Translation</a> = 44, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a52496f8867a7285b205ef55fb014d84e">RegexVar</a> = 54, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a7c0194dff17baffd0e9592b581944fda">SubstitutionVar</a> = 55, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a1042900000e9c99d0a52724d5c838c94">BackticksVar</a> = 57, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9ae9c623b599443071c8bb547279c7dd64">DoubleQuotedHereDocumentVar</a> = 61, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a7fb5b3d78cf402664941ceee7a17d758">BacktickHereDocumentVar</a> = 62, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a3a4919b9d5dfefc405bd70d8f6ce780a">QuotedStringQQVar</a> = 64, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a4bd8b77cc8ca06d77281c7c146b7f9be">QuotedStringQXVar</a> = 65, 
<a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a5dd29ed0e2532a609155a9f2279cda6b">QuotedStringQRVar</a> = 66
<br />
 }</td></tr>
<tr class="separator:a69516e9f701fceec0231cc3050b41da9"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-slots"></a>
Public Slots</h2></td></tr>
<tr class="memitem:a85aa8e72d81818a7edea1867362db16a"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a85aa8e72d81818a7edea1867362db16a">setFoldComments</a> (bool fold)</td></tr>
<tr class="separator:a85aa8e72d81818a7edea1867362db16a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a269b1c3c788ae863939fd8b1749a5abf"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a269b1c3c788ae863939fd8b1749a5abf">setFoldCompact</a> (bool fold)</td></tr>
<tr class="separator:a269b1c3c788ae863939fd8b1749a5abf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_slots_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_slots_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Slots inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a> (int autoindentstyle)</td></tr>
<tr class="separator:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a> (bool eoffill, int style=-1)</td></tr>
<tr class="separator:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a> (const QFont &amp;f, int style=-1)</td></tr>
<tr class="separator:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a36359d3e1cb6037b561f95fccf16881e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a36359d3e1cb6037b561f95fccf16881e">QsciLexerPerl</a> (QObject *parent=0)</td></tr>
<tr class="separator:a36359d3e1cb6037b561f95fccf16881e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6f87282ec40dbc5e752dc0bc0aec87a0"><td class="memItemLeft" align="right" valign="top"><a id="a6f87282ec40dbc5e752dc0bc0aec87a0"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a6f87282ec40dbc5e752dc0bc0aec87a0">~QsciLexerPerl</a> ()</td></tr>
<tr class="separator:a6f87282ec40dbc5e752dc0bc0aec87a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16fb82e08452dc260bdda610817c79ea"><td class="memItemLeft" align="right" valign="top"><a id="a16fb82e08452dc260bdda610817c79ea"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a16fb82e08452dc260bdda610817c79ea">language</a> () const</td></tr>
<tr class="separator:a16fb82e08452dc260bdda610817c79ea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aae9e42584c6466a8b859d56218eaf28c"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#aae9e42584c6466a8b859d56218eaf28c">lexer</a> () const</td></tr>
<tr class="separator:aae9e42584c6466a8b859d56218eaf28c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e4df63d7d5714b1bdb71c1975f7f99c"><td class="memItemLeft" align="right" valign="top">QStringList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a0e4df63d7d5714b1bdb71c1975f7f99c">autoCompletionWordSeparators</a> () const</td></tr>
<tr class="separator:a0e4df63d7d5714b1bdb71c1975f7f99c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42133f1b4127c78674f89e3209236a18"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a42133f1b4127c78674f89e3209236a18">blockEnd</a> (int *style=0) const</td></tr>
<tr class="separator:a42133f1b4127c78674f89e3209236a18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae33c3f0e337cfe173c61ea86c5cd3591"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#ae33c3f0e337cfe173c61ea86c5cd3591">blockStart</a> (int *style=0) const</td></tr>
<tr class="separator:ae33c3f0e337cfe173c61ea86c5cd3591"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3e90db838034f7404e65b2e284403604"><td class="memItemLeft" align="right" valign="top"><a id="a3e90db838034f7404e65b2e284403604"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a3e90db838034f7404e65b2e284403604">braceStyle</a> () const</td></tr>
<tr class="separator:a3e90db838034f7404e65b2e284403604"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5ffd80ff37350acb6fe03f798f34a912"><td class="memItemLeft" align="right" valign="top"><a id="a5ffd80ff37350acb6fe03f798f34a912"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a5ffd80ff37350acb6fe03f798f34a912">wordCharacters</a> () const</td></tr>
<tr class="separator:a5ffd80ff37350acb6fe03f798f34a912"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3ec3d302e4ad33ca360d3edbe14ac561"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a3ec3d302e4ad33ca360d3edbe14ac561">defaultColor</a> (int style) const</td></tr>
<tr class="separator:a3ec3d302e4ad33ca360d3edbe14ac561"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a242188212df611073f78d1eff326f5d5"><td class="memItemLeft" align="right" valign="top"><a id="a242188212df611073f78d1eff326f5d5"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a242188212df611073f78d1eff326f5d5">defaultEolFill</a> (int style) const</td></tr>
<tr class="separator:a242188212df611073f78d1eff326f5d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1deaafed565aeae806e4ea6083baa186"><td class="memItemLeft" align="right" valign="top"><a id="a1deaafed565aeae806e4ea6083baa186"></a>
QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a1deaafed565aeae806e4ea6083baa186">defaultFont</a> (int style) const</td></tr>
<tr class="separator:a1deaafed565aeae806e4ea6083baa186"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa54795b596b6bc9f3664865b9d76484"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#afa54795b596b6bc9f3664865b9d76484">defaultPaper</a> (int style) const</td></tr>
<tr class="separator:afa54795b596b6bc9f3664865b9d76484"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a57958c564d4d3127e7ee6148d232bd4b"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a57958c564d4d3127e7ee6148d232bd4b">keywords</a> (int set) const</td></tr>
<tr class="separator:a57958c564d4d3127e7ee6148d232bd4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a386c817d87735b2dd347735cb264d548"><td class="memItemLeft" align="right" valign="top">QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a386c817d87735b2dd347735cb264d548">description</a> (int style) const</td></tr>
<tr class="separator:a386c817d87735b2dd347735cb264d548"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac9868e2d0efbf3602a22d8bdac12a119"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#ac9868e2d0efbf3602a22d8bdac12a119">refreshProperties</a> ()</td></tr>
<tr class="separator:ac9868e2d0efbf3602a22d8bdac12a119"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a14705cac9643949facd57641e0892fb0"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a14705cac9643949facd57641e0892fb0">setFoldAtElse</a> (bool fold)</td></tr>
<tr class="separator:a14705cac9643949facd57641e0892fb0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a696abf6da5415e772e5ade8752eac3b2"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a696abf6da5415e772e5ade8752eac3b2">foldAtElse</a> () const</td></tr>
<tr class="separator:a696abf6da5415e772e5ade8752eac3b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae9881257bbcc887cdbe21e74bbb8ea65"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#ae9881257bbcc887cdbe21e74bbb8ea65">foldComments</a> () const</td></tr>
<tr class="separator:ae9881257bbcc887cdbe21e74bbb8ea65"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0c3e05e1bbdc4614fc7e76e508178592"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a0c3e05e1bbdc4614fc7e76e508178592">foldCompact</a> () const</td></tr>
<tr class="separator:a0c3e05e1bbdc4614fc7e76e508178592"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5e2cdbcaa57b02f18d65aea89d2faa54"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a5e2cdbcaa57b02f18d65aea89d2faa54">setFoldPackages</a> (bool fold)</td></tr>
<tr class="separator:a5e2cdbcaa57b02f18d65aea89d2faa54"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5ea4eb1e65b2cee23a09f143074790b4"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a5ea4eb1e65b2cee23a09f143074790b4">foldPackages</a> () const</td></tr>
<tr class="separator:a5ea4eb1e65b2cee23a09f143074790b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af0ee6abab37e283e68f527c597c50877"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#af0ee6abab37e283e68f527c597c50877">setFoldPODBlocks</a> (bool fold)</td></tr>
<tr class="separator:af0ee6abab37e283e68f527c597c50877"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab58e1e9d037d280fc74792ace83936d4"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#ab58e1e9d037d280fc74792ace83936d4">foldPODBlocks</a> () const</td></tr>
<tr class="separator:ab58e1e9d037d280fc74792ace83936d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a> (QObject *parent=0)</td></tr>
<tr class="separator:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="af6cc5bb9d9421d806e9941d018030068"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a> ()</td></tr>
<tr class="separator:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a> () const</td></tr>
<tr class="separator:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a> () const</td></tr>
<tr class="separator:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a6504a6fff35af16fbfd97889048db2a5"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a> () const</td></tr>
<tr class="separator:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a> ()</td></tr>
<tr class="separator:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a> () const</td></tr>
<tr class="separator:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf12117a142b6f68479ea425d80a4196 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a> (int *style=0) const</td></tr>
<tr class="separator:abf12117a142b6f68479ea425d80a4196 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="afccca7eb1aed463f89ac442d99135839"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a> () const</td></tr>
<tr class="separator:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a> (int style) const</td></tr>
<tr class="separator:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a> (int style) const</td></tr>
<tr class="separator:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a> (int style) const</td></tr>
<tr class="separator:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="aff4735542e937c5e35ecb2eb82e8f875"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a> () const</td></tr>
<tr class="separator:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a> () const</td></tr>
<tr class="separator:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a> (int style) const</td></tr>
<tr class="separator:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">defaultColor</a> () const</td></tr>
<tr class="separator:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">defaultFont</a> () const</td></tr>
<tr class="separator:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">defaultPaper</a> () const</td></tr>
<tr class="separator:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciScintilla.html">QsciScintilla</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a> () const</td></tr>
<tr class="separator:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a> (<a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *<a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>)</td></tr>
<tr class="separator:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a> (const QFont &amp;f)</td></tr>
<tr class="separator:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a1e81186b1f8f8bc2a4901a42cbca568a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>setEditor</b> (<a class="el" href="classQsciScintilla.html">QsciScintilla</a> *<a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>)</td></tr>
<tr class="separator:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td></tr>
<tr class="separator:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a> () const</td></tr>
<tr class="separator:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td></tr>
<tr class="separator:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:a47884fcfd8d2b0ab7b8d277cb0325c17"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a47884fcfd8d2b0ab7b8d277cb0325c17">readProperties</a> (QSettings &amp;qs, const QString &amp;prefix)</td></tr>
<tr class="separator:a47884fcfd8d2b0ab7b8d277cb0325c17"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16841e0262d8200d5ed3a85099d45b37"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPerl.html#a16841e0262d8200d5ed3a85099d45b37">writeProperties</a> (QSettings &amp;qs, const QString &amp;prefix) const</td></tr>
<tr class="separator:a16841e0262d8200d5ed3a85099d45b37"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pro_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a41d4521504d63ee63d43fd7ed0c003ee"></a>
QByteArray&#160;</td><td class="memItemRight" valign="bottom"><b>textAsBytes</b> (const QString &amp;text) const</td></tr>
<tr class="separator:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a5261dd606c209a5c6a494e608a9a111a"></a>
QString&#160;</td><td class="memItemRight" valign="bottom"><b>bytesAsText</b> (const char *bytes, int size) const</td></tr>
<tr class="separator:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header signals_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('signals_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Signals inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a> (bool eolfilled, int style)</td></tr>
<tr class="separator:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a> (const QFont &amp;f, int style)</td></tr>
<tr class="separator:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a> (const char *prop, const char *val)</td></tr>
<tr class="separator:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciLexerPerl.html" title="The QsciLexerPerl class encapsulates the Scintilla Perl lexer.">QsciLexerPerl</a> class encapsulates the Scintilla Perl lexer. </p>
</div><h2 class="groupheader">Member Enumeration Documentation</h2>
<a id="a69516e9f701fceec0231cc3050b41da9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a69516e9f701fceec0231cc3050b41da9">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the meanings of the different styles used by the Perl lexer. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9af5df69c0ed6d8c42bc39e717889aea78"></a>Default&#160;</td><td class="fielddoc"><p>The default. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a99a1c9873cd83852da55023a2420f5a8"></a>Error&#160;</td><td class="fielddoc"><p>An error. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9af2c29ccb75997807734f024b49998b6a"></a>Comment&#160;</td><td class="fielddoc"><p>A comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a64e30800729f8ef4d273130a90b62704"></a>POD&#160;</td><td class="fielddoc"><p>A POD. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a65ab3c30be465884145bee390d038a8f"></a>Number&#160;</td><td class="fielddoc"><p>A number. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9ae431c41ecbd64bf0f773f25b68a7973a"></a>Keyword&#160;</td><td class="fielddoc"><p>A keyword. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9aa14ec9d32c1fa5bbf171a3fb45473bcf"></a>DoubleQuotedString&#160;</td><td class="fielddoc"><p>A double-quoted string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a2cf9c05452a47bcde418b4cf691bbcd1"></a>SingleQuotedString&#160;</td><td class="fielddoc"><p>A single-quoted string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a2c43ed725d5edb523abb214f6867a5f4"></a>Operator&#160;</td><td class="fielddoc"><p>An operator. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a65e52532e4624b84e6f3cd89b37a48b8"></a>Identifier&#160;</td><td class="fielddoc"><p>An identifier. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a8fe3c7834c771123699097248a2a97fa"></a>Scalar&#160;</td><td class="fielddoc"><p>A scalar. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9aebe8e5c6b96783494e61c8cd03975570"></a>Array&#160;</td><td class="fielddoc"><p>An array. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9aaa260a4964100f84e24f3797150379ac"></a>Hash&#160;</td><td class="fielddoc"><p>A hash. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9adfaa14e55f48f7774f991a73f8a7fadc"></a>SymbolTable&#160;</td><td class="fielddoc"><p>A symbol table. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a1ba94b26ebb6f719bfec1e2fc5c180a7"></a>Regex&#160;</td><td class="fielddoc"><p>A regular expression. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a3edcaf1beac4277212faf8f30c8271b9"></a>Substitution&#160;</td><td class="fielddoc"><p>A substitution. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a53d80aaaa00ca3d47433a05bc93297c8"></a>Backticks&#160;</td><td class="fielddoc"><p>Backticks. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a8c119f4794e3dab23aa2a4f739a1e81f"></a>DataSection&#160;</td><td class="fielddoc"><p>A data section. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a1735d83cde8be27eda10acb6f7e2ed98"></a>HereDocumentDelimiter&#160;</td><td class="fielddoc"><p>A here document delimiter. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a73e0d55813d2d21a060a9e1e59360506"></a>SingleQuotedHereDocument&#160;</td><td class="fielddoc"><p>A single quoted here document. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a0a82588ab552f48b9caeb05db6d9428f"></a>DoubleQuotedHereDocument&#160;</td><td class="fielddoc"><p>A double quoted here document. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9aa75c7ba5ad98b870f0e303c94f0b9375"></a>BacktickHereDocument&#160;</td><td class="fielddoc"><p>A backtick here document. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a4eb6e937e8713d00368651dbeada3b74"></a>QuotedStringQ&#160;</td><td class="fielddoc"><p>A quoted string (q). </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a93f9be9adc1bcc1289155cca445eb860"></a>QuotedStringQQ&#160;</td><td class="fielddoc"><p>A quoted string (qq). </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a500ce87c3501f0e8d86db52eefdc7b8e"></a>QuotedStringQX&#160;</td><td class="fielddoc"><p>A quoted string (qx). </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a7931aea4826aceb60ba2aab3fd7b5884"></a>QuotedStringQR&#160;</td><td class="fielddoc"><p>A quoted string (qr). </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9ad9cf54861772d7a5cf696c4bb4be04cd"></a>QuotedStringQW&#160;</td><td class="fielddoc"><p>A quoted string (qw). </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9aecb9bf65704610bce3bf8dbfdbce40a4"></a>PODVerbatim&#160;</td><td class="fielddoc"><p>A verbatim POD. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9aa77b69ca726faae33472a1ff018d54af"></a>SubroutinePrototype&#160;</td><td class="fielddoc"><p>A Subroutine prototype. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9af1b0ae17967c8b101eea3d9e4cc173cd"></a>FormatIdentifier&#160;</td><td class="fielddoc"><p>A format identifier. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9adbc0e3fbe443a92d03f444a1f66b1d5c"></a>FormatBody&#160;</td><td class="fielddoc"><p>A format body. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a87d5e90f82615a99e0af4ccc4875dc65"></a>DoubleQuotedStringVar&#160;</td><td class="fielddoc"><p>A double-quoted string (interpolated variable). </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a84d882c68a32f9eefcfc6ad3ff953c6e"></a>Translation&#160;</td><td class="fielddoc"><p>A translation. </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a52496f8867a7285b205ef55fb014d84e"></a>RegexVar&#160;</td><td class="fielddoc"><p>A regular expression (interpolated variable). </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a7c0194dff17baffd0e9592b581944fda"></a>SubstitutionVar&#160;</td><td class="fielddoc"><p>A substitution (interpolated variable). </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a1042900000e9c99d0a52724d5c838c94"></a>BackticksVar&#160;</td><td class="fielddoc"><p>Backticks (interpolated variable). </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9ae9c623b599443071c8bb547279c7dd64"></a>DoubleQuotedHereDocumentVar&#160;</td><td class="fielddoc"><p>A double quoted here document (interpolated variable). </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a7fb5b3d78cf402664941ceee7a17d758"></a>BacktickHereDocumentVar&#160;</td><td class="fielddoc"><p>A backtick here document (interpolated variable). </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a3a4919b9d5dfefc405bd70d8f6ce780a"></a>QuotedStringQQVar&#160;</td><td class="fielddoc"><p>A quoted string (qq, interpolated variable). </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a4bd8b77cc8ca06d77281c7c146b7f9be"></a>QuotedStringQXVar&#160;</td><td class="fielddoc"><p>A quoted string (qx, interpolated variable). </p>
</td></tr>
<tr><td class="fieldname"><a id="a69516e9f701fceec0231cc3050b41da9a5dd29ed0e2532a609155a9f2279cda6b"></a>QuotedStringQRVar&#160;</td><td class="fielddoc"><p>A quoted string (qr, interpolated variable). </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a36359d3e1cb6037b561f95fccf16881e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a36359d3e1cb6037b561f95fccf16881e">&#9670;&nbsp;</a></span>QsciLexerPerl()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciLexerPerl::QsciLexerPerl </td>
          <td>(</td>
          <td class="paramtype">QObject *&#160;</td>
          <td class="paramname"><em>parent</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Construct a <a class="el" href="classQsciLexerPerl.html" title="The QsciLexerPerl class encapsulates the Scintilla Perl lexer.">QsciLexerPerl</a> with parent <em>parent</em>. <em>parent</em> is typically the <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> instance. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="aae9e42584c6466a8b859d56218eaf28c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aae9e42584c6466a8b859d56218eaf28c">&#9670;&nbsp;</a></span>lexer()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerPerl::lexer </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the name of the lexer. Some lexers support a number of languages. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ac7c40b97187e23ab85f6d95113f91b39">QsciLexer</a>.</p>

</div>
</div>
<a id="a0e4df63d7d5714b1bdb71c1975f7f99c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0e4df63d7d5714b1bdb71c1975f7f99c">&#9670;&nbsp;</a></span>autoCompletionWordSeparators()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QStringList QsciLexerPerl::autoCompletionWordSeparators </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the list of character sequences that can separate auto-completion words. The first in the list is assumed to be the sequence used to separate words in the lexer's API files. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">QsciLexer</a>.</p>

</div>
</div>
<a id="a42133f1b4127c78674f89e3209236a18"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a42133f1b4127c78674f89e3209236a18">&#9670;&nbsp;</a></span>blockEnd()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerPerl::blockEnd </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of words or characters in a particular style that define the end of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">QsciLexer</a>.</p>

</div>
</div>
<a id="ae33c3f0e337cfe173c61ea86c5cd3591"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae33c3f0e337cfe173c61ea86c5cd3591">&#9670;&nbsp;</a></span>blockStart()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerPerl::blockStart </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of words or characters in a particular style that define the start of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">QsciLexer</a>.</p>

</div>
</div>
<a id="a3ec3d302e4ad33ca360d3edbe14ac561"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3ec3d302e4ad33ca360d3edbe14ac561">&#9670;&nbsp;</a></span>defaultColor()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerPerl::defaultColor </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the foreground colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPerl.html#afa54795b596b6bc9f3664865b9d76484">defaultPaper()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#af7508f1b816a2c9446d36141edc9b5ce">QsciLexer</a>.</p>

</div>
</div>
<a id="afa54795b596b6bc9f3664865b9d76484"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afa54795b596b6bc9f3664865b9d76484">&#9670;&nbsp;</a></span>defaultPaper()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerPerl::defaultPaper </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the background colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPerl.html#a3ec3d302e4ad33ca360d3edbe14ac561">defaultColor()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a7e5ab7f541d913760c32abedbdc72963">QsciLexer</a>.</p>

</div>
</div>
<a id="a57958c564d4d3127e7ee6148d232bd4b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a57958c564d4d3127e7ee6148d232bd4b">&#9670;&nbsp;</a></span>keywords()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerPerl::keywords </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>set</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the set of keywords for the keyword set <em>set</em> recognised by the lexer as a space separated string. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a013b7c1bf9846e231b97827dfd9540b0">QsciLexer</a>.</p>

</div>
</div>
<a id="a386c817d87735b2dd347735cb264d548"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a386c817d87735b2dd347735cb264d548">&#9670;&nbsp;</a></span>description()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QString QsciLexerPerl::description </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the descriptive name for style number <em>style</em>. If the style is invalid for this language then an empty QString is returned. This is intended to be used in user preference dialogs. </p>

<p>Implements <a class="el" href="classQsciLexer.html#add9c20adb43bc38d1a0ca3083ac3e6fa">QsciLexer</a>.</p>

</div>
</div>
<a id="ac9868e2d0efbf3602a22d8bdac12a119"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac9868e2d0efbf3602a22d8bdac12a119">&#9670;&nbsp;</a></span>refreshProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerPerl::refreshProperties </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Causes all properties to be refreshed by emitting the <a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged()</a> signal as required. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ae508c3ab4ce1f338dfff3ddf5ee7e34c">QsciLexer</a>.</p>

</div>
</div>
<a id="a14705cac9643949facd57641e0892fb0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a14705cac9643949facd57641e0892fb0">&#9670;&nbsp;</a></span>setFoldAtElse()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerPerl::setFoldAtElse </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then "} else {" lines can be folded. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPerl.html#a696abf6da5415e772e5ade8752eac3b2">foldAtElse()</a> </dd></dl>

</div>
</div>
<a id="a696abf6da5415e772e5ade8752eac3b2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a696abf6da5415e772e5ade8752eac3b2">&#9670;&nbsp;</a></span>foldAtElse()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPerl::foldAtElse </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if "} else {" lines can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPerl.html#a14705cac9643949facd57641e0892fb0">setFoldAtElse()</a> </dd></dl>

</div>
</div>
<a id="ae9881257bbcc887cdbe21e74bbb8ea65"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae9881257bbcc887cdbe21e74bbb8ea65">&#9670;&nbsp;</a></span>foldComments()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPerl::foldComments </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if multi-line comment blocks can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPerl.html#a85aa8e72d81818a7edea1867362db16a">setFoldComments()</a> </dd></dl>

</div>
</div>
<a id="a0c3e05e1bbdc4614fc7e76e508178592"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0c3e05e1bbdc4614fc7e76e508178592">&#9670;&nbsp;</a></span>foldCompact()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPerl::foldCompact </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if trailing blank lines are included in a fold block.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPerl.html#a269b1c3c788ae863939fd8b1749a5abf">setFoldCompact()</a> </dd></dl>

</div>
</div>
<a id="a5e2cdbcaa57b02f18d65aea89d2faa54"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5e2cdbcaa57b02f18d65aea89d2faa54">&#9670;&nbsp;</a></span>setFoldPackages()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerPerl::setFoldPackages </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then packages can be folded. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPerl.html#a5ea4eb1e65b2cee23a09f143074790b4">foldPackages()</a> </dd></dl>

</div>
</div>
<a id="a5ea4eb1e65b2cee23a09f143074790b4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5ea4eb1e65b2cee23a09f143074790b4">&#9670;&nbsp;</a></span>foldPackages()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPerl::foldPackages </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if packages can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPerl.html#a5e2cdbcaa57b02f18d65aea89d2faa54">setFoldPackages()</a> </dd></dl>

</div>
</div>
<a id="af0ee6abab37e283e68f527c597c50877"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af0ee6abab37e283e68f527c597c50877">&#9670;&nbsp;</a></span>setFoldPODBlocks()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerPerl::setFoldPODBlocks </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then POD blocks can be folded. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPerl.html#ab58e1e9d037d280fc74792ace83936d4">foldPODBlocks()</a> </dd></dl>

</div>
</div>
<a id="ab58e1e9d037d280fc74792ace83936d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab58e1e9d037d280fc74792ace83936d4">&#9670;&nbsp;</a></span>foldPODBlocks()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPerl::foldPODBlocks </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if POD blocks can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPerl.html#af0ee6abab37e283e68f527c597c50877">setFoldPODBlocks()</a> </dd></dl>

</div>
</div>
<a id="a85aa8e72d81818a7edea1867362db16a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a85aa8e72d81818a7edea1867362db16a">&#9670;&nbsp;</a></span>setFoldComments</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerPerl::setFoldComments </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then multi-line comment blocks can be folded. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPerl.html#ae9881257bbcc887cdbe21e74bbb8ea65">foldComments()</a> </dd></dl>

</div>
</div>
<a id="a269b1c3c788ae863939fd8b1749a5abf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a269b1c3c788ae863939fd8b1749a5abf">&#9670;&nbsp;</a></span>setFoldCompact</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerPerl::setFoldCompact </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then trailing blank lines are included in a fold block. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPerl.html#a0c3e05e1bbdc4614fc7e76e508178592">foldCompact()</a> </dd></dl>

</div>
</div>
<a id="a47884fcfd8d2b0ab7b8d277cb0325c17"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a47884fcfd8d2b0ab7b8d277cb0325c17">&#9670;&nbsp;</a></span>readProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPerl::readProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are read from the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ad472b16506a4cbc19634f07aa90f1ea6">QsciLexer</a>.</p>

</div>
</div>
<a id="a16841e0262d8200d5ed3a85099d45b37"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a16841e0262d8200d5ed3a85099d45b37">&#9670;&nbsp;</a></span>writeProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPerl::writeProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are written to the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#abccc4e010b724df1a7b5c5f3bce29501">QsciLexer</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
