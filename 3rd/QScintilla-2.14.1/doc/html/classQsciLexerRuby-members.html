<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciLexerRuby Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">autoCompletionWordSeparators</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda40dc4d5fdccc6fa1de6189a4e07d4345">Backticks</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#aabf79a666eb40a912dfb7136d79f80e6">blockEnd</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a7ecc2269f4b7a4956b7209082032245d">blockStart</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a47eb0ab494fe54b5518b4c8bdcd2968e">blockStartKeyword</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#ae7a6d23e6e8748210198b4fee3932144">braceStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bytesAsText</b>(const char *bytes, int size) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda73fc696ddb8d12d4a0568b85a690a180">ClassName</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda8d3b74c47f0454a05b12f65ca98f13c1">ClassVariable</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda4d3bcdc4618dd38c999f30ec64a2be94">Comment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda36e45c5ce435eacd1f9f140adf265a78">DataSection</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdaaa0287c846694faece595f55d26fca1c">Default</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a55b4fb34deedc4131e4f85fc4f7e01bc">defaultColor</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">QsciLexer::defaultColor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#aa6e85b803ff580acecda16deaa70c758">defaultEolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#ae6a8edb1b3ae833cd5c5a2b56cf1ec3e">defaultFont</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">QsciLexer::defaultFont</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#af45a578123a772bdb293d326c29218dc">defaultPaper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">QsciLexer::defaultPaper</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda5a7463b193f6ad19397c1feead6b83b6">DemotedKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#aff36eb2ba5df9c4998eb9c8311f14de5">description</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda29b34bc0b6d300d9eec4e7d4b8352ca6">DoubleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a>(bool eolfilled, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda96bb579511d0e7c816783f3740de5aec">Error</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a846ebeb36f0847cee3599860f787bcde">foldComments</a>() const</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#acbaa96d72ad071768acc25d7d56b6324">foldCompact</a>() const</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a>(const QFont &amp;f, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdafc1f84fb3175d37e1a12822cdea2aabf">FunctionMethodName</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdac07c560208a60c08d1fea27a862ce60a">Global</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdae3c3ed5000ff843e47b7215bd175c0b6">HereDocument</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda1cf86e15ea041fc9e5d0700a56c1a220">HereDocumentDelimiter</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda1a052fb80029ed8c2990a996b311081d">Identifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdac62e9eb3fad7c9f5ffd551e37116b2bb">InstanceVariable</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda64027d287f4c03b3c5c790277e6bbbc4">Keyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#abd6f026e6cb154c64c581f6e5f7f2fed">keywords</a>(int set) const</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a700754468352f673157d08d4ff222e79">language</a>() const</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a14f1638b2f668fb7d98791cda719f8a0">lexer</a>() const</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda2e66ca91a2f6aa3f873e017b9d794710">ModuleName</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda9f9666ed92175c304b5733a425562a26">Number</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda620214bd8d8ed0e2839c4cc0c5143349">Operator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda6c5d3e3f93635ec89652a2ef93763f8f">PercentStringQ</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda4ddf93050bae629ab5c0f786e92809f6">PercentStringq</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda02679f74d964ce8130c528a3d75edeae">PercentStringr</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda3df92c38564360c4aa73c65abcad153a">PercentStringw</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdadff89867eee2f270b2bbf832a690a70c">PercentStringx</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdad72bbb5f1aa4cd77945f45796235e38d">POD</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a>(const char *prop, const char *val)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#aa3bb000261e4752d89e06afe69d665f0">QsciLexerRuby</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#afa0b9ecea2700420820e4e9b705cb784">readProperties</a>(QSettings &amp;qs, const QString &amp;prefix)</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#abf07311e229b5ec1370dd8a57873c1b6">refreshProperties</a>()</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda939c896299ac5cc057aced1a059250a2">Regex</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a>(QsciAbstractAPIs *apis)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a>(int autoindentstyle)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a>(const QFont &amp;f)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>setEditor</b>(QsciScintilla *editor) (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a>(bool eoffill, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#acf9bbfcaf3dfd6004428920e1c6572fd">setFoldComments</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a0e83f239ecb3c52bf4930412f32f51f1">setFoldCompact</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a>(const QFont &amp;f, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdabb7fbac71a097f21eb72fa0133f5c705">SingleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdae0c485bc9e3025341d39501600a5221d">Stderr</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdabb88fb8ac7aadad4027a14bfe2aa329b">Stdin</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda3d377471f1eb2d17957d8050ed4fdf6d">Stdout</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdaf94cff4b54c0376f5c0e99ab3bf5cbee">Symbol</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>textAsBytes</b>(const QString &amp;text) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#aace68e3dbcef9da1b031fb9cfd843c57">wordCharacters</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerRuby.html#af187d6973df01f3f704b181a446ea2f5">writeProperties</a>(QSettings &amp;qs, const QString &amp;prefix) const</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerRuby.html#a4fe52167ba709a506391026615d0ef7b">~QsciLexerRuby</a>()</td><td class="entry"><a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
