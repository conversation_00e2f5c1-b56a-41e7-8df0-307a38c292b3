<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_v"></a>- v -</h3><ul>
<li>v2UnicodeAllowed()
: <a class="el" href="classQsciLexerPython.html#add088b1bd36b0d5eb0f3b87e403cec10">QsciLexerPython</a>
</li>
<li>v3BinaryOctalAllowed()
: <a class="el" href="classQsciLexerPython.html#a02ad644d3bc229939e57d5e9f665a6b9">QsciLexerPython</a>
</li>
<li>v3BytesAllowed()
: <a class="el" href="classQsciLexerPython.html#a67308885b201ef6e21f0a273bf0b3c31">QsciLexerPython</a>
</li>
<li>validKey()
: <a class="el" href="classQsciCommand.html#aeb517d586cb9569d072fcd8a9658911b">QsciCommand</a>
</li>
<li>Value
: <a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0acdf1ff5843500ebbfe209bff6c0e4370">QsciLexerCSS</a>
, <a class="el" href="classQsciLexerSpice.html#a99b1b104224cab9d85ef6cf254ae631bab60e5eb1f93052950f0c913b91cacc73">QsciLexerSpice</a>
</li>
<li>Variable
: <a class="el" href="classQsciLexerBatch.html#a2e13faf432e7c61bee9cbe433b7451f4ad471bc2288e75b3f82a290f0e143c5f4">QsciLexerBatch</a>
, <a class="el" href="classQsciLexerCMake.html#a66895a601b7ef292c78a2ad73305cde5ac5108d9ae30f7b74ce6c621f69531e6e">QsciLexerCMake</a>
, <a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0ab7b8d54656b8a5eb94be142ad027f672">QsciLexerCSS</a>
, <a class="el" href="classQsciLexerMakefile.html#a77e8da2d368723364f5e2df432ce7874a58d72990db11b97d2dd8dbd8124f8386">QsciLexerMakefile</a>
</li>
<li>VBScriptComment
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a48028a0028e6c185c8c0a8b3310374ee">QsciLexerHTML</a>
</li>
<li>VBScriptDefault
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a5da6af0b230ed6209c2ed48574369ae3">QsciLexerHTML</a>
</li>
<li>VBScriptIdentifier
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aa8f04375ef50150287dcee5c24bcf285">QsciLexerHTML</a>
</li>
<li>VBScriptKeyword
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a5e5579413c3b931481d5881a18bc9e38">QsciLexerHTML</a>
</li>
<li>VBScriptNumber
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a71776167656c34320c2c9fc85e8ea33d">QsciLexerHTML</a>
</li>
<li>VBScriptStart
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aff0c53aa932f6d2150ae3605c686a363">QsciLexerHTML</a>
</li>
<li>VBScriptString
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a9a3c32775ad47d14eaa8bdb270ce722e">QsciLexerHTML</a>
</li>
<li>VBScriptUnclosedString
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42afe0d2ae2c61751803669067cdb62d4de">QsciLexerHTML</a>
</li>
<li>VCHome
: <a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ac443339cd46d0646cd97870506e91110">QsciCommand</a>
</li>
<li>VCHomeExtend
: <a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a1e0ff1255da4ea0a77750d55a9aaaef4">QsciCommand</a>
</li>
<li>VCHomeRectExtend
: <a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7adbf224a91e83518a244bb5a726c69bed">QsciCommand</a>
</li>
<li>VCHomeWrap
: <a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7affdd507c7e4221726f980f95910ed5a5">QsciCommand</a>
</li>
<li>VCHomeWrapExtend
: <a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7af1db27f7f04534cc2071e71c422e4a45">QsciCommand</a>
</li>
<li>VerbatimString
: <a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a6f653903645cf19e5ea1c7e870ae9efb">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1afb3585e07f994345f2c21f43f7e2ec1a">QsciLexerCPP</a>
</li>
<li>verbatimStringEscapeSequencesAllowed()
: <a class="el" href="classQsciLexerCPP.html#a20439ec93f1af6b8227bdcd48a6070ec">QsciLexerCPP</a>
</li>
<li>VerticalCentreCaret
: <a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a593083e106606bf5fb3d2322068c455f">QsciCommand</a>
</li>
<li>VerticalLine
: <a class="el" href="classQsciScintilla.html#a08467ef528d3048db763979f42664496acca6c5051d9438d75b757aaf5a0a6ab6">QsciScintilla</a>
</li>
<li>visible()
: <a class="el" href="classQsciStyle.html#a0fd0947a87e3bf43720227b8226b3edd">QsciStyle</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
