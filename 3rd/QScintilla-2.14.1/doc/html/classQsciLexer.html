<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciLexer Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-slots">Public Slots</a> &#124;
<a href="#signals">Signals</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="classQsciLexer-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciLexer Class Reference<span class="mlabels"><span class="mlabel">abstract</span></span></div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscilexer.h&gt;</code></p>

<p>Inherits QObject.</p>

<p>Inherited by <a class="el" href="classQsciLexerAsm.html">QsciLexerAsm</a>, <a class="el" href="classQsciLexerAVS.html">QsciLexerAVS</a>, <a class="el" href="classQsciLexerBash.html">QsciLexerBash</a>, <a class="el" href="classQsciLexerBatch.html">QsciLexerBatch</a>, <a class="el" href="classQsciLexerCMake.html">QsciLexerCMake</a>, <a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a>, <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>, <a class="el" href="classQsciLexerCSS.html">QsciLexerCSS</a>, <a class="el" href="classQsciLexerCustom.html">QsciLexerCustom</a>, <a class="el" href="classQsciLexerD.html">QsciLexerD</a>, <a class="el" href="classQsciLexerDiff.html">QsciLexerDiff</a>, <a class="el" href="classQsciLexerEDIFACT.html">QsciLexerEDIFACT</a>, <a class="el" href="classQsciLexerFortran77.html">QsciLexerFortran77</a>, <a class="el" href="classQsciLexerHex.html">QsciLexerHex</a>, <a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a>, <a class="el" href="classQsciLexerJSON.html">QsciLexerJSON</a>, <a class="el" href="classQsciLexerLua.html">QsciLexerLua</a>, <a class="el" href="classQsciLexerMakefile.html">QsciLexerMakefile</a>, <a class="el" href="classQsciLexerMarkdown.html">QsciLexerMarkdown</a>, <a class="el" href="classQsciLexerMatlab.html">QsciLexerMatlab</a>, <a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a>, <a class="el" href="classQsciLexerPerl.html">QsciLexerPerl</a>, <a class="el" href="classQsciLexerPO.html">QsciLexerPO</a>, <a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a>, <a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a>, <a class="el" href="classQsciLexerProperties.html">QsciLexerProperties</a>, <a class="el" href="classQsciLexerPython.html">QsciLexerPython</a>, <a class="el" href="classQsciLexerRuby.html">QsciLexerRuby</a>, <a class="el" href="classQsciLexerSpice.html">QsciLexerSpice</a>, <a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a>, <a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a>, <a class="el" href="classQsciLexerTeX.html">QsciLexerTeX</a>, <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>, <a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a>, and <a class="el" href="classQsciLexerYAML.html">QsciLexerYAML</a>.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-slots"></a>
Public Slots</h2></td></tr>
<tr class="memitem:a793e592d3ac100ff81ae09eefbaa74ef"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a> (int autoindentstyle)</td></tr>
<tr class="separator:a793e592d3ac100ff81ae09eefbaa74ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e4235e0bd33f64431a9c6e8c35038d4"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:a0e4235e0bd33f64431a9c6e8c35038d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fccdb7cb8f6524ecdeb3ff364ae5a49"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a> (bool eoffill, int style=-1)</td></tr>
<tr class="separator:a3fccdb7cb8f6524ecdeb3ff364ae5a49"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3484599b6db81b8392ab6cd4f50ab291"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a> (const QFont &amp;f, int style=-1)</td></tr>
<tr class="separator:a3484599b6db81b8392ab6cd4f50ab291"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addbc923c938f946180a15d494d17b567"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:addbc923c938f946180a15d494d17b567"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="signals"></a>
Signals</h2></td></tr>
<tr class="memitem:a901cf93072b3db3ffe503eab78ae6954"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:a901cf93072b3db3ffe503eab78ae6954"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66c01f0c9470164d4575c2b64f0e4220"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a> (bool eolfilled, int style)</td></tr>
<tr class="separator:a66c01f0c9470164d4575c2b64f0e4220"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac04ade8be901b67af681e5e3516c0946"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a> (const QFont &amp;f, int style)</td></tr>
<tr class="separator:ac04ade8be901b67af681e5e3516c0946"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8de1727583e902c7cae673673a78a1"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:adf8de1727583e902c7cae673673a78a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd8475f0da36449dc6b1189a587d7a83"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a> (const char *prop, const char *val)</td></tr>
<tr class="separator:acd8475f0da36449dc6b1189a587d7a83"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a49fc2fb49ed07f1cb5f8b0a96e07d0d4"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a> (QObject *parent=0)</td></tr>
<tr class="separator:a49fc2fb49ed07f1cb5f8b0a96e07d0d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6cc5bb9d9421d806e9941d018030068"><td class="memItemLeft" align="right" valign="top"><a id="af6cc5bb9d9421d806e9941d018030068"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a> ()</td></tr>
<tr class="separator:af6cc5bb9d9421d806e9941d018030068"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a3adc7b5c8926e097e6be4340bee920"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8a3adc7b5c8926e097e6be4340bee920">language</a> () const =0</td></tr>
<tr class="separator:a8a3adc7b5c8926e097e6be4340bee920"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7c40b97187e23ab85f6d95113f91b39"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac7c40b97187e23ab85f6d95113f91b39">lexer</a> () const</td></tr>
<tr class="separator:ac7c40b97187e23ab85f6d95113f91b39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9576dd2ce748647abe981724ee76c1ce"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a> () const</td></tr>
<tr class="separator:a9576dd2ce748647abe981724ee76c1ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aa2c7bc939d793db01bbc1863b15d63"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a> () const</td></tr>
<tr class="separator:a0aa2c7bc939d793db01bbc1863b15d63"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6504a6fff35af16fbfd97889048db2a5"><td class="memItemLeft" align="right" valign="top"><a id="a6504a6fff35af16fbfd97889048db2a5"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a> () const</td></tr>
<tr class="separator:a6504a6fff35af16fbfd97889048db2a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e10650b0e9ad137062ad5c17ad33e76"><td class="memItemLeft" align="right" valign="top">virtual QStringList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">autoCompletionWordSeparators</a> () const</td></tr>
<tr class="separator:a4e10650b0e9ad137062ad5c17ad33e76"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79c27285b6033c553b3f54cb6c56b338"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a> ()</td></tr>
<tr class="separator:a79c27285b6033c553b3f54cb6c56b338"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e294eba77713f516acbcebc10af1493"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">blockEnd</a> (int *style=0) const</td></tr>
<tr class="separator:a8e294eba77713f516acbcebc10af1493"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b1bb1261e7b9701c62bbe4f1d171e06"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a> () const</td></tr>
<tr class="separator:a8b1bb1261e7b9701c62bbe4f1d171e06"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a340eafe726fd6964c0adba956fe3428c"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">blockStart</a> (int *style=0) const</td></tr>
<tr class="separator:a340eafe726fd6964c0adba956fe3428c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf12117a142b6f68479ea425d80a4196"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a> (int *style=0) const</td></tr>
<tr class="separator:abf12117a142b6f68479ea425d80a4196"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:affe136114d62180e9a14caa81f2b7fd5"><td class="memItemLeft" align="right" valign="top"><a id="affe136114d62180e9a14caa81f2b7fd5"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#affe136114d62180e9a14caa81f2b7fd5">braceStyle</a> () const</td></tr>
<tr class="separator:affe136114d62180e9a14caa81f2b7fd5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afccca7eb1aed463f89ac442d99135839"><td class="memItemLeft" align="right" valign="top"><a id="afccca7eb1aed463f89ac442d99135839"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a> () const</td></tr>
<tr class="separator:afccca7eb1aed463f89ac442d99135839"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acff58ba06195b9458a61d7ef3573c701"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a> (int style) const</td></tr>
<tr class="separator:acff58ba06195b9458a61d7ef3573c701"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6ed26c11f54f71a305d3ee03d685f06"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a> (int style) const</td></tr>
<tr class="separator:aa6ed26c11f54f71a305d3ee03d685f06"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd34f0d3055b8c7b52f0156f92244e8c"><td class="memItemLeft" align="right" valign="top">virtual QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a> (int style) const</td></tr>
<tr class="separator:abd34f0d3055b8c7b52f0156f92244e8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff4735542e937c5e35ecb2eb82e8f875"><td class="memItemLeft" align="right" valign="top"><a id="aff4735542e937c5e35ecb2eb82e8f875"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a> () const</td></tr>
<tr class="separator:aff4735542e937c5e35ecb2eb82e8f875"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a013b7c1bf9846e231b97827dfd9540b0"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a013b7c1bf9846e231b97827dfd9540b0">keywords</a> (int set) const</td></tr>
<tr class="separator:a013b7c1bf9846e231b97827dfd9540b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2447139ff781bf55c74177881ac023ac"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a> () const</td></tr>
<tr class="separator:a2447139ff781bf55c74177881ac023ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add9c20adb43bc38d1a0ca3083ac3e6fa"><td class="memItemLeft" align="right" valign="top">virtual QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#add9c20adb43bc38d1a0ca3083ac3e6fa">description</a> (int style) const =0</td></tr>
<tr class="separator:add9c20adb43bc38d1a0ca3083ac3e6fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a519df98c9e7d9d26734a38ea9bed744a"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a> (int style) const</td></tr>
<tr class="separator:a519df98c9e7d9d26734a38ea9bed744a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31f12624858cbb8abdc59af34b5a85c7"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">defaultColor</a> () const</td></tr>
<tr class="separator:a31f12624858cbb8abdc59af34b5a85c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7508f1b816a2c9446d36141edc9b5ce"><td class="memItemLeft" align="right" valign="top"><a id="af7508f1b816a2c9446d36141edc9b5ce"></a>
virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#af7508f1b816a2c9446d36141edc9b5ce">defaultColor</a> (int style) const</td></tr>
<tr class="separator:af7508f1b816a2c9446d36141edc9b5ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a06228b73f8df699a211be872f54d8501"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a06228b73f8df699a211be872f54d8501">defaultEolFill</a> (int style) const</td></tr>
<tr class="separator:a06228b73f8df699a211be872f54d8501"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7cf70f76eb03d6d475985cc4b884b0e"><td class="memItemLeft" align="right" valign="top">QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">defaultFont</a> () const</td></tr>
<tr class="separator:ac7cf70f76eb03d6d475985cc4b884b0e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a147ab3b400fcbe1e5d733b8a897f4930"><td class="memItemLeft" align="right" valign="top"><a id="a147ab3b400fcbe1e5d733b8a897f4930"></a>
virtual QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a147ab3b400fcbe1e5d733b8a897f4930">defaultFont</a> (int style) const</td></tr>
<tr class="separator:a147ab3b400fcbe1e5d733b8a897f4930"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2dce337026551b6440e1dcdafa95b7d7"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">defaultPaper</a> () const</td></tr>
<tr class="separator:a2dce337026551b6440e1dcdafa95b7d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7e5ab7f541d913760c32abedbdc72963"><td class="memItemLeft" align="right" valign="top"><a id="a7e5ab7f541d913760c32abedbdc72963"></a>
virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a7e5ab7f541d913760c32abedbdc72963">defaultPaper</a> (int style) const</td></tr>
<tr class="separator:a7e5ab7f541d913760c32abedbdc72963"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad892735ca7ad0bad9b7fafdcb44eeaa8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciScintilla.html">QsciScintilla</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a> () const</td></tr>
<tr class="separator:ad892735ca7ad0bad9b7fafdcb44eeaa8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2e1ada934a5dc7685c1ee6a464de5fd"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a> (<a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *<a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>)</td></tr>
<tr class="separator:ac2e1ada934a5dc7685c1ee6a464de5fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32b16ee95c3dabbc7de61541dd110521"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a32b16ee95c3dabbc7de61541dd110521"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19f0b390b5594d0dff5e4d4b484e43d2"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a> (const QFont &amp;f)</td></tr>
<tr class="separator:a19f0b390b5594d0dff5e4d4b484e43d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ebaedee6979d4cb17399361b37e33e0"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a7ebaedee6979d4cb17399361b37e33e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e81186b1f8f8bc2a4901a42cbca568a"><td class="memItemLeft" align="right" valign="top"><a id="a1e81186b1f8f8bc2a4901a42cbca568a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>setEditor</b> (<a class="el" href="classQsciScintilla.html">QsciScintilla</a> *<a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>)</td></tr>
<tr class="separator:a1e81186b1f8f8bc2a4901a42cbca568a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27728e4e361c5f4bf87690d34d83057d"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td></tr>
<tr class="separator:a27728e4e361c5f4bf87690d34d83057d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae508c3ab4ce1f338dfff3ddf5ee7e34c"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ae508c3ab4ce1f338dfff3ddf5ee7e34c">refreshProperties</a> ()</td></tr>
<tr class="separator:ae508c3ab4ce1f338dfff3ddf5ee7e34c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab222fbddb7eb72261153d1bebb5a01ee"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a> () const</td></tr>
<tr class="separator:ab222fbddb7eb72261153d1bebb5a01ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aace68e3dbcef9da1b031fb9cfd843c57"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aace68e3dbcef9da1b031fb9cfd843c57">wordCharacters</a> () const</td></tr>
<tr class="separator:aace68e3dbcef9da1b031fb9cfd843c57"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a619ee93cb512755e3f946fe61ee097de"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td></tr>
<tr class="separator:a619ee93cb512755e3f946fe61ee097de"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:ad472b16506a4cbc19634f07aa90f1ea6"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ad472b16506a4cbc19634f07aa90f1ea6">readProperties</a> (QSettings &amp;qs, const QString &amp;prefix)</td></tr>
<tr class="separator:ad472b16506a4cbc19634f07aa90f1ea6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abccc4e010b724df1a7b5c5f3bce29501"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abccc4e010b724df1a7b5c5f3bce29501">writeProperties</a> (QSettings &amp;qs, const QString &amp;prefix) const</td></tr>
<tr class="separator:abccc4e010b724df1a7b5c5f3bce29501"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41d4521504d63ee63d43fd7ed0c003ee"><td class="memItemLeft" align="right" valign="top"><a id="a41d4521504d63ee63d43fd7ed0c003ee"></a>
QByteArray&#160;</td><td class="memItemRight" valign="bottom"><b>textAsBytes</b> (const QString &amp;text) const</td></tr>
<tr class="separator:a41d4521504d63ee63d43fd7ed0c003ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5261dd606c209a5c6a494e608a9a111a"><td class="memItemLeft" align="right" valign="top"><a id="a5261dd606c209a5c6a494e608a9a111a"></a>
QString&#160;</td><td class="memItemRight" valign="bottom"><b>bytesAsText</b> (const char *bytes, int size) const</td></tr>
<tr class="separator:a5261dd606c209a5c6a494e608a9a111a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciLexer.html" title="The QsciLexer class is an abstract class used as a base for language lexers.">QsciLexer</a> class is an abstract class used as a base for language lexers. </p>
<p>A lexer scans the text breaking it up into separate language objects, e.g. keywords, strings, operators. The lexer then uses a different style to draw each object. A style is identified by a style number and has a number of attributes, including colour and font. A specific language lexer will implement appropriate default styles which can be overriden by an application by further sub-classing the specific language lexer.</p>
<p>A lexer may provide one or more sets of words to be recognised as keywords. Most lexers only provide one set, but some may support languages embedded in other languages and provide several sets.</p>
<p><a class="el" href="classQsciLexer.html" title="The QsciLexer class is an abstract class used as a base for language lexers.">QsciLexer</a> provides convenience methods for saving and restoring user preferences for fonts and colours.</p>
<p>If you want to write a lexer for a new language then you can add it to the underlying Scintilla code and implement a corresponding <a class="el" href="classQsciLexer.html" title="The QsciLexer class is an abstract class used as a base for language lexers.">QsciLexer</a> sub-class to manage the different styles used. Alternatively you can implement a sub-class of <a class="el" href="classQsciLexerCustom.html" title="The QsciLexerCustom class is an abstract class used as a base for new language lexers.">QsciLexerCustom</a>. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a49fc2fb49ed07f1cb5f8b0a96e07d0d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">&#9670;&nbsp;</a></span>QsciLexer()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciLexer::QsciLexer </td>
          <td>(</td>
          <td class="paramtype">QObject *&#160;</td>
          <td class="paramname"><em>parent</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Construct a <a class="el" href="classQsciLexer.html" title="The QsciLexer class is an abstract class used as a base for language lexers.">QsciLexer</a> with parent <em>parent</em>. <em>parent</em> is typically the <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> instance. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a8a3adc7b5c8926e097e6be4340bee920"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8a3adc7b5c8926e097e6be4340bee920">&#9670;&nbsp;</a></span>language()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual const char* QsciLexer::language </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">pure virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the name of the language. It must be re-implemented by a sub-class. </p>

<p>Implemented in <a class="el" href="classQsciLexerYAML.html#a0cfceb4470cde4184e76076ac34dea29">QsciLexerYAML</a>, <a class="el" href="classQsciLexerXML.html#a801d7cef474dcf23d93e2f2f53034abe">QsciLexerXML</a>, <a class="el" href="classQsciLexerVHDL.html#a2a8cd041dea81adb54a869c17ee4c8ba">QsciLexerVHDL</a>, <a class="el" href="classQsciLexerVerilog.html#a79e3ff22e68d54f640bd2f7747a7a193">QsciLexerVerilog</a>, <a class="el" href="classQsciLexerTeX.html#ad94fbbd156020166afddb8a0a55eba6f">QsciLexerTeX</a>, <a class="el" href="classQsciLexerTekHex.html#ad9666a08ae0b9143a1d3f155ce563819">QsciLexerTekHex</a>, <a class="el" href="classQsciLexerTCL.html#a4a13fa4667146e0dca9d8c15255280a9">QsciLexerTCL</a>, <a class="el" href="classQsciLexerSRec.html#a3e37c3d7527369901bcb28bba3b823e6">QsciLexerSRec</a>, <a class="el" href="classQsciLexerSQL.html#a0b1959541108a437dcb0b104a46f1444">QsciLexerSQL</a>, <a class="el" href="classQsciLexerSpice.html#ae41f7a78d82f09b4d5176ec2a709ac67">QsciLexerSpice</a>, <a class="el" href="classQsciLexerRuby.html#a700754468352f673157d08d4ff222e79">QsciLexerRuby</a>, <a class="el" href="classQsciLexerPython.html#ae96690293b8128bea9cedf9b55b92ad6">QsciLexerPython</a>, <a class="el" href="classQsciLexerProperties.html#a5e15c53d398d9d7e9ef7e0df41bc3f62">QsciLexerProperties</a>, <a class="el" href="classQsciLexerPOV.html#a02880268227d380ef25a72af2605ef0f">QsciLexerPOV</a>, <a class="el" href="classQsciLexerPostScript.html#ab2d6a4d13e15769bf1110012b491ad90">QsciLexerPostScript</a>, <a class="el" href="classQsciLexerPO.html#ace592f4a2d86db6be6c6f363227c00ee">QsciLexerPO</a>, <a class="el" href="classQsciLexerPerl.html#a16fb82e08452dc260bdda610817c79ea">QsciLexerPerl</a>, <a class="el" href="classQsciLexerPascal.html#a072c10d35abc0e56e09806eeb78ab66f">QsciLexerPascal</a>, <a class="el" href="classQsciLexerOctave.html#acca6b44f3f90599d119fb05f375cb2b8">QsciLexerOctave</a>, <a class="el" href="classQsciLexerNASM.html#a41bba5ae9f9e264acf1a4e9ef1e443f6">QsciLexerNASM</a>, <a class="el" href="classQsciLexerMatlab.html#a1b26669dd868d97d8a04837aada5549f">QsciLexerMatlab</a>, <a class="el" href="classQsciLexerMASM.html#ac9f1def4274bef684308ca40cd1c997b">QsciLexerMASM</a>, <a class="el" href="classQsciLexerMarkdown.html#a7a06bdb17ebde731368ec204404ed0ee">QsciLexerMarkdown</a>, <a class="el" href="classQsciLexerMakefile.html#a561482313e4c6597b8c4627ec38e4d54">QsciLexerMakefile</a>, <a class="el" href="classQsciLexerLua.html#a942c993effc83d0dedec2fc20d8a741f">QsciLexerLua</a>, <a class="el" href="classQsciLexerJSON.html#a7a2271db1a39037a429faaa5ff8e399f">QsciLexerJSON</a>, <a class="el" href="classQsciLexerJavaScript.html#aa7bd1f345699cc97fac25cf29ae98a4e">QsciLexerJavaScript</a>, <a class="el" href="classQsciLexerJava.html#af0f0dd1756ceb60bd8f404f3b48f470c">QsciLexerJava</a>, <a class="el" href="classQsciLexerIntelHex.html#acb62449f90ad9ebb39a3c261bbe1e3ca">QsciLexerIntelHex</a>, <a class="el" href="classQsciLexerIDL.html#a56afa4275e743eeff3dc693d9da85fd3">QsciLexerIDL</a>, <a class="el" href="classQsciLexerHTML.html#a336165187c8ab4cc5e51912033316943">QsciLexerHTML</a>, <a class="el" href="classQsciLexerFortran77.html#ae3ef35311f24a24300140512dd005f54">QsciLexerFortran77</a>, <a class="el" href="classQsciLexerFortran.html#a83d7d8209efca06d10870607c9db3c72">QsciLexerFortran</a>, <a class="el" href="classQsciLexerEDIFACT.html#a77024f83fb756608060b105d3f21ae34">QsciLexerEDIFACT</a>, <a class="el" href="classQsciLexerDiff.html#a795af727d45974e6581ed01bf812b63e">QsciLexerDiff</a>, <a class="el" href="classQsciLexerD.html#a667febcf6234a15b7ca6d4ddbfb97bc6">QsciLexerD</a>, <a class="el" href="classQsciLexerCSS.html#a2c29f0bbe4d09c159040b5676c8143d2">QsciLexerCSS</a>, <a class="el" href="classQsciLexerCSharp.html#a92e6554430736b20b147b7290d4bfe16">QsciLexerCSharp</a>, <a class="el" href="classQsciLexerCPP.html#a7c5dafabba34ff3e6120d9f3606cade0">QsciLexerCPP</a>, <a class="el" href="classQsciLexerCoffeeScript.html#a126d81ec982782507eafae1af5d0d856">QsciLexerCoffeeScript</a>, <a class="el" href="classQsciLexerCMake.html#a0aa2f537e70f47e6a3e1bcf6d383a480">QsciLexerCMake</a>, <a class="el" href="classQsciLexerBatch.html#a18341dcb06d1b74269ed1f33c002b2a9">QsciLexerBatch</a>, <a class="el" href="classQsciLexerBash.html#a1bc505b1b0f41472062f86b205ea2860">QsciLexerBash</a>, and <a class="el" href="classQsciLexerAVS.html#a1ef24398e95c23a8b3c858179e5eb564">QsciLexerAVS</a>.</p>

</div>
</div>
<a id="ac7c40b97187e23ab85f6d95113f91b39"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac7c40b97187e23ab85f6d95113f91b39">&#9670;&nbsp;</a></span>lexer()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual const char* QsciLexer::lexer </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the name of the lexer. If 0 is returned then the lexer's numeric identifier is used. The default implementation returns 0.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId()</a> </dd></dl>

<p>Reimplemented in <a class="el" href="classQsciLexerYAML.html#ab946b8c9f34872b69c31e6e9cd0b0e57">QsciLexerYAML</a>, <a class="el" href="classQsciLexerXML.html#ae0bb41012d9d98366b435f9a534ce7a1">QsciLexerXML</a>, <a class="el" href="classQsciLexerVHDL.html#abf1a8dc25c7bd5d272c119d3c3e9e369">QsciLexerVHDL</a>, <a class="el" href="classQsciLexerVerilog.html#abc3666027fe7f0b8ae78ee34e3276069">QsciLexerVerilog</a>, <a class="el" href="classQsciLexerTeX.html#a409c5a8e561b153aca122ad5e0bedc82">QsciLexerTeX</a>, <a class="el" href="classQsciLexerTekHex.html#abf8cd7722d9bd5f7d5f7b03987ba15b8">QsciLexerTekHex</a>, <a class="el" href="classQsciLexerTCL.html#a15ec40b8e6b208521e08d44400eb56f8">QsciLexerTCL</a>, <a class="el" href="classQsciLexerSRec.html#ab53b28ee54f0463f5e5d499d86e81462">QsciLexerSRec</a>, <a class="el" href="classQsciLexerSQL.html#abd8d636e4717ed65e4ea77eca3c28df1">QsciLexerSQL</a>, <a class="el" href="classQsciLexerSpice.html#a0e389df0054dbbafafe6196c71d50738">QsciLexerSpice</a>, <a class="el" href="classQsciLexerRuby.html#a14f1638b2f668fb7d98791cda719f8a0">QsciLexerRuby</a>, <a class="el" href="classQsciLexerPython.html#a9fe6e18dbb7ef4cad7f370286d7db0b7">QsciLexerPython</a>, <a class="el" href="classQsciLexerProperties.html#a76890c95abff2bb6f5eebe7a2cb5a0a3">QsciLexerProperties</a>, <a class="el" href="classQsciLexerPOV.html#af41ceced7bf5eb12aefb77f81240b1eb">QsciLexerPOV</a>, <a class="el" href="classQsciLexerPostScript.html#a8f6156730e68c15fb63e120c53ce7832">QsciLexerPostScript</a>, <a class="el" href="classQsciLexerPO.html#a8eb17be2a61d63249564be87b7d777d8">QsciLexerPO</a>, <a class="el" href="classQsciLexerPerl.html#aae9e42584c6466a8b859d56218eaf28c">QsciLexerPerl</a>, <a class="el" href="classQsciLexerPascal.html#aebc02afb8158d445c4369efa287cc2ac">QsciLexerPascal</a>, <a class="el" href="classQsciLexerOctave.html#aa39859b74adb5cca0470d488186eb6af">QsciLexerOctave</a>, <a class="el" href="classQsciLexerNASM.html#a314bdc56c9b06fe0a99b484b142c541e">QsciLexerNASM</a>, <a class="el" href="classQsciLexerMatlab.html#a62234f5c4dfbeec23fd43dd6651d65e4">QsciLexerMatlab</a>, <a class="el" href="classQsciLexerMASM.html#a4b6d26169eca1609b3fd1ef979cb6e8f">QsciLexerMASM</a>, <a class="el" href="classQsciLexerMarkdown.html#af912a1a568b342c99f70fab70d89b178">QsciLexerMarkdown</a>, <a class="el" href="classQsciLexerMakefile.html#a42e28c95e4f32374ffb7b47a85239d34">QsciLexerMakefile</a>, <a class="el" href="classQsciLexerLua.html#a8124ec8b5b96d95bb225cbb4e95f55cb">QsciLexerLua</a>, <a class="el" href="classQsciLexerJSON.html#a04a2eaa1d93a2266bd170d392b70860b">QsciLexerJSON</a>, <a class="el" href="classQsciLexerIntelHex.html#ac555cadb9fbc0dec91f7630d019f1100">QsciLexerIntelHex</a>, <a class="el" href="classQsciLexerHTML.html#a48e7f3a456fcb347ee96a2c6a1f07231">QsciLexerHTML</a>, <a class="el" href="classQsciLexerFortran77.html#a1ef7534c295a6323be9176fca79b1cbe">QsciLexerFortran77</a>, <a class="el" href="classQsciLexerFortran.html#ad18b58e4e78b74f1c1cc0db18a2d74ca">QsciLexerFortran</a>, <a class="el" href="classQsciLexerEDIFACT.html#a55d08e564f88f40f5167c52bd686b61c">QsciLexerEDIFACT</a>, <a class="el" href="classQsciLexerDiff.html#aec71281020211f0e693143520f232079">QsciLexerDiff</a>, <a class="el" href="classQsciLexerD.html#a62032a66c22767af46af4611fb672cb3">QsciLexerD</a>, <a class="el" href="classQsciLexerCSS.html#a1fa70c8e86dd88d34508fc652d30f3f7">QsciLexerCSS</a>, <a class="el" href="classQsciLexerCPP.html#a3f7076535f370759450ec1243088c7f1">QsciLexerCPP</a>, <a class="el" href="classQsciLexerCoffeeScript.html#aac009a767572be4b4489a0613611cbdb">QsciLexerCoffeeScript</a>, <a class="el" href="classQsciLexerCMake.html#a99fc9415c35eeef2b0f45f066101736b">QsciLexerCMake</a>, <a class="el" href="classQsciLexerBatch.html#acf33e60d28291147562860b824ccd74d">QsciLexerBatch</a>, <a class="el" href="classQsciLexerBash.html#a21f1bb849edbfbc0cf58bc55cc75e8a3">QsciLexerBash</a>, and <a class="el" href="classQsciLexerAVS.html#af462fb11c1cb7d3a5d99cc66d2a4bc6b">QsciLexerAVS</a>.</p>

</div>
</div>
<a id="a9576dd2ce748647abe981724ee76c1ce"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9576dd2ce748647abe981724ee76c1ce">&#9670;&nbsp;</a></span>lexerId()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual int QsciLexer::lexerId </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the identifier (i.e. a QsciScintillaBase::SCLEX_* value) of the lexer. This is only used if <a class="el" href="classQsciLexer.html#ac7c40b97187e23ab85f6d95113f91b39">lexer()</a> returns 0. The default implementation returns <a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a62931496707b79f9d5b348aacbd51a6e">QsciScintillaBase::SCLEX_CONTAINER</a>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexer.html#ac7c40b97187e23ab85f6d95113f91b39">lexer()</a> </dd></dl>

</div>
</div>
<a id="a0aa2c7bc939d793db01bbc1863b15d63"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0aa2c7bc939d793db01bbc1863b15d63">&#9670;&nbsp;</a></span>apis()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a>* QsciLexer::apis </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns the current API set or 0 if there isn't one.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs()</a> </dd></dl>

</div>
</div>
<a id="a4e10650b0e9ad137062ad5c17ad33e76"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4e10650b0e9ad137062ad5c17ad33e76">&#9670;&nbsp;</a></span>autoCompletionWordSeparators()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual QStringList QsciLexer::autoCompletionWordSeparators </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the list of character sequences that can separate auto-completion words. The first in the list is assumed to be the sequence used to separate words in the lexer's API files. </p>

<p>Reimplemented in <a class="el" href="classQsciLexerPython.html#a305ec320aa2357947cbeb1608b95d840">QsciLexerPython</a>, <a class="el" href="classQsciLexerPerl.html#a0e4df63d7d5714b1bdb71c1975f7f99c">QsciLexerPerl</a>, <a class="el" href="classQsciLexerPascal.html#aa28fa3e32d5d4a4efccdad6655fb28c8">QsciLexerPascal</a>, <a class="el" href="classQsciLexerLua.html#aff715db68554a1022792135e8edd0dba">QsciLexerLua</a>, <a class="el" href="classQsciLexerD.html#a9080d0a47d2cbd972d5f2e6c737ba7fa">QsciLexerD</a>, <a class="el" href="classQsciLexerCPP.html#af0ccf94585e15b87a18f12ab9de1c977">QsciLexerCPP</a>, and <a class="el" href="classQsciLexerCoffeeScript.html#a0741fad0b942deb73642be16c3159eb1">QsciLexerCoffeeScript</a>.</p>

</div>
</div>
<a id="a79c27285b6033c553b3f54cb6c56b338"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a79c27285b6033c553b3f54cb6c56b338">&#9670;&nbsp;</a></span>autoIndentStyle()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int QsciLexer::autoIndentStyle </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns the auto-indentation style. The default is 0 if the language is block structured, or <a class="el" href="classQsciScintilla.html#a486adb3348b30c80f53cc1f00c4ed978a63083d9a621b8dc11de24e63f2ccdef6" title="A line is automatically indented to match the previous line.">QsciScintilla::AiMaintain</a> if not.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle()</a>, <a class="el" href="classQsciScintilla.html#a486adb3348b30c80f53cc1f00c4ed978a63083d9a621b8dc11de24e63f2ccdef6" title="A line is automatically indented to match the previous line.">QsciScintilla::AiMaintain</a>, <a class="el" href="classQsciScintilla.html#a486adb3348b30c80f53cc1f00c4ed978a4644ed0f2bb211f82d6ceec31cf0b1ad">QsciScintilla::AiOpening</a>, <a class="el" href="classQsciScintilla.html#a486adb3348b30c80f53cc1f00c4ed978acae08c8d6e6cc73fcd5492d46e2432eb">QsciScintilla::AiClosing</a> </dd></dl>

</div>
</div>
<a id="a8e294eba77713f516acbcebc10af1493"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e294eba77713f516acbcebc10af1493">&#9670;&nbsp;</a></span>blockEnd()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual const char* QsciLexer::blockEnd </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of words or characters in a particular style that define the end of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented in <a class="el" href="classQsciLexerRuby.html#aabf79a666eb40a912dfb7136d79f80e6">QsciLexerRuby</a>, <a class="el" href="classQsciLexerPerl.html#a42133f1b4127c78674f89e3209236a18">QsciLexerPerl</a>, <a class="el" href="classQsciLexerPascal.html#a9914377426e5e464f6d93ce2b64423a0">QsciLexerPascal</a>, <a class="el" href="classQsciLexerD.html#af9f73f93dd57019e3335011528ad6aed">QsciLexerD</a>, <a class="el" href="classQsciLexerCSS.html#ada48a387b3e1414927bebe2415de75f8">QsciLexerCSS</a>, <a class="el" href="classQsciLexerCPP.html#a2263531e4445463f1d75fdfd54102404">QsciLexerCPP</a>, and <a class="el" href="classQsciLexerCoffeeScript.html#a1796c98b07ec6cfc3d5953c225cc1f37">QsciLexerCoffeeScript</a>.</p>

</div>
</div>
<a id="a8b1bb1261e7b9701c62bbe4f1d171e06"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8b1bb1261e7b9701c62bbe4f1d171e06">&#9670;&nbsp;</a></span>blockLookback()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual int QsciLexer::blockLookback </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the number of lines prior to the current one when determining the scope of a block when auto-indenting. </p>

<p>Reimplemented in <a class="el" href="classQsciLexerPython.html#afe42ac5a09816340d4bec920b523aed6">QsciLexerPython</a>.</p>

</div>
</div>
<a id="a340eafe726fd6964c0adba956fe3428c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a340eafe726fd6964c0adba956fe3428c">&#9670;&nbsp;</a></span>blockStart()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual const char* QsciLexer::blockStart </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of words or characters in a particular style that define the start of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented in <a class="el" href="classQsciLexerRuby.html#a7ecc2269f4b7a4956b7209082032245d">QsciLexerRuby</a>, <a class="el" href="classQsciLexerPython.html#adc66ee4b78453d245ac1b4dff45490f4">QsciLexerPython</a>, <a class="el" href="classQsciLexerPerl.html#ae33c3f0e337cfe173c61ea86c5cd3591">QsciLexerPerl</a>, <a class="el" href="classQsciLexerPascal.html#a68d8b422b0d733592cc896086ca23652">QsciLexerPascal</a>, <a class="el" href="classQsciLexerLua.html#a157c462625b4826a5d7fb9eec42cfc78">QsciLexerLua</a>, <a class="el" href="classQsciLexerD.html#a7ea79082a0d55e78cd3a60f1f05af6d9">QsciLexerD</a>, <a class="el" href="classQsciLexerCSS.html#aae249ec529d5f7de5fa238de9208058d">QsciLexerCSS</a>, <a class="el" href="classQsciLexerCPP.html#a79d8b2101ef7b1aef1e7e01557090d6f">QsciLexerCPP</a>, and <a class="el" href="classQsciLexerCoffeeScript.html#a293f0a5c39990ec1db6de249dc618901">QsciLexerCoffeeScript</a>.</p>

</div>
</div>
<a id="abf12117a142b6f68479ea425d80a4196"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abf12117a142b6f68479ea425d80a4196">&#9670;&nbsp;</a></span>blockStartKeyword()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual const char* QsciLexer::blockStartKeyword </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of keywords in a particular style that define the start of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented in <a class="el" href="classQsciLexerRuby.html#a47eb0ab494fe54b5518b4c8bdcd2968e">QsciLexerRuby</a>, <a class="el" href="classQsciLexerPascal.html#abe045873399199ba05d26e94c0e28aae">QsciLexerPascal</a>, <a class="el" href="classQsciLexerD.html#ae4490715b80237feaa25ad92d2fb6313">QsciLexerD</a>, <a class="el" href="classQsciLexerCPP.html#a2cfcfea76c396c0b7b82fc41437ff16f">QsciLexerCPP</a>, and <a class="el" href="classQsciLexerCoffeeScript.html#a497144db9b43beba78cd405a795e08ac">QsciLexerCoffeeScript</a>.</p>

</div>
</div>
<a id="acff58ba06195b9458a61d7ef3573c701"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acff58ba06195b9458a61d7ef3573c701">&#9670;&nbsp;</a></span>color()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual QColor QsciLexer::color </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the foreground colour of the text for style number <em>style</em>. The default colour is that returned by <a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">defaultColor()</a>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">defaultColor()</a>, <a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper()</a> </dd></dl>

</div>
</div>
<a id="aa6ed26c11f54f71a305d3ee03d685f06"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa6ed26c11f54f71a305d3ee03d685f06">&#9670;&nbsp;</a></span>eolFill()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool QsciLexer::eolFill </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the end-of-line for style number <em>style</em>. The default is false. </p>

</div>
</div>
<a id="abd34f0d3055b8c7b52f0156f92244e8c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abd34f0d3055b8c7b52f0156f92244e8c">&#9670;&nbsp;</a></span>font()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual QFont QsciLexer::font </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the font for style number <em>style</em>. The default font is that returned by <a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">defaultFont()</a>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">defaultFont()</a> </dd></dl>

</div>
</div>
<a id="a013b7c1bf9846e231b97827dfd9540b0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a013b7c1bf9846e231b97827dfd9540b0">&#9670;&nbsp;</a></span>keywords()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual const char* QsciLexer::keywords </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>set</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the set of keywords for the keyword set <em>set</em> recognised by the lexer as a space separated string. Keyword sets are numbered from 1. 0 is returned if there is no such set. </p>

<p>Reimplemented in <a class="el" href="classQsciLexerYAML.html#add226b6ffbaee63c29a1f0da7de25784">QsciLexerYAML</a>, <a class="el" href="classQsciLexerXML.html#ab78937576c3c727f073921059ac87a59">QsciLexerXML</a>, <a class="el" href="classQsciLexerVHDL.html#aed2f3934c2fe336324d6e79526c2f7a8">QsciLexerVHDL</a>, <a class="el" href="classQsciLexerVerilog.html#aebb96727a845f9547a60848f6163d461">QsciLexerVerilog</a>, <a class="el" href="classQsciLexerTeX.html#aed0f87e43716cf9894e27e0b90396a98">QsciLexerTeX</a>, <a class="el" href="classQsciLexerTCL.html#a8739852ad69fa4686f0fabd61d18b214">QsciLexerTCL</a>, <a class="el" href="classQsciLexerSQL.html#ac74a6288e07e20f18ad04e900b48851b">QsciLexerSQL</a>, <a class="el" href="classQsciLexerSpice.html#ac4a5d52373228003f7bd51dade64fc85">QsciLexerSpice</a>, <a class="el" href="classQsciLexerRuby.html#abd6f026e6cb154c64c581f6e5f7f2fed">QsciLexerRuby</a>, <a class="el" href="classQsciLexerPython.html#a2467729449b6c78d63305b88b2f62789">QsciLexerPython</a>, <a class="el" href="classQsciLexerPOV.html#a6b21e4498723f3a01fe468e03ebe04f4">QsciLexerPOV</a>, <a class="el" href="classQsciLexerPostScript.html#a981f7ababe1cc561b29617fad8aa29b5">QsciLexerPostScript</a>, <a class="el" href="classQsciLexerPerl.html#a57958c564d4d3127e7ee6148d232bd4b">QsciLexerPerl</a>, <a class="el" href="classQsciLexerPascal.html#a9b6f6a462314471262e5f29057839b34">QsciLexerPascal</a>, <a class="el" href="classQsciLexerOctave.html#a72ce450fad8282f4c02cf28fc6a4b9d2">QsciLexerOctave</a>, <a class="el" href="classQsciLexerMatlab.html#a7afb79f0fec38396668dd52de7fc7c4b">QsciLexerMatlab</a>, <a class="el" href="classQsciLexerLua.html#a136982546f34f83f5e3dd21f67074d4d">QsciLexerLua</a>, <a class="el" href="classQsciLexerJSON.html#af4a9c85e527eda6c28663f055afa0be2">QsciLexerJSON</a>, <a class="el" href="classQsciLexerJavaScript.html#af00e1d05374302fd4d2e2eeec1a829ee">QsciLexerJavaScript</a>, <a class="el" href="classQsciLexerJava.html#ad741254381ce4447588d190ad9c67783">QsciLexerJava</a>, <a class="el" href="classQsciLexerIDL.html#a1fd3bee8279c7e3600ec0ed72dbc2d00">QsciLexerIDL</a>, <a class="el" href="classQsciLexerHTML.html#a56b7f081e520f7660490e3d206d83a73">QsciLexerHTML</a>, <a class="el" href="classQsciLexerFortran77.html#a21724c1f53b67ec6bc72c7ceb1e03d8f">QsciLexerFortran77</a>, <a class="el" href="classQsciLexerFortran.html#a56e0fd6b5d719677050a28ad0d5ae927">QsciLexerFortran</a>, <a class="el" href="classQsciLexerD.html#a9fc58fb17acc5e669780cb870d633514">QsciLexerD</a>, <a class="el" href="classQsciLexerCSS.html#a41d04b17da9c84a94289e91323fb5206">QsciLexerCSS</a>, <a class="el" href="classQsciLexerCSharp.html#a07fcac621f1cba033bb0918cf9d35231">QsciLexerCSharp</a>, <a class="el" href="classQsciLexerCPP.html#ac331bbae026859d8020ac5a6efd8fed1">QsciLexerCPP</a>, <a class="el" href="classQsciLexerCoffeeScript.html#a74867915ad9d609b9b516eff87101cc9">QsciLexerCoffeeScript</a>, <a class="el" href="classQsciLexerCMake.html#a90ed658a569976a68f1260901b7b3518">QsciLexerCMake</a>, <a class="el" href="classQsciLexerBatch.html#ac9329cbc86f1f1a915e548997af76a5f">QsciLexerBatch</a>, <a class="el" href="classQsciLexerBash.html#ac1665f22a91f143e6e6fb46b02e7b109">QsciLexerBash</a>, <a class="el" href="classQsciLexerAVS.html#a9af4c417c88911b8c0ca653d643e3778">QsciLexerAVS</a>, and <a class="el" href="classQsciLexerAsm.html#ad4eae3482cf519fc237705b9cb1aa87d">QsciLexerAsm</a>.</p>

</div>
</div>
<a id="a2447139ff781bf55c74177881ac023ac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2447139ff781bf55c74177881ac023ac">&#9670;&nbsp;</a></span>defaultStyle()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual int QsciLexer::defaultStyle </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the number of the style used for whitespace. The default implementation returns 0 which is the convention adopted by most lexers. </p>

</div>
</div>
<a id="add9c20adb43bc38d1a0ca3083ac3e6fa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#add9c20adb43bc38d1a0ca3083ac3e6fa">&#9670;&nbsp;</a></span>description()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual QString QsciLexer::description </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">pure virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the descriptive name for style number <em>style</em>. For a valid style number for this language a non-empty QString must be returned. If the style number is invalid then an empty QString must be returned. This is intended to be used in user preference dialogs. </p>

<p>Implemented in <a class="el" href="classQsciLexerYAML.html#aa0ca10c4e872620d0d6b2fa1fe1b9af0">QsciLexerYAML</a>, <a class="el" href="classQsciLexerVHDL.html#a367d2a52388bd2602642f4b5dc01bba2">QsciLexerVHDL</a>, <a class="el" href="classQsciLexerVerilog.html#ac6d9fdf26d30d14707e0b0778f80d54d">QsciLexerVerilog</a>, <a class="el" href="classQsciLexerTeX.html#a3218dcdca816cbdc739b2555df366a9a">QsciLexerTeX</a>, <a class="el" href="classQsciLexerTekHex.html#aa84f369b0c91a6f9211efa78b8c03efb">QsciLexerTekHex</a>, <a class="el" href="classQsciLexerTCL.html#a59f517180e03fd1790c4a6de73196a70">QsciLexerTCL</a>, <a class="el" href="classQsciLexerSRec.html#a2a2052f720aba665eea1135db3ad9aed">QsciLexerSRec</a>, <a class="el" href="classQsciLexerSQL.html#a5b2c0f0e93a1e35b0fb42f2dc1abea29">QsciLexerSQL</a>, <a class="el" href="classQsciLexerSpice.html#a8bf8606224bc8841da7ebf53099f8bca">QsciLexerSpice</a>, <a class="el" href="classQsciLexerRuby.html#aff36eb2ba5df9c4998eb9c8311f14de5">QsciLexerRuby</a>, <a class="el" href="classQsciLexerPython.html#aa3454a4c643cd0d479da8412341f1206">QsciLexerPython</a>, <a class="el" href="classQsciLexerProperties.html#a40dcaf1e09ebad7bc685d7f2c5d52a3b">QsciLexerProperties</a>, <a class="el" href="classQsciLexerPOV.html#a71cf91642f6879964a061133013a1f51">QsciLexerPOV</a>, <a class="el" href="classQsciLexerPostScript.html#a88492153c713084f4b5495ebe3bf1b40">QsciLexerPostScript</a>, <a class="el" href="classQsciLexerPO.html#a911dee848cf18712f663b2cfdc5084f1">QsciLexerPO</a>, <a class="el" href="classQsciLexerPerl.html#a386c817d87735b2dd347735cb264d548">QsciLexerPerl</a>, <a class="el" href="classQsciLexerPascal.html#ab47735b5b8b7961044bb9adf111c06bc">QsciLexerPascal</a>, <a class="el" href="classQsciLexerMatlab.html#ae43cc6f38a157e4c70ba460e5004615e">QsciLexerMatlab</a>, <a class="el" href="classQsciLexerMarkdown.html#a2f1340e861947f7c8c4299b1c9ded5a5">QsciLexerMarkdown</a>, <a class="el" href="classQsciLexerMakefile.html#a8be47404070281d5c305be5331616b15">QsciLexerMakefile</a>, <a class="el" href="classQsciLexerLua.html#ad77fd8b1e9ed6bac617f194306de2ea8">QsciLexerLua</a>, <a class="el" href="classQsciLexerJSON.html#af8d88cce706a1d7a95e1a519e0dc56c3">QsciLexerJSON</a>, <a class="el" href="classQsciLexerJavaScript.html#abc88c53a2cfe6dd61e059fad1e8f3539">QsciLexerJavaScript</a>, <a class="el" href="classQsciLexerIntelHex.html#addd404e86692a2b7afadefbd29d6f7e1">QsciLexerIntelHex</a>, <a class="el" href="classQsciLexerIDL.html#accd209bc74cec365745e3987c478a556">QsciLexerIDL</a>, <a class="el" href="classQsciLexerHTML.html#a638fcb2f0d2dd4be844881998cdb3b76">QsciLexerHTML</a>, <a class="el" href="classQsciLexerHex.html#a4cc758f6c9018dd3abeba0291a1008f2">QsciLexerHex</a>, <a class="el" href="classQsciLexerFortran77.html#aa58025e7a9aa9241a64026f00764fb4e">QsciLexerFortran77</a>, <a class="el" href="classQsciLexerEDIFACT.html#a94f9b521b521a540f848d55f2f4e8d45">QsciLexerEDIFACT</a>, <a class="el" href="classQsciLexerDiff.html#a1818bcdd3a7ec5b11ceacf720b07ddcd">QsciLexerDiff</a>, <a class="el" href="classQsciLexerD.html#a68f0cf388c3fa6a70ece2184020ffe55">QsciLexerD</a>, <a class="el" href="classQsciLexerCSS.html#aca9a53a01d50ef44d9f5ac0fd662bf84">QsciLexerCSS</a>, <a class="el" href="classQsciLexerCSharp.html#a3fd919ace71f975bd28b94b34ccd3a19">QsciLexerCSharp</a>, <a class="el" href="classQsciLexerCPP.html#a761b431d688aa99c5c9b5110b41dc712">QsciLexerCPP</a>, <a class="el" href="classQsciLexerCoffeeScript.html#ace6bf74522c57e70f2c3ac525e1fd830">QsciLexerCoffeeScript</a>, <a class="el" href="classQsciLexerCMake.html#aa9285b175e0d9964e427f047f484d0e5">QsciLexerCMake</a>, <a class="el" href="classQsciLexerBatch.html#a142446dc4954e057b2d7de11fe3e25e0">QsciLexerBatch</a>, <a class="el" href="classQsciLexerBash.html#a5017022e35efd5f1c9825d63e4336e73">QsciLexerBash</a>, <a class="el" href="classQsciLexerAVS.html#a23d3bdd816b3da42e65cb4b08f2b01ff">QsciLexerAVS</a>, and <a class="el" href="classQsciLexerAsm.html#a0d08b395dcfd9e5152e8b8f03c26e9c5">QsciLexerAsm</a>.</p>

</div>
</div>
<a id="a519df98c9e7d9d26734a38ea9bed744a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a519df98c9e7d9d26734a38ea9bed744a">&#9670;&nbsp;</a></span>paper()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual QColor QsciLexer::paper </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the background colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">defaultPaper()</a>, <a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color()</a> </dd></dl>

</div>
</div>
<a id="a31f12624858cbb8abdc59af34b5a85c7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a31f12624858cbb8abdc59af34b5a85c7">&#9670;&nbsp;</a></span>defaultColor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexer::defaultColor </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns the default text colour.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor()</a> </dd></dl>

</div>
</div>
<a id="a06228b73f8df699a211be872f54d8501"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a06228b73f8df699a211be872f54d8501">&#9670;&nbsp;</a></span>defaultEolFill()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool QsciLexer::defaultEolFill </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the default end-of-line for style number <em>style</em>. The default is false. </p>

<p>Reimplemented in <a class="el" href="classQsciLexerYAML.html#a01ff9a027edd3a2aa6d443e520b10b73">QsciLexerYAML</a>, <a class="el" href="classQsciLexerXML.html#a995fe59f125a7cb27cf178b9e83015bc">QsciLexerXML</a>, <a class="el" href="classQsciLexerVHDL.html#a8ab227fcb9ba5da466b2d8eded96af70">QsciLexerVHDL</a>, <a class="el" href="classQsciLexerVerilog.html#a59ad64688b9fb852792b3fa15c2b125d">QsciLexerVerilog</a>, <a class="el" href="classQsciLexerTCL.html#a634989e93d2975d1838016ed24f3e45f">QsciLexerTCL</a>, <a class="el" href="classQsciLexerSQL.html#a8c0952bb621cdf048b00191674824a87">QsciLexerSQL</a>, <a class="el" href="classQsciLexerRuby.html#aa6e85b803ff580acecda16deaa70c758">QsciLexerRuby</a>, <a class="el" href="classQsciLexerPython.html#a855939c35d62798c00b0361a0edc41da">QsciLexerPython</a>, <a class="el" href="classQsciLexerProperties.html#ad8cabbe5db0e4ba630cfad60ddfc79b1">QsciLexerProperties</a>, <a class="el" href="classQsciLexerPOV.html#a34fa0bd92884cfa29a27c279369797d5">QsciLexerPOV</a>, <a class="el" href="classQsciLexerPerl.html#a242188212df611073f78d1eff326f5d5">QsciLexerPerl</a>, <a class="el" href="classQsciLexerPascal.html#a45679bbf510fa7e0b264eb9654183f16">QsciLexerPascal</a>, <a class="el" href="classQsciLexerMakefile.html#a6537d97973481d6e7c911f8031385deb">QsciLexerMakefile</a>, <a class="el" href="classQsciLexerLua.html#a4d20a72f3087068af5840042d9beeca7">QsciLexerLua</a>, <a class="el" href="classQsciLexerJSON.html#a3ba9e8000c3896e453b79dcfce08b146">QsciLexerJSON</a>, <a class="el" href="classQsciLexerJavaScript.html#a814917aafe1fef03ec20571e91bb4571">QsciLexerJavaScript</a>, <a class="el" href="classQsciLexerHTML.html#a613622c676e3c70f2c9f002f34326427">QsciLexerHTML</a>, <a class="el" href="classQsciLexerFortran77.html#a43f710d31ccfd80ce8dd4f0ec8fc8d46">QsciLexerFortran77</a>, <a class="el" href="classQsciLexerD.html#ab55d105b2aa041682b67218fcdf964c6">QsciLexerD</a>, <a class="el" href="classQsciLexerCSharp.html#a024d39004611b62884f258c417b5acd3">QsciLexerCSharp</a>, <a class="el" href="classQsciLexerCPP.html#a870955b5547ce4bdf9940165181022b7">QsciLexerCPP</a>, <a class="el" href="classQsciLexerCoffeeScript.html#aad8c778b4c9ef2014e5a508f0ee52021">QsciLexerCoffeeScript</a>, <a class="el" href="classQsciLexerBatch.html#a57d4b4e77554476eea666d793f104540">QsciLexerBatch</a>, <a class="el" href="classQsciLexerBash.html#ac0b0eb74510dd3af8eed933d3e37e2ab">QsciLexerBash</a>, and <a class="el" href="classQsciLexerAsm.html#a6a63f18e16cef082580868662a6f80bf">QsciLexerAsm</a>.</p>

</div>
</div>
<a id="ac7cf70f76eb03d6d475985cc4b884b0e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac7cf70f76eb03d6d475985cc4b884b0e">&#9670;&nbsp;</a></span>defaultFont()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QFont QsciLexer::defaultFont </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns the default font.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont()</a> </dd></dl>

</div>
</div>
<a id="a2dce337026551b6440e1dcdafa95b7d7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2dce337026551b6440e1dcdafa95b7d7">&#9670;&nbsp;</a></span>defaultPaper()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexer::defaultPaper </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns the default paper colour.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper()</a> </dd></dl>

</div>
</div>
<a id="ad892735ca7ad0bad9b7fafdcb44eeaa8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad892735ca7ad0bad9b7fafdcb44eeaa8">&#9670;&nbsp;</a></span>editor()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classQsciScintilla.html">QsciScintilla</a>* QsciLexer::editor </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> instance that the lexer is currently attached to or 0 if it is unattached. </p>

</div>
</div>
<a id="ac2e1ada934a5dc7685c1ee6a464de5fd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac2e1ada934a5dc7685c1ee6a464de5fd">&#9670;&nbsp;</a></span>setAPIs()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexer::setAPIs </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *&#160;</td>
          <td class="paramname"><em>apis</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The current set of APIs is set to <em>apis</em>. If <em>apis</em> is 0 then any existing APIs for this lexer are removed.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis()</a> </dd></dl>

</div>
</div>
<a id="a32b16ee95c3dabbc7de61541dd110521"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a32b16ee95c3dabbc7de61541dd110521">&#9670;&nbsp;</a></span>setDefaultColor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexer::setDefaultColor </td>
          <td>(</td>
          <td class="paramtype">const QColor &amp;&#160;</td>
          <td class="paramname"><em>c</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The default text colour is set to <em>c</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">defaultColor()</a>, <a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color()</a> </dd></dl>

</div>
</div>
<a id="a19f0b390b5594d0dff5e4d4b484e43d2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a19f0b390b5594d0dff5e4d4b484e43d2">&#9670;&nbsp;</a></span>setDefaultFont()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexer::setDefaultFont </td>
          <td>(</td>
          <td class="paramtype">const QFont &amp;&#160;</td>
          <td class="paramname"><em>f</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The default font is set to <em>f</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">defaultFont()</a>, <a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font()</a> </dd></dl>

</div>
</div>
<a id="a7ebaedee6979d4cb17399361b37e33e0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7ebaedee6979d4cb17399361b37e33e0">&#9670;&nbsp;</a></span>setDefaultPaper()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexer::setDefaultPaper </td>
          <td>(</td>
          <td class="paramtype">const QColor &amp;&#160;</td>
          <td class="paramname"><em>c</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The default paper colour is set to <em>c</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">defaultPaper()</a>, <a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper()</a> </dd></dl>

</div>
</div>
<a id="a27728e4e361c5f4bf87690d34d83057d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a27728e4e361c5f4bf87690d34d83057d">&#9670;&nbsp;</a></span>readSettings()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexer::readSettings </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>prefix</em> = <code>&quot;/Scintilla&quot;</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The colour, paper, font and end-of-line for each style number, and all lexer specific properties are read from the settings <em>qs</em>. <em>prefix</em> is prepended to the key of each entry. true is returned if there was no error.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings()</a>, <a class="el" href="classQsciScintilla.html#a7bc5fb5d0daf8261544fb6fe738a0c91">QsciScintilla::setLexer()</a> </dd></dl>

</div>
</div>
<a id="ae508c3ab4ce1f338dfff3ddf5ee7e34c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae508c3ab4ce1f338dfff3ddf5ee7e34c">&#9670;&nbsp;</a></span>refreshProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexer::refreshProperties </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Causes all properties to be refreshed by emitting the <a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged()</a> signal as required. </p>

<p>Reimplemented in <a class="el" href="classQsciLexerYAML.html#ac263eb1fcaeaad44b23c2d990bad1bc1">QsciLexerYAML</a>, <a class="el" href="classQsciLexerXML.html#a29937d422c25f17612c57e16a7bddaf1">QsciLexerXML</a>, <a class="el" href="classQsciLexerVHDL.html#aa60e141b7b1a7aac51d79ad2c27c4c93">QsciLexerVHDL</a>, <a class="el" href="classQsciLexerVerilog.html#ad476092b3970fe44068dd023f8becc96">QsciLexerVerilog</a>, <a class="el" href="classQsciLexerTeX.html#acd80380b4727bd94148f5a0ff479742e">QsciLexerTeX</a>, <a class="el" href="classQsciLexerTCL.html#ad331ec23d27ba397d2095ba92cefaecd">QsciLexerTCL</a>, <a class="el" href="classQsciLexerSQL.html#a9d05744ee6d4c653a7e3976d9f71df23">QsciLexerSQL</a>, <a class="el" href="classQsciLexerRuby.html#abf07311e229b5ec1370dd8a57873c1b6">QsciLexerRuby</a>, <a class="el" href="classQsciLexerPython.html#abed099316dd95a6289c76d151a37c264">QsciLexerPython</a>, <a class="el" href="classQsciLexerProperties.html#a638b892c566301f0efe779c58516cbc0">QsciLexerProperties</a>, <a class="el" href="classQsciLexerPOV.html#a4864bf9360ed4748b9ca7a1d5e34e7d8">QsciLexerPOV</a>, <a class="el" href="classQsciLexerPostScript.html#a0f1e5402dce043de42ded75f5826588f">QsciLexerPostScript</a>, <a class="el" href="classQsciLexerPO.html#a17895e48d655d41d80e4fb4672c2fd72">QsciLexerPO</a>, <a class="el" href="classQsciLexerPerl.html#ac9868e2d0efbf3602a22d8bdac12a119">QsciLexerPerl</a>, <a class="el" href="classQsciLexerPascal.html#a92cb96a2f9d373ed5a91546c42ec0905">QsciLexerPascal</a>, <a class="el" href="classQsciLexerLua.html#a628efb828569208d6219a88f1fc6a1a7">QsciLexerLua</a>, <a class="el" href="classQsciLexerJSON.html#acd0614489de2d2c9f69379a574f1d5eb">QsciLexerJSON</a>, <a class="el" href="classQsciLexerHTML.html#a7c73d608fd96b019e70ebf448de23357">QsciLexerHTML</a>, <a class="el" href="classQsciLexerFortran77.html#a2033202288867ce63c4e93bc45dc55e3">QsciLexerFortran77</a>, <a class="el" href="classQsciLexerD.html#a3df48961344c5133ad595a555bbb8e55">QsciLexerD</a>, <a class="el" href="classQsciLexerCSS.html#a78f4690fa92e02c8511074a334c06096">QsciLexerCSS</a>, <a class="el" href="classQsciLexerCPP.html#a58506e1c965a181c9202376e0ba85c30">QsciLexerCPP</a>, <a class="el" href="classQsciLexerCoffeeScript.html#aefae6df689f1d3dad66d1f2fc141cc39">QsciLexerCoffeeScript</a>, <a class="el" href="classQsciLexerCMake.html#a7cc73bba065690f08e2b6b8e8c00d5d3">QsciLexerCMake</a>, <a class="el" href="classQsciLexerBash.html#aad047f411c36c262305ffcce5015944f">QsciLexerBash</a>, <a class="el" href="classQsciLexerAVS.html#af5a3f47c4f0be631303cabd42d904c3e">QsciLexerAVS</a>, and <a class="el" href="classQsciLexerAsm.html#a25cb4af3f28543dcd6125a79c0520447">QsciLexerAsm</a>.</p>

</div>
</div>
<a id="ab222fbddb7eb72261153d1bebb5a01ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab222fbddb7eb72261153d1bebb5a01ee">&#9670;&nbsp;</a></span>styleBitsNeeded()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual int QsciLexer::styleBitsNeeded </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the number of style bits needed by the lexer. Normally this should only be re-implemented by custom lexers. This is deprecated and no longer has any effect. </p>

<p>Reimplemented in <a class="el" href="classQsciLexerCustom.html#addc357462c04f032e20149b55cb8aeaa">QsciLexerCustom</a>.</p>

</div>
</div>
<a id="aace68e3dbcef9da1b031fb9cfd843c57"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aace68e3dbcef9da1b031fb9cfd843c57">&#9670;&nbsp;</a></span>wordCharacters()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual const char* QsciLexer::wordCharacters </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the string of characters that comprise a word. The default is 0 which implies the upper and lower case alphabetic characters and underscore. </p>

<p>Reimplemented in <a class="el" href="classQsciLexerVerilog.html#abb1544042444a8147123b5597e096ea2">QsciLexerVerilog</a>, <a class="el" href="classQsciLexerTeX.html#a1ba78d98f1f2a18338782acbeb73d22e">QsciLexerTeX</a>, <a class="el" href="classQsciLexerProperties.html#a61d706ce1554474fd54fe07359612814">QsciLexerProperties</a>, <a class="el" href="classQsciLexerPOV.html#aa45fd60cb7c2db5c88b5708f481dd6e2">QsciLexerPOV</a>, <a class="el" href="classQsciLexerPerl.html#a5ffd80ff37350acb6fe03f798f34a912">QsciLexerPerl</a>, <a class="el" href="classQsciLexerMakefile.html#a9e2c6ee91938aad61cfb7304de571bd4">QsciLexerMakefile</a>, <a class="el" href="classQsciLexerHTML.html#ad12b328c98474857186af058726bd38d">QsciLexerHTML</a>, <a class="el" href="classQsciLexerDiff.html#a6a4b4099b20109442416e2bd8309b494">QsciLexerDiff</a>, <a class="el" href="classQsciLexerD.html#a0436f412bb6c83fe195ea2eb3c058154">QsciLexerD</a>, <a class="el" href="classQsciLexerCSS.html#a7cbe39118747739dd557df191c91db0c">QsciLexerCSS</a>, <a class="el" href="classQsciLexerCPP.html#a822ca7489c4655f26bc72ed127285d8a">QsciLexerCPP</a>, <a class="el" href="classQsciLexerCoffeeScript.html#a2e5910796ca5a3f369258718bb75c1d8">QsciLexerCoffeeScript</a>, <a class="el" href="classQsciLexerBatch.html#a93f46567c5b91d993387d2ba033f2030">QsciLexerBatch</a>, <a class="el" href="classQsciLexerBash.html#a65ab592afff78804f6487dd7badd00cf">QsciLexerBash</a>, and <a class="el" href="classQsciLexerAVS.html#aef65e35b32701f0a15d8c2687c20516a">QsciLexerAVS</a>.</p>

</div>
</div>
<a id="a619ee93cb512755e3f946fe61ee097de"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a619ee93cb512755e3f946fe61ee097de">&#9670;&nbsp;</a></span>writeSettings()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexer::writeSettings </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>prefix</em> = <code>&quot;/Scintilla&quot;</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The colour, paper, font and end-of-line for each style number, and all lexer specific properties are written to the settings <em>qs</em>. <em>prefix</em> is prepended to the key of each entry. true is returned if there was no error.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings()</a> </dd></dl>

</div>
</div>
<a id="a793e592d3ac100ff81ae09eefbaa74ef"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a793e592d3ac100ff81ae09eefbaa74ef">&#9670;&nbsp;</a></span>setAutoIndentStyle</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexer::setAutoIndentStyle </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>autoindentstyle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The auto-indentation style is set to <em>autoindentstyle</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle()</a>, <a class="el" href="classQsciScintilla.html#a486adb3348b30c80f53cc1f00c4ed978a63083d9a621b8dc11de24e63f2ccdef6" title="A line is automatically indented to match the previous line.">QsciScintilla::AiMaintain</a>, <a class="el" href="classQsciScintilla.html#a486adb3348b30c80f53cc1f00c4ed978a4644ed0f2bb211f82d6ceec31cf0b1ad">QsciScintilla::AiOpening</a>, <a class="el" href="classQsciScintilla.html#a486adb3348b30c80f53cc1f00c4ed978acae08c8d6e6cc73fcd5492d46e2432eb">QsciScintilla::AiClosing</a> </dd></dl>

</div>
</div>
<a id="a0e4235e0bd33f64431a9c6e8c35038d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0e4235e0bd33f64431a9c6e8c35038d4">&#9670;&nbsp;</a></span>setColor</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexer::setColor </td>
          <td>(</td>
          <td class="paramtype">const QColor &amp;&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em> = <code>-1</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The foreground colour for style number <em>style</em> is set to <em>c</em>. If <em>style</em> is -1 then the colour is set for all styles. </p>

</div>
</div>
<a id="a3fccdb7cb8f6524ecdeb3ff364ae5a49"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3fccdb7cb8f6524ecdeb3ff364ae5a49">&#9670;&nbsp;</a></span>setEolFill</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexer::setEolFill </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>eoffill</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em> = <code>-1</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The end-of-line fill for style number <em>style</em> is set to <em>eoffill</em>. If <em>style</em> is -1 then the fill is set for all styles. </p>

</div>
</div>
<a id="a3484599b6db81b8392ab6cd4f50ab291"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3484599b6db81b8392ab6cd4f50ab291">&#9670;&nbsp;</a></span>setFont</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexer::setFont </td>
          <td>(</td>
          <td class="paramtype">const QFont &amp;&#160;</td>
          <td class="paramname"><em>f</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em> = <code>-1</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The font for style number <em>style</em> is set to <em>f</em>. If <em>style</em> is -1 then the font is set for all styles. </p>

</div>
</div>
<a id="addbc923c938f946180a15d494d17b567"></a>
<h2 class="memtitle"><span class="permalink"><a href="#addbc923c938f946180a15d494d17b567">&#9670;&nbsp;</a></span>setPaper</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexer::setPaper </td>
          <td>(</td>
          <td class="paramtype">const QColor &amp;&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em> = <code>-1</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The background colour for style number <em>style</em> is set to <em>c</em>. If <em>style</em> is -1 then the colour is set for all styles. </p>

</div>
</div>
<a id="a901cf93072b3db3ffe503eab78ae6954"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a901cf93072b3db3ffe503eab78ae6954">&#9670;&nbsp;</a></span>colorChanged</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexer::colorChanged </td>
          <td>(</td>
          <td class="paramtype">const QColor &amp;&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the foreground colour of style number <em>style</em> has changed. The new colour is <em>c</em>. </p>

</div>
</div>
<a id="a66c01f0c9470164d4575c2b64f0e4220"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a66c01f0c9470164d4575c2b64f0e4220">&#9670;&nbsp;</a></span>eolFillChanged</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexer::eolFillChanged </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>eolfilled</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the end-of-file fill of style number <em>style</em> has changed. The new fill is <em>eolfilled</em>. </p>

</div>
</div>
<a id="ac04ade8be901b67af681e5e3516c0946"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac04ade8be901b67af681e5e3516c0946">&#9670;&nbsp;</a></span>fontChanged</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexer::fontChanged </td>
          <td>(</td>
          <td class="paramtype">const QFont &amp;&#160;</td>
          <td class="paramname"><em>f</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the font of style number <em>style</em> has changed. The new font is <em>f</em>. </p>

</div>
</div>
<a id="adf8de1727583e902c7cae673673a78a1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adf8de1727583e902c7cae673673a78a1">&#9670;&nbsp;</a></span>paperChanged</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexer::paperChanged </td>
          <td>(</td>
          <td class="paramtype">const QColor &amp;&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the background colour of style number <em>style</em> has changed. The new colour is <em>c</em>. </p>

</div>
</div>
<a id="acd8475f0da36449dc6b1189a587d7a83"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acd8475f0da36449dc6b1189a587d7a83">&#9670;&nbsp;</a></span>propertyChanged</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexer::propertyChanged </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>prop</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>val</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the value of the lexer property <em>prop</em> needs to be changed. The new value is <em>val</em>. </p>

</div>
</div>
<a id="ad472b16506a4cbc19634f07aa90f1ea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad472b16506a4cbc19634f07aa90f1ea6">&#9670;&nbsp;</a></span>readProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool QsciLexer::readProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are read from the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error. </p>

<p>Reimplemented in <a class="el" href="classQsciLexerYAML.html#a35d4260e9c1a68073a6b4f625c846c11">QsciLexerYAML</a>, <a class="el" href="classQsciLexerXML.html#a2acbf99b93c18d9a9f922c9e2894bf4f">QsciLexerXML</a>, <a class="el" href="classQsciLexerVHDL.html#a46a01d03d516e909c8696fa3f9910c1f">QsciLexerVHDL</a>, <a class="el" href="classQsciLexerVerilog.html#aa1bd0effe3ed23e2bb3334b778efb74a">QsciLexerVerilog</a>, <a class="el" href="classQsciLexerTeX.html#a68e2eaca494e93937f896bd60b86429c">QsciLexerTeX</a>, <a class="el" href="classQsciLexerTCL.html#a1b1d726f87795c97839acca28d06dc6e">QsciLexerTCL</a>, <a class="el" href="classQsciLexerSQL.html#a377b83523f800cc4598126417d80f74c">QsciLexerSQL</a>, <a class="el" href="classQsciLexerRuby.html#afa0b9ecea2700420820e4e9b705cb784">QsciLexerRuby</a>, <a class="el" href="classQsciLexerPython.html#a1b8f36843f4abe6ec3ee75205b5b0111">QsciLexerPython</a>, <a class="el" href="classQsciLexerProperties.html#a4119053764ba32a9975ad7eeb8f0f067">QsciLexerProperties</a>, <a class="el" href="classQsciLexerPOV.html#a5a599e7d97b164fec1ee3c21ba167e80">QsciLexerPOV</a>, <a class="el" href="classQsciLexerPostScript.html#a87168d5b174ba3a9b969ef689f67b355">QsciLexerPostScript</a>, <a class="el" href="classQsciLexerPO.html#a8403f1e2f5ea0c5d67c32dd6053317c5">QsciLexerPO</a>, <a class="el" href="classQsciLexerPerl.html#a47884fcfd8d2b0ab7b8d277cb0325c17">QsciLexerPerl</a>, <a class="el" href="classQsciLexerPascal.html#a2a2beba3b365e2e0e1f21109079f0ffd">QsciLexerPascal</a>, <a class="el" href="classQsciLexerLua.html#a928315606c0bd973c59e0b6d9641c3cd">QsciLexerLua</a>, <a class="el" href="classQsciLexerJSON.html#aba9c88201491763d75a8716d118a4079">QsciLexerJSON</a>, <a class="el" href="classQsciLexerHTML.html#ab9ae7a11b4c9ba6f62d795dce8d6fab8">QsciLexerHTML</a>, <a class="el" href="classQsciLexerFortran77.html#a08b8ae54fae5b280a3864d5696fe009e">QsciLexerFortran77</a>, <a class="el" href="classQsciLexerD.html#abb94e0b0257a50dbde9b0ddbcfeb69d2">QsciLexerD</a>, <a class="el" href="classQsciLexerCSS.html#a7bfdaea964c9e2c51568f63f379b6108">QsciLexerCSS</a>, <a class="el" href="classQsciLexerCPP.html#aa37ea54c5e39721b866c25b0e0335591">QsciLexerCPP</a>, <a class="el" href="classQsciLexerCoffeeScript.html#ae15b25b5d6705a850f6c93ee1013bea7">QsciLexerCoffeeScript</a>, <a class="el" href="classQsciLexerCMake.html#a4578cacfbe802ab993fc07ddeaef3297">QsciLexerCMake</a>, <a class="el" href="classQsciLexerBash.html#aae0cfbb2dbfd2a833a16630c9cf2e36e">QsciLexerBash</a>, <a class="el" href="classQsciLexerAVS.html#ad65ebfab947de5d6e318238f8a0048e4">QsciLexerAVS</a>, and <a class="el" href="classQsciLexerAsm.html#abcd26a3075ae82103e76332fe20a4ed1">QsciLexerAsm</a>.</p>

</div>
</div>
<a id="abccc4e010b724df1a7b5c5f3bce29501"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abccc4e010b724df1a7b5c5f3bce29501">&#9670;&nbsp;</a></span>writeProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool QsciLexer::writeProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are written to the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error. </p>

<p>Reimplemented in <a class="el" href="classQsciLexerYAML.html#adc63ea477a2869f4ea9f1b3fe69d56fb">QsciLexerYAML</a>, <a class="el" href="classQsciLexerXML.html#a1cf58cba78405397f793b6a9aff64035">QsciLexerXML</a>, <a class="el" href="classQsciLexerVHDL.html#a0ed58ff3726deb2215eaff2c1892bc9b">QsciLexerVHDL</a>, <a class="el" href="classQsciLexerVerilog.html#a97b418522a5866d04d9553931dd1c7f4">QsciLexerVerilog</a>, <a class="el" href="classQsciLexerTeX.html#aab7c86d0058b7c8541b0fc7be043f902">QsciLexerTeX</a>, <a class="el" href="classQsciLexerTCL.html#a65a8d0928d9f04584972410a5af82888">QsciLexerTCL</a>, <a class="el" href="classQsciLexerSQL.html#a338a09c79011b57a842c581aa2556b4c">QsciLexerSQL</a>, <a class="el" href="classQsciLexerRuby.html#af187d6973df01f3f704b181a446ea2f5">QsciLexerRuby</a>, <a class="el" href="classQsciLexerPython.html#a8921849dce20c65c0fc024bc27255873">QsciLexerPython</a>, <a class="el" href="classQsciLexerProperties.html#a19a63e47d6b872b510d99d46abb2230f">QsciLexerProperties</a>, <a class="el" href="classQsciLexerPOV.html#a99f8420666e55b23980d05903e7eebc3">QsciLexerPOV</a>, <a class="el" href="classQsciLexerPostScript.html#a0fc741a415b0419464afa66deb2b9e5d">QsciLexerPostScript</a>, <a class="el" href="classQsciLexerPO.html#afebea3d6f2a2cffcb8be859c99c2cede">QsciLexerPO</a>, <a class="el" href="classQsciLexerPerl.html#a16841e0262d8200d5ed3a85099d45b37">QsciLexerPerl</a>, <a class="el" href="classQsciLexerPascal.html#a6a5b21a2ba8b43a2f6b3747af365156f">QsciLexerPascal</a>, <a class="el" href="classQsciLexerLua.html#af0fffa0361bad4a3a007c09a1811db9c">QsciLexerLua</a>, <a class="el" href="classQsciLexerJSON.html#ac4001660bfa52216fe475f84e2ce9d77">QsciLexerJSON</a>, <a class="el" href="classQsciLexerHTML.html#ae6e6be4b076718026d027629b28faba6">QsciLexerHTML</a>, <a class="el" href="classQsciLexerFortran77.html#a6ba40887a94b7f9fe807545eed4f7c83">QsciLexerFortran77</a>, <a class="el" href="classQsciLexerD.html#a4d8069f6efaeba7c4fa810630bed2e2e">QsciLexerD</a>, <a class="el" href="classQsciLexerCSS.html#ac70e03bceba5de91104b85edd00e1a68">QsciLexerCSS</a>, <a class="el" href="classQsciLexerCPP.html#a46bd37b48e91903451ab59314448f322">QsciLexerCPP</a>, <a class="el" href="classQsciLexerCoffeeScript.html#af2acfd7b7a9012577aed90f136ad3fb1">QsciLexerCoffeeScript</a>, <a class="el" href="classQsciLexerCMake.html#a0e2e832caa9adddace3085ebfa582948">QsciLexerCMake</a>, <a class="el" href="classQsciLexerBash.html#a490932b0c83bf7e4048c590565d6a32d">QsciLexerBash</a>, <a class="el" href="classQsciLexerAVS.html#a6b8fc8bf46c22c3efafd92179b644788">QsciLexerAVS</a>, and <a class="el" href="classQsciLexerAsm.html#aade332d1a99d97ddabf219df2f7009ad">QsciLexerAsm</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
