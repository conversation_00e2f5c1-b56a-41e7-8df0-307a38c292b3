<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciStyle Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciStyle.html">QsciStyle</a>, including all inherited members.</p>
<table class="directory">
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>apply</b>(QsciScintillaBase *sci) const (defined in <a class="el" href="classQsciStyle.html">QsciStyle</a>)</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciStyle.html#a4d1aa13e042609e48674f72aebd2ebae">changeable</a>() const</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciStyle.html#af349ce169da83e08ad9f995df48c6547">color</a>() const</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciStyle.html#a04e5cc64877290739b30603c526d84ce">description</a>() const</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciStyle.html#adcc34134da3341f1f07a847b09f6565b">eolFill</a>() const</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciStyle.html#af45628c04ab5488fc13b61a2356346ec">font</a>() const</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciStyle.html#a1df46714ab45c62e5ad5e52a5f41bf15">hotspot</a>() const</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciStyle.html#a4a0e012717bb1fd68de03209260a0609a83675e1da457009277d3642340dc82cc">LowerCase</a> enum value</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciStyle.html#a4a0e012717bb1fd68de03209260a0609a2fac015153af29a7c87eb69848fd4348">OriginalCase</a> enum value</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciStyle.html#a8912da5c6b95404e4642593db1b65d4c">paper</a>() const</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciStyle.html#a0464f0a24f4094431686c89e667e843e">QsciStyle</a>(int style=-1)</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciStyle.html#a4e1d6840e7d117886093bbaabbccd56f">QsciStyle</a>(int style, const QString &amp;description, const QColor &amp;color, const QColor &amp;paper, const QFont &amp;font, bool eolFill=false)</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciStyle.html#aa480e57bbdd83b8164129f875bd48976">refresh</a>()</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciStyle.html#af7e052d08efd3a677f810c8e4116dafc">setChangeable</a>(bool changeable)</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciStyle.html#aa7743a3805662a27ae52a56af3ac315a">setColor</a>(const QColor &amp;color)</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciStyle.html#abd88d76b875c154f099b4e9f36b6fcab">setDescription</a>(const QString &amp;description)</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciStyle.html#a6767dbb23f68292ef9e892dad31ffd9e">setEolFill</a>(bool fill)</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciStyle.html#ab09932c9dafb915b8138d4ec1cbc79cb">setFont</a>(const QFont &amp;font)</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciStyle.html#acb06ba468da57cc4ea9e8d496cb33f83">setHotspot</a>(bool hotspot)</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciStyle.html#a2d4ec76574fd507fbf3c0d006c7427da">setPaper</a>(const QColor &amp;paper)</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciStyle.html#af00ea2dd20e93c5d06d9ce99cbc2cf00">setStyle</a>(int style)</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciStyle.html#a25e9b8a34c334bf6160115a2c43a5256">setTextCase</a>(TextCase text_case)</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciStyle.html#a4f8b9edd94c36344bd7152d15731509a">setVisible</a>(bool visible)</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciStyle.html#a61582248f6b7276db9b4a1f9582c3828">style</a>() const</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciStyle.html#a16212f9f46162f67ece3ed6423207785">textCase</a>() const</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciStyle.html#a4a0e012717bb1fd68de03209260a0609">TextCase</a> enum name</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciStyle.html#a4a0e012717bb1fd68de03209260a0609aeb98b8f24b317cec7c271fd337185e75">UpperCase</a> enum value</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciStyle.html#a0fd0947a87e3bf43720227b8226b3edd">visible</a>() const</td><td class="entry"><a class="el" href="classQsciStyle.html">QsciStyle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
