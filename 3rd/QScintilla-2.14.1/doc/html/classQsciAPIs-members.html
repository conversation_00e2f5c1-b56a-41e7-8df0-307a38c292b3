<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciAPIs Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciAPIs.html">QsciAPIs</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classQsciAPIs.html#af46ca05571eb676d3aa65b080fb406c5">add</a>(const QString &amp;entry)</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciAPIs.html#aaa47506820a2596004688e241fc4cd9f">apiPreparationCancelled</a>()</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciAPIs.html#adf779559d29fed004ec65ef560483e3c">apiPreparationFinished</a>()</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciAPIs.html#a8fc5db618546fcfcc5bdc46e6d062995">apiPreparationStarted</a>()</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciAPIs.html#adff0073d1f4ee2e0ea8b3bf234ff2dd3">autoCompletionSelected</a>(const QString &amp;sel)</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciAPIs.html#a6a75974235c5e8d263bf962c778b3a3e">callTips</a>(const QStringList &amp;context, int commas, QsciScintilla::CallTipsStyle style, QList&lt; int &gt; &amp;shifts)</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciAPIs.html#aa5c7c8855162eeb1be74c226ebf1b1b6">cancelPreparation</a>()</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciAPIs.html#a6b29d84b0b5d63f2b590988195c7557c">clear</a>()</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciAPIs.html#a0a080d197e8226117a626c7b4b68b32d">defaultPreparedName</a>() const</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>event</b>(QEvent *e) (defined in <a class="el" href="classQsciAPIs.html">QsciAPIs</a>)</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciAPIs.html#aa2ee3021ffc6a998776547a5c252edca">installedAPIFiles</a>() const</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciAPIs.html#a9dc74576c602f1df961aa8efee652a3d">isPrepared</a>(const QString &amp;filename=QString()) const</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciAbstractAPIs.html#a90452ab6f4d40314ec519913f9e78ccc">lexer</a>() const</td><td class="entry"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciAPIs.html#a3084b749e4eb1c741fc1004e8a84a631">load</a>(const QString &amp;filename)</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciAPIs.html#af42a26a050bfeb4249d35ab61567ea9e">loadPrepared</a>(const QString &amp;filename=QString())</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciAPIs.html#a5c26b8395c49cf61243e5f73c0ce577f">prepare</a>()</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciAbstractAPIs.html#a9db5ebe8adda3f58892af676f5295e3a">QsciAbstractAPIs</a>(QsciLexer *lexer)</td><td class="entry"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciAPIs.html#aaf185d65d1034087b77995d8490b6475">QsciAPIs</a>(QsciLexer *lexer)</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciAPIs.html#acb1aa10ea05a7ee72a0d77376153b4d2">remove</a>(const QString &amp;entry)</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciAPIs.html#a742609f12e48e63edbab2565d7df3cb9">savePrepared</a>(const QString &amp;filename=QString()) const</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciAPIs.html#ab0f824492bb0f3ca54edb4d46945a3de">updateAutoCompletionList</a>(const QStringList &amp;context, QStringList &amp;list)</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciAbstractAPIs.html#a7ef866227b05482cb32c70b44e8bdec1">~QsciAbstractAPIs</a>()</td><td class="entry"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciAPIs.html#a07bc73b7a67f8f405578992bae29528c">~QsciAPIs</a>()</td><td class="entry"><a class="el" href="classQsciAPIs.html">QsciAPIs</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
