<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_a"></a>- a -</h3><ul>
<li>add()
: <a class="el" href="classQsciAPIs.html#af46ca05571eb676d3aa65b080fb406c5">QsciAPIs</a>
</li>
<li>addEdgeColumn()
: <a class="el" href="classQsciScintilla.html#ac46502c93651ec7a6642afe5dca86ffc">QsciScintilla</a>
</li>
<li>alternateKey()
: <a class="el" href="classQsciCommand.html#ae6949756a800e31f1d279aa753060966">QsciCommand</a>
</li>
<li>annotate()
: <a class="el" href="classQsciScintilla.html#acc3981a1149b87b38f93195bb0e00f34">QsciScintilla</a>
</li>
<li>annotation()
: <a class="el" href="classQsciScintilla.html#a6a577313664af6dc63885f03e88d03af">QsciScintilla</a>
</li>
<li>annotationDisplay()
: <a class="el" href="classQsciScintilla.html#a3045ab135148ca52330ad233703a57f1">QsciScintilla</a>
</li>
<li>apiContext()
: <a class="el" href="classQsciScintilla.html#ab8bfeae44abd61659d207a86660b100c">QsciScintilla</a>
</li>
<li>apiPreparationCancelled()
: <a class="el" href="classQsciAPIs.html#aaa47506820a2596004688e241fc4cd9f">QsciAPIs</a>
</li>
<li>apiPreparationFinished()
: <a class="el" href="classQsciAPIs.html#adf779559d29fed004ec65ef560483e3c">QsciAPIs</a>
</li>
<li>apiPreparationStarted()
: <a class="el" href="classQsciAPIs.html#a8fc5db618546fcfcc5bdc46e6d062995">QsciAPIs</a>
</li>
<li>apis()
: <a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">QsciLexer</a>
</li>
<li>append()
: <a class="el" href="classQsciScintilla.html#ae7310729b1be2aa937a22036f5d95b51">QsciScintilla</a>
</li>
<li>autoCompleteFromAll()
: <a class="el" href="classQsciScintilla.html#a5f10e95e76c2b38b9d20f6f728243e65">QsciScintilla</a>
</li>
<li>autoCompleteFromAPIs()
: <a class="el" href="classQsciScintilla.html#ae89d58a67f46efad7136bead41232fd6">QsciScintilla</a>
</li>
<li>autoCompleteFromDocument()
: <a class="el" href="classQsciScintilla.html#ae4d479c640e2ea4444aa905f69495321">QsciScintilla</a>
</li>
<li>autoCompletionCaseSensitivity()
: <a class="el" href="classQsciScintilla.html#a136a17a59a3800c40619a768ffff8d7a">QsciScintilla</a>
</li>
<li>autoCompletionFillups()
: <a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">QsciLexer</a>
, <a class="el" href="classQsciLexerHTML.html#ae6ef21c59fd374d1d4893adcc1e3ee9b">QsciLexerHTML</a>
</li>
<li>autoCompletionFillupsEnabled()
: <a class="el" href="classQsciScintilla.html#a47d0cdb5df5fa6c860b4e840184fe585">QsciScintilla</a>
</li>
<li>autoCompletionReplaceWord()
: <a class="el" href="classQsciScintilla.html#a527b309bfaf28b870c15d87a5af7c005">QsciScintilla</a>
</li>
<li>autoCompletionSelected()
: <a class="el" href="classQsciAbstractAPIs.html#a90fa0f912b748b707967ccb722f04ddc">QsciAbstractAPIs</a>
, <a class="el" href="classQsciAPIs.html#adff0073d1f4ee2e0ea8b3bf234ff2dd3">QsciAPIs</a>
</li>
<li>autoCompletionShowSingle()
: <a class="el" href="classQsciScintilla.html#aff3b4e47fcbadeb0cf2556cf6ad164e0">QsciScintilla</a>
</li>
<li>autoCompletionSource()
: <a class="el" href="classQsciScintilla.html#a6c06ccce022c08674e24a96093902b49">QsciScintilla</a>
</li>
<li>autoCompletionThreshold()
: <a class="el" href="classQsciScintilla.html#a5178b3fd3cb946ffd4b2e52df9bb1483">QsciScintilla</a>
</li>
<li>autoCompletionUseSingle()
: <a class="el" href="classQsciScintilla.html#aaeb4a9e6d4e2822524c84da5318a7f1e">QsciScintilla</a>
</li>
<li>autoCompletionWordSeparators()
: <a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">QsciLexer</a>
, <a class="el" href="classQsciLexerCoffeeScript.html#a0741fad0b942deb73642be16c3159eb1">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#af0ccf94585e15b87a18f12ab9de1c977">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerD.html#a9080d0a47d2cbd972d5f2e6c737ba7fa">QsciLexerD</a>
, <a class="el" href="classQsciLexerLua.html#aff715db68554a1022792135e8edd0dba">QsciLexerLua</a>
, <a class="el" href="classQsciLexerPascal.html#aa28fa3e32d5d4a4efccdad6655fb28c8">QsciLexerPascal</a>
, <a class="el" href="classQsciLexerPerl.html#a0e4df63d7d5714b1bdb71c1975f7f99c">QsciLexerPerl</a>
, <a class="el" href="classQsciLexerPython.html#a305ec320aa2357947cbeb1608b95d840">QsciLexerPython</a>
</li>
<li>autoIndent()
: <a class="el" href="classQsciScintilla.html#a828f1b56453686ccd423e979e55fbbae">QsciScintilla</a>
</li>
<li>autoIndentStyle()
: <a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">QsciLexer</a>
</li>
<li>QsciAbstractAPIs()
: <a class="el" href="classQsciAbstractAPIs.html#a9db5ebe8adda3f58892af676f5295e3a">QsciAbstractAPIs</a>
</li>
<li>QsciAPIs()
: <a class="el" href="classQsciAPIs.html#aaf185d65d1034087b77995d8490b6475">QsciAPIs</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
