<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciLexerTCL Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">autoCompletionWordSeparators</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">blockEnd</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">blockStart</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a6d4b2db2d518117945edcbbbc4e3d26d">braceStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>bytesAsText</b>(const char *bytes, int size) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8a1e00ce63c680961063bba87de9f4bc23">Comment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8a37d5cc3f8f43e1a9457f016fb8477fc2">CommentBlock</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8ad645e0c9a459e6319aca09d344ba9fe7">CommentBox</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8a333018506e26a9e4f3c4f42aa1193c1a">CommentLine</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8a8aeb499b1256741e651ddd90fb3b0bb5">Default</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a90015597a5748d85b36cc5b263fc05cf">defaultColor</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">QsciLexer::defaultColor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a634989e93d2975d1838016ed24f3e45f">defaultEolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#a80f3f0cbd594ce9268081a76174ee0e8">defaultFont</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">QsciLexer::defaultFont</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#ad6f1adced83d4017ef5ea75ea338c117">defaultPaper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">QsciLexer::defaultPaper</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a59f517180e03fd1790c4a6de73196a70">description</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a>(bool eolfilled, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8a1f6ee7e1310318ce54cbcf9a1a50f144">ExpandKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#a71b5b892a7a30a4f57b9efa64fdf5f32">foldComments</a>() const</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a>(const QFont &amp;f, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8a0c8b6993ec2f619ed29f8797fc27e441">Identifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8aa130758e2b9502d70213979a82134045">ITCLKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#a8739852ad69fa4686f0fabd61d18b214">keywords</a>(int set) const</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8a7e064a643483e44ef2d19f7aa9e947c0">KeywordSet6</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8a0fa1debaf4eebc4dad0a531f9bd5074a">KeywordSet7</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8a678e5cdd4369161e2974c1fca5ec0756">KeywordSet8</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8a04935c9218b079cf604ffb8c453d0d79">KeywordSet9</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a4a13fa4667146e0dca9d8c15255280a9">language</a>() const</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#a15ec40b8e6b208521e08d44400eb56f8">lexer</a>() const</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8ae958411da961413eaf269dc8cfab30eb">Modifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8a9dd8ccd07ebf3793c182f1e2026ec471">Number</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8a88aa37826c2111e43c2e64d175b631de">Operator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a>(const char *prop, const char *val)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a6a108be4899959ffcb262f59de538964">QsciLexerTCL</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8addadb2b0cf0671682752e80ba1650cce">QuotedKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8a4212e6615aa13a138d3d41d4f82a35ec">QuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#a1b1d726f87795c97839acca28d06dc6e">readProperties</a>(QSettings &amp;qs, const QString &amp;prefix)</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#ad331ec23d27ba397d2095ba92cefaecd">refreshProperties</a>()</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a>(QsciAbstractAPIs *apis)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a>(int autoindentstyle)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a>(const QFont &amp;f)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>setEditor</b>(QsciScintilla *editor) (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a>(bool eoffill, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#abceb6f3cf78367b7bc370265d7776bf1">setFoldComments</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a>(const QFont &amp;f, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8a56519805233273c84151d68bf400b2d9">Substitution</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8aa7072662f6d21b6077eaec2ed2ed6836">SubstitutionBrace</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8a573bd1485068b767dda643d3201fb5a1">TCLKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>textAsBytes</b>(const QString &amp;text) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8a1bf59ca4e6d1204ef32722d09890dff0">TkCommand</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8ace310f4d3487840abe7a4c2a4a0a50b8">TkKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aace68e3dbcef9da1b031fb9cfd843c57">wordCharacters</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerTCL.html#a65a8d0928d9f04584972410a5af82888">writeProperties</a>(QSettings &amp;qs, const QString &amp;prefix) const</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerTCL.html#aa4e0b16ffd568f44be50375e0572011c">~QsciLexerTCL</a>()</td><td class="entry"><a class="el" href="classQsciLexerTCL.html">QsciLexerTCL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
