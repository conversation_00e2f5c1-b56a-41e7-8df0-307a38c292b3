<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciPrinter Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciPrinter.html">QsciPrinter</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classQsciPrinter.html#a420e136529a8d49551eb8af0f5cdce03">formatPage</a>(QPainter &amp;painter, bool drawing, QRect &amp;area, int pagenr)</td><td class="entry"><a class="el" href="classQsciPrinter.html">QsciPrinter</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciPrinter.html#aafc924b9d8d494541b89ac8d461b4300">magnification</a>() const</td><td class="entry"><a class="el" href="classQsciPrinter.html">QsciPrinter</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciPrinter.html#a0bd255795c503a091ddc76d3564e2aac">printRange</a>(QsciScintillaBase *qsb, QPainter &amp;painter, int from=-1, int to=-1)</td><td class="entry"><a class="el" href="classQsciPrinter.html">QsciPrinter</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciPrinter.html#aae304336a4a8d4c2e332744ceeba1393">printRange</a>(QsciScintillaBase *qsb, int from=-1, int to=-1)</td><td class="entry"><a class="el" href="classQsciPrinter.html">QsciPrinter</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciPrinter.html#aaa54abecf0defffdfda80f95af6febf9">QsciPrinter</a>(PrinterMode mode=ScreenResolution)</td><td class="entry"><a class="el" href="classQsciPrinter.html">QsciPrinter</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciPrinter.html#ad66724c8a5e5e202998bd6533fef61be">setMagnification</a>(int magnification)</td><td class="entry"><a class="el" href="classQsciPrinter.html">QsciPrinter</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciPrinter.html#aa95827e3bd2c3c0e658afe55fa12476e">setWrapMode</a>(QsciScintilla::WrapMode wmode)</td><td class="entry"><a class="el" href="classQsciPrinter.html">QsciPrinter</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciPrinter.html#ad67d67c266263dd2dbfe940b4ad98584">wrapMode</a>() const</td><td class="entry"><a class="el" href="classQsciPrinter.html">QsciPrinter</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciPrinter.html#a9c7747919e355a885d6ebb4b0d0dc619">~QsciPrinter</a>()</td><td class="entry"><a class="el" href="classQsciPrinter.html">QsciPrinter</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
