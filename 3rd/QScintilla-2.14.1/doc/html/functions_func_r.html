<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_r"></a>- r -</h3><ul>
<li>read()
: <a class="el" href="classQsciScintilla.html#a89e46329c110449b2e7334ccf623ceed">QsciScintilla</a>
</li>
<li>readProperties()
: <a class="el" href="classQsciLexer.html#ad472b16506a4cbc19634f07aa90f1ea6">QsciLexer</a>
, <a class="el" href="classQsciLexerAsm.html#abcd26a3075ae82103e76332fe20a4ed1">QsciLexerAsm</a>
, <a class="el" href="classQsciLexerAVS.html#ad65ebfab947de5d6e318238f8a0048e4">QsciLexerAVS</a>
, <a class="el" href="classQsciLexerBash.html#aae0cfbb2dbfd2a833a16630c9cf2e36e">QsciLexerBash</a>
, <a class="el" href="classQsciLexerCMake.html#a4578cacfbe802ab993fc07ddeaef3297">QsciLexerCMake</a>
, <a class="el" href="classQsciLexerCoffeeScript.html#ae15b25b5d6705a850f6c93ee1013bea7">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#aa37ea54c5e39721b866c25b0e0335591">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerCSS.html#a7bfdaea964c9e2c51568f63f379b6108">QsciLexerCSS</a>
, <a class="el" href="classQsciLexerD.html#abb94e0b0257a50dbde9b0ddbcfeb69d2">QsciLexerD</a>
, <a class="el" href="classQsciLexerFortran77.html#a08b8ae54fae5b280a3864d5696fe009e">QsciLexerFortran77</a>
, <a class="el" href="classQsciLexerHTML.html#ab9ae7a11b4c9ba6f62d795dce8d6fab8">QsciLexerHTML</a>
, <a class="el" href="classQsciLexerJSON.html#aba9c88201491763d75a8716d118a4079">QsciLexerJSON</a>
, <a class="el" href="classQsciLexerLua.html#a928315606c0bd973c59e0b6d9641c3cd">QsciLexerLua</a>
, <a class="el" href="classQsciLexerPascal.html#a2a2beba3b365e2e0e1f21109079f0ffd">QsciLexerPascal</a>
, <a class="el" href="classQsciLexerPerl.html#a47884fcfd8d2b0ab7b8d277cb0325c17">QsciLexerPerl</a>
, <a class="el" href="classQsciLexerPO.html#a8403f1e2f5ea0c5d67c32dd6053317c5">QsciLexerPO</a>
, <a class="el" href="classQsciLexerPostScript.html#a87168d5b174ba3a9b969ef689f67b355">QsciLexerPostScript</a>
, <a class="el" href="classQsciLexerPOV.html#a5a599e7d97b164fec1ee3c21ba167e80">QsciLexerPOV</a>
, <a class="el" href="classQsciLexerProperties.html#a4119053764ba32a9975ad7eeb8f0f067">QsciLexerProperties</a>
, <a class="el" href="classQsciLexerPython.html#a1b8f36843f4abe6ec3ee75205b5b0111">QsciLexerPython</a>
, <a class="el" href="classQsciLexerRuby.html#afa0b9ecea2700420820e4e9b705cb784">QsciLexerRuby</a>
, <a class="el" href="classQsciLexerSQL.html#a377b83523f800cc4598126417d80f74c">QsciLexerSQL</a>
, <a class="el" href="classQsciLexerTCL.html#a1b1d726f87795c97839acca28d06dc6e">QsciLexerTCL</a>
, <a class="el" href="classQsciLexerTeX.html#a68e2eaca494e93937f896bd60b86429c">QsciLexerTeX</a>
, <a class="el" href="classQsciLexerVerilog.html#aa1bd0effe3ed23e2bb3334b778efb74a">QsciLexerVerilog</a>
, <a class="el" href="classQsciLexerVHDL.html#a46a01d03d516e909c8696fa3f9910c1f">QsciLexerVHDL</a>
, <a class="el" href="classQsciLexerXML.html#a2acbf99b93c18d9a9f922c9e2894bf4f">QsciLexerXML</a>
, <a class="el" href="classQsciLexerYAML.html#a35d4260e9c1a68073a6b4f625c846c11">QsciLexerYAML</a>
</li>
<li>readSettings()
: <a class="el" href="classQsciCommandSet.html#a44fd78a640f59309862d868d04f34e49">QsciCommandSet</a>
, <a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">QsciLexer</a>
</li>
<li>recolor()
: <a class="el" href="classQsciScintilla.html#a07fe5007913f0001a473d92f4c3fdbe6">QsciScintilla</a>
</li>
<li>redo()
: <a class="el" href="classQsciScintilla.html#a62220018d9e9295cde64041246cfb3c4">QsciScintilla</a>
</li>
<li>refresh()
: <a class="el" href="classQsciStyle.html#aa480e57bbdd83b8164129f875bd48976">QsciStyle</a>
</li>
<li>refreshProperties()
: <a class="el" href="classQsciLexer.html#ae508c3ab4ce1f338dfff3ddf5ee7e34c">QsciLexer</a>
, <a class="el" href="classQsciLexerAsm.html#a25cb4af3f28543dcd6125a79c0520447">QsciLexerAsm</a>
, <a class="el" href="classQsciLexerAVS.html#af5a3f47c4f0be631303cabd42d904c3e">QsciLexerAVS</a>
, <a class="el" href="classQsciLexerBash.html#aad047f411c36c262305ffcce5015944f">QsciLexerBash</a>
, <a class="el" href="classQsciLexerCMake.html#a7cc73bba065690f08e2b6b8e8c00d5d3">QsciLexerCMake</a>
, <a class="el" href="classQsciLexerCoffeeScript.html#aefae6df689f1d3dad66d1f2fc141cc39">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#a58506e1c965a181c9202376e0ba85c30">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerCSS.html#a78f4690fa92e02c8511074a334c06096">QsciLexerCSS</a>
, <a class="el" href="classQsciLexerD.html#a3df48961344c5133ad595a555bbb8e55">QsciLexerD</a>
, <a class="el" href="classQsciLexerFortran77.html#a2033202288867ce63c4e93bc45dc55e3">QsciLexerFortran77</a>
, <a class="el" href="classQsciLexerHTML.html#a7c73d608fd96b019e70ebf448de23357">QsciLexerHTML</a>
, <a class="el" href="classQsciLexerJSON.html#acd0614489de2d2c9f69379a574f1d5eb">QsciLexerJSON</a>
, <a class="el" href="classQsciLexerLua.html#a628efb828569208d6219a88f1fc6a1a7">QsciLexerLua</a>
, <a class="el" href="classQsciLexerPascal.html#a92cb96a2f9d373ed5a91546c42ec0905">QsciLexerPascal</a>
, <a class="el" href="classQsciLexerPerl.html#ac9868e2d0efbf3602a22d8bdac12a119">QsciLexerPerl</a>
, <a class="el" href="classQsciLexerPO.html#a17895e48d655d41d80e4fb4672c2fd72">QsciLexerPO</a>
, <a class="el" href="classQsciLexerPostScript.html#a0f1e5402dce043de42ded75f5826588f">QsciLexerPostScript</a>
, <a class="el" href="classQsciLexerPOV.html#a4864bf9360ed4748b9ca7a1d5e34e7d8">QsciLexerPOV</a>
, <a class="el" href="classQsciLexerProperties.html#a638b892c566301f0efe779c58516cbc0">QsciLexerProperties</a>
, <a class="el" href="classQsciLexerPython.html#abed099316dd95a6289c76d151a37c264">QsciLexerPython</a>
, <a class="el" href="classQsciLexerRuby.html#abf07311e229b5ec1370dd8a57873c1b6">QsciLexerRuby</a>
, <a class="el" href="classQsciLexerSQL.html#a9d05744ee6d4c653a7e3976d9f71df23">QsciLexerSQL</a>
, <a class="el" href="classQsciLexerTCL.html#ad331ec23d27ba397d2095ba92cefaecd">QsciLexerTCL</a>
, <a class="el" href="classQsciLexerTeX.html#acd80380b4727bd94148f5a0ff479742e">QsciLexerTeX</a>
, <a class="el" href="classQsciLexerVerilog.html#ad476092b3970fe44068dd023f8becc96">QsciLexerVerilog</a>
, <a class="el" href="classQsciLexerVHDL.html#aa60e141b7b1a7aac51d79ad2c27c4c93">QsciLexerVHDL</a>
, <a class="el" href="classQsciLexerXML.html#a29937d422c25f17612c57e16a7bddaf1">QsciLexerXML</a>
, <a class="el" href="classQsciLexerYAML.html#ac263eb1fcaeaad44b23c2d990bad1bc1">QsciLexerYAML</a>
</li>
<li>registerImage()
: <a class="el" href="classQsciScintilla.html#a1d35bee9f234dbde7066a68b924edeed">QsciScintilla</a>
</li>
<li>remove()
: <a class="el" href="classQsciAPIs.html#acb1aa10ea05a7ee72a0d77376153b4d2">QsciAPIs</a>
</li>
<li>removeSelectedText()
: <a class="el" href="classQsciScintilla.html#aef584b6c5b89736ad311ad20aa32269d">QsciScintilla</a>
</li>
<li>replace()
: <a class="el" href="classQsciScintilla.html#ad8a7afc4f25e04e805b7e291d5df1d35">QsciScintilla</a>
</li>
<li>replaceHorizontalScrollBar()
: <a class="el" href="classQsciScintillaBase.html#a1efa4394b588d27fd2a3bd40163a2342">QsciScintillaBase</a>
</li>
<li>replaceSelectedText()
: <a class="el" href="classQsciScintilla.html#a48f53865418cf2ebd5126d2ee5e9d1dd">QsciScintilla</a>
</li>
<li>replaceVerticalScrollBar()
: <a class="el" href="classQsciScintillaBase.html#a900e3a0287e262fe65c51162e562fc5d">QsciScintillaBase</a>
</li>
<li>resetFoldMarginColors()
: <a class="el" href="classQsciScintilla.html#ae1d3703631c9113a4309da17332ca180">QsciScintilla</a>
</li>
<li>resetHotspotBackgroundColor()
: <a class="el" href="classQsciScintilla.html#a366393c6bf0fd7ef5ce87b7682e6c6ae">QsciScintilla</a>
</li>
<li>resetHotspotForegroundColor()
: <a class="el" href="classQsciScintilla.html#a3fd2a1bbc409d1bbca44efc7976b808c">QsciScintilla</a>
</li>
<li>resetMatchedBraceIndicator()
: <a class="el" href="classQsciScintilla.html#ae308bac1c3567d835742e02f1bc35a6c">QsciScintilla</a>
</li>
<li>resetSelectionBackgroundColor()
: <a class="el" href="classQsciScintilla.html#ae8b040ae88e74aef9a38cdb4ce24295a">QsciScintilla</a>
</li>
<li>resetSelectionForegroundColor()
: <a class="el" href="classQsciScintilla.html#a9ce32df9e150ef76a24c50af6b09e966">QsciScintilla</a>
</li>
<li>resetUnmatchedBraceIndicator()
: <a class="el" href="classQsciScintilla.html#a48c91d4dd29c84bff4ee20962372ca23">QsciScintilla</a>
</li>
<li>resizeEvent()
: <a class="el" href="classQsciScintillaBase.html#a6d0427b93e05876c9a2b541eae08ddab">QsciScintillaBase</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
