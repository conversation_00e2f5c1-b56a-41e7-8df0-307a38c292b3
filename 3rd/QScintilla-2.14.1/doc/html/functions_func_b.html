<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_b"></a>- b -</h3><ul>
<li>backslashEscapes()
: <a class="el" href="classQsciLexerSQL.html#abf07dc83c19a3925e3cb977bf883b04c">QsciLexerSQL</a>
</li>
<li>backspaceUnindents()
: <a class="el" href="classQsciScintilla.html#a9122d4ac5b0b3eca120cf18ae7275bb1">QsciScintilla</a>
</li>
<li>beginUndoAction()
: <a class="el" href="classQsciScintilla.html#ac0f785ba228153e9df6df76ca036c030">QsciScintilla</a>
</li>
<li>blockEnd()
: <a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">QsciLexer</a>
, <a class="el" href="classQsciLexerCoffeeScript.html#a1796c98b07ec6cfc3d5953c225cc1f37">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#a2263531e4445463f1d75fdfd54102404">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerCSS.html#ada48a387b3e1414927bebe2415de75f8">QsciLexerCSS</a>
, <a class="el" href="classQsciLexerD.html#af9f73f93dd57019e3335011528ad6aed">QsciLexerD</a>
, <a class="el" href="classQsciLexerPascal.html#a9914377426e5e464f6d93ce2b64423a0">QsciLexerPascal</a>
, <a class="el" href="classQsciLexerPerl.html#a42133f1b4127c78674f89e3209236a18">QsciLexerPerl</a>
, <a class="el" href="classQsciLexerRuby.html#aabf79a666eb40a912dfb7136d79f80e6">QsciLexerRuby</a>
</li>
<li>blockLookback()
: <a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">QsciLexer</a>
, <a class="el" href="classQsciLexerPython.html#afe42ac5a09816340d4bec920b523aed6">QsciLexerPython</a>
</li>
<li>blockStart()
: <a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">QsciLexer</a>
, <a class="el" href="classQsciLexerCoffeeScript.html#a293f0a5c39990ec1db6de249dc618901">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#a79d8b2101ef7b1aef1e7e01557090d6f">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerCSS.html#aae249ec529d5f7de5fa238de9208058d">QsciLexerCSS</a>
, <a class="el" href="classQsciLexerD.html#a7ea79082a0d55e78cd3a60f1f05af6d9">QsciLexerD</a>
, <a class="el" href="classQsciLexerLua.html#a157c462625b4826a5d7fb9eec42cfc78">QsciLexerLua</a>
, <a class="el" href="classQsciLexerPascal.html#a68d8b422b0d733592cc896086ca23652">QsciLexerPascal</a>
, <a class="el" href="classQsciLexerPerl.html#ae33c3f0e337cfe173c61ea86c5cd3591">QsciLexerPerl</a>
, <a class="el" href="classQsciLexerPython.html#adc66ee4b78453d245ac1b4dff45490f4">QsciLexerPython</a>
, <a class="el" href="classQsciLexerRuby.html#a7ecc2269f4b7a4956b7209082032245d">QsciLexerRuby</a>
</li>
<li>blockStartKeyword()
: <a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">QsciLexer</a>
, <a class="el" href="classQsciLexerCoffeeScript.html#a497144db9b43beba78cd405a795e08ac">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#a2cfcfea76c396c0b7b82fc41437ff16f">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerD.html#ae4490715b80237feaa25ad92d2fb6313">QsciLexerD</a>
, <a class="el" href="classQsciLexerPascal.html#abe045873399199ba05d26e94c0e28aae">QsciLexerPascal</a>
, <a class="el" href="classQsciLexerRuby.html#a47eb0ab494fe54b5518b4c8bdcd2968e">QsciLexerRuby</a>
</li>
<li>braceMatching()
: <a class="el" href="classQsciScintilla.html#aa869897ad955e8a42c5568be590c529b">QsciScintilla</a>
</li>
<li>braceStyle()
: <a class="el" href="classQsciLexer.html#affe136114d62180e9a14caa81f2b7fd5">QsciLexer</a>
, <a class="el" href="classQsciLexerAVS.html#a9023ef1aa48fd622ecac97a419cb3afe">QsciLexerAVS</a>
, <a class="el" href="classQsciLexerBash.html#a96aca8cf94d490d3c7c11e71d823a9ee">QsciLexerBash</a>
, <a class="el" href="classQsciLexerCoffeeScript.html#add9b1d85d9da1c250f570482cd47eb39">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#a509df9a20a1841de287849d6738ec3dd">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerD.html#a24c82f4e0483ba0c13b8bf046b8c00b9">QsciLexerD</a>
, <a class="el" href="classQsciLexerFortran77.html#a7df3e986e8039ee6028b39d0df1741d1">QsciLexerFortran77</a>
, <a class="el" href="classQsciLexerLua.html#a30570eca6c21ea302b1c6c0bd733dc14">QsciLexerLua</a>
, <a class="el" href="classQsciLexerPascal.html#a4bd5b007424a8e88db37a326c0f154b5">QsciLexerPascal</a>
, <a class="el" href="classQsciLexerPerl.html#a3e90db838034f7404e65b2e284403604">QsciLexerPerl</a>
, <a class="el" href="classQsciLexerPostScript.html#a05f377a9017cf5f5d51deae3f1f83445">QsciLexerPostScript</a>
, <a class="el" href="classQsciLexerPOV.html#adfb83ee7ea262a33f775d1e53cf38bec">QsciLexerPOV</a>
, <a class="el" href="classQsciLexerPython.html#ab30fa749a26490888fe18f2fcea47b02">QsciLexerPython</a>
, <a class="el" href="classQsciLexerRuby.html#ae7a6d23e6e8748210198b4fee3932144">QsciLexerRuby</a>
, <a class="el" href="classQsciLexerSpice.html#aee09ddca3fd840b79ae954f6883fa581">QsciLexerSpice</a>
, <a class="el" href="classQsciLexerSQL.html#ac97e486c8c1f2233c0b35e744ef5a393">QsciLexerSQL</a>
, <a class="el" href="classQsciLexerTCL.html#a6d4b2db2d518117945edcbbbc4e3d26d">QsciLexerTCL</a>
, <a class="el" href="classQsciLexerVerilog.html#a003413f4436ff46553e10db632496288">QsciLexerVerilog</a>
, <a class="el" href="classQsciLexerVHDL.html#aad362dd8a212974c01e61d12c8991b7f">QsciLexerVHDL</a>
</li>
<li>bytes()
: <a class="el" href="classQsciScintilla.html#a9c1818383be531c3b04cd6848145d63b">QsciScintilla</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
