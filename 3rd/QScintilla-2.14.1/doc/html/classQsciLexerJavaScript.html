<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciLexerJavaScript Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classQsciLexerJavaScript-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciLexerJavaScript Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscilexerjavascript.h&gt;</code></p>

<p>Inherits <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a94cbc22361a55fe0681ad7fe5425dfb5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerJavaScript.html#a94cbc22361a55fe0681ad7fe5425dfb5">QsciLexerJavaScript</a> (QObject *parent=0)</td></tr>
<tr class="separator:a94cbc22361a55fe0681ad7fe5425dfb5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a99f91287ee998375f84c7a2467b0ff"><td class="memItemLeft" align="right" valign="top"><a id="a8a99f91287ee998375f84c7a2467b0ff"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerJavaScript.html#a8a99f91287ee998375f84c7a2467b0ff">~QsciLexerJavaScript</a> ()</td></tr>
<tr class="separator:a8a99f91287ee998375f84c7a2467b0ff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa7bd1f345699cc97fac25cf29ae98a4e"><td class="memItemLeft" align="right" valign="top"><a id="aa7bd1f345699cc97fac25cf29ae98a4e"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerJavaScript.html#aa7bd1f345699cc97fac25cf29ae98a4e">language</a> () const</td></tr>
<tr class="separator:aa7bd1f345699cc97fac25cf29ae98a4e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66b627130d76db15263b7502ec5d475c"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerJavaScript.html#a66b627130d76db15263b7502ec5d475c">defaultColor</a> (int style) const</td></tr>
<tr class="separator:a66b627130d76db15263b7502ec5d475c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a814917aafe1fef03ec20571e91bb4571"><td class="memItemLeft" align="right" valign="top"><a id="a814917aafe1fef03ec20571e91bb4571"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerJavaScript.html#a814917aafe1fef03ec20571e91bb4571">defaultEolFill</a> (int style) const</td></tr>
<tr class="separator:a814917aafe1fef03ec20571e91bb4571"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5245587f4db1c40ad90898a7712094ed"><td class="memItemLeft" align="right" valign="top"><a id="a5245587f4db1c40ad90898a7712094ed"></a>
QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerJavaScript.html#a5245587f4db1c40ad90898a7712094ed">defaultFont</a> (int style) const</td></tr>
<tr class="separator:a5245587f4db1c40ad90898a7712094ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af4b249f267973d29380b758a25b42e46"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerJavaScript.html#af4b249f267973d29380b758a25b42e46">defaultPaper</a> (int style) const</td></tr>
<tr class="separator:af4b249f267973d29380b758a25b42e46"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af00e1d05374302fd4d2e2eeec1a829ee"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerJavaScript.html#af00e1d05374302fd4d2e2eeec1a829ee">keywords</a> (int set) const</td></tr>
<tr class="separator:af00e1d05374302fd4d2e2eeec1a829ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc88c53a2cfe6dd61e059fad1e8f3539"><td class="memItemLeft" align="right" valign="top">QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerJavaScript.html#abc88c53a2cfe6dd61e059fad1e8f3539">description</a> (int style) const</td></tr>
<tr class="separator:abc88c53a2cfe6dd61e059fad1e8f3539"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classQsciLexerCPP"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classQsciLexerCPP')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td></tr>
<tr class="memitem:ae1451be7e7c6a57f28f361c72eb68a5f inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#ae1451be7e7c6a57f28f361c72eb68a5f">QsciLexerCPP</a> (QObject *parent=0, bool caseInsensitiveKeywords=false)</td></tr>
<tr class="separator:ae1451be7e7c6a57f28f361c72eb68a5f inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab9babc165bacf53b73abfb2d5d1aadad inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top"><a id="ab9babc165bacf53b73abfb2d5d1aadad"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#ab9babc165bacf53b73abfb2d5d1aadad">~QsciLexerCPP</a> ()</td></tr>
<tr class="separator:ab9babc165bacf53b73abfb2d5d1aadad inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3f7076535f370759450ec1243088c7f1 inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a3f7076535f370759450ec1243088c7f1">lexer</a> () const</td></tr>
<tr class="separator:a3f7076535f370759450ec1243088c7f1 inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af0ccf94585e15b87a18f12ab9de1c977 inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">QStringList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#af0ccf94585e15b87a18f12ab9de1c977">autoCompletionWordSeparators</a> () const</td></tr>
<tr class="separator:af0ccf94585e15b87a18f12ab9de1c977 inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2263531e4445463f1d75fdfd54102404 inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a2263531e4445463f1d75fdfd54102404">blockEnd</a> (int *style=0) const</td></tr>
<tr class="separator:a2263531e4445463f1d75fdfd54102404 inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79d8b2101ef7b1aef1e7e01557090d6f inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a79d8b2101ef7b1aef1e7e01557090d6f">blockStart</a> (int *style=0) const</td></tr>
<tr class="separator:a79d8b2101ef7b1aef1e7e01557090d6f inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2cfcfea76c396c0b7b82fc41437ff16f inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a2cfcfea76c396c0b7b82fc41437ff16f">blockStartKeyword</a> (int *style=0) const</td></tr>
<tr class="separator:a2cfcfea76c396c0b7b82fc41437ff16f inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a509df9a20a1841de287849d6738ec3dd inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top"><a id="a509df9a20a1841de287849d6738ec3dd"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a509df9a20a1841de287849d6738ec3dd">braceStyle</a> () const</td></tr>
<tr class="separator:a509df9a20a1841de287849d6738ec3dd inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a822ca7489c4655f26bc72ed127285d8a inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top"><a id="a822ca7489c4655f26bc72ed127285d8a"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a822ca7489c4655f26bc72ed127285d8a">wordCharacters</a> () const</td></tr>
<tr class="separator:a822ca7489c4655f26bc72ed127285d8a inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a58506e1c965a181c9202376e0ba85c30 inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a58506e1c965a181c9202376e0ba85c30">refreshProperties</a> ()</td></tr>
<tr class="separator:a58506e1c965a181c9202376e0ba85c30 inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad7b42963ca382afb23eb000b727de12 inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#aad7b42963ca382afb23eb000b727de12">foldAtElse</a> () const</td></tr>
<tr class="separator:aad7b42963ca382afb23eb000b727de12 inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc253f08156cde45b331c5a7ed07cfd7 inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#adc253f08156cde45b331c5a7ed07cfd7">foldComments</a> () const</td></tr>
<tr class="separator:adc253f08156cde45b331c5a7ed07cfd7 inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad0939852605ee45ce62f70647d47147b inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#ad0939852605ee45ce62f70647d47147b">foldCompact</a> () const</td></tr>
<tr class="separator:ad0939852605ee45ce62f70647d47147b inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3c93f14b36897ecb3f902b5e5de91ad6 inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a3c93f14b36897ecb3f902b5e5de91ad6">foldPreprocessor</a> () const</td></tr>
<tr class="separator:a3c93f14b36897ecb3f902b5e5de91ad6 inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac6f508a57750605ec3b9688408b092b2 inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#ac6f508a57750605ec3b9688408b092b2">stylePreprocessor</a> () const</td></tr>
<tr class="separator:ac6f508a57750605ec3b9688408b092b2 inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a06315a18051184926fe21459fc75b4cc inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a06315a18051184926fe21459fc75b4cc">setDollarsAllowed</a> (bool allowed)</td></tr>
<tr class="separator:a06315a18051184926fe21459fc75b4cc inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa20e183e0b38b5076aa9e883c5283791 inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#aa20e183e0b38b5076aa9e883c5283791">dollarsAllowed</a> () const</td></tr>
<tr class="separator:aa20e183e0b38b5076aa9e883c5283791 inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2ea8bd8758e10d72832dbf3642b06fb2 inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a2ea8bd8758e10d72832dbf3642b06fb2">setHighlightTripleQuotedStrings</a> (bool enabled)</td></tr>
<tr class="separator:a2ea8bd8758e10d72832dbf3642b06fb2 inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a23c6488e2416d54f6a4ec84015d860ec inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a23c6488e2416d54f6a4ec84015d860ec">highlightTripleQuotedStrings</a> () const</td></tr>
<tr class="separator:a23c6488e2416d54f6a4ec84015d860ec inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad0d9356583118309e6c3991e96a67ffe inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#ad0d9356583118309e6c3991e96a67ffe">setHighlightHashQuotedStrings</a> (bool enabled)</td></tr>
<tr class="separator:ad0d9356583118309e6c3991e96a67ffe inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab5f782645ff1d3a2d7ac371cbd9f2f5d inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#ab5f782645ff1d3a2d7ac371cbd9f2f5d">highlightHashQuotedStrings</a> () const</td></tr>
<tr class="separator:ab5f782645ff1d3a2d7ac371cbd9f2f5d inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa023c95fbbecbbbf7046c92d6fcfdce5 inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#aa023c95fbbecbbbf7046c92d6fcfdce5">setHighlightBackQuotedStrings</a> (bool enabled)</td></tr>
<tr class="separator:aa023c95fbbecbbbf7046c92d6fcfdce5 inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a015d6501ee4cca33a00036174529c161 inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a015d6501ee4cca33a00036174529c161">highlightBackQuotedStrings</a> () const</td></tr>
<tr class="separator:a015d6501ee4cca33a00036174529c161 inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6d6a21ea44e2ee9676aa27178021b06a inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a6d6a21ea44e2ee9676aa27178021b06a">setHighlightEscapeSequences</a> (bool enabled)</td></tr>
<tr class="separator:a6d6a21ea44e2ee9676aa27178021b06a inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aea4d8707f6e32c1fbf989504d12d9eaa inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#aea4d8707f6e32c1fbf989504d12d9eaa">highlightEscapeSequences</a> () const</td></tr>
<tr class="separator:aea4d8707f6e32c1fbf989504d12d9eaa inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a015dce05877d292d399fb207e79632cf inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a015dce05877d292d399fb207e79632cf">setVerbatimStringEscapeSequencesAllowed</a> (bool allowed)</td></tr>
<tr class="separator:a015dce05877d292d399fb207e79632cf inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a20439ec93f1af6b8227bdcd48a6070ec inherit pub_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a20439ec93f1af6b8227bdcd48a6070ec">verbatimStringEscapeSequencesAllowed</a> () const</td></tr>
<tr class="separator:a20439ec93f1af6b8227bdcd48a6070ec inherit pub_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a> (QObject *parent=0)</td></tr>
<tr class="separator:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="af6cc5bb9d9421d806e9941d018030068"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a> ()</td></tr>
<tr class="separator:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a> () const</td></tr>
<tr class="separator:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a> () const</td></tr>
<tr class="separator:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a6504a6fff35af16fbfd97889048db2a5"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a> () const</td></tr>
<tr class="separator:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a> ()</td></tr>
<tr class="separator:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a> () const</td></tr>
<tr class="separator:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="afccca7eb1aed463f89ac442d99135839"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a> () const</td></tr>
<tr class="separator:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a> (int style) const</td></tr>
<tr class="separator:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a> (int style) const</td></tr>
<tr class="separator:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a> (int style) const</td></tr>
<tr class="separator:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="aff4735542e937c5e35ecb2eb82e8f875"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a> () const</td></tr>
<tr class="separator:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a> () const</td></tr>
<tr class="separator:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a> (int style) const</td></tr>
<tr class="separator:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">defaultColor</a> () const</td></tr>
<tr class="separator:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">defaultFont</a> () const</td></tr>
<tr class="separator:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">defaultPaper</a> () const</td></tr>
<tr class="separator:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciScintilla.html">QsciScintilla</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a> () const</td></tr>
<tr class="separator:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a> (<a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *<a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>)</td></tr>
<tr class="separator:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a> (const QFont &amp;f)</td></tr>
<tr class="separator:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a1e81186b1f8f8bc2a4901a42cbca568a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>setEditor</b> (<a class="el" href="classQsciScintilla.html">QsciScintilla</a> *<a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>)</td></tr>
<tr class="separator:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td></tr>
<tr class="separator:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a> () const</td></tr>
<tr class="separator:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td></tr>
<tr class="separator:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_types_classQsciLexerCPP"><td colspan="2" onclick="javascript:toggleInherit('pub_types_classQsciLexerCPP')"><img src="closed.png" alt="-"/>&#160;Public Types inherited from <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td></tr>
<tr class="memitem:a30c13b0ea8b55b3204ea4e9f49a313b1 inherit pub_types_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1aaf3761b6b64a02e306095a77c6e44d22">Default</a> = 0, 
<b>InactiveDefault</b> = Default + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1ab05c80130359b9586979df7f9a85d3fe">Comment</a> = 1, 
<br />
&#160;&#160;<b>InactiveComment</b> = Comment + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1af7a31515ec66490642ab83b9fedb8a78">CommentLine</a> = 2, 
<b>InactiveCommentLine</b> = CommentLine + 64, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a12cc5d18b03e47a08bd19098be35631b">CommentDoc</a> = 3, 
<b>InactiveCommentDoc</b> = CommentDoc + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a3f7d7b7c70a53fe91b336ff31c59e195">Number</a> = 4, 
<br />
&#160;&#160;<b>InactiveNumber</b> = Number + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a2495558f63baf5987a97cf2dceddbfc7">Keyword</a> = 5, 
<b>InactiveKeyword</b> = Keyword + 64, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a9956498543ca425e9772a8d11e7555b5">DoubleQuotedString</a> = 6, 
<b>InactiveDoubleQuotedString</b> = DoubleQuotedString + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1ad7c37e1eaac5103b567dd7f677fbd5be">SingleQuotedString</a> = 7, 
<br />
&#160;&#160;<b>InactiveSingleQuotedString</b> = SingleQuotedString + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a3ce80212372e40f2ed903c52297f48c7">UUID</a> = 8, 
<b>InactiveUUID</b> = UUID + 64, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a16c747644d986c230126c5420de1497e">PreProcessor</a> = 9, 
<b>InactivePreProcessor</b> = PreProcessor + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a4007ebc2021d70cf1ad6e9c6c85aba4e">Operator</a> = 10, 
<br />
&#160;&#160;<b>InactiveOperator</b> = Operator + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a162f877d72b7a405250d3a931660080e">Identifier</a> = 11, 
<b>InactiveIdentifier</b> = Identifier + 64, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a7aae8e724d5fcbbca68f3e7f97460721">UnclosedString</a> = 12, 
<b>InactiveUnclosedString</b> = UnclosedString + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1afb3585e07f994345f2c21f43f7e2ec1a">VerbatimString</a> = 13, 
<br />
&#160;&#160;<b>InactiveVerbatimString</b> = VerbatimString + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1ac26a3735f09aa6702cfbbb9fac56d6f5">Regex</a> = 14, 
<b>InactiveRegex</b> = Regex + 64, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a659ebab287e989f11cf905532c1ccddf">CommentLineDoc</a> = 15, 
<b>InactiveCommentLineDoc</b> = CommentLineDoc + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1ae9944f1484321b3261c8749ccfadbe2d">KeywordSet2</a> = 16, 
<br />
&#160;&#160;<b>InactiveKeywordSet2</b> = KeywordSet2 + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1ac640cd198228b554ec3d0b60e00d91bd">CommentDocKeyword</a> = 17, 
<b>InactiveCommentDocKeyword</b> = CommentDocKeyword + 64, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a52db9ffb3d81b68562da67cbc70d3388">CommentDocKeywordError</a> = 18, 
<b>InactiveCommentDocKeywordError</b> = CommentDocKeywordError + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a713977fdf2b7b8b59c7e9d23004090dc">GlobalClass</a> = 19, 
<br />
&#160;&#160;<b>InactiveGlobalClass</b> = GlobalClass + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a12c1673479aaf32d03b5a2fde6f032a9">RawString</a> = 20, 
<b>InactiveRawString</b> = RawString + 64, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a68d65b20c3cd1c04f46914904bc9277c">TripleQuotedVerbatimString</a> = 21, 
<b>InactiveTripleQuotedVerbatimString</b> = TripleQuotedVerbatimString + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a1b8ef1921a218a2db1d6d7d65ac3242c">HashQuotedString</a> = 22, 
<br />
&#160;&#160;<b>InactiveHashQuotedString</b> = HashQuotedString + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a95c728e17fdb37a45ba20d09ee9eda9c">PreProcessorComment</a> = 23, 
<b>InactivePreProcessorComment</b> = PreProcessorComment + 64, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a5b319d25cf7a161e08b0810a0d2b8470">PreProcessorCommentLineDoc</a> = 24, 
<b>InactivePreProcessorCommentLineDoc</b> = PreProcessorCommentLineDoc + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a3043c7ad432f1e77406554dcab6f9e0f">UserLiteral</a> = 25, 
<br />
&#160;&#160;<b>InactiveUserLiteral</b> = UserLiteral + 64, 
<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a258224273023ab8c9504dd8a8efcad6c">TaskMarker</a> = 26, 
<b>InactiveTaskMarker</b> = TaskMarker + 64, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a96681c59afac878c90c821403c472903">EscapeSequence</a> = 27, 
<b>InactiveEscapeSequence</b> = EscapeSequence + 64
<br />
 }</td></tr>
<tr class="separator:a30c13b0ea8b55b3204ea4e9f49a313b1 inherit pub_types_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_slots_classQsciLexerCPP"><td colspan="2" onclick="javascript:toggleInherit('pub_slots_classQsciLexerCPP')"><img src="closed.png" alt="-"/>&#160;Public Slots inherited from <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td></tr>
<tr class="memitem:ad0a3dd6dfb77a069303bfeeeed43773f inherit pub_slots_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#ad0a3dd6dfb77a069303bfeeeed43773f">setFoldAtElse</a> (bool fold)</td></tr>
<tr class="separator:ad0a3dd6dfb77a069303bfeeeed43773f inherit pub_slots_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf1f8163b8baf27ef65c1e5219bbf1e2 inherit pub_slots_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#aaf1f8163b8baf27ef65c1e5219bbf1e2">setFoldComments</a> (bool fold)</td></tr>
<tr class="separator:aaf1f8163b8baf27ef65c1e5219bbf1e2 inherit pub_slots_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af17ac732d73445822ef23a59f3e45aef inherit pub_slots_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#af17ac732d73445822ef23a59f3e45aef">setFoldCompact</a> (bool fold)</td></tr>
<tr class="separator:af17ac732d73445822ef23a59f3e45aef inherit pub_slots_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6a8c59ca8409029fc6b27b9ad3c70886 inherit pub_slots_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a6a8c59ca8409029fc6b27b9ad3c70886">setFoldPreprocessor</a> (bool fold)</td></tr>
<tr class="separator:a6a8c59ca8409029fc6b27b9ad3c70886 inherit pub_slots_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66dc6ae74420ab3406043ff9f6f70cc4 inherit pub_slots_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a66dc6ae74420ab3406043ff9f6f70cc4">setStylePreprocessor</a> (bool style)</td></tr>
<tr class="separator:a66dc6ae74420ab3406043ff9f6f70cc4 inherit pub_slots_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_slots_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_slots_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Slots inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a> (int autoindentstyle)</td></tr>
<tr class="separator:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a> (bool eoffill, int style=-1)</td></tr>
<tr class="separator:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a> (const QFont &amp;f, int style=-1)</td></tr>
<tr class="separator:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header signals_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('signals_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Signals inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a> (bool eolfilled, int style)</td></tr>
<tr class="separator:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a> (const QFont &amp;f, int style)</td></tr>
<tr class="separator:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a> (const char *prop, const char *val)</td></tr>
<tr class="separator:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_methods_classQsciLexerCPP"><td colspan="2" onclick="javascript:toggleInherit('pro_methods_classQsciLexerCPP')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td></tr>
<tr class="memitem:aa37ea54c5e39721b866c25b0e0335591 inherit pro_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#aa37ea54c5e39721b866c25b0e0335591">readProperties</a> (QSettings &amp;qs, const QString &amp;prefix)</td></tr>
<tr class="separator:aa37ea54c5e39721b866c25b0e0335591 inherit pro_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a46bd37b48e91903451ab59314448f322 inherit pro_methods_classQsciLexerCPP"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCPP.html#a46bd37b48e91903451ab59314448f322">writeProperties</a> (QSettings &amp;qs, const QString &amp;prefix) const</td></tr>
<tr class="separator:a46bd37b48e91903451ab59314448f322 inherit pro_methods_classQsciLexerCPP"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pro_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a41d4521504d63ee63d43fd7ed0c003ee"></a>
QByteArray&#160;</td><td class="memItemRight" valign="bottom"><b>textAsBytes</b> (const QString &amp;text) const</td></tr>
<tr class="separator:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a5261dd606c209a5c6a494e608a9a111a"></a>
QString&#160;</td><td class="memItemRight" valign="bottom"><b>bytesAsText</b> (const char *bytes, int size) const</td></tr>
<tr class="separator:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciLexerJavaScript.html" title="The QsciLexerJavaScript class encapsulates the Scintilla JavaScript lexer.">QsciLexerJavaScript</a> class encapsulates the Scintilla JavaScript lexer. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a94cbc22361a55fe0681ad7fe5425dfb5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a94cbc22361a55fe0681ad7fe5425dfb5">&#9670;&nbsp;</a></span>QsciLexerJavaScript()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciLexerJavaScript::QsciLexerJavaScript </td>
          <td>(</td>
          <td class="paramtype">QObject *&#160;</td>
          <td class="paramname"><em>parent</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Construct a <a class="el" href="classQsciLexerJavaScript.html" title="The QsciLexerJavaScript class encapsulates the Scintilla JavaScript lexer.">QsciLexerJavaScript</a> with parent <em>parent</em>. <em>parent</em> is typically the <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> instance. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a66b627130d76db15263b7502ec5d475c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a66b627130d76db15263b7502ec5d475c">&#9670;&nbsp;</a></span>defaultColor()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerJavaScript::defaultColor </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the foreground colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerJavaScript.html#af4b249f267973d29380b758a25b42e46">defaultPaper()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexerCPP.html#a39af10ac6ff34cb347bb2c891f8de64f">QsciLexerCPP</a>.</p>

</div>
</div>
<a id="af4b249f267973d29380b758a25b42e46"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af4b249f267973d29380b758a25b42e46">&#9670;&nbsp;</a></span>defaultPaper()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerJavaScript::defaultPaper </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the background colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerJavaScript.html#a66b627130d76db15263b7502ec5d475c">defaultColor()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexerCPP.html#aebdebbf12dc8bf264479bd570f669268">QsciLexerCPP</a>.</p>

</div>
</div>
<a id="af00e1d05374302fd4d2e2eeec1a829ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af00e1d05374302fd4d2e2eeec1a829ee">&#9670;&nbsp;</a></span>keywords()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerJavaScript::keywords </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>set</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the set of keywords for the keyword set <em>set</em> recognised by the lexer as a space separated string. </p>

<p>Reimplemented from <a class="el" href="classQsciLexerCPP.html#ac331bbae026859d8020ac5a6efd8fed1">QsciLexerCPP</a>.</p>

</div>
</div>
<a id="abc88c53a2cfe6dd61e059fad1e8f3539"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc88c53a2cfe6dd61e059fad1e8f3539">&#9670;&nbsp;</a></span>description()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QString QsciLexerJavaScript::description </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the descriptive name for style number <em>style</em>. If the style is invalid for this language then an empty QString is returned. This is intended to be used in user preference dialogs. </p>

<p>Reimplemented from <a class="el" href="classQsciLexerCPP.html#a761b431d688aa99c5c9b5110b41dc712">QsciLexerCPP</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
