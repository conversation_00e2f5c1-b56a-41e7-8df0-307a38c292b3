<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciLexerXML Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciLexerXML.html">QsciLexerXML</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ae5c90edfb9068eaea785bf14f2371120">ASPAtStart</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aa14057c2c5ef886e2f72cddeb2914afb">ASPJavaScriptComment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a803c5ffa1134c3623ade6d4bb683c8e8">ASPJavaScriptCommentDoc</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a86cd63314aba32adc7926e6e47a4395d">ASPJavaScriptCommentLine</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a9f89a8d7bd5e2d2855eb957f9ba9c87b">ASPJavaScriptDefault</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42adb4788e2364a6860cf7248c72a457736">ASPJavaScriptDoubleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a342baec8e1e79525b30e887321e60b99">ASPJavaScriptKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a472fbd596cdf4ace8183bb4f050c8b2c">ASPJavaScriptNumber</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a0391cd592ce195d67507404f2a6f7cc1">ASPJavaScriptRegex</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a54a7f4bfb454ab5c0c94e11a0767d3af">ASPJavaScriptSingleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42af5583e1cd3c0f89d89a9500274412702">ASPJavaScriptStart</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a5c87399fc70041dd85ce718d94c6139e">ASPJavaScriptSymbol</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ab9e69127f9a571ab7bff1bc87c052776">ASPJavaScriptUnclosedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ab388e2836763fec9ba15e7a1b3743e6d">ASPJavaScriptWord</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a07bad38a70450a58da8bd7ebebc9f4d4">ASPPythonClassName</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a9f91ee5b30f04252a226410118f87cbb">ASPPythonComment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a24d9181cb4ffca0ec889f64d32e27302">ASPPythonDefault</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ac410213e6afae932c50c5d7386180a82">ASPPythonDoubleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a3ef7c5d90b7885f79a9200e8144d461c">ASPPythonFunctionMethodName</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a2ba2b64ee2400dce3ee221aef187e524">ASPPythonIdentifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a9fc4d4e4fc20ca395d19a52e6e29453e">ASPPythonKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ac1973f076c1eb88d6ab71aab19ee839d">ASPPythonNumber</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aa38331046f91ae174bed6bed7d1c1154">ASPPythonOperator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aeba824b1c987b60d06c7bdd6c77858a5">ASPPythonSingleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aae87de6d2d1f768e5e09e1b6d7d8e2c5">ASPPythonStart</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42abf69f7d37a77f061868d594516f21b75">ASPPythonTripleDoubleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42af3dd65a9a5b6e685630ead91aebdd994">ASPPythonTripleSingleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a292d607fdb907de9d5901c90b01f64a5">ASPStart</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ac93712a2bf29f750c9b8629ba1aa6a8d">ASPVBScriptComment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a37226b834916114680ba667ef9615293">ASPVBScriptDefault</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ac3a6e8ea35f788fd08bd245ab1238709">ASPVBScriptIdentifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a99e48d7de494bb0a1ea1b5503014a50e">ASPVBScriptKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aa60810f36db9d4690903279530d2f93e">ASPVBScriptNumber</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a0af21eed628c46b93d8f46d78af3e18e">ASPVBScriptStart</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ab5af85bbbcc82ee3bd6e3f60dfc6e43c">ASPVBScriptString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a70448ba15dedb0dea1a6e10d806ac03d">ASPVBScriptUnclosedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a93b7ceec0b76249d6c4ef8caeb8a1c6e">ASPXCComment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42acc9fa3017024877e48e2e4bdc139243c">Attribute</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#ae6ef21c59fd374d1d4893adcc1e3ee9b">autoCompletionFillups</a>() const</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">autoCompletionWordSeparators</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">blockEnd</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">blockStart</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#affe136114d62180e9a14caa81f2b7fd5">braceStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>bytesAsText</b>(const char *bytes, int size) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#ac53cb0d155aa3d81add74ee90585cb6a">caseSensitiveTags</a>() const</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aec707f7a4c069449024b9dcd806a9978">CDATA</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a8d1900807d1ac2f027fb67fb7483de29">Default</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerXML.html#a36f390db2c97da9c271b0d1ba2794278">defaultColor</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerXML.html">QsciLexerXML</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">QsciLexer::defaultColor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerXML.html#a995fe59f125a7cb27cf178b9e83015bc">defaultEolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerXML.html">QsciLexerXML</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerXML.html#a24d4902dc121381ae5a18a4b8e802479">defaultFont</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerXML.html">QsciLexerXML</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">QsciLexer::defaultFont</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerXML.html#a57ae4ff270b1c66316b0849ff9017677">defaultPaper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerXML.html">QsciLexerXML</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">QsciLexer::defaultPaper</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#a638fcb2f0d2dd4be844881998cdb3b76">description</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#a1379abf89d88a2dd7854f957b28656c5">djangoTemplates</a>() const</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a2cc0d9178c847cbde2bed0c104fe0c91">Entity</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a>(bool eolfilled, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af861d2c565994b427514715fe95a80b7">foldCompact</a>() const</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#a2f3d753794280bfc09719e3ca521be83">foldPreprocessor</a>() const</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#aaf885bb9d07591114c2a2cc5769bb02a">foldScriptComments</a>() const</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#aabad8cc171c34609ee0d6c600a534334">foldScriptHeredocs</a>() const</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a>(const QFont &amp;f, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a1d508b4ef80e80c1a5b880357ed2651f">HTMLComment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a9b3ff0738c01f777d1591d0a06ac95fa">HTMLDoubleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a89b27b58d6c068b649e247f5236f2c2d">HTMLNumber</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42af2b7e8d205c6193e3da1e9237f6e34be">HTMLSingleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a5cfd2a96dca88ed5b108a31707190ccf">HTMLValue</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a4abd3fe4667e3f3e07a349fe38465772">JavaScriptComment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a956ea631c098271e1dbda2480f2ee7bf">JavaScriptCommentDoc</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a7fd719a9c762649493cdddf21e77b3d4">JavaScriptCommentLine</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a48c75a37cb9808fb8e38b57ade4235f3">JavaScriptDefault</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a0df9d0b6ab51f5da9178a0627025a542">JavaScriptDoubleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a359bd714cc1ad89a586d749034a1141c">JavaScriptKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ae783df661533d59fe987b3cffdfe65fd">JavaScriptNumber</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a24fefe909c415666e66b25379c5ea447">JavaScriptRegex</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a23fc5cfa88114fc586c9d055e06ed97c">JavaScriptSingleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a812edec51acbc5656d061534adb92963">JavaScriptStart</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ae761e0897e90e25a7fe59a99b68215b9">JavaScriptSymbol</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aa7eb7816b851a536f4834c0bdaa89639">JavaScriptUnclosedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a3958baaa0ee358fbc7deef59528138a6">JavaScriptWord</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerXML.html#ab78937576c3c727f073921059ac87a59">keywords</a>(int set) const</td><td class="entry"><a class="el" href="classQsciLexerXML.html">QsciLexerXML</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerXML.html#a801d7cef474dcf23d93e2f2f53034abe">language</a>() const</td><td class="entry"><a class="el" href="classQsciLexerXML.html">QsciLexerXML</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerXML.html#ae0bb41012d9d98366b435f9a534ce7a1">lexer</a>() const</td><td class="entry"><a class="el" href="classQsciLexerXML.html">QsciLexerXML</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#ae669580d3e3332a9b310d1bf78251f07">makoTemplates</a>() const</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42afb51d8ea46c08f042378a802e2ab03fc">OtherInTag</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42afb916a440aa2213578e4358372a434c9">PHPComment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42adde9db4e3a3adf2f82aa9e1a86d54f3b">PHPCommentLine</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a15c3cdaa9b84f8928c71b2783662f278">PHPDefault</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a482ba4b07e0d2f876c0553923d186904">PHPDoubleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aed79ec532369cc9591f8fe66c9617280">PHPDoubleQuotedVariable</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a206448a0f85f732875e3f25e08474698">PHPKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a6369adb54b02ea44e77d5614860b4c67">PHPNumber</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a5191e5e6477b75df277927e9b268022e">PHPOperator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a07f194eab645ab7718f62625742e355c">PHPSingleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a776a678d7a43908f020a9075ec13d52d">PHPStart</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a68aa867924addc9a59d88fe092fe2664">PHPVariable</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a>(const char *prop, const char *val)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a5e5d6a493b61aaad190ac2f39bd67757">PythonClassName</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a87a9ae8e5d5eee95d6fa8f1487eb7cba">PythonComment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ab7db3f14bf6ceff1c2059464b7faba33">PythonDefault</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a834d9ff5f844b3636621cb7b29aab1bd">PythonDoubleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ab44eadc3b71a28a641e3bc231a7e19ca">PythonFunctionMethodName</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aa6852bf8af5d0efc73bc3aa3906602e4">PythonIdentifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a042f35e7ceb80fc1ca64c5e809f9d9c4">PythonKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a8522a961497e9ede980ecc214e30622a">PythonNumber</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a503f440eb6b191768ab8f9822c8ff112">PythonOperator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a1206e264b1e9388934611d87093f8ebd">PythonSingleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ad5b56e2991364fbc24424aa3ea8b91c5">PythonStart</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42adbadda3ca2f455f7bdf27b17e71018dd">PythonTripleDoubleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a956c471f37567572d4347c354506b377">PythonTripleSingleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#a4c5ae7bc7d27946b1b07b940ef30a093">QsciLexerHTML</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerXML.html#a9fc5fef8f86ef0f1162a18ca4cc88aa1">QsciLexerXML</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexerXML.html">QsciLexerXML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerXML.html#a2acbf99b93c18d9a9f922c9e2894bf4f">readProperties</a>(QSettings &amp;qs, const QString &amp;prefix)</td><td class="entry"><a class="el" href="classQsciLexerXML.html">QsciLexerXML</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerXML.html#a29937d422c25f17612c57e16a7bddaf1">refreshProperties</a>()</td><td class="entry"><a class="el" href="classQsciLexerXML.html">QsciLexerXML</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aeb4058a907dcaf6324564d345aa68918">Script</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerXML.html#a96ad1f818e51a3606404d24bf7a28a91">scriptsStyled</a>() const</td><td class="entry"><a class="el" href="classQsciLexerXML.html">QsciLexerXML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a>(QsciAbstractAPIs *apis)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a>(int autoindentstyle)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#a2fda8ad57009d4e2f1ac388cad2cfc92">setCaseSensitiveTags</a>(bool sens)</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a>(const QFont &amp;f)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#a59c9b8ff5d698d7e7e03ec2655a24764">setDjangoTemplates</a>(bool enabled)</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>setEditor</b>(QsciScintilla *editor) (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a>(bool eoffill, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#a1036c768307d29c40f09cc1bc2fce37c">setFoldCompact</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#aeba753c0e1fca8bf66834667e301458e">setFoldPreprocessor</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#a51401044d3ad272ede84e1f2a128cce6">setFoldScriptComments</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#a122450b5227d23ee119b2653b9e9be2f">setFoldScriptHeredocs</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a>(const QFont &amp;f, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#a8553315e763e1e53f56dd4dbe6b3c3d7">setMakoTemplates</a>(bool enabled)</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerXML.html#a7bbfdb6b269b6e52791fcbf1df60731e">setScriptsStyled</a>(bool styled)</td><td class="entry"><a class="el" href="classQsciLexerXML.html">QsciLexerXML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a418b3eec8fb360b335cb9dc45ce01e85">SGMLBlockDefault</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a11dde10577367f11ae2d4198556ddeec">SGMLCommand</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a23b4e494ae6353492b2637b6aa72d0b9">SGMLComment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a68c7c26352e7ee71cbe90a3626247f5a">SGMLDefault</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ade1b5b1729dae715fd4eeff275355c39">SGMLDoubleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a447f38ea2ca1777091030e74b1aa9ac0">SGMLEntity</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a6b97afc0c896637ae69a477e47ab938f">SGMLError</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a791994f6f8b23afd317efc08b2cc518d">SGMLParameter</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aa9ede90b43a8c8f1bede9ca6d7eefb70">SGMLParameterComment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a9a67d513fbf29032da55c86c6e8a584c">SGMLSingleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a853fb44d9faf4f1df33c262793bed3d2">SGMLSpecial</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42afa28b035f2fa221ca935f976c5d7c5d0">Tag</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>textAsBytes</b>(const QString &amp;text) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42afb566349f4b55ff5c95812b855b62b1d">UnknownAttribute</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aba4bf36f07a8e903ca72edd28a3f0a72">UnknownTag</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a48028a0028e6c185c8c0a8b3310374ee">VBScriptComment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a5da6af0b230ed6209c2ed48574369ae3">VBScriptDefault</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aa8f04375ef50150287dcee5c24bcf285">VBScriptIdentifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a5e5579413c3b931481d5881a18bc9e38">VBScriptKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a71776167656c34320c2c9fc85e8ea33d">VBScriptNumber</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aff0c53aa932f6d2150ae3605c686a363">VBScriptStart</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a9a3c32775ad47d14eaa8bdb270ce722e">VBScriptString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42afe0d2ae2c61751803669067cdb62d4de">VBScriptUnclosedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#ad12b328c98474857186af058726bd38d">wordCharacters</a>() const</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerXML.html#a1cf58cba78405397f793b6a9aff64035">writeProperties</a>(QSettings &amp;qs, const QString &amp;prefix) const</td><td class="entry"><a class="el" href="classQsciLexerXML.html">QsciLexerXML</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ae25dbca1f292ccf5aa82a63d84aa22f9">XMLEnd</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42afd21b87183c742cc1a3538cf8d28ce68">XMLStart</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ac9f00cd2c221a620326432b65b2ece95">XMLTagEnd</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerHTML.html#af031b3510193023158fb74ca637f79b2">~QsciLexerHTML</a>()</td><td class="entry"><a class="el" href="classQsciLexerHTML.html">QsciLexerHTML</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerXML.html#a89f9fc2c333d559ed9489cc6b121b91e">~QsciLexerXML</a>()</td><td class="entry"><a class="el" href="classQsciLexerXML.html">QsciLexerXML</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
