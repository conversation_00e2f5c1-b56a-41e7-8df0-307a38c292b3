<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciLexerLua Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-slots">Public Slots</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="classQsciLexerLua-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciLexerLua Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscilexerlua.h&gt;</code></p>

<p>Inherits <a class="el" href="classQsciLexer.html">QsciLexer</a>.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a34427b01d36d42008727d7cdc41d8d25"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a60c35a89c49dabb959c8433fc053295b">Default</a> = 0, 
<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25abf2c37dbd9d2f0f761e4c75b9a916c7f">Comment</a> = 1, 
<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a9aa357d7b337b9aabcb7f0566aff3aa9">LineComment</a> = 2, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25aa7100ee56315d914482fbd97843d98f5">Number</a> = 4, 
<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25ae5d52051b1b6459a13bff6db572c0dce">Keyword</a> = 5, 
<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a351f36d3635a5f3af4815f6a74863eae">String</a> = 6, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a95cd8dc67acc900b870665a61009b731">Character</a> = 7, 
<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a78b4fea76183ad94c31e18f744495e94">LiteralString</a> = 8, 
<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25af9f702c766041919da2c7dac8fc11901">Preprocessor</a> = 9, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a5267c1cdd34c280a959cd7df49b16ab2">Operator</a> = 10, 
<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a03fe51d98c72f4af37b148cfb2a1319f">Identifier</a> = 11, 
<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25afcd79271430a9af1fba6b94ee41cdf38">UnclosedString</a> = 12, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a39efcf7df417b4566cace55874fb668e">BasicFunctions</a> = 13, 
<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25af4a7065e3246c398a68f9af4ad839eb7">StringTableMathsFunctions</a> = 14, 
<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a1da860131fdcc821dbd51a25f65175ac">CoroutinesIOSystemFacilities</a> = 15, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25ae99b87ba3113da81b9b8a7b675ac5abd">KeywordSet5</a> = 16, 
<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a8b505486e278e80d9caef2ef9769544b">KeywordSet6</a> = 17, 
<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a39080fdb9bcf558f8aca25ebbc5877cb">KeywordSet7</a> = 18, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25ae15e71bd786c060d6412ce31551f3e42">KeywordSet8</a> = 19, 
<a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a64681c9d0cf443f6073155989512f8b9">Label</a> = 20
<br />
 }</td></tr>
<tr class="separator:a34427b01d36d42008727d7cdc41d8d25"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-slots"></a>
Public Slots</h2></td></tr>
<tr class="memitem:a2f54e561f646da5ff20c5e85b2f377ea"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#a2f54e561f646da5ff20c5e85b2f377ea">setFoldCompact</a> (bool fold)</td></tr>
<tr class="separator:a2f54e561f646da5ff20c5e85b2f377ea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_slots_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_slots_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Slots inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a> (int autoindentstyle)</td></tr>
<tr class="separator:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a> (bool eoffill, int style=-1)</td></tr>
<tr class="separator:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a> (const QFont &amp;f, int style=-1)</td></tr>
<tr class="separator:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a8932efc560175dc70a88e23b8136bb8f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#a8932efc560175dc70a88e23b8136bb8f">QsciLexerLua</a> (QObject *parent=0)</td></tr>
<tr class="separator:a8932efc560175dc70a88e23b8136bb8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adbeb88fef8346b6543d6ef8b2154e763"><td class="memItemLeft" align="right" valign="top"><a id="adbeb88fef8346b6543d6ef8b2154e763"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#adbeb88fef8346b6543d6ef8b2154e763">~QsciLexerLua</a> ()</td></tr>
<tr class="separator:adbeb88fef8346b6543d6ef8b2154e763"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a942c993effc83d0dedec2fc20d8a741f"><td class="memItemLeft" align="right" valign="top"><a id="a942c993effc83d0dedec2fc20d8a741f"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#a942c993effc83d0dedec2fc20d8a741f">language</a> () const</td></tr>
<tr class="separator:a942c993effc83d0dedec2fc20d8a741f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8124ec8b5b96d95bb225cbb4e95f55cb"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#a8124ec8b5b96d95bb225cbb4e95f55cb">lexer</a> () const</td></tr>
<tr class="separator:a8124ec8b5b96d95bb225cbb4e95f55cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff715db68554a1022792135e8edd0dba"><td class="memItemLeft" align="right" valign="top">QStringList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#aff715db68554a1022792135e8edd0dba">autoCompletionWordSeparators</a> () const</td></tr>
<tr class="separator:aff715db68554a1022792135e8edd0dba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a157c462625b4826a5d7fb9eec42cfc78"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#a157c462625b4826a5d7fb9eec42cfc78">blockStart</a> (int *style=0) const</td></tr>
<tr class="separator:a157c462625b4826a5d7fb9eec42cfc78"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a30570eca6c21ea302b1c6c0bd733dc14"><td class="memItemLeft" align="right" valign="top"><a id="a30570eca6c21ea302b1c6c0bd733dc14"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#a30570eca6c21ea302b1c6c0bd733dc14">braceStyle</a> () const</td></tr>
<tr class="separator:a30570eca6c21ea302b1c6c0bd733dc14"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1412f4f04885bf9b315fbb371c54dc7c"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#a1412f4f04885bf9b315fbb371c54dc7c">defaultColor</a> (int style) const</td></tr>
<tr class="separator:a1412f4f04885bf9b315fbb371c54dc7c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4d20a72f3087068af5840042d9beeca7"><td class="memItemLeft" align="right" valign="top"><a id="a4d20a72f3087068af5840042d9beeca7"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#a4d20a72f3087068af5840042d9beeca7">defaultEolFill</a> (int style) const</td></tr>
<tr class="separator:a4d20a72f3087068af5840042d9beeca7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2db5e63ff4667a3f8e9df24a0accdf3d"><td class="memItemLeft" align="right" valign="top"><a id="a2db5e63ff4667a3f8e9df24a0accdf3d"></a>
QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#a2db5e63ff4667a3f8e9df24a0accdf3d">defaultFont</a> (int style) const</td></tr>
<tr class="separator:a2db5e63ff4667a3f8e9df24a0accdf3d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aec007c8c5c374ca94b71d3eb0f47f467"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#aec007c8c5c374ca94b71d3eb0f47f467">defaultPaper</a> (int style) const</td></tr>
<tr class="separator:aec007c8c5c374ca94b71d3eb0f47f467"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a136982546f34f83f5e3dd21f67074d4d"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#a136982546f34f83f5e3dd21f67074d4d">keywords</a> (int set) const</td></tr>
<tr class="separator:a136982546f34f83f5e3dd21f67074d4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad77fd8b1e9ed6bac617f194306de2ea8"><td class="memItemLeft" align="right" valign="top">QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#ad77fd8b1e9ed6bac617f194306de2ea8">description</a> (int style) const</td></tr>
<tr class="separator:ad77fd8b1e9ed6bac617f194306de2ea8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a628efb828569208d6219a88f1fc6a1a7"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#a628efb828569208d6219a88f1fc6a1a7">refreshProperties</a> ()</td></tr>
<tr class="separator:a628efb828569208d6219a88f1fc6a1a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27383c2def3f59e903aec9537ef43d2c"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#a27383c2def3f59e903aec9537ef43d2c">foldCompact</a> () const</td></tr>
<tr class="separator:a27383c2def3f59e903aec9537ef43d2c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a> (QObject *parent=0)</td></tr>
<tr class="separator:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="af6cc5bb9d9421d806e9941d018030068"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a> ()</td></tr>
<tr class="separator:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a> () const</td></tr>
<tr class="separator:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a> () const</td></tr>
<tr class="separator:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a6504a6fff35af16fbfd97889048db2a5"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a> () const</td></tr>
<tr class="separator:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a> ()</td></tr>
<tr class="separator:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e294eba77713f516acbcebc10af1493 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">blockEnd</a> (int *style=0) const</td></tr>
<tr class="separator:a8e294eba77713f516acbcebc10af1493 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a> () const</td></tr>
<tr class="separator:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf12117a142b6f68479ea425d80a4196 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a> (int *style=0) const</td></tr>
<tr class="separator:abf12117a142b6f68479ea425d80a4196 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="afccca7eb1aed463f89ac442d99135839"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a> () const</td></tr>
<tr class="separator:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a> (int style) const</td></tr>
<tr class="separator:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a> (int style) const</td></tr>
<tr class="separator:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a> (int style) const</td></tr>
<tr class="separator:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="aff4735542e937c5e35ecb2eb82e8f875"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a> () const</td></tr>
<tr class="separator:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a> () const</td></tr>
<tr class="separator:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a> (int style) const</td></tr>
<tr class="separator:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">defaultColor</a> () const</td></tr>
<tr class="separator:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">defaultFont</a> () const</td></tr>
<tr class="separator:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">defaultPaper</a> () const</td></tr>
<tr class="separator:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciScintilla.html">QsciScintilla</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a> () const</td></tr>
<tr class="separator:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a> (<a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *<a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>)</td></tr>
<tr class="separator:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a> (const QFont &amp;f)</td></tr>
<tr class="separator:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a1e81186b1f8f8bc2a4901a42cbca568a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>setEditor</b> (<a class="el" href="classQsciScintilla.html">QsciScintilla</a> *<a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>)</td></tr>
<tr class="separator:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td></tr>
<tr class="separator:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a> () const</td></tr>
<tr class="separator:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aace68e3dbcef9da1b031fb9cfd843c57 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aace68e3dbcef9da1b031fb9cfd843c57">wordCharacters</a> () const</td></tr>
<tr class="separator:aace68e3dbcef9da1b031fb9cfd843c57 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td></tr>
<tr class="separator:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:a928315606c0bd973c59e0b6d9641c3cd"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#a928315606c0bd973c59e0b6d9641c3cd">readProperties</a> (QSettings &amp;qs, const QString &amp;prefix)</td></tr>
<tr class="separator:a928315606c0bd973c59e0b6d9641c3cd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af0fffa0361bad4a3a007c09a1811db9c"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerLua.html#af0fffa0361bad4a3a007c09a1811db9c">writeProperties</a> (QSettings &amp;qs, const QString &amp;prefix) const</td></tr>
<tr class="separator:af0fffa0361bad4a3a007c09a1811db9c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pro_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a41d4521504d63ee63d43fd7ed0c003ee"></a>
QByteArray&#160;</td><td class="memItemRight" valign="bottom"><b>textAsBytes</b> (const QString &amp;text) const</td></tr>
<tr class="separator:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a5261dd606c209a5c6a494e608a9a111a"></a>
QString&#160;</td><td class="memItemRight" valign="bottom"><b>bytesAsText</b> (const char *bytes, int size) const</td></tr>
<tr class="separator:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header signals_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('signals_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Signals inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a> (bool eolfilled, int style)</td></tr>
<tr class="separator:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a> (const QFont &amp;f, int style)</td></tr>
<tr class="separator:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a> (const char *prop, const char *val)</td></tr>
<tr class="separator:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciLexerLua.html" title="The QsciLexerLua class encapsulates the Scintilla Lua lexer.">QsciLexerLua</a> class encapsulates the Scintilla Lua lexer. </p>
</div><h2 class="groupheader">Member Enumeration Documentation</h2>
<a id="a34427b01d36d42008727d7cdc41d8d25"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a34427b01d36d42008727d7cdc41d8d25">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the meanings of the different styles used by the Lua lexer. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25a60c35a89c49dabb959c8433fc053295b"></a>Default&#160;</td><td class="fielddoc"><p>The default. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25abf2c37dbd9d2f0f761e4c75b9a916c7f"></a>Comment&#160;</td><td class="fielddoc"><p>A block comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25a9aa357d7b337b9aabcb7f0566aff3aa9"></a>LineComment&#160;</td><td class="fielddoc"><p>A line comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25aa7100ee56315d914482fbd97843d98f5"></a>Number&#160;</td><td class="fielddoc"><p>A number. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25ae5d52051b1b6459a13bff6db572c0dce"></a>Keyword&#160;</td><td class="fielddoc"><p>A keyword. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25a351f36d3635a5f3af4815f6a74863eae"></a>String&#160;</td><td class="fielddoc"><p>A string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25a95cd8dc67acc900b870665a61009b731"></a>Character&#160;</td><td class="fielddoc"><p>A character. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25a78b4fea76183ad94c31e18f744495e94"></a>LiteralString&#160;</td><td class="fielddoc"><p>A literal string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25af9f702c766041919da2c7dac8fc11901"></a>Preprocessor&#160;</td><td class="fielddoc"><p>Preprocessor. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25a5267c1cdd34c280a959cd7df49b16ab2"></a>Operator&#160;</td><td class="fielddoc"><p>An operator. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25a03fe51d98c72f4af37b148cfb2a1319f"></a>Identifier&#160;</td><td class="fielddoc"><p>An identifier. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25afcd79271430a9af1fba6b94ee41cdf38"></a>UnclosedString&#160;</td><td class="fielddoc"><p>The end of a line where a string is not closed. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25a39efcf7df417b4566cace55874fb668e"></a>BasicFunctions&#160;</td><td class="fielddoc"><p>Basic functions. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25af4a7065e3246c398a68f9af4ad839eb7"></a>StringTableMathsFunctions&#160;</td><td class="fielddoc"><p>String, table and maths functions. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25a1da860131fdcc821dbd51a25f65175ac"></a>CoroutinesIOSystemFacilities&#160;</td><td class="fielddoc"><p>Coroutines, I/O and system facilities. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25ae99b87ba3113da81b9b8a7b675ac5abd"></a>KeywordSet5&#160;</td><td class="fielddoc"><p>A keyword defined in keyword set number 5. The class must be sub-classed and re-implement <a class="el" href="classQsciLexerLua.html#a136982546f34f83f5e3dd21f67074d4d">keywords()</a> to make use of this style. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25a8b505486e278e80d9caef2ef9769544b"></a>KeywordSet6&#160;</td><td class="fielddoc"><p>A keyword defined in keyword set number 6. The class must be sub-classed and re-implement <a class="el" href="classQsciLexerLua.html#a136982546f34f83f5e3dd21f67074d4d">keywords()</a> to make use of this style. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25a39080fdb9bcf558f8aca25ebbc5877cb"></a>KeywordSet7&#160;</td><td class="fielddoc"><p>A keyword defined in keyword set number 7. The class must be sub-classed and re-implement <a class="el" href="classQsciLexerLua.html#a136982546f34f83f5e3dd21f67074d4d">keywords()</a> to make use of this style. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25ae15e71bd786c060d6412ce31551f3e42"></a>KeywordSet8&#160;</td><td class="fielddoc"><p>A keyword defined in keyword set number 8. The class must be sub-classed and re-implement <a class="el" href="classQsciLexerLua.html#a136982546f34f83f5e3dd21f67074d4d">keywords()</a> to make use of this style. </p>
</td></tr>
<tr><td class="fieldname"><a id="a34427b01d36d42008727d7cdc41d8d25a64681c9d0cf443f6073155989512f8b9"></a>Label&#160;</td><td class="fielddoc"><p>A label. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a8932efc560175dc70a88e23b8136bb8f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8932efc560175dc70a88e23b8136bb8f">&#9670;&nbsp;</a></span>QsciLexerLua()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciLexerLua::QsciLexerLua </td>
          <td>(</td>
          <td class="paramtype">QObject *&#160;</td>
          <td class="paramname"><em>parent</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Construct a <a class="el" href="classQsciLexerLua.html" title="The QsciLexerLua class encapsulates the Scintilla Lua lexer.">QsciLexerLua</a> with parent <em>parent</em>. <em>parent</em> is typically the <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> instance. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a8124ec8b5b96d95bb225cbb4e95f55cb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8124ec8b5b96d95bb225cbb4e95f55cb">&#9670;&nbsp;</a></span>lexer()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerLua::lexer </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the name of the lexer. Some lexers support a number of languages. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ac7c40b97187e23ab85f6d95113f91b39">QsciLexer</a>.</p>

</div>
</div>
<a id="aff715db68554a1022792135e8edd0dba"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aff715db68554a1022792135e8edd0dba">&#9670;&nbsp;</a></span>autoCompletionWordSeparators()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QStringList QsciLexerLua::autoCompletionWordSeparators </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the list of character sequences that can separate auto-completion words. The first in the list is assumed to be the sequence used to separate words in the lexer's API files. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">QsciLexer</a>.</p>

</div>
</div>
<a id="a157c462625b4826a5d7fb9eec42cfc78"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a157c462625b4826a5d7fb9eec42cfc78">&#9670;&nbsp;</a></span>blockStart()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerLua::blockStart </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of words or characters in a particular style that define the start of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">QsciLexer</a>.</p>

</div>
</div>
<a id="a1412f4f04885bf9b315fbb371c54dc7c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1412f4f04885bf9b315fbb371c54dc7c">&#9670;&nbsp;</a></span>defaultColor()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerLua::defaultColor </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the foreground colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerLua.html#aec007c8c5c374ca94b71d3eb0f47f467">defaultPaper()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#af7508f1b816a2c9446d36141edc9b5ce">QsciLexer</a>.</p>

</div>
</div>
<a id="aec007c8c5c374ca94b71d3eb0f47f467"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aec007c8c5c374ca94b71d3eb0f47f467">&#9670;&nbsp;</a></span>defaultPaper()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerLua::defaultPaper </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the background colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerLua.html#a1412f4f04885bf9b315fbb371c54dc7c">defaultColor()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a7e5ab7f541d913760c32abedbdc72963">QsciLexer</a>.</p>

</div>
</div>
<a id="a136982546f34f83f5e3dd21f67074d4d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a136982546f34f83f5e3dd21f67074d4d">&#9670;&nbsp;</a></span>keywords()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerLua::keywords </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>set</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the set of keywords for the keyword set <em>set</em> recognised by the lexer as a space separated string. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a013b7c1bf9846e231b97827dfd9540b0">QsciLexer</a>.</p>

</div>
</div>
<a id="ad77fd8b1e9ed6bac617f194306de2ea8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad77fd8b1e9ed6bac617f194306de2ea8">&#9670;&nbsp;</a></span>description()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QString QsciLexerLua::description </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the descriptive name for style number <em>style</em>. If the style is invalid for this language then an empty QString is returned. This is intended to be used in user preference dialogs. </p>

<p>Implements <a class="el" href="classQsciLexer.html#add9c20adb43bc38d1a0ca3083ac3e6fa">QsciLexer</a>.</p>

</div>
</div>
<a id="a628efb828569208d6219a88f1fc6a1a7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a628efb828569208d6219a88f1fc6a1a7">&#9670;&nbsp;</a></span>refreshProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerLua::refreshProperties </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Causes all properties to be refreshed by emitting the <a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged()</a> signal as required. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ae508c3ab4ce1f338dfff3ddf5ee7e34c">QsciLexer</a>.</p>

</div>
</div>
<a id="a27383c2def3f59e903aec9537ef43d2c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a27383c2def3f59e903aec9537ef43d2c">&#9670;&nbsp;</a></span>foldCompact()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerLua::foldCompact </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if trailing blank lines are included in a fold block.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerLua.html#a2f54e561f646da5ff20c5e85b2f377ea">setFoldCompact()</a> </dd></dl>

</div>
</div>
<a id="a2f54e561f646da5ff20c5e85b2f377ea"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2f54e561f646da5ff20c5e85b2f377ea">&#9670;&nbsp;</a></span>setFoldCompact</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerLua::setFoldCompact </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then trailing blank lines are included in a fold block. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerLua.html#a27383c2def3f59e903aec9537ef43d2c">foldCompact()</a> </dd></dl>

</div>
</div>
<a id="a928315606c0bd973c59e0b6d9641c3cd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a928315606c0bd973c59e0b6d9641c3cd">&#9670;&nbsp;</a></span>readProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerLua::readProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are read from the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ad472b16506a4cbc19634f07aa90f1ea6">QsciLexer</a>.</p>

</div>
</div>
<a id="af0fffa0361bad4a3a007c09a1811db9c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af0fffa0361bad4a3a007c09a1811db9c">&#9670;&nbsp;</a></span>writeProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerLua::writeProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are written to the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#abccc4e010b724df1a7b5c5f3bce29501">QsciLexer</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
