<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciPrinter Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classQsciPrinter-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciPrinter Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qsciprinter.h&gt;</code></p>

<p>Inherits QPrinter.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aaa54abecf0defffdfda80f95af6febf9"><td class="memItemLeft" align="right" valign="top"><a id="aaa54abecf0defffdfda80f95af6febf9"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciPrinter.html#aaa54abecf0defffdfda80f95af6febf9">QsciPrinter</a> (PrinterMode mode=ScreenResolution)</td></tr>
<tr class="separator:aaa54abecf0defffdfda80f95af6febf9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9c7747919e355a885d6ebb4b0d0dc619"><td class="memItemLeft" align="right" valign="top"><a id="a9c7747919e355a885d6ebb4b0d0dc619"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciPrinter.html#a9c7747919e355a885d6ebb4b0d0dc619">~QsciPrinter</a> ()</td></tr>
<tr class="separator:a9c7747919e355a885d6ebb4b0d0dc619"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a420e136529a8d49551eb8af0f5cdce03"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciPrinter.html#a420e136529a8d49551eb8af0f5cdce03">formatPage</a> (QPainter &amp;painter, bool drawing, QRect &amp;area, int pagenr)</td></tr>
<tr class="separator:a420e136529a8d49551eb8af0f5cdce03"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aafc924b9d8d494541b89ac8d461b4300"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciPrinter.html#aafc924b9d8d494541b89ac8d461b4300">magnification</a> () const</td></tr>
<tr class="separator:aafc924b9d8d494541b89ac8d461b4300"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad66724c8a5e5e202998bd6533fef61be"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciPrinter.html#ad66724c8a5e5e202998bd6533fef61be">setMagnification</a> (int <a class="el" href="classQsciPrinter.html#aafc924b9d8d494541b89ac8d461b4300">magnification</a>)</td></tr>
<tr class="separator:ad66724c8a5e5e202998bd6533fef61be"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0bd255795c503a091ddc76d3564e2aac"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciPrinter.html#a0bd255795c503a091ddc76d3564e2aac">printRange</a> (<a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a> *qsb, QPainter &amp;painter, int from=-1, int to=-1)</td></tr>
<tr class="separator:a0bd255795c503a091ddc76d3564e2aac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aae304336a4a8d4c2e332744ceeba1393"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciPrinter.html#aae304336a4a8d4c2e332744ceeba1393">printRange</a> (<a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a> *qsb, int from=-1, int to=-1)</td></tr>
<tr class="separator:aae304336a4a8d4c2e332744ceeba1393"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad67d67c266263dd2dbfe940b4ad98584"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciScintilla.html#a7081c7ff25b5f6bd5b3a6cbd478a9f42">QsciScintilla::WrapMode</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciPrinter.html#ad67d67c266263dd2dbfe940b4ad98584">wrapMode</a> () const</td></tr>
<tr class="separator:ad67d67c266263dd2dbfe940b4ad98584"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa95827e3bd2c3c0e658afe55fa12476e"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciPrinter.html#aa95827e3bd2c3c0e658afe55fa12476e">setWrapMode</a> (<a class="el" href="classQsciScintilla.html#a7081c7ff25b5f6bd5b3a6cbd478a9f42">QsciScintilla::WrapMode</a> wmode)</td></tr>
<tr class="separator:aa95827e3bd2c3c0e658afe55fa12476e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciPrinter.html" title="The QsciPrinter class is a sub-class of the Qt QPrinter class that is able to print the text of a Sci...">QsciPrinter</a> class is a sub-class of the Qt QPrinter class that is able to print the text of a Scintilla document. </p>
<p>The class can be further sub-classed to alter to layout of the text, adding headers and footers for example. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a420e136529a8d49551eb8af0f5cdce03"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a420e136529a8d49551eb8af0f5cdce03">&#9670;&nbsp;</a></span>formatPage()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciPrinter::formatPage </td>
          <td>(</td>
          <td class="paramtype">QPainter &amp;&#160;</td>
          <td class="paramname"><em>painter</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>drawing</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">QRect &amp;&#160;</td>
          <td class="paramname"><em>area</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>pagenr</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Format a page, by adding headers and footers for example, before the document text is drawn on it. <em>painter</em> is the painter to be used to add customised text and graphics. <em>drawing</em> is true if the page is actually being drawn rather than being sized. <em>painter</em> drawing methods must only be called when <em>drawing</em> is true. <em>area</em> is the area of the page that will be used to draw the text. This should be modified if it is necessary to reserve space for any customised text or graphics. By default the area is relative to the printable area of the page. Use QPrinter::setFullPage() before calling <a class="el" href="classQsciPrinter.html#a0bd255795c503a091ddc76d3564e2aac">printRange()</a> if you want to try and print over the whole page. <em>pagenr</em> is the number of the page. The first page is numbered 1. </p>

</div>
</div>
<a id="aafc924b9d8d494541b89ac8d461b4300"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aafc924b9d8d494541b89ac8d461b4300">&#9670;&nbsp;</a></span>magnification()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int QsciPrinter::magnification </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Return the number of points to add to each font when printing.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciPrinter.html#ad66724c8a5e5e202998bd6533fef61be">setMagnification()</a> </dd></dl>

</div>
</div>
<a id="ad66724c8a5e5e202998bd6533fef61be"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad66724c8a5e5e202998bd6533fef61be">&#9670;&nbsp;</a></span>setMagnification()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciPrinter::setMagnification </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>magnification</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sets the number of points to add to each font when printing to <em>magnification</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciPrinter.html#aafc924b9d8d494541b89ac8d461b4300">magnification()</a> </dd></dl>

</div>
</div>
<a id="a0bd255795c503a091ddc76d3564e2aac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0bd255795c503a091ddc76d3564e2aac">&#9670;&nbsp;</a></span>printRange() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual int QsciPrinter::printRange </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a> *&#160;</td>
          <td class="paramname"><em>qsb</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">QPainter &amp;&#160;</td>
          <td class="paramname"><em>painter</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>from</em> = <code>-1</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>to</em> = <code>-1</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Print a range of lines from the Scintilla instance <em>qsb</em> using the supplied QPainter <em>painter</em>. <em>from</em> is the first line to print and a negative value signifies the first line of text. <em>to</em> is the last line to print and a negative value signifies the last line of text. true is returned if there was no error. </p>

</div>
</div>
<a id="aae304336a4a8d4c2e332744ceeba1393"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aae304336a4a8d4c2e332744ceeba1393">&#9670;&nbsp;</a></span>printRange() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual int QsciPrinter::printRange </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a> *&#160;</td>
          <td class="paramname"><em>qsb</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>from</em> = <code>-1</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>to</em> = <code>-1</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Print a range of lines from the Scintilla instance <em>qsb</em> using a default QPainter. <em>from</em> is the first line to print and a negative value signifies the first line of text. <em>to</em> is the last line to print and a negative value signifies the last line of text. true is returned if there was no error. </p>

</div>
</div>
<a id="ad67d67c266263dd2dbfe940b4ad98584"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad67d67c266263dd2dbfe940b4ad98584">&#9670;&nbsp;</a></span>wrapMode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classQsciScintilla.html#a7081c7ff25b5f6bd5b3a6cbd478a9f42">QsciScintilla::WrapMode</a> QsciPrinter::wrapMode </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Return the line wrap mode used when printing. The default is <a class="el" href="classQsciScintilla.html#a7081c7ff25b5f6bd5b3a6cbd478a9f42aeb6fe909aad13c04aab01c9c216ac2ec" title="Lines are wrapped at word boundaries.">QsciScintilla::WrapWord</a>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciPrinter.html#aa95827e3bd2c3c0e658afe55fa12476e">setWrapMode()</a> </dd></dl>

</div>
</div>
<a id="aa95827e3bd2c3c0e658afe55fa12476e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa95827e3bd2c3c0e658afe55fa12476e">&#9670;&nbsp;</a></span>setWrapMode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciPrinter::setWrapMode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classQsciScintilla.html#a7081c7ff25b5f6bd5b3a6cbd478a9f42">QsciScintilla::WrapMode</a>&#160;</td>
          <td class="paramname"><em>wmode</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sets the line wrap mode used when printing to <em>wmode</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciPrinter.html#ad67d67c266263dd2dbfe940b4ad98584">wrapMode()</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
