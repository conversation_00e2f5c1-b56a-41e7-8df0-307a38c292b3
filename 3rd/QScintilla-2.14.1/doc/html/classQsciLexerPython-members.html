<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciLexerPython Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciLexerPython.html">QsciLexerPython</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a305ec320aa2357947cbeb1608b95d840">autoCompletionWordSeparators</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">blockEnd</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#afe42ac5a09816340d4bec920b523aed6">blockLookback</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#adc66ee4b78453d245ac1b4dff45490f4">blockStart</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#ab30fa749a26490888fe18f2fcea47b02">braceStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>bytesAsText</b>(const char *bytes, int size) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a13a264a4745f895d9b8218a5eb834cab">ClassName</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a3ae64eb6b01ecf28c28cfa568456018e">Comment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301af439b3e4c0ee6762c95d318c457e9396">CommentBlock</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a40d923760d674229ffb146233d1cefae">Decorator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301ac55b65493dace8925090544c401e8556">Default</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a7320152a6d9098d07bba3da6c99a232e">defaultColor</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">QsciLexer::defaultColor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a855939c35d62798c00b0361a0edc41da">defaultEolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a7ea1d7ae4594027f8b565380f3fffbb4">defaultFont</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">QsciLexer::defaultFont</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a5e9de211c7e94a22da5c0d599a9e494b">defaultPaper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">QsciLexer::defaultPaper</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#aa3454a4c643cd0d479da8412341f1206">description</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301ac25e8cbcb38d2022ec108d2e22d97910">DoubleQuotedFString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a53439291c4ec1556fa2143b582b21457">DoubleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a>(bool eolfilled, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a1a7acaa356fdbefd26cfe0f30264c43a">foldComments</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a21891669bab4719e8e7cf482e3bf5a51">foldCompact</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a167dbdb42a4c0ed65229a3418153d4dd">foldQuotes</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a>(const QFont &amp;f, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301af182efea8f041750b774f01071af8b10">FunctionMethodName</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a6b6db1e2f565b7945e40fa3b13d2ce5a">HighlightedIdentifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#af8c28786c65b23583d92030ac606d07c">highlightSubidentifiers</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301ad3c089de016a822c21aadf0760842dbe">Identifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fca205cf6f451c495acbe224d2479c9b512">Inconsistent</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#acb5ec792032e6108b3c2d6ec6e565f49">indentationGuideView</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#aff624320c72fa3b433d82d6a558238e8">indentationWarning</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fc">IndentationWarning</a> enum name</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a5afb6ff3eda4e10420bc19d8cfce6697">Keyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a2467729449b6c78d63305b88b2f62789">keywords</a>(int set) const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#ae96690293b8128bea9cedf9b55b92ad6">language</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a9fe6e18dbb7ef4cad7f370286d7db0b7">lexer</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fcaaf2844c26e0b5337e85653ca39584182">NoWarning</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a828776730e146194bdc38d5baecd99b6">Number</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a7a4293d091128563c2b51f4eaade7ff2">Operator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a>(const char *prop, const char *val)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a158b80fd7ee649cbb618b1df33491bab">QsciLexerPython</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a1b8f36843f4abe6ec3ee75205b5b0111">readProperties</a>(QSettings &amp;qs, const QString &amp;prefix)</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#abed099316dd95a6289c76d151a37c264">refreshProperties</a>()</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a>(QsciAbstractAPIs *apis)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a>(int autoindentstyle)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a>(const QFont &amp;f)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>setEditor</b>(QsciScintilla *editor) (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a>(bool eoffill, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a35e71b31d8d197052c7c5250ff21f094">setFoldComments</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a27dcfdcac480d0360029d1f12b14f724">setFoldCompact</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#afc0aaf4300e9ca02eb8fa49328bbe8d8">setFoldQuotes</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a>(const QFont &amp;f, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#ade07472f3cc8a4cccbb0bb6b964f0356">setHighlightSubidentifiers</a>(bool enabled)</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a421ab12187730bc0686dc72710867ec3">setIndentationWarning</a>(QsciLexerPython::IndentationWarning warn)</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a5887a36e4a8d6ff54f4c796b33bc2eef">setStringsOverNewlineAllowed</a>(bool allowed)</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#accc3cd3ccf7d62840ded955400695b9d">setV2UnicodeAllowed</a>(bool allowed)</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#ae6bc53fc7e6dc90a80a26e22f6f49acb">setV3BinaryOctalAllowed</a>(bool allowed)</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a856785e000203b1da8fa6f295daad13e">setV3BytesAllowed</a>(bool allowed)</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301ace53a2a59f95bc733101f4e7e57d1974">SingleQuotedFString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301aacabc0f11d5b649fb4b4814018fbc2d7">SingleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fcac76a1a962494e9526e70eabaa648c75e">Spaces</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#aa4abeabae54373d536961d0aabb5ecdf">stringsOverNewlineAllowed</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fca5be72dba23dedfc6d0b0f796c3ba163d">Tabs</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fcaee65363fac7fe11d24582a68c3864686">TabsAfterSpaces</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>textAsBytes</b>(const QString &amp;text) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301ad1142de6be72ec89e7ce114412c97f2e">TripleDoubleQuotedFString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a1495ec67c855b00c949a8bd8672aa1bc">TripleDoubleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a25b848383c93ca55b77d6ef1cc6b0fbf">TripleSingleQuotedFString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301ad85722ad55aee4bf1966db4a7cfd2b32">TripleSingleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301ac5ef1d9860f88a3f84521ff88dca3878">UnclosedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#add088b1bd36b0d5eb0f3b87e403cec10">v2UnicodeAllowed</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#a02ad644d3bc229939e57d5e9f665a6b9">v3BinaryOctalAllowed</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a67308885b201ef6e21f0a273bf0b3c31">v3BytesAllowed</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aace68e3dbcef9da1b031fb9cfd843c57">wordCharacters</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPython.html#a8921849dce20c65c0fc024bc27255873">writeProperties</a>(QSettings &amp;qs, const QString &amp;prefix) const</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPython.html#abf0e76eca3bc604650cc20d4fc110c7f">~QsciLexerPython</a>()</td><td class="entry"><a class="el" href="classQsciLexerPython.html">QsciLexerPython</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
