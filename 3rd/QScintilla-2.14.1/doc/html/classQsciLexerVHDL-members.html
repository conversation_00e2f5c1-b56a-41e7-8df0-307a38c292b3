<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciLexerVHDL Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300ae259747dcdaff0bffe5da604d93ee4a5">Attribute</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">autoCompletionWordSeparators</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">blockEnd</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">blockStart</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aad362dd8a212974c01e61d12c8991b7f">braceStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bytesAsText</b>(const char *bytes, int size) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300ac22bd7eac094ca7e6f5ba2b0f65124ad">Comment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300a4e88c5013c5e1a80ecd777322b07d4ab">CommentBlock</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300a9e1bb162045d720665c7d463e3824476">CommentLine</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300a58b45bf1904760b66784b6193100237b">Default</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#a6dbcaf590be7759f18699593c95c69e6">defaultColor</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">QsciLexer::defaultColor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#a8ab227fcb9ba5da466b2d8eded96af70">defaultEolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#a7ea95f77a5a0ae539b306473c3b808db">defaultFont</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">QsciLexer::defaultFont</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#a2a1215dab25c15adf3c1bd6a5b063f91">defaultPaper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">QsciLexer::defaultPaper</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#a367d2a52388bd2602642f4b5dc01bba2">description</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a>(bool eolfilled, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#a6443ca03dcf722445e6627e9991bb10c">foldAtBegin</a>() const</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#a46e8e5909bfc92669cf155317ecb6fe9">foldAtElse</a>() const</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#afd8eebb9ee14760d1529f614f18a2e52">foldAtParenthesis</a>() const</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#ae2d2e6936f7b0f6f9b891ac14dff7bc0">foldComments</a>() const</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#a043411367c3fa915c8f4797cc51d0c8c">foldCompact</a>() const</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a>(const QFont &amp;f, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300ab72a8603cda3b24dfa6eeed5c6a7ca93">Identifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300a0e6155fe6a0e10f1301072cb73d5ecc5">Keyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aed2f3934c2fe336324d6e79526c2f7a8">keywords</a>(int set) const</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300ab519e29e6205cdeeb66d8d5e1e90a4d0">KeywordSet7</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#a2a8cd041dea81adb54a869c17ee4c8ba">language</a>() const</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#abf1a8dc25c7bd5d272c119d3c3e9e369">lexer</a>() const</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300a9b0cba57f797ebaf8d98eda980c171d7">Number</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300ab027e96558ca9bd809cb4032b1aeb1ce">Operator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a>(const char *prop, const char *val)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#a2260bd1206a91b7f9487e9ffe366732f">QsciLexerVHDL</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#a46a01d03d516e909c8696fa3f9910c1f">readProperties</a>(QSettings &amp;qs, const QString &amp;prefix)</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aa60e141b7b1a7aac51d79ad2c27c4c93">refreshProperties</a>()</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a>(QsciAbstractAPIs *apis)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a>(int autoindentstyle)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a>(const QFont &amp;f)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>setEditor</b>(QsciScintilla *editor) (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a>(bool eoffill, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#a7f8da8d7fe8301cd49926b896bf5e286">setFoldAtBegin</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#ae8c0599c4eb74db6caa8624bcc416a8b">setFoldAtElse</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#ad6328325f4c46dce0226712e9db3bba7">setFoldAtParenthesis</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#af41d62ccd061b840e3eb2e9e2b26d6f5">setFoldComments</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#a40d1ca24b672c13e9e7e69add2f5ee42">setFoldCompact</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a>(const QFont &amp;f, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300ac0e0b6c72ddc65750f5f0e347a212543">StandardFunction</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300a5aa1000c3189173cae05443b809e1471">StandardOperator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300a54f69bab09ed1818a5aab51fd3569531">StandardPackage</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300a341ea56a3223fe36e9d89157c6e3b1d5">StandardType</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300ad62daedcc5bd7ae90562a1f95a982f09">String</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>textAsBytes</b>(const QString &amp;text) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300af2479b7be15f744baac9ef19fa7bfc7e">UnclosedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aace68e3dbcef9da1b031fb9cfd843c57">wordCharacters</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVHDL.html#a0ed58ff3726deb2215eaff2c1892bc9b">writeProperties</a>(QSettings &amp;qs, const QString &amp;prefix) const</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVHDL.html#aab4dd4635d954113eecb698c46395d0b">~QsciLexerVHDL</a>()</td><td class="entry"><a class="el" href="classQsciLexerVHDL.html">QsciLexerVHDL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
