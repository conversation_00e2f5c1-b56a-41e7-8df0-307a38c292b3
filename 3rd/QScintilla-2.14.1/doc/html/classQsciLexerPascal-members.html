<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciLexerPascal Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfa03866519c0b3f2113793dead5db53daa">Asm</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#aa28fa3e32d5d4a4efccdad6655fb28c8">autoCompletionWordSeparators</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a9914377426e5e464f6d93ce2b64423a0">blockEnd</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a68d8b422b0d733592cc896086ca23652">blockStart</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#abe045873399199ba05d26e94c0e28aae">blockStartKeyword</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a4bd5b007424a8e88db37a326c0f154b5">braceStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bytesAsText</b>(const char *bytes, int size) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfae68a19ab0843b318915c86f7d353590c">Character</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfab48837000308dc11499d7e96f302db6a">Comment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfab4771b618f3f481962bc73d7d1e63cc5">CommentLine</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfabd390815063a649b2cab3f5da6d4b113">CommentParenthesis</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfa71c490d79223177530ef9415edf52747">Default</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#ad09e8331b90feeab761f845ac80e0b6d">defaultColor</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">QsciLexer::defaultColor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a45679bbf510fa7e0b264eb9654183f16">defaultEolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#a9c18ede5b5271ee1885b38083271aa9e">defaultFont</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">QsciLexer::defaultFont</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#a1a5b06231766e0f9a7364606a991c879">defaultPaper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">QsciLexer::defaultPaper</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#ab47735b5b8b7961044bb9adf111c06bc">description</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a>(bool eolfilled, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a30d1fae97aaef0b3fafab8e790caf130">foldComments</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#ae88783c3de2f0c4a0129e5bec77cc5ca">foldCompact</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#adae268febf025354165c88afa2414c73">foldPreprocessor</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a>(const QFont &amp;f, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfa12bdf1e4458d0e5aa6ba031eee1c81b2">HexNumber</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfa51dbc500bd8cc6a05ca0a04e9220e9b8">Identifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfa0df50778af3ef8ecbd584fca00d5337a">Keyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#a9b6f6a462314471262e5f29057839b34">keywords</a>(int set) const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a072c10d35abc0e56e09806eeb78ab66f">language</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#aebc02afb8158d445c4369efa287cc2ac">lexer</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfac3c433f5759c8996553c5e203077fe0d">Number</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfae83775614e9f03b1bb41d78023f0121a">Operator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfacfd2039caa846a92af36182615b36777">PreProcessor</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfa414d215a01c3d205a300976cf7f81556">PreProcessorParenthesis</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a>(const char *prop, const char *val)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#a9688ce1d302666e492900d3cdfcbbaab">QsciLexerPascal</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a2a2beba3b365e2e0e1f21109079f0ffd">readProperties</a>(QSettings &amp;qs, const QString &amp;prefix)</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a92cb96a2f9d373ed5a91546c42ec0905">refreshProperties</a>()</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a>(QsciAbstractAPIs *apis)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a>(int autoindentstyle)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a>(const QFont &amp;f)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>setEditor</b>(QsciScintilla *editor) (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a>(bool eoffill, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#a05d880fd1451f6a757fd21a7bd43a358">setFoldComments</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a2d183c40c276dadd3bbb994b0c0f26ce">setFoldCompact</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#a85c009c5ccf84fc64726bb2c3b11bdec">setFoldPreprocessor</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a>(const QFont &amp;f, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a64f021f45d10f2cfca72fda0c1d28e1f">setSmartHighlighting</a>(bool enabled)</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfae325b4d8dbeeb693c7b76b746ee81e81">SingleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a71fd025ad904aa51a6127f43099805ad">smartHighlighting</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>textAsBytes</b>(const QString &amp;text) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfa19098093aa64ac0fdca24cacb30983cf">UnclosedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#aace68e3dbcef9da1b031fb9cfd843c57">wordCharacters</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPascal.html#a6a5b21a2ba8b43a2f6b3747af365156f">writeProperties</a>(QSettings &amp;qs, const QString &amp;prefix) const</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPascal.html#a6ec94de07f31c33a6b08c297259e6b01">~QsciLexerPascal</a>()</td><td class="entry"><a class="el" href="classQsciLexerPascal.html">QsciLexerPascal</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
