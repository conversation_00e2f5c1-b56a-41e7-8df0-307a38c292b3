<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciLexerPostScript Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a129e6281661808e7d4d53025706abc77a68508fc322ed18c4eef49d6f3c562dcc">ArrayParenthesis</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">autoCompletionWordSeparators</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a129e6281661808e7d4d53025706abc77a1098fc215e68862126c9774419e218d4">BadStringCharacter</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a129e6281661808e7d4d53025706abc77ab0f0d6fc5daa0226c5632458e3b2a014">Base85String</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">blockEnd</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">blockStart</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a05f377a9017cf5f5d51deae3f1f83445">braceStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bytesAsText</b>(const char *bytes, int size) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a129e6281661808e7d4d53025706abc77a4b36bbf9fdf62e5e6433b96210b1290d">Comment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a129e6281661808e7d4d53025706abc77a7568c47b226595ed4d2853b1f4f07796">Default</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a68474df4d256e32296c5f09c243a55db">defaultColor</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">QsciLexer::defaultColor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a06228b73f8df699a211be872f54d8501">defaultEolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a774cfde4ca55ef85c506258b3c789c9d">defaultFont</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">QsciLexer::defaultFont</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a60519c0adb042373a1a79a73b68d7892">defaultPaper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">QsciLexer::defaultPaper</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a88492153c713084f4b5495ebe3bf1b40">description</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a129e6281661808e7d4d53025706abc77a836bd1fce6a26fa56815960d3442f1f8">DictionaryParenthesis</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a129e6281661808e7d4d53025706abc77a284328e010e8e4cbad238d5f4c423e30">DSCComment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a129e6281661808e7d4d53025706abc77a46e081b0e15a00de147c0f62fbaa630e">DSCCommentValue</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a>(bool eolfilled, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#aa86a61cd082e2e9fd76e878e8d6a096a">foldAtElse</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPostScript.html#accb7e71496e6817503ea1c081ffdbab4">foldCompact</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a>(const QFont &amp;f, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a129e6281661808e7d4d53025706abc77ae6d498c2db5cb31defd472e8b78e0b37">HexString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a129e6281661808e7d4d53025706abc77a6b15de06a212e4317328cb760561c55b">ImmediateEvalLiteral</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a129e6281661808e7d4d53025706abc77a78f5e4602dcc6279983499ed04b9ace5">Keyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a981f7ababe1cc561b29617fad8aa29b5">keywords</a>(int set) const</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPostScript.html#ab2d6a4d13e15769bf1110012b491ad90">language</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a78834f5080f50c01ba5ec1094114bf40">level</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a8f6156730e68c15fb63e120c53ce7832">lexer</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a129e6281661808e7d4d53025706abc77a5f93f977654892b0017dd0e990d77572">Literal</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a129e6281661808e7d4d53025706abc77a5b790b5d458c9e3b260c965ce9755ea3">Name</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a129e6281661808e7d4d53025706abc77a24c2fdd017eb9871220bc1cdf13c7675">Number</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a129e6281661808e7d4d53025706abc77a05a498ca8c50a19f88c7294e6b30ff88">ProcedureParenthesis</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a>(const char *prop, const char *val)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a7670a50d4dce21461de96844235b4242">QsciLexerPostScript</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a87168d5b174ba3a9b969ef689f67b355">readProperties</a>(QSettings &amp;qs, const QString &amp;prefix)</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a0f1e5402dce043de42ded75f5826588f">refreshProperties</a>()</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a>(QsciAbstractAPIs *apis)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a>(int autoindentstyle)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a>(const QFont &amp;f)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>setEditor</b>(QsciScintilla *editor) (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a>(bool eoffill, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPostScript.html#aa303817de5a59137ab4bf592ff52a315">setFoldAtElse</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a277a3c519eca4ef69d73fd45ea4f5ab5">setFoldCompact</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a>(const QFont &amp;f, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a80c198967862ff5392982a49b8004f48">setLevel</a>(int level)</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a8d57801958b738cbb297936426bb8c61">setTokenize</a>(bool tokenize)</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a129e6281661808e7d4d53025706abc77ad763c4ea8a19d8fc4ce9eb9297b8bcb2">Text</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>textAsBytes</b>(const QString &amp;text) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#ade1a77293facd468100a7c023dedcacc">tokenize</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aace68e3dbcef9da1b031fb9cfd843c57">wordCharacters</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPostScript.html#a0fc741a415b0419464afa66deb2b9e5d">writeProperties</a>(QSettings &amp;qs, const QString &amp;prefix) const</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPostScript.html#aa5f12cd587bf1b8db68813601cb57e5b">~QsciLexerPostScript</a>()</td><td class="entry"><a class="el" href="classQsciLexerPostScript.html">QsciLexerPostScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
