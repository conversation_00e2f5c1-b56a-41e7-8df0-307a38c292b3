<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciLexerD Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciLexerD.html">QsciLexerD</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a9080d0a47d2cbd972d5f2e6c737ba7fa">autoCompletionWordSeparators</a>() const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca682cc956cd52ccfff101565bd51327e1">BackquoteString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#af9f73f93dd57019e3335011528ad6aed">blockEnd</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a7ea79082a0d55e78cd3a60f1f05af6d9">blockStart</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#ae4490715b80237feaa25ad92d2fb6313">blockStartKeyword</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a24c82f4e0483ba0c13b8bf046b8c00b9">braceStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bytesAsText</b>(const char *bytes, int size) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4cadb30a6870a257c1e28e8534833583564">Character</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca37135c4d1129a47fe7d1fa4353c3ef89">Comment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4cae4ef72a2092606e60ebd48a41c728863">CommentDoc</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4caf5c6b133d2a0391d65dd11ca8cd0dc46">CommentDocKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca8416e006ed8c6157e87fddc9497b56ab">CommentDocKeywordError</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca25654940726025136d8e7eb8edf5b11a">CommentLine</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4cadf5911a2c4224ab8d38ea4ebe7747cb4">CommentLineDoc</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca5bc763858b98e6d4c43307986b548db3">CommentNested</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4caa4df8837f097ea5f0727c0399c96ed59">Default</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a3c22a819683d430aa99d23a80fedee73">defaultColor</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">QsciLexer::defaultColor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#ab55d105b2aa041682b67218fcdf964c6">defaultEolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a0315e5b984e4ecd8ae2b0131cb78bf95">defaultFont</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">QsciLexer::defaultFont</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#adcc24b17317e0e283230ae8d5ccf1de3">defaultPaper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">QsciLexer::defaultPaper</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a68f0cf388c3fa6a70ece2184020ffe55">description</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a>(bool eolfilled, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a0e5afa1027b99648caeb70ed8423af2d">foldAtElse</a>() const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a8c74012833091c1f71e2bea9d1a2a5d5">foldComments</a>() const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a02cb3518d6145815b22359d8d5aa2cf1">foldCompact</a>() const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a>(const QFont &amp;f, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca47923636d0ac5375823bbae9ae291f50">Identifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca1726b12d4c63e5ab08f4fd2076be8342">Keyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4caab5b406f8c633b7d63f3dfe5d7be2df8">KeywordDoc</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a9fc58fb17acc5e669780cb870d633514">keywords</a>(int set) const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca86aa2ed7ea2e1baaee37feac86b0bc09">KeywordSecondary</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca41699ff5135f73d77045b68748e881b0">KeywordSet5</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca8bbc63e31f19f9b6d7b50c1e1c2667b0">KeywordSet6</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca9f6ffdc183c1d99ce9fb0edce756410e">KeywordSet7</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a667febcf6234a15b7ca6d4ddbfb97bc6">language</a>() const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a62032a66c22767af46af4611fb672cb3">lexer</a>() const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca426d92387246d1fa5138b626a039b252">Number</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca146db058b940cab519bdfd046b14cc0f">Operator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a>(const char *prop, const char *val)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a8b64cf1677896ea7966338b3f10be14b">QsciLexerD</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca8f0a37846a48085a681eb744375efbc9">RawString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#abb94e0b0257a50dbde9b0ddbcfeb69d2">readProperties</a>(QSettings &amp;qs, const QString &amp;prefix)</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a3df48961344c5133ad595a555bbb8e55">refreshProperties</a>()</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a>(QsciAbstractAPIs *apis)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a>(int autoindentstyle)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a>(const QFont &amp;f)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>setEditor</b>(QsciScintilla *editor) (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a>(bool eoffill, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a2dc2ffcd977cf514e65e315a80afcb18">setFoldAtElse</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#aa7bcbfe8a9e732630bba54860888e9d5">setFoldComments</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a97c7813c68c861b9f2b3f068d9b47fd7">setFoldCompact</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a>(const QFont &amp;f, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca36b40f73931a76fb1845ddac7618c996">String</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>textAsBytes</b>(const QString &amp;text) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca066688031c2850c809c6e78751600f24">Typedefs</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca2f341004504fd3e8154b64e90090a5ca">UnclosedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a0436f412bb6c83fe195ea2eb3c058154">wordCharacters</a>() const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerD.html#a4d8069f6efaeba7c4fa810630bed2e2e">writeProperties</a>(QSettings &amp;qs, const QString &amp;prefix) const</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerD.html#a37506e7f15691f73445422beb341e750">~QsciLexerD</a>()</td><td class="entry"><a class="el" href="classQsciLexerD.html">QsciLexerD</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
