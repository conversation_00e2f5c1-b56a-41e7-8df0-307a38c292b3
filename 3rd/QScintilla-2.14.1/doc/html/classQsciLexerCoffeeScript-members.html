<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciLexerCoffeeScript Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a0741fad0b942deb73642be16c3159eb1">autoCompletionWordSeparators</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a1796c98b07ec6cfc3d5953c225cc1f37">blockEnd</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a32607d1ce220881542a049d83406ce65">BlockRegex</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8acf3a1887749e806e04bf6a0097f724bb">BlockRegexComment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a293f0a5c39990ec1db6de249dc618901">blockStart</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a497144db9b43beba78cd405a795e08ac">blockStartKeyword</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#add9b1d85d9da1c250f570482cd47eb39">braceStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>bytesAsText</b>(const char *bytes, int size) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a908ae06d736d3add37f734a255ceeaa3">Comment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a2050935e0699ccd6660987e5b6f42c32">CommentBlock</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a5edc735d0127917185abed1f637a49f7">CommentDoc</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a7e8b105503aff566abe10b78bfff1575">CommentDocKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a48d773f3fce4500a8700b6d76f2ecf24">CommentDocKeywordError</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a379b349ef6edd66b752af87472fe41b4">CommentLine</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a406ac7ec4b5186a2d33b7a9074f6fa02">CommentLineDoc</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a2fb70f93de6a55714777a4fa55916d03">Default</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#ab2e7d189deabf8e5e20434e32346742c">defaultColor</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">QsciLexer::defaultColor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#aad8c778b4c9ef2014e5a508f0ee52021">defaultEolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#ac84577ad9cdb480293fe6001e71085a8">defaultFont</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">QsciLexer::defaultFont</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a402a849f5eed391f0c4cd3aac9beb075">defaultPaper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">QsciLexer::defaultPaper</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#ace6bf74522c57e70f2c3ac525e1fd830">description</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a5b95ed33711b09385c92fbfb9f1d2a5d">dollarsAllowed</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a9eb257127f3fd60ea6ee7ef126419f7d">DoubleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a>(bool eolfilled, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a6865962a5df72e37f4ba49c6e5e539b6">foldComments</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a16d546ecc7d16a609e368a4d2d557605">foldCompact</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a>(const QFont &amp;f, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8ab4d4a5d44c3c3584609996183880c179">GlobalClass</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a6fada5b4984531d13a0f03cf9bd082f8">Identifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a67579947345d4b8bca1317e697fe46d3">InstanceProperty</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a4a6519d9d7b7e0e068d6ce8b777a87d2">Keyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a74867915ad9d609b9b516eff87101cc9">keywords</a>(int set) const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8ae9524bc4d07a86f58eb88d57c1291083">KeywordSet2</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a126d81ec982782507eafae1af5d0d856">language</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#aac009a767572be4b4489a0613611cbdb">lexer</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8aba8f2217a58a7603d2a69ea1edeb1bc4">Number</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8afd477421bbc3829c44d0ceda25ef07ec">Operator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a84af89605b0d39edc60401dee749d076">PreProcessor</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a>(const char *prop, const char *val)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a73e71780818247bf678616a25cd13e90">QsciLexerCoffeeScript</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#ae15b25b5d6705a850f6c93ee1013bea7">readProperties</a>(QSettings &amp;qs, const QString &amp;prefix)</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#aefae6df689f1d3dad66d1f2fc141cc39">refreshProperties</a>()</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8aca5c64a4bc75adb3be878a492906cfba">Regex</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a>(QsciAbstractAPIs *apis)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a>(int autoindentstyle)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a>(const QFont &amp;f)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#affaec4d14f7908f7d24d16937df00c93">setDollarsAllowed</a>(bool allowed)</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>setEditor</b>(QsciScintilla *editor) (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a>(bool eoffill, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a57f1f1164f3719b4b855a3a163a78764">setFoldComments</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a8bc6aee27994356e61fc6b030e23a62f">setFoldCompact</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a>(const QFont &amp;f, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#aa1949e1c7fd18507f664babab7b3c56c">setStylePreprocessor</a>(bool style)</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8ad64eca43c5aa797920a0b5db86c7ebb7">SingleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#aba02f4e299dd7f25cea762e9c21b48b2">stylePreprocessor</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>textAsBytes</b>(const QString &amp;text) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8ad66f62da8384e226b3c0e33455d93bd4">UnclosedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a063d7d591aa18ae40fcde793e1be1f01">UUID</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a6f653903645cf19e5ea1c7e870ae9efb">VerbatimString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#a2e5910796ca5a3f369258718bb75c1d8">wordCharacters</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#af2acfd7b7a9012577aed90f136ad3fb1">writeProperties</a>(QSettings &amp;qs, const QString &amp;prefix) const</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html#ab7715c2c90861e2601587b8a3a6732fd">~QsciLexerCoffeeScript</a>()</td><td class="entry"><a class="el" href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
