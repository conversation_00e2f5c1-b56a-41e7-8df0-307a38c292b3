<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciLexerD Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-slots">Public Slots</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="classQsciLexerD-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciLexerD Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscilexerd.h&gt;</code></p>

<p>Inherits <a class="el" href="classQsciLexer.html">QsciLexer</a>.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a28ee24ad206c9acbcd2901f9b64faf4c"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4caa4df8837f097ea5f0727c0399c96ed59">Default</a> = 0, 
<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca37135c4d1129a47fe7d1fa4353c3ef89">Comment</a> = 1, 
<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca25654940726025136d8e7eb8edf5b11a">CommentLine</a> = 2, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4cae4ef72a2092606e60ebd48a41c728863">CommentDoc</a> = 3, 
<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca5bc763858b98e6d4c43307986b548db3">CommentNested</a> = 4, 
<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca426d92387246d1fa5138b626a039b252">Number</a> = 5, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca1726b12d4c63e5ab08f4fd2076be8342">Keyword</a> = 6, 
<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca86aa2ed7ea2e1baaee37feac86b0bc09">KeywordSecondary</a> = 7, 
<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4caab5b406f8c633b7d63f3dfe5d7be2df8">KeywordDoc</a> = 8, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca066688031c2850c809c6e78751600f24">Typedefs</a> = 9, 
<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca36b40f73931a76fb1845ddac7618c996">String</a> = 10, 
<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca2f341004504fd3e8154b64e90090a5ca">UnclosedString</a> = 11, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4cadb30a6870a257c1e28e8534833583564">Character</a> = 12, 
<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca146db058b940cab519bdfd046b14cc0f">Operator</a> = 13, 
<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca47923636d0ac5375823bbae9ae291f50">Identifier</a> = 14, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4cadf5911a2c4224ab8d38ea4ebe7747cb4">CommentLineDoc</a> = 15, 
<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4caf5c6b133d2a0391d65dd11ca8cd0dc46">CommentDocKeyword</a> = 16, 
<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca8416e006ed8c6157e87fddc9497b56ab">CommentDocKeywordError</a> = 17, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca682cc956cd52ccfff101565bd51327e1">BackquoteString</a> = 18, 
<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca8f0a37846a48085a681eb744375efbc9">RawString</a> = 19, 
<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca41699ff5135f73d77045b68748e881b0">KeywordSet5</a> = 20, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca8bbc63e31f19f9b6d7b50c1e1c2667b0">KeywordSet6</a> = 21, 
<a class="el" href="classQsciLexerD.html#a28ee24ad206c9acbcd2901f9b64faf4ca9f6ffdc183c1d99ce9fb0edce756410e">KeywordSet7</a> = 22
<br />
 }</td></tr>
<tr class="separator:a28ee24ad206c9acbcd2901f9b64faf4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-slots"></a>
Public Slots</h2></td></tr>
<tr class="memitem:a2dc2ffcd977cf514e65e315a80afcb18"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a2dc2ffcd977cf514e65e315a80afcb18">setFoldAtElse</a> (bool fold)</td></tr>
<tr class="separator:a2dc2ffcd977cf514e65e315a80afcb18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa7bcbfe8a9e732630bba54860888e9d5"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#aa7bcbfe8a9e732630bba54860888e9d5">setFoldComments</a> (bool fold)</td></tr>
<tr class="separator:aa7bcbfe8a9e732630bba54860888e9d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a97c7813c68c861b9f2b3f068d9b47fd7"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a97c7813c68c861b9f2b3f068d9b47fd7">setFoldCompact</a> (bool fold)</td></tr>
<tr class="separator:a97c7813c68c861b9f2b3f068d9b47fd7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_slots_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_slots_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Slots inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a> (int autoindentstyle)</td></tr>
<tr class="separator:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a> (bool eoffill, int style=-1)</td></tr>
<tr class="separator:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a> (const QFont &amp;f, int style=-1)</td></tr>
<tr class="separator:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a8b64cf1677896ea7966338b3f10be14b"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a8b64cf1677896ea7966338b3f10be14b">QsciLexerD</a> (QObject *parent=0)</td></tr>
<tr class="separator:a8b64cf1677896ea7966338b3f10be14b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a37506e7f15691f73445422beb341e750"><td class="memItemLeft" align="right" valign="top"><a id="a37506e7f15691f73445422beb341e750"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a37506e7f15691f73445422beb341e750">~QsciLexerD</a> ()</td></tr>
<tr class="separator:a37506e7f15691f73445422beb341e750"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a667febcf6234a15b7ca6d4ddbfb97bc6"><td class="memItemLeft" align="right" valign="top"><a id="a667febcf6234a15b7ca6d4ddbfb97bc6"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a667febcf6234a15b7ca6d4ddbfb97bc6">language</a> () const</td></tr>
<tr class="separator:a667febcf6234a15b7ca6d4ddbfb97bc6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a62032a66c22767af46af4611fb672cb3"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a62032a66c22767af46af4611fb672cb3">lexer</a> () const</td></tr>
<tr class="separator:a62032a66c22767af46af4611fb672cb3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9080d0a47d2cbd972d5f2e6c737ba7fa"><td class="memItemLeft" align="right" valign="top">QStringList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a9080d0a47d2cbd972d5f2e6c737ba7fa">autoCompletionWordSeparators</a> () const</td></tr>
<tr class="separator:a9080d0a47d2cbd972d5f2e6c737ba7fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af9f73f93dd57019e3335011528ad6aed"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#af9f73f93dd57019e3335011528ad6aed">blockEnd</a> (int *style=0) const</td></tr>
<tr class="separator:af9f73f93dd57019e3335011528ad6aed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ea79082a0d55e78cd3a60f1f05af6d9"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a7ea79082a0d55e78cd3a60f1f05af6d9">blockStart</a> (int *style=0) const</td></tr>
<tr class="separator:a7ea79082a0d55e78cd3a60f1f05af6d9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae4490715b80237feaa25ad92d2fb6313"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#ae4490715b80237feaa25ad92d2fb6313">blockStartKeyword</a> (int *style=0) const</td></tr>
<tr class="separator:ae4490715b80237feaa25ad92d2fb6313"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a24c82f4e0483ba0c13b8bf046b8c00b9"><td class="memItemLeft" align="right" valign="top"><a id="a24c82f4e0483ba0c13b8bf046b8c00b9"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a24c82f4e0483ba0c13b8bf046b8c00b9">braceStyle</a> () const</td></tr>
<tr class="separator:a24c82f4e0483ba0c13b8bf046b8c00b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0436f412bb6c83fe195ea2eb3c058154"><td class="memItemLeft" align="right" valign="top"><a id="a0436f412bb6c83fe195ea2eb3c058154"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a0436f412bb6c83fe195ea2eb3c058154">wordCharacters</a> () const</td></tr>
<tr class="separator:a0436f412bb6c83fe195ea2eb3c058154"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3c22a819683d430aa99d23a80fedee73"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a3c22a819683d430aa99d23a80fedee73">defaultColor</a> (int style) const</td></tr>
<tr class="separator:a3c22a819683d430aa99d23a80fedee73"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab55d105b2aa041682b67218fcdf964c6"><td class="memItemLeft" align="right" valign="top"><a id="ab55d105b2aa041682b67218fcdf964c6"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#ab55d105b2aa041682b67218fcdf964c6">defaultEolFill</a> (int style) const</td></tr>
<tr class="separator:ab55d105b2aa041682b67218fcdf964c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0315e5b984e4ecd8ae2b0131cb78bf95"><td class="memItemLeft" align="right" valign="top"><a id="a0315e5b984e4ecd8ae2b0131cb78bf95"></a>
QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a0315e5b984e4ecd8ae2b0131cb78bf95">defaultFont</a> (int style) const</td></tr>
<tr class="separator:a0315e5b984e4ecd8ae2b0131cb78bf95"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adcc24b17317e0e283230ae8d5ccf1de3"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#adcc24b17317e0e283230ae8d5ccf1de3">defaultPaper</a> (int style) const</td></tr>
<tr class="separator:adcc24b17317e0e283230ae8d5ccf1de3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9fc58fb17acc5e669780cb870d633514"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a9fc58fb17acc5e669780cb870d633514">keywords</a> (int set) const</td></tr>
<tr class="separator:a9fc58fb17acc5e669780cb870d633514"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a68f0cf388c3fa6a70ece2184020ffe55"><td class="memItemLeft" align="right" valign="top">QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a68f0cf388c3fa6a70ece2184020ffe55">description</a> (int style) const</td></tr>
<tr class="separator:a68f0cf388c3fa6a70ece2184020ffe55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3df48961344c5133ad595a555bbb8e55"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a3df48961344c5133ad595a555bbb8e55">refreshProperties</a> ()</td></tr>
<tr class="separator:a3df48961344c5133ad595a555bbb8e55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e5afa1027b99648caeb70ed8423af2d"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a0e5afa1027b99648caeb70ed8423af2d">foldAtElse</a> () const</td></tr>
<tr class="separator:a0e5afa1027b99648caeb70ed8423af2d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8c74012833091c1f71e2bea9d1a2a5d5"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a8c74012833091c1f71e2bea9d1a2a5d5">foldComments</a> () const</td></tr>
<tr class="separator:a8c74012833091c1f71e2bea9d1a2a5d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a02cb3518d6145815b22359d8d5aa2cf1"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a02cb3518d6145815b22359d8d5aa2cf1">foldCompact</a> () const</td></tr>
<tr class="separator:a02cb3518d6145815b22359d8d5aa2cf1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a> (QObject *parent=0)</td></tr>
<tr class="separator:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="af6cc5bb9d9421d806e9941d018030068"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a> ()</td></tr>
<tr class="separator:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a> () const</td></tr>
<tr class="separator:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a> () const</td></tr>
<tr class="separator:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a6504a6fff35af16fbfd97889048db2a5"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a> () const</td></tr>
<tr class="separator:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a> ()</td></tr>
<tr class="separator:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a> () const</td></tr>
<tr class="separator:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="afccca7eb1aed463f89ac442d99135839"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a> () const</td></tr>
<tr class="separator:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a> (int style) const</td></tr>
<tr class="separator:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a> (int style) const</td></tr>
<tr class="separator:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a> (int style) const</td></tr>
<tr class="separator:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="aff4735542e937c5e35ecb2eb82e8f875"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a> () const</td></tr>
<tr class="separator:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a> () const</td></tr>
<tr class="separator:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a> (int style) const</td></tr>
<tr class="separator:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">defaultColor</a> () const</td></tr>
<tr class="separator:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">defaultFont</a> () const</td></tr>
<tr class="separator:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">defaultPaper</a> () const</td></tr>
<tr class="separator:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciScintilla.html">QsciScintilla</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a> () const</td></tr>
<tr class="separator:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a> (<a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *<a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>)</td></tr>
<tr class="separator:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a> (const QFont &amp;f)</td></tr>
<tr class="separator:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a1e81186b1f8f8bc2a4901a42cbca568a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>setEditor</b> (<a class="el" href="classQsciScintilla.html">QsciScintilla</a> *<a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>)</td></tr>
<tr class="separator:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td></tr>
<tr class="separator:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a> () const</td></tr>
<tr class="separator:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td></tr>
<tr class="separator:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:abb94e0b0257a50dbde9b0ddbcfeb69d2"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#abb94e0b0257a50dbde9b0ddbcfeb69d2">readProperties</a> (QSettings &amp;qs, const QString &amp;prefix)</td></tr>
<tr class="separator:abb94e0b0257a50dbde9b0ddbcfeb69d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4d8069f6efaeba7c4fa810630bed2e2e"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerD.html#a4d8069f6efaeba7c4fa810630bed2e2e">writeProperties</a> (QSettings &amp;qs, const QString &amp;prefix) const</td></tr>
<tr class="separator:a4d8069f6efaeba7c4fa810630bed2e2e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pro_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a41d4521504d63ee63d43fd7ed0c003ee"></a>
QByteArray&#160;</td><td class="memItemRight" valign="bottom"><b>textAsBytes</b> (const QString &amp;text) const</td></tr>
<tr class="separator:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a5261dd606c209a5c6a494e608a9a111a"></a>
QString&#160;</td><td class="memItemRight" valign="bottom"><b>bytesAsText</b> (const char *bytes, int size) const</td></tr>
<tr class="separator:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header signals_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('signals_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Signals inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a> (bool eolfilled, int style)</td></tr>
<tr class="separator:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a> (const QFont &amp;f, int style)</td></tr>
<tr class="separator:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a> (const char *prop, const char *val)</td></tr>
<tr class="separator:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciLexerD.html" title="The QsciLexerD class encapsulates the Scintilla D lexer.">QsciLexerD</a> class encapsulates the Scintilla D lexer. </p>
</div><h2 class="groupheader">Member Enumeration Documentation</h2>
<a id="a28ee24ad206c9acbcd2901f9b64faf4c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a28ee24ad206c9acbcd2901f9b64faf4c">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the meanings of the different styles used by the D lexer. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4caa4df8837f097ea5f0727c0399c96ed59"></a>Default&#160;</td><td class="fielddoc"><p>The default. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4ca37135c4d1129a47fe7d1fa4353c3ef89"></a>Comment&#160;</td><td class="fielddoc"><p>A comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4ca25654940726025136d8e7eb8edf5b11a"></a>CommentLine&#160;</td><td class="fielddoc"><p>A comment line. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4cae4ef72a2092606e60ebd48a41c728863"></a>CommentDoc&#160;</td><td class="fielddoc"><p>A JavaDoc and Doxygen comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4ca5bc763858b98e6d4c43307986b548db3"></a>CommentNested&#160;</td><td class="fielddoc"><p>A nested comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4ca426d92387246d1fa5138b626a039b252"></a>Number&#160;</td><td class="fielddoc"><p>A number. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4ca1726b12d4c63e5ab08f4fd2076be8342"></a>Keyword&#160;</td><td class="fielddoc"><p>A keyword. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4ca86aa2ed7ea2e1baaee37feac86b0bc09"></a>KeywordSecondary&#160;</td><td class="fielddoc"><p>A secondary keyword. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4caab5b406f8c633b7d63f3dfe5d7be2df8"></a>KeywordDoc&#160;</td><td class="fielddoc"><p>A doc keyword. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4ca066688031c2850c809c6e78751600f24"></a>Typedefs&#160;</td><td class="fielddoc"><p>Typedefs and aliases. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4ca36b40f73931a76fb1845ddac7618c996"></a>String&#160;</td><td class="fielddoc"><p>A string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4ca2f341004504fd3e8154b64e90090a5ca"></a>UnclosedString&#160;</td><td class="fielddoc"><p>The end of a line where a string is not closed. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4cadb30a6870a257c1e28e8534833583564"></a>Character&#160;</td><td class="fielddoc"><p>A character. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4ca146db058b940cab519bdfd046b14cc0f"></a>Operator&#160;</td><td class="fielddoc"><p>An operator. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4ca47923636d0ac5375823bbae9ae291f50"></a>Identifier&#160;</td><td class="fielddoc"><p>An identifier. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4cadf5911a2c4224ab8d38ea4ebe7747cb4"></a>CommentLineDoc&#160;</td><td class="fielddoc"><p>A JavaDoc and Doxygen line. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4caf5c6b133d2a0391d65dd11ca8cd0dc46"></a>CommentDocKeyword&#160;</td><td class="fielddoc"><p>A JavaDoc and Doxygen keyword. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4ca8416e006ed8c6157e87fddc9497b56ab"></a>CommentDocKeywordError&#160;</td><td class="fielddoc"><p>A JavaDoc and Doxygen keyword error. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4ca682cc956cd52ccfff101565bd51327e1"></a>BackquoteString&#160;</td><td class="fielddoc"><p>A backquoted string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4ca8f0a37846a48085a681eb744375efbc9"></a>RawString&#160;</td><td class="fielddoc"><p>A raw, hexadecimal or delimited string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4ca41699ff5135f73d77045b68748e881b0"></a>KeywordSet5&#160;</td><td class="fielddoc"><p>A keyword defined in keyword set number 5. The class must be sub-classed and re-implement <a class="el" href="classQsciLexerD.html#a9fc58fb17acc5e669780cb870d633514">keywords()</a> to make use of this style. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4ca8bbc63e31f19f9b6d7b50c1e1c2667b0"></a>KeywordSet6&#160;</td><td class="fielddoc"><p>A keyword defined in keyword set number 6. The class must be sub-classed and re-implement <a class="el" href="classQsciLexerD.html#a9fc58fb17acc5e669780cb870d633514">keywords()</a> to make use of this style. </p>
</td></tr>
<tr><td class="fieldname"><a id="a28ee24ad206c9acbcd2901f9b64faf4ca9f6ffdc183c1d99ce9fb0edce756410e"></a>KeywordSet7&#160;</td><td class="fielddoc"><p>A keyword defined in keyword set number 7. The class must be sub-classed and re-implement <a class="el" href="classQsciLexerD.html#a9fc58fb17acc5e669780cb870d633514">keywords()</a> to make use of this style. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a8b64cf1677896ea7966338b3f10be14b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8b64cf1677896ea7966338b3f10be14b">&#9670;&nbsp;</a></span>QsciLexerD()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciLexerD::QsciLexerD </td>
          <td>(</td>
          <td class="paramtype">QObject *&#160;</td>
          <td class="paramname"><em>parent</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Construct a <a class="el" href="classQsciLexerD.html" title="The QsciLexerD class encapsulates the Scintilla D lexer.">QsciLexerD</a> with parent <em>parent</em>. <em>parent</em> is typically the <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> instance. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a62032a66c22767af46af4611fb672cb3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a62032a66c22767af46af4611fb672cb3">&#9670;&nbsp;</a></span>lexer()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerD::lexer </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the name of the lexer. Some lexers support a number of languages. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ac7c40b97187e23ab85f6d95113f91b39">QsciLexer</a>.</p>

</div>
</div>
<a id="a9080d0a47d2cbd972d5f2e6c737ba7fa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9080d0a47d2cbd972d5f2e6c737ba7fa">&#9670;&nbsp;</a></span>autoCompletionWordSeparators()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QStringList QsciLexerD::autoCompletionWordSeparators </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the list of character sequences that can separate auto-completion words. The first in the list is assumed to be the sequence used to separate words in the lexer's API files. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">QsciLexer</a>.</p>

</div>
</div>
<a id="af9f73f93dd57019e3335011528ad6aed"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af9f73f93dd57019e3335011528ad6aed">&#9670;&nbsp;</a></span>blockEnd()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerD::blockEnd </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of words or characters in a particular style that define the end of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">QsciLexer</a>.</p>

</div>
</div>
<a id="a7ea79082a0d55e78cd3a60f1f05af6d9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7ea79082a0d55e78cd3a60f1f05af6d9">&#9670;&nbsp;</a></span>blockStart()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerD::blockStart </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of words or characters in a particular style that define the start of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">QsciLexer</a>.</p>

</div>
</div>
<a id="ae4490715b80237feaa25ad92d2fb6313"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae4490715b80237feaa25ad92d2fb6313">&#9670;&nbsp;</a></span>blockStartKeyword()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerD::blockStartKeyword </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of keywords in a particular style that define the start of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">QsciLexer</a>.</p>

</div>
</div>
<a id="a3c22a819683d430aa99d23a80fedee73"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3c22a819683d430aa99d23a80fedee73">&#9670;&nbsp;</a></span>defaultColor()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerD::defaultColor </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the foreground colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerD.html#adcc24b17317e0e283230ae8d5ccf1de3">defaultPaper()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#af7508f1b816a2c9446d36141edc9b5ce">QsciLexer</a>.</p>

</div>
</div>
<a id="adcc24b17317e0e283230ae8d5ccf1de3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adcc24b17317e0e283230ae8d5ccf1de3">&#9670;&nbsp;</a></span>defaultPaper()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerD::defaultPaper </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the background colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerD.html#a3c22a819683d430aa99d23a80fedee73">defaultColor()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a7e5ab7f541d913760c32abedbdc72963">QsciLexer</a>.</p>

</div>
</div>
<a id="a9fc58fb17acc5e669780cb870d633514"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9fc58fb17acc5e669780cb870d633514">&#9670;&nbsp;</a></span>keywords()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerD::keywords </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>set</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the set of keywords for the keyword set <em>set</em> recognised by the lexer as a space separated string. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a013b7c1bf9846e231b97827dfd9540b0">QsciLexer</a>.</p>

</div>
</div>
<a id="a68f0cf388c3fa6a70ece2184020ffe55"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a68f0cf388c3fa6a70ece2184020ffe55">&#9670;&nbsp;</a></span>description()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QString QsciLexerD::description </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the descriptive name for style number <em>style</em>. If the style is invalid for this language then an empty QString is returned. This is intended to be used in user preference dialogs. </p>

<p>Implements <a class="el" href="classQsciLexer.html#add9c20adb43bc38d1a0ca3083ac3e6fa">QsciLexer</a>.</p>

</div>
</div>
<a id="a3df48961344c5133ad595a555bbb8e55"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3df48961344c5133ad595a555bbb8e55">&#9670;&nbsp;</a></span>refreshProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerD::refreshProperties </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Causes all properties to be refreshed by emitting the <a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged()</a> signal as required. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ae508c3ab4ce1f338dfff3ddf5ee7e34c">QsciLexer</a>.</p>

</div>
</div>
<a id="a0e5afa1027b99648caeb70ed8423af2d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0e5afa1027b99648caeb70ed8423af2d">&#9670;&nbsp;</a></span>foldAtElse()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerD::foldAtElse </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if "} else {" lines can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerD.html#a2dc2ffcd977cf514e65e315a80afcb18">setFoldAtElse()</a> </dd></dl>

</div>
</div>
<a id="a8c74012833091c1f71e2bea9d1a2a5d5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8c74012833091c1f71e2bea9d1a2a5d5">&#9670;&nbsp;</a></span>foldComments()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerD::foldComments </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if multi-line comment blocks can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerD.html#aa7bcbfe8a9e732630bba54860888e9d5">setFoldComments()</a> </dd></dl>

</div>
</div>
<a id="a02cb3518d6145815b22359d8d5aa2cf1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a02cb3518d6145815b22359d8d5aa2cf1">&#9670;&nbsp;</a></span>foldCompact()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerD::foldCompact </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if trailing blank lines are included in a fold block.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerD.html#a97c7813c68c861b9f2b3f068d9b47fd7">setFoldCompact()</a> </dd></dl>

</div>
</div>
<a id="a2dc2ffcd977cf514e65e315a80afcb18"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2dc2ffcd977cf514e65e315a80afcb18">&#9670;&nbsp;</a></span>setFoldAtElse</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerD::setFoldAtElse </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then "} else {" lines can be folded. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerD.html#a0e5afa1027b99648caeb70ed8423af2d">foldAtElse()</a> </dd></dl>

</div>
</div>
<a id="aa7bcbfe8a9e732630bba54860888e9d5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa7bcbfe8a9e732630bba54860888e9d5">&#9670;&nbsp;</a></span>setFoldComments</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerD::setFoldComments </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then multi-line comment blocks can be folded. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerD.html#a8c74012833091c1f71e2bea9d1a2a5d5">foldComments()</a> </dd></dl>

</div>
</div>
<a id="a97c7813c68c861b9f2b3f068d9b47fd7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a97c7813c68c861b9f2b3f068d9b47fd7">&#9670;&nbsp;</a></span>setFoldCompact</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerD::setFoldCompact </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then trailing blank lines are included in a fold block. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerD.html#a02cb3518d6145815b22359d8d5aa2cf1">foldCompact()</a> </dd></dl>

</div>
</div>
<a id="abb94e0b0257a50dbde9b0ddbcfeb69d2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abb94e0b0257a50dbde9b0ddbcfeb69d2">&#9670;&nbsp;</a></span>readProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerD::readProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are read from the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerD.html#a4d8069f6efaeba7c4fa810630bed2e2e">writeProperties()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ad472b16506a4cbc19634f07aa90f1ea6">QsciLexer</a>.</p>

</div>
</div>
<a id="a4d8069f6efaeba7c4fa810630bed2e2e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4d8069f6efaeba7c4fa810630bed2e2e">&#9670;&nbsp;</a></span>writeProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerD::writeProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are written to the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerD.html#abb94e0b0257a50dbde9b0ddbcfeb69d2">readProperties()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#abccc4e010b724df1a7b5c5f3bce29501">QsciLexer</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
