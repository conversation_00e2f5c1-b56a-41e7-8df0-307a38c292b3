<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_s"></a>- s -</h3><ul>
<li>QsciScintilla()
: <a class="el" href="classQsciScintilla.html#a978b6679ccd9d9edb6091502001a5f45">QsciScintilla</a>
</li>
<li>QsciScintillaBase()
: <a class="el" href="classQsciScintillaBase.html#a425344ca700d69b60ffeb3f8122f7ff9">QsciScintillaBase</a>
</li>
<li>QsciStyle()
: <a class="el" href="classQsciStyle.html#a0464f0a24f4094431686c89e667e843e">QsciStyle</a>
</li>
<li>QsciStyledText()
: <a class="el" href="classQsciStyledText.html#a72dbd9d847a577fe5c438d1582920887">QsciStyledText</a>
</li>
<li>save()
: <a class="el" href="classQsciMacro.html#a6af9c876a10d746177790189067aaf6a">QsciMacro</a>
</li>
<li>savePrepared()
: <a class="el" href="classQsciAPIs.html#a742609f12e48e63edbab2565d7df3cb9">QsciAPIs</a>
</li>
<li>SCEN_CHANGE()
: <a class="el" href="classQsciScintillaBase.html#af2cc3652d35b4d0ec1d8c9ac18e2225e">QsciScintillaBase</a>
</li>
<li>SCN_AUTOCCANCELLED()
: <a class="el" href="classQsciScintillaBase.html#a1719fba80d9e60cf9fce1bb75f304568">QsciScintillaBase</a>
</li>
<li>SCN_AUTOCCHARDELETED()
: <a class="el" href="classQsciScintillaBase.html#aabab23e5653c35dae8a6f144d73c4657">QsciScintillaBase</a>
</li>
<li>SCN_AUTOCCOMPLETED()
: <a class="el" href="classQsciScintillaBase.html#a41e738411112b8f509e0b49b6fc3e318">QsciScintillaBase</a>
</li>
<li>SCN_AUTOCSELECTION()
: <a class="el" href="classQsciScintillaBase.html#a61c43c53a753272c51c5c5ac14bda136">QsciScintillaBase</a>
</li>
<li>SCN_AUTOCSELECTIONCHANGE()
: <a class="el" href="classQsciScintillaBase.html#a721a1879cabaa76883ae1a02a34a76e8">QsciScintillaBase</a>
</li>
<li>SCN_CALLTIPCLICK()
: <a class="el" href="classQsciScintillaBase.html#a13f22ec5a59e2e8e97a27ac24967f74d">QsciScintillaBase</a>
</li>
<li>SCN_CHARADDED()
: <a class="el" href="classQsciScintillaBase.html#ae8d8fa5d5f063a7c7d37d527f86b5fe8">QsciScintillaBase</a>
</li>
<li>SCN_DOUBLECLICK()
: <a class="el" href="classQsciScintillaBase.html#ad3ca5787399ed886cb9000c8feab3c08">QsciScintillaBase</a>
</li>
<li>SCN_DWELLEND()
: <a class="el" href="classQsciScintillaBase.html#a9ecd605284870ddbf703cf4c8c995ca6">QsciScintillaBase</a>
</li>
<li>SCN_DWELLSTART()
: <a class="el" href="classQsciScintillaBase.html#adfd788dce5c1a91d1fcd5e6fdd2fca59">QsciScintillaBase</a>
</li>
<li>SCN_FOCUSIN()
: <a class="el" href="classQsciScintillaBase.html#ae53947625062cec64a212dc68877ddc3">QsciScintillaBase</a>
</li>
<li>SCN_FOCUSOUT()
: <a class="el" href="classQsciScintillaBase.html#a2febc4ea74d45d6a8bc9c758635dd99d">QsciScintillaBase</a>
</li>
<li>SCN_HOTSPOTCLICK()
: <a class="el" href="classQsciScintillaBase.html#a5eff383e6fa96cbbaba6a2558b076c0b">QsciScintillaBase</a>
</li>
<li>SCN_HOTSPOTDOUBLECLICK()
: <a class="el" href="classQsciScintillaBase.html#a682cc736272338433efdc86bc936e0e8">QsciScintillaBase</a>
</li>
<li>SCN_HOTSPOTRELEASECLICK()
: <a class="el" href="classQsciScintillaBase.html#a906faecb0defd2d5a14cac54f8415dcf">QsciScintillaBase</a>
</li>
<li>SCN_INDICATORCLICK()
: <a class="el" href="classQsciScintillaBase.html#aeec8d7e585e93451307df88ff2fc2b87">QsciScintillaBase</a>
</li>
<li>SCN_INDICATORRELEASE()
: <a class="el" href="classQsciScintillaBase.html#a93d1e96c88745ca7f2737602e80dc76a">QsciScintillaBase</a>
</li>
<li>SCN_MACRORECORD()
: <a class="el" href="classQsciScintillaBase.html#abdae368f2b81955c4927dc6f26fc2c77">QsciScintillaBase</a>
</li>
<li>SCN_MARGINCLICK()
: <a class="el" href="classQsciScintillaBase.html#a722a2f16b67ef5f46def6914a6e178c3">QsciScintillaBase</a>
</li>
<li>SCN_MARGINRIGHTCLICK()
: <a class="el" href="classQsciScintillaBase.html#a39e90958ae903d2f6198ec0c58f56ed9">QsciScintillaBase</a>
</li>
<li>SCN_MODIFYATTEMPTRO()
: <a class="el" href="classQsciScintillaBase.html#adb5bad7d1dad9ab3fe74adb3e0812969">QsciScintillaBase</a>
</li>
<li>SCN_PAINTED()
: <a class="el" href="classQsciScintillaBase.html#a94a1cff08b2ef6558d054177fa88ea47">QsciScintillaBase</a>
</li>
<li>SCN_SAVEPOINTLEFT()
: <a class="el" href="classQsciScintillaBase.html#af3a619a5e59cef000f0b550e809c94de">QsciScintillaBase</a>
</li>
<li>SCN_SAVEPOINTREACHED()
: <a class="el" href="classQsciScintillaBase.html#a0db8c3ad0764a96f3ccf0fec71de0d26">QsciScintillaBase</a>
</li>
<li>SCN_STYLENEEDED()
: <a class="el" href="classQsciScintillaBase.html#a72c0bc1c83fd675714626cd786ca4fb9">QsciScintillaBase</a>
</li>
<li>SCN_UPDATEUI()
: <a class="el" href="classQsciScintillaBase.html#ad88db21d86df33667c234d00af1fdf94">QsciScintillaBase</a>
</li>
<li>SCN_URIDROPPED()
: <a class="el" href="classQsciScintillaBase.html#a42cb45ea05c71180a594e0cc8041c07d">QsciScintillaBase</a>
</li>
<li>SCN_USERLISTSELECTION()
: <a class="el" href="classQsciScintillaBase.html#a8225643b25dc6f1dedc48b4a7af4b83d">QsciScintillaBase</a>
</li>
<li>scriptsStyled()
: <a class="el" href="classQsciLexerXML.html#a96ad1f818e51a3606404d24bf7a28a91">QsciLexerXML</a>
</li>
<li>scrollWidth()
: <a class="el" href="classQsciScintilla.html#ad5fa8715b931fc0143aa72a20420578d">QsciScintilla</a>
</li>
<li>scrollWidthTracking()
: <a class="el" href="classQsciScintilla.html#abf895c5e4157e4b6effd28683c728b63">QsciScintilla</a>
</li>
<li>SCSSLanguage()
: <a class="el" href="classQsciLexerCSS.html#ae8630fee6378af65bbd772b8f20fe4c9">QsciLexerCSS</a>
</li>
<li>selectAll()
: <a class="el" href="classQsciScintilla.html#a8aae5a0037937ad6c8bdfe868e4a8ad5">QsciScintilla</a>
</li>
<li>selectedText()
: <a class="el" href="classQsciScintilla.html#a10c8d8f5e97fb5ef86ea351407fe1023">QsciScintilla</a>
</li>
<li>selectionChanged()
: <a class="el" href="classQsciScintilla.html#a194e86e59129ed570af044e19697d0e9">QsciScintilla</a>
</li>
<li>selectionToEol()
: <a class="el" href="classQsciScintilla.html#aaf77d8976ae47a1d5e5ee804bc0645d3">QsciScintilla</a>
</li>
<li>selectToMatchingBrace()
: <a class="el" href="classQsciScintilla.html#a986f1235405f51f9d5b2edda17423563">QsciScintilla</a>
</li>
<li>SendScintilla()
: <a class="el" href="classQsciScintillaBase.html#a8820ab8d7563bd7ed24ce6384846079e">QsciScintillaBase</a>
</li>
<li>SendScintillaPtrResult()
: <a class="el" href="classQsciScintillaBase.html#a5f140c587d361cf8539814d820d680f4">QsciScintillaBase</a>
</li>
<li>setAlternateKey()
: <a class="el" href="classQsciCommand.html#a8c00e5f08abe7ad05fe54653c0f040ae">QsciCommand</a>
</li>
<li>setAnnotationDisplay()
: <a class="el" href="classQsciScintilla.html#aeda9c17a6e746e177fa6f5311d96dc03">QsciScintilla</a>
</li>
<li>setAPIs()
: <a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">QsciLexer</a>
</li>
<li>setAutoCompletionCaseSensitivity()
: <a class="el" href="classQsciScintilla.html#a8348c07fe8cff0bf6141a94ca3014ed2">QsciScintilla</a>
</li>
<li>setAutoCompletionFillups()
: <a class="el" href="classQsciScintilla.html#a9851c2349b4140faa129b7125bee416d">QsciScintilla</a>
</li>
<li>setAutoCompletionFillupsEnabled()
: <a class="el" href="classQsciScintilla.html#a0d273a0573088d9fb6d66d7b2633ea4b">QsciScintilla</a>
</li>
<li>setAutoCompletionReplaceWord()
: <a class="el" href="classQsciScintilla.html#af67874dae6e032c44d4ccde569e2decb">QsciScintilla</a>
</li>
<li>setAutoCompletionShowSingle()
: <a class="el" href="classQsciScintilla.html#a6b494f1a0395a62c3e61f50eabc50679">QsciScintilla</a>
</li>
<li>setAutoCompletionSource()
: <a class="el" href="classQsciScintilla.html#a48860b20014ce8b044760c055156ba57">QsciScintilla</a>
</li>
<li>setAutoCompletionThreshold()
: <a class="el" href="classQsciScintilla.html#a508eb34df3030ac28321b12d86d6670c">QsciScintilla</a>
</li>
<li>setAutoCompletionUseSingle()
: <a class="el" href="classQsciScintilla.html#aefa257027a014475cb468b6d77bcf1f7">QsciScintilla</a>
</li>
<li>setAutoCompletionWordSeparators()
: <a class="el" href="classQsciScintilla.html#a4ba18e98a98310113fb30d8ada30fe14">QsciScintilla</a>
</li>
<li>setAutoIndent()
: <a class="el" href="classQsciScintilla.html#a02105d06ad853647906eb72d27face67">QsciScintilla</a>
</li>
<li>setAutoIndentStyle()
: <a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">QsciLexer</a>
</li>
<li>setBackslashEscapes()
: <a class="el" href="classQsciLexerSQL.html#ab64e768ab8e7af6af93ce95db074c90a">QsciLexerSQL</a>
</li>
<li>setBackspaceUnindents()
: <a class="el" href="classQsciScintilla.html#adca3e8b2b7d4d0bf65ad23322f64f6ef">QsciScintilla</a>
</li>
<li>setBraceMatching()
: <a class="el" href="classQsciScintilla.html#ae53911447eddf1f0f93811f17ca4ecf8">QsciScintilla</a>
</li>
<li>setCallTipsBackgroundColor()
: <a class="el" href="classQsciScintilla.html#af780380f9f1f2a66c729759b1d37ba69">QsciScintilla</a>
</li>
<li>setCallTipsForegroundColor()
: <a class="el" href="classQsciScintilla.html#a13a64159770a6eb451567bc2d293c2a2">QsciScintilla</a>
</li>
<li>setCallTipsHighlightColor()
: <a class="el" href="classQsciScintilla.html#ae64151db464b22eedd012225f82c810d">QsciScintilla</a>
</li>
<li>setCallTipsPosition()
: <a class="el" href="classQsciScintilla.html#a7f9d93c22ed8b7b00996408da578cd2a">QsciScintilla</a>
</li>
<li>setCallTipsStyle()
: <a class="el" href="classQsciScintilla.html#a253807bb0f4b3db471b059afc70b77db">QsciScintilla</a>
</li>
<li>setCallTipsVisible()
: <a class="el" href="classQsciScintilla.html#aec47d94706ffb14ef35035ba827b5b45">QsciScintilla</a>
</li>
<li>setCaretForegroundColor()
: <a class="el" href="classQsciScintilla.html#af9015c62600c87eef82f715bf61da913">QsciScintilla</a>
</li>
<li>setCaretLineBackgroundColor()
: <a class="el" href="classQsciScintilla.html#a8c227f8c948aeb5e6a2cf73397387cc0">QsciScintilla</a>
</li>
<li>setCaretLineFrameWidth()
: <a class="el" href="classQsciScintilla.html#a0999f0d3c67472b6986486fd06775240">QsciScintilla</a>
</li>
<li>setCaretLineVisible()
: <a class="el" href="classQsciScintilla.html#a37f2cddeeef588533be46798ae18ffab">QsciScintilla</a>
</li>
<li>setCaretWidth()
: <a class="el" href="classQsciScintilla.html#a2c1974c2bdc9c0f2923c28b66afe811f">QsciScintilla</a>
</li>
<li>setCaseSensitiveTags()
: <a class="el" href="classQsciLexerHTML.html#a2fda8ad57009d4e2f1ac388cad2cfc92">QsciLexerHTML</a>
</li>
<li>setChangeable()
: <a class="el" href="classQsciStyle.html#af7e052d08efd3a677f810c8e4116dafc">QsciStyle</a>
</li>
<li>setColor()
: <a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">QsciLexer</a>
, <a class="el" href="classQsciScintilla.html#a8c8e4776767cc88b945f50f07d235770">QsciScintilla</a>
, <a class="el" href="classQsciStyle.html#aa7743a3805662a27ae52a56af3ac315a">QsciStyle</a>
</li>
<li>setCommentDelimiter()
: <a class="el" href="classQsciLexerAsm.html#a127674491f7805ca43b18f1bb93b47b4">QsciLexerAsm</a>
</li>
<li>setContractedFolds()
: <a class="el" href="classQsciScintilla.html#a9405d8aaa240dfc8fe30c3a4b5556ecc">QsciScintilla</a>
</li>
<li>setCursorPosition()
: <a class="el" href="classQsciScintilla.html#aea97c6fb0079a6e3e647443b6101ae9d">QsciScintilla</a>
</li>
<li>setDefaultColor()
: <a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">QsciLexer</a>
</li>
<li>setDefaultFont()
: <a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">QsciLexer</a>
</li>
<li>setDefaultPaper()
: <a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">QsciLexer</a>
</li>
<li>setDescription()
: <a class="el" href="classQsciStyle.html#abd88d76b875c154f099b4e9f36b6fcab">QsciStyle</a>
</li>
<li>setDjangoTemplates()
: <a class="el" href="classQsciLexerHTML.html#a59c9b8ff5d698d7e7e03ec2655a24764">QsciLexerHTML</a>
</li>
<li>setDocument()
: <a class="el" href="classQsciScintilla.html#a3a0d1c86f15f218fe6c0e04fea0ba6d9">QsciScintilla</a>
</li>
<li>setDollarsAllowed()
: <a class="el" href="classQsciLexerCoffeeScript.html#affaec4d14f7908f7d24d16937df00c93">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#a06315a18051184926fe21459fc75b4cc">QsciLexerCPP</a>
</li>
<li>setDottedWords()
: <a class="el" href="classQsciLexerSQL.html#aba150bef5f977fb65d66fcaec9c6664c">QsciLexerSQL</a>
</li>
<li>setEdgeColor()
: <a class="el" href="classQsciScintilla.html#aec296526c86ae02deb561b4b4836a886">QsciScintilla</a>
</li>
<li>setEdgeColumn()
: <a class="el" href="classQsciScintilla.html#a8e88f3f4369b73980bb40d5d3a8caf26">QsciScintilla</a>
</li>
<li>setEdgeMode()
: <a class="el" href="classQsciScintilla.html#a3493e72e97607270ca64c01b521f933f">QsciScintilla</a>
</li>
<li>setEditor()
: <a class="el" href="classQsciLexerCustom.html#a224fe82235b9a1c7b9c8bec9dd441178">QsciLexerCustom</a>
</li>
<li>setEolFill()
: <a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">QsciLexer</a>
, <a class="el" href="classQsciStyle.html#a6767dbb23f68292ef9e892dad31ffd9e">QsciStyle</a>
</li>
<li>setEolMode()
: <a class="el" href="classQsciScintilla.html#a0f2353468d2c37abe9c933d4ac0478ad">QsciScintilla</a>
</li>
<li>setEolVisibility()
: <a class="el" href="classQsciScintilla.html#ab98db9f5166ccc23792aea15a19c2294">QsciScintilla</a>
</li>
<li>setExtraAscent()
: <a class="el" href="classQsciScintilla.html#a87e61d47e344dbbb84c4608fdc8536d0">QsciScintilla</a>
</li>
<li>setExtraDescent()
: <a class="el" href="classQsciScintilla.html#a06458817a42498ee65e890c36f63453e">QsciScintilla</a>
</li>
<li>setFirstVisibleLine()
: <a class="el" href="classQsciScintilla.html#a451bcf235c2ad7628d32940a13d22116">QsciScintilla</a>
</li>
<li>setFoldAtBegin()
: <a class="el" href="classQsciLexerVHDL.html#a7f8da8d7fe8301cd49926b896bf5e286">QsciLexerVHDL</a>
</li>
<li>setFoldAtElse()
: <a class="el" href="classQsciLexerCMake.html#aaae969a8e94db29a49849d7497e2cc74">QsciLexerCMake</a>
, <a class="el" href="classQsciLexerCPP.html#ad0a3dd6dfb77a069303bfeeeed43773f">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerD.html#a2dc2ffcd977cf514e65e315a80afcb18">QsciLexerD</a>
, <a class="el" href="classQsciLexerPerl.html#a14705cac9643949facd57641e0892fb0">QsciLexerPerl</a>
, <a class="el" href="classQsciLexerPostScript.html#aa303817de5a59137ab4bf592ff52a315">QsciLexerPostScript</a>
, <a class="el" href="classQsciLexerSQL.html#a35dfbbd04762b0450232c14862ec3ea6">QsciLexerSQL</a>
, <a class="el" href="classQsciLexerVerilog.html#a7b84f78b170cec259efb2f367c54ce4b">QsciLexerVerilog</a>
, <a class="el" href="classQsciLexerVHDL.html#ae8c0599c4eb74db6caa8624bcc416a8b">QsciLexerVHDL</a>
</li>
<li>setFoldAtModule()
: <a class="el" href="classQsciLexerVerilog.html#af57050a2bcb9d1d285199159da0ba6e0">QsciLexerVerilog</a>
</li>
<li>setFoldAtParenthesis()
: <a class="el" href="classQsciLexerVHDL.html#ad6328325f4c46dce0226712e9db3bba7">QsciLexerVHDL</a>
</li>
<li>setFoldComments()
: <a class="el" href="classQsciLexerAsm.html#acb6433de9e477aeefdce46814dfb89ca">QsciLexerAsm</a>
, <a class="el" href="classQsciLexerAVS.html#a86be2cbea60ab7b3419ed3bf2db7c5ce">QsciLexerAVS</a>
, <a class="el" href="classQsciLexerBash.html#ab743740491685360f2d50e5c12be876b">QsciLexerBash</a>
, <a class="el" href="classQsciLexerCoffeeScript.html#a57f1f1164f3719b4b855a3a163a78764">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#aaf1f8163b8baf27ef65c1e5219bbf1e2">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerCSS.html#a5f77be4cb83422d47220c5b38d9f0a99">QsciLexerCSS</a>
, <a class="el" href="classQsciLexerD.html#aa7bcbfe8a9e732630bba54860888e9d5">QsciLexerD</a>
, <a class="el" href="classQsciLexerPascal.html#a05d880fd1451f6a757fd21a7bd43a358">QsciLexerPascal</a>
, <a class="el" href="classQsciLexerPerl.html#a85aa8e72d81818a7edea1867362db16a">QsciLexerPerl</a>
, <a class="el" href="classQsciLexerPO.html#abb698a7598847dea3cec0686c88ba43a">QsciLexerPO</a>
, <a class="el" href="classQsciLexerPOV.html#a189a9efbe5c2fa07757d67c013229e19">QsciLexerPOV</a>
, <a class="el" href="classQsciLexerPython.html#a35e71b31d8d197052c7c5250ff21f094">QsciLexerPython</a>
, <a class="el" href="classQsciLexerRuby.html#acf9bbfcaf3dfd6004428920e1c6572fd">QsciLexerRuby</a>
, <a class="el" href="classQsciLexerSQL.html#a6efb8e98287c21ec5175a466d7e5cc55">QsciLexerSQL</a>
, <a class="el" href="classQsciLexerTCL.html#abceb6f3cf78367b7bc370265d7776bf1">QsciLexerTCL</a>
, <a class="el" href="classQsciLexerTeX.html#a2097c0d473f379dbcd0faa9653bcc943">QsciLexerTeX</a>
, <a class="el" href="classQsciLexerVerilog.html#ac79b616c3ba0872856d90b119bfd81b8">QsciLexerVerilog</a>
, <a class="el" href="classQsciLexerVHDL.html#af41d62ccd061b840e3eb2e9e2b26d6f5">QsciLexerVHDL</a>
, <a class="el" href="classQsciLexerYAML.html#a5fc9da8d92341819072536ce840902fc">QsciLexerYAML</a>
</li>
<li>setFoldCompact()
: <a class="el" href="classQsciLexerAsm.html#a8defdc421cdee2af973ee44b7005f1a5">QsciLexerAsm</a>
, <a class="el" href="classQsciLexerAVS.html#ac814c0fdc49d3c27a027a8e075aa7626">QsciLexerAVS</a>
, <a class="el" href="classQsciLexerBash.html#a80a1f387059600fd67bbf6d2699981e3">QsciLexerBash</a>
, <a class="el" href="classQsciLexerCoffeeScript.html#a8bc6aee27994356e61fc6b030e23a62f">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#af17ac732d73445822ef23a59f3e45aef">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerCSS.html#aaf9762aeea19ed1c8d6766a9e6a52cd3">QsciLexerCSS</a>
, <a class="el" href="classQsciLexerD.html#a97c7813c68c861b9f2b3f068d9b47fd7">QsciLexerD</a>
, <a class="el" href="classQsciLexerFortran77.html#a206ea971cb4152f8ca00087544574d15">QsciLexerFortran77</a>
, <a class="el" href="classQsciLexerHTML.html#a1036c768307d29c40f09cc1bc2fce37c">QsciLexerHTML</a>
, <a class="el" href="classQsciLexerJSON.html#a8a24cd2bdd449e16ae5d00db7a1826bf">QsciLexerJSON</a>
, <a class="el" href="classQsciLexerLua.html#a2f54e561f646da5ff20c5e85b2f377ea">QsciLexerLua</a>
, <a class="el" href="classQsciLexerPascal.html#a2d183c40c276dadd3bbb994b0c0f26ce">QsciLexerPascal</a>
, <a class="el" href="classQsciLexerPerl.html#a269b1c3c788ae863939fd8b1749a5abf">QsciLexerPerl</a>
, <a class="el" href="classQsciLexerPO.html#af3d4ae3f76854d01444b2fd4637c9b8e">QsciLexerPO</a>
, <a class="el" href="classQsciLexerPostScript.html#a277a3c519eca4ef69d73fd45ea4f5ab5">QsciLexerPostScript</a>
, <a class="el" href="classQsciLexerPOV.html#a831ed1e8074990eafb57d4b9ebaf3d2f">QsciLexerPOV</a>
, <a class="el" href="classQsciLexerProperties.html#a4caa0f46faeb171710ec2657cd23436e">QsciLexerProperties</a>
, <a class="el" href="classQsciLexerPython.html#a27dcfdcac480d0360029d1f12b14f724">QsciLexerPython</a>
, <a class="el" href="classQsciLexerRuby.html#a0e83f239ecb3c52bf4930412f32f51f1">QsciLexerRuby</a>
, <a class="el" href="classQsciLexerSQL.html#a302b9b881fdc5dca82c5dea5fca5cd3e">QsciLexerSQL</a>
, <a class="el" href="classQsciLexerTeX.html#a21f63849918a4fbeda81dc5f79fa81c2">QsciLexerTeX</a>
, <a class="el" href="classQsciLexerVerilog.html#a17ff342a5c1d94ce760a3dc02cfcda1d">QsciLexerVerilog</a>
, <a class="el" href="classQsciLexerVHDL.html#a40d1ca24b672c13e9e7e69add2f5ee42">QsciLexerVHDL</a>
</li>
<li>setFoldDirectives()
: <a class="el" href="classQsciLexerPOV.html#aea30a66d5e4e7d0064366eefec03364c">QsciLexerPOV</a>
</li>
<li>setFolding()
: <a class="el" href="classQsciScintilla.html#a340cd78e46fb58fc9b3b78ed158ba56e">QsciScintilla</a>
</li>
<li>setFoldMarginColors()
: <a class="el" href="classQsciScintilla.html#a6bdcf192822a31094e680ffb2f142aab">QsciScintilla</a>
</li>
<li>setFoldOnlyBegin()
: <a class="el" href="classQsciLexerSQL.html#a680cba1b994603e73da00610e81debfe">QsciLexerSQL</a>
</li>
<li>setFoldPackages()
: <a class="el" href="classQsciLexerPerl.html#a5e2cdbcaa57b02f18d65aea89d2faa54">QsciLexerPerl</a>
</li>
<li>setFoldPODBlocks()
: <a class="el" href="classQsciLexerPerl.html#af0ee6abab37e283e68f527c597c50877">QsciLexerPerl</a>
</li>
<li>setFoldPreprocessor()
: <a class="el" href="classQsciLexerCPP.html#a6a8c59ca8409029fc6b27b9ad3c70886">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerHTML.html#aeba753c0e1fca8bf66834667e301458e">QsciLexerHTML</a>
, <a class="el" href="classQsciLexerPascal.html#a85c009c5ccf84fc64726bb2c3b11bdec">QsciLexerPascal</a>
, <a class="el" href="classQsciLexerVerilog.html#ab7c13e959940db389fe0daeb96267d8e">QsciLexerVerilog</a>
</li>
<li>setFoldQuotes()
: <a class="el" href="classQsciLexerPython.html#afc0aaf4300e9ca02eb8fa49328bbe8d8">QsciLexerPython</a>
</li>
<li>setFoldScriptComments()
: <a class="el" href="classQsciLexerHTML.html#a51401044d3ad272ede84e1f2a128cce6">QsciLexerHTML</a>
</li>
<li>setFoldScriptHeredocs()
: <a class="el" href="classQsciLexerHTML.html#a122450b5227d23ee119b2653b9e9be2f">QsciLexerHTML</a>
</li>
<li>setFoldSyntaxBased()
: <a class="el" href="classQsciLexerAsm.html#a16d51d4aa2ab5836f9aff6f394aeca85">QsciLexerAsm</a>
</li>
<li>setFont()
: <a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">QsciLexer</a>
, <a class="el" href="classQsciScintilla.html#a26eb997695e6b7292896743ac825a8ee">QsciScintilla</a>
, <a class="el" href="classQsciStyle.html#ab09932c9dafb915b8138d4ec1cbc79cb">QsciStyle</a>
</li>
<li>setHashComments()
: <a class="el" href="classQsciLexerSQL.html#acc91bd455ff72d93d0bb73b553afbbb8">QsciLexerSQL</a>
</li>
<li>setHighlightBackQuotedStrings()
: <a class="el" href="classQsciLexerCPP.html#aa023c95fbbecbbbf7046c92d6fcfdce5">QsciLexerCPP</a>
</li>
<li>setHighlightComments()
: <a class="el" href="classQsciLexerJSON.html#aad1f452948047cc4ce0afc9bc9374061">QsciLexerJSON</a>
</li>
<li>setHighlightEscapeSequences()
: <a class="el" href="classQsciLexerCPP.html#a6d6a21ea44e2ee9676aa27178021b06a">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerJSON.html#ac4b582db9efad2743e270ee63234804f">QsciLexerJSON</a>
</li>
<li>setHighlightHashQuotedStrings()
: <a class="el" href="classQsciLexerCPP.html#ad0d9356583118309e6c3991e96a67ffe">QsciLexerCPP</a>
</li>
<li>setHighlightSubidentifiers()
: <a class="el" href="classQsciLexerPython.html#ade07472f3cc8a4cccbb0bb6b964f0356">QsciLexerPython</a>
</li>
<li>setHighlightTripleQuotedStrings()
: <a class="el" href="classQsciLexerCPP.html#a2ea8bd8758e10d72832dbf3642b06fb2">QsciLexerCPP</a>
</li>
<li>setHotspot()
: <a class="el" href="classQsciStyle.html#acb06ba468da57cc4ea9e8d496cb33f83">QsciStyle</a>
</li>
<li>setHotspotBackgroundColor()
: <a class="el" href="classQsciScintilla.html#aaf19a3abaa1b1662a0f1b499ef4b6602">QsciScintilla</a>
</li>
<li>setHotspotForegroundColor()
: <a class="el" href="classQsciScintilla.html#a4c6a412b7d066b9fce90f3976350348c">QsciScintilla</a>
</li>
<li>setHotspotUnderline()
: <a class="el" href="classQsciScintilla.html#ac961cfe1be7cd29038a2772f30b71bfc">QsciScintilla</a>
</li>
<li>setHotspotWrap()
: <a class="el" href="classQsciScintilla.html#a7245335691700f82db41016d257d63cc">QsciScintilla</a>
</li>
<li>setHSSLanguage()
: <a class="el" href="classQsciLexerCSS.html#a9e61fa490e6e6c1480f3de5187ffed02">QsciLexerCSS</a>
</li>
<li>setIndentation()
: <a class="el" href="classQsciScintilla.html#aa46e60536be6297de6ca1fb16d36cd51">QsciScintilla</a>
</li>
<li>setIndentationGuides()
: <a class="el" href="classQsciScintilla.html#a1b4591eb73dcef0153861f698edc8726">QsciScintilla</a>
</li>
<li>setIndentationGuidesBackgroundColor()
: <a class="el" href="classQsciScintilla.html#a18d150beb0cd818ebcbcee886217de6a">QsciScintilla</a>
</li>
<li>setIndentationGuidesForegroundColor()
: <a class="el" href="classQsciScintilla.html#ace4acd9ee0d1c3e31099d65cdd8219b2">QsciScintilla</a>
</li>
<li>setIndentationsUseTabs()
: <a class="el" href="classQsciScintilla.html#a065b392e0a39ecfd39df787a3a25e814">QsciScintilla</a>
</li>
<li>setIndentationWarning()
: <a class="el" href="classQsciLexerPython.html#a421ab12187730bc0686dc72710867ec3">QsciLexerPython</a>
</li>
<li>setIndentationWidth()
: <a class="el" href="classQsciScintilla.html#a8010e1671a15976254fd11b59ca3e03d">QsciScintilla</a>
</li>
<li>setIndicatorDrawUnder()
: <a class="el" href="classQsciScintilla.html#a5ba2e241be76c209c0f5509804a995c9">QsciScintilla</a>
</li>
<li>setIndicatorForegroundColor()
: <a class="el" href="classQsciScintilla.html#a2ffd6d691d5a63940e448138f3754a47">QsciScintilla</a>
</li>
<li>setIndicatorHoverForegroundColor()
: <a class="el" href="classQsciScintilla.html#a616edbb9da241c08f8381835d65bb18c">QsciScintilla</a>
</li>
<li>setIndicatorHoverStyle()
: <a class="el" href="classQsciScintilla.html#ae76354288b8e5c2bc6f7a7e7bd97e44e">QsciScintilla</a>
</li>
<li>setIndicatorOutlineColor()
: <a class="el" href="classQsciScintilla.html#af63627804eeffc4f0f1290181cda7781">QsciScintilla</a>
</li>
<li>setInitialSpaces()
: <a class="el" href="classQsciLexerProperties.html#a2243845007f5165eb5718a131be3ada3">QsciLexerProperties</a>
</li>
<li>setKey()
: <a class="el" href="classQsciCommand.html#a6488ddf82659fcf42d704f787b6cb522">QsciCommand</a>
</li>
<li>setLessLanguage()
: <a class="el" href="classQsciLexerCSS.html#a388e532d847652dbf18207593e236e5e">QsciLexerCSS</a>
</li>
<li>setLevel()
: <a class="el" href="classQsciLexerPostScript.html#a80c198967862ff5392982a49b8004f48">QsciLexerPostScript</a>
</li>
<li>setLexer()
: <a class="el" href="classQsciScintilla.html#a7bc5fb5d0daf8261544fb6fe738a0c91">QsciScintilla</a>
</li>
<li>setMagnification()
: <a class="el" href="classQsciPrinter.html#ad66724c8a5e5e202998bd6533fef61be">QsciPrinter</a>
</li>
<li>setMakoTemplates()
: <a class="el" href="classQsciLexerHTML.html#a8553315e763e1e53f56dd4dbe6b3c3d7">QsciLexerHTML</a>
</li>
<li>setMarginBackgroundColor()
: <a class="el" href="classQsciScintilla.html#a18c2bd1ee70c87809ba307ae6b695272">QsciScintilla</a>
</li>
<li>setMarginLineNumbers()
: <a class="el" href="classQsciScintilla.html#a5fddd1e6e19cf2e2b40c15a39e62d198">QsciScintilla</a>
</li>
<li>setMarginMarkerMask()
: <a class="el" href="classQsciScintilla.html#aae6392483ffb59cdb94b7bd4b8a6dec9">QsciScintilla</a>
</li>
<li>setMarginOptions()
: <a class="el" href="classQsciScintilla.html#a626103a61623dd360dc44210fe435ad7">QsciScintilla</a>
</li>
<li>setMargins()
: <a class="el" href="classQsciScintilla.html#a9c3a34cb9edf25913af16c9dc284cc5d">QsciScintilla</a>
</li>
<li>setMarginsBackgroundColor()
: <a class="el" href="classQsciScintilla.html#a419ab8aed49ea1711ce4ffcf19146df1">QsciScintilla</a>
</li>
<li>setMarginSensitivity()
: <a class="el" href="classQsciScintilla.html#a064b51eca1ab2d32d4c4c328e69a395d">QsciScintilla</a>
</li>
<li>setMarginsFont()
: <a class="el" href="classQsciScintilla.html#a672f2fb901048f290997cb69216b7a00">QsciScintilla</a>
</li>
<li>setMarginsForegroundColor()
: <a class="el" href="classQsciScintilla.html#a263f0c4753c9a0c950adf1377737444e">QsciScintilla</a>
</li>
<li>setMarginText()
: <a class="el" href="classQsciScintilla.html#ad1b6838e763eb9d7c0b3562b6c2a96ae">QsciScintilla</a>
</li>
<li>setMarginType()
: <a class="el" href="classQsciScintilla.html#a4dd046074be580fbde318ba2ae343d39">QsciScintilla</a>
</li>
<li>setMarginWidth()
: <a class="el" href="classQsciScintilla.html#a1a8b798b6b36db2b5f60b0cdccba88a1">QsciScintilla</a>
</li>
<li>setMarkerBackgroundColor()
: <a class="el" href="classQsciScintilla.html#acf47d4b76a8c85a48fe9a27423997071">QsciScintilla</a>
</li>
<li>setMarkerForegroundColor()
: <a class="el" href="classQsciScintilla.html#a6abf177ca5bf8eea0930106d2867edae">QsciScintilla</a>
</li>
<li>setMatchedBraceBackgroundColor()
: <a class="el" href="classQsciScintilla.html#abf85680f914ee631aa3a513ba823271f">QsciScintilla</a>
</li>
<li>setMatchedBraceForegroundColor()
: <a class="el" href="classQsciScintilla.html#af590f3e7196b21860e0405670cfa512d">QsciScintilla</a>
</li>
<li>setMatchedBraceIndicator()
: <a class="el" href="classQsciScintilla.html#a067cd392c008e07ff259ffdd0ce25fcb">QsciScintilla</a>
</li>
<li>setModified()
: <a class="el" href="classQsciScintilla.html#aff32517974ac1d8c8cd3c5b6c757ddc9">QsciScintilla</a>
</li>
<li>setOverwriteMode()
: <a class="el" href="classQsciScintilla.html#aa627ee937acaae02dc0c5b468fd2643b">QsciScintilla</a>
</li>
<li>setPaper()
: <a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">QsciLexer</a>
, <a class="el" href="classQsciScintilla.html#aa805f90f3bbe067299e9ab8902eafbf3">QsciScintilla</a>
, <a class="el" href="classQsciStyle.html#a2d4ec76574fd507fbf3c0d006c7427da">QsciStyle</a>
</li>
<li>setProcessComments()
: <a class="el" href="classQsciLexerTeX.html#a1895725812d581b40913c1a85d2ab533">QsciLexerTeX</a>
</li>
<li>setProcessIf()
: <a class="el" href="classQsciLexerTeX.html#a479ca70b474910355294d1fcec011572">QsciLexerTeX</a>
</li>
<li>setQuotedIdentifiers()
: <a class="el" href="classQsciLexerSQL.html#ae6e5819a3ddec15ac6926b5e19927bff">QsciLexerSQL</a>
</li>
<li>setReadOnly()
: <a class="el" href="classQsciScintilla.html#ab26d156ff430e904e8f92d3dad9730bc">QsciScintilla</a>
</li>
<li>setScriptsStyled()
: <a class="el" href="classQsciLexerXML.html#a7bbfdb6b269b6e52791fcbf1df60731e">QsciLexerXML</a>
</li>
<li>setScrollWidth()
: <a class="el" href="classQsciScintilla.html#a9b1a8ed3235c506ffca09260cdd0e209">QsciScintilla</a>
</li>
<li>setScrollWidthTracking()
: <a class="el" href="classQsciScintilla.html#a7451e82e2ee3d0ddb3b8418edb0202f2">QsciScintilla</a>
</li>
<li>setSCSSLanguage()
: <a class="el" href="classQsciLexerCSS.html#a2a2195f681df3657fbadf72c55003863">QsciLexerCSS</a>
</li>
<li>setSelection()
: <a class="el" href="classQsciScintilla.html#a391299d076b0164402118f504c83d09c">QsciScintilla</a>
</li>
<li>setSelectionBackgroundColor()
: <a class="el" href="classQsciScintilla.html#a6882a7641822a859e812601f1bae65eb">QsciScintilla</a>
</li>
<li>setSelectionForegroundColor()
: <a class="el" href="classQsciScintilla.html#a1060a2e187518d1c8b2814c393e227c6">QsciScintilla</a>
</li>
<li>setSelectionToEol()
: <a class="el" href="classQsciScintilla.html#a7e36f3595e0d89910b9322dc5295152d">QsciScintilla</a>
</li>
<li>setSmartHighlighting()
: <a class="el" href="classQsciLexerPascal.html#a64f021f45d10f2cfca72fda0c1d28e1f">QsciLexerPascal</a>
</li>
<li>setStringsOverNewlineAllowed()
: <a class="el" href="classQsciLexerPython.html#a5887a36e4a8d6ff54f4c796b33bc2eef">QsciLexerPython</a>
</li>
<li>setStyle()
: <a class="el" href="classQsciStyle.html#af00ea2dd20e93c5d06d9ce99cbc2cf00">QsciStyle</a>
</li>
<li>setStylePreprocessor()
: <a class="el" href="classQsciLexerCoffeeScript.html#aa1949e1c7fd18507f664babab7b3c56c">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#a66dc6ae74420ab3406043ff9f6f70cc4">QsciLexerCPP</a>
</li>
<li>setStyling()
: <a class="el" href="classQsciLexerCustom.html#a5ba7f97b19cfa7bd0b846fc56d94fa3c">QsciLexerCustom</a>
</li>
<li>setTabDrawMode()
: <a class="el" href="classQsciScintilla.html#a69f35ec6e80059bbb351c8f8845cd8f6">QsciScintilla</a>
</li>
<li>setTabIndents()
: <a class="el" href="classQsciScintilla.html#a957eaab9ac1785eb043fb83f703a0b57">QsciScintilla</a>
</li>
<li>setTabWidth()
: <a class="el" href="classQsciScintilla.html#a1bd5470bc123a43c98facfc5c4a1e523">QsciScintilla</a>
</li>
<li>setText()
: <a class="el" href="classQsciScintilla.html#a5786917722e156e26d6afca807d05fee">QsciScintilla</a>
</li>
<li>setTextCase()
: <a class="el" href="classQsciStyle.html#a25e9b8a34c334bf6160115a2c43a5256">QsciStyle</a>
</li>
<li>setTokenize()
: <a class="el" href="classQsciLexerPostScript.html#a8d57801958b738cbb297936426bb8c61">QsciLexerPostScript</a>
</li>
<li>setUnmatchedBraceBackgroundColor()
: <a class="el" href="classQsciScintilla.html#a3035ddd4e1360c2d9a6c86b362a0d905">QsciScintilla</a>
</li>
<li>setUnmatchedBraceForegroundColor()
: <a class="el" href="classQsciScintilla.html#a511a4f492a9912df3d430fba33b67d5c">QsciScintilla</a>
</li>
<li>setUnmatchedBraceIndicator()
: <a class="el" href="classQsciScintilla.html#a4b6bdaf96ffaedeeaf7aa6d92b28913f">QsciScintilla</a>
</li>
<li>setUtf8()
: <a class="el" href="classQsciScintilla.html#a9071c0772ce576f60fce08395ce04274">QsciScintilla</a>
</li>
<li>setV2UnicodeAllowed()
: <a class="el" href="classQsciLexerPython.html#accc3cd3ccf7d62840ded955400695b9d">QsciLexerPython</a>
</li>
<li>setV3BinaryOctalAllowed()
: <a class="el" href="classQsciLexerPython.html#ae6bc53fc7e6dc90a80a26e22f6f49acb">QsciLexerPython</a>
</li>
<li>setV3BytesAllowed()
: <a class="el" href="classQsciLexerPython.html#a856785e000203b1da8fa6f295daad13e">QsciLexerPython</a>
</li>
<li>setVerbatimStringEscapeSequencesAllowed()
: <a class="el" href="classQsciLexerCPP.html#a015dce05877d292d399fb207e79632cf">QsciLexerCPP</a>
</li>
<li>setVisible()
: <a class="el" href="classQsciStyle.html#a4f8b9edd94c36344bd7152d15731509a">QsciStyle</a>
</li>
<li>setWhitespaceBackgroundColor()
: <a class="el" href="classQsciScintilla.html#a9fdd43a276cf3d9a3e7cc86dc7f280f5">QsciScintilla</a>
</li>
<li>setWhitespaceForegroundColor()
: <a class="el" href="classQsciScintilla.html#a67177e2b1d8584d8cf8f1b276174b258">QsciScintilla</a>
</li>
<li>setWhitespaceSize()
: <a class="el" href="classQsciScintilla.html#a7436ea4b640c312fd07945e9b436e19b">QsciScintilla</a>
</li>
<li>setWhitespaceVisibility()
: <a class="el" href="classQsciScintilla.html#aa2bca1d2d137ea4a3f944a4f41f98a94">QsciScintilla</a>
</li>
<li>setWrapIndentMode()
: <a class="el" href="classQsciScintilla.html#ad8424876c29b1a77fd1df45a534722d1">QsciScintilla</a>
</li>
<li>setWrapMode()
: <a class="el" href="classQsciPrinter.html#aa95827e3bd2c3c0e658afe55fa12476e">QsciPrinter</a>
, <a class="el" href="classQsciScintilla.html#ac04428d2f90c36458d68a673f107e40c">QsciScintilla</a>
</li>
<li>setWrapVisualFlags()
: <a class="el" href="classQsciScintilla.html#ab696e4703374af4c01651453d094ac08">QsciScintilla</a>
</li>
<li>showUserList()
: <a class="el" href="classQsciScintilla.html#a42ae037173aab16ce5e14788e6331623">QsciScintilla</a>
</li>
<li>smartHighlighting()
: <a class="el" href="classQsciLexerPascal.html#a71fd025ad904aa51a6127f43099805ad">QsciLexerPascal</a>
</li>
<li>standardCommands()
: <a class="el" href="classQsciScintilla.html#a8911af504ebdc870f09da4c7a491eeeb">QsciScintilla</a>
</li>
<li>startRecording()
: <a class="el" href="classQsciMacro.html#a4a5648ea6c1e35aaaa55f9aaf83e7eda">QsciMacro</a>
</li>
<li>startStyling()
: <a class="el" href="classQsciLexerCustom.html#a19d92643c31c4ec10eab14da7c931b55">QsciLexerCustom</a>
</li>
<li>stringsOverNewlineAllowed()
: <a class="el" href="classQsciLexerPython.html#aa4abeabae54373d536961d0aabb5ecdf">QsciLexerPython</a>
</li>
<li>style()
: <a class="el" href="classQsciStyle.html#a61582248f6b7276db9b4a1f9582c3828">QsciStyle</a>
, <a class="el" href="classQsciStyledText.html#a6a5f837ca80d54322b70aa4b8465afa1">QsciStyledText</a>
</li>
<li>styleBitsNeeded()
: <a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">QsciLexer</a>
, <a class="el" href="classQsciLexerCustom.html#addc357462c04f032e20149b55cb8aeaa">QsciLexerCustom</a>
</li>
<li>stylePreprocessor()
: <a class="el" href="classQsciLexerCoffeeScript.html#aba02f4e299dd7f25cea762e9c21b48b2">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#ac6f508a57750605ec3b9688408b092b2">QsciLexerCPP</a>
</li>
<li>styleText()
: <a class="el" href="classQsciLexerCustom.html#a91d71c4bdff5140ae0b0cb34b4511f79">QsciLexerCustom</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
