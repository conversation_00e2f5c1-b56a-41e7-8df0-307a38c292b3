<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciAPIs Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#signals">Signals</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classQsciAPIs-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciAPIs Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qsciapis.h&gt;</code></p>

<p>Inherits <a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a>.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="signals"></a>
Signals</h2></td></tr>
<tr class="memitem:aaa47506820a2596004688e241fc4cd9f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#aaa47506820a2596004688e241fc4cd9f">apiPreparationCancelled</a> ()</td></tr>
<tr class="separator:aaa47506820a2596004688e241fc4cd9f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8fc5db618546fcfcc5bdc46e6d062995"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#a8fc5db618546fcfcc5bdc46e6d062995">apiPreparationStarted</a> ()</td></tr>
<tr class="separator:a8fc5db618546fcfcc5bdc46e6d062995"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf779559d29fed004ec65ef560483e3c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#adf779559d29fed004ec65ef560483e3c">apiPreparationFinished</a> ()</td></tr>
<tr class="separator:adf779559d29fed004ec65ef560483e3c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aaf185d65d1034087b77995d8490b6475"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#aaf185d65d1034087b77995d8490b6475">QsciAPIs</a> (<a class="el" href="classQsciLexer.html">QsciLexer</a> *<a class="el" href="classQsciAbstractAPIs.html#a90452ab6f4d40314ec519913f9e78ccc">lexer</a>)</td></tr>
<tr class="separator:aaf185d65d1034087b77995d8490b6475"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a07bc73b7a67f8f405578992bae29528c"><td class="memItemLeft" align="right" valign="top"><a id="a07bc73b7a67f8f405578992bae29528c"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#a07bc73b7a67f8f405578992bae29528c">~QsciAPIs</a> ()</td></tr>
<tr class="separator:a07bc73b7a67f8f405578992bae29528c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af46ca05571eb676d3aa65b080fb406c5"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#af46ca05571eb676d3aa65b080fb406c5">add</a> (const QString &amp;entry)</td></tr>
<tr class="separator:af46ca05571eb676d3aa65b080fb406c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b29d84b0b5d63f2b590988195c7557c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#a6b29d84b0b5d63f2b590988195c7557c">clear</a> ()</td></tr>
<tr class="separator:a6b29d84b0b5d63f2b590988195c7557c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3084b749e4eb1c741fc1004e8a84a631"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#a3084b749e4eb1c741fc1004e8a84a631">load</a> (const QString &amp;filename)</td></tr>
<tr class="separator:a3084b749e4eb1c741fc1004e8a84a631"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acb1aa10ea05a7ee72a0d77376153b4d2"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#acb1aa10ea05a7ee72a0d77376153b4d2">remove</a> (const QString &amp;entry)</td></tr>
<tr class="separator:acb1aa10ea05a7ee72a0d77376153b4d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5c26b8395c49cf61243e5f73c0ce577f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#a5c26b8395c49cf61243e5f73c0ce577f">prepare</a> ()</td></tr>
<tr class="separator:a5c26b8395c49cf61243e5f73c0ce577f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa5c7c8855162eeb1be74c226ebf1b1b6"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#aa5c7c8855162eeb1be74c226ebf1b1b6">cancelPreparation</a> ()</td></tr>
<tr class="separator:aa5c7c8855162eeb1be74c226ebf1b1b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0a080d197e8226117a626c7b4b68b32d"><td class="memItemLeft" align="right" valign="top">QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#a0a080d197e8226117a626c7b4b68b32d">defaultPreparedName</a> () const</td></tr>
<tr class="separator:a0a080d197e8226117a626c7b4b68b32d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9dc74576c602f1df961aa8efee652a3d"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#a9dc74576c602f1df961aa8efee652a3d">isPrepared</a> (const QString &amp;filename=QString()) const</td></tr>
<tr class="separator:a9dc74576c602f1df961aa8efee652a3d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af42a26a050bfeb4249d35ab61567ea9e"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#af42a26a050bfeb4249d35ab61567ea9e">loadPrepared</a> (const QString &amp;filename=QString())</td></tr>
<tr class="separator:af42a26a050bfeb4249d35ab61567ea9e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a742609f12e48e63edbab2565d7df3cb9"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#a742609f12e48e63edbab2565d7df3cb9">savePrepared</a> (const QString &amp;filename=QString()) const</td></tr>
<tr class="separator:a742609f12e48e63edbab2565d7df3cb9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab0f824492bb0f3ca54edb4d46945a3de"><td class="memItemLeft" align="right" valign="top"><a id="ab0f824492bb0f3ca54edb4d46945a3de"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#ab0f824492bb0f3ca54edb4d46945a3de">updateAutoCompletionList</a> (const QStringList &amp;context, QStringList &amp;list)</td></tr>
<tr class="separator:ab0f824492bb0f3ca54edb4d46945a3de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adff0073d1f4ee2e0ea8b3bf234ff2dd3"><td class="memItemLeft" align="right" valign="top"><a id="adff0073d1f4ee2e0ea8b3bf234ff2dd3"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#adff0073d1f4ee2e0ea8b3bf234ff2dd3">autoCompletionSelected</a> (const QString &amp;sel)</td></tr>
<tr class="separator:adff0073d1f4ee2e0ea8b3bf234ff2dd3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6a75974235c5e8d263bf962c778b3a3e"><td class="memItemLeft" align="right" valign="top"><a id="a6a75974235c5e8d263bf962c778b3a3e"></a>
virtual QStringList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#a6a75974235c5e8d263bf962c778b3a3e">callTips</a> (const QStringList &amp;context, int commas, <a class="el" href="classQsciScintilla.html#a62d0174cb0a07e3f2d48fc0603192668">QsciScintilla::CallTipsStyle</a> style, QList&lt; int &gt; &amp;shifts)</td></tr>
<tr class="separator:a6a75974235c5e8d263bf962c778b3a3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aade407551cb7e9e116e098f08ef5d26c"><td class="memItemLeft" align="right" valign="top"><a id="aade407551cb7e9e116e098f08ef5d26c"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>event</b> (QEvent *e)</td></tr>
<tr class="separator:aade407551cb7e9e116e098f08ef5d26c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa2ee3021ffc6a998776547a5c252edca"><td class="memItemLeft" align="right" valign="top">QStringList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAPIs.html#aa2ee3021ffc6a998776547a5c252edca">installedAPIFiles</a> () const</td></tr>
<tr class="separator:aa2ee3021ffc6a998776547a5c252edca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classQsciAbstractAPIs"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classQsciAbstractAPIs')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a></td></tr>
<tr class="memitem:a9db5ebe8adda3f58892af676f5295e3a inherit pub_methods_classQsciAbstractAPIs"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAbstractAPIs.html#a9db5ebe8adda3f58892af676f5295e3a">QsciAbstractAPIs</a> (<a class="el" href="classQsciLexer.html">QsciLexer</a> *<a class="el" href="classQsciAbstractAPIs.html#a90452ab6f4d40314ec519913f9e78ccc">lexer</a>)</td></tr>
<tr class="separator:a9db5ebe8adda3f58892af676f5295e3a inherit pub_methods_classQsciAbstractAPIs"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ef866227b05482cb32c70b44e8bdec1 inherit pub_methods_classQsciAbstractAPIs"><td class="memItemLeft" align="right" valign="top"><a id="a7ef866227b05482cb32c70b44e8bdec1"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAbstractAPIs.html#a7ef866227b05482cb32c70b44e8bdec1">~QsciAbstractAPIs</a> ()</td></tr>
<tr class="separator:a7ef866227b05482cb32c70b44e8bdec1 inherit pub_methods_classQsciAbstractAPIs"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a90452ab6f4d40314ec519913f9e78ccc inherit pub_methods_classQsciAbstractAPIs"><td class="memItemLeft" align="right" valign="top"><a id="a90452ab6f4d40314ec519913f9e78ccc"></a>
<a class="el" href="classQsciLexer.html">QsciLexer</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAbstractAPIs.html#a90452ab6f4d40314ec519913f9e78ccc">lexer</a> () const</td></tr>
<tr class="separator:a90452ab6f4d40314ec519913f9e78ccc inherit pub_methods_classQsciAbstractAPIs"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciAPIs.html" title="The QsciAPIs class provies an implementation of the textual API information used in call tips and for...">QsciAPIs</a> class provies an implementation of the textual API information used in call tips and for auto-completion. </p>
<p>Raw API information is read from one or more files. Each API function is described by a single line of text comprising the function's name, followed by the function's optional comma separated parameters enclosed in parenthesis, and finally followed by optional explanatory text.</p>
<p>A function name may be followed by a &lsquo;?&rsquo; and a number. The number is used by auto-completion to display a registered QPixmap with the function name.</p>
<p>All function names are used by auto-completion, but only those that include function parameters are used in call tips.</p>
<p>QScintilla only deals with prepared API information and not the raw information described above. This is done so that large APIs can be handled while still being responsive to user input. The conversion of raw information to prepared information is time consuming (think tens of seconds) and implemented in a separate thread. Prepared information can be quickly saved to and loaded from files. Such files are portable between different architectures.</p>
<p>QScintilla based applications that want to support large APIs would normally provide the user with the ability to specify a set of, possibly project specific, raw API files and convert them to prepared files that are loaded quickly when the application is invoked. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="aaf185d65d1034087b77995d8490b6475"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaf185d65d1034087b77995d8490b6475">&#9670;&nbsp;</a></span>QsciAPIs()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciAPIs::QsciAPIs </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classQsciLexer.html">QsciLexer</a> *&#160;</td>
          <td class="paramname"><em>lexer</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Constructs a <a class="el" href="classQsciAPIs.html" title="The QsciAPIs class provies an implementation of the textual API information used in call tips and for...">QsciAPIs</a> instance attached to lexer <em>lexer</em>. <em>lexer</em> becomes the instance's parent object although the instance can also be subsequently attached to other lexers. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="af46ca05571eb676d3aa65b080fb406c5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af46ca05571eb676d3aa65b080fb406c5">&#9670;&nbsp;</a></span>add()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciAPIs::add </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>entry</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Add the single raw API entry <em>entry</em> to the current set.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciAPIs.html#a6b29d84b0b5d63f2b590988195c7557c">clear()</a>, <a class="el" href="classQsciAPIs.html#a3084b749e4eb1c741fc1004e8a84a631">load()</a>, <a class="el" href="classQsciAPIs.html#acb1aa10ea05a7ee72a0d77376153b4d2">remove()</a> </dd></dl>

</div>
</div>
<a id="a6b29d84b0b5d63f2b590988195c7557c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6b29d84b0b5d63f2b590988195c7557c">&#9670;&nbsp;</a></span>clear()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciAPIs::clear </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Deletes all raw API information.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciAPIs.html#af46ca05571eb676d3aa65b080fb406c5">add()</a>, <a class="el" href="classQsciAPIs.html#a3084b749e4eb1c741fc1004e8a84a631">load()</a>, <a class="el" href="classQsciAPIs.html#acb1aa10ea05a7ee72a0d77376153b4d2">remove()</a> </dd></dl>

</div>
</div>
<a id="a3084b749e4eb1c741fc1004e8a84a631"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3084b749e4eb1c741fc1004e8a84a631">&#9670;&nbsp;</a></span>load()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciAPIs::load </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>filename</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Load the API information from the file named <em>filename</em>, adding it to the current set. Returns true if successful, otherwise false. </p>

</div>
</div>
<a id="acb1aa10ea05a7ee72a0d77376153b4d2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acb1aa10ea05a7ee72a0d77376153b4d2">&#9670;&nbsp;</a></span>remove()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciAPIs::remove </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>entry</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Remove the single raw API entry <em>entry</em> from the current set.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciAPIs.html#af46ca05571eb676d3aa65b080fb406c5">add()</a>, <a class="el" href="classQsciAPIs.html#a6b29d84b0b5d63f2b590988195c7557c">clear()</a>, <a class="el" href="classQsciAPIs.html#a3084b749e4eb1c741fc1004e8a84a631">load()</a> </dd></dl>

</div>
</div>
<a id="a5c26b8395c49cf61243e5f73c0ce577f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5c26b8395c49cf61243e5f73c0ce577f">&#9670;&nbsp;</a></span>prepare()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciAPIs::prepare </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Convert the current raw API information to prepared API information. This is implemented by a separate thread.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciAPIs.html#aa5c7c8855162eeb1be74c226ebf1b1b6">cancelPreparation()</a> </dd></dl>

</div>
</div>
<a id="aa5c7c8855162eeb1be74c226ebf1b1b6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa5c7c8855162eeb1be74c226ebf1b1b6">&#9670;&nbsp;</a></span>cancelPreparation()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciAPIs::cancelPreparation </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Cancel the conversion of the current raw API information to prepared API information.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciAPIs.html#a5c26b8395c49cf61243e5f73c0ce577f">prepare()</a> </dd></dl>

</div>
</div>
<a id="a0a080d197e8226117a626c7b4b68b32d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0a080d197e8226117a626c7b4b68b32d">&#9670;&nbsp;</a></span>defaultPreparedName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QString QsciAPIs::defaultPreparedName </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return the default name of the prepared API information file. It is based on the name of the associated lexer and in the directory defined by the QSCIDIR environment variable. If the environment variable isn't set then $HOME/.qsci is used. </p>

</div>
</div>
<a id="a9dc74576c602f1df961aa8efee652a3d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9dc74576c602f1df961aa8efee652a3d">&#9670;&nbsp;</a></span>isPrepared()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciAPIs::isPrepared </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>filename</em> = <code>QString()</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Check to see is a prepared API information file named <em>filename</em> exists. If <em>filename</em> is empty then the value returned by <a class="el" href="classQsciAPIs.html#a0a080d197e8226117a626c7b4b68b32d">defaultPreparedName()</a> is used. Returns true if successful, otherwise false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciAPIs.html#a0a080d197e8226117a626c7b4b68b32d">defaultPreparedName()</a> </dd></dl>

</div>
</div>
<a id="af42a26a050bfeb4249d35ab61567ea9e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af42a26a050bfeb4249d35ab61567ea9e">&#9670;&nbsp;</a></span>loadPrepared()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciAPIs::loadPrepared </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>filename</em> = <code>QString()</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Load the prepared API information from the file named <em>filename</em>. If <em>filename</em> is empty then a name is constructed based on the name of the associated lexer and saved in the directory defined by the QSCIDIR environment variable. If the environment variable isn't set then $HOME/.qsci is used. Returns true if successful, otherwise false. </p>

</div>
</div>
<a id="a742609f12e48e63edbab2565d7df3cb9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a742609f12e48e63edbab2565d7df3cb9">&#9670;&nbsp;</a></span>savePrepared()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciAPIs::savePrepared </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>filename</em> = <code>QString()</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Save the prepared API information to the file named <em>filename</em>. If <em>filename</em> is empty then a name is constructed based on the name of the associated lexer and saved in the directory defined by the QSCIDIR environment variable. If the environment variable isn't set then $HOME/.qsci is used. Returns true if successful, otherwise false. </p>

</div>
</div>
<a id="aa2ee3021ffc6a998776547a5c252edca"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa2ee3021ffc6a998776547a5c252edca">&#9670;&nbsp;</a></span>installedAPIFiles()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QStringList QsciAPIs::installedAPIFiles </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return a list of the installed raw API file names for the associated lexer. </p>

</div>
</div>
<a id="aaa47506820a2596004688e241fc4cd9f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaa47506820a2596004688e241fc4cd9f">&#9670;&nbsp;</a></span>apiPreparationCancelled</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciAPIs::apiPreparationCancelled </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the conversion of raw API information to prepared API information has been cancelled.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciAPIs.html#adf779559d29fed004ec65ef560483e3c">apiPreparationFinished()</a>, <a class="el" href="classQsciAPIs.html#a8fc5db618546fcfcc5bdc46e6d062995">apiPreparationStarted()</a> </dd></dl>

</div>
</div>
<a id="a8fc5db618546fcfcc5bdc46e6d062995"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8fc5db618546fcfcc5bdc46e6d062995">&#9670;&nbsp;</a></span>apiPreparationStarted</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciAPIs::apiPreparationStarted </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the conversion of raw API information to prepared API information starts and can be used to give some visual feedback to the user.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciAPIs.html#aaa47506820a2596004688e241fc4cd9f">apiPreparationCancelled()</a>, <a class="el" href="classQsciAPIs.html#adf779559d29fed004ec65ef560483e3c">apiPreparationFinished()</a> </dd></dl>

</div>
</div>
<a id="adf779559d29fed004ec65ef560483e3c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adf779559d29fed004ec65ef560483e3c">&#9670;&nbsp;</a></span>apiPreparationFinished</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciAPIs::apiPreparationFinished </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the conversion of raw API information to prepared API information has finished.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciAPIs.html#aaa47506820a2596004688e241fc4cd9f">apiPreparationCancelled()</a>, <a class="el" href="classQsciAPIs.html#a8fc5db618546fcfcc5bdc46e6d062995">apiPreparationStarted()</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
