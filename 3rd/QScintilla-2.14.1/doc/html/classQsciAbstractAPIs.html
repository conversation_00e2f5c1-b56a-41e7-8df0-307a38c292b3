<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciAbstractAPIs Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classQsciAbstractAPIs-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciAbstractAPIs Class Reference<span class="mlabels"><span class="mlabel">abstract</span></span></div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qsciabstractapis.h&gt;</code></p>

<p>Inherits QObject.</p>

<p>Inherited by <a class="el" href="classQsciAPIs.html">QsciAPIs</a>.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a9db5ebe8adda3f58892af676f5295e3a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAbstractAPIs.html#a9db5ebe8adda3f58892af676f5295e3a">QsciAbstractAPIs</a> (<a class="el" href="classQsciLexer.html">QsciLexer</a> *<a class="el" href="classQsciAbstractAPIs.html#a90452ab6f4d40314ec519913f9e78ccc">lexer</a>)</td></tr>
<tr class="separator:a9db5ebe8adda3f58892af676f5295e3a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ef866227b05482cb32c70b44e8bdec1"><td class="memItemLeft" align="right" valign="top"><a id="a7ef866227b05482cb32c70b44e8bdec1"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAbstractAPIs.html#a7ef866227b05482cb32c70b44e8bdec1">~QsciAbstractAPIs</a> ()</td></tr>
<tr class="separator:a7ef866227b05482cb32c70b44e8bdec1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a90452ab6f4d40314ec519913f9e78ccc"><td class="memItemLeft" align="right" valign="top"><a id="a90452ab6f4d40314ec519913f9e78ccc"></a>
<a class="el" href="classQsciLexer.html">QsciLexer</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAbstractAPIs.html#a90452ab6f4d40314ec519913f9e78ccc">lexer</a> () const</td></tr>
<tr class="separator:a90452ab6f4d40314ec519913f9e78ccc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a62a50642b0b058016ed2fdf0922bdee8"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAbstractAPIs.html#a62a50642b0b058016ed2fdf0922bdee8">updateAutoCompletionList</a> (const QStringList &amp;context, QStringList &amp;list)=0</td></tr>
<tr class="separator:a62a50642b0b058016ed2fdf0922bdee8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a90fa0f912b748b707967ccb722f04ddc"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAbstractAPIs.html#a90fa0f912b748b707967ccb722f04ddc">autoCompletionSelected</a> (const QString &amp;selection)</td></tr>
<tr class="separator:a90fa0f912b748b707967ccb722f04ddc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af9c6c3f8dc068371398a50c6b23dcbf4"><td class="memItemLeft" align="right" valign="top">virtual QStringList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciAbstractAPIs.html#af9c6c3f8dc068371398a50c6b23dcbf4">callTips</a> (const QStringList &amp;context, int commas, <a class="el" href="classQsciScintilla.html#a62d0174cb0a07e3f2d48fc0603192668">QsciScintilla::CallTipsStyle</a> style, QList&lt; int &gt; &amp;shifts)=0</td></tr>
<tr class="separator:af9c6c3f8dc068371398a50c6b23dcbf4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciAbstractAPIs.html" title="The QsciAbstractAPIs class represents the interface to the textual API information used in call tips ...">QsciAbstractAPIs</a> class represents the interface to the textual API information used in call tips and for auto-completion. A sub-class will provide the actual implementation of the interface. </p>
<p>API information is specific to a particular language lexer but can be shared by multiple instances of the lexer. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a9db5ebe8adda3f58892af676f5295e3a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9db5ebe8adda3f58892af676f5295e3a">&#9670;&nbsp;</a></span>QsciAbstractAPIs()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciAbstractAPIs::QsciAbstractAPIs </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classQsciLexer.html">QsciLexer</a> *&#160;</td>
          <td class="paramname"><em>lexer</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Constructs a <a class="el" href="classQsciAbstractAPIs.html" title="The QsciAbstractAPIs class represents the interface to the textual API information used in call tips ...">QsciAbstractAPIs</a> instance attached to lexer <em>lexer</em>. <em>lexer</em> becomes the instance's parent object although the instance can also be subsequently attached to other lexers. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a62a50642b0b058016ed2fdf0922bdee8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a62a50642b0b058016ed2fdf0922bdee8">&#9670;&nbsp;</a></span>updateAutoCompletionList()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciAbstractAPIs::updateAutoCompletionList </td>
          <td>(</td>
          <td class="paramtype">const QStringList &amp;&#160;</td>
          <td class="paramname"><em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">QStringList &amp;&#160;</td>
          <td class="paramname"><em>list</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">pure virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Update the list <em>list</em> with API entries derived from <em>context</em>. <em>context</em> is the list of words in the text preceding the cursor position. The characters that make up a word and the characters that separate words are defined by the lexer. The last word is a partial word and may be empty if the user has just entered a word separator. </p>

<p>Implemented in <a class="el" href="classQsciAPIs.html#ab0f824492bb0f3ca54edb4d46945a3de">QsciAPIs</a>.</p>

</div>
</div>
<a id="a90fa0f912b748b707967ccb722f04ddc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a90fa0f912b748b707967ccb722f04ddc">&#9670;&nbsp;</a></span>autoCompletionSelected()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciAbstractAPIs::autoCompletionSelected </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>selection</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This is called when the user selects the entry <em>selection</em> from the auto-completion list. A sub-class can use this as a hint to provide more specific API entries in future calls to <a class="el" href="classQsciAbstractAPIs.html#a62a50642b0b058016ed2fdf0922bdee8">updateAutoCompletionList()</a>. The default implementation does nothing. </p>

<p>Reimplemented in <a class="el" href="classQsciAPIs.html#adff0073d1f4ee2e0ea8b3bf234ff2dd3">QsciAPIs</a>.</p>

</div>
</div>
<a id="af9c6c3f8dc068371398a50c6b23dcbf4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af9c6c3f8dc068371398a50c6b23dcbf4">&#9670;&nbsp;</a></span>callTips()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual QStringList QsciAbstractAPIs::callTips </td>
          <td>(</td>
          <td class="paramtype">const QStringList &amp;&#160;</td>
          <td class="paramname"><em>context</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>commas</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classQsciScintilla.html#a62d0174cb0a07e3f2d48fc0603192668">QsciScintilla::CallTipsStyle</a>&#160;</td>
          <td class="paramname"><em>style</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">QList&lt; int &gt; &amp;&#160;</td>
          <td class="paramname"><em>shifts</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">pure virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Return the call tips valid for the context <em>context</em>. (Note that the last word of the context will always be empty.) <em>commas</em> is the number of commas the user has typed after the context and before the cursor position. The exact position of the list of call tips can be adjusted by specifying a corresponding left character shift in <em>shifts</em>. This is normally done to correct for any displayed context according to <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciAbstractAPIs.html#a62a50642b0b058016ed2fdf0922bdee8">updateAutoCompletionList()</a> </dd></dl>

<p>Implemented in <a class="el" href="classQsciAPIs.html#a6a75974235c5e8d263bf962c778b3a3e">QsciAPIs</a>.</p>

</div>
</div>
</div><!-- contents -->
<div class="ttc" id="aclassQsciStyle_html_af45628c04ab5488fc13b61a2356346ec"><div class="ttname"><a href="classQsciStyle.html#af45628c04ab5488fc13b61a2356346ec">QsciStyle::font</a></div><div class="ttdeci">QFont font() const</div><div class="ttdef"><b>Definition:</b> qscistyle.h:131</div></div>
<div class="ttc" id="aclassQsciLexerIntelHex_html"><div class="ttname"><a href="classQsciLexerIntelHex.html">QsciLexerIntelHex</a></div><div class="ttdoc">The QsciLexerIntelHex class encapsulates the Scintilla Intel Hex lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerintelhex.h:33</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a9c1818383be531c3b04cd6848145d63b"><div class="ttname"><a href="classQsciScintilla.html#a9c1818383be531c3b04cd6848145d63b">QsciScintilla::bytes</a></div><div class="ttdeci">QByteArray bytes(int start, int end) const</div></div>
<div class="ttc" id="aclassQsciLexerOctave_html_a72ce450fad8282f4c02cf28fc6a4b9d2"><div class="ttname"><a href="classQsciLexerOctave.html#a72ce450fad8282f4c02cf28fc6a4b9d2">QsciLexerOctave::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexerJavaScript_html"><div class="ttname"><a href="classQsciLexerJavaScript.html">QsciLexerJavaScript</a></div><div class="ttdoc">The QsciLexerJavaScript class encapsulates the Scintilla JavaScript lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerjavascript.h:33</div></div>
<div class="ttc" id="aclassQsciLexer_html_a0aa2c7bc939d793db01bbc1863b15d63"><div class="ttname"><a href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">QsciLexer::apis</a></div><div class="ttdeci">QsciAbstractAPIs * apis() const</div></div>
<div class="ttc" id="aclassQsciLexerSpice_html_ae41f7a78d82f09b4d5176ec2a709ac67"><div class="ttname"><a href="classQsciLexerSpice.html#ae41f7a78d82f09b4d5176ec2a709ac67">QsciLexerSpice::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerJava_html_ad741254381ce4447588d190ad9c67783"><div class="ttname"><a href="classQsciLexerJava.html#ad741254381ce4447588d190ad9c67783">QsciLexerJava::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexerXML_html"><div class="ttname"><a href="classQsciLexerXML.html">QsciLexerXML</a></div><div class="ttdoc">The QsciLexerXML class encapsulates the Scintilla XML lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerxml.h:32</div></div>
<div class="ttc" id="aclassQsciPrinter_html_a0bd255795c503a091ddc76d3564e2aac"><div class="ttname"><a href="classQsciPrinter.html#a0bd255795c503a091ddc76d3564e2aac">QsciPrinter::printRange</a></div><div class="ttdeci">virtual int printRange(QsciScintillaBase *qsb, QPainter &amp;painter, int from=-1, int to=-1)</div></div>
<div class="ttc" id="aclassQsciLexerFortran77_html_a08b8ae54fae5b280a3864d5696fe009e"><div class="ttname"><a href="classQsciLexerFortran77.html#a08b8ae54fae5b280a3864d5696fe009e">QsciLexerFortran77::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a58506e1c965a181c9202376e0ba85c30"><div class="ttname"><a href="classQsciLexerCPP.html#a58506e1c965a181c9202376e0ba85c30">QsciLexerCPP::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ae8d8fa5d5f063a7c7d37d527f86b5fe8"><div class="ttname"><a href="classQsciScintillaBase.html#ae8d8fa5d5f063a7c7d37d527f86b5fe8">QsciScintillaBase::SCN_CHARADDED</a></div><div class="ttdeci">void SCN_CHARADDED(int charadded)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a02105d06ad853647906eb72d27face67"><div class="ttname"><a href="classQsciScintilla.html#a02105d06ad853647906eb72d27face67">QsciScintilla::setAutoIndent</a></div><div class="ttdeci">virtual void setAutoIndent(bool autoindent)</div></div>
<div class="ttc" id="aclassQsciLexerCSharp_html_a024d39004611b62884f258c417b5acd3"><div class="ttname"><a href="classQsciLexerCSharp.html#a024d39004611b62884f258c417b5acd3">QsciLexerCSharp::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a37a46d7dbbb88374f4651feb64f55926"><div class="ttname"><a href="classQsciScintilla.html#a37a46d7dbbb88374f4651feb64f55926">QsciScintilla::extraAscent</a></div><div class="ttdeci">int extraAscent() const</div></div>
<div class="ttc" id="aclassQsciAPIs_html_adf779559d29fed004ec65ef560483e3c"><div class="ttname"><a href="classQsciAPIs.html#adf779559d29fed004ec65ef560483e3c">QsciAPIs::apiPreparationFinished</a></div><div class="ttdeci">void apiPreparationFinished()</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a9261ce8b2aa1dda886e5259fbd6b8712"><div class="ttname"><a href="classQsciScintilla.html#a9261ce8b2aa1dda886e5259fbd6b8712">QsciScintilla::userListActivated</a></div><div class="ttdeci">void userListActivated(int id, const QString &amp;string)</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_ae32591385112bb3d33de8c1c40888190"><div class="ttname"><a href="classQsciLexerCSS.html#ae32591385112bb3d33de8c1c40888190">QsciLexerCSS::foldComments</a></div><div class="ttdeci">bool foldComments() const</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_a73e71780818247bf678616a25cd13e90"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#a73e71780818247bf678616a25cd13e90">QsciLexerCoffeeScript::QsciLexerCoffeeScript</a></div><div class="ttdeci">QsciLexerCoffeeScript(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_afea26881f9979a1769ba85c668351cee"><div class="ttname"><a href="classQsciScintilla.html#afea26881f9979a1769ba85c668351cee">QsciScintilla::foldAll</a></div><div class="ttdeci">virtual void foldAll(bool children=false)</div></div>
<div class="ttc" id="aclassQsciStyle_html_a0464f0a24f4094431686c89e667e843e"><div class="ttname"><a href="classQsciStyle.html#a0464f0a24f4094431686c89e667e843e">QsciStyle::QsciStyle</a></div><div class="ttdeci">QsciStyle(int style=-1)</div></div>
<div class="ttc" id="aclassQsciLexerTCL_html_a8739852ad69fa4686f0fabd61d18b214"><div class="ttname"><a href="classQsciLexerTCL.html#a8739852ad69fa4686f0fabd61d18b214">QsciLexerTCL::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexerMarkdown_html_acecf54d7daf87ff9fc5464fac8f1d502"><div class="ttname"><a href="classQsciLexerMarkdown.html#acecf54d7daf87ff9fc5464fac8f1d502">QsciLexerMarkdown::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a43b84483d91245a1fda14b758cd8b052"><div class="ttname"><a href="classQsciScintilla.html#a43b84483d91245a1fda14b758cd8b052">QsciScintilla::isModified</a></div><div class="ttdeci">bool isModified() const</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_ade18380cb4440beca5452b908d66f5c9"><div class="ttname"><a href="classQsciLexerAsm.html#ade18380cb4440beca5452b908d66f5c9">QsciLexerAsm::foldSyntaxBased</a></div><div class="ttdeci">bool foldSyntaxBased() const</div></div>
<div class="ttc" id="aclassQsciLexer_html_ad472b16506a4cbc19634f07aa90f1ea6"><div class="ttname"><a href="classQsciLexer.html#ad472b16506a4cbc19634f07aa90f1ea6">QsciLexer::readProperties</a></div><div class="ttdeci">virtual bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerCMake_html_aa3c50f5bd00d091303665066c8f4d741"><div class="ttname"><a href="classQsciLexerCMake.html#aa3c50f5bd00d091303665066c8f4d741">QsciLexerCMake::foldAtElse</a></div><div class="ttdeci">bool foldAtElse() const</div></div>
<div class="ttc" id="aclassQsciLexerCMake_html_a0aa2f537e70f47e6a3e1bcf6d383a480"><div class="ttname"><a href="classQsciLexerCMake.html#a0aa2f537e70f47e6a3e1bcf6d383a480">QsciLexerCMake::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerDiff_html_aec71281020211f0e693143520f232079"><div class="ttname"><a href="classQsciLexerDiff.html#aec71281020211f0e693143520f232079">QsciLexerDiff::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerSRec_html_ab53b28ee54f0463f5e5d499d86e81462"><div class="ttname"><a href="classQsciLexerSRec.html#ab53b28ee54f0463f5e5d499d86e81462">QsciLexerSRec::lexer</a></div><div class="ttdeci">const char * lexer() const</div><div class="ttdoc">Returns the name of the lexer.</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a4272087bb0000cf8fd5dfa17a9b71383"><div class="ttname"><a href="classQsciLexerSQL.html#a4272087bb0000cf8fd5dfa17a9b71383">QsciLexerSQL::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a9c3a34cb9edf25913af16c9dc284cc5d"><div class="ttname"><a href="classQsciScintilla.html#a9c3a34cb9edf25913af16c9dc284cc5d">QsciScintilla::setMargins</a></div><div class="ttdeci">void setMargins(int margins)</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_ada48a387b3e1414927bebe2415de75f8"><div class="ttname"><a href="classQsciLexerCSS.html#ada48a387b3e1414927bebe2415de75f8">QsciLexerCSS::blockEnd</a></div><div class="ttdeci">const char * blockEnd(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciAPIs_html_aaf185d65d1034087b77995d8490b6475"><div class="ttname"><a href="classQsciAPIs.html#aaf185d65d1034087b77995d8490b6475">QsciAPIs::QsciAPIs</a></div><div class="ttdeci">QsciAPIs(QsciLexer *lexer)</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a0315e5b984e4ecd8ae2b0131cb78bf95"><div class="ttname"><a href="classQsciLexerD.html#a0315e5b984e4ecd8ae2b0131cb78bf95">QsciLexerD::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciCommand_html_aeaecb067c0834ba132e204a09dd942c7"><div class="ttname"><a href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7">QsciCommand::Command</a></div><div class="ttdeci">Command</div><div class="ttdoc">This enum defines the different commands that can be assigned to a key.</div><div class="ttdef"><b>Definition:</b> qscicommand.h:43</div></div>
<div class="ttc" id="aclassQsciLexerEDIFACT_html_a493b8afa5100c40153aac6f74c78ffa8"><div class="ttname"><a href="classQsciLexerEDIFACT.html#a493b8afa5100c40153aac6f74c78ffa8">QsciLexerEDIFACT::~QsciLexerEDIFACT</a></div><div class="ttdeci">virtual ~QsciLexerEDIFACT()</div><div class="ttdoc">Destroys the QsciLexerEDIFACT instance.</div></div>
<div class="ttc" id="aclassQsciLexerBatch_html_a9f0ad3d0c52cc81d43a0b733558e2392"><div class="ttname"><a href="classQsciLexerBatch.html#a9f0ad3d0c52cc81d43a0b733558e2392">QsciLexerBatch::QsciLexerBatch</a></div><div class="ttdeci">QsciLexerBatch(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerEDIFACT_html_adeff8cbfdc624ad709cd1fd8a4a93c80"><div class="ttname"><a href="classQsciLexerEDIFACT.html#adeff8cbfdc624ad709cd1fd8a4a93c80">QsciLexerEDIFACT::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a84ab3eb3c8e81cc58bbf2d3f472e757c"><div class="ttname"><a href="classQsciScintilla.html#a84ab3eb3c8e81cc58bbf2d3f472e757c">QsciScintilla::event</a></div><div class="ttdeci">virtual bool event(QEvent *e)</div><div class="ttdoc">\reimp</div></div>
<div class="ttc" id="aclassQsciLexerPO_html_accea86f8532bd5d83dac9c4b771bafa9"><div class="ttname"><a href="classQsciLexerPO.html#accea86f8532bd5d83dac9c4b771bafa9">QsciLexerPO::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae8277ccb3a2af0ae9a1495d8f8ea0523"><div class="ttname"><a href="classQsciScintilla.html#ae8277ccb3a2af0ae9a1495d8f8ea0523">QsciScintilla::BraceMatch</a></div><div class="ttdeci">BraceMatch</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:133</div></div>
<div class="ttc" id="aclassQsciLexerEDIFACT_html_a55d08e564f88f40f5167c52bd686b61c"><div class="ttname"><a href="classQsciLexerEDIFACT.html#a55d08e564f88f40f5167c52bd686b61c">QsciLexerEDIFACT::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aef97a9061de95a09b57d527f6410881dabc9d1fe2afaf18bbe19f2f4eff151576"><div class="ttname"><a href="classQsciScintilla.html#aef97a9061de95a09b57d527f6410881dabc9d1fe2afaf18bbe19f2f4eff151576">QsciScintilla::CallTipsBelowText</a></div><div class="ttdeci">@ CallTipsBelowText</div><div class="ttdoc">Call tips are placed below the text.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:149</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_aba9c88201491763d75a8716d118a4079"><div class="ttname"><a href="classQsciLexerJSON.html#aba9c88201491763d75a8716d118a4079">QsciLexerJSON::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerD_html_aa7bcbfe8a9e732630bba54860888e9d5"><div class="ttname"><a href="classQsciLexerD.html#aa7bcbfe8a9e732630bba54860888e9d5">QsciLexerD::setFoldComments</a></div><div class="ttdeci">virtual void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerMarkdown_html_ab4a9d2b6e3aeee22d7636072f5163499"><div class="ttname"><a href="classQsciLexerMarkdown.html#ab4a9d2b6e3aeee22d7636072f5163499">QsciLexerMarkdown::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a16841e0262d8200d5ed3a85099d45b37"><div class="ttname"><a href="classQsciLexerPerl.html#a16841e0262d8200d5ed3a85099d45b37">QsciLexerPerl::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ac466f32c3d7e51790b6b25c864783179"><div class="ttname"><a href="classQsciScintilla.html#ac466f32c3d7e51790b6b25c864783179">QsciScintilla::AutoCompletionSource</a></div><div class="ttdeci">AutoCompletionSource</div><div class="ttdoc">This enum defines the different sources for auto-completion lists.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:116</div></div>
<div class="ttc" id="aclassQsciLexerNASM_html_a252023a8e12a490e52c5bb6fd9a37556"><div class="ttname"><a href="classQsciLexerNASM.html#a252023a8e12a490e52c5bb6fd9a37556">QsciLexerNASM::QsciLexerNASM</a></div><div class="ttdeci">QsciLexerNASM(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aeda9c17a6e746e177fa6f5311d96dc03"><div class="ttname"><a href="classQsciScintilla.html#aeda9c17a6e746e177fa6f5311d96dc03">QsciScintilla::setAnnotationDisplay</a></div><div class="ttdeci">void setAnnotationDisplay(AnnotationDisplay display)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a626103a61623dd360dc44210fe435ad7"><div class="ttname"><a href="classQsciScintilla.html#a626103a61623dd360dc44210fe435ad7">QsciScintilla::setMarginOptions</a></div><div class="ttdeci">void setMarginOptions(int options)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae21678ec76acecd0bdd960fbd6a81843"><div class="ttname"><a href="classQsciScintilla.html#ae21678ec76acecd0bdd960fbd6a81843">QsciScintilla::markerDefine</a></div><div class="ttdeci">int markerDefine(const QImage &amp;im, int markerNumber=-1)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a93d1e96c88745ca7f2737602e80dc76a"><div class="ttname"><a href="classQsciScintillaBase.html#a93d1e96c88745ca7f2737602e80dc76a">QsciScintillaBase::SCN_INDICATORRELEASE</a></div><div class="ttdeci">void SCN_INDICATORRELEASE(int position, int modifiers)</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_af2acfd7b7a9012577aed90f136ad3fb1"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#af2acfd7b7a9012577aed90f136ad3fb1">QsciLexerCoffeeScript::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a0d273a0573088d9fb6d66d7b2633ea4b"><div class="ttname"><a href="classQsciScintilla.html#a0d273a0573088d9fb6d66d7b2633ea4b">QsciScintilla::setAutoCompletionFillupsEnabled</a></div><div class="ttdeci">void setAutoCompletionFillupsEnabled(bool enabled)</div></div>
<div class="ttc" id="aclassQsciLexerHex_html_a144ed6067644aa5a7a2b7e6c9c45250a"><div class="ttname"><a href="classQsciLexerHex.html#a144ed6067644aa5a7a2b7e6c9c45250a">QsciLexerHex::~QsciLexerHex</a></div><div class="ttdeci">virtual ~QsciLexerHex()</div><div class="ttdoc">Destroys the QsciLexerHex instance.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8c227f8c948aeb5e6a2cf73397387cc0"><div class="ttname"><a href="classQsciScintilla.html#a8c227f8c948aeb5e6a2cf73397387cc0">QsciScintilla::setCaretLineBackgroundColor</a></div><div class="ttdeci">virtual void setCaretLineBackgroundColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ab3721e1af5d7a71efff5a35b63ed1174"><div class="ttname"><a href="classQsciScintilla.html#ab3721e1af5d7a71efff5a35b63ed1174">QsciScintilla::annotate</a></div><div class="ttdeci">void annotate(int line, const QsciStyledText &amp;text)</div><div class="ttdoc">Annotate the line line with the styled text text.</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_a497144db9b43beba78cd405a795e08ac"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#a497144db9b43beba78cd405a795e08ac">QsciLexerCoffeeScript::blockStartKeyword</a></div><div class="ttdeci">const char * blockStartKeyword(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a830f23268edb039344d30cef52d7c8f3"><div class="ttname"><a href="classQsciScintilla.html#a830f23268edb039344d30cef52d7c8f3">QsciScintilla::getCursorPosition</a></div><div class="ttdeci">void getCursorPosition(int *line, int *index) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a064b51eca1ab2d32d4c4c328e69a395d"><div class="ttname"><a href="classQsciScintilla.html#a064b51eca1ab2d32d4c4c328e69a395d">QsciScintilla::setMarginSensitivity</a></div><div class="ttdeci">virtual void setMarginSensitivity(int margin, bool sens)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_acc3981a1149b87b38f93195bb0e00f34"><div class="ttname"><a href="classQsciScintilla.html#acc3981a1149b87b38f93195bb0e00f34">QsciScintilla::annotate</a></div><div class="ttdeci">void annotate(int line, const QList&lt; QsciStyledText &gt; &amp;text)</div><div class="ttdoc">Annotate the line line with the list of styled text text.</div></div>
<div class="ttc" id="aclassQsciLexerCMake_html_aa9285b175e0d9964e427f047f484d0e5"><div class="ttname"><a href="classQsciLexerCMake.html#aa9285b175e0d9964e427f047f484d0e5">QsciLexerCMake::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a761b431d688aa99c5c9b5110b41dc712"><div class="ttname"><a href="classQsciLexerCPP.html#a761b431d688aa99c5c9b5110b41dc712">QsciLexerCPP::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html_a81437ae22cb610108f29d8d367ce6faa"><div class="ttname"><a href="classQsciLexerProperties.html#a81437ae22cb610108f29d8d367ce6faa">QsciLexerProperties::QsciLexerProperties</a></div><div class="ttdeci">QsciLexerProperties(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerSpice_html_a5e8a0548fb4c972f0eb0070721dd7762"><div class="ttname"><a href="classQsciLexerSpice.html#a5e8a0548fb4c972f0eb0070721dd7762">QsciLexerSpice::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerCSharp_html_a07fcac621f1cba033bb0918cf9d35231"><div class="ttname"><a href="classQsciLexerCSharp.html#a07fcac621f1cba033bb0918cf9d35231">QsciLexerCSharp::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a900e3a0287e262fe65c51162e562fc5d"><div class="ttname"><a href="classQsciScintillaBase.html#a900e3a0287e262fe65c51162e562fc5d">QsciScintillaBase::replaceVerticalScrollBar</a></div><div class="ttdeci">void replaceVerticalScrollBar(QScrollBar *scrollBar)</div></div>
<div class="ttc" id="aclassQsciLexerIntelHex_html_ac555cadb9fbc0dec91f7630d019f1100"><div class="ttname"><a href="classQsciLexerIntelHex.html#ac555cadb9fbc0dec91f7630d019f1100">QsciLexerIntelHex::lexer</a></div><div class="ttdeci">const char * lexer() const</div><div class="ttdoc">Returns the name of the lexer.</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_ae1451be7e7c6a57f28f361c72eb68a5f"><div class="ttname"><a href="classQsciLexerCPP.html#ae1451be7e7c6a57f28f361c72eb68a5f">QsciLexerCPP::QsciLexerCPP</a></div><div class="ttdeci">QsciLexerCPP(QObject *parent=0, bool caseInsensitiveKeywords=false)</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a0e4df63d7d5714b1bdb71c1975f7f99c"><div class="ttname"><a href="classQsciLexerPerl.html#a0e4df63d7d5714b1bdb71c1975f7f99c">QsciLexerPerl::autoCompletionWordSeparators</a></div><div class="ttdeci">QStringList autoCompletionWordSeparators() const</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_accb7e71496e6817503ea1c081ffdbab4"><div class="ttname"><a href="classQsciLexerPostScript.html#accb7e71496e6817503ea1c081ffdbab4">QsciLexerPostScript::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div></div>
<div class="ttc" id="aclassQsciLexerEDIFACT_html"><div class="ttname"><a href="classQsciLexerEDIFACT.html">QsciLexerEDIFACT</a></div><div class="ttdoc">The QsciLexerEDIFACT class encapsulates the Scintilla EDIFACT lexer.</div><div class="ttdef"><b>Definition:</b> qscilexeredifact.h:32</div></div>
<div class="ttc" id="aclassQsciLexerFortran77_html"><div class="ttname"><a href="classQsciLexerFortran77.html">QsciLexerFortran77</a></div><div class="ttdoc">The QsciLexerFortran77 class encapsulates the Scintilla Fortran77 lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerfortran77.h:33</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_aeec8d7e585e93451307df88ff2fc2b87"><div class="ttname"><a href="classQsciScintillaBase.html#aeec8d7e585e93451307df88ff2fc2b87">QsciScintillaBase::SCN_INDICATORCLICK</a></div><div class="ttdeci">void SCN_INDICATORCLICK(int position, int modifiers)</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_a554d4c9b25ad66e23092adf6f9b0460e"><div class="ttname"><a href="classQsciLexerBash.html#a554d4c9b25ad66e23092adf6f9b0460e">QsciLexerBash::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_af45a578123a772bdb293d326c29218dc"><div class="ttname"><a href="classQsciLexerRuby.html#af45a578123a772bdb293d326c29218dc">QsciLexerRuby::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a1a8b798b6b36db2b5f60b0cdccba88a1"><div class="ttname"><a href="classQsciScintilla.html#a1a8b798b6b36db2b5f60b0cdccba88a1">QsciScintilla::setMarginWidth</a></div><div class="ttdeci">virtual void setMarginWidth(int margin, int width)</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html"><div class="ttname"><a href="classQsciLexerTeX.html">QsciLexerTeX</a></div><div class="ttdoc">The QsciLexerTeX class encapsulates the Scintilla TeX lexer.</div><div class="ttdef"><b>Definition:</b> qscilexertex.h:32</div></div>
<div class="ttc" id="aclassQsciLexerCMake_html_af84de1838a68d08ef99be3aa666dc09f"><div class="ttname"><a href="classQsciLexerCMake.html#af84de1838a68d08ef99be3aa666dc09f">QsciLexerCMake::QsciLexerCMake</a></div><div class="ttdeci">QsciLexerCMake(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerMatlab_html_a62234f5c4dfbeec23fd43dd6651d65e4"><div class="ttname"><a href="classQsciLexerMatlab.html#a62234f5c4dfbeec23fd43dd6651d65e4">QsciLexerMatlab::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a46bd37b48e91903451ab59314448f322"><div class="ttname"><a href="classQsciLexerCPP.html#a46bd37b48e91903451ab59314448f322">QsciLexerCPP::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_a402a849f5eed391f0c4cd3aac9beb075"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#a402a849f5eed391f0c4cd3aac9beb075">QsciLexerCoffeeScript::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerYAML_html_af48deb2ec781d4c8c08f17530b9516d3"><div class="ttname"><a href="classQsciLexerYAML.html#af48deb2ec781d4c8c08f17530b9516d3">QsciLexerYAML::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerPO_html_ace592f4a2d86db6be6c6f363227c00ee"><div class="ttname"><a href="classQsciLexerPO.html#ace592f4a2d86db6be6c6f363227c00ee">QsciLexerPO::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_a57a2659a5ea9eba6898e3ced0565953f"><div class="ttname"><a href="classQsciLexerBash.html#a57a2659a5ea9eba6898e3ced0565953f">QsciLexerBash::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html"><div class="ttname"><a href="classQsciLexerCSS.html">QsciLexerCSS</a></div><div class="ttdoc">The QsciLexerCSS class encapsulates the Scintilla CSS lexer.</div><div class="ttdef"><b>Definition:</b> qscilexercss.h:32</div></div>
<div class="ttc" id="aclassQsciLexerD_html_adcc24b17317e0e283230ae8d5ccf1de3"><div class="ttname"><a href="classQsciLexerD.html#adcc24b17317e0e283230ae8d5ccf1de3">QsciLexerD::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_ab2e7d189deabf8e5e20434e32346742c"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#ab2e7d189deabf8e5e20434e32346742c">QsciLexerCoffeeScript::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciStyle_html_af349ce169da83e08ad9f995df48c6547"><div class="ttname"><a href="classQsciStyle.html#af349ce169da83e08ad9f995df48c6547">QsciStyle::color</a></div><div class="ttdeci">QColor color() const</div><div class="ttdef"><b>Definition:</b> qscistyle.h:109</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a667febcf6234a15b7ca6d4ddbfb97bc6"><div class="ttname"><a href="classQsciLexerD.html#a667febcf6234a15b7ca6d4ddbfb97bc6">QsciLexerD::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_ad70da8e3f3695cfc277d02ab9c0396d3"><div class="ttname"><a href="classQsciLexerVerilog.html#ad70da8e3f3695cfc277d02ab9c0396d3">QsciLexerVerilog::foldPreprocessor</a></div><div class="ttdeci">bool foldPreprocessor() const</div><div class="ttdef"><b>Definition:</b> qscilexerverilog.h:213</div></div>
<div class="ttc" id="aclassQsciLexer_html_ac2e1ada934a5dc7685c1ee6a464de5fd"><div class="ttname"><a href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">QsciLexer::setAPIs</a></div><div class="ttdeci">void setAPIs(QsciAbstractAPIs *apis)</div></div>
<div class="ttc" id="aclassQsciLexerMakefile_html_a42e28c95e4f32374ffb7b47a85239d34"><div class="ttname"><a href="classQsciLexerMakefile.html#a42e28c95e4f32374ffb7b47a85239d34">QsciLexerMakefile::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_affaec4d14f7908f7d24d16937df00c93"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#affaec4d14f7908f7d24d16937df00c93">QsciLexerCoffeeScript::setDollarsAllowed</a></div><div class="ttdeci">void setDollarsAllowed(bool allowed)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae1d3703631c9113a4309da17332ca180"><div class="ttname"><a href="classQsciScintilla.html#ae1d3703631c9113a4309da17332ca180">QsciScintilla::resetFoldMarginColors</a></div><div class="ttdeci">void resetFoldMarginColors()</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_a96aca8cf94d490d3c7c11e71d823a9ee"><div class="ttname"><a href="classQsciLexerBash.html#a96aca8cf94d490d3c7c11e71d823a9ee">QsciLexerBash::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_a46a01d03d516e909c8696fa3f9910c1f"><div class="ttname"><a href="classQsciLexerVHDL.html#a46a01d03d516e909c8696fa3f9910c1f">QsciLexerVHDL::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_aab7c86d0058b7c8541b0fc7be043f902"><div class="ttname"><a href="classQsciLexerTeX.html#aab7c86d0058b7c8541b0fc7be043f902">QsciLexerTeX::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a9d05744ee6d4c653a7e3976d9f71df23"><div class="ttname"><a href="classQsciLexerSQL.html#a9d05744ee6d4c653a7e3976d9f71df23">QsciLexerSQL::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_a04a2eaa1d93a2266bd170d392b70860b"><div class="ttname"><a href="classQsciLexerJSON.html#a04a2eaa1d93a2266bd170d392b70860b">QsciLexerJSON::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_ab47735b5b8b7961044bb9adf111c06bc"><div class="ttname"><a href="classQsciLexerPascal.html#ab47735b5b8b7961044bb9adf111c06bc">QsciLexerPascal::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ab6856c73ad57ed92d8a9e4882867559e"><div class="ttname"><a href="classQsciScintilla.html#ab6856c73ad57ed92d8a9e4882867559e">QsciScintilla::indicatorDefine</a></div><div class="ttdeci">int indicatorDefine(IndicatorStyle style, int indicatorNumber=-1)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae8277ccb3a2af0ae9a1495d8f8ea0523adedf5faefc13c44ee7abd4cf142b03a4"><div class="ttname"><a href="classQsciScintilla.html#ae8277ccb3a2af0ae9a1495d8f8ea0523adedf5faefc13c44ee7abd4cf142b03a4">QsciScintilla::NoBraceMatch</a></div><div class="ttdeci">@ NoBraceMatch</div><div class="ttdoc">Brace matching is disabled.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:135</div></div>
<div class="ttc" id="aclassQsciLexerCMake_html_abd4b8e9e29aa577fdd546ef0de20c7ff"><div class="ttname"><a href="classQsciLexerCMake.html#abd4b8e9e29aa577fdd546ef0de20c7ff">QsciLexerCMake::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a5668132073d8c3d97ea56dc7131c2def"><div class="ttname"><a href="classQsciLexerSQL.html#a5668132073d8c3d97ea56dc7131c2def">QsciLexerSQL::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a856785e000203b1da8fa6f295daad13e"><div class="ttname"><a href="classQsciLexerPython.html#a856785e000203b1da8fa6f295daad13e">QsciLexerPython::setV3BytesAllowed</a></div><div class="ttdeci">void setV3BytesAllowed(bool allowed)</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_ac97e486c8c1f2233c0b35e744ef5a393"><div class="ttname"><a href="classQsciLexerSQL.html#ac97e486c8c1f2233c0b35e744ef5a393">QsciLexerSQL::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciLexerXML_html_a801d7cef474dcf23d93e2f2f53034abe"><div class="ttname"><a href="classQsciLexerXML.html#a801d7cef474dcf23d93e2f2f53034abe">QsciLexerXML::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a1deaafed565aeae806e4ea6083baa186"><div class="ttname"><a href="classQsciLexerPerl.html#a1deaafed565aeae806e4ea6083baa186">QsciLexerPerl::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_af17ac732d73445822ef23a59f3e45aef"><div class="ttname"><a href="classQsciLexerCPP.html#af17ac732d73445822ef23a59f3e45aef">QsciLexerCPP::setFoldCompact</a></div><div class="ttdeci">virtual void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_af25249a8e4e0f0966395b5006a5362d9"><div class="ttname"><a href="classQsciScintillaBase.html#af25249a8e4e0f0966395b5006a5362d9">QsciScintillaBase::dragMoveEvent</a></div><div class="ttdeci">virtual void dragMoveEvent(QDragMoveEvent *e)</div><div class="ttdoc">Re-implemented to handle drag moves.</div></div>
<div class="ttc" id="aclassQsciLexer_html_a793e592d3ac100ff81ae09eefbaa74ef"><div class="ttname"><a href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">QsciLexer::setAutoIndentStyle</a></div><div class="ttdeci">virtual void setAutoIndentStyle(int autoindentstyle)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca2bbb4d3dea9e0b75ef9374c1c2b23c65"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca2bbb4d3dea9e0b75ef9374c1c2b23c65">QsciScintillaBase::SC_MARK_CIRCLEMINUSCONNECTED</a></div><div class="ttdeci">@ SC_MARK_CIRCLEMINUSCONNECTED</div><div class="ttdoc">A drawn minus sign in a connected circle.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2631</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a149b39fedd3779fe797cf7c9ae793c8a"><div class="ttname"><a href="classQsciScintilla.html#a149b39fedd3779fe797cf7c9ae793c8a">QsciScintilla::clear</a></div><div class="ttdeci">virtual void clear()</div><div class="ttdoc">Deletes all the text in the text edit.</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_afd8eebb9ee14760d1529f614f18a2e52"><div class="ttname"><a href="classQsciLexerVHDL.html#afd8eebb9ee14760d1529f614f18a2e52">QsciLexerVHDL::foldAtParenthesis</a></div><div class="ttdeci">bool foldAtParenthesis() const</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a9a7c5fb256df97053fbe3203aaf3a93a"><div class="ttname"><a href="classQsciLexerSQL.html#a9a7c5fb256df97053fbe3203aaf3a93a">QsciLexerSQL::foldOnlyBegin</a></div><div class="ttdeci">bool foldOnlyBegin() const</div><div class="ttdef"><b>Definition:</b> qscilexersql.h:206</div></div>
<div class="ttc" id="aclassQsciStyle_html_a4e1d6840e7d117886093bbaabbccd56f"><div class="ttname"><a href="classQsciStyle.html#a4e1d6840e7d117886093bbaabbccd56f">QsciStyle::QsciStyle</a></div><div class="ttdeci">QsciStyle(int style, const QString &amp;description, const QColor &amp;color, const QColor &amp;paper, const QFont &amp;font, bool eolFill=false)</div></div>
<div class="ttc" id="aclassQsciLexerMASM_html_a4b6d26169eca1609b3fd1ef979cb6e8f"><div class="ttname"><a href="classQsciLexerMASM.html#a4b6d26169eca1609b3fd1ef979cb6e8f">QsciLexerMASM::lexer</a></div><div class="ttdeci">const char * lexer() const</div><div class="ttdoc">Returns the name of the lexer.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8fdc8049cd5d876b10c2fd9149e72699"><div class="ttname"><a href="classQsciScintilla.html#a8fdc8049cd5d876b10c2fd9149e72699">QsciScintilla::callTipsVisible</a></div><div class="ttdeci">int callTipsVisible() const</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:653</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a62032a66c22767af46af4611fb672cb3"><div class="ttname"><a href="classQsciLexerD.html#a62032a66c22767af46af4611fb672cb3">QsciLexerD::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_a16d546ecc7d16a609e368a4d2d557605"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#a16d546ecc7d16a609e368a4d2d557605">QsciLexerCoffeeScript::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div><div class="ttdef"><b>Definition:</b> qscilexercoffeescript.h:214</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a0b1959541108a437dcb0b104a46f1444"><div class="ttname"><a href="classQsciLexerSQL.html#a0b1959541108a437dcb0b104a46f1444">QsciLexerSQL::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html_ad65ebfab947de5d6e318238f8a0048e4"><div class="ttname"><a href="classQsciLexerAVS.html#ad65ebfab947de5d6e318238f8a0048e4">QsciLexerAVS::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_a0e83f239ecb3c52bf4930412f32f51f1"><div class="ttname"><a href="classQsciLexerRuby.html#a0e83f239ecb3c52bf4930412f32f51f1">QsciLexerRuby::setFoldCompact</a></div><div class="ttdeci">void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_a5f3257db4289b4581958fd8f36b7fe90"><div class="ttname"><a href="classQsciLexerAsm.html#a5f3257db4289b4581958fd8f36b7fe90">QsciLexerAsm::QsciLexerAsm</a></div><div class="ttdeci">QsciLexerAsm(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a21891669bab4719e8e7cf482e3bf5a51"><div class="ttname"><a href="classQsciLexerPython.html#a21891669bab4719e8e7cf482e3bf5a51">QsciLexerPython::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div><div class="ttdef"><b>Definition:</b> qscilexerpython.h:199</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_af0ee6abab37e283e68f527c597c50877"><div class="ttname"><a href="classQsciLexerPerl.html#af0ee6abab37e283e68f527c597c50877">QsciLexerPerl::setFoldPODBlocks</a></div><div class="ttdeci">void setFoldPODBlocks(bool fold)</div></div>
<div class="ttc" id="aclassQsciStyle_html_a16212f9f46162f67ece3ed6423207785"><div class="ttname"><a href="classQsciStyle.html#a16212f9f46162f67ece3ed6423207785">QsciStyle::textCase</a></div><div class="ttdeci">TextCase textCase() const</div><div class="ttdef"><b>Definition:</b> qscistyle.h:152</div></div>
<div class="ttc" id="aclassQsciLexerSpice_html_aee09ddca3fd840b79ae954f6883fa581"><div class="ttname"><a href="classQsciLexerSpice.html#aee09ddca3fd840b79ae954f6883fa581">QsciLexerSpice::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a794e5cccbc1aa419bad4c7cfe65f8f02"><div class="ttname"><a href="classQsciScintilla.html#a794e5cccbc1aa419bad4c7cfe65f8f02">QsciScintilla::text</a></div><div class="ttdeci">QString text(int start, int end) const</div></div>
<div class="ttc" id="aclassQsciLexerSpice_html_a7d38d89680d55b7f77463e67634f84be"><div class="ttname"><a href="classQsciLexerSpice.html#a7d38d89680d55b7f77463e67634f84be">QsciLexerSpice::QsciLexerSpice</a></div><div class="ttdeci">QsciLexerSpice(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciAPIs_html_a5c26b8395c49cf61243e5f73c0ce577f"><div class="ttname"><a href="classQsciAPIs.html#a5c26b8395c49cf61243e5f73c0ce577f">QsciAPIs::prepare</a></div><div class="ttdeci">void prepare()</div></div>
<div class="ttc" id="aclassQsciLexerOctave_html_aa39859b74adb5cca0470d488186eb6af"><div class="ttname"><a href="classQsciLexerOctave.html#aa39859b74adb5cca0470d488186eb6af">QsciLexerOctave::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a94b8329c4259ea90a6a28b1e745837de"><div class="ttname"><a href="classQsciScintilla.html#a94b8329c4259ea90a6a28b1e745837de">QsciScintilla::write</a></div><div class="ttdeci">bool write(QIODevice *io) const</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_af861d2c565994b427514715fe95a80b7"><div class="ttname"><a href="classQsciLexerHTML.html#af861d2c565994b427514715fe95a80b7">QsciLexerHTML::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div><div class="ttdef"><b>Definition:</b> qscilexerhtml.h:440</div></div>
<div class="ttc" id="aclassQsciLexer_html_a2447139ff781bf55c74177881ac023ac"><div class="ttname"><a href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">QsciLexer::defaultStyle</a></div><div class="ttdeci">virtual int defaultStyle() const</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_a27f6ffff6c6020126b5318ed8ba76c54"><div class="ttname"><a href="classQsciLexerBash.html#a27f6ffff6c6020126b5318ed8ba76c54">QsciLexerBash::foldComments</a></div><div class="ttdeci">bool foldComments() const</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_a4f286fb01fbf71a895a6a6ca2424b9c5"><div class="ttname"><a href="classQsciLexerPOV.html#a4f286fb01fbf71a895a6a6ca2424b9c5">QsciLexerPOV::QsciLexerPOV</a></div><div class="ttdeci">QsciLexerPOV(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a36deb25fada219957350847732d05889"><div class="ttname"><a href="classQsciScintilla.html#a36deb25fada219957350847732d05889">QsciScintilla::copy</a></div><div class="ttdeci">virtual void copy()</div></div>
<div class="ttc" id="aclassQsciLexerPO_html_a40b4351699e48f7b34faa84c5dbc4704"><div class="ttname"><a href="classQsciLexerPO.html#a40b4351699e48f7b34faa84c5dbc4704">QsciLexerPO::foldComments</a></div><div class="ttdeci">bool foldComments() const</div></div>
<div class="ttc" id="aclassQsciLexer_html_abd34f0d3055b8c7b52f0156f92244e8c"><div class="ttname"><a href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">QsciLexer::font</a></div><div class="ttdeci">virtual QFont font(int style) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a2febc4ea74d45d6a8bc9c758635dd99d"><div class="ttname"><a href="classQsciScintillaBase.html#a2febc4ea74d45d6a8bc9c758635dd99d">QsciScintillaBase::SCN_FOCUSOUT</a></div><div class="ttdeci">void SCN_FOCUSOUT()</div><div class="ttdoc">This signal is emitted when focus is lost.</div></div>
<div class="ttc" id="aclassQsciAPIs_html_a8fc5db618546fcfcc5bdc46e6d062995"><div class="ttname"><a href="classQsciAPIs.html#a8fc5db618546fcfcc5bdc46e6d062995">QsciAPIs::apiPreparationStarted</a></div><div class="ttdeci">void apiPreparationStarted()</div></div>
<div class="ttc" id="aclassQsciLexerFortran77_html_a43f710d31ccfd80ce8dd4f0ec8fc8d46"><div class="ttname"><a href="classQsciLexerFortran77.html#a43f710d31ccfd80ce8dd4f0ec8fc8d46">QsciLexerFortran77::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_a05f377a9017cf5f5d51deae3f1f83445"><div class="ttname"><a href="classQsciLexerPostScript.html#a05f377a9017cf5f5d51deae3f1f83445">QsciLexerPostScript::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciCommandSet_html_af244d8499c10c569b9924c25af17655a"><div class="ttname"><a href="classQsciCommandSet.html#af244d8499c10c569b9924c25af17655a">QsciCommandSet::clearAlternateKeys</a></div><div class="ttdeci">void clearAlternateKeys()</div><div class="ttdoc">The alternate keys bindings for all commands are removed.</div></div>
<div class="ttc" id="aclassQsciLexerCSharp_html_a3fd919ace71f975bd28b94b34ccd3a19"><div class="ttname"><a href="classQsciLexerCSharp.html#a3fd919ace71f975bd28b94b34ccd3a19">QsciLexerCSharp::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aff3b4e47fcbadeb0cf2556cf6ad164e0"><div class="ttname"><a href="classQsciScintilla.html#aff3b4e47fcbadeb0cf2556cf6ad164e0">QsciScintilla::autoCompletionShowSingle</a></div><div class="ttdeci">bool autoCompletionShowSingle() const</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_a7670a50d4dce21461de96844235b4242"><div class="ttname"><a href="classQsciLexerPostScript.html#a7670a50d4dce21461de96844235b4242">QsciLexerPostScript::QsciLexerPostScript</a></div><div class="ttdeci">QsciLexerPostScript(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_acf9bbfcaf3dfd6004428920e1c6572fd"><div class="ttname"><a href="classQsciLexerRuby.html#acf9bbfcaf3dfd6004428920e1c6572fd">QsciLexerRuby::setFoldComments</a></div><div class="ttdeci">void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerXML_html_a9fc5fef8f86ef0f1162a18ca4cc88aa1"><div class="ttname"><a href="classQsciLexerXML.html#a9fc5fef8f86ef0f1162a18ca4cc88aa1">QsciLexerXML::QsciLexerXML</a></div><div class="ttdeci">QsciLexerXML(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a9b6f6a462314471262e5f29057839b34"><div class="ttname"><a href="classQsciLexerPascal.html#a9b6f6a462314471262e5f29057839b34">QsciLexerPascal::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ab000e5f46abc6568405585b711067d1e"><div class="ttname"><a href="classQsciScintilla.html#ab000e5f46abc6568405585b711067d1e">QsciScintilla::tabDrawMode</a></div><div class="ttdeci">TabDrawMode tabDrawMode() const</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_a06495bd35607512278859d9251e68ce1"><div class="ttname"><a href="classQsciLexerTeX.html#a06495bd35607512278859d9251e68ce1">QsciLexerTeX::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div><div class="ttdoc">Returns the foreground colour of the text for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerJavaScript_html_abc88c53a2cfe6dd61e059fad1e8f3539"><div class="ttname"><a href="classQsciLexerJavaScript.html#abc88c53a2cfe6dd61e059fad1e8f3539">QsciLexerJavaScript::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexer_html_a6504a6fff35af16fbfd97889048db2a5"><div class="ttname"><a href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">QsciLexer::autoCompletionFillups</a></div><div class="ttdeci">virtual const char * autoCompletionFillups() const</div><div class="ttdoc">Returns the characters that can fill up auto-completion.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a065b392e0a39ecfd39df787a3a25e814"><div class="ttname"><a href="classQsciScintilla.html#a065b392e0a39ecfd39df787a3a25e814">QsciScintilla::setIndentationsUseTabs</a></div><div class="ttdeci">virtual void setIndentationsUseTabs(bool tabs)</div></div>
<div class="ttc" id="aclassQsciDocument_html"><div class="ttname"><a href="classQsciDocument.html">QsciDocument</a></div><div class="ttdoc">The QsciDocument class represents a document to be edited.</div><div class="ttdef"><b>Definition:</b> qscidocument.h:38</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html_a23d3bdd816b3da42e65cb4b08f2b01ff"><div class="ttname"><a href="classQsciLexerAVS.html#a23d3bdd816b3da42e65cb4b08f2b01ff">QsciLexerAVS::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciStyle_html_a4d1aa13e042609e48674f72aebd2ebae"><div class="ttname"><a href="classQsciStyle.html#a4d1aa13e042609e48674f72aebd2ebae">QsciStyle::changeable</a></div><div class="ttdeci">bool changeable() const</div><div class="ttdef"><b>Definition:</b> qscistyle.h:173</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca500892fc4eef318262b009f6eddc9eda"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca500892fc4eef318262b009f6eddc9eda">QsciScintillaBase::SC_MARK_BOOKMARK</a></div><div class="ttdeci">@ SC_MARK_BOOKMARK</div><div class="ttdoc">A bookmark.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2664</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca60f9340f78d0c386cb2df238849f121d"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca60f9340f78d0c386cb2df238849f121d">QsciScintillaBase::SC_MARK_SHORTARROW</a></div><div class="ttdeci">@ SC_MARK_SHORTARROW</div><div class="ttdoc">An arrow pointing to the right.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2576</div></div>
<div class="ttc" id="aclassQsciLexerIDL_html"><div class="ttname"><a href="classQsciLexerIDL.html">QsciLexerIDL</a></div><div class="ttdoc">The QsciLexerIDL class encapsulates the Scintilla IDL lexer.</div><div class="ttdef"><b>Definition:</b> qscilexeridl.h:33</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_a7ecc2269f4b7a4956b7209082032245d"><div class="ttname"><a href="classQsciLexerRuby.html#a7ecc2269f4b7a4956b7209082032245d">QsciLexerRuby::blockStart</a></div><div class="ttdeci">const char * blockStart(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciLexerJavaScript_html_af00e1d05374302fd4d2e2eeec1a829ee"><div class="ttname"><a href="classQsciLexerJavaScript.html#af00e1d05374302fd4d2e2eeec1a829ee">QsciLexerJavaScript::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_ae96690293b8128bea9cedf9b55b92ad6"><div class="ttname"><a href="classQsciLexerPython.html#ae96690293b8128bea9cedf9b55b92ad6">QsciLexerPython::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a29e0cbc36bafac84bc1c755b6ee26abd"><div class="ttname"><a href="classQsciScintilla.html#a29e0cbc36bafac84bc1c755b6ee26abd">QsciScintilla::cancelList</a></div><div class="ttdeci">void cancelList()</div><div class="ttdoc">Cancel any current auto-completion or user defined list.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ab7ed107d6ace096e9026c31145c48b41a7776c14d3a1424576a26a8da304b96bf"><div class="ttname"><a href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41a7776c14d3a1424576a26a8da304b96bf">QsciScintillaBase::SC_MARGIN_SYMBOL</a></div><div class="ttdeci">@ SC_MARGIN_SYMBOL</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2690</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aff5e2abd10fd64752adc1a89fc626e1e"><div class="ttname"><a href="classQsciScintilla.html#aff5e2abd10fd64752adc1a89fc626e1e">QsciScintilla::lexer</a></div><div class="ttdeci">QsciLexer * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a4f0a73894fc542ffc420113046c82f41"><div class="ttname"><a href="classQsciLexerSQL.html#a4f0a73894fc542ffc420113046c82f41">QsciLexerSQL::dottedWords</a></div><div class="ttdeci">bool dottedWords() const</div><div class="ttdef"><b>Definition:</b> qscilexersql.h:174</div></div>
<div class="ttc" id="aclassQsciLexer_html_ab222fbddb7eb72261153d1bebb5a01ee"><div class="ttname"><a href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">QsciLexer::styleBitsNeeded</a></div><div class="ttdeci">virtual int styleBitsNeeded() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae8b040ae88e74aef9a38cdb4ce24295a"><div class="ttname"><a href="classQsciScintilla.html#ae8b040ae88e74aef9a38cdb4ce24295a">QsciScintilla::resetSelectionBackgroundColor</a></div><div class="ttdeci">virtual void resetSelectionBackgroundColor()</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_a47eb0ab494fe54b5518b4c8bdcd2968e"><div class="ttname"><a href="classQsciLexerRuby.html#a47eb0ab494fe54b5518b4c8bdcd2968e">QsciLexerRuby::blockStartKeyword</a></div><div class="ttdeci">const char * blockStartKeyword(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae478a896ae32a30e8a375049a3d477e0"><div class="ttname"><a href="classQsciScintilla.html#ae478a896ae32a30e8a375049a3d477e0">QsciScintilla::FoldStyle</a></div><div class="ttdeci">FoldStyle</div><div class="ttdoc">This enum defines the different styles for the folding margin.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:209</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_ae8630fee6378af65bbd772b8f20fe4c9"><div class="ttname"><a href="classQsciLexerCSS.html#ae8630fee6378af65bbd772b8f20fe4c9">QsciLexerCSS::SCSSLanguage</a></div><div class="ttdeci">bool SCSSLanguage() const</div><div class="ttdef"><b>Definition:</b> qscilexercss.h:207</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a7ea1d7ae4594027f8b565380f3fffbb4"><div class="ttname"><a href="classQsciLexerPython.html#a7ea1d7ae4594027f8b565380f3fffbb4">QsciLexerPython::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca6f07eeddc235e313c4ca597220c71a0c"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca6f07eeddc235e313c4ca597220c71a0c">QsciScintillaBase::SC_MARK_ARROWS</a></div><div class="ttdeci">@ SC_MARK_ARROWS</div><div class="ttdoc">Three drawn arrows pointing right.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2641</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_aae9e42584c6466a8b859d56218eaf28c"><div class="ttname"><a href="classQsciLexerPerl.html#aae9e42584c6466a8b859d56218eaf28c">QsciLexerPerl::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_a78834f5080f50c01ba5ec1094114bf40"><div class="ttname"><a href="classQsciLexerPostScript.html#a78834f5080f50c01ba5ec1094114bf40">QsciLexerPostScript::level</a></div><div class="ttdeci">int level() const</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a822ca7489c4655f26bc72ed127285d8a"><div class="ttname"><a href="classQsciLexerCPP.html#a822ca7489c4655f26bc72ed127285d8a">QsciLexerCPP::wordCharacters</a></div><div class="ttdeci">const char * wordCharacters() const</div><div class="ttdoc">Returns the string of characters that comprise a word.</div></div>
<div class="ttc" id="aclassQsciLexer_html_a49fc2fb49ed07f1cb5f8b0a96e07d0d4"><div class="ttname"><a href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer::QsciLexer</a></div><div class="ttdeci">QsciLexer(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a9daa92535de2f43e4c04305007ce5475"><div class="ttname"><a href="classQsciScintilla.html#a9daa92535de2f43e4c04305007ce5475">QsciScintilla::wheelEvent</a></div><div class="ttdeci">virtual void wheelEvent(QWheelEvent *e)</div><div class="ttdoc">\reimp</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_a127674491f7805ca43b18f1bb93b47b4"><div class="ttname"><a href="classQsciLexerAsm.html#a127674491f7805ca43b18f1bb93b47b4">QsciLexerAsm::setCommentDelimiter</a></div><div class="ttdeci">virtual void setCommentDelimiter(QChar delimeter)</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_ae669580d3e3332a9b310d1bf78251f07"><div class="ttname"><a href="classQsciLexerHTML.html#ae669580d3e3332a9b310d1bf78251f07">QsciLexerHTML::makoTemplates</a></div><div class="ttdeci">bool makoTemplates() const</div><div class="ttdef"><b>Definition:</b> qscilexerhtml.h:478</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_add088b1bd36b0d5eb0f3b87e403cec10"><div class="ttname"><a href="classQsciLexerPython.html#add088b1bd36b0d5eb0f3b87e403cec10">QsciLexerPython::v2UnicodeAllowed</a></div><div class="ttdeci">bool v2UnicodeAllowed() const</div><div class="ttdef"><b>Definition:</b> qscilexerpython.h:246</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a0b527097e38858853ea2ac5861278233"><div class="ttname"><a href="classQsciScintilla.html#a0b527097e38858853ea2ac5861278233">QsciScintilla::whitespaceSize</a></div><div class="ttdeci">int whitespaceSize() const</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_a080ef6e2fd0569a6f3d538ed0f82da85"><div class="ttname"><a href="classQsciLexerBash.html#a080ef6e2fd0569a6f3d538ed0f82da85">QsciLexerBash::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerCustom_html"><div class="ttname"><a href="classQsciLexerCustom.html">QsciLexerCustom</a></div><div class="ttdoc">The QsciLexerCustom class is an abstract class used as a base for new language lexers.</div><div class="ttdef"><b>Definition:</b> qscilexercustom.h:43</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html_af462fb11c1cb7d3a5d99cc66d2a4bc6b"><div class="ttname"><a href="classQsciLexerAVS.html#af462fb11c1cb7d3a5d99cc66d2a4bc6b">QsciLexerAVS::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciStyle_html_a25e9b8a34c334bf6160115a2c43a5256"><div class="ttname"><a href="classQsciStyle.html#a25e9b8a34c334bf6160115a2c43a5256">QsciStyle::setTextCase</a></div><div class="ttdeci">void setTextCase(TextCase text_case)</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_a59ad64688b9fb852792b3fa15c2b125d"><div class="ttname"><a href="classQsciLexerVerilog.html#a59ad64688b9fb852792b3fa15c2b125d">QsciLexerVerilog::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ac4d1c67938c75806b2c139d0779d0478a690a08e6da3622e6f101fe67ca914816"><div class="ttname"><a href="classQsciScintilla.html#ac4d1c67938c75806b2c139d0779d0478a690a08e6da3622e6f101fe67ca914816">QsciScintilla::WrapFlagByBorder</a></div><div class="ttdeci">@ WrapFlagByBorder</div><div class="ttdoc">A wrap flag is displayed by the border.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:501</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca58fc2ba342cf7cc9e5f5e9a59d4319bc"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca58fc2ba342cf7cc9e5f5e9a59d4319bc">QsciScintillaBase::SC_MARK_CIRCLEMINUS</a></div><div class="ttdeci">@ SC_MARK_CIRCLEMINUS</div><div class="ttdoc">A drawn minus sign in a circle.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2628</div></div>
<div class="ttc" id="aclassQsciLexerJavaScript_html_a8a99f91287ee998375f84c7a2467b0ff"><div class="ttname"><a href="classQsciLexerJavaScript.html#a8a99f91287ee998375f84c7a2467b0ff">QsciLexerJavaScript::~QsciLexerJavaScript</a></div><div class="ttdeci">virtual ~QsciLexerJavaScript()</div><div class="ttdoc">Destroys the QsciLexerJavaScript instance.</div></div>
<div class="ttc" id="aclassQsciLexerBatch_html_a93f46567c5b91d993387d2ba033f2030"><div class="ttname"><a href="classQsciLexerBatch.html#a93f46567c5b91d993387d2ba033f2030">QsciLexerBatch::wordCharacters</a></div><div class="ttdeci">const char * wordCharacters() const</div><div class="ttdoc">Returns the string of characters that comprise a word.</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_a7af6a9822a535e06a6874db0b73c2efd"><div class="ttname"><a href="classQsciLexerBash.html#a7af6a9822a535e06a6874db0b73c2efd">QsciLexerBash::~QsciLexerBash</a></div><div class="ttdeci">virtual ~QsciLexerBash()</div><div class="ttdoc">Destroys the QsciLexerBash instance.</div></div>
<div class="ttc" id="aclassQsciAPIs_html_aa2ee3021ffc6a998776547a5c252edca"><div class="ttname"><a href="classQsciAPIs.html#aa2ee3021ffc6a998776547a5c252edca">QsciAPIs::installedAPIFiles</a></div><div class="ttdeci">QStringList installedAPIFiles() const</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_add132f5762831171fdee856172a0a5dc"><div class="ttname"><a href="classQsciLexerSQL.html#add132f5762831171fdee856172a0a5dc">QsciLexerSQL::foldComments</a></div><div class="ttdeci">bool foldComments() const</div><div class="ttdef"><b>Definition:</b> qscilexersql.h:190</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214cadf0e9f9a2980c5e693c67819a64f132e"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cadf0e9f9a2980c5e693c67819a64f132e">QsciScintillaBase::SC_MARK_UNDERLINE</a></div><div class="ttdeci">@ SC_MARK_UNDERLINE</div><div class="ttdoc">The line is underlined using the marker's background color.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2658</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_ab743740491685360f2d50e5c12be876b"><div class="ttname"><a href="classQsciLexerBash.html#ab743740491685360f2d50e5c12be876b">QsciLexerBash::setFoldComments</a></div><div class="ttdeci">virtual void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_a79e3ff22e68d54f640bd2f7747a7a193"><div class="ttname"><a href="classQsciLexerVerilog.html#a79e3ff22e68d54f640bd2f7747a7a193">QsciLexerVerilog::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_acf47d4b76a8c85a48fe9a27423997071"><div class="ttname"><a href="classQsciScintilla.html#acf47d4b76a8c85a48fe9a27423997071">QsciScintilla::setMarkerBackgroundColor</a></div><div class="ttdeci">void setMarkerBackgroundColor(const QColor &amp;col, int markerNumber=-1)</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a42133f1b4127c78674f89e3209236a18"><div class="ttname"><a href="classQsciLexerPerl.html#a42133f1b4127c78674f89e3209236a18">QsciLexerPerl::blockEnd</a></div><div class="ttdeci">const char * blockEnd(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciAbstractAPIs_html_a9db5ebe8adda3f58892af676f5295e3a"><div class="ttname"><a href="classQsciAbstractAPIs.html#a9db5ebe8adda3f58892af676f5295e3a">QsciAbstractAPIs::QsciAbstractAPIs</a></div><div class="ttdeci">QsciAbstractAPIs(QsciLexer *lexer)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a3de45d9f5485af099041d8046e734103"><div class="ttname"><a href="classQsciScintilla.html#a3de45d9f5485af099041d8046e734103">QsciScintilla::zoomOut</a></div><div class="ttdeci">virtual void zoomOut()</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_a277a3c519eca4ef69d73fd45ea4f5ab5"><div class="ttname"><a href="classQsciLexerPostScript.html#a277a3c519eca4ef69d73fd45ea4f5ab5">QsciLexerPostScript::setFoldCompact</a></div><div class="ttdeci">virtual void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_ade1a77293facd468100a7c023dedcacc"><div class="ttname"><a href="classQsciLexerPostScript.html#ade1a77293facd468100a7c023dedcacc">QsciLexerPostScript::tokenize</a></div><div class="ttdeci">bool tokenize() const</div></div>
<div class="ttc" id="aclassQsciCommand_html_aebfa962809b16312fa03f7526cc60f07"><div class="ttname"><a href="classQsciCommand.html#aebfa962809b16312fa03f7526cc60f07">QsciCommand::command</a></div><div class="ttdeci">Command command() const</div><div class="ttdoc">Return the command that will be executed by this instance.</div><div class="ttdef"><b>Definition:</b> qscicommand.h:348</div></div>
<div class="ttc" id="aclassQsciLexerTCL_html_a15ec40b8e6b208521e08d44400eb56f8"><div class="ttname"><a href="classQsciLexerTCL.html#a15ec40b8e6b208521e08d44400eb56f8">QsciLexerTCL::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_acd80380b4727bd94148f5a0ff479742e"><div class="ttname"><a href="classQsciLexerTeX.html#acd80380b4727bd94148f5a0ff479742e">QsciLexerTeX::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a5b2c0f0e93a1e35b0fb42f2dc1abea29"><div class="ttname"><a href="classQsciLexerSQL.html#a5b2c0f0e93a1e35b0fb42f2dc1abea29">QsciLexerSQL::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_aefae6df689f1d3dad66d1f2fc141cc39"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#aefae6df689f1d3dad66d1f2fc141cc39">QsciLexerCoffeeScript::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_a1ba5b1e505b4f6fe7d7b12ce69dee9a8"><div class="ttname"><a href="classQsciLexerBash.html#a1ba5b1e505b4f6fe7d7b12ce69dee9a8">QsciLexerBash::QsciLexerBash</a></div><div class="ttdeci">QsciLexerBash(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aa2bca1d2d137ea4a3f944a4f41f98a94"><div class="ttname"><a href="classQsciScintilla.html#aa2bca1d2d137ea4a3f944a4f41f98a94">QsciScintilla::setWhitespaceVisibility</a></div><div class="ttdeci">virtual void setWhitespaceVisibility(WhitespaceVisibility mode)</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_a126d81ec982782507eafae1af5d0d856"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#a126d81ec982782507eafae1af5d0d856">QsciLexerCoffeeScript::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a3c93f14b36897ecb3f902b5e5de91ad6"><div class="ttname"><a href="classQsciLexerCPP.html#a3c93f14b36897ecb3f902b5e5de91ad6">QsciLexerCPP::foldPreprocessor</a></div><div class="ttdeci">bool foldPreprocessor() const</div><div class="ttdef"><b>Definition:</b> qscilexercpp.h:247</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a67c004c60c462dc9d3840a6e2405d3f8"><div class="ttname"><a href="classQsciScintilla.html#a67c004c60c462dc9d3840a6e2405d3f8">QsciScintilla::isCallTipActive</a></div><div class="ttdeci">bool isCallTipActive() const</div><div class="ttdoc">Returns true if a call tip is currently active.</div></div>
<div class="ttc" id="aclassQsciLexer_html_adf8de1727583e902c7cae673673a78a1"><div class="ttname"><a href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">QsciLexer::paperChanged</a></div><div class="ttdeci">void paperChanged(const QColor &amp;c, int style)</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_aa303817de5a59137ab4bf592ff52a315"><div class="ttname"><a href="classQsciLexerPostScript.html#aa303817de5a59137ab4bf592ff52a315">QsciLexerPostScript::setFoldAtElse</a></div><div class="ttdeci">virtual void setFoldAtElse(bool fold)</div></div>
<div class="ttc" id="aclassQsciPrinter_html_ad66724c8a5e5e202998bd6533fef61be"><div class="ttname"><a href="classQsciPrinter.html#ad66724c8a5e5e202998bd6533fef61be">QsciPrinter::setMagnification</a></div><div class="ttdeci">virtual void setMagnification(int magnification)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a1b4591eb73dcef0153861f698edc8726"><div class="ttname"><a href="classQsciScintilla.html#a1b4591eb73dcef0153861f698edc8726">QsciScintilla::setIndentationGuides</a></div><div class="ttdeci">virtual void setIndentationGuides(bool enable)</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a0c3e05e1bbdc4614fc7e76e508178592"><div class="ttname"><a href="classQsciLexerPerl.html#a0c3e05e1bbdc4614fc7e76e508178592">QsciLexerPerl::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a870955b5547ce4bdf9940165181022b7"><div class="ttname"><a href="classQsciLexerCPP.html#a870955b5547ce4bdf9940165181022b7">QsciLexerCPP::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a6882a7641822a859e812601f1bae65eb"><div class="ttname"><a href="classQsciScintilla.html#a6882a7641822a859e812601f1bae65eb">QsciScintilla::setSelectionBackgroundColor</a></div><div class="ttdeci">virtual void setSelectionBackgroundColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a48860b20014ce8b044760c055156ba57"><div class="ttname"><a href="classQsciScintilla.html#a48860b20014ce8b044760c055156ba57">QsciScintilla::setAutoCompletionSource</a></div><div class="ttdeci">virtual void setAutoCompletionSource(AutoCompletionSource source)</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a97c7813c68c861b9f2b3f068d9b47fd7"><div class="ttname"><a href="classQsciLexerD.html#a97c7813c68c861b9f2b3f068d9b47fd7">QsciLexerD::setFoldCompact</a></div><div class="ttdeci">virtual void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aaf77d8976ae47a1d5e5ee804bc0645d3"><div class="ttname"><a href="classQsciScintilla.html#aaf77d8976ae47a1d5e5ee804bc0645d3">QsciScintilla::selectionToEol</a></div><div class="ttdeci">bool selectionToEol() const</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_a6dbcaf590be7759f18699593c95c69e6"><div class="ttname"><a href="classQsciLexerVHDL.html#a6dbcaf590be7759f18699593c95c69e6">QsciLexerVHDL::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerCMake_html_a7cc73bba065690f08e2b6b8e8c00d5d3"><div class="ttname"><a href="classQsciLexerCMake.html#a7cc73bba065690f08e2b6b8e8c00d5d3">QsciLexerCMake::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_a5b95ed33711b09385c92fbfb9f1d2a5d"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#a5b95ed33711b09385c92fbfb9f1d2a5d">QsciLexerCoffeeScript::dollarsAllowed</a></div><div class="ttdeci">bool dollarsAllowed() const</div><div class="ttdef"><b>Definition:</b> qscilexercoffeescript.h:192</div></div>
<div class="ttc" id="aclassQsciLexerHex_html"><div class="ttname"><a href="classQsciLexerHex.html">QsciLexerHex</a></div><div class="ttdoc">The abstract QsciLexerHex class encapsulates the Scintilla Hex lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerhex.h:33</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8166be72487a7a660cb8e44c5126c852"><div class="ttname"><a href="classQsciScintilla.html#a8166be72487a7a660cb8e44c5126c852">QsciScintilla::marginRightClicked</a></div><div class="ttdeci">void marginRightClicked(int margin, int line, Qt::KeyboardModifiers state)</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a8921849dce20c65c0fc024bc27255873"><div class="ttname"><a href="classQsciLexerPython.html#a8921849dce20c65c0fc024bc27255873">QsciLexerPython::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciCommand_html_aa0bf23ebd61dd46a4eb59447e43c4cab"><div class="ttname"><a href="classQsciCommand.html#aa0bf23ebd61dd46a4eb59447e43c4cab">QsciCommand::execute</a></div><div class="ttdeci">void execute()</div><div class="ttdoc">Execute the command.</div></div>
<div class="ttc" id="aclassQsciLexerYAML_html_a01ff9a027edd3a2aa6d443e520b10b73"><div class="ttname"><a href="classQsciLexerYAML.html#a01ff9a027edd3a2aa6d443e520b10b73">QsciLexerYAML::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_a293f0a5c39990ec1db6de249dc618901"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#a293f0a5c39990ec1db6de249dc618901">QsciLexerCoffeeScript::blockStart</a></div><div class="ttdeci">const char * blockStart(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_abf07311e229b5ec1370dd8a57873c1b6"><div class="ttname"><a href="classQsciLexerRuby.html#abf07311e229b5ec1370dd8a57873c1b6">QsciLexerRuby::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciAPIs_html_adff0073d1f4ee2e0ea8b3bf234ff2dd3"><div class="ttname"><a href="classQsciAPIs.html#adff0073d1f4ee2e0ea8b3bf234ff2dd3">QsciAPIs::autoCompletionSelected</a></div><div class="ttdeci">virtual void autoCompletionSelected(const QString &amp;sel)</div><div class="ttdoc">\reimp</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_a41d04b17da9c84a94289e91323fb5206"><div class="ttname"><a href="classQsciLexerCSS.html#a41d04b17da9c84a94289e91323fb5206">QsciLexerCSS::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexer_html"><div class="ttname"><a href="classQsciLexer.html">QsciLexer</a></div><div class="ttdoc">The QsciLexer class is an abstract class used as a base for language lexers.</div><div class="ttdef"><b>Definition:</b> qscilexer.h:63</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_a80a1f387059600fd67bbf6d2699981e3"><div class="ttname"><a href="classQsciLexerBash.html#a80a1f387059600fd67bbf6d2699981e3">QsciLexerBash::setFoldCompact</a></div><div class="ttdeci">virtual void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ad85546712df2387738e63be38dc41d61"><div class="ttname"><a href="classQsciScintilla.html#ad85546712df2387738e63be38dc41d61">QsciScintilla::setMarginText</a></div><div class="ttdeci">void setMarginText(int line, const QsciStyledText &amp;text)</div><div class="ttdoc">Set the margin text of line line with the styled text text.</div></div>
<div class="ttc" id="aclassQsciLexerMakefile_html_a561482313e4c6597b8c4627ec38e4d54"><div class="ttname"><a href="classQsciLexerMakefile.html#a561482313e4c6597b8c4627ec38e4d54">QsciLexerMakefile::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ae53947625062cec64a212dc68877ddc3"><div class="ttname"><a href="classQsciScintillaBase.html#ae53947625062cec64a212dc68877ddc3">QsciScintillaBase::SCN_FOCUSIN</a></div><div class="ttdeci">void SCN_FOCUSIN()</div><div class="ttdoc">This signal is emitted when focus is received.</div></div>
<div class="ttc" id="aclassQsciLexerMarkdown_html_aeffb57391fe593ab01e6f257f95ad2f6"><div class="ttname"><a href="classQsciLexerMarkdown.html#aeffb57391fe593ab01e6f257f95ad2f6">QsciLexerMarkdown::QsciLexerMarkdown</a></div><div class="ttdeci">QsciLexerMarkdown(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a4bf424d21079ab835dae90ce042400a0"><div class="ttname"><a href="classQsciScintilla.html#a4bf424d21079ab835dae90ce042400a0">QsciScintilla::wrapMode</a></div><div class="ttdeci">WrapMode wrapMode() const</div></div>
<div class="ttc" id="aclassQsciLexerTCL_html_a65a8d0928d9f04584972410a5af82888"><div class="ttname"><a href="classQsciLexerTCL.html#a65a8d0928d9f04584972410a5af82888">QsciLexerTCL::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html"><div class="ttname"><a href="classQsciLexerPostScript.html">QsciLexerPostScript</a></div><div class="ttdoc">The QsciLexerPostScript class encapsulates the Scintilla PostScript lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerpostscript.h:33</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a6abf177ca5bf8eea0930106d2867edae"><div class="ttname"><a href="classQsciScintilla.html#a6abf177ca5bf8eea0930106d2867edae">QsciScintilla::setMarkerForegroundColor</a></div><div class="ttdeci">void setMarkerForegroundColor(const QColor &amp;col, int markerNumber=-1)</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_aad047f411c36c262305ffcce5015944f"><div class="ttname"><a href="classQsciLexerBash.html#aad047f411c36c262305ffcce5015944f">QsciLexerBash::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_ac4001660bfa52216fe475f84e2ce9d77"><div class="ttname"><a href="classQsciLexerJSON.html#ac4001660bfa52216fe475f84e2ce9d77">QsciLexerJSON::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad3ca5787399ed886cb9000c8feab3c08"><div class="ttname"><a href="classQsciScintillaBase.html#ad3ca5787399ed886cb9000c8feab3c08">QsciScintillaBase::SCN_DOUBLECLICK</a></div><div class="ttdeci">void SCN_DOUBLECLICK(int position, int line, int modifiers)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aaed07ffc03430a61dc90ff6b28fb6fd7"><div class="ttname"><a href="classQsciScintilla.html#aaed07ffc03430a61dc90ff6b28fb6fd7">QsciScintilla::indicatorReleased</a></div><div class="ttdeci">void indicatorReleased(int line, int index, Qt::KeyboardModifiers state)</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a9080d0a47d2cbd972d5f2e6c737ba7fa"><div class="ttname"><a href="classQsciLexerD.html#a9080d0a47d2cbd972d5f2e6c737ba7fa">QsciLexerD::autoCompletionWordSeparators</a></div><div class="ttdeci">QStringList autoCompletionWordSeparators() const</div></div>
<div class="ttc" id="aclassQsciLexerMASM_html_ac9f1def4274bef684308ca40cd1c997b"><div class="ttname"><a href="classQsciLexerMASM.html#ac9f1def4274bef684308ca40cd1c997b">QsciLexerMASM::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ab4b6b4286a74e173a86de0a7f55241d5"><div class="ttname"><a href="classQsciScintilla.html#ab4b6b4286a74e173a86de0a7f55241d5">QsciScintilla::EolMode</a></div><div class="ttdeci">EolMode</div><div class="ttdoc">This enum defines the different end-of-line modes.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:197</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_aad7b42963ca382afb23eb000b727de12"><div class="ttname"><a href="classQsciLexerCPP.html#aad7b42963ca382afb23eb000b727de12">QsciLexerCPP::foldAtElse</a></div><div class="ttdeci">bool foldAtElse() const</div><div class="ttdef"><b>Definition:</b> qscilexercpp.h:232</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad7b8480681e4b4d5689d0e6d822dc3c0"><div class="ttname"><a href="classQsciScintillaBase.html#ad7b8480681e4b4d5689d0e6d822dc3c0">QsciScintillaBase::dragEnterEvent</a></div><div class="ttdeci">virtual void dragEnterEvent(QDragEnterEvent *e)</div><div class="ttdoc">Re-implemented to handle drag enters.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca8d928f238170d0765acb492d0e8f0f65"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca8d928f238170d0765acb492d0e8f0f65">QsciScintillaBase::SC_MARK_BOXMINUSCONNECTED</a></div><div class="ttdeci">@ SC_MARK_BOXMINUSCONNECTED</div><div class="ttdoc">A drawn minus sign in a connected box.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2611</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ab83469cc9550eadcd5d4e8ca3d20d07b"><div class="ttname"><a href="classQsciScintilla.html#ab83469cc9550eadcd5d4e8ca3d20d07b">QsciScintilla::WhitespaceVisibility</a></div><div class="ttdeci">WhitespaceVisibility</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:463</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aa226d94a0f7ed194012ca972d0f49ce8"><div class="ttname"><a href="classQsciScintilla.html#aa226d94a0f7ed194012ca972d0f49ce8">QsciScintilla::markerDeleteHandle</a></div><div class="ttdeci">void markerDeleteHandle(int mhandle)</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a6f87282ec40dbc5e752dc0bc0aec87a0"><div class="ttname"><a href="classQsciLexerPerl.html#a6f87282ec40dbc5e752dc0bc0aec87a0">QsciLexerPerl::~QsciLexerPerl</a></div><div class="ttdeci">virtual ~QsciLexerPerl()</div><div class="ttdoc">Destroys the QsciLexerPerl instance.</div></div>
<div class="ttc" id="aclassQsciLexerTekHex_html_aa84f369b0c91a6f9211efa78b8c03efb"><div class="ttname"><a href="classQsciLexerTekHex.html#aa84f369b0c91a6f9211efa78b8c03efb">QsciLexerTekHex::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciStyledText_html_a9f84f97932caaa6481cea79f48c70647"><div class="ttname"><a href="classQsciStyledText.html#a9f84f97932caaa6481cea79f48c70647">QsciStyledText::QsciStyledText</a></div><div class="ttdeci">QsciStyledText(const QString &amp;text, const QsciStyle &amp;style)</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_ad0d9356583118309e6c3991e96a67ffe"><div class="ttname"><a href="classQsciLexerCPP.html#ad0d9356583118309e6c3991e96a67ffe">QsciLexerCPP::setHighlightHashQuotedStrings</a></div><div class="ttdeci">void setHighlightHashQuotedStrings(bool enabled)</div></div>
<div class="ttc" id="aclassQsciCommand_html_abf9dffcf6c222ecc02b28c3f6d17eb8e"><div class="ttname"><a href="classQsciCommand.html#abf9dffcf6c222ecc02b28c3f6d17eb8e">QsciCommand::key</a></div><div class="ttdeci">int key() const</div><div class="ttdef"><b>Definition:</b> qscicommand.h:377</div></div>
<div class="ttc" id="aclassQsciLexerJava_html"><div class="ttname"><a href="classQsciLexerJava.html">QsciLexerJava</a></div><div class="ttdoc">The QsciLexerJava class encapsulates the Scintilla Java lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerjava.h:32</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a855939c35d62798c00b0361a0edc41da"><div class="ttname"><a href="classQsciLexerPython.html#a855939c35d62798c00b0361a0edc41da">QsciLexerPython::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciStyle_html_a04e5cc64877290739b30603c526d84ce"><div class="ttname"><a href="classQsciStyle.html#a04e5cc64877290739b30603c526d84ce">QsciStyle::description</a></div><div class="ttdeci">QString description() const</div><div class="ttdef"><b>Definition:</b> qscistyle.h:98</div></div>
<div class="ttc" id="aclassQsciLexer_html_a2dce337026551b6440e1dcdafa95b7d7"><div class="ttname"><a href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">QsciLexer::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper() const</div></div>
<div class="ttc" id="aclassQsciStyle_html_a1df46714ab45c62e5ad5e52a5f41bf15"><div class="ttname"><a href="classQsciStyle.html#a1df46714ab45c62e5ad5e52a5f41bf15">QsciStyle::hotspot</a></div><div class="ttdeci">bool hotspot() const</div><div class="ttdef"><b>Definition:</b> qscistyle.h:184</div></div>
<div class="ttc" id="aclassQsciAbstractAPIs_html_a90fa0f912b748b707967ccb722f04ddc"><div class="ttname"><a href="classQsciAbstractAPIs.html#a90fa0f912b748b707967ccb722f04ddc">QsciAbstractAPIs::autoCompletionSelected</a></div><div class="ttdeci">virtual void autoCompletionSelected(const QString &amp;selection)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a6a5b949013c382c1b5f341137cd37752"><div class="ttname"><a href="classQsciScintillaBase.html#a6a5b949013c382c1b5f341137cd37752">QsciScintillaBase::mouseReleaseEvent</a></div><div class="ttdeci">virtual void mouseReleaseEvent(QMouseEvent *e)</div><div class="ttdoc">Re-implemented to handle mouse releases.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a437d58cc225880e34560b65f8c0b3b50"><div class="ttname"><a href="classQsciScintilla.html#a437d58cc225880e34560b65f8c0b3b50">QsciScintilla::findFirstInSelection</a></div><div class="ttdeci">virtual bool findFirstInSelection(const QString &amp;expr, bool re, bool cs, bool wo, bool forward=true, bool show=true, bool posix=false, bool cxx11=false)</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html"><div class="ttname"><a href="classQsciLexerVerilog.html">QsciLexerVerilog</a></div><div class="ttdoc">The QsciLexerVerilog class encapsulates the Scintilla Verilog lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerverilog.h:33</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_a6b21e4498723f3a01fe468e03ebe04f4"><div class="ttname"><a href="classQsciLexerPOV.html#a6b21e4498723f3a01fe468e03ebe04f4">QsciLexerPOV::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca222004d413ee607197204f26950f3a0c"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca222004d413ee607197204f26950f3a0c">QsciScintillaBase::SC_MARK_CIRCLEPLUS</a></div><div class="ttdeci">@ SC_MARK_CIRCLEPLUS</div><div class="ttdoc">A drawn plus sign in a circle.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2622</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_abcd26a3075ae82103e76332fe20a4ed1"><div class="ttname"><a href="classQsciLexerAsm.html#abcd26a3075ae82103e76332fe20a4ed1">QsciLexerAsm::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html_a9d2bd8ea72760796590963c702082e5b"><div class="ttname"><a href="classQsciLexerProperties.html#a9d2bd8ea72760796590963c702082e5b">QsciLexerProperties::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a194e86e59129ed570af044e19697d0e9"><div class="ttname"><a href="classQsciScintilla.html#a194e86e59129ed570af044e19697d0e9">QsciScintilla::selectionChanged</a></div><div class="ttdeci">void selectionChanged()</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_a51401044d3ad272ede84e1f2a128cce6"><div class="ttname"><a href="classQsciLexerHTML.html#a51401044d3ad272ede84e1f2a128cce6">QsciLexerHTML::setFoldScriptComments</a></div><div class="ttdeci">void setFoldScriptComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerYAML_html_a7dcc25d7ced16c1bc409c14276e6843c"><div class="ttname"><a href="classQsciLexerYAML.html#a7dcc25d7ced16c1bc409c14276e6843c">QsciLexerYAML::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a24d7e487c0493f7164cb5bcce51d403d"><div class="ttname"><a href="classQsciLexerSQL.html#a24d7e487c0493f7164cb5bcce51d403d">QsciLexerSQL::hashComments</a></div><div class="ttdeci">bool hashComments() const</div><div class="ttdef"><b>Definition:</b> qscilexersql.h:218</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a4e895e320f983296f4a14a131dac4c66"><div class="ttname"><a href="classQsciScintilla.html#a4e895e320f983296f4a14a131dac4c66">QsciScintilla::indicatorDrawUnder</a></div><div class="ttdeci">bool indicatorDrawUnder(int indicatorNumber) const</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_a55b4fb34deedc4131e4f85fc4f7e01bc"><div class="ttname"><a href="classQsciLexerRuby.html#a55b4fb34deedc4131e4f85fc4f7e01bc">QsciLexerRuby::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a3c22a819683d430aa99d23a80fedee73"><div class="ttname"><a href="classQsciLexerD.html#a3c22a819683d430aa99d23a80fedee73">QsciLexerD::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a61c43c53a753272c51c5c5ac14bda136"><div class="ttname"><a href="classQsciScintillaBase.html#a61c43c53a753272c51c5c5ac14bda136">QsciScintillaBase::SCN_AUTOCSELECTION</a></div><div class="ttdeci">void SCN_AUTOCSELECTION(const char *selection, int position, int ch, int method)</div></div>
<div class="ttc" id="aclassQsciLexerBatch_html_a5fbcc10c345a43e51a6042919a9d30c4"><div class="ttname"><a href="classQsciLexerBatch.html#a5fbcc10c345a43e51a6042919a9d30c4">QsciLexerBatch::~QsciLexerBatch</a></div><div class="ttdeci">virtual ~QsciLexerBatch()</div><div class="ttdoc">Destroys the QsciLexerBatch instance.</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html"><div class="ttname"><a href="classQsciLexerSQL.html">QsciLexerSQL</a></div><div class="ttdoc">The QsciLexerSQL class encapsulates the Scintilla SQL lexer.</div><div class="ttdef"><b>Definition:</b> qscilexersql.h:32</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_a1036c768307d29c40f09cc1bc2fce37c"><div class="ttname"><a href="classQsciLexerHTML.html#a1036c768307d29c40f09cc1bc2fce37c">QsciLexerHTML::setFoldCompact</a></div><div class="ttdeci">virtual void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a67e012ca1e51ed73521c720237b7a507"><div class="ttname"><a href="classQsciScintilla.html#a67e012ca1e51ed73521c720237b7a507">QsciScintilla::indentationsUseTabs</a></div><div class="ttdeci">bool indentationsUseTabs() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aedf7c8532be11bcfb8d38411bea88792"><div class="ttname"><a href="classQsciScintilla.html#aedf7c8532be11bcfb8d38411bea88792">QsciScintilla::length</a></div><div class="ttdeci">int length() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8348c07fe8cff0bf6141a94ca3014ed2"><div class="ttname"><a href="classQsciScintilla.html#a8348c07fe8cff0bf6141a94ca3014ed2">QsciScintilla::setAutoCompletionCaseSensitivity</a></div><div class="ttdeci">virtual void setAutoCompletionCaseSensitivity(bool cs)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a47d5e09e3507840ae898dfdc15acd858"><div class="ttname"><a href="classQsciScintilla.html#a47d5e09e3507840ae898dfdc15acd858">QsciScintilla::createStandardContextMenu</a></div><div class="ttdeci">QMenu * createStandardContextMenu()</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a99fe632cb2613a0bac2ffb77c571c647"><div class="ttname"><a href="classQsciScintilla.html#a99fe632cb2613a0bac2ffb77c571c647">QsciScintilla::unindent</a></div><div class="ttdeci">virtual void unindent(int line)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a48f53865418cf2ebd5126d2ee5e9d1dd"><div class="ttname"><a href="classQsciScintilla.html#a48f53865418cf2ebd5126d2ee5e9d1dd">QsciScintilla::replaceSelectedText</a></div><div class="ttdeci">virtual void replaceSelectedText(const QString &amp;text)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a9fdd43a276cf3d9a3e7cc86dc7f280f5"><div class="ttname"><a href="classQsciScintilla.html#a9fdd43a276cf3d9a3e7cc86dc7f280f5">QsciScintilla::setWhitespaceBackgroundColor</a></div><div class="ttdeci">void setWhitespaceBackgroundColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a2ef800561a02b8ec02008d33e0c6e55e"><div class="ttname"><a href="classQsciScintilla.html#a2ef800561a02b8ec02008d33e0c6e55e">QsciScintilla::modificationAttempted</a></div><div class="ttdeci">void modificationAttempted()</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_a8d57801958b738cbb297936426bb8c61"><div class="ttname"><a href="classQsciLexerPostScript.html#a8d57801958b738cbb297936426bb8c61">QsciLexerPostScript::setTokenize</a></div><div class="ttdeci">virtual void setTokenize(bool tokenize)</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_a700754468352f673157d08d4ff222e79"><div class="ttname"><a href="classQsciLexerRuby.html#a700754468352f673157d08d4ff222e79">QsciLexerRuby::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ab7ed107d6ace096e9026c31145c48b41a68a70615f89282762ba21aa6ec629dac"><div class="ttname"><a href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41a68a70615f89282762ba21aa6ec629dac">QsciScintillaBase::SC_MARGIN_BACK</a></div><div class="ttdeci">@ SC_MARGIN_BACK</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2697</div></div>
<div class="ttc" id="aclassQsciLexerFortran77_html_a2033202288867ce63c4e93bc45dc55e3"><div class="ttname"><a href="classQsciLexerFortran77.html#a2033202288867ce63c4e93bc45dc55e3">QsciLexerFortran77::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_a613622c676e3c70f2c9f002f34326427"><div class="ttname"><a href="classQsciLexerHTML.html#a613622c676e3c70f2c9f002f34326427">QsciLexerHTML::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerCMake_html_a99fc9415c35eeef2b0f45f066101736b"><div class="ttname"><a href="classQsciLexerCMake.html#a99fc9415c35eeef2b0f45f066101736b">QsciLexerCMake::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca6f1caf375a3079d67c36998c1bd453a4"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca6f1caf375a3079d67c36998c1bd453a4">QsciScintillaBase::SC_MARK_ARROWDOWN</a></div><div class="ttdeci">@ SC_MARK_ARROWDOWN</div><div class="ttdoc">A triangle pointing down.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2583</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_a902f46f94e9d5e5177bec26e2a7bf8ef"><div class="ttname"><a href="classQsciLexerJSON.html#a902f46f94e9d5e5177bec26e2a7bf8ef">QsciLexerJSON::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a3f7076535f370759450ec1243088c7f1"><div class="ttname"><a href="classQsciLexerCPP.html#a3f7076535f370759450ec1243088c7f1">QsciLexerCPP::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciMacro_html_a6af9c876a10d746177790189067aaf6a"><div class="ttname"><a href="classQsciMacro.html#a6af9c876a10d746177790189067aaf6a">QsciMacro::save</a></div><div class="ttdeci">QString save() const</div></div>
<div class="ttc" id="aclassQsciLexerMASM_html_a00763c84274386e27be1994cc3404fd4"><div class="ttname"><a href="classQsciLexerMASM.html#a00763c84274386e27be1994cc3404fd4">QsciLexerMASM::QsciLexerMASM</a></div><div class="ttdeci">QsciLexerMASM(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerCMake_html_a90ed658a569976a68f1260901b7b3518"><div class="ttname"><a href="classQsciLexerCMake.html#a90ed658a569976a68f1260901b7b3518">QsciLexerCMake::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_a68e2eaca494e93937f896bd60b86429c"><div class="ttname"><a href="classQsciLexerTeX.html#a68e2eaca494e93937f896bd60b86429c">QsciLexerTeX::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_acf0c4eb451d93a21887462bc7746f2e4"><div class="ttname"><a href="classQsciScintilla.html#acf0c4eb451d93a21887462bc7746f2e4">QsciScintilla::markerDefine</a></div><div class="ttdeci">int markerDefine(const QPixmap &amp;pm, int markerNumber=-1)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a52aaeee4b9171222c20af3557adaf0ad"><div class="ttname"><a href="classQsciScintilla.html#a52aaeee4b9171222c20af3557adaf0ad">QsciScintilla::markerDeleteAll</a></div><div class="ttdeci">void markerDeleteAll(int markerNumber=-1)</div></div>
<div class="ttc" id="aclassQsciStyle_html_a2d4ec76574fd507fbf3c0d006c7427da"><div class="ttname"><a href="classQsciStyle.html#a2d4ec76574fd507fbf3c0d006c7427da">QsciStyle::setPaper</a></div><div class="ttdeci">void setPaper(const QColor &amp;paper)</div></div>
<div class="ttc" id="aclassQsciStyledText_html_a526eff4f40349af1913dd4cfa3464131"><div class="ttname"><a href="classQsciStyledText.html#a526eff4f40349af1913dd4cfa3464131">QsciStyledText::text</a></div><div class="ttdeci">const QString &amp; text() const</div><div class="ttdoc">Returns a reference to the text.</div><div class="ttdef"><b>Definition:</b> qscistyledtext.h:50</div></div>
<div class="ttc" id="aclassQsciAPIs_html_a6b29d84b0b5d63f2b590988195c7557c"><div class="ttname"><a href="classQsciAPIs.html#a6b29d84b0b5d63f2b590988195c7557c">QsciAPIs::clear</a></div><div class="ttdeci">void clear()</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ab7ed107d6ace096e9026c31145c48b41aa29598ff9ba1349daee66560cdd692bd"><div class="ttname"><a href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41aa29598ff9ba1349daee66560cdd692bd">QsciScintillaBase::SC_MARGIN_TEXT</a></div><div class="ttdeci">@ SC_MARGIN_TEXT</div><div class="ttdoc">The margin will display text.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2704</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_a628efb828569208d6219a88f1fc6a1a7"><div class="ttname"><a href="classQsciLexerLua.html#a628efb828569208d6219a88f1fc6a1a7">QsciLexerLua::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a4e27f94e78e5f184f37382950963d831"><div class="ttname"><a href="classQsciScintilla.html#a4e27f94e78e5f184f37382950963d831">QsciScintilla::isWordCharacter</a></div><div class="ttdeci">bool isWordCharacter(char ch) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214caa9ae33880a1ee19ce4db6544bb61a84d"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214caa9ae33880a1ee19ce4db6544bb61a84d">QsciScintillaBase::SC_MARK_TCORNER</a></div><div class="ttdeci">@ SC_MARK_TCORNER</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2599</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a02cb3518d6145815b22359d8d5aa2cf1"><div class="ttname"><a href="classQsciLexerD.html#a02cb3518d6145815b22359d8d5aa2cf1">QsciLexerD::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_adae268febf025354165c88afa2414c73"><div class="ttname"><a href="classQsciLexerPascal.html#adae268febf025354165c88afa2414c73">QsciLexerPascal::foldPreprocessor</a></div><div class="ttdeci">bool foldPreprocessor() const</div></div>
<div class="ttc" id="aclassQsciLexerFortran77_html_a5783815b0ab1200a2d5ff729b7eba074"><div class="ttname"><a href="classQsciLexerFortran77.html#a5783815b0ab1200a2d5ff729b7eba074">QsciLexerFortran77::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html_a56f3d257ed1e6e1851252ccfceef0ef9"><div class="ttname"><a href="classQsciLexerAVS.html#a56f3d257ed1e6e1851252ccfceef0ef9">QsciLexerAVS::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a253807bb0f4b3db471b059afc70b77db"><div class="ttname"><a href="classQsciScintilla.html#a253807bb0f4b3db471b059afc70b77db">QsciScintilla::setCallTipsStyle</a></div><div class="ttdeci">void setCallTipsStyle(CallTipsStyle style)</div></div>
<div class="ttc" id="aclassQsciStyle_html_a0fd0947a87e3bf43720227b8226b3edd"><div class="ttname"><a href="classQsciStyle.html#a0fd0947a87e3bf43720227b8226b3edd">QsciStyle::visible</a></div><div class="ttdeci">bool visible() const</div><div class="ttdef"><b>Definition:</b> qscistyle.h:162</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_a7a2271db1a39037a429faaa5ff8e399f"><div class="ttname"><a href="classQsciLexerJSON.html#a7a2271db1a39037a429faaa5ff8e399f">QsciLexerJSON::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_acb5ec792032e6108b3c2d6ec6e565f49"><div class="ttname"><a href="classQsciLexerPython.html#acb5ec792032e6108b3c2d6ec6e565f49">QsciLexerPython::indentationGuideView</a></div><div class="ttdeci">virtual int indentationGuideView() const</div><div class="ttdoc">Returns the view used for indentation guides.</div></div>
<div class="ttc" id="aclassQsciLexerD_html_ae4490715b80237feaa25ad92d2fb6313"><div class="ttname"><a href="classQsciLexerD.html#ae4490715b80237feaa25ad92d2fb6313">QsciLexerD::blockStartKeyword</a></div><div class="ttdeci">const char * blockStartKeyword(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_adcf1f06161144f511c549695f9641c77"><div class="ttname"><a href="classQsciScintilla.html#adcf1f06161144f511c549695f9641c77">QsciScintilla::insert</a></div><div class="ttdeci">virtual void insert(const QString &amp;text)</div><div class="ttdoc">Insert the text text at the current position.</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_ad6328325f4c46dce0226712e9db3bba7"><div class="ttname"><a href="classQsciLexerVHDL.html#ad6328325f4c46dce0226712e9db3bba7">QsciLexerVHDL::setFoldAtParenthesis</a></div><div class="ttdeci">virtual void setFoldAtParenthesis(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerMatlab_html_a2bfdf998696531faacf29f6e0aae9a6c"><div class="ttname"><a href="classQsciLexerMatlab.html#a2bfdf998696531faacf29f6e0aae9a6c">QsciLexerMatlab::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciStyle_html_acb06ba468da57cc4ea9e8d496cb33f83"><div class="ttname"><a href="classQsciStyle.html#acb06ba468da57cc4ea9e8d496cb33f83">QsciStyle::setHotspot</a></div><div class="ttdeci">void setHotspot(bool hotspot)</div></div>
<div class="ttc" id="aclassQsciAPIs_html_a07bc73b7a67f8f405578992bae29528c"><div class="ttname"><a href="classQsciAPIs.html#a07bc73b7a67f8f405578992bae29528c">QsciAPIs::~QsciAPIs</a></div><div class="ttdeci">virtual ~QsciAPIs()</div><div class="ttdoc">Destroy the QsciAPIs instance.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a63c0f682eecba626fff511c6b1612ab6"><div class="ttname"><a href="classQsciScintilla.html#a63c0f682eecba626fff511c6b1612ab6">QsciScintilla::contractedFolds</a></div><div class="ttdeci">QList&lt; int &gt; contractedFolds() const</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a8b64cf1677896ea7966338b3f10be14b"><div class="ttname"><a href="classQsciLexerD.html#a8b64cf1677896ea7966338b3f10be14b">QsciLexerD::QsciLexerD</a></div><div class="ttdeci">QsciLexerD(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_ab7048836522bb0c92df17ccc9e8c1577"><div class="ttname"><a href="classQsciLexerJSON.html#ab7048836522bb0c92df17ccc9e8c1577">QsciLexerJSON::highlightComments</a></div><div class="ttdeci">bool highlightComments() const</div><div class="ttdef"><b>Definition:</b> qscilexerjson.h:134</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_a981f7ababe1cc561b29617fad8aa29b5"><div class="ttname"><a href="classQsciLexerPostScript.html#a981f7ababe1cc561b29617fad8aa29b5">QsciLexerPostScript::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexerXML_html_a29937d422c25f17612c57e16a7bddaf1"><div class="ttname"><a href="classQsciLexerXML.html#a29937d422c25f17612c57e16a7bddaf1">QsciLexerXML::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_a367d2a52388bd2602642f4b5dc01bba2"><div class="ttname"><a href="classQsciLexerVHDL.html#a367d2a52388bd2602642f4b5dc01bba2">QsciLexerVHDL::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_af187d6973df01f3f704b181a446ea2f5"><div class="ttname"><a href="classQsciLexerRuby.html#af187d6973df01f3f704b181a446ea2f5">QsciLexerRuby::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a2cef89549882e03a6290af8cbbf1a3ce"><div class="ttname"><a href="classQsciScintillaBase.html#a2cef89549882e03a6290af8cbbf1a3ce">QsciScintillaBase::SendScintilla</a></div><div class="ttdeci">long SendScintilla(unsigned int msg, unsigned long wParam, const QPixmap &amp;lParam) const</div><div class="ttdoc">This is an overloaded member function, provided for convenience. It differs from the above function o...</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a7c5dafabba34ff3e6120d9f3606cade0"><div class="ttname"><a href="classQsciLexerCPP.html#a7c5dafabba34ff3e6120d9f3606cade0">QsciLexerCPP::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ac05da1bc5b91c682f192ff594552a306"><div class="ttname"><a href="classQsciScintilla.html#ac05da1bc5b91c682f192ff594552a306">QsciScintilla::changeEvent</a></div><div class="ttdeci">virtual void changeEvent(QEvent *e)</div><div class="ttdoc">\reimp</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_ac84577ad9cdb480293fe6001e71085a8"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#ac84577ad9cdb480293fe6001e71085a8">QsciLexerCoffeeScript::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerMarkdown_html"><div class="ttname"><a href="classQsciLexerMarkdown.html">QsciLexerMarkdown</a></div><div class="ttdoc">The QsciLexerMarkdown class encapsulates the Scintilla Markdown lexer.</div><div class="ttdef"><b>Definition:</b> qscilexermarkdown.h:33</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a47884fcfd8d2b0ab7b8d277cb0325c17"><div class="ttname"><a href="classQsciLexerPerl.html#a47884fcfd8d2b0ab7b8d277cb0325c17">QsciLexerPerl::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_afe42ac5a09816340d4bec920b523aed6"><div class="ttname"><a href="classQsciLexerPython.html#afe42ac5a09816340d4bec920b523aed6">QsciLexerPython::blockLookback</a></div><div class="ttdeci">int blockLookback() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8aae5a0037937ad6c8bdfe868e4a8ad5"><div class="ttname"><a href="classQsciScintilla.html#a8aae5a0037937ad6c8bdfe868e4a8ad5">QsciScintilla::selectAll</a></div><div class="ttdeci">virtual void selectAll(bool select=true)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aa9612621315a0b3fcecdb8d0f2a2abb2"><div class="ttname"><a href="classQsciScintilla.html#aa9612621315a0b3fcecdb8d0f2a2abb2">QsciScintilla::markerLine</a></div><div class="ttdeci">int markerLine(int mhandle) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a7451e82e2ee3d0ddb3b8418edb0202f2"><div class="ttname"><a href="classQsciScintilla.html#a7451e82e2ee3d0ddb3b8418edb0202f2">QsciScintilla::setScrollWidthTracking</a></div><div class="ttdeci">void setScrollWidthTracking(bool enabled)</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a0afd856aa4add375643659eace2238fa"><div class="ttname"><a href="classQsciLexerSQL.html#a0afd856aa4add375643659eace2238fa">QsciLexerSQL::~QsciLexerSQL</a></div><div class="ttdeci">virtual ~QsciLexerSQL()</div><div class="ttdoc">Destroys the QsciLexerSQL instance.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca5293176024a0bba9eeb54b061a2930f9"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca5293176024a0bba9eeb54b061a2930f9">QsciScintillaBase::SC_MARK_CIRCLEPLUSCONNECTED</a></div><div class="ttdeci">@ SC_MARK_CIRCLEPLUSCONNECTED</div><div class="ttdoc">A drawn plus sign in a connected box.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2625</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae478a896ae32a30e8a375049a3d477e0af7f61cc984312eacadfaf8e581c1f756"><div class="ttname"><a href="classQsciScintilla.html#ae478a896ae32a30e8a375049a3d477e0af7f61cc984312eacadfaf8e581c1f756">QsciScintilla::NoFoldStyle</a></div><div class="ttdeci">@ NoFoldStyle</div><div class="ttdoc">Folding is disabled.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:211</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_a8a4389bd37a806046a7c0b51cc1a6ead"><div class="ttname"><a href="classQsciLexerVerilog.html#a8a4389bd37a806046a7c0b51cc1a6ead">QsciLexerVerilog::foldAtModule</a></div><div class="ttdeci">bool foldAtModule() const</div><div class="ttdef"><b>Definition:</b> qscilexerverilog.h:223</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html_ad8cabbe5db0e4ba630cfad60ddfc79b1"><div class="ttname"><a href="classQsciLexerProperties.html#ad8cabbe5db0e4ba630cfad60ddfc79b1">QsciLexerProperties::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_a831ed1e8074990eafb57d4b9ebaf3d2f"><div class="ttname"><a href="classQsciLexerPOV.html#a831ed1e8074990eafb57d4b9ebaf3d2f">QsciLexerPOV::setFoldCompact</a></div><div class="ttdeci">virtual void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aec47d94706ffb14ef35035ba827b5b45"><div class="ttname"><a href="classQsciScintilla.html#aec47d94706ffb14ef35035ba827b5b45">QsciScintilla::setCallTipsVisible</a></div><div class="ttdeci">void setCallTipsVisible(int nr)</div></div>
<div class="ttc" id="aclassQsciLexerMarkdown_html_a5372d959cc774781c7271334b2c61b4f"><div class="ttname"><a href="classQsciLexerMarkdown.html#a5372d959cc774781c7271334b2c61b4f">QsciLexerMarkdown::~QsciLexerMarkdown</a></div><div class="ttdeci">virtual ~QsciLexerMarkdown()</div><div class="ttdoc">Destroys the QsciLexerMarkdown instance.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca8770dbe317581062d5d1bcb85592b784"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca8770dbe317581062d5d1bcb85592b784">QsciScintillaBase::SC_MARK_VLINE</a></div><div class="ttdeci">@ SC_MARK_VLINE</div><div class="ttdoc">A vertical line drawn in the background colour.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2592</div></div>
<div class="ttc" id="aclassQsciScintilla_html_afba4d5b30fae60dab7b4279c580beb8c"><div class="ttname"><a href="classQsciScintilla.html#afba4d5b30fae60dab7b4279c580beb8c">QsciScintilla::insertAt</a></div><div class="ttdeci">virtual void insertAt(const QString &amp;text, int line, int index)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a5f140c587d361cf8539814d820d680f4"><div class="ttname"><a href="classQsciScintillaBase.html#a5f140c587d361cf8539814d820d680f4">QsciScintillaBase::SendScintillaPtrResult</a></div><div class="ttdeci">void * SendScintillaPtrResult(unsigned int msg) const</div><div class="ttdoc">Send the Scintilla message msg and return a pointer result.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8e88f3f4369b73980bb40d5d3a8caf26"><div class="ttname"><a href="classQsciScintilla.html#a8e88f3f4369b73980bb40d5d3a8caf26">QsciScintilla::setEdgeColumn</a></div><div class="ttdeci">void setEdgeColumn(int colnr)</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_ac53cb0d155aa3d81add74ee90585cb6a"><div class="ttname"><a href="classQsciLexerHTML.html#ac53cb0d155aa3d81add74ee90585cb6a">QsciLexerHTML::caseSensitiveTags</a></div><div class="ttdeci">bool caseSensitiveTags() const</div><div class="ttdef"><b>Definition:</b> qscilexerhtml.h:424</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_a2a2195f681df3657fbadf72c55003863"><div class="ttname"><a href="classQsciLexerCSS.html#a2a2195f681df3657fbadf72c55003863">QsciLexerCSS::setSCSSLanguage</a></div><div class="ttdeci">void setSCSSLanguage(bool enabled)</div></div>
<div class="ttc" id="aclassQsciLexerCMake_html_a54cd2f0d4c87b28f41dc325d0fe485cc"><div class="ttname"><a href="classQsciLexerCMake.html#a54cd2f0d4c87b28f41dc325d0fe485cc">QsciLexerCMake::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a9da0038950253b116ecd3863a4f63928"><div class="ttname"><a href="classQsciScintilla.html#a9da0038950253b116ecd3863a4f63928">QsciScintilla::linesChanged</a></div><div class="ttdeci">void linesChanged()</div><div class="ttdoc">This signal is emitted whenever the number of lines of text changes.</div></div>
<div class="ttc" id="aclassQsciMacro_html_a1c342b5db46e12a8d73567fac9959543"><div class="ttname"><a href="classQsciMacro.html#a1c342b5db46e12a8d73567fac9959543">QsciMacro::load</a></div><div class="ttdeci">bool load(const QString &amp;asc)</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_aca20aadf0e0bc419b20eeb8ee4e7e261"><div class="ttname"><a href="classQsciLexerAsm.html#aca20aadf0e0bc419b20eeb8ee4e7e261">QsciLexerAsm::foldComments</a></div><div class="ttdeci">bool foldComments() const</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_aabad8cc171c34609ee0d6c600a534334"><div class="ttname"><a href="classQsciLexerHTML.html#aabad8cc171c34609ee0d6c600a534334">QsciLexerHTML::foldScriptHeredocs</a></div><div class="ttdeci">bool foldScriptHeredocs() const</div><div class="ttdef"><b>Definition:</b> qscilexerhtml.h:467</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_a1895725812d581b40913c1a85d2ab533"><div class="ttname"><a href="classQsciLexerTeX.html#a1895725812d581b40913c1a85d2ab533">QsciLexerTeX::setProcessComments</a></div><div class="ttdeci">void setProcessComments(bool enable)</div></div>
<div class="ttc" id="aclassQsciLexerCustom_html_a8dbdaca7dffe587e442d09f5b780fab5"><div class="ttname"><a href="classQsciLexerCustom.html#a8dbdaca7dffe587e442d09f5b780fab5">QsciLexerCustom::QsciLexerCustom</a></div><div class="ttdeci">QsciLexerCustom(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerYAML_html_ab946b8c9f34872b69c31e6e9cd0b0e57"><div class="ttname"><a href="classQsciLexerYAML.html#ab946b8c9f34872b69c31e6e9cd0b0e57">QsciLexerYAML::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerFortran77_html_a1ab3498874bc09e7136c1325498fc49f"><div class="ttname"><a href="classQsciLexerFortran77.html#a1ab3498874bc09e7136c1325498fc49f">QsciLexerFortran77::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html_a9023ef1aa48fd622ecac97a419cb3afe"><div class="ttname"><a href="classQsciLexerAVS.html#a9023ef1aa48fd622ecac97a419cb3afe">QsciLexerAVS::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ab696e4703374af4c01651453d094ac08"><div class="ttname"><a href="classQsciScintilla.html#ab696e4703374af4c01651453d094ac08">QsciScintilla::setWrapVisualFlags</a></div><div class="ttdeci">void setWrapVisualFlags(WrapVisualFlag endFlag, WrapVisualFlag startFlag=WrapFlagNone, int indent=0)</div></div>
<div class="ttc" id="aclassQsciLexerFortran_html_ad18b58e4e78b74f1c1cc0db18a2d74ca"><div class="ttname"><a href="classQsciLexerFortran.html#ad18b58e4e78b74f1c1cc0db18a2d74ca">QsciLexerFortran::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a5e4372bd9b4249d7e574402935444b84"><div class="ttname"><a href="classQsciScintilla.html#a5e4372bd9b4249d7e574402935444b84">QsciScintilla::eolVisibility</a></div><div class="ttdeci">bool eolVisibility() const</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_a2fda8ad57009d4e2f1ac388cad2cfc92"><div class="ttname"><a href="classQsciLexerHTML.html#a2fda8ad57009d4e2f1ac388cad2cfc92">QsciLexerHTML::setCaseSensitiveTags</a></div><div class="ttdeci">virtual void setCaseSensitiveTags(bool sens)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a6b494f1a0395a62c3e61f50eabc50679"><div class="ttname"><a href="classQsciScintilla.html#a6b494f1a0395a62c3e61f50eabc50679">QsciScintilla::setAutoCompletionShowSingle</a></div><div class="ttdeci">virtual void setAutoCompletionShowSingle(bool single)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a1bd5470bc123a43c98facfc5c4a1e523"><div class="ttname"><a href="classQsciScintilla.html#a1bd5470bc123a43c98facfc5c4a1e523">QsciScintilla::setTabWidth</a></div><div class="ttdeci">virtual void setTabWidth(int width)</div></div>
<div class="ttc" id="aclassQsciPrinter_html_aa95827e3bd2c3c0e658afe55fa12476e"><div class="ttname"><a href="classQsciPrinter.html#aa95827e3bd2c3c0e658afe55fa12476e">QsciPrinter::setWrapMode</a></div><div class="ttdeci">virtual void setWrapMode(QsciScintilla::WrapMode wmode)</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_af8c28786c65b23583d92030ac606d07c"><div class="ttname"><a href="classQsciLexerPython.html#af8c28786c65b23583d92030ac606d07c">QsciLexerPython::highlightSubidentifiers</a></div><div class="ttdeci">bool highlightSubidentifiers() const</div><div class="ttdef"><b>Definition:</b> qscilexerpython.h:223</div></div>
<div class="ttc" id="aclassQsciLexerOctave_html"><div class="ttname"><a href="classQsciLexerOctave.html">QsciLexerOctave</a></div><div class="ttdoc">The QsciLexerOctave class encapsulates the Scintilla Octave file lexer.</div><div class="ttdef"><b>Definition:</b> qscilexeroctave.h:33</div></div>
<div class="ttc" id="aclassQsciLexerSRec_html_a3e37c3d7527369901bcb28bba3b823e6"><div class="ttname"><a href="classQsciLexerSRec.html#a3e37c3d7527369901bcb28bba3b823e6">QsciLexerSRec::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a906faecb0defd2d5a14cac54f8415dcf"><div class="ttname"><a href="classQsciScintillaBase.html#a906faecb0defd2d5a14cac54f8415dcf">QsciScintillaBase::SCN_HOTSPOTRELEASECLICK</a></div><div class="ttdeci">void SCN_HOTSPOTRELEASECLICK(int position, int modifiers)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aee2887fadd0ddac9cee9dcdaee8d8696"><div class="ttname"><a href="classQsciScintilla.html#aee2887fadd0ddac9cee9dcdaee8d8696">QsciScintilla::edgeColor</a></div><div class="ttdeci">QColor edgeColor() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae478a896ae32a30e8a375049a3d477e0a15a9a221d1506423c667adbcd27af185"><div class="ttname"><a href="classQsciScintilla.html#ae478a896ae32a30e8a375049a3d477e0a15a9a221d1506423c667adbcd27af185">QsciScintilla::PlainFoldStyle</a></div><div class="ttdeci">@ PlainFoldStyle</div><div class="ttdoc">Plain folding style using plus and minus symbols.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:214</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_abd6f026e6cb154c64c581f6e5f7f2fed"><div class="ttname"><a href="classQsciLexerRuby.html#abd6f026e6cb154c64c581f6e5f7f2fed">QsciLexerRuby::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae8277ccb3a2af0ae9a1495d8f8ea0523ac95c16fe24bef36ac479b7ca282442ab"><div class="ttname"><a href="classQsciScintilla.html#ae8277ccb3a2af0ae9a1495d8f8ea0523ac95c16fe24bef36ac479b7ca282442ab">QsciScintilla::StrictBraceMatch</a></div><div class="ttdeci">@ StrictBraceMatch</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:139</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a42cb45ea05c71180a594e0cc8041c07d"><div class="ttname"><a href="classQsciScintillaBase.html#a42cb45ea05c71180a594e0cc8041c07d">QsciScintillaBase::SCN_URIDROPPED</a></div><div class="ttdeci">void SCN_URIDROPPED(const QUrl &amp;url)</div></div>
<div class="ttc" id="aclassQsciAPIs_html_a6a75974235c5e8d263bf962c778b3a3e"><div class="ttname"><a href="classQsciAPIs.html#a6a75974235c5e8d263bf962c778b3a3e">QsciAPIs::callTips</a></div><div class="ttdeci">virtual QStringList callTips(const QStringList &amp;context, int commas, QsciScintilla::CallTipsStyle style, QList&lt; int &gt; &amp;shifts)</div><div class="ttdoc">\reimp</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a12673046e00bd7fdd7616a5e8c62a924"><div class="ttname"><a href="classQsciScintilla.html#a12673046e00bd7fdd7616a5e8c62a924">QsciScintilla::markerAdd</a></div><div class="ttdeci">int markerAdd(int linenr, int markerNumber)</div></div>
<div class="ttc" id="aclassQsciLexerCustom_html_a224fe82235b9a1c7b9c8bec9dd441178"><div class="ttname"><a href="classQsciLexerCustom.html#a224fe82235b9a1c7b9c8bec9dd441178">QsciLexerCustom::setEditor</a></div><div class="ttdeci">virtual void setEditor(QsciScintilla *editor)</div><div class="ttdoc">\reimp</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a340cd78e46fb58fc9b3b78ed158ba56e"><div class="ttname"><a href="classQsciScintilla.html#a340cd78e46fb58fc9b3b78ed158ba56e">QsciScintilla::setFolding</a></div><div class="ttdeci">virtual void setFolding(FoldStyle fold, int margin=2)</div></div>
<div class="ttc" id="aclassQsciLexerXML_html_a2acbf99b93c18d9a9f922c9e2894bf4f"><div class="ttname"><a href="classQsciLexerXML.html#a2acbf99b93c18d9a9f922c9e2894bf4f">QsciLexerXML::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_abed099316dd95a6289c76d151a37c264"><div class="ttname"><a href="classQsciLexerPython.html#abed099316dd95a6289c76d151a37c264">QsciLexerPython::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_aff3e2883cee59a8858f85964b39ae59c"><div class="ttname"><a href="classQsciLexerAsm.html#aff3e2883cee59a8858f85964b39ae59c">QsciLexerAsm::commentDelimiter</a></div><div class="ttdeci">QChar commentDelimiter() const</div></div>
<div class="ttc" id="aclassQsciLexerTekHex_html_a5844e0809729932ab2e3702990b756a4"><div class="ttname"><a href="classQsciLexerTekHex.html#a5844e0809729932ab2e3702990b756a4">QsciLexerTekHex::QsciLexerTekHex</a></div><div class="ttdeci">QsciLexerTekHex(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a2ffd6d691d5a63940e448138f3754a47"><div class="ttname"><a href="classQsciScintilla.html#a2ffd6d691d5a63940e448138f3754a47">QsciScintilla::setIndicatorForegroundColor</a></div><div class="ttdeci">void setIndicatorForegroundColor(const QColor &amp;col, int indicatorNumber=-1)</div></div>
<div class="ttc" id="aclassQsciLexerYAML_html_a64b5ac3d0a8a5a7113905fa421edb1ad"><div class="ttname"><a href="classQsciLexerYAML.html#a64b5ac3d0a8a5a7113905fa421edb1ad">QsciLexerYAML::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerTekHex_html"><div class="ttname"><a href="classQsciLexerTekHex.html">QsciLexerTekHex</a></div><div class="ttdoc">The QsciLexerTekHex class encapsulates the Scintilla Tektronix Hex lexer.</div><div class="ttdef"><b>Definition:</b> qscilexertekhex.h:33</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ab29919614210e2bb21525b1d7fec44b1"><div class="ttname"><a href="classQsciScintilla.html#ab29919614210e2bb21525b1d7fec44b1">QsciScintilla::marginMarkerMask</a></div><div class="ttdeci">int marginMarkerMask(int margin) const</div></div>
<div class="ttc" id="aclassQsciLexer_html_a4e10650b0e9ad137062ad5c17ad33e76"><div class="ttname"><a href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">QsciLexer::autoCompletionWordSeparators</a></div><div class="ttdeci">virtual QStringList autoCompletionWordSeparators() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a5fddd1e6e19cf2e2b40c15a39e62d198"><div class="ttname"><a href="classQsciScintilla.html#a5fddd1e6e19cf2e2b40c15a39e62d198">QsciScintilla::setMarginLineNumbers</a></div><div class="ttdeci">virtual void setMarginLineNumbers(int margin, bool lnrs)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8403fbfd3afcaed7076359bb899d5136"><div class="ttname"><a href="classQsciScintilla.html#a8403fbfd3afcaed7076359bb899d5136">QsciScintilla::isReadOnly</a></div><div class="ttdeci">bool isReadOnly() const</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_a122450b5227d23ee119b2653b9e9be2f"><div class="ttname"><a href="classQsciLexerHTML.html#a122450b5227d23ee119b2653b9e9be2f">QsciLexerHTML::setFoldScriptHeredocs</a></div><div class="ttdeci">void setFoldScriptHeredocs(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_a14f1638b2f668fb7d98791cda719f8a0"><div class="ttname"><a href="classQsciLexerRuby.html#a14f1638b2f668fb7d98791cda719f8a0">QsciLexerRuby::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_a7ceeb1fcb0dee86889484767dae5440b"><div class="ttname"><a href="classQsciLexerHTML.html#a7ceeb1fcb0dee86889484767dae5440b">QsciLexerHTML::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerHex_html_a4cc758f6c9018dd3abeba0291a1008f2"><div class="ttname"><a href="classQsciLexerHex.html#a4cc758f6c9018dd3abeba0291a1008f2">QsciLexerHex::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciAPIs_html_acb1aa10ea05a7ee72a0d77376153b4d2"><div class="ttname"><a href="classQsciAPIs.html#acb1aa10ea05a7ee72a0d77376153b4d2">QsciAPIs::remove</a></div><div class="ttdeci">void remove(const QString &amp;entry)</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html_a2e2317346a85697a98a2e19d1c596a48"><div class="ttname"><a href="classQsciLexerProperties.html#a2e2317346a85697a98a2e19d1c596a48">QsciLexerProperties::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div><div class="ttdef"><b>Definition:</b> qscilexerproperties.h:104</div></div>
<div class="ttc" id="aclassQsciScintilla_html_af9015c62600c87eef82f715bf61da913"><div class="ttname"><a href="classQsciScintilla.html#af9015c62600c87eef82f715bf61da913">QsciScintilla::setCaretForegroundColor</a></div><div class="ttdeci">virtual void setCaretForegroundColor(const QColor &amp;col)</div><div class="ttdoc">Sets the foreground colour of the caret to col.</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_acc91bd455ff72d93d0bb73b553afbbb8"><div class="ttname"><a href="classQsciLexerSQL.html#acc91bd455ff72d93d0bb73b553afbbb8">QsciLexerSQL::setHashComments</a></div><div class="ttdeci">void setHashComments(bool enable)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a0aeb5804552ea61f04aff90cb6739f39"><div class="ttname"><a href="classQsciScintilla.html#a0aeb5804552ea61f04aff90cb6739f39">QsciScintilla::paste</a></div><div class="ttdeci">virtual void paste()</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a42ae037173aab16ce5e14788e6331623"><div class="ttname"><a href="classQsciScintilla.html#a42ae037173aab16ce5e14788e6331623">QsciScintilla::showUserList</a></div><div class="ttdeci">void showUserList(int id, const QStringList &amp;list)</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_abf07dc83c19a3925e3cb977bf883b04c"><div class="ttname"><a href="classQsciLexerSQL.html#abf07dc83c19a3925e3cb977bf883b04c">QsciLexerSQL::backslashEscapes</a></div><div class="ttdeci">bool backslashEscapes() const</div><div class="ttdef"><b>Definition:</b> qscilexersql.h:163</div></div>
<div class="ttc" id="aclassQsciScintilla_html_acd6fbd352060d3ee893bacc9bbb28a4e"><div class="ttname"><a href="classQsciScintilla.html#acd6fbd352060d3ee893bacc9bbb28a4e">QsciScintilla::indentationGuides</a></div><div class="ttdeci">bool indentationGuides() const</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html"><div class="ttname"><a href="classQsciLexerAsm.html">QsciLexerAsm</a></div><div class="ttdoc">The abstract QsciLexerAsm class encapsulates the Scintilla Asm lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerasm.h:34</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a0128aae31e7d02fa580b55c9675d66e9"><div class="ttname"><a href="classQsciScintilla.html#a0128aae31e7d02fa580b55c9675d66e9">QsciScintilla::undo</a></div><div class="ttdeci">virtual void undo()</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214cabf08078081d1fb79be98e1b5a6401ec3"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cabf08078081d1fb79be98e1b5a6401ec3">QsciScintillaBase::SC_MARK_CIRCLE</a></div><div class="ttdeci">@ SC_MARK_CIRCLE</div><div class="ttdoc">A circle.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2564</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_adbeb88fef8346b6543d6ef8b2154e763"><div class="ttname"><a href="classQsciLexerLua.html#adbeb88fef8346b6543d6ef8b2154e763">QsciLexerLua::~QsciLexerLua</a></div><div class="ttdeci">virtual ~QsciLexerLua()</div><div class="ttdoc">Destroys the QsciLexerLua instance.</div></div>
<div class="ttc" id="aclassQsciAbstractAPIs_html_a90452ab6f4d40314ec519913f9e78ccc"><div class="ttname"><a href="classQsciAbstractAPIs.html#a90452ab6f4d40314ec519913f9e78ccc">QsciAbstractAPIs::lexer</a></div><div class="ttdeci">QsciLexer * lexer() const</div><div class="ttdoc">Return the lexer that the instance is attached to.</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html_a86be2cbea60ab7b3419ed3bf2db7c5ce"><div class="ttname"><a href="classQsciLexerAVS.html#a86be2cbea60ab7b3419ed3bf2db7c5ce">QsciLexerAVS::setFoldComments</a></div><div class="ttdeci">virtual void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a68f0cf388c3fa6a70ece2184020ffe55"><div class="ttname"><a href="classQsciLexerD.html#a68f0cf388c3fa6a70ece2184020ffe55">QsciLexerD::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexer_html_a19f0b390b5594d0dff5e4d4b484e43d2"><div class="ttname"><a href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">QsciLexer::setDefaultFont</a></div><div class="ttdeci">void setDefaultFont(const QFont &amp;f)</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_ae8c0599c4eb74db6caa8624bcc416a8b"><div class="ttname"><a href="classQsciLexerVHDL.html#ae8c0599c4eb74db6caa8624bcc416a8b">QsciLexerVHDL::setFoldAtElse</a></div><div class="ttdeci">virtual void setFoldAtElse(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexer_html_a79c27285b6033c553b3f54cb6c56b338"><div class="ttname"><a href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">QsciLexer::autoIndentStyle</a></div><div class="ttdeci">int autoIndentStyle()</div></div>
<div class="ttc" id="aclassQsciLexerCSharp_html"><div class="ttname"><a href="classQsciLexerCSharp.html">QsciLexerCSharp</a></div><div class="ttdoc">The QsciLexerCSharp class encapsulates the Scintilla C# lexer.</div><div class="ttdef"><b>Definition:</b> qscilexercsharp.h:33</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a07fe5007913f0001a473d92f4c3fdbe6"><div class="ttname"><a href="classQsciScintilla.html#a07fe5007913f0001a473d92f4c3fdbe6">QsciScintilla::recolor</a></div><div class="ttdeci">virtual void recolor(int start=0, int end=-1)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a9b1a8ed3235c506ffca09260cdd0e209"><div class="ttname"><a href="classQsciScintilla.html#a9b1a8ed3235c506ffca09260cdd0e209">QsciScintilla::setScrollWidth</a></div><div class="ttdeci">void setScrollWidth(int pixelWidth)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aeeccf4091f42418284a79dcf67419a5c"><div class="ttname"><a href="classQsciScintilla.html#aeeccf4091f42418284a79dcf67419a5c">QsciScintilla::caseSensitive</a></div><div class="ttdeci">bool caseSensitive() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ab98db9f5166ccc23792aea15a19c2294"><div class="ttname"><a href="classQsciScintilla.html#ab98db9f5166ccc23792aea15a19c2294">QsciScintilla::setEolVisibility</a></div><div class="ttdeci">virtual void setEolVisibility(bool visible)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae2e859fdafd8c56106b823d9e1ef99dd"><div class="ttname"><a href="classQsciScintilla.html#ae2e859fdafd8c56106b823d9e1ef99dd">QsciScintilla::indentation</a></div><div class="ttdeci">int indentation(int line) const</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_a99f8420666e55b23980d05903e7eebc3"><div class="ttname"><a href="classQsciLexerPOV.html#a99f8420666e55b23980d05903e7eebc3">QsciLexerPOV::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_a189a9efbe5c2fa07757d67c013229e19"><div class="ttname"><a href="classQsciLexerPOV.html#a189a9efbe5c2fa07757d67c013229e19">QsciLexerPOV::setFoldComments</a></div><div class="ttdeci">virtual void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a1060a2e187518d1c8b2814c393e227c6"><div class="ttname"><a href="classQsciScintilla.html#a1060a2e187518d1c8b2814c393e227c6">QsciScintilla::setSelectionForegroundColor</a></div><div class="ttdeci">virtual void setSelectionForegroundColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciLexerCMake_html_a7ca16327ee98a2e0dc3a59b2f745d778"><div class="ttname"><a href="classQsciLexerCMake.html#a7ca16327ee98a2e0dc3a59b2f745d778">QsciLexerCMake::~QsciLexerCMake</a></div><div class="ttdeci">virtual ~QsciLexerCMake()</div><div class="ttdoc">Destroys the QsciLexerCMake instance.</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_aae0cfbb2dbfd2a833a16630c9cf2e36e"><div class="ttname"><a href="classQsciLexerBash.html#aae0cfbb2dbfd2a833a16630c9cf2e36e">QsciLexerBash::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciStyle_html"><div class="ttname"><a href="classQsciStyle.html">QsciStyle</a></div><div class="ttdoc">The QsciStyle class encapsulates all the attributes of a style.</div><div class="ttdef"><b>Definition:</b> qscistyle.h:42</div></div>
<div class="ttc" id="aclassQsciLexer_html_add9c20adb43bc38d1a0ca3083ac3e6fa"><div class="ttname"><a href="classQsciLexer.html#add9c20adb43bc38d1a0ca3083ac3e6fa">QsciLexer::description</a></div><div class="ttdeci">virtual QString description(int style) const =0</div></div>
<div class="ttc" id="aclassQsciStyle_html_adcc34134da3341f1f07a847b09f6565b"><div class="ttname"><a href="classQsciStyle.html#adcc34134da3341f1f07a847b09f6565b">QsciStyle::eolFill</a></div><div class="ttdeci">bool eolFill() const</div><div class="ttdef"><b>Definition:</b> qscistyle.h:141</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ad8a7afc4f25e04e805b7e291d5df1d35"><div class="ttname"><a href="classQsciScintilla.html#ad8a7afc4f25e04e805b7e291d5df1d35">QsciScintilla::replace</a></div><div class="ttdeci">virtual void replace(const QString &amp;replaceStr)</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html_a61d706ce1554474fd54fe07359612814"><div class="ttname"><a href="classQsciLexerProperties.html#a61d706ce1554474fd54fe07359612814">QsciLexerProperties::wordCharacters</a></div><div class="ttdeci">const char * wordCharacters() const</div><div class="ttdoc">Returns the string of characters that comprise a word.</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_a21f63849918a4fbeda81dc5f79fa81c2"><div class="ttname"><a href="classQsciLexerTeX.html#a21f63849918a4fbeda81dc5f79fa81c2">QsciLexerTeX::setFoldCompact</a></div><div class="ttdeci">void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciPrinter_html_a9c7747919e355a885d6ebb4b0d0dc619"><div class="ttname"><a href="classQsciPrinter.html#a9c7747919e355a885d6ebb4b0d0dc619">QsciPrinter::~QsciPrinter</a></div><div class="ttdeci">virtual ~QsciPrinter()</div><div class="ttdoc">Destroys the QsciPrinter instance.</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a2a2beba3b365e2e0e1f21109079f0ffd"><div class="ttname"><a href="classQsciLexerPascal.html#a2a2beba3b365e2e0e1f21109079f0ffd">QsciLexerPascal::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_a942c993effc83d0dedec2fc20d8a741f"><div class="ttname"><a href="classQsciLexerLua.html#a942c993effc83d0dedec2fc20d8a741f">QsciLexerLua::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae628d46489efa3db3b0c42336a1bf8d3afd235254a43ffd260648079f71a31f7e"><div class="ttname"><a href="classQsciScintilla.html#ae628d46489efa3db3b0c42336a1bf8d3afd235254a43ffd260648079f71a31f7e">QsciScintilla::AcusNever</a></div><div class="ttdeci">@ AcusNever</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:102</div></div>
<div class="ttc" id="aclassQsciLexerTCL_html_ad6f1adced83d4017ef5ea75ea338c117"><div class="ttname"><a href="classQsciLexerTCL.html#ad6f1adced83d4017ef5ea75ea338c117">QsciLexerTCL::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_aea30a66d5e4e7d0064366eefec03364c"><div class="ttname"><a href="classQsciLexerPOV.html#aea30a66d5e4e7d0064366eefec03364c">QsciLexerPOV::setFoldDirectives</a></div><div class="ttdeci">virtual void setFoldDirectives(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_a2a1215dab25c15adf3c1bd6a5b063f91"><div class="ttname"><a href="classQsciLexerVHDL.html#a2a1215dab25c15adf3c1bd6a5b063f91">QsciLexerVHDL::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a9ce32df9e150ef76a24c50af6b09e966"><div class="ttname"><a href="classQsciScintilla.html#a9ce32df9e150ef76a24c50af6b09e966">QsciScintilla::resetSelectionForegroundColor</a></div><div class="ttdeci">virtual void resetSelectionForegroundColor()</div></div>
<div class="ttc" id="aclassQsciLexerFortran77_html_ae3ef35311f24a24300140512dd005f54"><div class="ttname"><a href="classQsciLexerFortran77.html#ae3ef35311f24a24300140512dd005f54">QsciLexerFortran77::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a136a17a59a3800c40619a768ffff8d7a"><div class="ttname"><a href="classQsciScintilla.html#a136a17a59a3800c40619a768ffff8d7a">QsciScintilla::autoCompletionCaseSensitivity</a></div><div class="ttdeci">bool autoCompletionCaseSensitivity() const</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a9c18ede5b5271ee1885b38083271aa9e"><div class="ttname"><a href="classQsciLexerPascal.html#a9c18ede5b5271ee1885b38083271aa9e">QsciLexerPascal::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexer_html_aff4735542e937c5e35ecb2eb82e8f875"><div class="ttname"><a href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">QsciLexer::indentationGuideView</a></div><div class="ttdeci">virtual int indentationGuideView() const</div><div class="ttdoc">Returns the view used for indentation guides.</div></div>
<div class="ttc" id="aclassQsciLexerMASM_html_a3664445f51245044f5e82941d36931d6"><div class="ttname"><a href="classQsciLexerMASM.html#a3664445f51245044f5e82941d36931d6">QsciLexerMASM::~QsciLexerMASM</a></div><div class="ttdeci">virtual ~QsciLexerMASM()</div><div class="ttdoc">Destroys the QsciLexerMASM instance.</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a072c10d35abc0e56e09806eeb78ab66f"><div class="ttname"><a href="classQsciLexerPascal.html#a072c10d35abc0e56e09806eeb78ab66f">QsciLexerPascal::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_a4ceef374a27fd3bb012a7b55f19303e2"><div class="ttname"><a href="classQsciLexerHTML.html#a4ceef374a27fd3bb012a7b55f19303e2">QsciLexerHTML::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_ab86225b96219799a77f77600f145042a"><div class="ttname"><a href="classQsciLexerSQL.html#ab86225b96219799a77f77600f145042a">QsciLexerSQL::QsciLexerSQL</a></div><div class="ttdeci">QsciLexerSQL(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a722a2f16b67ef5f46def6914a6e178c3"><div class="ttname"><a href="classQsciScintillaBase.html#a722a2f16b67ef5f46def6914a6e178c3">QsciScintillaBase::SCN_MARGINCLICK</a></div><div class="ttdeci">void SCN_MARGINCLICK(int position, int modifiers, int margin)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a5afec2f913c2a46d661c5fdda29f5e87"><div class="ttname"><a href="classQsciScintilla.html#a5afec2f913c2a46d661c5fdda29f5e87">QsciScintilla::marginClicked</a></div><div class="ttdeci">void marginClicked(int margin, int line, Qt::KeyboardModifiers state)</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_aaeb3a87a051af9cc20b5319ed8cd6ca1"><div class="ttname"><a href="classQsciLexerVerilog.html#aaeb3a87a051af9cc20b5319ed8cd6ca1">QsciLexerVerilog::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciMacro_html"><div class="ttname"><a href="classQsciMacro.html">QsciMacro</a></div><div class="ttdoc">The QsciMacro class represents a sequence of recordable editor commands.</div><div class="ttdef"><b>Definition:</b> qscimacro.h:41</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html"><div class="ttname"><a href="classQsciLexerHTML.html">QsciLexerHTML</a></div><div class="ttdoc">The QsciLexerHTML class encapsulates the Scintilla HTML lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerhtml.h:32</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_acd0614489de2d2c9f69379a574f1d5eb"><div class="ttname"><a href="classQsciLexerJSON.html#acd0614489de2d2c9f69379a574f1d5eb">QsciLexerJSON::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ac141edcf0d537b247d8a1e79194db60b"><div class="ttname"><a href="classQsciScintilla.html#ac141edcf0d537b247d8a1e79194db60b">QsciScintilla::marginSensitivity</a></div><div class="ttdeci">bool marginSensitivity(int margin) const</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_a8bc6aee27994356e61fc6b030e23a62f"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#a8bc6aee27994356e61fc6b030e23a62f">QsciLexerCoffeeScript::setFoldCompact</a></div><div class="ttdeci">void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_aebc02afb8158d445c4369efa287cc2ac"><div class="ttname"><a href="classQsciLexerPascal.html#aebc02afb8158d445c4369efa287cc2ac">QsciLexerPascal::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciStyle_html_abd88d76b875c154f099b4e9f36b6fcab"><div class="ttname"><a href="classQsciStyle.html#abd88d76b875c154f099b4e9f36b6fcab">QsciStyle::setDescription</a></div><div class="ttdeci">void setDescription(const QString &amp;description)</div><div class="ttdef"><b>Definition:</b> qscistyle.h:93</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a9fa4dc539be7b199e91d6ff0f83e5f8d"><div class="ttname"><a href="classQsciScintillaBase.html#a9fa4dc539be7b199e91d6ff0f83e5f8d">QsciScintillaBase::SendScintilla</a></div><div class="ttdeci">long SendScintilla(unsigned int msg, const char *wParam, const char *lParam) const</div><div class="ttdoc">This is an overloaded member function, provided for convenience. It differs from the above function o...</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a56542fe5a9e5790eab29936b73ef0fa3"><div class="ttname"><a href="classQsciScintillaBase.html#a56542fe5a9e5790eab29936b73ef0fa3">QsciScintillaBase::SendScintilla</a></div><div class="ttdeci">long SendScintilla(unsigned int msg, unsigned long wParam, const QImage &amp;lParam) const</div><div class="ttdoc">This is an overloaded member function, provided for convenience. It differs from the above function o...</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a85aa8e72d81818a7edea1867362db16a"><div class="ttname"><a href="classQsciLexerPerl.html#a85aa8e72d81818a7edea1867362db16a">QsciLexerPerl::setFoldComments</a></div><div class="ttdeci">virtual void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a6a5b21a2ba8b43a2f6b3747af365156f"><div class="ttname"><a href="classQsciLexerPascal.html#a6a5b21a2ba8b43a2f6b3747af365156f">QsciLexerPascal::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_aa1949e1c7fd18507f664babab7b3c56c"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#aa1949e1c7fd18507f664babab7b3c56c">QsciLexerCoffeeScript::setStylePreprocessor</a></div><div class="ttdeci">void setStylePreprocessor(bool style)</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_a8a24cd2bdd449e16ae5d00db7a1826bf"><div class="ttname"><a href="classQsciLexerJSON.html#a8a24cd2bdd449e16ae5d00db7a1826bf">QsciLexerJSON::setFoldCompact</a></div><div class="ttdeci">void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae89d58a67f46efad7136bead41232fd6"><div class="ttname"><a href="classQsciScintilla.html#ae89d58a67f46efad7136bead41232fd6">QsciScintilla::autoCompleteFromAPIs</a></div><div class="ttdeci">virtual void autoCompleteFromAPIs()</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8010e1671a15976254fd11b59ca3e03d"><div class="ttname"><a href="classQsciScintilla.html#a8010e1671a15976254fd11b59ca3e03d">QsciScintilla::setIndentationWidth</a></div><div class="ttdeci">virtual void setIndentationWidth(int width)</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_a0e2e853e0ad4e806f13c0468ccb80132"><div class="ttname"><a href="classQsciLexerAsm.html#a0e2e853e0ad4e806f13c0468ccb80132">QsciLexerAsm::~QsciLexerAsm</a></div><div class="ttdeci">virtual ~QsciLexerAsm()</div><div class="ttdoc">Destroys the QsciLexerAsm instance.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a3fd2a1bbc409d1bbca44efc7976b808c"><div class="ttname"><a href="classQsciScintilla.html#a3fd2a1bbc409d1bbca44efc7976b808c">QsciScintilla::resetHotspotForegroundColor</a></div><div class="ttdeci">void resetHotspotForegroundColor()</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_a78f4690fa92e02c8511074a334c06096"><div class="ttname"><a href="classQsciLexerCSS.html#a78f4690fa92e02c8511074a334c06096">QsciLexerCSS::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html"><div class="ttname"><a href="classQsciScintillaBase.html">QsciScintillaBase</a></div><div class="ttdoc">The QsciScintillaBase class implements the Scintilla editor widget and its low-level API.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:61</div></div>
<div class="ttc" id="aclassQsciLexer_html_aa6ed26c11f54f71a305d3ee03d685f06"><div class="ttname"><a href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">QsciLexer::eolFill</a></div><div class="ttdeci">virtual bool eolFill(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a21af5f49389a552a5beb05764714882a"><div class="ttname"><a href="classQsciScintilla.html#a21af5f49389a552a5beb05764714882a">QsciScintilla::marginWidth</a></div><div class="ttdeci">int marginWidth(int margin) const</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_ab2d6a4d13e15769bf1110012b491ad90"><div class="ttname"><a href="classQsciLexerPostScript.html#ab2d6a4d13e15769bf1110012b491ad90">QsciLexerPostScript::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_a07a738771d1dd29aaae584944cf6d637"><div class="ttname"><a href="classQsciLexerJSON.html#a07a738771d1dd29aaae584944cf6d637">QsciLexerJSON::~QsciLexerJSON</a></div><div class="ttdeci">virtual ~QsciLexerJSON()</div><div class="ttdoc">Destroys the QsciLexerJSON instance.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aa85349be1012a11e4198949c5537a6b2"><div class="ttname"><a href="classQsciScintilla.html#aa85349be1012a11e4198949c5537a6b2">QsciScintilla::ensureCursorVisible</a></div><div class="ttdeci">virtual void ensureCursorVisible()</div><div class="ttdoc">Ensures that the cursor is visible.</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_a1bc505b1b0f41472062f86b205ea2860"><div class="ttname"><a href="classQsciLexerBash.html#a1bc505b1b0f41472062f86b205ea2860">QsciLexerBash::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a6423854a3c2f7da07db1e47f2f0c8c71"><div class="ttname"><a href="classQsciScintilla.html#a6423854a3c2f7da07db1e47f2f0c8c71">QsciScintilla::getSelection</a></div><div class="ttdeci">void getSelection(int *lineFrom, int *indexFrom, int *lineTo, int *indexTo) const</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a68d8b422b0d733592cc896086ca23652"><div class="ttname"><a href="classQsciLexerPascal.html#a68d8b422b0d733592cc896086ca23652">QsciLexerPascal::blockStart</a></div><div class="ttdeci">const char * blockStart(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_abe7fcae4d84483ecd934c60bfdaee4a8"><div class="ttname"><a href="classQsciScintilla.html#abe7fcae4d84483ecd934c60bfdaee4a8">QsciScintilla::ensureLineVisible</a></div><div class="ttdeci">virtual void ensureLineVisible(int line)</div><div class="ttdoc">Ensures that the line number line is visible.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_acb9f67f141d5e81f68342e9507a308d3"><div class="ttname"><a href="classQsciScintilla.html#acb9f67f141d5e81f68342e9507a308d3">QsciScintilla::TabDrawMode</a></div><div class="ttdeci">TabDrawMode</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:452</div></div>
<div class="ttc" id="aclassQsciLexerCSharp_html_a3e9cfc0d233a1dcd3f20d33c4401e9fd"><div class="ttname"><a href="classQsciLexerCSharp.html#a3e9cfc0d233a1dcd3f20d33c4401e9fd">QsciLexerCSharp::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a33c8d6d9915a1375c0d7c24beaceb951"><div class="ttname"><a href="classQsciScintillaBase.html#a33c8d6d9915a1375c0d7c24beaceb951">QsciScintillaBase::dropEvent</a></div><div class="ttdeci">virtual void dropEvent(QDropEvent *e)</div><div class="ttdoc">Re-implemented to handle drops.</div></div>
<div class="ttc" id="aclassQsciMacro_html_a4387e4fa992c8671dd508c0c2651e34f"><div class="ttname"><a href="classQsciMacro.html#a4387e4fa992c8671dd508c0c2651e34f">QsciMacro::clear</a></div><div class="ttdeci">void clear()</div><div class="ttdoc">Clear the contents of the macro.</div></div>
<div class="ttc" id="aclassQsciLexerMakefile_html_a4568ee117191969976b674227b16c860"><div class="ttname"><a href="classQsciLexerMakefile.html#a4568ee117191969976b674227b16c860">QsciLexerMakefile::QsciLexerMakefile</a></div><div class="ttdeci">QsciLexerMakefile(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html_a1ef24398e95c23a8b3c858179e5eb564"><div class="ttname"><a href="classQsciLexerAVS.html#a1ef24398e95c23a8b3c858179e5eb564">QsciLexerAVS::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a6efb8e98287c21ec5175a466d7e5cc55"><div class="ttname"><a href="classQsciLexerSQL.html#a6efb8e98287c21ec5175a466d7e5cc55">QsciLexerSQL::setFoldComments</a></div><div class="ttdeci">virtual void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a67177e2b1d8584d8cf8f1b276174b258"><div class="ttname"><a href="classQsciScintilla.html#a67177e2b1d8584d8cf8f1b276174b258">QsciScintilla::setWhitespaceForegroundColor</a></div><div class="ttdeci">void setWhitespaceForegroundColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciLexerD_html_abb94e0b0257a50dbde9b0ddbcfeb69d2"><div class="ttname"><a href="classQsciLexerD.html#abb94e0b0257a50dbde9b0ddbcfeb69d2">QsciLexerD::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_a774cfde4ca55ef85c506258b3c789c9d"><div class="ttname"><a href="classQsciLexerPostScript.html#a774cfde4ca55ef85c506258b3c789c9d">QsciLexerPostScript::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a897792c74e365b70c4d2827419dc3ecf"><div class="ttname"><a href="classQsciScintilla.html#a897792c74e365b70c4d2827419dc3ecf">QsciScintilla::copyAvailable</a></div><div class="ttdeci">void copyAvailable(bool yes)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a5f105b9ec17cd73a0cd601ac9be82dd4"><div class="ttname"><a href="classQsciScintillaBase.html#a5f105b9ec17cd73a0cd601ac9be82dd4">QsciScintillaBase::fromMimeData</a></div><div class="ttdeci">virtual QByteArray fromMimeData(const QMimeData *source, bool &amp;rectangular) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a5ba2e241be76c209c0f5509804a995c9"><div class="ttname"><a href="classQsciScintilla.html#a5ba2e241be76c209c0f5509804a995c9">QsciScintilla::setIndicatorDrawUnder</a></div><div class="ttdeci">void setIndicatorDrawUnder(bool under, int indicatorNumber=-1)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_adc2560a55f77a14329db8409dba11c54"><div class="ttname"><a href="classQsciScintilla.html#adc2560a55f77a14329db8409dba11c54">QsciScintilla::findNext</a></div><div class="ttdeci">virtual bool findNext()</div></div>
<div class="ttc" id="aclassQsciCommandSet_html_a7f46a38f9fd309442aacfb7ad2b87143"><div class="ttname"><a href="classQsciCommandSet.html#a7f46a38f9fd309442aacfb7ad2b87143">QsciCommandSet::commands</a></div><div class="ttdeci">QList&lt; QsciCommand * &gt; &amp; commands()</div><div class="ttdoc">The commands in the set are returned as a list.</div><div class="ttdef"><b>Definition:</b> qscicommandset.h:62</div></div>
<div class="ttc" id="aclassQsciScintilla_html_afec7e71628d4af7a6573d1c74504ee20"><div class="ttname"><a href="classQsciScintilla.html#afec7e71628d4af7a6573d1c74504ee20">QsciScintilla::isUtf8</a></div><div class="ttdeci">bool isUtf8() const</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_a7cbe39118747739dd557df191c91db0c"><div class="ttname"><a href="classQsciLexerCSS.html#a7cbe39118747739dd557df191c91db0c">QsciLexerCSS::wordCharacters</a></div><div class="ttdeci">const char * wordCharacters() const</div><div class="ttdoc">Returns the string of characters that comprise a word.</div></div>
<div class="ttc" id="aclassQsciLexerYAML_html_ac263eb1fcaeaad44b23c2d990bad1bc1"><div class="ttname"><a href="classQsciLexerYAML.html#ac263eb1fcaeaad44b23c2d990bad1bc1">QsciLexerYAML::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a30d1fae97aaef0b3fafab8e790caf130"><div class="ttname"><a href="classQsciLexerPascal.html#a30d1fae97aaef0b3fafab8e790caf130">QsciLexerPascal::foldComments</a></div><div class="ttdeci">bool foldComments() const</div></div>
<div class="ttc" id="aclassQsciLexerPO_html_a17895e48d655d41d80e4fb4672c2fd72"><div class="ttname"><a href="classQsciLexerPO.html#a17895e48d655d41d80e4fb4672c2fd72">QsciLexerPO::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a7081c7ff25b5f6bd5b3a6cbd478a9f42"><div class="ttname"><a href="classQsciScintilla.html#a7081c7ff25b5f6bd5b3a6cbd478a9f42">QsciScintilla::WrapMode</a></div><div class="ttdeci">WrapMode</div><div class="ttdoc">This enum defines the different line wrap modes.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:478</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_a40d1ca24b672c13e9e7e69add2f5ee42"><div class="ttname"><a href="classQsciLexerVHDL.html#a40d1ca24b672c13e9e7e69add2f5ee42">QsciLexerVHDL::setFoldCompact</a></div><div class="ttdeci">virtual void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_ae6ef21c59fd374d1d4893adcc1e3ee9b"><div class="ttname"><a href="classQsciLexerHTML.html#ae6ef21c59fd374d1d4893adcc1e3ee9b">QsciLexerHTML::autoCompletionFillups</a></div><div class="ttdeci">const char * autoCompletionFillups() const</div><div class="ttdoc">Returns the characters that can fill up auto-completion.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a5178b3fd3cb946ffd4b2e52df9bb1483"><div class="ttname"><a href="classQsciScintilla.html#a5178b3fd3cb946ffd4b2e52df9bb1483">QsciScintilla::autoCompletionThreshold</a></div><div class="ttdeci">int autoCompletionThreshold() const</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:604</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a18c2bd1ee70c87809ba307ae6b695272"><div class="ttname"><a href="classQsciScintilla.html#a18c2bd1ee70c87809ba307ae6b695272">QsciScintilla::setMarginBackgroundColor</a></div><div class="ttdeci">void setMarginBackgroundColor(int margin, const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_ad8248a4659f290511d45a64fede1f63d"><div class="ttname"><a href="classQsciLexerHTML.html#ad8248a4659f290511d45a64fede1f63d">QsciLexerHTML::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8b593f92c03b3d6f999cd9d769c5028b"><div class="ttname"><a href="classQsciScintilla.html#a8b593f92c03b3d6f999cd9d769c5028b">QsciScintilla::wrapIndentMode</a></div><div class="ttdeci">WrapIndentMode wrapIndentMode() const</div></div>
<div class="ttc" id="aclassQsciPrinter_html_aae304336a4a8d4c2e332744ceeba1393"><div class="ttname"><a href="classQsciPrinter.html#aae304336a4a8d4c2e332744ceeba1393">QsciPrinter::printRange</a></div><div class="ttdeci">virtual int printRange(QsciScintillaBase *qsb, int from=-1, int to=-1)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214cacebb22ccb805fa137c65eda743d32e0a"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cacebb22ccb805fa137c65eda743d32e0a">QsciScintillaBase::SC_MARK_BOXMINUS</a></div><div class="ttdeci">@ SC_MARK_BOXMINUS</div><div class="ttdoc">A drawn minus sign in a box.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2608</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_a34fa0bd92884cfa29a27c279369797d5"><div class="ttname"><a href="classQsciLexerPOV.html#a34fa0bd92884cfa29a27c279369797d5">QsciLexerPOV::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214cacf6e7cca56bcd03b660b3590153d1075"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cacf6e7cca56bcd03b660b3590153d1075">QsciScintillaBase::SC_MARK_EMPTY</a></div><div class="ttdeci">@ SC_MARK_EMPTY</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2580</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_a490932b0c83bf7e4048c590565d6a32d"><div class="ttname"><a href="classQsciLexerBash.html#a490932b0c83bf7e4048c590565d6a32d">QsciLexerBash::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a6ec94de07f31c33a6b08c297259e6b01"><div class="ttname"><a href="classQsciLexerPascal.html#a6ec94de07f31c33a6b08c297259e6b01">QsciLexerPascal::~QsciLexerPascal</a></div><div class="ttdeci">virtual ~QsciLexerPascal()</div><div class="ttdoc">Destroys the QsciLexerPascal instance.</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html_a3f758c9ddd617ab1eb870d0eb20877d5"><div class="ttname"><a href="classQsciLexerAVS.html#a3f758c9ddd617ab1eb870d0eb20877d5">QsciLexerAVS::~QsciLexerAVS</a></div><div class="ttdeci">virtual ~QsciLexerAVS()</div><div class="ttdoc">Destroys the QsciLexerAVS instance.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a0e4576d83d7604b24bd13be91011c5f7"><div class="ttname"><a href="classQsciScintilla.html#a0e4576d83d7604b24bd13be91011c5f7">QsciScintilla::folding</a></div><div class="ttdeci">FoldStyle folding() const</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:860</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_a2a8cd041dea81adb54a869c17ee4c8ba"><div class="ttname"><a href="classQsciLexerVHDL.html#a2a8cd041dea81adb54a869c17ee4c8ba">QsciLexerVHDL::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ac4d1c67938c75806b2c139d0779d0478a2a3d02013e9e15f31413c12668824472"><div class="ttname"><a href="classQsciScintilla.html#ac4d1c67938c75806b2c139d0779d0478a2a3d02013e9e15f31413c12668824472">QsciScintilla::WrapFlagNone</a></div><div class="ttdeci">@ WrapFlagNone</div><div class="ttdoc">No wrap flag is displayed.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:495</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a616edbb9da241c08f8381835d65bb18c"><div class="ttname"><a href="classQsciScintilla.html#a616edbb9da241c08f8381835d65bb18c">QsciScintilla::setIndicatorHoverForegroundColor</a></div><div class="ttdeci">void setIndicatorHoverForegroundColor(const QColor &amp;col, int indicatorNumber=-1)</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_a6865962a5df72e37f4ba49c6e5e539b6"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#a6865962a5df72e37f4ba49c6e5e539b6">QsciLexerCoffeeScript::foldComments</a></div><div class="ttdeci">bool foldComments() const</div><div class="ttdef"><b>Definition:</b> qscilexercoffeescript.h:203</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_a9aa8e062b1eff3f6fb101cf51b507886"><div class="ttname"><a href="classQsciLexerTeX.html#a9aa8e062b1eff3f6fb101cf51b507886">QsciLexerTeX::processComments</a></div><div class="ttdeci">bool processComments() const</div><div class="ttdef"><b>Definition:</b> qscilexertex.h:122</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_a95a8b4cc2d6f8437c8feada8f518daff"><div class="ttname"><a href="classQsciLexerCSS.html#a95a8b4cc2d6f8437c8feada8f518daff">QsciLexerCSS::HSSLanguage</a></div><div class="ttdeci">bool HSSLanguage() const</div><div class="ttdef"><b>Definition:</b> qscilexercss.h:185</div></div>
<div class="ttc" id="aclassQsciLexer_html_addbc923c938f946180a15d494d17b567"><div class="ttname"><a href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">QsciLexer::setPaper</a></div><div class="ttdeci">virtual void setPaper(const QColor &amp;c, int style=-1)</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html"><div class="ttname"><a href="classQsciLexerProperties.html">QsciLexerProperties</a></div><div class="ttdoc">The QsciLexerProperties class encapsulates the Scintilla Properties lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerproperties.h:33</div></div>
<div class="ttc" id="aclassQsciLexerPython_html"><div class="ttname"><a href="classQsciLexerPython.html">QsciLexerPython</a></div><div class="ttdoc">The QsciLexerPython class encapsulates the Scintilla Python lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerpython.h:33</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_a98140e8245532496f7ed97bcaa8671f3"><div class="ttname"><a href="classQsciLexerCSS.html#a98140e8245532496f7ed97bcaa8671f3">QsciLexerCSS::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div><div class="ttdoc">Returns the foreground colour of the text for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_adc66ee4b78453d245ac1b4dff45490f4"><div class="ttname"><a href="classQsciLexerPython.html#adc66ee4b78453d245ac1b4dff45490f4">QsciLexerPython::blockStart</a></div><div class="ttdeci">const char * blockStart(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a47d0cdb5df5fa6c860b4e840184fe585"><div class="ttname"><a href="classQsciScintilla.html#a47d0cdb5df5fa6c860b4e840184fe585">QsciScintilla::autoCompletionFillupsEnabled</a></div><div class="ttdeci">bool autoCompletionFillupsEnabled() const</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a67308885b201ef6e21f0a273bf0b3c31"><div class="ttname"><a href="classQsciLexerPython.html#a67308885b201ef6e21f0a273bf0b3c31">QsciLexerPython::v3BytesAllowed</a></div><div class="ttdeci">bool v3BytesAllowed() const</div><div class="ttdef"><b>Definition:</b> qscilexerpython.h:270</div></div>
<div class="ttc" id="aclassQsciCommandSet_html_a44fd78a640f59309862d868d04f34e49"><div class="ttname"><a href="classQsciCommandSet.html#a44fd78a640f59309862d868d04f34e49">QsciCommandSet::readSettings</a></div><div class="ttdeci">bool readSettings(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_aea4d8707f6e32c1fbf989504d12d9eaa"><div class="ttname"><a href="classQsciLexerCPP.html#aea4d8707f6e32c1fbf989504d12d9eaa">QsciLexerCPP::highlightEscapeSequences</a></div><div class="ttdeci">bool highlightEscapeSequences() const</div><div class="ttdef"><b>Definition:</b> qscilexercpp.h:308</div></div>
<div class="ttc" id="aclassQsciLexerBatch_html_a57d4b4e77554476eea666d793f104540"><div class="ttname"><a href="classQsciLexerBatch.html#a57d4b4e77554476eea666d793f104540">QsciLexerBatch::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerXML_html_ab78937576c3c727f073921059ac87a59"><div class="ttname"><a href="classQsciLexerXML.html#ab78937576c3c727f073921059ac87a59">QsciLexerXML::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_a8ab227fcb9ba5da466b2d8eded96af70"><div class="ttname"><a href="classQsciLexerVHDL.html#a8ab227fcb9ba5da466b2d8eded96af70">QsciLexerVHDL::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerCustom_html_a5ba7f97b19cfa7bd0b846fc56d94fa3c"><div class="ttname"><a href="classQsciLexerCustom.html#a5ba7f97b19cfa7bd0b846fc56d94fa3c">QsciLexerCustom::setStyling</a></div><div class="ttdeci">void setStyling(int length, const QsciStyle &amp;style)</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_a638fcb2f0d2dd4be844881998cdb3b76"><div class="ttname"><a href="classQsciLexerHTML.html#a638fcb2f0d2dd4be844881998cdb3b76">QsciLexerHTML::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciAPIs_html_a742609f12e48e63edbab2565d7df3cb9"><div class="ttname"><a href="classQsciAPIs.html#a742609f12e48e63edbab2565d7df3cb9">QsciAPIs::savePrepared</a></div><div class="ttdeci">bool savePrepared(const QString &amp;filename=QString()) const</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a3ec3d302e4ad33ca360d3edbe14ac561"><div class="ttname"><a href="classQsciLexerPerl.html#a3ec3d302e4ad33ca360d3edbe14ac561">QsciLexerPerl::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_a9e61fa490e6e6c1480f3de5187ffed02"><div class="ttname"><a href="classQsciLexerCSS.html#a9e61fa490e6e6c1480f3de5187ffed02">QsciLexerCSS::setHSSLanguage</a></div><div class="ttdeci">void setHSSLanguage(bool enabled)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a391299d076b0164402118f504c83d09c"><div class="ttname"><a href="classQsciScintilla.html#a391299d076b0164402118f504c83d09c">QsciScintilla::setSelection</a></div><div class="ttdeci">virtual void setSelection(int lineFrom, int indexFrom, int lineTo, int indexTo)</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_a920953f5bde920bb22e853fc5aa6ef8d"><div class="ttname"><a href="classQsciLexerPOV.html#a920953f5bde920bb22e853fc5aa6ef8d">QsciLexerPOV::~QsciLexerPOV</a></div><div class="ttdeci">virtual ~QsciLexerPOV()</div><div class="ttdoc">Destroys the QsciLexerPOV instance.</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_aa230583b6c0a067894ffbd4ff3feee18"><div class="ttname"><a href="classQsciLexerTeX.html#aa230583b6c0a067894ffbd4ff3feee18">QsciLexerTeX::processIf</a></div><div class="ttdeci">bool processIf() const</div><div class="ttdef"><b>Definition:</b> qscilexertex.h:133</div></div>
<div class="ttc" id="aclassQsciLexerSpice_html"><div class="ttname"><a href="classQsciLexerSpice.html">QsciLexerSpice</a></div><div class="ttdoc">The QsciLexerSpice class encapsulates the Scintilla Spice lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerspice.h:32</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a1d73603ec19f317dd0d6271ec852c0fc"><div class="ttname"><a href="classQsciLexerSQL.html#a1d73603ec19f317dd0d6271ec852c0fc">QsciLexerSQL::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div><div class="ttdef"><b>Definition:</b> qscilexersql.h:195</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_aae249ec529d5f7de5fa238de9208058d"><div class="ttname"><a href="classQsciLexerCSS.html#aae249ec529d5f7de5fa238de9208058d">QsciLexerCSS::blockStart</a></div><div class="ttdeci">const char * blockStart(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciCommand_html_a76ed201e9e7309084795ddbc8f6e5b49"><div class="ttname"><a href="classQsciCommand.html#a76ed201e9e7309084795ddbc8f6e5b49">QsciCommand::description</a></div><div class="ttdeci">QString description() const</div><div class="ttdoc">The user friendly description of the command is returned.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ac04428d2f90c36458d68a673f107e40c"><div class="ttname"><a href="classQsciScintilla.html#ac04428d2f90c36458d68a673f107e40c">QsciScintilla::setWrapMode</a></div><div class="ttdeci">virtual void setWrapMode(WrapMode mode)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae628d46489efa3db3b0c42336a1bf8d3"><div class="ttname"><a href="classQsciScintilla.html#ae628d46489efa3db3b0c42336a1bf8d3">QsciScintilla::AutoCompletionUseSingle</a></div><div class="ttdeci">AutoCompletionUseSingle</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:99</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a08467ef528d3048db763979f42664496"><div class="ttname"><a href="classQsciScintilla.html#a08467ef528d3048db763979f42664496">QsciScintilla::MarkerSymbol</a></div><div class="ttdeci">MarkerSymbol</div><div class="ttdoc">This enum defines the different pre-defined marker symbols.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:353</div></div>
<div class="ttc" id="aclassQsciLexerDiff_html_a1554c4fce67cdb1d34e5e7e4268708c5"><div class="ttname"><a href="classQsciLexerDiff.html#a1554c4fce67cdb1d34e5e7e4268708c5">QsciLexerDiff::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div><div class="ttdoc">Returns the foreground colour of the text for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerCMake_html_a4578cacfbe802ab993fc07ddeaef3297"><div class="ttname"><a href="classQsciLexerCMake.html#a4578cacfbe802ab993fc07ddeaef3297">QsciLexerCMake::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_a5d074b15d624c82c5931ceba7a91a455"><div class="ttname"><a href="classQsciLexerPOV.html#a5d074b15d624c82c5931ceba7a91a455">QsciLexerPOV::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_aa1bd0effe3ed23e2bb3334b778efb74a"><div class="ttname"><a href="classQsciLexerVerilog.html#aa1bd0effe3ed23e2bb3334b778efb74a">QsciLexerVerilog::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a4cf0c0ab9cb0628c515910c67fab9950"><div class="ttname"><a href="classQsciLexerSQL.html#a4cf0c0ab9cb0628c515910c67fab9950">QsciLexerSQL::foldAtElse</a></div><div class="ttdeci">bool foldAtElse() const</div><div class="ttdef"><b>Definition:</b> qscilexersql.h:185</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a9208cc0aaf2e0a32239924fc6d0b67b7"><div class="ttname"><a href="classQsciScintillaBase.html#a9208cc0aaf2e0a32239924fc6d0b67b7">QsciScintillaBase::SCN_USERLISTSELECTION</a></div><div class="ttdeci">void SCN_USERLISTSELECTION(const char *selection, int id, int ch, int method)</div><div class="ttdoc">This is an overloaded member function, provided for convenience. It differs from the above function o...</div></div>
<div class="ttc" id="aclassQsciLexerMakefile_html"><div class="ttname"><a href="classQsciLexerMakefile.html">QsciLexerMakefile</a></div><div class="ttdoc">The QsciLexerMakefile class encapsulates the Scintilla Makefile lexer.</div><div class="ttdef"><b>Definition:</b> qscilexermakefile.h:33</div></div>
<div class="ttc" id="aclassQsciScintilla_html_af76ffea29540b830aebdf62a00a5e90d"><div class="ttname"><a href="classQsciScintilla.html#af76ffea29540b830aebdf62a00a5e90d">QsciScintilla::text</a></div><div class="ttdeci">QString text() const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a1ad82492f8015a60dea97f6ebd712d64"><div class="ttname"><a href="classQsciScintillaBase.html#a1ad82492f8015a60dea97f6ebd712d64">QsciScintillaBase::SCN_AUTOCSELECTION</a></div><div class="ttdeci">void SCN_AUTOCSELECTION(const char *selection, int position)</div><div class="ttdoc">This is an overloaded member function, provided for convenience. It differs from the above function o...</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a903187bfe219603ad7e20612d008ea7a"><div class="ttname"><a href="classQsciScintilla.html#a903187bfe219603ad7e20612d008ea7a">QsciScintilla::callTipsPosition</a></div><div class="ttdeci">CallTipsPosition callTipsPosition() const</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:643</div></div>
<div class="ttc" id="aclassQsciScintilla_html"><div class="ttname"><a href="classQsciScintilla.html">QsciScintilla</a></div><div class="ttdoc">The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:60</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_ac4b582db9efad2743e270ee63234804f"><div class="ttname"><a href="classQsciLexerJSON.html#ac4b582db9efad2743e270ee63234804f">QsciLexerJSON::setHighlightEscapeSequences</a></div><div class="ttdeci">void setHighlightEscapeSequences(bool highlight)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ad8fcb6e32235be34335e443230fb9000"><div class="ttname"><a href="classQsciScintilla.html#ad8fcb6e32235be34335e443230fb9000">QsciScintilla::contextMenuEvent</a></div><div class="ttdeci">virtual void contextMenuEvent(QContextMenuEvent *e)</div><div class="ttdoc">\reimp</div></div>
<div class="ttc" id="aclassQsciLexerOctave_html_aca3644f3fed3a83716d794cca822549a"><div class="ttname"><a href="classQsciLexerOctave.html#aca3644f3fed3a83716d794cca822549a">QsciLexerOctave::QsciLexerOctave</a></div><div class="ttdeci">QsciLexerOctave(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciMacro_html_a933a97fb937d67dbe86a4abe229c755f"><div class="ttname"><a href="classQsciMacro.html#a933a97fb937d67dbe86a4abe229c755f">QsciMacro::QsciMacro</a></div><div class="ttdeci">QsciMacro(const QString &amp;asc, QsciScintilla *parent)</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_aa45fd60cb7c2db5c88b5708f481dd6e2"><div class="ttname"><a href="classQsciLexerPOV.html#aa45fd60cb7c2db5c88b5708f481dd6e2">QsciLexerPOV::wordCharacters</a></div><div class="ttdeci">const char * wordCharacters() const</div><div class="ttdoc">Returns the string of characters that comprise a word.</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_a1796c98b07ec6cfc3d5953c225cc1f37"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#a1796c98b07ec6cfc3d5953c225cc1f37">QsciLexerCoffeeScript::blockEnd</a></div><div class="ttdeci">const char * blockEnd(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciLexer_html_a27728e4e361c5f4bf87690d34d83057d"><div class="ttname"><a href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">QsciLexer::readSettings</a></div><div class="ttdeci">bool readSettings(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ac466f32c3d7e51790b6b25c864783179ac66171d5698c13fb78053b1cccc4024a"><div class="ttname"><a href="classQsciScintilla.html#ac466f32c3d7e51790b6b25c864783179ac66171d5698c13fb78053b1cccc4024a">QsciScintilla::AcsNone</a></div><div class="ttdeci">@ AcsNone</div><div class="ttdoc">No sources are used, ie. automatic auto-completion is disabled.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:118</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a0db8c3ad0764a96f3ccf0fec71de0d26"><div class="ttname"><a href="classQsciScintillaBase.html#a0db8c3ad0764a96f3ccf0fec71de0d26">QsciScintillaBase::SCN_SAVEPOINTREACHED</a></div><div class="ttdeci">void SCN_SAVEPOINTREACHED()</div></div>
<div class="ttc" id="aclassQsciScintilla_html_af1651b676dd24c8768a90b829400a6cf"><div class="ttname"><a href="classQsciScintilla.html#af1651b676dd24c8768a90b829400a6cf">QsciScintilla::textHeight</a></div><div class="ttdeci">int textHeight(int linenr) const</div><div class="ttdoc">Returns the height in pixels of the text in line number linenr.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_af780380f9f1f2a66c729759b1d37ba69"><div class="ttname"><a href="classQsciScintilla.html#af780380f9f1f2a66c729759b1d37ba69">QsciScintilla::setCallTipsBackgroundColor</a></div><div class="ttdeci">void setCallTipsBackgroundColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_a71cf91642f6879964a061133013a1f51"><div class="ttname"><a href="classQsciLexerPOV.html#a71cf91642f6879964a061133013a1f51">QsciLexerPOV::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerOctave_html_ac0e8c0dfab0ae3c0b076c21d30fccc5f"><div class="ttname"><a href="classQsciLexerOctave.html#ac0e8c0dfab0ae3c0b076c21d30fccc5f">QsciLexerOctave::~QsciLexerOctave</a></div><div class="ttdeci">virtual ~QsciLexerOctave()</div><div class="ttdoc">Destroys the QsciLexerOctave instance.</div></div>
<div class="ttc" id="aclassQsciLexer_html_a7e5ab7f541d913760c32abedbdc72963"><div class="ttname"><a href="classQsciLexer.html#a7e5ab7f541d913760c32abedbdc72963">QsciLexer::defaultPaper</a></div><div class="ttdeci">virtual QColor defaultPaper(int style) const</div><div class="ttdoc">Returns the default paper colour for style number style.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a2ea74fb61f3d2d983d142a6ec8c3cc9d"><div class="ttname"><a href="classQsciScintillaBase.html#a2ea74fb61f3d2d983d142a6ec8c3cc9d">QsciScintillaBase::paintEvent</a></div><div class="ttdeci">virtual void paintEvent(QPaintEvent *e)</div><div class="ttdoc">Re-implemented to paint the viewport.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8e6ff4072224ba360403fe1ade1375ba"><div class="ttname"><a href="classQsciScintilla.html#a8e6ff4072224ba360403fe1ade1375ba">QsciScintilla::markerFindPrevious</a></div><div class="ttdeci">int markerFindPrevious(int linenr, unsigned mask) const</div></div>
<div class="ttc" id="aclassQsciLexerBatch_html_abf1b01e4ea47d78610f33a337245980b"><div class="ttname"><a href="classQsciLexerBatch.html#abf1b01e4ea47d78610f33a337245980b">QsciLexerBatch::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a7f9d93c22ed8b7b00996408da578cd2a"><div class="ttname"><a href="classQsciScintilla.html#a7f9d93c22ed8b7b00996408da578cd2a">QsciScintilla::setCallTipsPosition</a></div><div class="ttdeci">void setCallTipsPosition(CallTipsPosition position)</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_a0fc741a415b0419464afa66deb2b9e5d"><div class="ttname"><a href="classQsciLexerPostScript.html#a0fc741a415b0419464afa66deb2b9e5d">QsciLexerPostScript::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae478a896ae32a30e8a375049a3d477e0afacc823b3e29ee1611ede83d0c8fabbd"><div class="ttname"><a href="classQsciScintilla.html#ae478a896ae32a30e8a375049a3d477e0afacc823b3e29ee1611ede83d0c8fabbd">QsciScintilla::BoxedFoldStyle</a></div><div class="ttdeci">@ BoxedFoldStyle</div><div class="ttdoc">Boxed folding style using boxed plus and minus symbols.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:220</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a89e46329c110449b2e7334ccf623ceed"><div class="ttname"><a href="classQsciScintilla.html#a89e46329c110449b2e7334ccf623ceed">QsciScintilla::read</a></div><div class="ttdeci">bool read(QIODevice *io)</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_a02880268227d380ef25a72af2605ef0f"><div class="ttname"><a href="classQsciLexerPOV.html#a02880268227d380ef25a72af2605ef0f">QsciLexerPOV::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ab33ae790e43f4b856e44d202b9b3ce07"><div class="ttname"><a href="classQsciScintilla.html#ab33ae790e43f4b856e44d202b9b3ce07">QsciScintilla::zoomTo</a></div><div class="ttdeci">virtual void zoomTo(int size)</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_adc253f08156cde45b331c5a7ed07cfd7"><div class="ttname"><a href="classQsciLexerCPP.html#adc253f08156cde45b331c5a7ed07cfd7">QsciLexerCPP::foldComments</a></div><div class="ttdeci">bool foldComments() const</div><div class="ttdef"><b>Definition:</b> qscilexercpp.h:237</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a5eff383e6fa96cbbaba6a2558b076c0b"><div class="ttname"><a href="classQsciScintillaBase.html#a5eff383e6fa96cbbaba6a2558b076c0b">QsciScintillaBase::SCN_HOTSPOTCLICK</a></div><div class="ttdeci">void SCN_HOTSPOTCLICK(int position, int modifiers)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a48c91d4dd29c84bff4ee20962372ca23"><div class="ttname"><a href="classQsciScintilla.html#a48c91d4dd29c84bff4ee20962372ca23">QsciScintilla::resetUnmatchedBraceIndicator</a></div><div class="ttdeci">void resetUnmatchedBraceIndicator()</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ab7ed107d6ace096e9026c31145c48b41a2f3ac8cfede54b81db88e29b7f81e19c"><div class="ttname"><a href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41a2f3ac8cfede54b81db88e29b7f81e19c">QsciScintillaBase::SC_MARGIN_FORE</a></div><div class="ttdeci">@ SC_MARGIN_FORE</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2701</div></div>
<div class="ttc" id="aclassQsciLexerHex_html_af4b8ccf697854d78987f903c82874c33"><div class="ttname"><a href="classQsciLexerHex.html#af4b8ccf697854d78987f903c82874c33">QsciLexerHex::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div><div class="ttdoc">Returns the foreground colour of the text for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a64f021f45d10f2cfca72fda0c1d28e1f"><div class="ttname"><a href="classQsciLexerPascal.html#a64f021f45d10f2cfca72fda0c1d28e1f">QsciLexerPascal::setSmartHighlighting</a></div><div class="ttdeci">void setSmartHighlighting(bool enabled)</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_ab7715c2c90861e2601587b8a3a6732fd"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#ab7715c2c90861e2601587b8a3a6732fd">QsciLexerCoffeeScript::~QsciLexerCoffeeScript</a></div><div class="ttdeci">virtual ~QsciLexerCoffeeScript()</div><div class="ttdoc">Destroys the QsciLexerCoffeeScript instance.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a0b1f9dfc8a34bbee0c131eebeace9a06"><div class="ttname"><a href="classQsciScintilla.html#a0b1f9dfc8a34bbee0c131eebeace9a06">QsciScintilla::indentationWidth</a></div><div class="ttdeci">int indentationWidth() const</div></div>
<div class="ttc" id="aclassQsciLexerMatlab_html_a382aa49629299c9694d3b225bace0c16"><div class="ttname"><a href="classQsciLexerMatlab.html#a382aa49629299c9694d3b225bace0c16">QsciLexerMatlab::QsciLexerMatlab</a></div><div class="ttdeci">QsciLexerMatlab(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a4b6bdaf96ffaedeeaf7aa6d92b28913f"><div class="ttname"><a href="classQsciScintilla.html#a4b6bdaf96ffaedeeaf7aa6d92b28913f">QsciScintilla::setUnmatchedBraceIndicator</a></div><div class="ttdeci">void setUnmatchedBraceIndicator(int indicatorNumber)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a62d0174cb0a07e3f2d48fc0603192668ad8a963c1bf6418a78da554bfdb61efe2"><div class="ttname"><a href="classQsciScintilla.html#a62d0174cb0a07e3f2d48fc0603192668ad8a963c1bf6418a78da554bfdb61efe2">QsciScintilla::CallTipsNoAutoCompletionContext</a></div><div class="ttdeci">@ CallTipsNoAutoCompletionContext</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:169</div></div>
<div class="ttc" id="aclassQsciLexerJavaScript_html_aa7bd1f345699cc97fac25cf29ae98a4e"><div class="ttname"><a href="classQsciLexerJavaScript.html#aa7bd1f345699cc97fac25cf29ae98a4e">QsciLexerJavaScript::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a67c4a9da730c69a2b9fda0a1a02348f1"><div class="ttname"><a href="classQsciScintillaBase.html#a67c4a9da730c69a2b9fda0a1a02348f1">QsciScintillaBase::dragLeaveEvent</a></div><div class="ttdeci">virtual void dragLeaveEvent(QDragLeaveEvent *e)</div><div class="ttdoc">Re-implemented to handle drag leaves.</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html"><div class="ttname"><a href="classQsciLexerRuby.html">QsciLexerRuby</a></div><div class="ttdoc">The QsciLexerRuby class encapsulates the Scintilla Ruby lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerruby.h:32</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a79d8b2101ef7b1aef1e7e01557090d6f"><div class="ttname"><a href="classQsciLexerCPP.html#a79d8b2101ef7b1aef1e7e01557090d6f">QsciLexerCPP::blockStart</a></div><div class="ttdeci">const char * blockStart(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_ae33c3f0e337cfe173c61ea86c5cd3591"><div class="ttname"><a href="classQsciLexerPerl.html#ae33c3f0e337cfe173c61ea86c5cd3591">QsciLexerPerl::blockStart</a></div><div class="ttdeci">const char * blockStart(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a4a1068502802e494fb58ae50f6b6aedf"><div class="ttname"><a href="classQsciScintilla.html#a4a1068502802e494fb58ae50f6b6aedf">QsciScintilla::zoomIn</a></div><div class="ttdeci">virtual void zoomIn()</div></div>
<div class="ttc" id="aclassQsciLexerMakefile_html_ab7ecf66ac5e7285b72620df79bc9e711"><div class="ttname"><a href="classQsciLexerMakefile.html#ab7ecf66ac5e7285b72620df79bc9e711">QsciLexerMakefile::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a2dc2ffcd977cf514e65e315a80afcb18"><div class="ttname"><a href="classQsciLexerD.html#a2dc2ffcd977cf514e65e315a80afcb18">QsciLexerD::setFoldAtElse</a></div><div class="ttdeci">virtual void setFoldAtElse(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerFortran_html_a6e896d1a75f43c0e021326a29a07be67"><div class="ttname"><a href="classQsciLexerFortran.html#a6e896d1a75f43c0e021326a29a07be67">QsciLexerFortran::QsciLexerFortran</a></div><div class="ttdeci">QsciLexerFortran(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_a1897433fcacac32d0364f11acc56de97"><div class="ttname"><a href="classQsciLexerAsm.html#a1897433fcacac32d0364f11acc56de97">QsciLexerAsm::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8a8a5b9bcb9df18089b9fe2650e701db"><div class="ttname"><a href="classQsciScintilla.html#a8a8a5b9bcb9df18089b9fe2650e701db">QsciScintilla::lineAt</a></div><div class="ttdeci">int lineAt(const QPoint &amp;point) const</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html_acee212ef3dceca125cadb16ae9cc5fc3"><div class="ttname"><a href="classQsciLexerAVS.html#acee212ef3dceca125cadb16ae9cc5fc3">QsciLexerAVS::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_ad16788518def261f1ce55b35141642ad"><div class="ttname"><a href="classQsciLexerPOV.html#ad16788518def261f1ce55b35141642ad">QsciLexerPOV::foldDirectives</a></div><div class="ttdeci">bool foldDirectives() const</div></div>
<div class="ttc" id="aclassQsciLexerD_html"><div class="ttname"><a href="classQsciLexerD.html">QsciLexerD</a></div><div class="ttdoc">The QsciLexerD class encapsulates the Scintilla D lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerd.h:32</div></div>
<div class="ttc" id="aclassQsciLexer_html_aace68e3dbcef9da1b031fb9cfd843c57"><div class="ttname"><a href="classQsciLexer.html#aace68e3dbcef9da1b031fb9cfd843c57">QsciLexer::wordCharacters</a></div><div class="ttdeci">virtual const char * wordCharacters() const</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_aaf1f8163b8baf27ef65c1e5219bbf1e2"><div class="ttname"><a href="classQsciLexerCPP.html#aaf1f8163b8baf27ef65c1e5219bbf1e2">QsciLexerCPP::setFoldComments</a></div><div class="ttdeci">virtual void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_a2f54e561f646da5ff20c5e85b2f377ea"><div class="ttname"><a href="classQsciLexerLua.html#a2f54e561f646da5ff20c5e85b2f377ea">QsciLexerLua::setFoldCompact</a></div><div class="ttdeci">virtual void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a1719fba80d9e60cf9fce1bb75f304568"><div class="ttname"><a href="classQsciScintillaBase.html#a1719fba80d9e60cf9fce1bb75f304568">QsciScintillaBase::SCN_AUTOCCANCELLED</a></div><div class="ttdeci">void SCN_AUTOCCANCELLED()</div></div>
<div class="ttc" id="aclassQsciLexerCustom_html_addc357462c04f032e20149b55cb8aeaa"><div class="ttname"><a href="classQsciLexerCustom.html#addc357462c04f032e20149b55cb8aeaa">QsciLexerCustom::styleBitsNeeded</a></div><div class="ttdeci">virtual int styleBitsNeeded() const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a2c339fd90e92408440230ee9d84cabcf"><div class="ttname"><a href="classQsciScintillaBase.html#a2c339fd90e92408440230ee9d84cabcf">QsciScintillaBase::focusInEvent</a></div><div class="ttdeci">virtual void focusInEvent(QFocusEvent *e)</div><div class="ttdoc">Re-implemented to tell Scintilla it has the focus.</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_a9a4ec081c6812ffb3ebc5082c08bf0db"><div class="ttname"><a href="classQsciLexerPOV.html#a9a4ec081c6812ffb3ebc5082c08bf0db">QsciLexerPOV::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div></div>
<div class="ttc" id="aclassQsciLexerTekHex_html_adaa40d43e3df5109be724ee443cbc749"><div class="ttname"><a href="classQsciLexerTekHex.html#adaa40d43e3df5109be724ee443cbc749">QsciLexerTekHex::~QsciLexerTekHex</a></div><div class="ttdeci">virtual ~QsciLexerTekHex()</div><div class="ttdoc">Destroys the QsciLexerTekHex instance.</div></div>
<div class="ttc" id="aclassQsciLexerFortran_html_a56e0fd6b5d719677050a28ad0d5ae927"><div class="ttname"><a href="classQsciLexerFortran.html#a56e0fd6b5d719677050a28ad0d5ae927">QsciLexerFortran::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a7677d42ce77cb5a150dc961d12da92e1"><div class="ttname"><a href="classQsciScintilla.html#a7677d42ce77cb5a150dc961d12da92e1">QsciScintilla::setMarginText</a></div><div class="ttdeci">void setMarginText(int line, const QString &amp;text, int style)</div></div>
<div class="ttc" id="aclassQsciLexerPO_html_aed4ceee51f10a94071a14371295b4c95"><div class="ttname"><a href="classQsciLexerPO.html#aed4ceee51f10a94071a14371295b4c95">QsciLexerPO::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca6b210dd7ba9ae1b8c503965b6e9ada9a"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca6b210dd7ba9ae1b8c503965b6e9ada9a">QsciScintillaBase::SC_MARK_BOXPLUS</a></div><div class="ttdeci">@ SC_MARK_BOXPLUS</div><div class="ttdoc">A drawn plus sign in a box.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2602</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a8225643b25dc6f1dedc48b4a7af4b83d"><div class="ttname"><a href="classQsciScintillaBase.html#a8225643b25dc6f1dedc48b4a7af4b83d">QsciScintillaBase::SCN_USERLISTSELECTION</a></div><div class="ttdeci">void SCN_USERLISTSELECTION(const char *selection, int id, int ch, int method, int position)</div></div>
<div class="ttc" id="aclassQsciStyledText_html_a6a5f837ca80d54322b70aa4b8465afa1"><div class="ttname"><a href="classQsciStyledText.html#a6a5f837ca80d54322b70aa4b8465afa1">QsciStyledText::style</a></div><div class="ttdeci">int style() const</div><div class="ttdoc">Returns the number of the style.</div></div>
<div class="ttc" id="aclassQsciLexerCSharp_html_abf77dec5e71fbcd67c5cf8772b59004a"><div class="ttname"><a href="classQsciLexerCSharp.html#abf77dec5e71fbcd67c5cf8772b59004a">QsciLexerCSharp::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a07cb228e7f39e5a97271126df9ba9ef2"><div class="ttname"><a href="classQsciScintilla.html#a07cb228e7f39e5a97271126df9ba9ef2">QsciScintilla::marginOptions</a></div><div class="ttdeci">int marginOptions() const</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_acbaa96d72ad071768acc25d7d56b6324"><div class="ttname"><a href="classQsciLexerRuby.html#acbaa96d72ad071768acc25d7d56b6324">QsciLexerRuby::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div><div class="ttdef"><b>Definition:</b> qscilexerruby.h:214</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_ac79b616c3ba0872856d90b119bfd81b8"><div class="ttname"><a href="classQsciLexerVerilog.html#ac79b616c3ba0872856d90b119bfd81b8">QsciLexerVerilog::setFoldComments</a></div><div class="ttdeci">void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a5887a36e4a8d6ff54f4c796b33bc2eef"><div class="ttname"><a href="classQsciLexerPython.html#a5887a36e4a8d6ff54f4c796b33bc2eef">QsciLexerPython::setStringsOverNewlineAllowed</a></div><div class="ttdeci">void setStringsOverNewlineAllowed(bool allowed)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a978b6679ccd9d9edb6091502001a5f45"><div class="ttname"><a href="classQsciScintilla.html#a978b6679ccd9d9edb6091502001a5f45">QsciScintilla::QsciScintilla</a></div><div class="ttdeci">QsciScintilla(QWidget *parent=0)</div><div class="ttdoc">Construct an empty QsciScintilla with parent parent.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_aacfd4923492644933902b278efea1787"><div class="ttname"><a href="classQsciScintillaBase.html#aacfd4923492644933902b278efea1787">QsciScintillaBase::SendScintilla</a></div><div class="ttdeci">long SendScintilla(unsigned int msg, unsigned long wParam, QPainter *hdc, const QRect &amp;rc, long cpMin, long cpMax) const</div><div class="ttdoc">This is an overloaded member function, provided for convenience. It differs from the above function o...</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a302b9b881fdc5dca82c5dea5fca5cd3e"><div class="ttname"><a href="classQsciLexerSQL.html#a302b9b881fdc5dca82c5dea5fca5cd3e">QsciLexerSQL::setFoldCompact</a></div><div class="ttdeci">virtual void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_a7a08d9dcb4935d7e2c99696bdcfd8e7a"><div class="ttname"><a href="classQsciLexerPOV.html#a7a08d9dcb4935d7e2c99696bdcfd8e7a">QsciLexerPOV::foldComments</a></div><div class="ttdeci">bool foldComments() const</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_a0d08b395dcfd9e5152e8b8f03c26e9c5"><div class="ttname"><a href="classQsciLexerAsm.html#a0d08b395dcfd9e5152e8b8f03c26e9c5">QsciLexerAsm::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerMatlab_html"><div class="ttname"><a href="classQsciLexerMatlab.html">QsciLexerMatlab</a></div><div class="ttdoc">The QsciLexerMatlab class encapsulates the Scintilla Matlab file lexer.</div><div class="ttdef"><b>Definition:</b> qscilexermatlab.h:33</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a747feb07236c1beccadd446562b53b84"><div class="ttname"><a href="classQsciScintillaBase.html#a747feb07236c1beccadd446562b53b84">QsciScintillaBase::QSCN_SELCHANGED</a></div><div class="ttdeci">void QSCN_SELCHANGED(bool yes)</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_a7bfdaea964c9e2c51568f63f379b6108"><div class="ttname"><a href="classQsciLexerCSS.html#a7bfdaea964c9e2c51568f63f379b6108">QsciLexerCSS::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aeadb693affb5816e24d28432d8dc240f"><div class="ttname"><a href="classQsciScintilla.html#aeadb693affb5816e24d28432d8dc240f">QsciScintilla::clearMarginText</a></div><div class="ttdeci">void clearMarginText(int line=-1)</div></div>
<div class="ttc" id="aclassQsciLexerYAML_html_a90fcdb3295720e3bdcf3d04dae4ae0c9"><div class="ttname"><a href="classQsciLexerYAML.html#a90fcdb3295720e3bdcf3d04dae4ae0c9">QsciLexerYAML::~QsciLexerYAML</a></div><div class="ttdeci">virtual ~QsciLexerYAML()</div><div class="ttdoc">Destroys the QsciLexerYAML instance.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_abeaabcd8076d1f47e8a847006451ce36"><div class="ttname"><a href="classQsciScintilla.html#abeaabcd8076d1f47e8a847006451ce36">QsciScintilla::wordAtPoint</a></div><div class="ttdeci">QString wordAtPoint(const QPoint &amp;point) const</div><div class="ttdoc">Returns the word at the point pixel coordinates.</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_a8124ec8b5b96d95bb225cbb4e95f55cb"><div class="ttname"><a href="classQsciLexerLua.html#a8124ec8b5b96d95bb225cbb4e95f55cb">QsciLexerLua::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a97fd48c290c961342f09149d8996c053"><div class="ttname"><a href="classQsciScintilla.html#a97fd48c290c961342f09149d8996c053">QsciScintilla::markerDefine</a></div><div class="ttdeci">int markerDefine(char ch, int markerNumber=-1)</div></div>
<div class="ttc" id="aclassQsciLexerMatlab_html_afd80aca910d70abcf6bcb34bfad4afc0"><div class="ttname"><a href="classQsciLexerMatlab.html#afd80aca910d70abcf6bcb34bfad4afc0">QsciLexerMatlab::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerNASM_html_a41bba5ae9f9e264acf1a4e9ef1e443f6"><div class="ttname"><a href="classQsciLexerNASM.html#a41bba5ae9f9e264acf1a4e9ef1e443f6">QsciLexerNASM::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_adb5bad7d1dad9ab3fe74adb3e0812969"><div class="ttname"><a href="classQsciScintillaBase.html#adb5bad7d1dad9ab3fe74adb3e0812969">QsciScintillaBase::SCN_MODIFYATTEMPTRO</a></div><div class="ttdeci">void SCN_MODIFYATTEMPTRO()</div></div>
<div class="ttc" id="aclassQsciStyle_html_aa480e57bbdd83b8164129f875bd48976"><div class="ttname"><a href="classQsciStyle.html#aa480e57bbdd83b8164129f875bd48976">QsciStyle::refresh</a></div><div class="ttdeci">void refresh()</div><div class="ttdoc">Refresh the style settings.</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_a2097c0d473f379dbcd0faa9653bcc943"><div class="ttname"><a href="classQsciLexerTeX.html#a2097c0d473f379dbcd0faa9653bcc943">QsciLexerTeX::setFoldComments</a></div><div class="ttdeci">void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerNASM_html_a314bdc56c9b06fe0a99b484b142c541e"><div class="ttname"><a href="classQsciLexerNASM.html#a314bdc56c9b06fe0a99b484b142c541e">QsciLexerNASM::lexer</a></div><div class="ttdeci">const char * lexer() const</div><div class="ttdoc">Returns the name of the lexer.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aa869897ad955e8a42c5568be590c529b"><div class="ttname"><a href="classQsciScintilla.html#aa869897ad955e8a42c5568be590c529b">QsciScintilla::braceMatching</a></div><div class="ttdeci">BraceMatch braceMatching() const</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:632</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aaeb4a9e6d4e2822524c84da5318a7f1e"><div class="ttname"><a href="classQsciScintilla.html#aaeb4a9e6d4e2822524c84da5318a7f1e">QsciScintilla::autoCompletionUseSingle</a></div><div class="ttdeci">AutoCompletionUseSingle autoCompletionUseSingle() const</div></div>
<div class="ttc" id="aclassQsciLexerCMake_html"><div class="ttname"><a href="classQsciLexerCMake.html">QsciLexerCMake</a></div><div class="ttdoc">The QsciLexerCMake class encapsulates the Scintilla CMake lexer.</div><div class="ttdef"><b>Definition:</b> qscilexercmake.h:32</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a986f1235405f51f9d5b2edda17423563"><div class="ttname"><a href="classQsciScintilla.html#a986f1235405f51f9d5b2edda17423563">QsciScintilla::selectToMatchingBrace</a></div><div class="ttdeci">virtual void selectToMatchingBrace()</div></div>
<div class="ttc" id="aclassQsciDocument_html_a3da32a3198c407aa692764ccd98ad66f"><div class="ttname"><a href="classQsciDocument.html#a3da32a3198c407aa692764ccd98ad66f">QsciDocument::QsciDocument</a></div><div class="ttdeci">QsciDocument()</div><div class="ttdoc">Create a new unattached document.</div></div>
<div class="ttc" id="aclassQsciLexerD_html_af9f73f93dd57019e3335011528ad6aed"><div class="ttname"><a href="classQsciLexerD.html#af9f73f93dd57019e3335011528ad6aed">QsciLexerD::blockEnd</a></div><div class="ttdeci">const char * blockEnd(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a1a7acaa356fdbefd26cfe0f30264c43a"><div class="ttname"><a href="classQsciLexerPython.html#a1a7acaa356fdbefd26cfe0f30264c43a">QsciLexerPython::foldComments</a></div><div class="ttdeci">bool foldComments() const</div><div class="ttdef"><b>Definition:</b> qscilexerpython.h:188</div></div>
<div class="ttc" id="aclassQsciLexerCustom_html_a3a6b7f17d604db4e06e0d469b55b7602"><div class="ttname"><a href="classQsciLexerCustom.html#a3a6b7f17d604db4e06e0d469b55b7602">QsciLexerCustom::~QsciLexerCustom</a></div><div class="ttdeci">virtual ~QsciLexerCustom()</div><div class="ttdoc">Destroy the QSciLexerCustom.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae64151db464b22eedd012225f82c810d"><div class="ttname"><a href="classQsciScintilla.html#ae64151db464b22eedd012225f82c810d">QsciScintilla::setCallTipsHighlightColor</a></div><div class="ttdeci">void setCallTipsHighlightColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_ae15b25b5d6705a850f6c93ee1013bea7"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#ae15b25b5d6705a850f6c93ee1013bea7">QsciLexerCoffeeScript::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_ae7a6d23e6e8748210198b4fee3932144"><div class="ttname"><a href="classQsciLexerRuby.html#ae7a6d23e6e8748210198b4fee3932144">QsciLexerRuby::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a158b80fd7ee649cbb618b1df33491bab"><div class="ttname"><a href="classQsciLexerPython.html#a158b80fd7ee649cbb618b1df33491bab">QsciLexerPython::QsciLexerPython</a></div><div class="ttdeci">QsciLexerPython(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a92cb96a2f9d373ed5a91546c42ec0905"><div class="ttname"><a href="classQsciLexerPascal.html#a92cb96a2f9d373ed5a91546c42ec0905">QsciLexerPascal::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8386414f7c04968c1642ecdd47b281df"><div class="ttname"><a href="classQsciScintilla.html#a8386414f7c04968c1642ecdd47b281df">QsciScintilla::isRedoAvailable</a></div><div class="ttdeci">bool isRedoAvailable() const</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_a2a1cd44b041d1d4f4c11c22f91de99c3"><div class="ttname"><a href="classQsciLexerCSS.html#a2a1cd44b041d1d4f4c11c22f91de99c3">QsciLexerCSS::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a0a5656ec94ad1b31e3acc6ba86ebf8c4"><div class="ttname"><a href="classQsciScintilla.html#a0a5656ec94ad1b31e3acc6ba86ebf8c4">QsciScintilla::cut</a></div><div class="ttdeci">virtual void cut()</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_ad94fbbd156020166afddb8a0a55eba6f"><div class="ttname"><a href="classQsciLexerTeX.html#ad94fbbd156020166afddb8a0a55eba6f">QsciLexerTeX::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_aad8c778b4c9ef2014e5a508f0ee52021"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#aad8c778b4c9ef2014e5a508f0ee52021">QsciLexerCoffeeScript::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ad6557ee0ca58413e8d3e76b942f25a7f"><div class="ttname"><a href="classQsciScintilla.html#ad6557ee0ca58413e8d3e76b942f25a7f">QsciScintilla::foldLine</a></div><div class="ttdeci">virtual void foldLine(int line)</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_aaf885bb9d07591114c2a2cc5769bb02a"><div class="ttname"><a href="classQsciLexerHTML.html#aaf885bb9d07591114c2a2cc5769bb02a">QsciLexerHTML::foldScriptComments</a></div><div class="ttdeci">bool foldScriptComments() const</div><div class="ttdef"><b>Definition:</b> qscilexerhtml.h:456</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_a5017022e35efd5f1c9825d63e4336e73"><div class="ttname"><a href="classQsciLexerBash.html#a5017022e35efd5f1c9825d63e4336e73">QsciLexerBash::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciAPIs_html_af42a26a050bfeb4249d35ab61567ea9e"><div class="ttname"><a href="classQsciAPIs.html#af42a26a050bfeb4249d35ab61567ea9e">QsciAPIs::loadPrepared</a></div><div class="ttdeci">bool loadPrepared(const QString &amp;filename=QString())</div></div>
<div class="ttc" id="aclassQsciLexerPO_html_a8403f1e2f5ea0c5d67c32dd6053317c5"><div class="ttname"><a href="classQsciLexerPO.html#a8403f1e2f5ea0c5d67c32dd6053317c5">QsciLexerPO::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a4bca4c8ca2d5a426aabac915f8d5f0f3"><div class="ttname"><a href="classQsciScintilla.html#a4bca4c8ca2d5a426aabac915f8d5f0f3">QsciScintilla::tabWidth</a></div><div class="ttdeci">int tabWidth() const</div></div>
<div class="ttc" id="aclassQsciLexerDiff_html_af1c70ba1dfa8ccf5fe2106069041cd5e"><div class="ttname"><a href="classQsciLexerDiff.html#af1c70ba1dfa8ccf5fe2106069041cd5e">QsciLexerDiff::QsciLexerDiff</a></div><div class="ttdeci">QsciLexerDiff(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexer_html_af7508f1b816a2c9446d36141edc9b5ce"><div class="ttname"><a href="classQsciLexer.html#af7508f1b816a2c9446d36141edc9b5ce">QsciLexer::defaultColor</a></div><div class="ttdeci">virtual QColor defaultColor(int style) const</div><div class="ttdoc">Returns the default text colour for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_abf1a8dc25c7bd5d272c119d3c3e9e369"><div class="ttname"><a href="classQsciLexerVHDL.html#abf1a8dc25c7bd5d272c119d3c3e9e369">QsciLexerVHDL::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a0c7c970d275176f678a86e46f5e2d07d"><div class="ttname"><a href="classQsciScintilla.html#a0c7c970d275176f678a86e46f5e2d07d">QsciScintilla::zoomOut</a></div><div class="ttdeci">virtual void zoomOut(int range)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_abf85680f914ee631aa3a513ba823271f"><div class="ttname"><a href="classQsciScintilla.html#abf85680f914ee631aa3a513ba823271f">QsciScintilla::setMatchedBraceBackgroundColor</a></div><div class="ttdeci">void setMatchedBraceBackgroundColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciLexerMatlab_html_ae43cc6f38a157e4c70ba460e5004615e"><div class="ttname"><a href="classQsciLexerMatlab.html#ae43cc6f38a157e4c70ba460e5004615e">QsciLexerMatlab::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a62d0174cb0a07e3f2d48fc0603192668a9c4767863f6ddd8b4e8ca381091ed497"><div class="ttname"><a href="classQsciScintilla.html#a62d0174cb0a07e3f2d48fc0603192668a9c4767863f6ddd8b4e8ca381091ed497">QsciScintilla::CallTipsNoContext</a></div><div class="ttdeci">@ CallTipsNoContext</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:163</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae478a896ae32a30e8a375049a3d477e0abd0dcc4e3cbdb15d7ce2076c3f2f1c1c"><div class="ttname"><a href="classQsciScintilla.html#ae478a896ae32a30e8a375049a3d477e0abd0dcc4e3cbdb15d7ce2076c3f2f1c1c">QsciScintilla::CircledTreeFoldStyle</a></div><div class="ttdeci">@ CircledTreeFoldStyle</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:224</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_ab64e768ab8e7af6af93ce95db074c90a"><div class="ttname"><a href="classQsciLexerSQL.html#ab64e768ab8e7af6af93ce95db074c90a">QsciLexerSQL::setBackslashEscapes</a></div><div class="ttdeci">virtual void setBackslashEscapes(bool enable)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_adacd79ec5e25430f0cbff34e6584afe7"><div class="ttname"><a href="classQsciScintilla.html#adacd79ec5e25430f0cbff34e6584afe7">QsciScintilla::annotate</a></div><div class="ttdeci">void annotate(int line, const QString &amp;text, int style)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a7e36f3595e0d89910b9322dc5295152d"><div class="ttname"><a href="classQsciScintilla.html#a7e36f3595e0d89910b9322dc5295152d">QsciScintilla::setSelectionToEol</a></div><div class="ttdeci">void setSelectionToEol(bool filled)</div></div>
<div class="ttc" id="aclassQsciLexerIDL_html_accd209bc74cec365745e3987c478a556"><div class="ttname"><a href="classQsciLexerIDL.html#accd209bc74cec365745e3987c478a556">QsciLexerIDL::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_a16a2faffdfcc5893a6fd08d270d69798"><div class="ttname"><a href="classQsciLexerJSON.html#a16a2faffdfcc5893a6fd08d270d69798">QsciLexerJSON::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div><div class="ttdef"><b>Definition:</b> qscilexerjson.h:156</div></div>
<div class="ttc" id="aclassQsciLexerFortran77_html_a855940eae63985a7ff92ffd545d88bd4"><div class="ttname"><a href="classQsciLexerFortran77.html#a855940eae63985a7ff92ffd545d88bd4">QsciLexerFortran77::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_ab9babc165bacf53b73abfb2d5d1aadad"><div class="ttname"><a href="classQsciLexerCPP.html#ab9babc165bacf53b73abfb2d5d1aadad">QsciLexerCPP::~QsciLexerCPP</a></div><div class="ttdeci">virtual ~QsciLexerCPP()</div><div class="ttdoc">Destroys the QsciLexerCPP instance.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a3ad17d87cb436e0f6da52a68cd14750e"><div class="ttname"><a href="classQsciScintilla.html#a3ad17d87cb436e0f6da52a68cd14750e">QsciScintilla::clearAnnotations</a></div><div class="ttdeci">void clearAnnotations(int line=-1)</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_ade07472f3cc8a4cccbb0bb6b964f0356"><div class="ttname"><a href="classQsciLexerPython.html#ade07472f3cc8a4cccbb0bb6b964f0356">QsciLexerPython::setHighlightSubidentifiers</a></div><div class="ttdeci">void setHighlightSubidentifiers(bool enabled)</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_a7b84f78b170cec259efb2f367c54ce4b"><div class="ttname"><a href="classQsciLexerVerilog.html#a7b84f78b170cec259efb2f367c54ce4b">QsciLexerVerilog::setFoldAtElse</a></div><div class="ttdeci">void setFoldAtElse(bool fold)</div></div>
<div class="ttc" id="aclassQsciStyledText_html"><div class="ttname"><a href="classQsciStyledText.html">QsciStyledText</a></div><div class="ttdoc">The QsciStyledText class is a container for a piece of text and the style used to display the text.</div><div class="ttdef"><b>Definition:</b> qscistyledtext.h:36</div></div>
<div class="ttc" id="aclassQsciLexerFortran_html_a83d7d8209efca06d10870607c9db3c72"><div class="ttname"><a href="classQsciLexerFortran.html#a83d7d8209efca06d10870607c9db3c72">QsciLexerFortran::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexer_html_a32b16ee95c3dabbc7de61541dd110521"><div class="ttname"><a href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">QsciLexer::setDefaultColor</a></div><div class="ttdeci">void setDefaultColor(const QColor &amp;c)</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a2263531e4445463f1d75fdfd54102404"><div class="ttname"><a href="classQsciLexerCPP.html#a2263531e4445463f1d75fdfd54102404">QsciLexerCPP::blockEnd</a></div><div class="ttdeci">const char * blockEnd(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a4bffe1a63e3484da908340274f7a7607"><div class="ttname"><a href="classQsciScintilla.html#a4bffe1a63e3484da908340274f7a7607">QsciScintilla::marginLineNumbers</a></div><div class="ttdeci">bool marginLineNumbers(int margin) const</div></div>
<div class="ttc" id="aclassQsciLexerBatch_html_aab3e54ed46006daeb2dbdcea3f64192d"><div class="ttname"><a href="classQsciLexerBatch.html#aab3e54ed46006daeb2dbdcea3f64192d">QsciLexerBatch::caseSensitive</a></div><div class="ttdeci">bool caseSensitive() const</div><div class="ttdoc">Returns true if the language is case sensitive. The default is true.</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a1a5b06231766e0f9a7364606a991c879"><div class="ttname"><a href="classQsciLexerPascal.html#a1a5b06231766e0f9a7364606a991c879">QsciLexerPascal::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerPO_html_a1ec44d2dfe2f40d6267f3067f66c9a3d"><div class="ttname"><a href="classQsciLexerPO.html#a1ec44d2dfe2f40d6267f3067f66c9a3d">QsciLexerPO::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div></div>
<div class="ttc" id="aclassQsciLexer_html_a619ee93cb512755e3f946fe61ee097de"><div class="ttname"><a href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">QsciLexer::writeSettings</a></div><div class="ttdeci">bool writeSettings(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_a88492153c713084f4b5495ebe3bf1b40"><div class="ttname"><a href="classQsciLexerPostScript.html#a88492153c713084f4b5495ebe3bf1b40">QsciLexerPostScript::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca4f29b2c933a525fe0a80f0a58ba7eb61"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca4f29b2c933a525fe0a80f0a58ba7eb61">QsciScintillaBase::SC_MARK_FULLRECT</a></div><div class="ttdeci">@ SC_MARK_FULLRECT</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2648</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca08a00107e2110dce658fe7cb10f75e58"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca08a00107e2110dce658fe7cb10f75e58">QsciScintillaBase::SC_MARK_LEFTRECT</a></div><div class="ttdeci">@ SC_MARK_LEFTRECT</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2652</div></div>
<div class="ttc" id="aclassQsciLexerPO_html_afebea3d6f2a2cffcb8be859c99c2cede"><div class="ttname"><a href="classQsciLexerPO.html#afebea3d6f2a2cffcb8be859c99c2cede">QsciLexerPO::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a689eed3f6219e20924bcffdb95f27526"><div class="ttname"><a href="classQsciScintilla.html#a689eed3f6219e20924bcffdb95f27526">QsciScintilla::firstVisibleLine</a></div><div class="ttdeci">int firstVisibleLine() const</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a2cfcfea76c396c0b7b82fc41437ff16f"><div class="ttname"><a href="classQsciLexerCPP.html#a2cfcfea76c396c0b7b82fc41437ff16f">QsciLexerCPP::blockStartKeyword</a></div><div class="ttdeci">const char * blockStartKeyword(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciLexerTekHex_html_ad9666a08ae0b9143a1d3f155ce563819"><div class="ttname"><a href="classQsciLexerTekHex.html#ad9666a08ae0b9143a1d3f155ce563819">QsciLexerTekHex::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_aabf79a666eb40a912dfb7136d79f80e6"><div class="ttname"><a href="classQsciLexerRuby.html#aabf79a666eb40a912dfb7136d79f80e6">QsciLexerRuby::blockEnd</a></div><div class="ttdeci">const char * blockEnd(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciAPIs_html_ab0f824492bb0f3ca54edb4d46945a3de"><div class="ttname"><a href="classQsciAPIs.html#ab0f824492bb0f3ca54edb4d46945a3de">QsciAPIs::updateAutoCompletionList</a></div><div class="ttdeci">virtual void updateAutoCompletionList(const QStringList &amp;context, QStringList &amp;list)</div><div class="ttdoc">\reimp</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a2ea8bd8758e10d72832dbf3642b06fb2"><div class="ttname"><a href="classQsciLexerCPP.html#a2ea8bd8758e10d72832dbf3642b06fb2">QsciLexerCPP::setHighlightTripleQuotedStrings</a></div><div class="ttdeci">void setHighlightTripleQuotedStrings(bool enabled)</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_af8d88cce706a1d7a95e1a519e0dc56c3"><div class="ttname"><a href="classQsciLexerJSON.html#af8d88cce706a1d7a95e1a519e0dc56c3">QsciLexerJSON::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a44d1c322098eb0cf44cf78e866ed80cb"><div class="ttname"><a href="classQsciScintilla.html#a44d1c322098eb0cf44cf78e866ed80cb">QsciScintilla::fillIndicatorRange</a></div><div class="ttdeci">void fillIndicatorRange(int lineFrom, int indexFrom, int lineTo, int indexTo, int indicatorNumber)</div></div>
<div class="ttc" id="aclassQsciLexerTCL_html"><div class="ttname"><a href="classQsciLexerTCL.html">QsciLexerTCL</a></div><div class="ttdoc">The QsciLexerTCL class encapsulates the Scintilla TCL lexer.</div><div class="ttdef"><b>Definition:</b> qscilexertcl.h:32</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_ac0b0eb74510dd3af8eed933d3e37e2ab"><div class="ttname"><a href="classQsciLexerBash.html#ac0b0eb74510dd3af8eed933d3e37e2ab">QsciLexerBash::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a13721a9f909b67bca298f51ea34a01db"><div class="ttname"><a href="classQsciScintilla.html#a13721a9f909b67bca298f51ea34a01db">QsciScintilla::clearIndicatorRange</a></div><div class="ttdeci">void clearIndicatorRange(int lineFrom, int indexFrom, int lineTo, int indexTo, int indicatorNumber)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a33a692fb0d5781ec40080a361859fd66"><div class="ttname"><a href="classQsciScintilla.html#a33a692fb0d5781ec40080a361859fd66">QsciScintilla::callTip</a></div><div class="ttdeci">virtual void callTip()</div></div>
<div class="ttc" id="aclassQsciLexerFortran77_html_a6ba40887a94b7f9fe807545eed4f7c83"><div class="ttname"><a href="classQsciLexerFortran77.html#a6ba40887a94b7f9fe807545eed4f7c83">QsciLexerFortran77::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a5e2cdbcaa57b02f18d65aea89d2faa54"><div class="ttname"><a href="classQsciLexerPerl.html#a5e2cdbcaa57b02f18d65aea89d2faa54">QsciLexerPerl::setFoldPackages</a></div><div class="ttdeci">void setFoldPackages(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ad309f6cb931b47d67e67a59b3a66ea84"><div class="ttname"><a href="classQsciScintilla.html#ad309f6cb931b47d67e67a59b3a66ea84">QsciScintilla::lineIndexFromPosition</a></div><div class="ttdeci">void lineIndexFromPosition(int position, int *line, int *index) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae53911447eddf1f0f93811f17ca4ecf8"><div class="ttname"><a href="classQsciScintilla.html#ae53911447eddf1f0f93811f17ca4ecf8">QsciScintilla::setBraceMatching</a></div><div class="ttdeci">virtual void setBraceMatching(BraceMatch bm)</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a45679bbf510fa7e0b264eb9654183f16"><div class="ttname"><a href="classQsciLexerPascal.html#a45679bbf510fa7e0b264eb9654183f16">QsciLexerPascal::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_ab7c13e959940db389fe0daeb96267d8e"><div class="ttname"><a href="classQsciLexerVerilog.html#ab7c13e959940db389fe0daeb96267d8e">QsciLexerVerilog::setFoldPreprocessor</a></div><div class="ttdeci">void setFoldPreprocessor(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a0045744463119646a5fe33ecc4d104fb"><div class="ttname"><a href="classQsciScintillaBase.html#a0045744463119646a5fe33ecc4d104fb">QsciScintillaBase::SCN_USERLISTSELECTION</a></div><div class="ttdeci">void SCN_USERLISTSELECTION(const char *selection, int id)</div><div class="ttdoc">This is an overloaded member function, provided for convenience. It differs from the above function o...</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_a16d51d4aa2ab5836f9aff6f394aeca85"><div class="ttname"><a href="classQsciLexerAsm.html#a16d51d4aa2ab5836f9aff6f394aeca85">QsciLexerAsm::setFoldSyntaxBased</a></div><div class="ttdeci">virtual void setFoldSyntaxBased(bool syntax_based)</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_a4d20a72f3087068af5840042d9beeca7"><div class="ttname"><a href="classQsciLexerLua.html#a4d20a72f3087068af5840042d9beeca7">QsciLexerLua::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a015d6501ee4cca33a00036174529c161"><div class="ttname"><a href="classQsciLexerCPP.html#a015d6501ee4cca33a00036174529c161">QsciLexerCPP::highlightBackQuotedStrings</a></div><div class="ttdeci">bool highlightBackQuotedStrings() const</div><div class="ttdef"><b>Definition:</b> qscilexercpp.h:297</div></div>
<div class="ttc" id="aclassQsciLexerBatch_html_ac378e6bd25b850c9523d0b9c291cfc62"><div class="ttname"><a href="classQsciLexerBatch.html#ac378e6bd25b850c9523d0b9c291cfc62">QsciLexerBatch::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aef97a9061de95a09b57d527f6410881da6dbb5180c0f14cb5588c27a139476f8b"><div class="ttname"><a href="classQsciScintilla.html#aef97a9061de95a09b57d527f6410881da6dbb5180c0f14cb5588c27a139476f8b">QsciScintilla::CallTipsAboveText</a></div><div class="ttdeci">@ CallTipsAboveText</div><div class="ttdoc">Call tips are placed above the text.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:152</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a366393c6bf0fd7ef5ce87b7682e6c6ae"><div class="ttname"><a href="classQsciScintilla.html#a366393c6bf0fd7ef5ce87b7682e6c6ae">QsciScintilla::resetHotspotBackgroundColor</a></div><div class="ttdeci">void resetHotspotBackgroundColor()</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca85a6c4d68e4c936c46c8711f656d95ca"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca85a6c4d68e4c936c46c8711f656d95ca">QsciScintillaBase::SC_MARK_LCORNERCURVE</a></div><div class="ttdeci">@ SC_MARK_LCORNERCURVE</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2615</div></div>
<div class="ttc" id="aclassQsciLexerTCL_html_a6d4b2db2d518117945edcbbbc4e3d26d"><div class="ttname"><a href="classQsciLexerTCL.html#a6d4b2db2d518117945edcbbbc4e3d26d">QsciLexerTCL::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a10adbea430478c16813df76dd2ecd10f"><div class="ttname"><a href="classQsciScintilla.html#a10adbea430478c16813df76dd2ecd10f">QsciScintilla::margins</a></div><div class="ttdeci">int margins() const</div></div>
<div class="ttc" id="aclassQsciLexerDiff_html_ae3deccb74fbb24c32621e70d0e94355a"><div class="ttname"><a href="classQsciLexerDiff.html#ae3deccb74fbb24c32621e70d0e94355a">QsciLexerDiff::~QsciLexerDiff</a></div><div class="ttdeci">virtual ~QsciLexerDiff()</div><div class="ttdoc">Destroys the QsciLexerDiff instance.</div></div>
<div class="ttc" id="aclassQsciLexer_html_a013b7c1bf9846e231b97827dfd9540b0"><div class="ttname"><a href="classQsciLexer.html#a013b7c1bf9846e231b97827dfd9540b0">QsciLexer::keywords</a></div><div class="ttdeci">virtual const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexer_html_a7ebaedee6979d4cb17399361b37e33e0"><div class="ttname"><a href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">QsciLexer::setDefaultPaper</a></div><div class="ttdeci">void setDefaultPaper(const QColor &amp;c)</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_ae1f192b93ad970cb792b5dcac4aa22d8"><div class="ttname"><a href="classQsciLexerVerilog.html#ae1f192b93ad970cb792b5dcac4aa22d8">QsciLexerVerilog::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div><div class="ttdef"><b>Definition:</b> qscilexerverilog.h:202</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ac466f32c3d7e51790b6b25c864783179a8ce5fd7a52d924d7e519eb7beccab5a1"><div class="ttname"><a href="classQsciScintilla.html#ac466f32c3d7e51790b6b25c864783179a8ce5fd7a52d924d7e519eb7beccab5a1">QsciScintilla::AcsDocument</a></div><div class="ttdeci">@ AcsDocument</div><div class="ttdoc">The source is the current document.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:124</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a1d84e61ba19c5177386ba30ba512345f"><div class="ttname"><a href="classQsciScintillaBase.html#a1d84e61ba19c5177386ba30ba512345f">QsciScintillaBase::SendScintilla</a></div><div class="ttdeci">long SendScintilla(unsigned int msg, unsigned long wParam, const QColor &amp;col) const</div><div class="ttdoc">This is an overloaded member function, provided for convenience. It differs from the above function o...</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_adf01efe3951a727dab9c7a1c35d29e0f"><div class="ttname"><a href="classQsciScintillaBase.html#adf01efe3951a727dab9c7a1c35d29e0f">QsciScintillaBase::SendScintilla</a></div><div class="ttdeci">long SendScintilla(unsigned int msg, unsigned long wParam, void *lParam) const</div><div class="ttdoc">This is an overloaded member function, provided for convenience. It differs from the above function o...</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_af935c2c5d8eeb3aeb25ba9b48539f879"><div class="ttname"><a href="classQsciScintillaBase.html#af935c2c5d8eeb3aeb25ba9b48539f879">QsciScintillaBase::SendScintilla</a></div><div class="ttdeci">long SendScintilla(unsigned int msg, const QColor &amp;col) const</div><div class="ttdoc">This is an overloaded member function, provided for convenience. It differs from the above function o...</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_ad12b328c98474857186af058726bd38d"><div class="ttname"><a href="classQsciLexerHTML.html#ad12b328c98474857186af058726bd38d">QsciLexerHTML::wordCharacters</a></div><div class="ttdeci">const char * wordCharacters() const</div><div class="ttdoc">Returns the string of characters that comprise a word.</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a680cba1b994603e73da00610e81debfe"><div class="ttname"><a href="classQsciLexerSQL.html#a680cba1b994603e73da00610e81debfe">QsciLexerSQL::setFoldOnlyBegin</a></div><div class="ttdeci">void setFoldOnlyBegin(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerDiff_html_a795af727d45974e6581ed01bf812b63e"><div class="ttname"><a href="classQsciLexerDiff.html#a795af727d45974e6581ed01bf812b63e">QsciLexerDiff::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_ab5f782645ff1d3a2d7ac371cbd9f2f5d"><div class="ttname"><a href="classQsciLexerCPP.html#ab5f782645ff1d3a2d7ac371cbd9f2f5d">QsciLexerCPP::highlightHashQuotedStrings</a></div><div class="ttdeci">bool highlightHashQuotedStrings() const</div><div class="ttdef"><b>Definition:</b> qscilexercpp.h:286</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a6a8c59ca8409029fc6b27b9ad3c70886"><div class="ttname"><a href="classQsciLexerCPP.html#a6a8c59ca8409029fc6b27b9ad3c70886">QsciLexerCPP::setFoldPreprocessor</a></div><div class="ttdeci">virtual void setFoldPreprocessor(bool fold)</div></div>
<div class="ttc" id="aclassQsciPrinter_html_ad67d67c266263dd2dbfe940b4ad98584"><div class="ttname"><a href="classQsciPrinter.html#ad67d67c266263dd2dbfe940b4ad98584">QsciPrinter::wrapMode</a></div><div class="ttdeci">QsciScintilla::WrapMode wrapMode() const</div><div class="ttdef"><b>Definition:</b> qsciprinter.h:103</div></div>
<div class="ttc" id="aclassQsciLexer_html_a0e4235e0bd33f64431a9c6e8c35038d4"><div class="ttname"><a href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">QsciLexer::setColor</a></div><div class="ttdeci">virtual void setColor(const QColor &amp;c, int style=-1)</div></div>
<div class="ttc" id="aclassQsciLexerPO_html_abb698a7598847dea3cec0686c88ba43a"><div class="ttname"><a href="classQsciLexerPO.html#abb698a7598847dea3cec0686c88ba43a">QsciLexerPO::setFoldComments</a></div><div class="ttdeci">virtual void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a2a85bb9cb78ce6a57cf053dc83333759"><div class="ttname"><a href="classQsciScintilla.html#a2a85bb9cb78ce6a57cf053dc83333759">QsciScintilla::tabIndents</a></div><div class="ttdeci">bool tabIndents() const</div></div>
<div class="ttc" id="aclassQsciAbstractAPIs_html_af9c6c3f8dc068371398a50c6b23dcbf4"><div class="ttname"><a href="classQsciAbstractAPIs.html#af9c6c3f8dc068371398a50c6b23dcbf4">QsciAbstractAPIs::callTips</a></div><div class="ttdeci">virtual QStringList callTips(const QStringList &amp;context, int commas, QsciScintilla::CallTipsStyle style, QList&lt; int &gt; &amp;shifts)=0</div></div>
<div class="ttc" id="aclassQsciScintilla_html_abf895c5e4157e4b6effd28683c728b63"><div class="ttname"><a href="classQsciScintilla.html#abf895c5e4157e4b6effd28683c728b63">QsciScintilla::scrollWidthTracking</a></div><div class="ttdeci">bool scrollWidthTracking() const</div></div>
<div class="ttc" id="aclassQsciLexerFortran_html_a43b721afc8c7b33c5c0699ff9da7d1c7"><div class="ttname"><a href="classQsciLexerFortran.html#a43b721afc8c7b33c5c0699ff9da7d1c7">QsciLexerFortran::~QsciLexerFortran</a></div><div class="ttdeci">virtual ~QsciLexerFortran()</div><div class="ttdoc">Destroys the QsciLexerFortran instance.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a9122d4ac5b0b3eca120cf18ae7275bb1"><div class="ttname"><a href="classQsciScintilla.html#a9122d4ac5b0b3eca120cf18ae7275bb1">QsciScintilla::backspaceUnindents</a></div><div class="ttdeci">bool backspaceUnindents() const</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a2d183c40c276dadd3bbb994b0c0f26ce"><div class="ttname"><a href="classQsciLexerPascal.html#a2d183c40c276dadd3bbb994b0c0f26ce">QsciLexerPascal::setFoldCompact</a></div><div class="ttdeci">virtual void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerMarkdown_html_af912a1a568b342c99f70fab70d89b178"><div class="ttname"><a href="classQsciLexerMarkdown.html#af912a1a568b342c99f70fab70d89b178">QsciLexerMarkdown::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexer_html_abf12117a142b6f68479ea425d80a4196"><div class="ttname"><a href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">QsciLexer::blockStartKeyword</a></div><div class="ttdeci">virtual const char * blockStartKeyword(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_aa28fa3e32d5d4a4efccdad6655fb28c8"><div class="ttname"><a href="classQsciLexerPascal.html#aa28fa3e32d5d4a4efccdad6655fb28c8">QsciLexerPascal::autoCompletionWordSeparators</a></div><div class="ttdeci">QStringList autoCompletionWordSeparators() const</div></div>
<div class="ttc" id="aclassQsciLexer_html_a66c01f0c9470164d4575c2b64f0e4220"><div class="ttname"><a href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">QsciLexer::eolFillChanged</a></div><div class="ttdeci">void eolFillChanged(bool eolfilled, int style)</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_ae6a8edb1b3ae833cd5c5a2b56cf1ec3e"><div class="ttname"><a href="classQsciLexerRuby.html#ae6a8edb1b3ae833cd5c5a2b56cf1ec3e">QsciLexerRuby::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_ae88783c3de2f0c4a0129e5bec77cc5ca"><div class="ttname"><a href="classQsciLexerPascal.html#ae88783c3de2f0c4a0129e5bec77cc5ca">QsciLexerPascal::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div></div>
<div class="ttc" id="aclassQsciLexer_html_a06228b73f8df699a211be872f54d8501"><div class="ttname"><a href="classQsciLexer.html#a06228b73f8df699a211be872f54d8501">QsciLexer::defaultEolFill</a></div><div class="ttdeci">virtual bool defaultEolFill(int style) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_aab2b37b2f67991e9c083d9412cba2264"><div class="ttname"><a href="classQsciScintillaBase.html#aab2b37b2f67991e9c083d9412cba2264">QsciScintillaBase::SendScintilla</a></div><div class="ttdeci">long SendScintilla(unsigned int msg, uintptr_t wParam, const char *lParam) const</div><div class="ttdoc">This is an overloaded member function, provided for convenience. It differs from the above function o...</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html_a42a1cdec7111af0685a9d89419a821bd"><div class="ttname"><a href="classQsciLexerAVS.html#a42a1cdec7111af0685a9d89419a821bd">QsciLexerAVS::foldComments</a></div><div class="ttdeci">bool foldComments() const</div></div>
<div class="ttc" id="aclassQsciCommandSet_html_a7a15e4a269b804a830c881edda1563f7"><div class="ttname"><a href="classQsciCommandSet.html#a7a15e4a269b804a830c881edda1563f7">QsciCommandSet::clearKeys</a></div><div class="ttdeci">void clearKeys()</div><div class="ttdoc">The primary keys bindings for all commands are removed.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a72c0bc1c83fd675714626cd786ca4fb9"><div class="ttname"><a href="classQsciScintillaBase.html#a72c0bc1c83fd675714626cd786ca4fb9">QsciScintillaBase::SCN_STYLENEEDED</a></div><div class="ttdeci">void SCN_STYLENEEDED(int position)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a3793111b6e2a86351c798c68deda7d0c"><div class="ttname"><a href="classQsciScintilla.html#a3793111b6e2a86351c798c68deda7d0c">QsciScintilla::AnnotationDisplay</a></div><div class="ttdeci">AnnotationDisplay</div><div class="ttdoc">This enum defines the different annotation display styles.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:83</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a3045ab135148ca52330ad233703a57f1"><div class="ttname"><a href="classQsciScintilla.html#a3045ab135148ca52330ad233703a57f1">QsciScintilla::annotationDisplay</a></div><div class="ttdeci">AnnotationDisplay annotationDisplay() const</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html_a98400585500ee1c17618992a8e300683"><div class="ttname"><a href="classQsciLexerProperties.html#a98400585500ee1c17618992a8e300683">QsciLexerProperties::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_a9912a293e50e75adc23a532b352a57ea"><div class="ttname"><a href="classQsciLexerTeX.html#a9912a293e50e75adc23a532b352a57ea">QsciLexerTeX::~QsciLexerTeX</a></div><div class="ttdeci">virtual ~QsciLexerTeX()</div><div class="ttdoc">Destroys the QsciLexerTeX instance.</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_acb6433de9e477aeefdce46814dfb89ca"><div class="ttname"><a href="classQsciLexerAsm.html#acb6433de9e477aeefdce46814dfb89ca">QsciLexerAsm::setFoldComments</a></div><div class="ttdeci">virtual void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html_ac814c0fdc49d3c27a027a8e075aa7626"><div class="ttname"><a href="classQsciLexerAVS.html#ac814c0fdc49d3c27a027a8e075aa7626">QsciLexerAVS::setFoldCompact</a></div><div class="ttdeci">virtual void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_aac009a767572be4b4489a0613611cbdb"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#aac009a767572be4b4489a0613611cbdb">QsciLexerCoffeeScript::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerTCL_html_a1b1d726f87795c97839acca28d06dc6e"><div class="ttname"><a href="classQsciLexerTCL.html#a1b1d726f87795c97839acca28d06dc6e">QsciLexerTCL::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciStyle_html_af00ea2dd20e93c5d06d9ce99cbc2cf00"><div class="ttname"><a href="classQsciStyle.html#af00ea2dd20e93c5d06d9ce99cbc2cf00">QsciStyle::setStyle</a></div><div class="ttdeci">void setStyle(int style)</div><div class="ttdef"><b>Definition:</b> qscistyle.h:82</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_ae9881257bbcc887cdbe21e74bbb8ea65"><div class="ttname"><a href="classQsciLexerPerl.html#ae9881257bbcc887cdbe21e74bbb8ea65">QsciLexerPerl::foldComments</a></div><div class="ttdeci">bool foldComments() const</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a57958c564d4d3127e7ee6148d232bd4b"><div class="ttname"><a href="classQsciLexerPerl.html#a57958c564d4d3127e7ee6148d232bd4b">QsciLexerPerl::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexerDiff_html_a6a4b4099b20109442416e2bd8309b494"><div class="ttname"><a href="classQsciLexerDiff.html#a6a4b4099b20109442416e2bd8309b494">QsciLexerDiff::wordCharacters</a></div><div class="ttdeci">const char * wordCharacters() const</div><div class="ttdoc">Returns the string of characters that comprise a word.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aa428fb0b96d28bba4a1d0515ea27643d"><div class="ttname"><a href="classQsciScintilla.html#aa428fb0b96d28bba4a1d0515ea27643d">QsciScintilla::marginType</a></div><div class="ttdeci">MarginType marginType(int margin) const</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a06315a18051184926fe21459fc75b4cc"><div class="ttname"><a href="classQsciLexerCPP.html#a06315a18051184926fe21459fc75b4cc">QsciLexerCPP::setDollarsAllowed</a></div><div class="ttdeci">void setDollarsAllowed(bool allowed)</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a16fb82e08452dc260bdda610817c79ea"><div class="ttname"><a href="classQsciLexerPerl.html#a16fb82e08452dc260bdda610817c79ea">QsciLexerPerl::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_abd8d636e4717ed65e4ea77eca3c28df1"><div class="ttname"><a href="classQsciLexerSQL.html#abd8d636e4717ed65e4ea77eca3c28df1">QsciLexerSQL::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_aeba753c0e1fca8bf66834667e301458e"><div class="ttname"><a href="classQsciLexerHTML.html#aeba753c0e1fca8bf66834667e301458e">QsciLexerHTML::setFoldPreprocessor</a></div><div class="ttdeci">virtual void setFoldPreprocessor(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_aade332d1a99d97ddabf219df2f7009ad"><div class="ttname"><a href="classQsciLexerAsm.html#aade332d1a99d97ddabf219df2f7009ad">QsciLexerAsm::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_ab30fa749a26490888fe18f2fcea47b02"><div class="ttname"><a href="classQsciLexerPython.html#ab30fa749a26490888fe18f2fcea47b02">QsciLexerPython::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a509df9a20a1841de287849d6738ec3dd"><div class="ttname"><a href="classQsciLexerCPP.html#a509df9a20a1841de287849d6738ec3dd">QsciLexerCPP::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciLexerTCL_html_ad331ec23d27ba397d2095ba92cefaecd"><div class="ttname"><a href="classQsciLexerTCL.html#ad331ec23d27ba397d2095ba92cefaecd">QsciLexerTCL::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a7ea79082a0d55e78cd3a60f1f05af6d9"><div class="ttname"><a href="classQsciLexerD.html#a7ea79082a0d55e78cd3a60f1f05af6d9">QsciLexerD::blockStart</a></div><div class="ttdeci">const char * blockStart(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_a928315606c0bd973c59e0b6d9641c3cd"><div class="ttname"><a href="classQsciLexerLua.html#a928315606c0bd973c59e0b6d9641c3cd">QsciLexerLua::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a87e61d47e344dbbb84c4608fdc8536d0"><div class="ttname"><a href="classQsciScintilla.html#a87e61d47e344dbbb84c4608fdc8536d0">QsciScintilla::setExtraAscent</a></div><div class="ttdeci">void setExtraAscent(int extra)</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a5ffd80ff37350acb6fe03f798f34a912"><div class="ttname"><a href="classQsciLexerPerl.html#a5ffd80ff37350acb6fe03f798f34a912">QsciLexerPerl::wordCharacters</a></div><div class="ttdeci">const char * wordCharacters() const</div><div class="ttdoc">Returns the string of characters that comprise a word.</div></div>
<div class="ttc" id="aclassQsciLexerPO_html"><div class="ttname"><a href="classQsciLexerPO.html">QsciLexerPO</a></div><div class="ttdoc">The QsciLexerPO class encapsulates the Scintilla PO lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerpo.h:32</div></div>
<div class="ttc" id="aclassQsciCommand_html"><div class="ttname"><a href="classQsciCommand.html">QsciCommand</a></div><div class="ttdoc">The QsciCommand class represents an internal editor command that may have one or two keys bound to it...</div><div class="ttdef"><b>Definition:</b> qscicommand.h:40</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_ae9fc5faac317ee19add21f8105ff21c5"><div class="ttname"><a href="classQsciLexerPOV.html#ae9fc5faac317ee19add21f8105ff21c5">QsciLexerPOV::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca961e0562a26ab763fba1bc1e92123b85"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca961e0562a26ab763fba1bc1e92123b85">QsciScintillaBase::SC_MARK_DOTDOTDOT</a></div><div class="ttdeci">@ SC_MARK_DOTDOTDOT</div><div class="ttdoc">Three drawn dots.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2638</div></div>
<div class="ttc" id="aclassQsciLexerXML_html_a24d4902dc121381ae5a18a4b8e802479"><div class="ttname"><a href="classQsciLexerXML.html#a24d4902dc121381ae5a18a4b8e802479">QsciLexerXML::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerCSharp_html_a3b06123388fb6a56432819a4dfb30f2c"><div class="ttname"><a href="classQsciLexerCSharp.html#a3b06123388fb6a56432819a4dfb30f2c">QsciLexerCSharp::~QsciLexerCSharp</a></div><div class="ttdeci">virtual ~QsciLexerCSharp()</div><div class="ttdoc">Destroys the QsciLexerCSharp instance.</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_a80c198967862ff5392982a49b8004f48"><div class="ttname"><a href="classQsciLexerPostScript.html#a80c198967862ff5392982a49b8004f48">QsciLexerPostScript::setLevel</a></div><div class="ttdeci">virtual void setLevel(int level)</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_af57050a2bcb9d1d285199159da0ba6e0"><div class="ttname"><a href="classQsciLexerVerilog.html#af57050a2bcb9d1d285199159da0ba6e0">QsciLexerVerilog::setFoldAtModule</a></div><div class="ttdeci">void setFoldAtModule(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a1efa4394b588d27fd2a3bd40163a2342"><div class="ttname"><a href="classQsciScintillaBase.html#a1efa4394b588d27fd2a3bd40163a2342">QsciScintillaBase::replaceHorizontalScrollBar</a></div><div class="ttdeci">void replaceHorizontalScrollBar(QScrollBar *scrollBar)</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_aa8d47420bede5e7fde576ee8dc2728c5"><div class="ttname"><a href="classQsciLexerBash.html#aa8d47420bede5e7fde576ee8dc2728c5">QsciLexerBash::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a1d35bee9f234dbde7066a68b924edeed"><div class="ttname"><a href="classQsciScintilla.html#a1d35bee9f234dbde7066a68b924edeed">QsciScintilla::registerImage</a></div><div class="ttdeci">void registerImage(int id, const QImage &amp;im)</div></div>
<div class="ttc" id="aclassQsciLexerPO_html_af3d4ae3f76854d01444b2fd4637c9b8e"><div class="ttname"><a href="classQsciLexerPO.html#af3d4ae3f76854d01444b2fd4637c9b8e">QsciLexerPO::setFoldCompact</a></div><div class="ttdeci">virtual void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciAPIs_html"><div class="ttname"><a href="classQsciAPIs.html">QsciAPIs</a></div><div class="ttdoc">The QsciAPIs class provies an implementation of the textual API information used in call tips and for...</div><div class="ttdef"><b>Definition:</b> qsciapis.h:66</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a39f62b8e6cee02e86d7af508d20a191d"><div class="ttname"><a href="classQsciScintillaBase.html#a39f62b8e6cee02e86d7af508d20a191d">QsciScintillaBase::keyPressEvent</a></div><div class="ttdeci">virtual void keyPressEvent(QKeyEvent *e)</div><div class="ttdoc">Re-implemented to handle key presses.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_aabab23e5653c35dae8a6f144d73c4657"><div class="ttname"><a href="classQsciScintillaBase.html#aabab23e5653c35dae8a6f144d73c4657">QsciScintillaBase::SCN_AUTOCCHARDELETED</a></div><div class="ttdeci">void SCN_AUTOCCHARDELETED()</div></div>
<div class="ttc" id="aclassQsciStyle_html_ab09932c9dafb915b8138d4ec1cbc79cb"><div class="ttname"><a href="classQsciStyle.html#ab09932c9dafb915b8138d4ec1cbc79cb">QsciStyle::setFont</a></div><div class="ttdeci">void setFont(const QFont &amp;font)</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_abc3666027fe7f0b8ae78ee34e3276069"><div class="ttname"><a href="classQsciLexerVerilog.html#abc3666027fe7f0b8ae78ee34e3276069">QsciLexerVerilog::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad88db21d86df33667c234d00af1fdf94"><div class="ttname"><a href="classQsciScintillaBase.html#ad88db21d86df33667c234d00af1fdf94">QsciScintillaBase::SCN_UPDATEUI</a></div><div class="ttdeci">void SCN_UPDATEUI(int updated)</div></div>
<div class="ttc" id="aclassQsciAbstractAPIs_html_a7ef866227b05482cb32c70b44e8bdec1"><div class="ttname"><a href="classQsciAbstractAPIs.html#a7ef866227b05482cb32c70b44e8bdec1">QsciAbstractAPIs::~QsciAbstractAPIs</a></div><div class="ttdeci">virtual ~QsciAbstractAPIs()</div><div class="ttdoc">Destroy the QsciAbstractAPIs instance.</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a696abf6da5415e772e5ade8752eac3b2"><div class="ttname"><a href="classQsciLexerPerl.html#a696abf6da5415e772e5ade8752eac3b2">QsciLexerPerl::foldAtElse</a></div><div class="ttdeci">bool foldAtElse() const</div><div class="ttdef"><b>Definition:</b> qscilexerperl.h:237</div></div>
<div class="ttc" id="aclassQsciLexer_html_ac7c40b97187e23ab85f6d95113f91b39"><div class="ttname"><a href="classQsciLexer.html#ac7c40b97187e23ab85f6d95113f91b39">QsciLexer::lexer</a></div><div class="ttdeci">virtual const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerMakefile_html_a6537d97973481d6e7c911f8031385deb"><div class="ttname"><a href="classQsciLexerMakefile.html#a6537d97973481d6e7c911f8031385deb">QsciLexerMakefile::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_a46e8e5909bfc92669cf155317ecb6fe9"><div class="ttname"><a href="classQsciLexerVHDL.html#a46e8e5909bfc92669cf155317ecb6fe9">QsciLexerVHDL::foldAtElse</a></div><div class="ttdeci">bool foldAtElse() const</div></div>
<div class="ttc" id="aclassQsciLexerFortran77_html_a206ea971cb4152f8ca00087544574d15"><div class="ttname"><a href="classQsciLexerFortran77.html#a206ea971cb4152f8ca00087544574d15">QsciLexerFortran77::setFoldCompact</a></div><div class="ttdeci">virtual void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca787e7e471b489bda535116b75765acad"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca787e7e471b489bda535116b75765acad">QsciScintillaBase::SC_MARK_TCORNERCURVE</a></div><div class="ttdeci">@ SC_MARK_TCORNERCURVE</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2619</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_afa0b9ecea2700420820e4e9b705cb784"><div class="ttname"><a href="classQsciLexerRuby.html#afa0b9ecea2700420820e4e9b705cb784">QsciLexerRuby::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a419ab8aed49ea1711ce4ffcf19146df1"><div class="ttname"><a href="classQsciScintilla.html#a419ab8aed49ea1711ce4ffcf19146df1">QsciScintilla::setMarginsBackgroundColor</a></div><div class="ttdeci">virtual void setMarginsBackgroundColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_adfd788dce5c1a91d1fcd5e6fdd2fca59"><div class="ttname"><a href="classQsciScintillaBase.html#adfd788dce5c1a91d1fcd5e6fdd2fca59">QsciScintillaBase::SCN_DWELLSTART</a></div><div class="ttdeci">void SCN_DWELLSTART(int position, int x, int y)</div></div>
<div class="ttc" id="aclassQsciLexerPO_html_a911dee848cf18712f663b2cfdc5084f1"><div class="ttname"><a href="classQsciLexerPO.html#a911dee848cf18712f663b2cfdc5084f1">QsciLexerPO::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a0f2353468d2c37abe9c933d4ac0478ad"><div class="ttname"><a href="classQsciScintilla.html#a0f2353468d2c37abe9c933d4ac0478ad">QsciScintilla::setEolMode</a></div><div class="ttdeci">virtual void setEolMode(EolMode mode)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a7d5c7190fb8b022e05e20ca011a0c65e"><div class="ttname"><a href="classQsciScintilla.html#a7d5c7190fb8b022e05e20ca011a0c65e">QsciScintilla::text</a></div><div class="ttdeci">QString text(int line) const</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_a74867915ad9d609b9b516eff87101cc9"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#a74867915ad9d609b9b516eff87101cc9">QsciLexerCoffeeScript::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_a4864bf9360ed4748b9ca7a1d5e34e7d8"><div class="ttname"><a href="classQsciLexerPOV.html#a4864bf9360ed4748b9ca7a1d5e34e7d8">QsciLexerPOV::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_af0ccf94585e15b87a18f12ab9de1c977"><div class="ttname"><a href="classQsciLexerCPP.html#af0ccf94585e15b87a18f12ab9de1c977">QsciLexerCPP::autoCompletionWordSeparators</a></div><div class="ttdeci">QStringList autoCompletionWordSeparators() const</div></div>
<div class="ttc" id="aclassQsciLexerXML_html_a36f390db2c97da9c271b0d1ba2794278"><div class="ttname"><a href="classQsciLexerXML.html#a36f390db2c97da9c271b0d1ba2794278">QsciLexerXML::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_aaf9762aeea19ed1c8d6766a9e6a52cd3"><div class="ttname"><a href="classQsciLexerCSS.html#aaf9762aeea19ed1c8d6766a9e6a52cd3">QsciLexerCSS::setFoldCompact</a></div><div class="ttdeci">virtual void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a5f10e95e76c2b38b9d20f6f728243e65"><div class="ttname"><a href="classQsciScintilla.html#a5f10e95e76c2b38b9d20f6f728243e65">QsciScintilla::autoCompleteFromAll</a></div><div class="ttdeci">virtual void autoCompleteFromAll()</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_a0741fad0b942deb73642be16c3159eb1"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#a0741fad0b942deb73642be16c3159eb1">QsciLexerCoffeeScript::autoCompletionWordSeparators</a></div><div class="ttdeci">QStringList autoCompletionWordSeparators() const</div></div>
<div class="ttc" id="aclassQsciLexerSRec_html"><div class="ttname"><a href="classQsciLexerSRec.html">QsciLexerSRec</a></div><div class="ttdoc">The QsciLexerSRec class encapsulates the Scintilla S-Record lexer.</div><div class="ttdef"><b>Definition:</b> qscilexersrec.h:32</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a18b2b3426de5ec4822f4584ca60ed2b9"><div class="ttname"><a href="classQsciScintilla.html#a18b2b3426de5ec4822f4584ca60ed2b9">QsciScintilla::modificationChanged</a></div><div class="ttdeci">void modificationChanged(bool m)</div></div>
<div class="ttc" id="aclassQsciLexerEDIFACT_html_a94f9b521b521a540f848d55f2f4e8d45"><div class="ttname"><a href="classQsciLexerEDIFACT.html#a94f9b521b521a540f848d55f2f4e8d45">QsciLexerEDIFACT::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_adfb83ee7ea262a33f775d1e53cf38bec"><div class="ttname"><a href="classQsciLexerPOV.html#adfb83ee7ea262a33f775d1e53cf38bec">QsciLexerPOV::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ad1b6838e763eb9d7c0b3562b6c2a96ae"><div class="ttname"><a href="classQsciScintilla.html#ad1b6838e763eb9d7c0b3562b6c2a96ae">QsciScintilla::setMarginText</a></div><div class="ttdeci">void setMarginText(int line, const QString &amp;text, const QsciStyle &amp;style)</div></div>
<div class="ttc" id="aclassQsciLexerIntelHex_html_a224b3f42dbf3afc51f3ba7c86f08e325"><div class="ttname"><a href="classQsciLexerIntelHex.html#a224b3f42dbf3afc51f3ba7c86f08e325">QsciLexerIntelHex::~QsciLexerIntelHex</a></div><div class="ttdeci">virtual ~QsciLexerIntelHex()</div><div class="ttdoc">Destroys the QsciLexerIntelHex instance.</div></div>
<div class="ttc" id="aclassQsciLexerCustom_html_a91d71c4bdff5140ae0b0cb34b4511f79"><div class="ttname"><a href="classQsciLexerCustom.html#a91d71c4bdff5140ae0b0cb34b4511f79">QsciLexerCustom::styleText</a></div><div class="ttdeci">virtual void styleText(int start, int end)=0</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214cae324f61ed2740c6be760489cbaa69fb8"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cae324f61ed2740c6be760489cbaa69fb8">QsciScintillaBase::SC_MARK_PLUS</a></div><div class="ttdeci">@ SC_MARK_PLUS</div><div class="ttdoc">A drawn plus sign.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2589</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html_af5a3f47c4f0be631303cabd42d904c3e"><div class="ttname"><a href="classQsciLexerAVS.html#af5a3f47c4f0be631303cabd42d904c3e">QsciLexerAVS::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexer_html_a9576dd2ce748647abe981724ee76c1ce"><div class="ttname"><a href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">QsciLexer::lexerId</a></div><div class="ttdeci">virtual int lexerId() const</div></div>
<div class="ttc" id="aclassQsciLexerTCL_html_a59f517180e03fd1790c4a6de73196a70"><div class="ttname"><a href="classQsciLexerTCL.html#a59f517180e03fd1790c4a6de73196a70">QsciLexerTCL::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerYAML_html_a5fc9da8d92341819072536ce840902fc"><div class="ttname"><a href="classQsciLexerYAML.html#a5fc9da8d92341819072536ce840902fc">QsciLexerYAML::setFoldComments</a></div><div class="ttdeci">virtual void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_a043411367c3fa915c8f4797cc51d0c8c"><div class="ttname"><a href="classQsciLexerVHDL.html#a043411367c3fa915c8f4797cc51d0c8c">QsciLexerVHDL::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a830b832b87182332b9dbaa0a69c6a145"><div class="ttname"><a href="classQsciLexerSQL.html#a830b832b87182332b9dbaa0a69c6a145">QsciLexerSQL::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_a287cf2adecde291abab55095227864a9"><div class="ttname"><a href="classQsciLexerJSON.html#a287cf2adecde291abab55095227864a9">QsciLexerJSON::QsciLexerJSON</a></div><div class="ttdeci">QsciLexerJSON(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerYAML_html_adfb332858ee86dc00f58f309f394d9d8"><div class="ttname"><a href="classQsciLexerYAML.html#adfb332858ee86dc00f58f309f394d9d8">QsciLexerYAML::foldComments</a></div><div class="ttdeci">bool foldComments() const</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_ac6f508a57750605ec3b9688408b092b2"><div class="ttname"><a href="classQsciLexerCPP.html#ac6f508a57750605ec3b9688408b092b2">QsciLexerCPP::stylePreprocessor</a></div><div class="ttdeci">bool stylePreprocessor() const</div><div class="ttdef"><b>Definition:</b> qscilexercpp.h:253</div></div>
<div class="ttc" id="aclassQsciLexerBatch_html_aff0007dfcbcced2ee7c89ebb12376f22"><div class="ttname"><a href="classQsciLexerBatch.html#aff0007dfcbcced2ee7c89ebb12376f22">QsciLexerBatch::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aedab060e87e0533083ea8f1398302090"><div class="ttname"><a href="classQsciScintilla.html#aedab060e87e0533083ea8f1398302090">QsciScintilla::MarginType</a></div><div class="ttdeci">MarginType</div><div class="ttdoc">This enum defines the different margin types.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:326</div></div>
<div class="ttc" id="aclassQsciLexerXML_html_ae0bb41012d9d98366b435f9a534ce7a1"><div class="ttname"><a href="classQsciLexerXML.html#ae0bb41012d9d98366b435f9a534ce7a1">QsciLexerXML::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a13a64159770a6eb451567bc2d293c2a2"><div class="ttname"><a href="classQsciScintilla.html#a13a64159770a6eb451567bc2d293c2a2">QsciScintilla::setCallTipsForegroundColor</a></div><div class="ttdeci">void setCallTipsForegroundColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciLexerXML_html_a89f9fc2c333d559ed9489cc6b121b91e"><div class="ttname"><a href="classQsciLexerXML.html#a89f9fc2c333d559ed9489cc6b121b91e">QsciLexerXML::~QsciLexerXML</a></div><div class="ttdeci">virtual ~QsciLexerXML()</div><div class="ttdoc">Destroys the QsciLexerXML instance.</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html_a5e15c53d398d9d7e9ef7e0df41bc3f62"><div class="ttname"><a href="classQsciLexerProperties.html#a5e15c53d398d9d7e9ef7e0df41bc3f62">QsciLexerProperties::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_ad77fd8b1e9ed6bac617f194306de2ea8"><div class="ttname"><a href="classQsciLexerLua.html#ad77fd8b1e9ed6bac617f194306de2ea8">QsciLexerLua::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a0f69249f4e97b96f09ea70f546df7464"><div class="ttname"><a href="classQsciScintillaBase.html#a0f69249f4e97b96f09ea70f546df7464">QsciScintillaBase::pool</a></div><div class="ttdeci">static QsciScintillaBase * pool()</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a527b309bfaf28b870c15d87a5af7c005"><div class="ttname"><a href="classQsciScintilla.html#a527b309bfaf28b870c15d87a5af7c005">QsciScintilla::autoCompletionReplaceWord</a></div><div class="ttdeci">bool autoCompletionReplaceWord() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aec296526c86ae02deb561b4b4836a886"><div class="ttname"><a href="classQsciScintilla.html#aec296526c86ae02deb561b4b4836a886">QsciScintilla::setEdgeColor</a></div><div class="ttdeci">void setEdgeColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_a2260bd1206a91b7f9487e9ffe366732f"><div class="ttname"><a href="classQsciLexerVHDL.html#a2260bd1206a91b7f9487e9ffe366732f">QsciLexerVHDL::QsciLexerVHDL</a></div><div class="ttdeci">QsciLexerVHDL(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerCMake_html_a0e2e832caa9adddace3085ebfa582948"><div class="ttname"><a href="classQsciLexerCMake.html#a0e2e832caa9adddace3085ebfa582948">QsciLexerCMake::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciLexer_html_affe136114d62180e9a14caa81f2b7fd5"><div class="ttname"><a href="classQsciLexer.html#affe136114d62180e9a14caa81f2b7fd5">QsciLexer::braceStyle</a></div><div class="ttdeci">virtual int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_aff624320c72fa3b433d82d6a558238e8"><div class="ttname"><a href="classQsciLexerPython.html#aff624320c72fa3b433d82d6a558238e8">QsciLexerPython::indentationWarning</a></div><div class="ttdeci">QsciLexerPython::IndentationWarning indentationWarning() const</div><div class="ttdef"><b>Definition:</b> qscilexerpython.h:210</div></div>
<div class="ttc" id="aclassQsciLexerMatlab_html_a1b26669dd868d97d8a04837aada5549f"><div class="ttname"><a href="classQsciLexerMatlab.html#a1b26669dd868d97d8a04837aada5549f">QsciLexerMatlab::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexer_html_a340eafe726fd6964c0adba956fe3428c"><div class="ttname"><a href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">QsciLexer::blockStart</a></div><div class="ttdeci">virtual const char * blockStart(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciAbstractAPIs_html"><div class="ttname"><a href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a></div><div class="ttdoc">The QsciAbstractAPIs class represents the interface to the textual API information used in call tips ...</div><div class="ttdef"><b>Definition:</b> qsciabstractapis.h:42</div></div>
<div class="ttc" id="aclassQsciLexerEDIFACT_html_a77024f83fb756608060b105d3f21ae34"><div class="ttname"><a href="classQsciLexerEDIFACT.html#a77024f83fb756608060b105d3f21ae34">QsciLexerEDIFACT::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a5ea4eb1e65b2cee23a09f143074790b4"><div class="ttname"><a href="classQsciLexerPerl.html#a5ea4eb1e65b2cee23a09f143074790b4">QsciLexerPerl::foldPackages</a></div><div class="ttdeci">bool foldPackages() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_abb3418e72ca4479b276276d652048461"><div class="ttname"><a href="classQsciScintilla.html#abb3418e72ca4479b276276d652048461">QsciScintilla::~QsciScintilla</a></div><div class="ttdeci">virtual ~QsciScintilla()</div><div class="ttdoc">Destroys the QsciScintilla instance.</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_a0ed58ff3726deb2215eaff2c1892bc9b"><div class="ttname"><a href="classQsciLexerVHDL.html#a0ed58ff3726deb2215eaff2c1892bc9b">QsciLexerVHDL::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aefa257027a014475cb468b6d77bcf1f7"><div class="ttname"><a href="classQsciScintilla.html#aefa257027a014475cb468b6d77bcf1f7">QsciScintilla::setAutoCompletionUseSingle</a></div><div class="ttdeci">virtual void setAutoCompletionUseSingle(AutoCompletionUseSingle single)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a778c09295bdb4924aacf40d3134c50ba"><div class="ttname"><a href="classQsciScintilla.html#a778c09295bdb4924aacf40d3134c50ba">QsciScintilla::whitespaceVisibility</a></div><div class="ttdeci">WhitespaceVisibility whitespaceVisibility() const</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_a8defdc421cdee2af973ee44b7005f1a5"><div class="ttname"><a href="classQsciLexerAsm.html#a8defdc421cdee2af973ee44b7005f1a5">QsciLexerAsm::setFoldCompact</a></div><div class="ttdeci">virtual void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a7bc5fb5d0daf8261544fb6fe738a0c91"><div class="ttname"><a href="classQsciScintilla.html#a7bc5fb5d0daf8261544fb6fe738a0c91">QsciScintilla::setLexer</a></div><div class="ttdeci">virtual void setLexer(QsciLexer *lexer=0)</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_a21f1bb849edbfbc0cf58bc55cc75e8a3"><div class="ttname"><a href="classQsciLexerBash.html#a21f1bb849edbfbc0cf58bc55cc75e8a3">QsciLexerBash::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerMakefile_html_a8be47404070281d5c305be5331616b15"><div class="ttname"><a href="classQsciLexerMakefile.html#a8be47404070281d5c305be5331616b15">QsciLexerMakefile::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_accc3cd3ccf7d62840ded955400695b9d"><div class="ttname"><a href="classQsciLexerPython.html#accc3cd3ccf7d62840ded955400695b9d">QsciLexerPython::setV2UnicodeAllowed</a></div><div class="ttdeci">void setV2UnicodeAllowed(bool allowed)</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_a2c29f0bbe4d09c159040b5676c8143d2"><div class="ttname"><a href="classQsciLexerCSS.html#a2c29f0bbe4d09c159040b5676c8143d2">QsciLexerCSS::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca7baf71a4e105fbebbaa7803a3f722b0f"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca7baf71a4e105fbebbaa7803a3f722b0f">QsciScintillaBase::SC_MARK_MINUS</a></div><div class="ttdeci">@ SC_MARK_MINUS</div><div class="ttdoc">A drawn minus sign.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2586</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_ad300e5513ee85748aa3dfab2d9617ad5"><div class="ttname"><a href="classQsciLexerJSON.html#ad300e5513ee85748aa3dfab2d9617ad5">QsciLexerJSON::highlightEscapeSequences</a></div><div class="ttdeci">bool highlightEscapeSequences() const</div><div class="ttdef"><b>Definition:</b> qscilexerjson.h:145</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_ae6e6be4b076718026d027629b28faba6"><div class="ttname"><a href="classQsciLexerHTML.html#ae6e6be4b076718026d027629b28faba6">QsciLexerHTML::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ab8bfeae44abd61659d207a86660b100c"><div class="ttname"><a href="classQsciScintilla.html#ab8bfeae44abd61659d207a86660b100c">QsciScintilla::apiContext</a></div><div class="ttdeci">virtual QStringList apiContext(int pos, int &amp;context_start, int &amp;last_word_start)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a18d150beb0cd818ebcbcee886217de6a"><div class="ttname"><a href="classQsciScintilla.html#a18d150beb0cd818ebcbcee886217de6a">QsciScintilla::setIndentationGuidesBackgroundColor</a></div><div class="ttdeci">virtual void setIndentationGuidesBackgroundColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a3035ddd4e1360c2d9a6c86b362a0d905"><div class="ttname"><a href="classQsciScintilla.html#a3035ddd4e1360c2d9a6c86b362a0d905">QsciScintilla::setUnmatchedBraceBackgroundColor</a></div><div class="ttdeci">void setUnmatchedBraceBackgroundColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ad55ff1f454f9cb5c14f63c5c6870c895"><div class="ttname"><a href="classQsciScintilla.html#ad55ff1f454f9cb5c14f63c5c6870c895">QsciScintilla::indicatorClicked</a></div><div class="ttdeci">void indicatorClicked(int line, int index, Qt::KeyboardModifiers state)</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_ad4eae3482cf519fc237705b9cb1aa87d"><div class="ttname"><a href="classQsciLexerAsm.html#ad4eae3482cf519fc237705b9cb1aa87d">QsciLexerAsm::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a6d0427b93e05876c9a2b541eae08ddab"><div class="ttname"><a href="classQsciScintillaBase.html#a6d0427b93e05876c9a2b541eae08ddab">QsciScintillaBase::resizeEvent</a></div><div class="ttdeci">virtual void resizeEvent(QResizeEvent *e)</div><div class="ttdoc">Re-implemented to handle resizes.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ac629ee3f5ca0741d4590f6aef59611c8"><div class="ttname"><a href="classQsciScintilla.html#ac629ee3f5ca0741d4590f6aef59611c8">QsciScintilla::eolMode</a></div><div class="ttdeci">EolMode eolMode() const</div></div>
<div class="ttc" id="aclassQsciAPIs_html_a9dc74576c602f1df961aa8efee652a3d"><div class="ttname"><a href="classQsciAPIs.html#a9dc74576c602f1df961aa8efee652a3d">QsciAPIs::isPrepared</a></div><div class="ttdeci">bool isPrepared(const QString &amp;filename=QString()) const</div></div>
<div class="ttc" id="aclassQsciLexerPO_html_a8eb17be2a61d63249564be87b7d777d8"><div class="ttname"><a href="classQsciLexerPO.html#a8eb17be2a61d63249564be87b7d777d8">QsciLexerPO::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_aa023c95fbbecbbbf7046c92d6fcfdce5"><div class="ttname"><a href="classQsciLexerCPP.html#aa023c95fbbecbbbf7046c92d6fcfdce5">QsciLexerCPP::setHighlightBackQuotedStrings</a></div><div class="ttdeci">void setHighlightBackQuotedStrings(bool enabled)</div></div>
<div class="ttc" id="aclassQsciLexerBash_html"><div class="ttname"><a href="classQsciLexerBash.html">QsciLexerBash</a></div><div class="ttdoc">The QsciLexerBash class encapsulates the Scintilla Bash lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerbash.h:32</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae478a896ae32a30e8a375049a3d477e0a157be2e74764c6913ff97b4181f1d178"><div class="ttname"><a href="classQsciScintilla.html#ae478a896ae32a30e8a375049a3d477e0a157be2e74764c6913ff97b4181f1d178">QsciScintilla::CircledFoldStyle</a></div><div class="ttdeci">@ CircledFoldStyle</div><div class="ttdoc">Circled folding style using circled plus and minus symbols.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:217</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a263f0c4753c9a0c950adf1377737444e"><div class="ttname"><a href="classQsciScintilla.html#a263f0c4753c9a0c950adf1377737444e">QsciScintilla::setMarginsForegroundColor</a></div><div class="ttdeci">virtual void setMarginsForegroundColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a06458817a42498ee65e890c36f63453e"><div class="ttname"><a href="classQsciScintilla.html#a06458817a42498ee65e890c36f63453e">QsciScintilla::setExtraDescent</a></div><div class="ttdeci">void setExtraDescent(int extra)</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html_a76890c95abff2bb6f5eebe7a2cb5a0a3"><div class="ttname"><a href="classQsciLexerProperties.html#a76890c95abff2bb6f5eebe7a2cb5a0a3">QsciLexerProperties::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a7436ea4b640c312fd07945e9b436e19b"><div class="ttname"><a href="classQsciScintilla.html#a7436ea4b640c312fd07945e9b436e19b">QsciScintilla::setWhitespaceSize</a></div><div class="ttdeci">void setWhitespaceSize(int size)</div></div>
<div class="ttc" id="aclassQsciLexerIDL_html_a56afa4275e743eeff3dc693d9da85fd3"><div class="ttname"><a href="classQsciLexerIDL.html#a56afa4275e743eeff3dc693d9da85fd3">QsciLexerIDL::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a5a4a2c5466d1b4f7d6e835c253cb1730"><div class="ttname"><a href="classQsciScintillaBase.html#a5a4a2c5466d1b4f7d6e835c253cb1730">QsciScintillaBase::mouseMoveEvent</a></div><div class="ttdeci">virtual void mouseMoveEvent(QMouseEvent *e)</div><div class="ttdoc">Re-implemented to handle mouse moves.</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_a01e79cce2d3e498bc5473db51e1d0bc6"><div class="ttname"><a href="classQsciLexerTeX.html#a01e79cce2d3e498bc5473db51e1d0bc6">QsciLexerTeX::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div><div class="ttdef"><b>Definition:</b> qscilexertex.h:111</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a94a1cff08b2ef6558d054177fa88ea47"><div class="ttname"><a href="classQsciScintillaBase.html#a94a1cff08b2ef6558d054177fa88ea47">QsciScintillaBase::SCN_PAINTED</a></div><div class="ttdeci">void SCN_PAINTED()</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_a410bcada9eb227aa5689304b861c9997"><div class="ttname"><a href="classQsciLexerVerilog.html#a410bcada9eb227aa5689304b861c9997">QsciLexerVerilog::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerJavaScript_html_a814917aafe1fef03ec20571e91bb4571"><div class="ttname"><a href="classQsciLexerJavaScript.html#a814917aafe1fef03ec20571e91bb4571">QsciLexerJavaScript::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html"><div class="ttname"><a href="classQsciLexerAVS.html">QsciLexerAVS</a></div><div class="ttdoc">The QsciLexerAVS class encapsulates the Scintilla AVS lexer.</div><div class="ttdef"><b>Definition:</b> qscilexeravs.h:32</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_a377ab8d8a44c7fff2f355cc8ed45e611"><div class="ttname"><a href="classQsciLexerCSS.html#a377ab8d8a44c7fff2f355cc8ed45e611">QsciLexerCSS::~QsciLexerCSS</a></div><div class="ttdeci">virtual ~QsciLexerCSS()</div><div class="ttdoc">Destroys the QsciLexerCSS instance.</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_ab9ae7a11b4c9ba6f62d795dce8d6fab8"><div class="ttname"><a href="classQsciLexerHTML.html#ab9ae7a11b4c9ba6f62d795dce8d6fab8">QsciLexerHTML::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerCSharp_html_a897d4004ebe6faefcb97e27713e4a8cc"><div class="ttname"><a href="classQsciLexerCSharp.html#a897d4004ebe6faefcb97e27713e4a8cc">QsciLexerCSharp::QsciLexerCSharp</a></div><div class="ttdeci">QsciLexerCSharp(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_a7c73d608fd96b019e70ebf448de23357"><div class="ttname"><a href="classQsciLexerHTML.html#a7c73d608fd96b019e70ebf448de23357">QsciLexerHTML::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_a4fe52167ba709a506391026615d0ef7b"><div class="ttname"><a href="classQsciLexerRuby.html#a4fe52167ba709a506391026615d0ef7b">QsciLexerRuby::~QsciLexerRuby</a></div><div class="ttdeci">virtual ~QsciLexerRuby()</div><div class="ttdoc">Destroys the QsciLexerRuby instance.</div></div>
<div class="ttc" id="aclassQsciLexerIntelHex_html_acb62449f90ad9ebb39a3c261bbe1e3ca"><div class="ttname"><a href="classQsciLexerIntelHex.html#acb62449f90ad9ebb39a3c261bbe1e3ca">QsciLexerIntelHex::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_aa86a61cd082e2e9fd76e878e8d6a096a"><div class="ttname"><a href="classQsciLexerPostScript.html#aa86a61cd082e2e9fd76e878e8d6a096a">QsciLexerPostScript::foldAtElse</a></div><div class="ttdeci">bool foldAtElse() const</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_aff715db68554a1022792135e8edd0dba"><div class="ttname"><a href="classQsciLexerLua.html#aff715db68554a1022792135e8edd0dba">QsciLexerLua::autoCompletionWordSeparators</a></div><div class="ttdeci">QStringList autoCompletionWordSeparators() const</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_acbf55e58ad04813101573146ecc43c67"><div class="ttname"><a href="classQsciLexerCSS.html#acbf55e58ad04813101573146ecc43c67">QsciLexerCSS::QsciLexerCSS</a></div><div class="ttdeci">QsciLexerCSS(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a9071c0772ce576f60fce08395ce04274"><div class="ttname"><a href="classQsciScintilla.html#a9071c0772ce576f60fce08395ce04274">QsciScintilla::setUtf8</a></div><div class="ttdeci">virtual void setUtf8(bool cp)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a965242ee4392b838cc182c823de54ff6"><div class="ttname"><a href="classQsciScintillaBase.html#a965242ee4392b838cc182c823de54ff6">QsciScintillaBase::~QsciScintillaBase</a></div><div class="ttdeci">virtual ~QsciScintillaBase()</div><div class="ttdoc">Destroys the QsciScintillaBase instance.</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_a2f3d753794280bfc09719e3ca521be83"><div class="ttname"><a href="classQsciLexerHTML.html#a2f3d753794280bfc09719e3ca521be83">QsciLexerHTML::foldPreprocessor</a></div><div class="ttdeci">bool foldPreprocessor() const</div><div class="ttdef"><b>Definition:</b> qscilexerhtml.h:445</div></div>
<div class="ttc" id="aclassQsciMacro_html_a17533fc70491bd7752d4a8ead5facf01"><div class="ttname"><a href="classQsciMacro.html#a17533fc70491bd7752d4a8ead5facf01">QsciMacro::~QsciMacro</a></div><div class="ttdeci">virtual ~QsciMacro()</div><div class="ttdoc">Destroy the QsciMacro instance.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ad5fa8715b931fc0143aa72a20420578d"><div class="ttname"><a href="classQsciScintilla.html#ad5fa8715b931fc0143aa72a20420578d">QsciScintilla::scrollWidth</a></div><div class="ttdeci">int scrollWidth() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aef97a9061de95a09b57d527f6410881d"><div class="ttname"><a href="classQsciScintilla.html#aef97a9061de95a09b57d527f6410881d">QsciScintilla::CallTipsPosition</a></div><div class="ttdeci">CallTipsPosition</div><div class="ttdoc">This enum defines the different call tip positions.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:147</div></div>
<div class="ttc" id="aclassQsciPrinter_html_aaa54abecf0defffdfda80f95af6febf9"><div class="ttname"><a href="classQsciPrinter.html#aaa54abecf0defffdfda80f95af6febf9">QsciPrinter::QsciPrinter</a></div><div class="ttdeci">QsciPrinter(PrinterMode mode=ScreenResolution)</div><div class="ttdoc">Constructs a printer paint device with mode mode.</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_ace6bf74522c57e70f2c3ac525e1fd830"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#ace6bf74522c57e70f2c3ac525e1fd830">QsciLexerCoffeeScript::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_a1bb598533be61e117a252d06cf5e4a4b"><div class="ttname"><a href="classQsciLexerVerilog.html#a1bb598533be61e117a252d06cf5e4a4b">QsciLexerVerilog::foldAtElse</a></div><div class="ttdeci">bool foldAtElse() const</div><div class="ttdef"><b>Definition:</b> qscilexerverilog.h:180</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca8a44e2cee896ee89527e1d026e8cd9ff"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca8a44e2cee896ee89527e1d026e8cd9ff">QsciScintillaBase::SC_MARK_BACKGROUND</a></div><div class="ttdeci">@ SC_MARK_BACKGROUND</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2635</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a4dd046074be580fbde318ba2ae343d39"><div class="ttname"><a href="classQsciScintilla.html#a4dd046074be580fbde318ba2ae343d39">QsciScintilla::setMarginType</a></div><div class="ttdeci">void setMarginType(int margin, MarginType type)</div></div>
<div class="ttc" id="aclassQsciLexerTCL_html_a634989e93d2975d1838016ed24f3e45f"><div class="ttname"><a href="classQsciLexerTCL.html#a634989e93d2975d1838016ed24f3e45f">QsciLexerTCL::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a8c74012833091c1f71e2bea9d1a2a5d5"><div class="ttname"><a href="classQsciLexerD.html#a8c74012833091c1f71e2bea9d1a2a5d5">QsciLexerD::foldComments</a></div><div class="ttdeci">bool foldComments() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a067cd392c008e07ff259ffdd0ce25fcb"><div class="ttname"><a href="classQsciScintilla.html#a067cd392c008e07ff259ffdd0ce25fcb">QsciScintilla::setMatchedBraceIndicator</a></div><div class="ttdeci">void setMatchedBraceIndicator(int indicatorNumber)</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_ac74a6288e07e20f18ad04e900b48851b"><div class="ttname"><a href="classQsciLexerSQL.html#ac74a6288e07e20f18ad04e900b48851b">QsciLexerSQL::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a07c7b6c9889e61f7c7971bf126bff4f2"><div class="ttname"><a href="classQsciScintilla.html#a07c7b6c9889e61f7c7971bf126bff4f2">QsciScintilla::marginBackgroundColor</a></div><div class="ttdeci">QColor marginBackgroundColor(int margin) const</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_aa3454a4c643cd0d479da8412341f1206"><div class="ttname"><a href="classQsciLexerPython.html#aa3454a4c643cd0d479da8412341f1206">QsciLexerPython::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerTCL_html_aa4e0b16ffd568f44be50375e0572011c"><div class="ttname"><a href="classQsciLexerTCL.html#aa4e0b16ffd568f44be50375e0572011c">QsciLexerTCL::~QsciLexerTCL</a></div><div class="ttdeci">virtual ~QsciLexerTCL()</div><div class="ttdoc">Destroys the QsciLexerTCL instance.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a794059a7bf67982063ae349847599376"><div class="ttname"><a href="classQsciScintilla.html#a794059a7bf67982063ae349847599376">QsciScintilla::markerDefine</a></div><div class="ttdeci">int markerDefine(MarkerSymbol sym, int markerNumber=-1)</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html_aef65e35b32701f0a15d8c2687c20516a"><div class="ttname"><a href="classQsciLexerAVS.html#aef65e35b32701f0a15d8c2687c20516a">QsciLexerAVS::wordCharacters</a></div><div class="ttdeci">const char * wordCharacters() const</div><div class="ttdoc">Returns the string of characters that comprise a word.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a5d08214cc5bab0a03b383809f5c626f7"><div class="ttname"><a href="classQsciScintilla.html#a5d08214cc5bab0a03b383809f5c626f7">QsciScintilla::endUndoAction</a></div><div class="ttdeci">void endUndoAction()</div></div>
<div class="ttc" id="aclassQsciMacro_html_a168da9e3a890906f229505cd253eec4b"><div class="ttname"><a href="classQsciMacro.html#a168da9e3a890906f229505cd253eec4b">QsciMacro::QsciMacro</a></div><div class="ttdeci">QsciMacro(QsciScintilla *parent)</div><div class="ttdoc">Construct a QsciMacro with parent parent.</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a27dcfdcac480d0360029d1f12b14f724"><div class="ttname"><a href="classQsciLexerPython.html#a27dcfdcac480d0360029d1f12b14f724">QsciLexerPython::setFoldCompact</a></div><div class="ttdeci">void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aece608d0192ccad13cc706c4b79005e3"><div class="ttname"><a href="classQsciScintilla.html#aece608d0192ccad13cc706c4b79005e3">QsciScintilla::setMarginWidth</a></div><div class="ttdeci">virtual void setMarginWidth(int margin, const QString &amp;s)</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a377b83523f800cc4598126417d80f74c"><div class="ttname"><a href="classQsciLexerSQL.html#a377b83523f800cc4598126417d80f74c">QsciLexerSQL::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a508eb34df3030ac28321b12d86d6670c"><div class="ttname"><a href="classQsciScintilla.html#a508eb34df3030ac28321b12d86d6670c">QsciScintilla::setAutoCompletionThreshold</a></div><div class="ttdeci">virtual void setAutoCompletionThreshold(int thresh)</div></div>
<div class="ttc" id="aclassQsciLexerCSharp_html_a8fd6da876109a8ba13892c018eaefa40"><div class="ttname"><a href="classQsciLexerCSharp.html#a8fd6da876109a8ba13892c018eaefa40">QsciLexerCSharp::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a62220018d9e9295cde64041246cfb3c4"><div class="ttname"><a href="classQsciScintilla.html#a62220018d9e9295cde64041246cfb3c4">QsciScintilla::redo</a></div><div class="ttdeci">virtual void redo()</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_aba02f4e299dd7f25cea762e9c21b48b2"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#aba02f4e299dd7f25cea762e9c21b48b2">QsciLexerCoffeeScript::stylePreprocessor</a></div><div class="ttdeci">bool stylePreprocessor() const</div><div class="ttdef"><b>Definition:</b> qscilexercoffeescript.h:226</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a37646aa8dff617d11505617002439216"><div class="ttname"><a href="classQsciScintilla.html#a37646aa8dff617d11505617002439216">QsciScintilla::positionFromLineIndex</a></div><div class="ttdeci">int positionFromLineIndex(int line, int index) const</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_aebdebbf12dc8bf264479bd570f669268"><div class="ttname"><a href="classQsciLexerCPP.html#aebdebbf12dc8bf264479bd570f669268">QsciLexerCPP::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a39e90958ae903d2f6198ec0c58f56ed9"><div class="ttname"><a href="classQsciScintillaBase.html#a39e90958ae903d2f6198ec0c58f56ed9">QsciScintillaBase::SCN_MARGINRIGHTCLICK</a></div><div class="ttdeci">void SCN_MARGINRIGHTCLICK(int position, int modifiers, int margin)</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_a7e9725132ec5521255eb7d9ac81ae853"><div class="ttname"><a href="classQsciLexerVerilog.html#a7e9725132ec5521255eb7d9ac81ae853">QsciLexerVerilog::foldComments</a></div><div class="ttdeci">bool foldComments() const</div><div class="ttdef"><b>Definition:</b> qscilexerverilog.h:191</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_a25cb4af3f28543dcd6125a79c0520447"><div class="ttname"><a href="classQsciLexerAsm.html#a25cb4af3f28543dcd6125a79c0520447">QsciLexerAsm::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a7e1e146787204eba48aa5376287de41f"><div class="ttname"><a href="classQsciScintillaBase.html#a7e1e146787204eba48aa5376287de41f">QsciScintillaBase::toMimeData</a></div><div class="ttdeci">virtual QMimeData * toMimeData(const QByteArray &amp;text, bool rectangular) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a13f22ec5a59e2e8e97a27ac24967f74d"><div class="ttname"><a href="classQsciScintillaBase.html#a13f22ec5a59e2e8e97a27ac24967f74d">QsciScintillaBase::SCN_CALLTIPCLICK</a></div><div class="ttdeci">void SCN_CALLTIPCLICK(int direction)</div></div>
<div class="ttc" id="aclassQsciLexerPO_html_a4d8011ef7e9d6401597d3a4012a809a7"><div class="ttname"><a href="classQsciLexerPO.html#a4d8011ef7e9d6401597d3a4012a809a7">QsciLexerPO::QsciLexerPO</a></div><div class="ttdeci">QsciLexerPO(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_acb05eb7e7c7cac07547a08d0628013fe"><div class="ttname"><a href="classQsciScintillaBase.html#acb05eb7e7c7cac07547a08d0628013fe">QsciScintillaBase::inputMethodEvent</a></div><div class="ttdeci">virtual void inputMethodEvent(QInputMethodEvent *event)</div><div class="ttdoc">Re-implemented to handle composed characters.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aca81f16f09dbbaf463d5926f04d4b53c"><div class="ttname"><a href="classQsciScintilla.html#aca81f16f09dbbaf463d5926f04d4b53c">QsciScintilla::cursorPositionChanged</a></div><div class="ttdeci">void cursorPositionChanged(int line, int index)</div></div>
<div class="ttc" id="aclassQsciLexerMASM_html"><div class="ttname"><a href="classQsciLexerMASM.html">QsciLexerMASM</a></div><div class="ttdoc">The QsciLexerMASM class encapsulates the Scintilla MASM lexer.</div><div class="ttdef"><b>Definition:</b> qscilexermasm.h:32</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a3493e72e97607270ca64c01b521f933f"><div class="ttname"><a href="classQsciScintilla.html#a3493e72e97607270ca64c01b521f933f">QsciScintilla::setEdgeMode</a></div><div class="ttdeci">void setEdgeMode(EdgeMode mode)</div></div>
<div class="ttc" id="aclassQsciLexerOctave_html_acca6b44f3f90599d119fb05f375cb2b8"><div class="ttname"><a href="classQsciLexerOctave.html#acca6b44f3f90599d119fb05f375cb2b8">QsciLexerOctave::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_aab16e9b7ca9a17af2af3b7ca7f14c8c4"><div class="ttname"><a href="classQsciScintillaBase.html#aab16e9b7ca9a17af2af3b7ca7f14c8c4">QsciScintillaBase::mouseDoubleClickEvent</a></div><div class="ttdeci">virtual void mouseDoubleClickEvent(QMouseEvent *e)</div><div class="ttdoc">Re-implemented to handle mouse double-clicks.</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_aed0f87e43716cf9894e27e0b90396a98"><div class="ttname"><a href="classQsciLexerTeX.html#aed0f87e43716cf9894e27e0b90396a98">QsciLexerTeX::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a6c06ccce022c08674e24a96093902b49"><div class="ttname"><a href="classQsciScintilla.html#a6c06ccce022c08674e24a96093902b49">QsciScintilla::autoCompletionSource</a></div><div class="ttdeci">AutoCompletionSource autoCompletionSource() const</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:598</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aae6392483ffb59cdb94b7bd4b8a6dec9"><div class="ttname"><a href="classQsciScintilla.html#aae6392483ffb59cdb94b7bd4b8a6dec9">QsciScintilla::setMarginMarkerMask</a></div><div class="ttdeci">virtual void setMarginMarkerMask(int margin, int mask)</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a66dc6ae74420ab3406043ff9f6f70cc4"><div class="ttname"><a href="classQsciLexerCPP.html#a66dc6ae74420ab3406043ff9f6f70cc4">QsciLexerCPP::setStylePreprocessor</a></div><div class="ttdeci">virtual void setStylePreprocessor(bool style)</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_a2e5910796ca5a3f369258718bb75c1d8"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#a2e5910796ca5a3f369258718bb75c1d8">QsciLexerCoffeeScript::wordCharacters</a></div><div class="ttdeci">const char * wordCharacters() const</div><div class="ttdoc">Returns the string of characters that comprise a word.</div></div>
<div class="ttc" id="aclassQsciLexer_html_a901cf93072b3db3ffe503eab78ae6954"><div class="ttname"><a href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">QsciLexer::colorChanged</a></div><div class="ttdeci">void colorChanged(const QColor &amp;c, int style)</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_ad5f7fc89705dd0588937b1565a6e5a26"><div class="ttname"><a href="classQsciLexerSQL.html#ad5f7fc89705dd0588937b1565a6e5a26">QsciLexerSQL::quotedIdentifiers</a></div><div class="ttdeci">bool quotedIdentifiers() const</div><div class="ttdef"><b>Definition:</b> qscilexersql.h:229</div></div>
<div class="ttc" id="aclassQsciLexer_html_a31f12624858cbb8abdc59af34b5a85c7"><div class="ttname"><a href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">QsciLexer::defaultColor</a></div><div class="ttdeci">QColor defaultColor() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a70c1bd30a1d26b2e059236a4b2835c44"><div class="ttname"><a href="classQsciScintilla.html#a70c1bd30a1d26b2e059236a4b2835c44">QsciScintilla::wordAtLineIndex</a></div><div class="ttdeci">QString wordAtLineIndex(int line, int index) const</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html_a34578c60a0d404116a5017ff454477f5"><div class="ttname"><a href="classQsciLexerProperties.html#a34578c60a0d404116a5017ff454477f5">QsciLexerProperties::~QsciLexerProperties</a></div><div class="ttdeci">virtual ~QsciLexerProperties()</div><div class="ttdoc">Destroys the QsciLexerProperties instance.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a672f2fb901048f290997cb69216b7a00"><div class="ttname"><a href="classQsciScintilla.html#a672f2fb901048f290997cb69216b7a00">QsciScintilla::setMarginsFont</a></div><div class="ttdeci">virtual void setMarginsFont(const QFont &amp;f)</div><div class="ttdoc">Set the font used in all margins to f.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a69f35ec6e80059bbb351c8f8845cd8f6"><div class="ttname"><a href="classQsciScintilla.html#a69f35ec6e80059bbb351c8f8845cd8f6">QsciScintilla::setTabDrawMode</a></div><div class="ttdeci">void setTabDrawMode(TabDrawMode mode)</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_a6443ca03dcf722445e6627e9991bb10c"><div class="ttname"><a href="classQsciLexerVHDL.html#a6443ca03dcf722445e6627e9991bb10c">QsciLexerVHDL::foldAtBegin</a></div><div class="ttdeci">bool foldAtBegin() const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ab7ed107d6ace096e9026c31145c48b41aabd3cb3735935f9be890931a34d07989"><div class="ttname"><a href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41aabd3cb3735935f9be890931a34d07989">QsciScintillaBase::SC_MARGIN_COLOUR</a></div><div class="ttdeci">@ SC_MARGIN_COLOUR</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2711</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a20439ec93f1af6b8227bdcd48a6070ec"><div class="ttname"><a href="classQsciLexerCPP.html#a20439ec93f1af6b8227bdcd48a6070ec">QsciLexerCPP::verbatimStringEscapeSequencesAllowed</a></div><div class="ttdeci">bool verbatimStringEscapeSequencesAllowed() const</div><div class="ttdef"><b>Definition:</b> qscilexercpp.h:319</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ac419a79eed86a754cfb556b3961e990d"><div class="ttname"><a href="classQsciScintilla.html#ac419a79eed86a754cfb556b3961e990d">QsciScintilla::overwriteMode</a></div><div class="ttdeci">bool overwriteMode() const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ab7ed107d6ace096e9026c31145c48b41a55a92a7661156a126c48237234251e1d"><div class="ttname"><a href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41a55a92a7661156a126c48237234251e1d">QsciScintillaBase::SC_MARGIN_NUMBER</a></div><div class="ttdeci">@ SC_MARGIN_NUMBER</div><div class="ttdoc">The margin will display line numbers.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2693</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a451bcf235c2ad7628d32940a13d22116"><div class="ttname"><a href="classQsciScintilla.html#a451bcf235c2ad7628d32940a13d22116">QsciScintilla::setFirstVisibleLine</a></div><div class="ttdeci">void setFirstVisibleLine(int linenr)</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html"><div class="ttname"><a href="classQsciLexerVHDL.html">QsciLexerVHDL</a></div><div class="ttdoc">The QsciLexerVHDL class encapsulates the Scintilla VHDL lexer.</div><div class="ttdef"><b>Definition:</b> qscilexervhdl.h:32</div></div>
<div class="ttc" id="aclassQsciLexer_html_acff58ba06195b9458a61d7ef3573c701"><div class="ttname"><a href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">QsciLexer::color</a></div><div class="ttdeci">virtual QColor color(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerNASM_html_ab3dd11c53be5a4b8a13a6e6cb67d17e1"><div class="ttname"><a href="classQsciLexerNASM.html#ab3dd11c53be5a4b8a13a6e6cb67d17e1">QsciLexerNASM::~QsciLexerNASM</a></div><div class="ttdeci">virtual ~QsciLexerNASM()</div><div class="ttdoc">Destroys the QsciLexerNASM instance.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8ec9b9a33f698fab59f46864ee9a2267"><div class="ttname"><a href="classQsciScintilla.html#a8ec9b9a33f698fab59f46864ee9a2267">QsciScintilla::annotate</a></div><div class="ttdeci">void annotate(int line, const QString &amp;text, const QsciStyle &amp;style)</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_a1412f4f04885bf9b315fbb371c54dc7c"><div class="ttname"><a href="classQsciLexerLua.html#a1412f4f04885bf9b315fbb371c54dc7c">QsciLexerLua::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexer_html_ae508c3ab4ce1f338dfff3ddf5ee7e34c"><div class="ttname"><a href="classQsciLexer.html#ae508c3ab4ce1f338dfff3ddf5ee7e34c">QsciLexer::refreshProperties</a></div><div class="ttdeci">virtual void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a3755bcaa3e90c522162a7962de464baf"><div class="ttname"><a href="classQsciScintilla.html#a3755bcaa3e90c522162a7962de464baf">QsciScintilla::clearRegisteredImages</a></div><div class="ttdeci">void clearRegisteredImages()</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_aca9a53a01d50ef44d9f5ac0fd662bf84"><div class="ttname"><a href="classQsciLexerCSS.html#aca9a53a01d50ef44d9f5ac0fd662bf84">QsciLexerCSS::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a02ad644d3bc229939e57d5e9f665a6b9"><div class="ttname"><a href="classQsciLexerPython.html#a02ad644d3bc229939e57d5e9f665a6b9">QsciLexerPython::v3BinaryOctalAllowed</a></div><div class="ttdeci">bool v3BinaryOctalAllowed() const</div><div class="ttdef"><b>Definition:</b> qscilexerpython.h:258</div></div>
<div class="ttc" id="aclassQsciLexerMatlab_html_a66c17910b9d9171d786b7c76b17276c5"><div class="ttname"><a href="classQsciLexerMatlab.html#a66c17910b9d9171d786b7c76b17276c5">QsciLexerMatlab::~QsciLexerMatlab</a></div><div class="ttdeci">virtual ~QsciLexerMatlab()</div><div class="ttdoc">Destroys the QsciLexerMatlab instance.</div></div>
<div class="ttc" id="aclassQsciLexerJava_html_a76f547a0431bb391a89efd764eb337c7"><div class="ttname"><a href="classQsciLexerJava.html#a76f547a0431bb391a89efd764eb337c7">QsciLexerJava::~QsciLexerJava</a></div><div class="ttdeci">virtual ~QsciLexerJava()</div><div class="ttdoc">Destroys the QsciLexerJava instance.</div></div>
<div class="ttc" id="aclassQsciPrinter_html_aafc924b9d8d494541b89ac8d461b4300"><div class="ttname"><a href="classQsciPrinter.html#aafc924b9d8d494541b89ac8d461b4300">QsciPrinter::magnification</a></div><div class="ttdeci">int magnification() const</div><div class="ttdef"><b>Definition:</b> qsciprinter.h:76</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a41e738411112b8f509e0b49b6fc3e318"><div class="ttname"><a href="classQsciScintillaBase.html#a41e738411112b8f509e0b49b6fc3e318">QsciScintillaBase::SCN_AUTOCCOMPLETED</a></div><div class="ttdeci">void SCN_AUTOCCOMPLETED(const char *selection, int position, int ch, int method)</div></div>
<div class="ttc" id="aclassQsciCommandSet_html"><div class="ttname"><a href="classQsciCommandSet.html">QsciCommandSet</a></div><div class="ttdoc">The QsciCommandSet class represents the set of all internal editor commands that may have keys bound.</div><div class="ttdef"><b>Definition:</b> qscicommandset.h:45</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a9ecd605284870ddbf703cf4c8c995ca6"><div class="ttname"><a href="classQsciScintillaBase.html#a9ecd605284870ddbf703cf4c8c995ca6">QsciScintillaBase::SCN_DWELLEND</a></div><div class="ttdeci">void SCN_DWELLEND(int position, int x, int y)</div></div>
<div class="ttc" id="aclassQsciLexerNASM_html"><div class="ttname"><a href="classQsciLexerNASM.html">QsciLexerNASM</a></div><div class="ttdoc">The QsciLexerNASM class encapsulates the Scintilla NASM lexer.</div><div class="ttdef"><b>Definition:</b> qscilexernasm.h:32</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ad6b00de0b5fa769ff6d30036f9d784de"><div class="ttname"><a href="classQsciScintilla.html#ad6b00de0b5fa769ff6d30036f9d784de">QsciScintilla::hasSelectedText</a></div><div class="ttdeci">bool hasSelectedText() const</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:882</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8a92ecc1db1576267f50a03d8ae93303"><div class="ttname"><a href="classQsciScintilla.html#a8a92ecc1db1576267f50a03d8ae93303">QsciScintilla::color</a></div><div class="ttdeci">QColor color() const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca940ced5307e462959ce165d8717a31d4"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca940ced5307e462959ce165d8717a31d4">QsciScintillaBase::SC_MARK_ARROW</a></div><div class="ttdeci">@ SC_MARK_ARROW</div><div class="ttdoc">A triangle pointing to the right.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2570</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_a76f950916c4638019fa3bd8c4ab601a3"><div class="ttname"><a href="classQsciLexerAsm.html#a76f950916c4638019fa3bd8c4ab601a3">QsciLexerAsm::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div></div>
<div class="ttc" id="aclassQsciLexerFortran77_html_ac631b370fc57344197a5dda058c16461"><div class="ttname"><a href="classQsciLexerFortran77.html#ac631b370fc57344197a5dda058c16461">QsciLexerFortran77::~QsciLexerFortran77</a></div><div class="ttdeci">virtual ~QsciLexerFortran77()</div><div class="ttdoc">Destroys the QsciLexerFortran77 instance.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_abdae368f2b81955c4927dc6f26fc2c77"><div class="ttname"><a href="classQsciScintillaBase.html#abdae368f2b81955c4927dc6f26fc2c77">QsciScintillaBase::SCN_MACRORECORD</a></div><div class="ttdeci">void SCN_MACRORECORD(unsigned int, unsigned long, void *)</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_ac9868e2d0efbf3602a22d8bdac12a119"><div class="ttname"><a href="classQsciLexerPerl.html#ac9868e2d0efbf3602a22d8bdac12a119">QsciLexerPerl::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_abb1544042444a8147123b5597e096ea2"><div class="ttname"><a href="classQsciLexerVerilog.html#abb1544042444a8147123b5597e096ea2">QsciLexerVerilog::wordCharacters</a></div><div class="ttdeci">const char * wordCharacters() const</div><div class="ttdoc">Returns the string of characters that comprise a word.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ac466f32c3d7e51790b6b25c864783179a98ee0a7e261ec6cac6d89ba869117546"><div class="ttname"><a href="classQsciScintilla.html#ac466f32c3d7e51790b6b25c864783179a98ee0a7e261ec6cac6d89ba869117546">QsciScintilla::AcsAll</a></div><div class="ttdeci">@ AcsAll</div><div class="ttdoc">The source is all available sources.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:121</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_a136982546f34f83f5e3dd21f67074d4d"><div class="ttname"><a href="classQsciLexerLua.html#a136982546f34f83f5e3dd21f67074d4d">QsciLexerLua::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexerCustom_html_aae1debbb0a7be0266725c0ca327b0834"><div class="ttname"><a href="classQsciLexerCustom.html#aae1debbb0a7be0266725c0ca327b0834">QsciLexerCustom::setStyling</a></div><div class="ttdeci">void setStyling(int length, int style)</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html_a2d66e4001f22b971d1d0d92953b614ba"><div class="ttname"><a href="classQsciLexerProperties.html#a2d66e4001f22b971d1d0d92953b614ba">QsciLexerProperties::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_afa54795b596b6bc9f3664865b9d76484"><div class="ttname"><a href="classQsciLexerPerl.html#afa54795b596b6bc9f3664865b9d76484">QsciLexerPerl::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a9851c2349b4140faa129b7125bee416d"><div class="ttname"><a href="classQsciScintilla.html#a9851c2349b4140faa129b7125bee416d">QsciScintilla::setAutoCompletionFillups</a></div><div class="ttdeci">void setAutoCompletionFillups(const char *fillups)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_adb45fb04c1ad8c6459fea750d8788584"><div class="ttname"><a href="classQsciScintillaBase.html#adb45fb04c1ad8c6459fea750d8788584">QsciScintillaBase::SendScintilla</a></div><div class="ttdeci">long SendScintilla(unsigned int msg, const char *lParam) const</div><div class="ttdoc">This is an overloaded member function, provided for convenience. It differs from the above function o...</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_aba150bef5f977fb65d66fcaec9c6664c"><div class="ttname"><a href="classQsciLexerSQL.html#aba150bef5f977fb65d66fcaec9c6664c">QsciLexerSQL::setDottedWords</a></div><div class="ttdeci">void setDottedWords(bool enable)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_af3a619a5e59cef000f0b550e809c94de"><div class="ttname"><a href="classQsciScintillaBase.html#af3a619a5e59cef000f0b550e809c94de">QsciScintillaBase::SCN_SAVEPOINTLEFT</a></div><div class="ttdeci">void SCN_SAVEPOINTLEFT()</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_a409c5a8e561b153aca122ad5e0bedc82"><div class="ttname"><a href="classQsciLexerTeX.html#a409c5a8e561b153aca122ad5e0bedc82">QsciLexerTeX::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_a48e7f3a456fcb347ee96a2c6a1f07231"><div class="ttname"><a href="classQsciLexerHTML.html#a48e7f3a456fcb347ee96a2c6a1f07231">QsciLexerHTML::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aa46e60536be6297de6ca1fb16d36cd51"><div class="ttname"><a href="classQsciScintilla.html#aa46e60536be6297de6ca1fb16d36cd51">QsciScintilla::setIndentation</a></div><div class="ttdeci">virtual void setIndentation(int line, int indentation)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca3582c3195c0533bca604a874ee1ecab8"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca3582c3195c0533bca604a874ee1ecab8">QsciScintillaBase::SC_MARK_BOXPLUSCONNECTED</a></div><div class="ttdeci">@ SC_MARK_BOXPLUSCONNECTED</div><div class="ttdoc">A drawn plus sign in a connected box.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2605</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae7310729b1be2aa937a22036f5d95b51"><div class="ttname"><a href="classQsciScintilla.html#ae7310729b1be2aa937a22036f5d95b51">QsciScintilla::append</a></div><div class="ttdeci">virtual void append(const QString &amp;text)</div></div>
<div class="ttc" id="aclassQsciLexerMakefile_html_a0b10a59a79011e968a1ef9512d41c3d6"><div class="ttname"><a href="classQsciLexerMakefile.html#a0b10a59a79011e968a1ef9512d41c3d6">QsciLexerMakefile::~QsciLexerMakefile</a></div><div class="ttdeci">virtual ~QsciLexerMakefile()</div><div class="ttdoc">Destroys the QsciLexerMakefile instance.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ac8a72227fc8efff78505733d1663f927"><div class="ttname"><a href="classQsciScintillaBase.html#ac8a72227fc8efff78505733d1663f927">QsciScintillaBase::changeEvent</a></div><div class="ttdeci">virtual void changeEvent(QEvent *e)</div><div class="ttdoc">\reimp</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aef584b6c5b89736ad311ad20aa32269d"><div class="ttname"><a href="classQsciScintilla.html#aef584b6c5b89736ad311ad20aa32269d">QsciScintilla::removeSelectedText</a></div><div class="ttdeci">virtual void removeSelectedText()</div></div>
<div class="ttc" id="aclassQsciLexer_html_a3484599b6db81b8392ab6cd4f50ab291"><div class="ttname"><a href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">QsciLexer::setFont</a></div><div class="ttdeci">virtual void setFont(const QFont &amp;f, int style=-1)</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_a5f77be4cb83422d47220c5b38d9f0a99"><div class="ttname"><a href="classQsciLexerCSS.html#a5f77be4cb83422d47220c5b38d9f0a99">QsciLexerCSS::setFoldComments</a></div><div class="ttdeci">virtual void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerXML_html_a7bbfdb6b269b6e52791fcbf1df60731e"><div class="ttname"><a href="classQsciLexerXML.html#a7bbfdb6b269b6e52791fcbf1df60731e">QsciLexerXML::setScriptsStyled</a></div><div class="ttdeci">void setScriptsStyled(bool styled)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_abce274ef71035c67baadaa167a1fe5a7"><div class="ttname"><a href="classQsciScintillaBase.html#abce274ef71035c67baadaa167a1fe5a7">QsciScintillaBase::canInsertFromMimeData</a></div><div class="ttdeci">virtual bool canInsertFromMimeData(const QMimeData *source) const</div></div>
<div class="ttc" id="aclassQsciLexerHex_html_a9526bf662c14719ecdfcf3ac8f1cc090"><div class="ttname"><a href="classQsciLexerHex.html#a9526bf662c14719ecdfcf3ac8f1cc090">QsciLexerHex::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerCSharp_html_a92e6554430736b20b147b7290d4bfe16"><div class="ttname"><a href="classQsciLexerCSharp.html#a92e6554430736b20b147b7290d4bfe16">QsciLexerCSharp::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciStyle_html_a61582248f6b7276db9b4a1f9582c3828"><div class="ttname"><a href="classQsciStyle.html#a61582248f6b7276db9b4a1f9582c3828">QsciStyle::style</a></div><div class="ttdeci">int style() const</div><div class="ttdef"><b>Definition:</b> qscistyle.h:88</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ac4d1c67938c75806b2c139d0779d0478"><div class="ttname"><a href="classQsciScintilla.html#ac4d1c67938c75806b2c139d0779d0478">QsciScintilla::WrapVisualFlag</a></div><div class="ttdeci">WrapVisualFlag</div><div class="ttdoc">This enum defines the different line wrap visual flags.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:493</div></div>
<div class="ttc" id="aclassQsciLexerXML_html_a1cf58cba78405397f793b6a9aff64035"><div class="ttname"><a href="classQsciLexerXML.html#a1cf58cba78405397f793b6a9aff64035">QsciLexerXML::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciAPIs_html_af46ca05571eb676d3aa65b080fb406c5"><div class="ttname"><a href="classQsciAPIs.html#af46ca05571eb676d3aa65b080fb406c5">QsciAPIs::add</a></div><div class="ttdeci">void add(const QString &amp;entry)</div></div>
<div class="ttc" id="aclassQsciStyle_html_af7e052d08efd3a677f810c8e4116dafc"><div class="ttname"><a href="classQsciStyle.html#af7e052d08efd3a677f810c8e4116dafc">QsciStyle::setChangeable</a></div><div class="ttdeci">void setChangeable(bool changeable)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a4c6a412b7d066b9fce90f3976350348c"><div class="ttname"><a href="classQsciScintilla.html#a4c6a412b7d066b9fce90f3976350348c">QsciScintilla::setHotspotForegroundColor</a></div><div class="ttdeci">void setHotspotForegroundColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_a7ea95f77a5a0ae539b306473c3b808db"><div class="ttname"><a href="classQsciLexerVHDL.html#a7ea95f77a5a0ae539b306473c3b808db">QsciLexerVHDL::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_aa20e183e0b38b5076aa9e883c5283791"><div class="ttname"><a href="classQsciLexerCPP.html#aa20e183e0b38b5076aa9e883c5283791">QsciLexerCPP::dollarsAllowed</a></div><div class="ttdeci">bool dollarsAllowed() const</div><div class="ttdef"><b>Definition:</b> qscilexercpp.h:264</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_ac331bbae026859d8020ac5a6efd8fed1"><div class="ttname"><a href="classQsciLexerCPP.html#ac331bbae026859d8020ac5a6efd8fed1">QsciLexerCPP::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_adca3e8b2b7d4d0bf65ad23322f64f6ef"><div class="ttname"><a href="classQsciScintilla.html#adca3e8b2b7d4d0bf65ad23322f64f6ef">QsciScintilla::setBackspaceUnindents</a></div><div class="ttdeci">virtual void setBackspaceUnindents(bool unindent)</div></div>
<div class="ttc" id="aclassQsciLexerIDL_html_ad8a83d778cced2584c895d2b9b07c33b"><div class="ttname"><a href="classQsciLexerIDL.html#ad8a83d778cced2584c895d2b9b07c33b">QsciLexerIDL::~QsciLexerIDL</a></div><div class="ttdeci">virtual ~QsciLexerIDL()</div><div class="ttdoc">Destroys the QsciLexerIDL instance.</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_a59c9b8ff5d698d7e7e03ec2655a24764"><div class="ttname"><a href="classQsciLexerHTML.html#a59c9b8ff5d698d7e7e03ec2655a24764">QsciLexerHTML::setDjangoTemplates</a></div><div class="ttdeci">void setDjangoTemplates(bool enabled)</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a14705cac9643949facd57641e0892fb0"><div class="ttname"><a href="classQsciLexerPerl.html#a14705cac9643949facd57641e0892fb0">QsciLexerPerl::setFoldAtElse</a></div><div class="ttdeci">void setFoldAtElse(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a94a66b0c8459f5a407eef6783cd6d80b"><div class="ttname"><a href="classQsciScintillaBase.html#a94a66b0c8459f5a407eef6783cd6d80b">QsciScintillaBase::SendScintilla</a></div><div class="ttdeci">long SendScintilla(unsigned int msg, long wParam) const</div><div class="ttdoc">This is an overloaded member function, provided for convenience. It differs from the above function o...</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_a3ba9e8000c3896e453b79dcfce08b146"><div class="ttname"><a href="classQsciLexerJSON.html#a3ba9e8000c3896e453b79dcfce08b146">QsciLexerJSON::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerJava_html_abaa737931800774e8067765d6cdc9a5d"><div class="ttname"><a href="classQsciLexerJava.html#abaa737931800774e8067765d6cdc9a5d">QsciLexerJava::QsciLexerJava</a></div><div class="ttdeci">QsciLexerJava(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214caf591d473d118d6fa98adf5e73fd9c61d"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214caf591d473d118d6fa98adf5e73fd9c61d">QsciScintillaBase::SC_MARK_LCORNER</a></div><div class="ttdeci">@ SC_MARK_LCORNER</div><div class="ttdoc">A bottom left corner drawn in the background colour.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2595</div></div>
<div class="ttc" id="aclassQsciLexerYAML_html"><div class="ttname"><a href="classQsciLexerYAML.html">QsciLexerYAML</a></div><div class="ttdoc">The QsciLexerYAML class encapsulates the Scintilla YAML lexer.</div><div class="ttdef"><b>Definition:</b> qscilexeryaml.h:32</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_ac70e03bceba5de91104b85edd00e1a68"><div class="ttname"><a href="classQsciLexerCSS.html#ac70e03bceba5de91104b85edd00e1a68">QsciLexerCSS::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_abf0e76eca3bc604650cc20d4fc110c7f"><div class="ttname"><a href="classQsciLexerPython.html#abf0e76eca3bc604650cc20d4fc110c7f">QsciLexerPython::~QsciLexerPython</a></div><div class="ttdeci">virtual ~QsciLexerPython()</div><div class="ttdoc">Destroys the QsciLexerPython instance.</div></div>
<div class="ttc" id="aclassQsciLexerBatch_html_a142446dc4954e057b2d7de11fe3e25e0"><div class="ttname"><a href="classQsciLexerBatch.html#a142446dc4954e057b2d7de11fe3e25e0">QsciLexerBatch::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexer_html_abccc4e010b724df1a7b5c5f3bce29501"><div class="ttname"><a href="classQsciLexer.html#abccc4e010b724df1a7b5c5f3bce29501">QsciLexer::writeProperties</a></div><div class="ttdeci">virtual bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_aebb96727a845f9547a60848f6163d461"><div class="ttname"><a href="classQsciLexerVerilog.html#aebb96727a845f9547a60848f6163d461">QsciLexerVerilog::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexer_html_ac7cf70f76eb03d6d475985cc4b884b0e"><div class="ttname"><a href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">QsciLexer::defaultFont</a></div><div class="ttdeci">QFont defaultFont() const</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_a0f1e5402dce043de42ded75f5826588f"><div class="ttname"><a href="classQsciLexerPostScript.html#a0f1e5402dce043de42ded75f5826588f">QsciLexerPostScript::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a24c82f4e0483ba0c13b8bf046b8c00b9"><div class="ttname"><a href="classQsciLexerD.html#a24c82f4e0483ba0c13b8bf046b8c00b9">QsciLexerD::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciLexerIDL_html_a36cce16a8abf9455e397bbec012c9838"><div class="ttname"><a href="classQsciLexerIDL.html#a36cce16a8abf9455e397bbec012c9838">QsciLexerIDL::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div><div class="ttdoc">Returns the foreground colour of the text for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_af7088827cec6904663d94507a199d19a"><div class="ttname"><a href="classQsciScintilla.html#af7088827cec6904663d94507a199d19a">QsciScintilla::indent</a></div><div class="ttdeci">virtual void indent(int line)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8f3899166ef067d2780867d154539267"><div class="ttname"><a href="classQsciScintilla.html#a8f3899166ef067d2780867d154539267">QsciScintilla::convertEols</a></div><div class="ttdeci">void convertEols(EolMode mode)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a6c892370b4ee3afd2eef080ee8c25fde"><div class="ttname"><a href="classQsciScintillaBase.html#a6c892370b4ee3afd2eef080ee8c25fde">QsciScintillaBase::SendScintilla</a></div><div class="ttdeci">long SendScintilla(unsigned int msg, long cpMin, long cpMax, char *lpstrText) const</div><div class="ttdoc">This is an overloaded member function, provided for convenience. It differs from the above function o...</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a04780d47f799c56b6af0a10b91875045"><div class="ttname"><a href="classQsciScintilla.html#a04780d47f799c56b6af0a10b91875045">QsciScintilla::findFirst</a></div><div class="ttdeci">virtual bool findFirst(const QString &amp;expr, bool re, bool cs, bool wo, bool wrap, bool forward=true, int line=-1, int index=-1, bool show=true, bool posix=false, bool cxx11=false)</div></div>
<div class="ttc" id="aclassQsciLexer_html_a8b1bb1261e7b9701c62bbe4f1d171e06"><div class="ttname"><a href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">QsciLexer::blockLookback</a></div><div class="ttdeci">virtual int blockLookback() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a4d4634e48eedb5b12bafe8c6fa6c41f7"><div class="ttname"><a href="classQsciScintilla.html#a4d4634e48eedb5b12bafe8c6fa6c41f7">QsciScintilla::clearFolds</a></div><div class="ttdeci">void clearFolds()</div></div>
<div class="ttc" id="aclassQsciLexer_html_a8a3adc7b5c8926e097e6be4340bee920"><div class="ttname"><a href="classQsciLexer.html#a8a3adc7b5c8926e097e6be4340bee920">QsciLexer::language</a></div><div class="ttdeci">virtual const char * language() const =0</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a37f2cddeeef588533be46798ae18ffab"><div class="ttname"><a href="classQsciScintilla.html#a37f2cddeeef588533be46798ae18ffab">QsciScintilla::setCaretLineVisible</a></div><div class="ttdeci">virtual void setCaretLineVisible(bool enable)</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_a479ca70b474910355294d1fcec011572"><div class="ttname"><a href="classQsciLexerTeX.html#a479ca70b474910355294d1fcec011572">QsciLexerTeX::setProcessIf</a></div><div class="ttdeci">void setProcessIf(bool enable)</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a1b8f36843f4abe6ec3ee75205b5b0111"><div class="ttname"><a href="classQsciLexerPython.html#a1b8f36843f4abe6ec3ee75205b5b0111">QsciLexerPython::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_ad476092b3970fe44068dd023f8becc96"><div class="ttname"><a href="classQsciLexerVerilog.html#ad476092b3970fe44068dd023f8becc96">QsciLexerVerilog::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ace4acd9ee0d1c3e31099d65cdd8219b2"><div class="ttname"><a href="classQsciScintilla.html#ace4acd9ee0d1c3e31099d65cdd8219b2">QsciScintilla::setIndentationGuidesForegroundColor</a></div><div class="ttdeci">virtual void setIndentationGuidesForegroundColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html"><div class="ttname"><a href="classQsciLexerJSON.html">QsciLexerJSON</a></div><div class="ttdoc">The QsciLexerJSON class encapsulates the Scintilla JSON lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerjson.h:32</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_a1fa70c8e86dd88d34508fc652d30f3f7"><div class="ttname"><a href="classQsciLexerCSS.html#a1fa70c8e86dd88d34508fc652d30f3f7">QsciLexerCSS::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_a17ff342a5c1d94ce760a3dc02cfcda1d"><div class="ttname"><a href="classQsciLexerVerilog.html#a17ff342a5c1d94ce760a3dc02cfcda1d">QsciLexerVerilog::setFoldCompact</a></div><div class="ttdeci">void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_ac1665f22a91f143e6e6fb46b02e7b109"><div class="ttname"><a href="classQsciLexerBash.html#ac1665f22a91f143e6e6fb46b02e7b109">QsciLexerBash::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_aa37ea54c5e39721b866c25b0e0335591"><div class="ttname"><a href="classQsciLexerCPP.html#aa37ea54c5e39721b866c25b0e0335591">QsciLexerCPP::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a511a4f492a9912df3d430fba33b67d5c"><div class="ttname"><a href="classQsciScintilla.html#a511a4f492a9912df3d430fba33b67d5c">QsciScintilla::setUnmatchedBraceForegroundColor</a></div><div class="ttdeci">void setUnmatchedBraceForegroundColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a338a09c79011b57a842c581aa2556b4c"><div class="ttname"><a href="classQsciLexerSQL.html#a338a09c79011b57a842c581aa2556b4c">QsciLexerSQL::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a6d6f13610560a2c281f638f3a40046f6"><div class="ttname"><a href="classQsciScintillaBase.html#a6d6f13610560a2c281f638f3a40046f6">QsciScintillaBase::mousePressEvent</a></div><div class="ttdeci">virtual void mousePressEvent(QMouseEvent *e)</div><div class="ttdoc">Re-implemented to handle mouse presses.</div></div>
<div class="ttc" id="aclassQsciLexerDiff_html"><div class="ttname"><a href="classQsciLexerDiff.html">QsciLexerDiff</a></div><div class="ttdoc">The QsciLexerDiff class encapsulates the Scintilla Diff lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerdiff.h:33</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html"><div class="ttname"><a href="classQsciLexerPOV.html">QsciLexerPOV</a></div><div class="ttdoc">The QsciLexerPOV class encapsulates the Scintilla POV lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerpov.h:32</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a0436f412bb6c83fe195ea2eb3c058154"><div class="ttname"><a href="classQsciLexerD.html#a0436f412bb6c83fe195ea2eb3c058154">QsciLexerD::wordCharacters</a></div><div class="ttdeci">const char * wordCharacters() const</div><div class="ttdoc">Returns the string of characters that comprise a word.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_add035b6176dbe36b3c975f05573c0e61"><div class="ttname"><a href="classQsciScintillaBase.html#add035b6176dbe36b3c975f05573c0e61">QsciScintillaBase::SendScintilla</a></div><div class="ttdeci">long SendScintilla(unsigned int msg, int wParam) const</div><div class="ttdoc">This is an overloaded member function, provided for convenience. It differs from the above function o...</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a0e5afa1027b99648caeb70ed8423af2d"><div class="ttname"><a href="classQsciLexerD.html#a0e5afa1027b99648caeb70ed8423af2d">QsciLexerD::foldAtElse</a></div><div class="ttdeci">bool foldAtElse() const</div></div>
<div class="ttc" id="aclassQsciLexerFortran_html"><div class="ttname"><a href="classQsciLexerFortran.html">QsciLexerFortran</a></div><div class="ttdoc">The QsciLexerFortran class encapsulates the Scintilla Fortran lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerfortran.h:32</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_a388e532d847652dbf18207593e236e5e"><div class="ttname"><a href="classQsciLexerCSS.html#a388e532d847652dbf18207593e236e5e">QsciLexerCSS::setLessLanguage</a></div><div class="ttdeci">void setLessLanguage(bool enabled)</div></div>
<div class="ttc" id="aclassQsciAPIs_html_a0a080d197e8226117a626c7b4b68b32d"><div class="ttname"><a href="classQsciAPIs.html#a0a080d197e8226117a626c7b4b68b32d">QsciAPIs::defaultPreparedName</a></div><div class="ttdeci">QString defaultPreparedName() const</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a3df48961344c5133ad595a555bbb8e55"><div class="ttname"><a href="classQsciLexerD.html#a3df48961344c5133ad595a555bbb8e55">QsciLexerD::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a9fc58fb17acc5e669780cb870d633514"><div class="ttname"><a href="classQsciLexerD.html#a9fc58fb17acc5e669780cb870d633514">QsciLexerD::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexer_html_ac04ade8be901b67af681e5e3516c0946"><div class="ttname"><a href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">QsciLexer::fontChanged</a></div><div class="ttdeci">void fontChanged(const QFont &amp;f, int style)</div></div>
<div class="ttc" id="aclassQsciLexerTCL_html_abceb6f3cf78367b7bc370265d7776bf1"><div class="ttname"><a href="classQsciLexerTCL.html#abceb6f3cf78367b7bc370265d7776bf1">QsciLexerTCL::setFoldComments</a></div><div class="ttdeci">void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_aecbd103b6dff15873e60cdf48e634a4a"><div class="ttname"><a href="classQsciLexerCSS.html#aecbd103b6dff15873e60cdf48e634a4a">QsciLexerCSS::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_a5bf7d01e2764c51b106d6524959ad032"><div class="ttname"><a href="classQsciLexerAsm.html#a5bf7d01e2764c51b106d6524959ad032">QsciLexerAsm::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a721a1879cabaa76883ae1a02a34a76e8"><div class="ttname"><a href="classQsciScintillaBase.html#a721a1879cabaa76883ae1a02a34a76e8">QsciScintillaBase::SCN_AUTOCSELECTIONCHANGE</a></div><div class="ttdeci">void SCN_AUTOCSELECTIONCHANGE(const char *selection, int id, int position)</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_afc0aaf4300e9ca02eb8fa49328bbe8d8"><div class="ttname"><a href="classQsciLexerPython.html#afc0aaf4300e9ca02eb8fa49328bbe8d8">QsciLexerPython::setFoldQuotes</a></div><div class="ttdeci">virtual void setFoldQuotes(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ab26d156ff430e904e8f92d3dad9730bc"><div class="ttname"><a href="classQsciScintilla.html#ab26d156ff430e904e8f92d3dad9730bc">QsciScintilla::setReadOnly</a></div><div class="ttdeci">virtual void setReadOnly(bool ro)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a957eaab9ac1785eb043fb83f703a0b57"><div class="ttname"><a href="classQsciScintilla.html#a957eaab9ac1785eb043fb83f703a0b57">QsciScintilla::setTabIndents</a></div><div class="ttdeci">virtual void setTabIndents(bool indent)</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_a7fd3e6c1faee7c7986db2ec4c0b793ae"><div class="ttname"><a href="classQsciLexerVerilog.html#a7fd3e6c1faee7c7986db2ec4c0b793ae">QsciLexerVerilog::~QsciLexerVerilog</a></div><div class="ttdeci">virtual ~QsciLexerVerilog()</div><div class="ttdoc">Destroys the QsciLexerVerilog instance.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a10c8d8f5e97fb5ef86ea351407fe1023"><div class="ttname"><a href="classQsciScintilla.html#a10c8d8f5e97fb5ef86ea351407fe1023">QsciScintilla::selectedText</a></div><div class="ttdeci">QString selectedText() const</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a71fd025ad904aa51a6127f43099805ad"><div class="ttname"><a href="classQsciLexerPascal.html#a71fd025ad904aa51a6127f43099805ad">QsciLexerPascal::smartHighlighting</a></div><div class="ttdeci">bool smartHighlighting() const</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_aa5f12cd587bf1b8db68813601cb57e5b"><div class="ttname"><a href="classQsciLexerPostScript.html#aa5f12cd587bf1b8db68813601cb57e5b">QsciLexerPostScript::~QsciLexerPostScript</a></div><div class="ttdeci">virtual ~QsciLexerPostScript()</div><div class="ttdoc">Destroys the QsciLexerPostScript instance.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae9bbf9fa6fad6f8c9c5c9181b5dc2d45"><div class="ttname"><a href="classQsciScintilla.html#ae9bbf9fa6fad6f8c9c5c9181b5dc2d45">QsciScintilla::clearEdgeColumns</a></div><div class="ttdeci">void clearEdgeColumns()</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ad8424876c29b1a77fd1df45a534722d1"><div class="ttname"><a href="classQsciScintilla.html#ad8424876c29b1a77fd1df45a534722d1">QsciScintilla::setWrapIndentMode</a></div><div class="ttdeci">void setWrapIndentMode(WrapIndentMode mode)</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a8c0952bb621cdf048b00191674824a87"><div class="ttname"><a href="classQsciLexerSQL.html#a8c0952bb621cdf048b00191674824a87">QsciLexerSQL::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a38bf5a8680df52675e5299fccbbc7181"><div class="ttname"><a href="classQsciScintilla.html#a38bf5a8680df52675e5299fccbbc7181">QsciScintilla::registerImage</a></div><div class="ttdeci">void registerImage(int id, const QPixmap &amp;pm)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ab7ed107d6ace096e9026c31145c48b41af99d2ba5aa3873f646a8eac1a889de6a"><div class="ttname"><a href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41af99d2ba5aa3873f646a8eac1a889de6a">QsciScintillaBase::SC_MARGIN_RTEXT</a></div><div class="ttdeci">@ SC_MARGIN_RTEXT</div><div class="ttdoc">The margin will display right justified text.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2707</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aa805f90f3bbe067299e9ab8902eafbf3"><div class="ttname"><a href="classQsciScintilla.html#aa805f90f3bbe067299e9ab8902eafbf3">QsciScintilla::setPaper</a></div><div class="ttdeci">virtual void setPaper(const QColor &amp;c)</div></div>
<div class="ttc" id="aclassQsciLexerJavaScript_html_a5245587f4db1c40ad90898a7712094ed"><div class="ttname"><a href="classQsciLexerJavaScript.html#a5245587f4db1c40ad90898a7712094ed">QsciLexerJavaScript::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html"><div class="ttname"><a href="classQsciLexerCPP.html">QsciLexerCPP</a></div><div class="ttdoc">The QsciLexerCPP class encapsulates the Scintilla C++ lexer.</div><div class="ttdef"><b>Definition:</b> qscilexercpp.h:33</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a4fe2c0ce4d6fd17617c266983537f5b6"><div class="ttname"><a href="classQsciScintilla.html#a4fe2c0ce4d6fd17617c266983537f5b6">QsciScintilla::paper</a></div><div class="ttdeci">QColor paper() const</div></div>
<div class="ttc" id="aclassQsciLexer_html_a8e294eba77713f516acbcebc10af1493"><div class="ttname"><a href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">QsciLexer::blockEnd</a></div><div class="ttdeci">virtual const char * blockEnd(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciPrinter_html"><div class="ttname"><a href="classQsciPrinter.html">QsciPrinter</a></div><div class="ttdoc">The QsciPrinter class is a sub-class of the Qt QPrinter class that is able to print the text of a Sci...</div><div class="ttdef"><b>Definition:</b> qsciprinter.h:51</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_a1379abf89d88a2dd7854f957b28656c5"><div class="ttname"><a href="classQsciLexerHTML.html#a1379abf89d88a2dd7854f957b28656c5">QsciLexerHTML::djangoTemplates</a></div><div class="ttdeci">bool djangoTemplates() const</div><div class="ttdef"><b>Definition:</b> qscilexerhtml.h:435</div></div>
<div class="ttc" id="aclassQsciLexerFortran77_html_ae73ce9660679076bcd4b93ef3712586a"><div class="ttname"><a href="classQsciLexerFortran77.html#ae73ce9660679076bcd4b93ef3712586a">QsciLexerFortran77::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_ae2d2e6936f7b0f6f9b891ac14dff7bc0"><div class="ttname"><a href="classQsciLexerVHDL.html#ae2d2e6936f7b0f6f9b891ac14dff7bc0">QsciLexerVHDL::foldComments</a></div><div class="ttdeci">bool foldComments() const</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a23c6488e2416d54f6a4ec84015d860ec"><div class="ttname"><a href="classQsciLexerCPP.html#a23c6488e2416d54f6a4ec84015d860ec">QsciLexerCPP::highlightTripleQuotedStrings</a></div><div class="ttdeci">bool highlightTripleQuotedStrings() const</div><div class="ttdef"><b>Definition:</b> qscilexercpp.h:275</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html"><div class="ttname"><a href="classQsciLexerPerl.html">QsciLexerPerl</a></div><div class="ttdoc">The QsciLexerPerl class encapsulates the Scintilla Perl lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerperl.h:33</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a36359d3e1cb6037b561f95fccf16881e"><div class="ttname"><a href="classQsciLexerPerl.html#a36359d3e1cb6037b561f95fccf16881e">QsciLexerPerl::QsciLexerPerl</a></div><div class="ttdeci">QsciLexerPerl(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_a2db5e63ff4667a3f8e9df24a0accdf3d"><div class="ttname"><a href="classQsciLexerLua.html#a2db5e63ff4667a3f8e9df24a0accdf3d">QsciLexerLua::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_af63627804eeffc4f0f1290181cda7781"><div class="ttname"><a href="classQsciScintilla.html#af63627804eeffc4f0f1290181cda7781">QsciScintilla::setIndicatorOutlineColor</a></div><div class="ttdeci">void setIndicatorOutlineColor(const QColor &amp;col, int indicatorNumber=-1)</div></div>
<div class="ttc" id="aclassQsciCommand_html_ae6949756a800e31f1d279aa753060966"><div class="ttname"><a href="classQsciCommand.html#ae6949756a800e31f1d279aa753060966">QsciCommand::alternateKey</a></div><div class="ttdeci">int alternateKey() const</div><div class="ttdef"><b>Definition:</b> qscicommand.h:383</div></div>
<div class="ttc" id="aclassQsciLexerFortran77_html_a2784362f75607dc575d42a3ddf43bcf9"><div class="ttname"><a href="classQsciLexerFortran77.html#a2784362f75607dc575d42a3ddf43bcf9">QsciLexerFortran77::QsciLexerFortran77</a></div><div class="ttdeci">QsciLexerFortran77(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a044b772f07f8d5218170a11db05cc3d5"><div class="ttname"><a href="classQsciScintilla.html#a044b772f07f8d5218170a11db05cc3d5">QsciScintilla::edgeColumn</a></div><div class="ttdeci">int edgeColumn() const</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a386c817d87735b2dd347735cb264d548"><div class="ttname"><a href="classQsciLexerPerl.html#a386c817d87735b2dd347735cb264d548">QsciLexerPerl::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_af2cc3652d35b4d0ec1d8c9ac18e2225e"><div class="ttname"><a href="classQsciScintillaBase.html#af2cc3652d35b4d0ec1d8c9ac18e2225e">QsciScintillaBase::SCEN_CHANGE</a></div><div class="ttdeci">void SCEN_CHANGE()</div><div class="ttdoc">This signal is emitted when the document has changed for any reason.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ab6643f6fe8cec6f3d7e14126fd52340d"><div class="ttname"><a href="classQsciScintilla.html#ab6643f6fe8cec6f3d7e14126fd52340d">QsciScintilla::document</a></div><div class="ttdeci">QsciDocument document() const</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:710</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a3e90db838034f7404e65b2e284403604"><div class="ttname"><a href="classQsciLexerPerl.html#a3e90db838034f7404e65b2e284403604">QsciLexerPerl::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8696968d0a32b07bfc77fb0701c5bf62"><div class="ttname"><a href="classQsciScintilla.html#a8696968d0a32b07bfc77fb0701c5bf62">QsciScintilla::textChanged</a></div><div class="ttdeci">void textChanged()</div><div class="ttdoc">This signal is emitted whenever the text in the text edit changes.</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_a6d22aebdf6475acb8d9aa18c244bd9cc"><div class="ttname"><a href="classQsciLexerJSON.html#a6d22aebdf6475acb8d9aa18c244bd9cc">QsciLexerJSON::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerBatch_html"><div class="ttname"><a href="classQsciLexerBatch.html">QsciLexerBatch</a></div><div class="ttdoc">The QsciLexerBatch class encapsulates the Scintilla batch file lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerbatch.h:33</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a84118aff26655dcc4313d26d57d5f4fc"><div class="ttname"><a href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fc">QsciLexerPython::IndentationWarning</a></div><div class="ttdeci">IndentationWarning</div><div class="ttdef"><b>Definition:</b> qscilexerpython.h:104</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_a003413f4436ff46553e10db632496288"><div class="ttname"><a href="classQsciLexerVerilog.html#a003413f4436ff46553e10db632496288">QsciLexerVerilog::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_aff36eb2ba5df9c4998eb9c8311f14de5"><div class="ttname"><a href="classQsciLexerRuby.html#aff36eb2ba5df9c4998eb9c8311f14de5">QsciLexerRuby::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_af41d62ccd061b840e3eb2e9e2b26d6f5"><div class="ttname"><a href="classQsciLexerVHDL.html#af41d62ccd061b840e3eb2e9e2b26d6f5">QsciLexerVHDL::setFoldComments</a></div><div class="ttdeci">virtual void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae76354288b8e5c2bc6f7a7e7bd97e44e"><div class="ttname"><a href="classQsciScintilla.html#ae76354288b8e5c2bc6f7a7e7bd97e44e">QsciScintilla::setIndicatorHoverStyle</a></div><div class="ttdeci">void setIndicatorHoverStyle(IndicatorStyle style, int indicatorNumber=-1)</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_ad0939852605ee45ce62f70647d47147b"><div class="ttname"><a href="classQsciLexerCPP.html#ad0939852605ee45ce62f70647d47147b">QsciLexerCPP::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div><div class="ttdef"><b>Definition:</b> qscilexercpp.h:242</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a85c009c5ccf84fc64726bb2c3b11bdec"><div class="ttname"><a href="classQsciLexerPascal.html#a85c009c5ccf84fc64726bb2c3b11bdec">QsciLexerPascal::setFoldPreprocessor</a></div><div class="ttdeci">virtual void setFoldPreprocessor(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_af8cedca829a47783199ab0e1dfc160d5"><div class="ttname"><a href="classQsciScintilla.html#af8cedca829a47783199ab0e1dfc160d5">QsciScintilla::markerFindNext</a></div><div class="ttdeci">int markerFindNext(int linenr, unsigned mask) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a9792eb38e5e8dce8a4bd32b2224c73d4"><div class="ttname"><a href="classQsciScintilla.html#a9792eb38e5e8dce8a4bd32b2224c73d4">QsciScintilla::moveToMatchingBrace</a></div><div class="ttdeci">virtual void moveToMatchingBrace()</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8c8e4776767cc88b945f50f07d235770"><div class="ttname"><a href="classQsciScintilla.html#a8c8e4776767cc88b945f50f07d235770">QsciScintilla::setColor</a></div><div class="ttdeci">virtual void setColor(const QColor &amp;c)</div></div>
<div class="ttc" id="aclassQsciMacro_html_a783f17e12ca844655568b5718aa26a35"><div class="ttname"><a href="classQsciMacro.html#a783f17e12ca844655568b5718aa26a35">QsciMacro::endRecording</a></div><div class="ttdeci">virtual void endRecording()</div><div class="ttdoc">Stop recording user commands.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a3333f3a47163153c1bd7db1a362b8974"><div class="ttname"><a href="classQsciScintilla.html#a3333f3a47163153c1bd7db1a362b8974">QsciScintilla::IndicatorStyle</a></div><div class="ttdeci">IndicatorStyle</div><div class="ttdoc">This enum defines the different indicator styles.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:232</div></div>
<div class="ttc" id="aclassQsciLexer_html_af6cc5bb9d9421d806e9941d018030068"><div class="ttname"><a href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">QsciLexer::~QsciLexer</a></div><div class="ttdeci">virtual ~QsciLexer()</div><div class="ttdoc">Destroy the QSciLexer.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a1052b3e64558fe29271c0829e6d0fdda"><div class="ttname"><a href="classQsciScintilla.html#a1052b3e64558fe29271c0829e6d0fdda">QsciScintilla::wordCharacters</a></div><div class="ttdeci">const char * wordCharacters() const</div></div>
<div class="ttc" id="aclassQsciLexerMarkdown_html_a2f1340e861947f7c8c4299b1c9ded5a5"><div class="ttname"><a href="classQsciLexerMarkdown.html#a2f1340e861947f7c8c4299b1c9ded5a5">QsciLexerMarkdown::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciStyle_html_a8912da5c6b95404e4642593db1b65d4c"><div class="ttname"><a href="classQsciStyle.html#a8912da5c6b95404e4642593db1b65d4c">QsciStyle::paper</a></div><div class="ttdeci">QColor paper() const</div><div class="ttdef"><b>Definition:</b> qscistyle.h:120</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a2c1974c2bdc9c0f2923c28b66afe811f"><div class="ttname"><a href="classQsciScintilla.html#a2c1974c2bdc9c0f2923c28b66afe811f">QsciScintilla::setCaretWidth</a></div><div class="ttdeci">virtual void setCaretWidth(int width)</div></div>
<div class="ttc" id="aclassQsciLexerHex_html_a6397d2675439df63b7d1e789185ad224"><div class="ttname"><a href="classQsciLexerHex.html#a6397d2675439df63b7d1e789185ad224">QsciLexerHex::QsciLexerHex</a></div><div class="ttdeci">QsciLexerHex(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a7245335691700f82db41016d257d63cc"><div class="ttname"><a href="classQsciScintilla.html#a7245335691700f82db41016d257d63cc">QsciScintilla::setHotspotWrap</a></div><div class="ttdeci">void setHotspotWrap(bool enable)</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_a7f8da8d7fe8301cd49926b896bf5e286"><div class="ttname"><a href="classQsciLexerVHDL.html#a7f8da8d7fe8301cd49926b896bf5e286">QsciLexerVHDL::setFoldAtBegin</a></div><div class="ttdeci">virtual void setFoldAtBegin(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerSpice_html_ac4a5d52373228003f7bd51dade64fc85"><div class="ttname"><a href="classQsciLexerSpice.html#ac4a5d52373228003f7bd51dade64fc85">QsciLexerSpice::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexerSRec_html_a2a2052f720aba665eea1135db3ad9aed"><div class="ttname"><a href="classQsciLexerSRec.html#a2a2052f720aba665eea1135db3ad9aed">QsciLexerSRec::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexer_html_a3fccdb7cb8f6524ecdeb3ff364ae5a49"><div class="ttname"><a href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">QsciLexer::setEolFill</a></div><div class="ttdeci">virtual void setEolFill(bool eoffill, int style=-1)</div></div>
<div class="ttc" id="aclassQsciLexerSRec_html_ad149f20b5fafc86c33706857ac0248f5"><div class="ttname"><a href="classQsciLexerSRec.html#ad149f20b5fafc86c33706857ac0248f5">QsciLexerSRec::QsciLexerSRec</a></div><div class="ttdeci">QsciLexerSRec(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_aec007c8c5c374ca94b71d3eb0f47f467"><div class="ttname"><a href="classQsciLexerLua.html#aec007c8c5c374ca94b71d3eb0f47f467">QsciLexerLua::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerMarkdown_html_a2d0cd9ae9bac9e8fc29477ce1f0b9ca1"><div class="ttname"><a href="classQsciLexerMarkdown.html#a2d0cd9ae9bac9e8fc29477ce1f0b9ca1">QsciLexerMarkdown::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerXML_html_a995fe59f125a7cb27cf178b9e83015bc"><div class="ttname"><a href="classQsciLexerXML.html#a995fe59f125a7cb27cf178b9e83015bc">QsciLexerXML::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a39af10ac6ff34cb347bb2c891f8de64f"><div class="ttname"><a href="classQsciLexerCPP.html#a39af10ac6ff34cb347bb2c891f8de64f">QsciLexerCPP::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexer_html_a519df98c9e7d9d26734a38ea9bed744a"><div class="ttname"><a href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">QsciLexer::paper</a></div><div class="ttdeci">virtual QColor paper(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerTCL_html_a80f3f0cbd594ce9268081a76174ee0e8"><div class="ttname"><a href="classQsciLexerTCL.html#a80f3f0cbd594ce9268081a76174ee0e8">QsciLexerTCL::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciPrinter_html_a420e136529a8d49551eb8af0f5cdce03"><div class="ttname"><a href="classQsciPrinter.html#a420e136529a8d49551eb8af0f5cdce03">QsciPrinter::formatPage</a></div><div class="ttdeci">virtual void formatPage(QPainter &amp;painter, bool drawing, QRect &amp;area, int pagenr)</div></div>
<div class="ttc" id="aclassQsciStyle_html_a4a0e012717bb1fd68de03209260a0609"><div class="ttname"><a href="classQsciStyle.html#a4a0e012717bb1fd68de03209260a0609">QsciStyle::TextCase</a></div><div class="ttdeci">TextCase</div><div class="ttdef"><b>Definition:</b> qscistyle.h:46</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a167dbdb42a4c0ed65229a3418153d4dd"><div class="ttname"><a href="classQsciLexerPython.html#a167dbdb42a4c0ed65229a3418153d4dd">QsciLexerPython::foldQuotes</a></div><div class="ttdeci">bool foldQuotes() const</div><div class="ttdef"><b>Definition:</b> qscilexerpython.h:204</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a05d880fd1451f6a757fd21a7bd43a358"><div class="ttname"><a href="classQsciLexerPascal.html#a05d880fd1451f6a757fd21a7bd43a358">QsciLexerPascal::setFoldComments</a></div><div class="ttdeci">virtual void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html"><div class="ttname"><a href="classQsciLexerPascal.html">QsciLexerPascal</a></div><div class="ttdoc">The QsciLexerPascal class encapsulates the Scintilla Pascal lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerpascal.h:32</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html_a40dcaf1e09ebad7bc685d7f2c5d52a3b"><div class="ttname"><a href="classQsciLexerProperties.html#a40dcaf1e09ebad7bc685d7f2c5d52a3b">QsciLexerProperties::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html"><div class="ttname"><a href="classQsciLexerCoffeeScript.html">QsciLexerCoffeeScript</a></div><div class="ttdoc">The QsciLexerCoffeeScript class encapsulates the Scintilla CoffeeScript lexer.</div><div class="ttdef"><b>Definition:</b> qscilexercoffeescript.h:33</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a5b4b113d57b73a7b531f137a98ecc1bc"><div class="ttname"><a href="classQsciScintilla.html#a5b4b113d57b73a7b531f137a98ecc1bc">QsciScintilla::findMatchingBrace</a></div><div class="ttdeci">bool findMatchingBrace(long &amp;brace, long &amp;other, BraceMatch mode)</div></div>
<div class="ttc" id="aclassQsciLexerCMake_html_a058af5212c83c1eef0a5f39252651743"><div class="ttname"><a href="classQsciLexerCMake.html#a058af5212c83c1eef0a5f39252651743">QsciLexerCMake::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a0da7428fe1c237620cddbf30d06329da"><div class="ttname"><a href="classQsciScintilla.html#a0da7428fe1c237620cddbf30d06329da">QsciScintilla::markerDelete</a></div><div class="ttdeci">void markerDelete(int linenr, int markerNumber=-1)</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a6d6a21ea44e2ee9676aa27178021b06a"><div class="ttname"><a href="classQsciLexerCPP.html#a6d6a21ea44e2ee9676aa27178021b06a">QsciLexerCPP::setHighlightEscapeSequences</a></div><div class="ttdeci">void setHighlightEscapeSequences(bool enabled)</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_a8553315e763e1e53f56dd4dbe6b3c3d7"><div class="ttname"><a href="classQsciLexerHTML.html#a8553315e763e1e53f56dd4dbe6b3c3d7">QsciLexerHTML::setMakoTemplates</a></div><div class="ttdeci">void setMakoTemplates(bool enabled)</div></div>
<div class="ttc" id="aclassQsciLexerBatch_html_acf33e60d28291147562860b824ccd74d"><div class="ttname"><a href="classQsciLexerBatch.html#acf33e60d28291147562860b824ccd74d">QsciLexerBatch::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerTCL_html_a90015597a5748d85b36cc5b263fc05cf"><div class="ttname"><a href="classQsciLexerTCL.html#a90015597a5748d85b36cc5b263fc05cf">QsciLexerTCL::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerJava_html_af0f0dd1756ceb60bd8f404f3b48f470c"><div class="ttname"><a href="classQsciLexerJava.html#af0f0dd1756ceb60bd8f404f3b48f470c">QsciLexerJava::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexer_html_ad892735ca7ad0bad9b7fafdcb44eeaa8"><div class="ttname"><a href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">QsciLexer::editor</a></div><div class="ttdeci">QsciScintilla * editor() const</div><div class="ttdef"><b>Definition:</b> qscilexer.h:207</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_a689e8352655111f8d1c9421552f454c4"><div class="ttname"><a href="classQsciLexerJSON.html#a689e8352655111f8d1c9421552f454c4">QsciLexerJSON::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_a4bf7cce95e65755a221fd75bd731a3cd"><div class="ttname"><a href="classQsciLexerTeX.html#a4bf7cce95e65755a221fd75bd731a3cd">QsciLexerTeX::QsciLexerTeX</a></div><div class="ttdeci">QsciLexerTeX(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_ac6d9fdf26d30d14707e0b0778f80d54d"><div class="ttname"><a href="classQsciLexerVerilog.html#ac6d9fdf26d30d14707e0b0778f80d54d">QsciLexerVerilog::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_a6e1319ec464ff7f150ea7153f109acf6"><div class="ttname"><a href="classQsciLexerAsm.html#a6e1319ec464ff7f150ea7153f109acf6">QsciLexerAsm::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div><div class="ttdoc">Returns the foreground colour of the text for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_a8f6156730e68c15fb63e120c53ce7832"><div class="ttname"><a href="classQsciLexerPostScript.html#a8f6156730e68c15fb63e120c53ce7832">QsciLexerPostScript::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_af41ceced7bf5eb12aefb77f81240b1eb"><div class="ttname"><a href="classQsciLexerPOV.html#af41ceced7bf5eb12aefb77f81240b1eb">QsciLexerPOV::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciCommand_html_a8c00e5f08abe7ad05fe54653c0f040ae"><div class="ttname"><a href="classQsciCommand.html#a8c00e5f08abe7ad05fe54653c0f040ae">QsciCommand::setAlternateKey</a></div><div class="ttdeci">void setAlternateKey(int altkey)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a85f97649b701717e65b2390d57f5ad03"><div class="ttname"><a href="classQsciScintilla.html#a85f97649b701717e65b2390d57f5ad03">QsciScintilla::lines</a></div><div class="ttdeci">int lines() const</div><div class="ttdoc">Returns the number of lines of text.</div></div>
<div class="ttc" id="aclassQsciLexerXML_html_a96ad1f818e51a3606404d24bf7a28a91"><div class="ttname"><a href="classQsciLexerXML.html#a96ad1f818e51a3606404d24bf7a28a91">QsciLexerXML::scriptsStyled</a></div><div class="ttdeci">bool scriptsStyled() const</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_af6839d80f9b92eaead072803664a497f"><div class="ttname"><a href="classQsciLexerPOV.html#af6839d80f9b92eaead072803664a497f">QsciLexerPOV::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciStyledText_html_a72dbd9d847a577fe5c438d1582920887"><div class="ttname"><a href="classQsciStyledText.html#a72dbd9d847a577fe5c438d1582920887">QsciStyledText::QsciStyledText</a></div><div class="ttdeci">QsciStyledText(const QString &amp;text, int style)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad6fb5a9621640080fc9909f94b6c0213"><div class="ttname"><a href="classQsciScintillaBase.html#ad6fb5a9621640080fc9909f94b6c0213">QsciScintillaBase::focusNextPrevChild</a></div><div class="ttdeci">virtual bool focusNextPrevChild(bool next)</div><div class="ttdoc">Re-implemented to allow tabs to be entered as text.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a9405d8aaa240dfc8fe30c3a4b5556ecc"><div class="ttname"><a href="classQsciScintilla.html#a9405d8aaa240dfc8fe30c3a4b5556ecc">QsciScintilla::setContractedFolds</a></div><div class="ttdeci">void setContractedFolds(const QList&lt; int &gt; &amp;folds)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a4ba18e98a98310113fb30d8ada30fe14"><div class="ttname"><a href="classQsciScintilla.html#a4ba18e98a98310113fb30d8ada30fe14">QsciScintilla::setAutoCompletionWordSeparators</a></div><div class="ttdeci">void setAutoCompletionWordSeparators(const QStringList &amp;separators)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae308bac1c3567d835742e02f1bc35a6c"><div class="ttname"><a href="classQsciScintilla.html#ae308bac1c3567d835742e02f1bc35a6c">QsciScintilla::resetMatchedBraceIndicator</a></div><div class="ttdeci">void resetMatchedBraceIndicator()</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a40b8ec37e068b12d9c83ee497929a00e"><div class="ttname"><a href="classQsciScintilla.html#a40b8ec37e068b12d9c83ee497929a00e">QsciScintilla::EdgeMode</a></div><div class="ttdeci">EdgeMode</div><div class="ttdoc">This enum defines the different edge modes for long lines.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:178</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_a336165187c8ab4cc5e51912033316943"><div class="ttname"><a href="classQsciLexerHTML.html#a336165187c8ab4cc5e51912033316943">QsciLexerHTML::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerFortran77_html_a21724c1f53b67ec6bc72c7ceb1e03d8f"><div class="ttname"><a href="classQsciLexerFortran77.html#a21724c1f53b67ec6bc72c7ceb1e03d8f">QsciLexerFortran77::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciMacro_html_a3de5fbc4e99be9cb9d10d90dd8b1059d"><div class="ttname"><a href="classQsciMacro.html#a3de5fbc4e99be9cb9d10d90dd8b1059d">QsciMacro::play</a></div><div class="ttdeci">virtual void play()</div><div class="ttdoc">Play the macro.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aaf19a3abaa1b1662a0f1b499ef4b6602"><div class="ttname"><a href="classQsciScintilla.html#aaf19a3abaa1b1662a0f1b499ef4b6602">QsciScintilla::setHotspotBackgroundColor</a></div><div class="ttdeci">void setHotspotBackgroundColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ac0f785ba228153e9df6df76ca036c030"><div class="ttname"><a href="classQsciScintilla.html#ac0f785ba228153e9df6df76ca036c030">QsciScintilla::beginUndoAction</a></div><div class="ttdeci">void beginUndoAction()</div></div>
<div class="ttc" id="aclassQsciAPIs_html_a3084b749e4eb1c741fc1004e8a84a631"><div class="ttname"><a href="classQsciAPIs.html#a3084b749e4eb1c741fc1004e8a84a631">QsciAPIs::load</a></div><div class="ttdeci">bool load(const QString &amp;filename)</div></div>
<div class="ttc" id="aclassQsciLexerJavaScript_html_af4b249f267973d29380b758a25b42e46"><div class="ttname"><a href="classQsciLexerJavaScript.html#af4b249f267973d29380b758a25b42e46">QsciLexerJavaScript::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a632fe3f132c3679ada1fc22a57ca88f7"><div class="ttname"><a href="classQsciScintilla.html#a632fe3f132c3679ada1fc22a57ca88f7">QsciScintilla::callTipsStyle</a></div><div class="ttdeci">CallTipsStyle callTipsStyle() const</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:648</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_a06f4a0a490680e76ba0edb0fe4fe4dc5"><div class="ttname"><a href="classQsciLexerTeX.html#a06f4a0a490680e76ba0edb0fe4fe4dc5">QsciLexerTeX::foldComments</a></div><div class="ttdeci">bool foldComments() const</div><div class="ttdef"><b>Definition:</b> qscilexertex.h:100</div></div>
<div class="ttc" id="aclassQsciLexerPOV_html_a5a599e7d97b164fec1ee3c21ba167e80"><div class="ttname"><a href="classQsciLexerPOV.html#a5a599e7d97b164fec1ee3c21ba167e80">QsciLexerPOV::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a8820ab8d7563bd7ed24ce6384846079e"><div class="ttname"><a href="classQsciScintillaBase.html#a8820ab8d7563bd7ed24ce6384846079e">QsciScintillaBase::SendScintilla</a></div><div class="ttdeci">long SendScintilla(unsigned int msg, unsigned long wParam=0, long lParam=0) const</div></div>
<div class="ttc" id="aclassQsciLexerXML_html_a57ae4ff270b1c66316b0849ff9017677"><div class="ttname"><a href="classQsciLexerXML.html#a57ae4ff270b1c66316b0849ff9017677">QsciLexerXML::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html_a2243845007f5165eb5718a131be3ada3"><div class="ttname"><a href="classQsciLexerProperties.html#a2243845007f5165eb5718a131be3ada3">QsciLexerProperties::setInitialSpaces</a></div><div class="ttdeci">void setInitialSpaces(bool enable)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ac961cfe1be7cd29038a2772f30b71bfc"><div class="ttname"><a href="classQsciScintilla.html#ac961cfe1be7cd29038a2772f30b71bfc">QsciScintilla::setHotspotUnderline</a></div><div class="ttdeci">void setHotspotUnderline(bool enable)</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_ab58e1e9d037d280fc74792ace83936d4"><div class="ttname"><a href="classQsciLexerPerl.html#ab58e1e9d037d280fc74792ace83936d4">QsciLexerPerl::foldPODBlocks</a></div><div class="ttdeci">bool foldPODBlocks() const</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_af031b3510193023158fb74ca637f79b2"><div class="ttname"><a href="classQsciLexerHTML.html#af031b3510193023158fb74ca637f79b2">QsciLexerHTML::~QsciLexerHTML</a></div><div class="ttdeci">virtual ~QsciLexerHTML()</div><div class="ttdoc">Destroys the QsciLexerHTML instance.</div></div>
<div class="ttc" id="aclassQsciLexer_html_acd8475f0da36449dc6b1189a587d7a83"><div class="ttname"><a href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">QsciLexer::propertyChanged</a></div><div class="ttdeci">void propertyChanged(const char *prop, const char *val)</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_ae6bc53fc7e6dc90a80a26e22f6f49acb"><div class="ttname"><a href="classQsciLexerPython.html#ae6bc53fc7e6dc90a80a26e22f6f49acb">QsciLexerPython::setV3BinaryOctalAllowed</a></div><div class="ttdeci">void setV3BinaryOctalAllowed(bool allowed)</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_a3360bca839d08fdd2acf546b19b2fddd"><div class="ttname"><a href="classQsciLexerVerilog.html#a3360bca839d08fdd2acf546b19b2fddd">QsciLexerVerilog::QsciLexerVerilog</a></div><div class="ttdeci">QsciLexerVerilog(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerMakefile_html_ab7c3560b5333c595506d4244134e9b1b"><div class="ttname"><a href="classQsciLexerMakefile.html#ab7c3560b5333c595506d4244134e9b1b">QsciLexerMakefile::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae4d479c640e2ea4444aa905f69495321"><div class="ttname"><a href="classQsciScintilla.html#ae4d479c640e2ea4444aa905f69495321">QsciScintilla::autoCompleteFromDocument</a></div><div class="ttdeci">virtual void autoCompleteFromDocument()</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_a4c5ae7bc7d27946b1b07b940ef30a093"><div class="ttname"><a href="classQsciLexerHTML.html#a4c5ae7bc7d27946b1b07b940ef30a093">QsciLexerHTML::QsciLexerHTML</a></div><div class="ttdeci">QsciLexerHTML(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_aa60e141b7b1a7aac51d79ad2c27c4c93"><div class="ttname"><a href="classQsciLexerVHDL.html#aa60e141b7b1a7aac51d79ad2c27c4c93">QsciLexerVHDL::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexerTCL_html_a6a108be4899959ffcb262f59de538964"><div class="ttname"><a href="classQsciLexerTCL.html#a6a108be4899959ffcb262f59de538964">QsciLexerTCL::QsciLexerTCL</a></div><div class="ttdeci">QsciLexerTCL(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html_a4119053764ba32a9975ad7eeb8f0f067"><div class="ttname"><a href="classQsciLexerProperties.html#a4119053764ba32a9975ad7eeb8f0f067">QsciLexerProperties::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerAsm_html_a6a63f18e16cef082580868662a6f80bf"><div class="ttname"><a href="classQsciLexerAsm.html#a6a63f18e16cef082580868662a6f80bf">QsciLexerAsm::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_ae6e5819a3ddec15ac6926b5e19927bff"><div class="ttname"><a href="classQsciLexerSQL.html#ae6e5819a3ddec15ac6926b5e19927bff">QsciLexerSQL::setQuotedIdentifiers</a></div><div class="ttdeci">void setQuotedIdentifiers(bool enable)</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_a157c462625b4826a5d7fb9eec42cfc78"><div class="ttname"><a href="classQsciLexerLua.html#a157c462625b4826a5d7fb9eec42cfc78">QsciLexerLua::blockStart</a></div><div class="ttdeci">const char * blockStart(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciLexerLua_html"><div class="ttname"><a href="classQsciLexerLua.html">QsciLexerLua</a></div><div class="ttdoc">The QsciLexerLua class encapsulates the Scintilla Lua lexer.</div><div class="ttdef"><b>Definition:</b> qscilexerlua.h:33</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ab5ecfa6bee9e78e5306986367e1194af"><div class="ttname"><a href="classQsciScintilla.html#ab5ecfa6bee9e78e5306986367e1194af">QsciScintilla::lineLength</a></div><div class="ttdeci">int lineLength(int line) const</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html_a15390924acb08542856527f5d0101dab"><div class="ttname"><a href="classQsciLexerAVS.html#a15390924acb08542856527f5d0101dab">QsciLexerAVS::QsciLexerAVS</a></div><div class="ttdeci">QsciLexerAVS(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a242188212df611073f78d1eff326f5d5"><div class="ttname"><a href="classQsciLexerPerl.html#a242188212df611073f78d1eff326f5d5">QsciLexerPerl::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_ad0a3dd6dfb77a069303bfeeeed43773f"><div class="ttname"><a href="classQsciLexerCPP.html#ad0a3dd6dfb77a069303bfeeeed43773f">QsciLexerCPP::setFoldAtElse</a></div><div class="ttdeci">virtual void setFoldAtElse(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a4bd5b007424a8e88db37a326c0f154b5"><div class="ttname"><a href="classQsciLexerPascal.html#a4bd5b007424a8e88db37a326c0f154b5">QsciLexerPascal::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_adbaf4979024f12f9382df61cba0e75e8"><div class="ttname"><a href="classQsciLexerVerilog.html#adbaf4979024f12f9382df61cba0e75e8">QsciLexerVerilog::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciAPIs_html_aaa47506820a2596004688e241fc4cd9f"><div class="ttname"><a href="classQsciAPIs.html#aaa47506820a2596004688e241fc4cd9f">QsciAPIs::apiPreparationCancelled</a></div><div class="ttdeci">void apiPreparationCancelled()</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_aad362dd8a212974c01e61d12c8991b7f"><div class="ttname"><a href="classQsciLexerVHDL.html#aad362dd8a212974c01e61d12c8991b7f">QsciLexerVHDL::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a828f1b56453686ccd423e979e55fbbae"><div class="ttname"><a href="classQsciScintilla.html#a828f1b56453686ccd423e979e55fbbae">QsciScintilla::autoIndent</a></div><div class="ttdeci">bool autoIndent() const</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:615</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_aa3bb000261e4752d89e06afe69d665f0"><div class="ttname"><a href="classQsciLexerRuby.html#aa3bb000261e4752d89e06afe69d665f0">QsciLexerRuby::QsciLexerRuby</a></div><div class="ttdeci">QsciLexerRuby(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a9688ce1d302666e492900d3cdfcbbaab"><div class="ttname"><a href="classQsciLexerPascal.html#a9688ce1d302666e492900d3cdfcbbaab">QsciLexerPascal::QsciLexerPascal</a></div><div class="ttdeci">QsciLexerPascal(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerFortran77_html_a1ef7534c295a6323be9176fca79b1cbe"><div class="ttname"><a href="classQsciLexerFortran77.html#a1ef7534c295a6323be9176fca79b1cbe">QsciLexerFortran77::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerSRec_html_a202c29debcf73a554dd9b1195e6baf13"><div class="ttname"><a href="classQsciLexerSRec.html#a202c29debcf73a554dd9b1195e6baf13">QsciLexerSRec::~QsciLexerSRec</a></div><div class="ttdeci">virtual ~QsciLexerSRec()</div><div class="ttdoc">Destroys the QsciLexerSRec instance.</div></div>
<div class="ttc" id="aclassQsciLexerMatlab_html_a7afb79f0fec38396668dd52de7fc7c4b"><div class="ttname"><a href="classQsciLexerMatlab.html#a7afb79f0fec38396668dd52de7fc7c4b">QsciLexerMatlab::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a6bdcf192822a31094e680ffb2f142aab"><div class="ttname"><a href="classQsciScintilla.html#a6bdcf192822a31094e680ffb2f142aab">QsciScintilla::setFoldMarginColors</a></div><div class="ttdeci">void setFoldMarginColors(const QColor &amp;fore, const QColor &amp;back)</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_a57f1f1164f3719b4b855a3a163a78764"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#a57f1f1164f3719b4b855a3a163a78764">QsciLexerCoffeeScript::setFoldComments</a></div><div class="ttdeci">void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a62d0174cb0a07e3f2d48fc0603192668"><div class="ttname"><a href="classQsciScintilla.html#a62d0174cb0a07e3f2d48fc0603192668">QsciScintilla::CallTipsStyle</a></div><div class="ttdeci">CallTipsStyle</div><div class="ttdoc">This enum defines the different call tip styles.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:156</div></div>
<div class="ttc" id="aclassQsciLexerTCL_html_a4a13fa4667146e0dca9d8c15255280a9"><div class="ttname"><a href="classQsciLexerTCL.html#a4a13fa4667146e0dca9d8c15255280a9">QsciLexerTCL::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_af590f3e7196b21860e0405670cfa512d"><div class="ttname"><a href="classQsciScintilla.html#af590f3e7196b21860e0405670cfa512d">QsciScintilla::setMatchedBraceForegroundColor</a></div><div class="ttdeci">void setMatchedBraceForegroundColor(const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a5786917722e156e26d6afca807d05fee"><div class="ttname"><a href="classQsciScintilla.html#a5786917722e156e26d6afca807d05fee">QsciScintilla::setText</a></div><div class="ttdeci">virtual void setText(const QString &amp;text)</div></div>
<div class="ttc" id="aclassQsciLexerSQL_html_a35dfbbd04762b0450232c14862ec3ea6"><div class="ttname"><a href="classQsciLexerSQL.html#a35dfbbd04762b0450232c14862ec3ea6">QsciLexerSQL::setFoldAtElse</a></div><div class="ttdeci">void setFoldAtElse(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerBatch_html_a18341dcb06d1b74269ed1f33c002b2a9"><div class="ttname"><a href="classQsciLexerBatch.html#a18341dcb06d1b74269ed1f33c002b2a9">QsciLexerBatch::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a9fe6e18dbb7ef4cad7f370286d7db0b7"><div class="ttname"><a href="classQsciLexerPython.html#a9fe6e18dbb7ef4cad7f370286d7db0b7">QsciLexerPython::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciLexerJavaScript_html_a94cbc22361a55fe0681ad7fe5425dfb5"><div class="ttname"><a href="classQsciLexerJavaScript.html#a94cbc22361a55fe0681ad7fe5425dfb5">QsciLexerJavaScript::QsciLexerJavaScript</a></div><div class="ttdeci">QsciLexerJavaScript(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerDiff_html_a1818bcdd3a7ec5b11ceacf720b07ddcd"><div class="ttname"><a href="classQsciLexerDiff.html#a1818bcdd3a7ec5b11ceacf720b07ddcd">QsciLexerDiff::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciCommand_html_aeb517d586cb9569d072fcd8a9658911b"><div class="ttname"><a href="classQsciCommand.html#aeb517d586cb9569d072fcd8a9658911b">QsciCommand::validKey</a></div><div class="ttdeci">static bool validKey(int key)</div><div class="ttdoc">If the key key is valid then true is returned.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ae628d46489efa3db3b0c42336a1bf8d3a58a27ab02bc6a09334b2e91a16443ed4"><div class="ttname"><a href="classQsciScintilla.html#ae628d46489efa3db3b0c42336a1bf8d3a58a27ab02bc6a09334b2e91a16443ed4">QsciScintilla::AcusExplicit</a></div><div class="ttdeci">@ AcusExplicit</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:108</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a7ae8627b7ef9faf3bb3a25fdbcb3cd97"><div class="ttname"><a href="classQsciLexerCPP.html#a7ae8627b7ef9faf3bb3a25fdbcb3cd97">QsciLexerCPP::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerIntelHex_html_addd404e86692a2b7afadefbd29d6f7e1"><div class="ttname"><a href="classQsciLexerIntelHex.html#addd404e86692a2b7afadefbd29d6f7e1">QsciLexerIntelHex::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerHex_html_a34dd77797fb20f1c1eb09bda117269ec"><div class="ttname"><a href="classQsciLexerHex.html#a34dd77797fb20f1c1eb09bda117269ec">QsciLexerHex::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_a9914377426e5e464f6d93ce2b64423a0"><div class="ttname"><a href="classQsciLexerPascal.html#a9914377426e5e464f6d93ce2b64423a0">QsciLexerPascal::blockEnd</a></div><div class="ttdeci">const char * blockEnd(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciLexerEDIFACT_html_aec9604240be31cf15709b0c0f2942d6e"><div class="ttname"><a href="classQsciLexerEDIFACT.html#aec9604240be31cf15709b0c0f2942d6e">QsciLexerEDIFACT::QsciLexerEDIFACT</a></div><div class="ttdeci">QsciLexerEDIFACT(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_aad1f452948047cc4ce0afc9bc9374061"><div class="ttname"><a href="classQsciLexerJSON.html#aad1f452948047cc4ce0afc9bc9374061">QsciLexerJSON::setHighlightComments</a></div><div class="ttdeci">void setHighlightComments(bool highlight)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a59b529ccfcb1f7896efb523025371a03"><div class="ttname"><a href="classQsciScintilla.html#a59b529ccfcb1f7896efb523025371a03">QsciScintilla::WrapIndentMode</a></div><div class="ttdeci">WrapIndentMode</div><div class="ttdoc">This enum defines the different line wrap indentation modes.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:508</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html_a19a63e47d6b872b510d99d46abb2230f"><div class="ttname"><a href="classQsciLexerProperties.html#a19a63e47d6b872b510d99d46abb2230f">QsciLexerProperties::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aa627ee937acaae02dc0c5b468fd2643b"><div class="ttname"><a href="classQsciScintilla.html#aa627ee937acaae02dc0c5b468fd2643b">QsciScintilla::setOverwriteMode</a></div><div class="ttdeci">void setOverwriteMode(bool overwrite)</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_af0fffa0361bad4a3a007c09a1811db9c"><div class="ttname"><a href="classQsciLexerLua.html#af0fffa0361bad4a3a007c09a1811db9c">QsciLexerLua::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a1aec9b47eeaf611687eeeef0d1aa3a00"><div class="ttname"><a href="classQsciScintillaBase.html#a1aec9b47eeaf611687eeeef0d1aa3a00">QsciScintillaBase::focusOutEvent</a></div><div class="ttdeci">virtual void focusOutEvent(QFocusEvent *e)</div><div class="ttdoc">Re-implemented to tell Scintilla it has lost the focus.</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html_a4caa0f46faeb171710ec2657cd23436e"><div class="ttname"><a href="classQsciLexerProperties.html#a4caa0f46faeb171710ec2657cd23436e">QsciLexerProperties::setFoldCompact</a></div><div class="ttdeci">virtual void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerPO_html_adfdd8ebdf8346a62055922065c5c3863"><div class="ttname"><a href="classQsciLexerPO.html#adfdd8ebdf8346a62055922065c5c3863">QsciLexerPO::~QsciLexerPO</a></div><div class="ttdeci">virtual ~QsciLexerPO()</div><div class="ttdoc">Destroys the QsciLexerPO instance.</div></div>
<div class="ttc" id="aclassQsciLexerYAML_html_add226b6ffbaee63c29a1f0da7de25784"><div class="ttname"><a href="classQsciLexerYAML.html#add226b6ffbaee63c29a1f0da7de25784">QsciLexerYAML::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a26eb997695e6b7292896743ac825a8ee"><div class="ttname"><a href="classQsciScintilla.html#a26eb997695e6b7292896743ac825a8ee">QsciScintilla::setFont</a></div><div class="ttdeci">void setFont(const QFont &amp;f)</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_a60519c0adb042373a1a79a73b68d7892"><div class="ttname"><a href="classQsciLexerPostScript.html#a60519c0adb042373a1a79a73b68d7892">QsciLexerPostScript::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerIDL_html_a1fd3bee8279c7e3600ec0ed72dbc2d00"><div class="ttname"><a href="classQsciLexerIDL.html#a1fd3bee8279c7e3600ec0ed72dbc2d00">QsciLexerIDL::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexerYAML_html_a35d4260e9c1a68073a6b4f625c846c11"><div class="ttname"><a href="classQsciLexerYAML.html#a35d4260e9c1a68073a6b4f625c846c11">QsciLexerYAML::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_a8932efc560175dc70a88e23b8136bb8f"><div class="ttname"><a href="classQsciLexerLua.html#a8932efc560175dc70a88e23b8136bb8f">QsciLexerLua::QsciLexerLua</a></div><div class="ttdeci">QsciLexerLua(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerSpice_html_a8bf8606224bc8841da7ebf53099f8bca"><div class="ttname"><a href="classQsciLexerSpice.html#a8bf8606224bc8841da7ebf53099f8bca">QsciLexerSpice::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a37506e7f15691f73445422beb341e750"><div class="ttname"><a href="classQsciLexerD.html#a37506e7f15691f73445422beb341e750">QsciLexerD::~QsciLexerD</a></div><div class="ttdeci">virtual ~QsciLexerD()</div><div class="ttdoc">Destroys the QsciLexerD instance.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a405e4804f84e58476494314e7bf4d0a7"><div class="ttname"><a href="classQsciScintilla.html#a405e4804f84e58476494314e7bf4d0a7">QsciScintilla::cancelFind</a></div><div class="ttdeci">void cancelFind()</div></div>
<div class="ttc" id="aclassQsciAbstractAPIs_html_a62a50642b0b058016ed2fdf0922bdee8"><div class="ttname"><a href="classQsciAbstractAPIs.html#a62a50642b0b058016ed2fdf0922bdee8">QsciAbstractAPIs::updateAutoCompletionList</a></div><div class="ttdeci">virtual void updateAutoCompletionList(const QStringList &amp;context, QStringList &amp;list)=0</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214cafa1a0af509be0284f7c69df8134d85ca"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cafa1a0af509be0284f7c69df8134d85ca">QsciScintillaBase::SC_MARK_SMALLRECT</a></div><div class="ttdeci">@ SC_MARK_SMALLRECT</div><div class="ttdoc">A smaller rectangle.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2573</div></div>
<div class="ttc" id="aclassQsciScintilla_html_afb98254d553128ba4ebf6151a496679f"><div class="ttname"><a href="classQsciScintilla.html#afb98254d553128ba4ebf6151a496679f">QsciScintilla::zoomIn</a></div><div class="ttdeci">virtual void zoomIn(int range)</div></div>
<div class="ttc" id="aclassQsciLexerYAML_html_aa0ca10c4e872620d0d6b2fa1fe1b9af0"><div class="ttname"><a href="classQsciLexerYAML.html#aa0ca10c4e872620d0d6b2fa1fe1b9af0">QsciLexerYAML::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_a846ebeb36f0847cee3599860f787bcde"><div class="ttname"><a href="classQsciLexerRuby.html#a846ebeb36f0847cee3599860f787bcde">QsciLexerRuby::foldComments</a></div><div class="ttdeci">bool foldComments() const</div><div class="ttdef"><b>Definition:</b> qscilexerruby.h:203</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_a30570eca6c21ea302b1c6c0bd733dc14"><div class="ttname"><a href="classQsciLexerLua.html#a30570eca6c21ea302b1c6c0bd733dc14">QsciLexerLua::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciLexerJSON_html_af4a9c85e527eda6c28663f055afa0be2"><div class="ttname"><a href="classQsciLexerJSON.html#af4a9c85e527eda6c28663f055afa0be2">QsciLexerJSON::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a3a0d1c86f15f218fe6c0e04fea0ba6d9"><div class="ttname"><a href="classQsciScintilla.html#a3a0d1c86f15f218fe6c0e04fea0ba6d9">QsciScintilla::setDocument</a></div><div class="ttdeci">void setDocument(const QsciDocument &amp;document)</div></div>
<div class="ttc" id="aclassQsciCommandSet_html_a7933fbb5a8b5cb234c4e48b472adc4a3"><div class="ttname"><a href="classQsciCommandSet.html#a7933fbb5a8b5cb234c4e48b472adc4a3">QsciCommandSet::writeSettings</a></div><div class="ttdeci">bool writeSettings(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</div></div>
<div class="ttc" id="aclassQsciLexerMakefile_html_a9e2c6ee91938aad61cfb7304de571bd4"><div class="ttname"><a href="classQsciLexerMakefile.html#a9e2c6ee91938aad61cfb7304de571bd4">QsciLexerMakefile::wordCharacters</a></div><div class="ttdeci">const char * wordCharacters() const</div><div class="ttdoc">Returns the string of characters that comprise a word.</div></div>
<div class="ttc" id="aclassQsciLexerCMake_html_aaae969a8e94db29a49849d7497e2cc74"><div class="ttname"><a href="classQsciLexerCMake.html#aaae969a8e94db29a49849d7497e2cc74">QsciLexerCMake::setFoldAtElse</a></div><div class="ttdeci">virtual void setFoldAtElse(bool fold)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aea97c6fb0079a6e3e647443b6101ae9d"><div class="ttname"><a href="classQsciScintilla.html#aea97c6fb0079a6e3e647443b6101ae9d">QsciScintilla::setCursorPosition</a></div><div class="ttdeci">virtual void setCursorPosition(int line, int index)</div></div>
<div class="ttc" id="aclassQsciLexerSpice_html_af9821d4ad823bc0840178c6fb9ab7a1d"><div class="ttname"><a href="classQsciLexerSpice.html#af9821d4ad823bc0840178c6fb9ab7a1d">QsciLexerSpice::~QsciLexerSpice</a></div><div class="ttdeci">virtual ~QsciLexerSpice()</div><div class="ttdoc">Destroys the QsciLexerSpice instance.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aff32517974ac1d8c8cd3c5b6c757ddc9"><div class="ttname"><a href="classQsciScintilla.html#aff32517974ac1d8c8cd3c5b6c757ddc9">QsciScintilla::setModified</a></div><div class="ttdeci">virtual void setModified(bool m)</div></div>
<div class="ttc" id="aclassQsciLexerTekHex_html_abf8cd7722d9bd5f7d5f7b03987ba15b8"><div class="ttname"><a href="classQsciLexerTekHex.html#abf8cd7722d9bd5f7d5f7b03987ba15b8">QsciLexerTekHex::lexer</a></div><div class="ttdeci">const char * lexer() const</div><div class="ttdoc">Returns the name of the lexer.</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a7320152a6d9098d07bba3da6c99a232e"><div class="ttname"><a href="classQsciLexerPython.html#a7320152a6d9098d07bba3da6c99a232e">QsciLexerPython::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciAPIs_html_aa5c7c8855162eeb1be74c226ebf1b1b6"><div class="ttname"><a href="classQsciAPIs.html#aa5c7c8855162eeb1be74c226ebf1b1b6">QsciAPIs::cancelPreparation</a></div><div class="ttdeci">void cancelPreparation()</div></div>
<div class="ttc" id="aclassQsciLexerYAML_html_a5e1785141798faf81dcff567b8df651f"><div class="ttname"><a href="classQsciLexerYAML.html#a5e1785141798faf81dcff567b8df651f">QsciLexerYAML::QsciLexerYAML</a></div><div class="ttdeci">QsciLexerYAML(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexer_html_a147ab3b400fcbe1e5d733b8a897f4930"><div class="ttname"><a href="classQsciLexer.html#a147ab3b400fcbe1e5d733b8a897f4930">QsciLexer::defaultFont</a></div><div class="ttdeci">virtual QFont defaultFont(int style) const</div><div class="ttdoc">Returns the default font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerBatch_html_ac9329cbc86f1f1a915e548997af76a5f"><div class="ttname"><a href="classQsciLexerBatch.html#ac9329cbc86f1f1a915e548997af76a5f">QsciLexerBatch::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_a68474df4d256e32296c5f09c243a55db"><div class="ttname"><a href="classQsciLexerPostScript.html#a68474df4d256e32296c5f09c243a55db">QsciLexerPostScript::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerCustom_html_a19d92643c31c4ec10eab14da7c931b55"><div class="ttname"><a href="classQsciLexerCustom.html#a19d92643c31c4ec10eab14da7c931b55">QsciLexerCustom::startStyling</a></div><div class="ttdeci">void startStyling(int pos, int styleBits=0)</div></div>
<div class="ttc" id="aclassQsciLexerJavaScript_html_a66b627130d76db15263b7502ec5d475c"><div class="ttname"><a href="classQsciLexerJavaScript.html#a66b627130d76db15263b7502ec5d475c">QsciLexerJavaScript::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html_a638b892c566301f0efe779c58516cbc0"><div class="ttname"><a href="classQsciLexerProperties.html#a638b892c566301f0efe779c58516cbc0">QsciLexerProperties::refreshProperties</a></div><div class="ttdeci">void refreshProperties()</div></div>
<div class="ttc" id="aclassQsciLexerD_html_a4d8069f6efaeba7c4fa810630bed2e2e"><div class="ttname"><a href="classQsciLexerD.html#a4d8069f6efaeba7c4fa810630bed2e2e">QsciLexerD::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_aed2f3934c2fe336324d6e79526c2f7a8"><div class="ttname"><a href="classQsciLexerVHDL.html#aed2f3934c2fe336324d6e79526c2f7a8">QsciLexerVHDL::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a0999f0d3c67472b6986486fd06775240"><div class="ttname"><a href="classQsciScintilla.html#a0999f0d3c67472b6986486fd06775240">QsciScintilla::setCaretLineFrameWidth</a></div><div class="ttdeci">virtual void setCaretLineFrameWidth(int width)</div></div>
<div class="ttc" id="aclassQsciLexerPerl_html_a269b1c3c788ae863939fd8b1749a5abf"><div class="ttname"><a href="classQsciLexerPerl.html#a269b1c3c788ae863939fd8b1749a5abf">QsciLexerPerl::setFoldCompact</a></div><div class="ttdeci">virtual void setFoldCompact(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_a1ba78d98f1f2a18338782acbeb73d22e"><div class="ttname"><a href="classQsciLexerTeX.html#a1ba78d98f1f2a18338782acbeb73d22e">QsciLexerTeX::wordCharacters</a></div><div class="ttdeci">const char * wordCharacters() const</div><div class="ttdoc">Returns the string of characters that comprise a word.</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html_abfb306fd9267f3af76bd144409776ba6"><div class="ttname"><a href="classQsciLexerAVS.html#abfb306fd9267f3af76bd144409776ba6">QsciLexerAVS::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ac46502c93651ec7a6642afe5dca86ffc"><div class="ttname"><a href="classQsciScintilla.html#ac46502c93651ec7a6642afe5dca86ffc">QsciScintilla::addEdgeColumn</a></div><div class="ttdeci">void addEdgeColumn(int colnr, const QColor &amp;col)</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html_a9af4c417c88911b8c0ca653d643e3778"><div class="ttname"><a href="classQsciLexerAVS.html#a9af4c417c88911b8c0ca653d643e3778">QsciLexerAVS::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_adb8531cdc862f79cce9fa4d970bc13a2"><div class="ttname"><a href="classQsciScintillaBase.html#adb8531cdc862f79cce9fa4d970bc13a2">QsciScintillaBase::contextMenuEvent</a></div><div class="ttdeci">virtual void contextMenuEvent(QContextMenuEvent *e)</div><div class="ttdoc">Re-implemented to handle the context menu.</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_ad004786b74db7858f6642c23447a214ca39e5d4cae13901613bcfae619cd496b5"><div class="ttname"><a href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca39e5d4cae13901613bcfae619cd496b5">QsciScintillaBase::SC_MARK_ROUNDRECT</a></div><div class="ttdeci">@ SC_MARK_ROUNDRECT</div><div class="ttdoc">A rectangle.</div><div class="ttdef"><b>Definition:</b> qsciscintillabase.h:2567</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a87366cd0dc2931f62e9e4d6bcd870fdf"><div class="ttname"><a href="classQsciScintilla.html#a87366cd0dc2931f62e9e4d6bcd870fdf">QsciScintilla::isUndoAvailable</a></div><div class="ttdeci">bool isUndoAvailable() const</div></div>
<div class="ttc" id="aclassQsciLexerBash_html_a65ab592afff78804f6487dd7badd00cf"><div class="ttname"><a href="classQsciLexerBash.html#a65ab592afff78804f6487dd7badd00cf">QsciLexerBash::wordCharacters</a></div><div class="ttdeci">const char * wordCharacters() const</div><div class="ttdoc">Returns the string of characters that comprise a word.</div></div>
<div class="ttc" id="aclassQsciLexerCSS_html_a26df830be43cb0ca067b5e0ad037171a"><div class="ttname"><a href="classQsciLexerCSS.html#a26df830be43cb0ca067b5e0ad037171a">QsciLexerCSS::LessLanguage</a></div><div class="ttdeci">bool LessLanguage() const</div><div class="ttdef"><b>Definition:</b> qscilexercss.h:196</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a8911af504ebdc870f09da4c7a491eeeb"><div class="ttname"><a href="classQsciScintilla.html#a8911af504ebdc870f09da4c7a491eeeb">QsciScintilla::standardCommands</a></div><div class="ttdeci">QsciCommandSet * standardCommands() const</div><div class="ttdoc">The standard command set is returned.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:1550</div></div>
<div class="ttc" id="aclassQsciStyle_html_aa7743a3805662a27ae52a56af3ac315a"><div class="ttname"><a href="classQsciStyle.html#aa7743a3805662a27ae52a56af3ac315a">QsciStyle::setColor</a></div><div class="ttdeci">void setColor(const QColor &amp;color)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_ac4d1c67938c75806b2c139d0779d0478ab86cea167b725bddc0ad9f677bcf3120"><div class="ttname"><a href="classQsciScintilla.html#ac4d1c67938c75806b2c139d0779d0478ab86cea167b725bddc0ad9f677bcf3120">QsciScintilla::WrapFlagByText</a></div><div class="ttdeci">@ WrapFlagByText</div><div class="ttdoc">A wrap flag is displayed by the text.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:498</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a682cc736272338433efdc86bc936e0e8"><div class="ttname"><a href="classQsciScintillaBase.html#a682cc736272338433efdc86bc936e0e8">QsciScintillaBase::SCN_HOTSPOTDOUBLECLICK</a></div><div class="ttdeci">void SCN_HOTSPOTDOUBLECLICK(int position, int modifiers)</div></div>
<div class="ttc" id="aclassQsciCommand_html_a6488ddf82659fcf42d704f787b6cb522"><div class="ttname"><a href="classQsciCommand.html#a6488ddf82659fcf42d704f787b6cb522">QsciCommand::setKey</a></div><div class="ttdeci">void setKey(int key)</div></div>
<div class="ttc" id="aclassQsciLexerIntelHex_html_adb2e1ab6e6bf1dc17b8d027ba57dbd16"><div class="ttname"><a href="classQsciLexerIntelHex.html#adb2e1ab6e6bf1dc17b8d027ba57dbd16">QsciLexerIntelHex::QsciLexerIntelHex</a></div><div class="ttdeci">QsciLexerIntelHex(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerProperties_html_a3786c34824a30c5a366f30cb9e8bf115"><div class="ttname"><a href="classQsciLexerProperties.html#a3786c34824a30c5a366f30cb9e8bf115">QsciLexerProperties::initialSpaces</a></div><div class="ttdeci">bool initialSpaces() const</div><div class="ttdef"><b>Definition:</b> qscilexerproperties.h:115</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a2b148c2c2065f1ef1563421303a02225"><div class="ttname"><a href="classQsciScintilla.html#a2b148c2c2065f1ef1563421303a02225">QsciScintilla::setMarginText</a></div><div class="ttdeci">void setMarginText(int line, const QList&lt; QsciStyledText &gt; &amp;text)</div></div>
<div class="ttc" id="aclassQsciScintillaBase_html_a425344ca700d69b60ffeb3f8122f7ff9"><div class="ttname"><a href="classQsciScintillaBase.html#a425344ca700d69b60ffeb3f8122f7ff9">QsciScintillaBase::QsciScintillaBase</a></div><div class="ttdeci">QsciScintillaBase(QWidget *parent=0)</div><div class="ttdoc">Construct an empty QsciScintillaBase with parent parent.</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a2467729449b6c78d63305b88b2f62789"><div class="ttname"><a href="classQsciLexerPython.html#a2467729449b6c78d63305b88b2f62789">QsciLexerPython::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a5e9de211c7e94a22da5c0d599a9e494b"><div class="ttname"><a href="classQsciLexerPython.html#a5e9de211c7e94a22da5c0d599a9e494b">QsciLexerPython::defaultPaper</a></div><div class="ttdeci">QColor defaultPaper(int style) const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a6a577313664af6dc63885f03e88d03af"><div class="ttname"><a href="classQsciScintilla.html#a6a577313664af6dc63885f03e88d03af">QsciScintilla::annotation</a></div><div class="ttdeci">QString annotation(int line) const</div><div class="ttdoc">Returns the annotation on line line, if any.</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_ad09e8331b90feeab761f845ac80e0b6d"><div class="ttname"><a href="classQsciLexerPascal.html#ad09e8331b90feeab761f845ac80e0b6d">QsciLexerPascal::defaultColor</a></div><div class="ttdeci">QColor defaultColor(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerYAML_html_a0cfceb4470cde4184e76076ac34dea29"><div class="ttname"><a href="classQsciLexerYAML.html#a0cfceb4470cde4184e76076ac34dea29">QsciLexerYAML::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciMacro_html_a4a5648ea6c1e35aaaa55f9aaf83e7eda"><div class="ttname"><a href="classQsciMacro.html#a4a5648ea6c1e35aaaa55f9aaf83e7eda">QsciMacro::startRecording</a></div><div class="ttdeci">virtual void startRecording()</div><div class="ttdoc">Start recording user commands and add them to the macro.</div></div>
<div class="ttc" id="aclassQsciLexerSpice_html_a0e389df0054dbbafafe6196c71d50738"><div class="ttname"><a href="classQsciLexerSpice.html#a0e389df0054dbbafafe6196c71d50738">QsciLexerSpice::lexer</a></div><div class="ttdeci">const char * lexer() const</div></div>
<div class="ttc" id="aclassQsciStyle_html_a4f8b9edd94c36344bd7152d15731509a"><div class="ttname"><a href="classQsciStyle.html#a4f8b9edd94c36344bd7152d15731509a">QsciStyle::setVisible</a></div><div class="ttdeci">void setVisible(bool visible)</div></div>
<div class="ttc" id="aclassQsciLexerRuby_html_aa6e85b803ff580acecda16deaa70c758"><div class="ttname"><a href="classQsciLexerRuby.html#aa6e85b803ff580acecda16deaa70c758">QsciLexerRuby::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_af67874dae6e032c44d4ccde569e2decb"><div class="ttname"><a href="classQsciScintilla.html#af67874dae6e032c44d4ccde569e2decb">QsciScintilla::setAutoCompletionReplaceWord</a></div><div class="ttdeci">virtual void setAutoCompletionReplaceWord(bool replace)</div></div>
<div class="ttc" id="aclassQsciLexerLua_html_a27383c2def3f59e903aec9537ef43d2c"><div class="ttname"><a href="classQsciLexerLua.html#a27383c2def3f59e903aec9537ef43d2c">QsciLexerLua::foldCompact</a></div><div class="ttdeci">bool foldCompact() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a3eace684c0a33e044cceb300b1a97798"><div class="ttname"><a href="classQsciScintilla.html#a3eace684c0a33e044cceb300b1a97798">QsciScintilla::markersAtLine</a></div><div class="ttdeci">unsigned markersAtLine(int linenr) const</div></div>
<div class="ttc" id="aclassQsciLexer_html_afccca7eb1aed463f89ac442d99135839"><div class="ttname"><a href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">QsciLexer::caseSensitive</a></div><div class="ttdeci">virtual bool caseSensitive() const</div><div class="ttdoc">Returns true if the language is case sensitive. The default is true.</div></div>
<div class="ttc" id="aclassQsciLexerTCL_html_a71b5b892a7a30a4f57b9efa64fdf5f32"><div class="ttname"><a href="classQsciLexerTCL.html#a71b5b892a7a30a4f57b9efa64fdf5f32">QsciLexerTCL::foldComments</a></div><div class="ttdeci">bool foldComments() const</div><div class="ttdef"><b>Definition:</b> qscilexertcl.h:165</div></div>
<div class="ttc" id="aclassQsciLexerPascal_html_abe045873399199ba05d26e94c0e28aae"><div class="ttname"><a href="classQsciLexerPascal.html#abe045873399199ba05d26e94c0e28aae">QsciLexerPascal::blockStartKeyword</a></div><div class="ttdeci">const char * blockStartKeyword(int *style=0) const</div></div>
<div class="ttc" id="aclassQsciLexerTeX_html_a3218dcdca816cbdc739b2555df366a9a"><div class="ttname"><a href="classQsciLexerTeX.html#a3218dcdca816cbdc739b2555df366a9a">QsciLexerTeX::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerIDL_html_af929c0ded0d4d822d7b31d00103262de"><div class="ttname"><a href="classQsciLexerIDL.html#af929c0ded0d4d822d7b31d00103262de">QsciLexerIDL::QsciLexerIDL</a></div><div class="ttdeci">QsciLexerIDL(QObject *parent=0)</div></div>
<div class="ttc" id="aclassQsciLexerMakefile_html_a4c9199cfee7bb097359fb238859470a9"><div class="ttname"><a href="classQsciLexerMakefile.html#a4c9199cfee7bb097359fb238859470a9">QsciLexerMakefile::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciStyle_html_a6767dbb23f68292ef9e892dad31ffd9e"><div class="ttname"><a href="classQsciStyle.html#a6767dbb23f68292ef9e892dad31ffd9e">QsciStyle::setEolFill</a></div><div class="ttdeci">void setEolFill(bool fill)</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a3b281217e1abc5b4b4ccf8145df4b2ca"><div class="ttname"><a href="classQsciScintilla.html#a3b281217e1abc5b4b4ccf8145df4b2ca">QsciScintilla::isListActive</a></div><div class="ttdeci">bool isListActive() const</div></div>
<div class="ttc" id="aclassQsciLexerD_html_ab55d105b2aa041682b67218fcdf964c6"><div class="ttname"><a href="classQsciLexerD.html#ab55d105b2aa041682b67218fcdf964c6">QsciLexerD::defaultEolFill</a></div><div class="ttdeci">bool defaultEolFill(int style) const</div><div class="ttdoc">Returns the end-of-line fill for style number style.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a6fca34c3778ad8b4c067d577816ebc2b"><div class="ttname"><a href="classQsciScintilla.html#a6fca34c3778ad8b4c067d577816ebc2b">QsciScintilla::edgeMode</a></div><div class="ttdeci">EdgeMode edgeMode() const</div></div>
<div class="ttc" id="aclassQsciScintilla_html_aa44a01f20a2d88c06c561f0043b4f83b"><div class="ttname"><a href="classQsciScintilla.html#aa44a01f20a2d88c06c561f0043b4f83b">QsciScintilla::extraDescent</a></div><div class="ttdeci">int extraDescent() const</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_aa4abeabae54373d536961d0aabb5ecdf"><div class="ttname"><a href="classQsciLexerPython.html#aa4abeabae54373d536961d0aabb5ecdf">QsciLexerPython::stringsOverNewlineAllowed</a></div><div class="ttdeci">bool stringsOverNewlineAllowed() const</div><div class="ttdef"><b>Definition:</b> qscilexerpython.h:234</div></div>
<div class="ttc" id="aclassQsciLexerMarkdown_html_a7a06bdb17ebde731368ec204404ed0ee"><div class="ttname"><a href="classQsciLexerMarkdown.html#a7a06bdb17ebde731368ec204404ed0ee">QsciLexerMarkdown::language</a></div><div class="ttdeci">const char * language() const</div><div class="ttdoc">Returns the name of the language.</div></div>
<div class="ttc" id="aclassQsciScintilla_html_a62d0174cb0a07e3f2d48fc0603192668aedf5d722a7f87ba55272f4355fa5880b"><div class="ttname"><a href="classQsciScintilla.html#a62d0174cb0a07e3f2d48fc0603192668aedf5d722a7f87ba55272f4355fa5880b">QsciScintilla::CallTipsNone</a></div><div class="ttdeci">@ CallTipsNone</div><div class="ttdoc">Call tips are disabled.</div><div class="ttdef"><b>Definition:</b> qsciscintilla.h:158</div></div>
<div class="ttc" id="aclassQsciLexerFortran77_html_aa58025e7a9aa9241a64026f00764fb4e"><div class="ttname"><a href="classQsciLexerFortran77.html#aa58025e7a9aa9241a64026f00764fb4e">QsciLexerFortran77::description</a></div><div class="ttdeci">QString description(int style) const</div></div>
<div class="ttc" id="aclassQsciLexerSpice_html_a7360e3bd243de3e4cbb76637f6d2313d"><div class="ttname"><a href="classQsciLexerSpice.html#a7360e3bd243de3e4cbb76637f6d2313d">QsciLexerSpice::defaultFont</a></div><div class="ttdeci">QFont defaultFont(int style) const</div><div class="ttdoc">Returns the font for style number style.</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a35e71b31d8d197052c7c5250ff21f094"><div class="ttname"><a href="classQsciLexerPython.html#a35e71b31d8d197052c7c5250ff21f094">QsciLexerPython::setFoldComments</a></div><div class="ttdeci">virtual void setFoldComments(bool fold)</div></div>
<div class="ttc" id="aclassQsciLexerPostScript_html_a87168d5b174ba3a9b969ef689f67b355"><div class="ttname"><a href="classQsciLexerPostScript.html#a87168d5b174ba3a9b969ef689f67b355">QsciLexerPostScript::readProperties</a></div><div class="ttdeci">bool readProperties(QSettings &amp;qs, const QString &amp;prefix)</div></div>
<div class="ttc" id="aclassQsciLexerYAML_html_adc63ea477a2869f4ea9f1b3fe69d56fb"><div class="ttname"><a href="classQsciLexerYAML.html#adc63ea477a2869f4ea9f1b3fe69d56fb">QsciLexerYAML::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciLexerCPP_html_a015dce05877d292d399fb207e79632cf"><div class="ttname"><a href="classQsciLexerCPP.html#a015dce05877d292d399fb207e79632cf">QsciLexerCPP::setVerbatimStringEscapeSequencesAllowed</a></div><div class="ttdeci">void setVerbatimStringEscapeSequencesAllowed(bool allowed)</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a305ec320aa2357947cbeb1608b95d840"><div class="ttname"><a href="classQsciLexerPython.html#a305ec320aa2357947cbeb1608b95d840">QsciLexerPython::autoCompletionWordSeparators</a></div><div class="ttdeci">QStringList autoCompletionWordSeparators() const</div></div>
<div class="ttc" id="aclassQsciLexerVerilog_html_a97b418522a5866d04d9553931dd1c7f4"><div class="ttname"><a href="classQsciLexerVerilog.html#a97b418522a5866d04d9553931dd1c7f4">QsciLexerVerilog::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciLexerPython_html_a421ab12187730bc0686dc72710867ec3"><div class="ttname"><a href="classQsciLexerPython.html#a421ab12187730bc0686dc72710867ec3">QsciLexerPython::setIndentationWarning</a></div><div class="ttdeci">virtual void setIndentationWarning(QsciLexerPython::IndentationWarning warn)</div></div>
<div class="ttc" id="aclassQsciLexerVHDL_html_aab4dd4635d954113eecb698c46395d0b"><div class="ttname"><a href="classQsciLexerVHDL.html#aab4dd4635d954113eecb698c46395d0b">QsciLexerVHDL::~QsciLexerVHDL</a></div><div class="ttdeci">virtual ~QsciLexerVHDL()</div><div class="ttdoc">Destroys the QsciLexerVHDL instance.</div></div>
<div class="ttc" id="aclassQsciLexerAVS_html_a6b8fc8bf46c22c3efafd92179b644788"><div class="ttname"><a href="classQsciLexerAVS.html#a6b8fc8bf46c22c3efafd92179b644788">QsciLexerAVS::writeProperties</a></div><div class="ttdeci">bool writeProperties(QSettings &amp;qs, const QString &amp;prefix) const</div></div>
<div class="ttc" id="aclassQsciLexerFortran77_html_a7df3e986e8039ee6028b39d0df1741d1"><div class="ttname"><a href="classQsciLexerFortran77.html#a7df3e986e8039ee6028b39d0df1741d1">QsciLexerFortran77::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<div class="ttc" id="aclassQsciLexerHTML_html_a56b7f081e520f7660490e3d206d83a73"><div class="ttname"><a href="classQsciLexerHTML.html#a56b7f081e520f7660490e3d206d83a73">QsciLexerHTML::keywords</a></div><div class="ttdeci">const char * keywords(int set) const</div></div>
<div class="ttc" id="aclassQsciLexerCoffeeScript_html_add9b1d85d9da1c250f570482cd47eb39"><div class="ttname"><a href="classQsciLexerCoffeeScript.html#add9b1d85d9da1c250f570482cd47eb39">QsciLexerCoffeeScript::braceStyle</a></div><div class="ttdeci">int braceStyle() const</div><div class="ttdoc">Returns the style used for braces for brace matching.</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
