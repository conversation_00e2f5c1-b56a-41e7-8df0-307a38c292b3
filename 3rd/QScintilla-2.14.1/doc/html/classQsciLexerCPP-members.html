<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciLexerCPP Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#af0ccf94585e15b87a18f12ab9de1c977">autoCompletionWordSeparators</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a2263531e4445463f1d75fdfd54102404">blockEnd</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a79d8b2101ef7b1aef1e7e01557090d6f">blockStart</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a2cfcfea76c396c0b7b82fc41437ff16f">blockStartKeyword</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a509df9a20a1841de287849d6738ec3dd">braceStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>bytesAsText</b>(const char *bytes, int size) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1ab05c80130359b9586979df7f9a85d3fe">Comment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a12cc5d18b03e47a08bd19098be35631b">CommentDoc</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1ac640cd198228b554ec3d0b60e00d91bd">CommentDocKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a52db9ffb3d81b68562da67cbc70d3388">CommentDocKeywordError</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1af7a31515ec66490642ab83b9fedb8a78">CommentLine</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a659ebab287e989f11cf905532c1ccddf">CommentLineDoc</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1aaf3761b6b64a02e306095a77c6e44d22">Default</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a39af10ac6ff34cb347bb2c891f8de64f">defaultColor</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">QsciLexer::defaultColor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a870955b5547ce4bdf9940165181022b7">defaultEolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a7ae8627b7ef9faf3bb3a25fdbcb3cd97">defaultFont</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">QsciLexer::defaultFont</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#aebdebbf12dc8bf264479bd570f669268">defaultPaper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">QsciLexer::defaultPaper</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a761b431d688aa99c5c9b5110b41dc712">description</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#aa20e183e0b38b5076aa9e883c5283791">dollarsAllowed</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a9956498543ca425e9772a8d11e7555b5">DoubleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a>(bool eolfilled, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a96681c59afac878c90c821403c472903">EscapeSequence</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#aad7b42963ca382afb23eb000b727de12">foldAtElse</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#adc253f08156cde45b331c5a7ed07cfd7">foldComments</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#ad0939852605ee45ce62f70647d47147b">foldCompact</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a3c93f14b36897ecb3f902b5e5de91ad6">foldPreprocessor</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a>(const QFont &amp;f, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a713977fdf2b7b8b59c7e9d23004090dc">GlobalClass</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a1b8ef1921a218a2db1d6d7d65ac3242c">HashQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a015d6501ee4cca33a00036174529c161">highlightBackQuotedStrings</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#aea4d8707f6e32c1fbf989504d12d9eaa">highlightEscapeSequences</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#ab5f782645ff1d3a2d7ac371cbd9f2f5d">highlightHashQuotedStrings</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a23c6488e2416d54f6a4ec84015d860ec">highlightTripleQuotedStrings</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a162f877d72b7a405250d3a931660080e">Identifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveComment</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveCommentDoc</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveCommentDocKeyword</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveCommentDocKeywordError</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveCommentLine</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveCommentLineDoc</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveDefault</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveDoubleQuotedString</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveEscapeSequence</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveGlobalClass</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveHashQuotedString</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveIdentifier</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveKeyword</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveKeywordSet2</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveNumber</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveOperator</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactivePreProcessor</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactivePreProcessorComment</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactivePreProcessorCommentLineDoc</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveRawString</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveRegex</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveSingleQuotedString</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveTaskMarker</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveTripleQuotedVerbatimString</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveUnclosedString</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveUserLiteral</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveUUID</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveVerbatimString</b> enum value (defined in <a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a>)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a2495558f63baf5987a97cf2dceddbfc7">Keyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#ac331bbae026859d8020ac5a6efd8fed1">keywords</a>(int set) const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1ae9944f1484321b3261c8749ccfadbe2d">KeywordSet2</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a7c5dafabba34ff3e6120d9f3606cade0">language</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a3f7076535f370759450ec1243088c7f1">lexer</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a3f7d7b7c70a53fe91b336ff31c59e195">Number</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a4007ebc2021d70cf1ad6e9c6c85aba4e">Operator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a16c747644d986c230126c5420de1497e">PreProcessor</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a95c728e17fdb37a45ba20d09ee9eda9c">PreProcessorComment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a5b319d25cf7a161e08b0810a0d2b8470">PreProcessorCommentLineDoc</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a>(const char *prop, const char *val)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#ae1451be7e7c6a57f28f361c72eb68a5f">QsciLexerCPP</a>(QObject *parent=0, bool caseInsensitiveKeywords=false)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a12c1673479aaf32d03b5a2fde6f032a9">RawString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#aa37ea54c5e39721b866c25b0e0335591">readProperties</a>(QSettings &amp;qs, const QString &amp;prefix)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a58506e1c965a181c9202376e0ba85c30">refreshProperties</a>()</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1ac26a3735f09aa6702cfbbb9fac56d6f5">Regex</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a>(QsciAbstractAPIs *apis)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a>(int autoindentstyle)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a>(const QFont &amp;f)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a06315a18051184926fe21459fc75b4cc">setDollarsAllowed</a>(bool allowed)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>setEditor</b>(QsciScintilla *editor) (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a>(bool eoffill, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#ad0a3dd6dfb77a069303bfeeeed43773f">setFoldAtElse</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#aaf1f8163b8baf27ef65c1e5219bbf1e2">setFoldComments</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#af17ac732d73445822ef23a59f3e45aef">setFoldCompact</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a6a8c59ca8409029fc6b27b9ad3c70886">setFoldPreprocessor</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a>(const QFont &amp;f, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#aa023c95fbbecbbbf7046c92d6fcfdce5">setHighlightBackQuotedStrings</a>(bool enabled)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a6d6a21ea44e2ee9676aa27178021b06a">setHighlightEscapeSequences</a>(bool enabled)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#ad0d9356583118309e6c3991e96a67ffe">setHighlightHashQuotedStrings</a>(bool enabled)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a2ea8bd8758e10d72832dbf3642b06fb2">setHighlightTripleQuotedStrings</a>(bool enabled)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a66dc6ae74420ab3406043ff9f6f70cc4">setStylePreprocessor</a>(bool style)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a015dce05877d292d399fb207e79632cf">setVerbatimStringEscapeSequencesAllowed</a>(bool allowed)</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1ad7c37e1eaac5103b567dd7f677fbd5be">SingleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#ac6f508a57750605ec3b9688408b092b2">stylePreprocessor</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a258224273023ab8c9504dd8a8efcad6c">TaskMarker</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>textAsBytes</b>(const QString &amp;text) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a68d65b20c3cd1c04f46914904bc9277c">TripleQuotedVerbatimString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a7aae8e724d5fcbbca68f3e7f97460721">UnclosedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a3043c7ad432f1e77406554dcab6f9e0f">UserLiteral</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a3ce80212372e40f2ed903c52297f48c7">UUID</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1afb3585e07f994345f2c21f43f7e2ec1a">VerbatimString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a20439ec93f1af6b8227bdcd48a6070ec">verbatimStringEscapeSequencesAllowed</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#a822ca7489c4655f26bc72ed127285d8a">wordCharacters</a>() const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerCPP.html#a46bd37b48e91903451ab59314448f322">writeProperties</a>(QSettings &amp;qs, const QString &amp;prefix) const</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerCPP.html#ab9babc165bacf53b73abfb2d5d1aadad">~QsciLexerCPP</a>()</td><td class="entry"><a class="el" href="classQsciLexerCPP.html">QsciLexerCPP</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
