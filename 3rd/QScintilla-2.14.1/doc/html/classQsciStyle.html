<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciStyle Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classQsciStyle-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciStyle Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscistyle.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a4a0e012717bb1fd68de03209260a0609"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#a4a0e012717bb1fd68de03209260a0609">TextCase</a> { <a class="el" href="classQsciStyle.html#a4a0e012717bb1fd68de03209260a0609a2fac015153af29a7c87eb69848fd4348">OriginalCase</a> = 0, 
<a class="el" href="classQsciStyle.html#a4a0e012717bb1fd68de03209260a0609aeb98b8f24b317cec7c271fd337185e75">UpperCase</a> = 1, 
<a class="el" href="classQsciStyle.html#a4a0e012717bb1fd68de03209260a0609a83675e1da457009277d3642340dc82cc">LowerCase</a> = 2
 }</td></tr>
<tr class="separator:a4a0e012717bb1fd68de03209260a0609"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a0464f0a24f4094431686c89e667e843e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#a0464f0a24f4094431686c89e667e843e">QsciStyle</a> (int <a class="el" href="classQsciStyle.html#a61582248f6b7276db9b4a1f9582c3828">style</a>=-1)</td></tr>
<tr class="separator:a0464f0a24f4094431686c89e667e843e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e1d6840e7d117886093bbaabbccd56f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#a4e1d6840e7d117886093bbaabbccd56f">QsciStyle</a> (int <a class="el" href="classQsciStyle.html#a61582248f6b7276db9b4a1f9582c3828">style</a>, const QString &amp;<a class="el" href="classQsciStyle.html#a04e5cc64877290739b30603c526d84ce">description</a>, const QColor &amp;<a class="el" href="classQsciStyle.html#af349ce169da83e08ad9f995df48c6547">color</a>, const QColor &amp;<a class="el" href="classQsciStyle.html#a8912da5c6b95404e4642593db1b65d4c">paper</a>, const QFont &amp;<a class="el" href="classQsciStyle.html#af45628c04ab5488fc13b61a2356346ec">font</a>, bool <a class="el" href="classQsciStyle.html#adcc34134da3341f1f07a847b09f6565b">eolFill</a>=false)</td></tr>
<tr class="separator:a4e1d6840e7d117886093bbaabbccd56f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0cbbbe987ecb18537965d30f0e451a8"><td class="memItemLeft" align="right" valign="top"><a id="ae0cbbbe987ecb18537965d30f0e451a8"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>apply</b> (<a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a> *sci) const</td></tr>
<tr class="separator:ae0cbbbe987ecb18537965d30f0e451a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af00ea2dd20e93c5d06d9ce99cbc2cf00"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#af00ea2dd20e93c5d06d9ce99cbc2cf00">setStyle</a> (int <a class="el" href="classQsciStyle.html#a61582248f6b7276db9b4a1f9582c3828">style</a>)</td></tr>
<tr class="separator:af00ea2dd20e93c5d06d9ce99cbc2cf00"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a61582248f6b7276db9b4a1f9582c3828"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#a61582248f6b7276db9b4a1f9582c3828">style</a> () const</td></tr>
<tr class="separator:a61582248f6b7276db9b4a1f9582c3828"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd88d76b875c154f099b4e9f36b6fcab"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#abd88d76b875c154f099b4e9f36b6fcab">setDescription</a> (const QString &amp;<a class="el" href="classQsciStyle.html#a04e5cc64877290739b30603c526d84ce">description</a>)</td></tr>
<tr class="separator:abd88d76b875c154f099b4e9f36b6fcab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a04e5cc64877290739b30603c526d84ce"><td class="memItemLeft" align="right" valign="top">QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#a04e5cc64877290739b30603c526d84ce">description</a> () const</td></tr>
<tr class="separator:a04e5cc64877290739b30603c526d84ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa7743a3805662a27ae52a56af3ac315a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#aa7743a3805662a27ae52a56af3ac315a">setColor</a> (const QColor &amp;<a class="el" href="classQsciStyle.html#af349ce169da83e08ad9f995df48c6547">color</a>)</td></tr>
<tr class="separator:aa7743a3805662a27ae52a56af3ac315a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af349ce169da83e08ad9f995df48c6547"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#af349ce169da83e08ad9f995df48c6547">color</a> () const</td></tr>
<tr class="separator:af349ce169da83e08ad9f995df48c6547"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d4ec76574fd507fbf3c0d006c7427da"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#a2d4ec76574fd507fbf3c0d006c7427da">setPaper</a> (const QColor &amp;<a class="el" href="classQsciStyle.html#a8912da5c6b95404e4642593db1b65d4c">paper</a>)</td></tr>
<tr class="separator:a2d4ec76574fd507fbf3c0d006c7427da"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8912da5c6b95404e4642593db1b65d4c"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#a8912da5c6b95404e4642593db1b65d4c">paper</a> () const</td></tr>
<tr class="separator:a8912da5c6b95404e4642593db1b65d4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab09932c9dafb915b8138d4ec1cbc79cb"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#ab09932c9dafb915b8138d4ec1cbc79cb">setFont</a> (const QFont &amp;<a class="el" href="classQsciStyle.html#af45628c04ab5488fc13b61a2356346ec">font</a>)</td></tr>
<tr class="separator:ab09932c9dafb915b8138d4ec1cbc79cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af45628c04ab5488fc13b61a2356346ec"><td class="memItemLeft" align="right" valign="top">QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#af45628c04ab5488fc13b61a2356346ec">font</a> () const</td></tr>
<tr class="separator:af45628c04ab5488fc13b61a2356346ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6767dbb23f68292ef9e892dad31ffd9e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#a6767dbb23f68292ef9e892dad31ffd9e">setEolFill</a> (bool fill)</td></tr>
<tr class="separator:a6767dbb23f68292ef9e892dad31ffd9e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adcc34134da3341f1f07a847b09f6565b"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#adcc34134da3341f1f07a847b09f6565b">eolFill</a> () const</td></tr>
<tr class="separator:adcc34134da3341f1f07a847b09f6565b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a25e9b8a34c334bf6160115a2c43a5256"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#a25e9b8a34c334bf6160115a2c43a5256">setTextCase</a> (<a class="el" href="classQsciStyle.html#a4a0e012717bb1fd68de03209260a0609">TextCase</a> text_case)</td></tr>
<tr class="separator:a25e9b8a34c334bf6160115a2c43a5256"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16212f9f46162f67ece3ed6423207785"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciStyle.html#a4a0e012717bb1fd68de03209260a0609">TextCase</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#a16212f9f46162f67ece3ed6423207785">textCase</a> () const</td></tr>
<tr class="separator:a16212f9f46162f67ece3ed6423207785"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f8b9edd94c36344bd7152d15731509a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#a4f8b9edd94c36344bd7152d15731509a">setVisible</a> (bool <a class="el" href="classQsciStyle.html#a0fd0947a87e3bf43720227b8226b3edd">visible</a>)</td></tr>
<tr class="separator:a4f8b9edd94c36344bd7152d15731509a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0fd0947a87e3bf43720227b8226b3edd"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#a0fd0947a87e3bf43720227b8226b3edd">visible</a> () const</td></tr>
<tr class="separator:a0fd0947a87e3bf43720227b8226b3edd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7e052d08efd3a677f810c8e4116dafc"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#af7e052d08efd3a677f810c8e4116dafc">setChangeable</a> (bool <a class="el" href="classQsciStyle.html#a4d1aa13e042609e48674f72aebd2ebae">changeable</a>)</td></tr>
<tr class="separator:af7e052d08efd3a677f810c8e4116dafc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4d1aa13e042609e48674f72aebd2ebae"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#a4d1aa13e042609e48674f72aebd2ebae">changeable</a> () const</td></tr>
<tr class="separator:a4d1aa13e042609e48674f72aebd2ebae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acb06ba468da57cc4ea9e8d496cb33f83"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#acb06ba468da57cc4ea9e8d496cb33f83">setHotspot</a> (bool <a class="el" href="classQsciStyle.html#a1df46714ab45c62e5ad5e52a5f41bf15">hotspot</a>)</td></tr>
<tr class="separator:acb06ba468da57cc4ea9e8d496cb33f83"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1df46714ab45c62e5ad5e52a5f41bf15"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#a1df46714ab45c62e5ad5e52a5f41bf15">hotspot</a> () const</td></tr>
<tr class="separator:a1df46714ab45c62e5ad5e52a5f41bf15"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa480e57bbdd83b8164129f875bd48976"><td class="memItemLeft" align="right" valign="top"><a id="aa480e57bbdd83b8164129f875bd48976"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyle.html#aa480e57bbdd83b8164129f875bd48976">refresh</a> ()</td></tr>
<tr class="separator:aa480e57bbdd83b8164129f875bd48976"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciStyle.html" title="The QsciStyle class encapsulates all the attributes of a style.">QsciStyle</a> class encapsulates all the attributes of a style. </p>
<p>Each character of a document has an associated style which determines how the character is displayed, e.g. its font and color. A style is identified by a number. Lexers define styles for each of the language's features so that they are displayed differently. Some style numbers have hard-coded meanings, e.g. the style used for call tips. </p>
</div><h2 class="groupheader">Member Enumeration Documentation</h2>
<a id="a4a0e012717bb1fd68de03209260a0609"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4a0e012717bb1fd68de03209260a0609">&#9670;&nbsp;</a></span>TextCase</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="classQsciStyle.html#a4a0e012717bb1fd68de03209260a0609">QsciStyle::TextCase</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the different ways the displayed case of the text can be changed. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a4a0e012717bb1fd68de03209260a0609a2fac015153af29a7c87eb69848fd4348"></a>OriginalCase&#160;</td><td class="fielddoc"><p>The text is displayed as its original case. </p>
</td></tr>
<tr><td class="fieldname"><a id="a4a0e012717bb1fd68de03209260a0609aeb98b8f24b317cec7c271fd337185e75"></a>UpperCase&#160;</td><td class="fielddoc"><p>The text is displayed as upper case. </p>
</td></tr>
<tr><td class="fieldname"><a id="a4a0e012717bb1fd68de03209260a0609a83675e1da457009277d3642340dc82cc"></a>LowerCase&#160;</td><td class="fielddoc"><p>The text is displayed as lower case. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a0464f0a24f4094431686c89e667e843e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0464f0a24f4094431686c89e667e843e">&#9670;&nbsp;</a></span>QsciStyle() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciStyle::QsciStyle </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em> = <code>-1</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Constructs a <a class="el" href="classQsciStyle.html" title="The QsciStyle class encapsulates all the attributes of a style.">QsciStyle</a> instance for style number <em>style</em>. If <em>style</em> is negative then a new style number is automatically allocated if possible. If it is not possible then <a class="el" href="classQsciStyle.html#a61582248f6b7276db9b4a1f9582c3828">style()</a> will return a negative value.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#a61582248f6b7276db9b4a1f9582c3828">style()</a> </dd></dl>

</div>
</div>
<a id="a4e1d6840e7d117886093bbaabbccd56f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4e1d6840e7d117886093bbaabbccd56f">&#9670;&nbsp;</a></span>QsciStyle() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciStyle::QsciStyle </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>description</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QColor &amp;&#160;</td>
          <td class="paramname"><em>color</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QColor &amp;&#160;</td>
          <td class="paramname"><em>paper</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QFont &amp;&#160;</td>
          <td class="paramname"><em>font</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>eolFill</em> = <code>false</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Constructs a <a class="el" href="classQsciStyle.html" title="The QsciStyle class encapsulates all the attributes of a style.">QsciStyle</a> instance for style number <em>style</em>. If <em>style</em> is negative then a new style number is automatically allocated if possible. If it is not possible then <a class="el" href="classQsciStyle.html#a61582248f6b7276db9b4a1f9582c3828">style()</a> will return a negative value. The styles description, color, paper color, font and end-of-line fill are set to <em>description</em>, <em>color</em>, <em>paper</em>, <em>font</em> and <em>eolFill</em> respectively.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#a61582248f6b7276db9b4a1f9582c3828">style()</a> </dd></dl>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="af00ea2dd20e93c5d06d9ce99cbc2cf00"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af00ea2dd20e93c5d06d9ce99cbc2cf00">&#9670;&nbsp;</a></span>setStyle()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciStyle::setStyle </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The style's number is set to <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#a61582248f6b7276db9b4a1f9582c3828">style()</a> </dd></dl>

</div>
</div>
<a id="a61582248f6b7276db9b4a1f9582c3828"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a61582248f6b7276db9b4a1f9582c3828">&#9670;&nbsp;</a></span>style()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int QsciStyle::style </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the number of the style. This will be negative if the style is invalid.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#af00ea2dd20e93c5d06d9ce99cbc2cf00">setStyle()</a> </dd></dl>

</div>
</div>
<a id="abd88d76b875c154f099b4e9f36b6fcab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abd88d76b875c154f099b4e9f36b6fcab">&#9670;&nbsp;</a></span>setDescription()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciStyle::setDescription </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>description</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The style's description is set to <em>description</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#a04e5cc64877290739b30603c526d84ce">description()</a> </dd></dl>

</div>
</div>
<a id="a04e5cc64877290739b30603c526d84ce"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a04e5cc64877290739b30603c526d84ce">&#9670;&nbsp;</a></span>description()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QString QsciStyle::description </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the style's description.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#abd88d76b875c154f099b4e9f36b6fcab">setDescription()</a> </dd></dl>

</div>
</div>
<a id="aa7743a3805662a27ae52a56af3ac315a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa7743a3805662a27ae52a56af3ac315a">&#9670;&nbsp;</a></span>setColor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciStyle::setColor </td>
          <td>(</td>
          <td class="paramtype">const QColor &amp;&#160;</td>
          <td class="paramname"><em>color</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The style's foreground color is set to <em>color</em>. The default is taken from the application's default palette.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#af349ce169da83e08ad9f995df48c6547">color()</a> </dd></dl>

</div>
</div>
<a id="af349ce169da83e08ad9f995df48c6547"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af349ce169da83e08ad9f995df48c6547">&#9670;&nbsp;</a></span>color()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciStyle::color </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the style's foreground color.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#aa7743a3805662a27ae52a56af3ac315a">setColor()</a> </dd></dl>

</div>
</div>
<a id="a2d4ec76574fd507fbf3c0d006c7427da"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2d4ec76574fd507fbf3c0d006c7427da">&#9670;&nbsp;</a></span>setPaper()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciStyle::setPaper </td>
          <td>(</td>
          <td class="paramtype">const QColor &amp;&#160;</td>
          <td class="paramname"><em>paper</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The style's background color is set to <em>paper</em>. The default is taken from the application's default palette.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#a8912da5c6b95404e4642593db1b65d4c">paper()</a> </dd></dl>

</div>
</div>
<a id="a8912da5c6b95404e4642593db1b65d4c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8912da5c6b95404e4642593db1b65d4c">&#9670;&nbsp;</a></span>paper()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciStyle::paper </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the style's background color.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#a2d4ec76574fd507fbf3c0d006c7427da">setPaper()</a> </dd></dl>

</div>
</div>
<a id="ab09932c9dafb915b8138d4ec1cbc79cb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab09932c9dafb915b8138d4ec1cbc79cb">&#9670;&nbsp;</a></span>setFont()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciStyle::setFont </td>
          <td>(</td>
          <td class="paramtype">const QFont &amp;&#160;</td>
          <td class="paramname"><em>font</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The style's font is set to <em>font</em>. The default is the application's default font.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#af45628c04ab5488fc13b61a2356346ec">font()</a> </dd></dl>

</div>
</div>
<a id="af45628c04ab5488fc13b61a2356346ec"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af45628c04ab5488fc13b61a2356346ec">&#9670;&nbsp;</a></span>font()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QFont QsciStyle::font </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the style's font.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#ab09932c9dafb915b8138d4ec1cbc79cb">setFont()</a> </dd></dl>

</div>
</div>
<a id="a6767dbb23f68292ef9e892dad31ffd9e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6767dbb23f68292ef9e892dad31ffd9e">&#9670;&nbsp;</a></span>setEolFill()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciStyle::setEolFill </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fill</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The style's end-of-line fill is set to <em>fill</em>. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#adcc34134da3341f1f07a847b09f6565b">eolFill()</a> </dd></dl>

</div>
</div>
<a id="adcc34134da3341f1f07a847b09f6565b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adcc34134da3341f1f07a847b09f6565b">&#9670;&nbsp;</a></span>eolFill()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciStyle::eolFill </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the style's end-of-line fill.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#a6767dbb23f68292ef9e892dad31ffd9e">setEolFill()</a> </dd></dl>

</div>
</div>
<a id="a25e9b8a34c334bf6160115a2c43a5256"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a25e9b8a34c334bf6160115a2c43a5256">&#9670;&nbsp;</a></span>setTextCase()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciStyle::setTextCase </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classQsciStyle.html#a4a0e012717bb1fd68de03209260a0609">TextCase</a>&#160;</td>
          <td class="paramname"><em>text_case</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The style's text case is set to <em>text_case</em>. The default is OriginalCase.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#a16212f9f46162f67ece3ed6423207785">textCase()</a> </dd></dl>

</div>
</div>
<a id="a16212f9f46162f67ece3ed6423207785"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a16212f9f46162f67ece3ed6423207785">&#9670;&nbsp;</a></span>textCase()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classQsciStyle.html#a4a0e012717bb1fd68de03209260a0609">TextCase</a> QsciStyle::textCase </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the style's text case.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#a25e9b8a34c334bf6160115a2c43a5256">setTextCase()</a> </dd></dl>

</div>
</div>
<a id="a4f8b9edd94c36344bd7152d15731509a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4f8b9edd94c36344bd7152d15731509a">&#9670;&nbsp;</a></span>setVisible()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciStyle::setVisible </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>visible</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The style's visibility is set to <em>visible</em>. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#a0fd0947a87e3bf43720227b8226b3edd">visible()</a> </dd></dl>

</div>
</div>
<a id="a0fd0947a87e3bf43720227b8226b3edd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0fd0947a87e3bf43720227b8226b3edd">&#9670;&nbsp;</a></span>visible()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciStyle::visible </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the style's visibility.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#a4f8b9edd94c36344bd7152d15731509a">setVisible()</a> </dd></dl>

</div>
</div>
<a id="af7e052d08efd3a677f810c8e4116dafc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af7e052d08efd3a677f810c8e4116dafc">&#9670;&nbsp;</a></span>setChangeable()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciStyle::setChangeable </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>changeable</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The style's changeability is set to <em>changeable</em>. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#a4d1aa13e042609e48674f72aebd2ebae">changeable()</a> </dd></dl>

</div>
</div>
<a id="a4d1aa13e042609e48674f72aebd2ebae"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4d1aa13e042609e48674f72aebd2ebae">&#9670;&nbsp;</a></span>changeable()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciStyle::changeable </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the style's changeability.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#af7e052d08efd3a677f810c8e4116dafc">setChangeable()</a> </dd></dl>

</div>
</div>
<a id="acb06ba468da57cc4ea9e8d496cb33f83"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acb06ba468da57cc4ea9e8d496cb33f83">&#9670;&nbsp;</a></span>setHotspot()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciStyle::setHotspot </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>hotspot</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The style's sensitivity to mouse clicks is set to <em>hotspot</em>. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#a1df46714ab45c62e5ad5e52a5f41bf15">hotspot()</a> </dd></dl>

</div>
</div>
<a id="a1df46714ab45c62e5ad5e52a5f41bf15"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1df46714ab45c62e5ad5e52a5f41bf15">&#9670;&nbsp;</a></span>hotspot()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciStyle::hotspot </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the style's sensitivity to mouse clicks.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciStyle.html#acb06ba468da57cc4ea9e8d496cb33f83">setHotspot()</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
