<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciDocument Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classQsciDocument-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciDocument Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscidocument.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a3da32a3198c407aa692764ccd98ad66f"><td class="memItemLeft" align="right" valign="top"><a id="a3da32a3198c407aa692764ccd98ad66f"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciDocument.html#a3da32a3198c407aa692764ccd98ad66f">QsciDocument</a> ()</td></tr>
<tr class="separator:a3da32a3198c407aa692764ccd98ad66f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a08517919b3caaf3815183823d2ffbd7c"><td class="memItemLeft" align="right" valign="top"><a id="a08517919b3caaf3815183823d2ffbd7c"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>QsciDocument</b> (const <a class="el" href="classQsciDocument.html">QsciDocument</a> &amp;)</td></tr>
<tr class="separator:a08517919b3caaf3815183823d2ffbd7c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a831e7d3de0e0a36be46f6da3fc49d715"><td class="memItemLeft" align="right" valign="top"><a id="a831e7d3de0e0a36be46f6da3fc49d715"></a>
<a class="el" href="classQsciDocument.html">QsciDocument</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><b>operator=</b> (const <a class="el" href="classQsciDocument.html">QsciDocument</a> &amp;)</td></tr>
<tr class="separator:a831e7d3de0e0a36be46f6da3fc49d715"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciDocument.html" title="The QsciDocument class represents a document to be edited.">QsciDocument</a> class represents a document to be edited. </p>
<p>It is an opaque class that can be attached to multiple instances of <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> to create different simultaneous views of the same document. <a class="el" href="classQsciDocument.html" title="The QsciDocument class represents a document to be edited.">QsciDocument</a> uses implicit sharing so that copying class instances is a cheap operation. </p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
