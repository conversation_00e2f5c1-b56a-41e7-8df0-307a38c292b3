<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciLexerPOV Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">autoCompletionWordSeparators</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#a3ab9a4de5f6885945d3d780142501865a50caef61534d689d00e80efbf631fffd">BadDirective</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">blockEnd</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">blockStart</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#adfb83ee7ea262a33f775d1e53cf38bec">braceStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bytesAsText</b>(const char *bytes, int size) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#a3ab9a4de5f6885945d3d780142501865ae5ca8ff1353ee7c45d6ce5d6e3fd1f00">Comment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#a3ab9a4de5f6885945d3d780142501865aa21767d42e17e6f895efa2b180f264bb">CommentLine</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#a3ab9a4de5f6885945d3d780142501865a07b640606eb18fe3f8dfc49bbc91a415">Default</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#ae9fc5faac317ee19add21f8105ff21c5">defaultColor</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">QsciLexer::defaultColor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#a34fa0bd92884cfa29a27c279369797d5">defaultEolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#af6839d80f9b92eaead072803664a497f">defaultFont</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">QsciLexer::defaultFont</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#a5d074b15d624c82c5931ceba7a91a455">defaultPaper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">QsciLexer::defaultPaper</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#a71cf91642f6879964a061133013a1f51">description</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#a3ab9a4de5f6885945d3d780142501865a8a608b891ac344348afeedfe8a4ac54b">Directive</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a>(bool eolfilled, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#a7a08d9dcb4935d7e2c99696bdcfd8e7a">foldComments</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#a9a4ec081c6812ffb3ebc5082c08bf0db">foldCompact</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#ad16788518def261f1ce55b35141642ad">foldDirectives</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a>(const QFont &amp;f, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#a3ab9a4de5f6885945d3d780142501865ace12eb00b29be82c86869c131c43bd7f">Identifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#a6b21e4498723f3a01fe468e03ebe04f4">keywords</a>(int set) const</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#a3ab9a4de5f6885945d3d780142501865ad6fd6fa9940116fcd7f01371d01f530b">KeywordSet6</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#a3ab9a4de5f6885945d3d780142501865a27a6dc70698893bcc922c69dcac4a8fa">KeywordSet7</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#a3ab9a4de5f6885945d3d780142501865a50d0c3a0bb96dd811592c6fa6348b66f">KeywordSet8</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#a02880268227d380ef25a72af2605ef0f">language</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#af41ceced7bf5eb12aefb77f81240b1eb">lexer</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#a3ab9a4de5f6885945d3d780142501865a46e89975e80b8b1ccada568900f784f7">Number</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#a3ab9a4de5f6885945d3d780142501865a02499b3710237cdd660cf1bce89f27bf">ObjectsCSGAppearance</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#a3ab9a4de5f6885945d3d780142501865abe05c40246afb65563d5e5013977240d">Operator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#a3ab9a4de5f6885945d3d780142501865af91917dbaed11a4006fa44e3e27d59c7">PredefinedFunctions</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#a3ab9a4de5f6885945d3d780142501865a626eefd58e520c62f69320fd00ab2869">PredefinedIdentifiers</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a>(const char *prop, const char *val)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#a4f286fb01fbf71a895a6a6ca2424b9c5">QsciLexerPOV</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#a5a599e7d97b164fec1ee3c21ba167e80">readProperties</a>(QSettings &amp;qs, const QString &amp;prefix)</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#a4864bf9360ed4748b9ca7a1d5e34e7d8">refreshProperties</a>()</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a>(QsciAbstractAPIs *apis)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a>(int autoindentstyle)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a>(const QFont &amp;f)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>setEditor</b>(QsciScintilla *editor) (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a>(bool eoffill, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#a189a9efbe5c2fa07757d67c013229e19">setFoldComments</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#a831ed1e8074990eafb57d4b9ebaf3d2f">setFoldCompact</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#aea30a66d5e4e7d0064366eefec03364c">setFoldDirectives</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a>(const QFont &amp;f, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#a3ab9a4de5f6885945d3d780142501865a9eae7fcd2dbdb17e1aeaf6eb5853a5b2">String</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>textAsBytes</b>(const QString &amp;text) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#a3ab9a4de5f6885945d3d780142501865ab4fc4fd29a371a49c534a75dc1bc55ee">TypesModifiersItems</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#a3ab9a4de5f6885945d3d780142501865adcae9bdc3f27f2791e7b6a0608fcd6f2">UnclosedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#aa45fd60cb7c2db5c88b5708f481dd6e2">wordCharacters</a>() const</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerPOV.html#a99f8420666e55b23980d05903e7eebc3">writeProperties</a>(QSettings &amp;qs, const QString &amp;prefix) const</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerPOV.html#a920953f5bde920bb22e853fc5aa6ef8d">~QsciLexerPOV</a>()</td><td class="entry"><a class="el" href="classQsciLexerPOV.html">QsciLexerPOV</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
