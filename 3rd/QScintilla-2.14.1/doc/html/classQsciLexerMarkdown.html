<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciLexerMarkdown Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classQsciLexerMarkdown-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciLexerMarkdown Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscilexermarkdown.h&gt;</code></p>

<p>Inherits <a class="el" href="classQsciLexer.html">QsciLexer</a>.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:ad09694087faec9ff4f49ff5cc3388e54"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a6682db85866de38b1b3b0a02749a05d4">Default</a> = 0, 
<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54ade428e04a07f3c12bc49b3894ac9f308">Special</a> = 1, 
<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a31fcb70a32415babac5b88241cb73623">StrongEmphasisAsterisks</a> = 2, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54aaa8e9324e7aea282138de01e76f8f56c">StrongEmphasisUnderscores</a> = 3, 
<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a3b86f1f637bdd5a9f894bcf8d94039c4">EmphasisAsterisks</a> = 4, 
<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a6d496317b63aabf2f94b7abd0681ffc7">EmphasisUnderscores</a> = 5, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a72b1c31bd564177da4442c5c3ad29673">Header1</a> = 6, 
<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a2a8592f89e9ed96dc5284f7532fd4b5a">Header2</a> = 7, 
<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a2603a8da38b275a2a6663fb1a0be0013">Header3</a> = 8, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a4b1050c5cdb3517a798c5cfeac8021d7">Header4</a> = 9, 
<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54ac7776d56b4bf5a50f220408aa6f825a9">Header5</a> = 10, 
<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a7058cb8ab326921f8d2165c7031eaabe">Header6</a> = 11, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a3b030c189a22e2cdad1db39f200048d0">Prechar</a> = 12, 
<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a4dee86f709fab3714e84cadbb9f6c4a3">UnorderedListItem</a> = 13, 
<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a59adacf9920946acf11060eb1c70a3ff">OrderedListItem</a> = 14, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a802a025f696e2e1a7800f212e48da6fb">BlockQuote</a> = 15, 
<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a5d43deb58f0cb230bcb445b304b0127e">StrikeOut</a> = 16, 
<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a98d328ad6d4dd08d1cea6896c01d0bf1">HorizontalRule</a> = 17, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54aba09f8253217b599639184da32985c54">Link</a> = 18, 
<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a24f1888af4753fb171b38ea00a6b4fd6">CodeBackticks</a> = 19, 
<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a4d8694cfbb7b37351d09d070bab264bc">CodeDoubleBackticks</a> = 20, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54aec90058e8208d49ab7d8e226d69cd670">CodeBlock</a> = 21
<br />
 }</td></tr>
<tr class="separator:ad09694087faec9ff4f49ff5cc3388e54"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aeffb57391fe593ab01e6f257f95ad2f6"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerMarkdown.html#aeffb57391fe593ab01e6f257f95ad2f6">QsciLexerMarkdown</a> (QObject *parent=0)</td></tr>
<tr class="separator:aeffb57391fe593ab01e6f257f95ad2f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5372d959cc774781c7271334b2c61b4f"><td class="memItemLeft" align="right" valign="top"><a id="a5372d959cc774781c7271334b2c61b4f"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerMarkdown.html#a5372d959cc774781c7271334b2c61b4f">~QsciLexerMarkdown</a> ()</td></tr>
<tr class="separator:a5372d959cc774781c7271334b2c61b4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7a06bdb17ebde731368ec204404ed0ee"><td class="memItemLeft" align="right" valign="top"><a id="a7a06bdb17ebde731368ec204404ed0ee"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerMarkdown.html#a7a06bdb17ebde731368ec204404ed0ee">language</a> () const</td></tr>
<tr class="separator:a7a06bdb17ebde731368ec204404ed0ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af912a1a568b342c99f70fab70d89b178"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerMarkdown.html#af912a1a568b342c99f70fab70d89b178">lexer</a> () const</td></tr>
<tr class="separator:af912a1a568b342c99f70fab70d89b178"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d0cd9ae9bac9e8fc29477ce1f0b9ca1"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerMarkdown.html#a2d0cd9ae9bac9e8fc29477ce1f0b9ca1">defaultColor</a> (int style) const</td></tr>
<tr class="separator:a2d0cd9ae9bac9e8fc29477ce1f0b9ca1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab4a9d2b6e3aeee22d7636072f5163499"><td class="memItemLeft" align="right" valign="top"><a id="ab4a9d2b6e3aeee22d7636072f5163499"></a>
QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerMarkdown.html#ab4a9d2b6e3aeee22d7636072f5163499">defaultFont</a> (int style) const</td></tr>
<tr class="separator:ab4a9d2b6e3aeee22d7636072f5163499"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acecf54d7daf87ff9fc5464fac8f1d502"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerMarkdown.html#acecf54d7daf87ff9fc5464fac8f1d502">defaultPaper</a> (int style) const</td></tr>
<tr class="separator:acecf54d7daf87ff9fc5464fac8f1d502"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2f1340e861947f7c8c4299b1c9ded5a5"><td class="memItemLeft" align="right" valign="top">QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerMarkdown.html#a2f1340e861947f7c8c4299b1c9ded5a5">description</a> (int style) const</td></tr>
<tr class="separator:a2f1340e861947f7c8c4299b1c9ded5a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a> (QObject *parent=0)</td></tr>
<tr class="separator:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="af6cc5bb9d9421d806e9941d018030068"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a> ()</td></tr>
<tr class="separator:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a> () const</td></tr>
<tr class="separator:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a> () const</td></tr>
<tr class="separator:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a6504a6fff35af16fbfd97889048db2a5"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a> () const</td></tr>
<tr class="separator:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e10650b0e9ad137062ad5c17ad33e76 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QStringList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">autoCompletionWordSeparators</a> () const</td></tr>
<tr class="separator:a4e10650b0e9ad137062ad5c17ad33e76 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a> ()</td></tr>
<tr class="separator:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e294eba77713f516acbcebc10af1493 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">blockEnd</a> (int *style=0) const</td></tr>
<tr class="separator:a8e294eba77713f516acbcebc10af1493 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a> () const</td></tr>
<tr class="separator:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a340eafe726fd6964c0adba956fe3428c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">blockStart</a> (int *style=0) const</td></tr>
<tr class="separator:a340eafe726fd6964c0adba956fe3428c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf12117a142b6f68479ea425d80a4196 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a> (int *style=0) const</td></tr>
<tr class="separator:abf12117a142b6f68479ea425d80a4196 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:affe136114d62180e9a14caa81f2b7fd5 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="affe136114d62180e9a14caa81f2b7fd5"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#affe136114d62180e9a14caa81f2b7fd5">braceStyle</a> () const</td></tr>
<tr class="separator:affe136114d62180e9a14caa81f2b7fd5 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="afccca7eb1aed463f89ac442d99135839"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a> () const</td></tr>
<tr class="separator:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a> (int style) const</td></tr>
<tr class="separator:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a> (int style) const</td></tr>
<tr class="separator:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a> (int style) const</td></tr>
<tr class="separator:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="aff4735542e937c5e35ecb2eb82e8f875"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a> () const</td></tr>
<tr class="separator:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a013b7c1bf9846e231b97827dfd9540b0 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a013b7c1bf9846e231b97827dfd9540b0">keywords</a> (int set) const</td></tr>
<tr class="separator:a013b7c1bf9846e231b97827dfd9540b0 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a> () const</td></tr>
<tr class="separator:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a> (int style) const</td></tr>
<tr class="separator:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">defaultColor</a> () const</td></tr>
<tr class="separator:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a06228b73f8df699a211be872f54d8501 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a06228b73f8df699a211be872f54d8501">defaultEolFill</a> (int style) const</td></tr>
<tr class="separator:a06228b73f8df699a211be872f54d8501 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">defaultFont</a> () const</td></tr>
<tr class="separator:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">defaultPaper</a> () const</td></tr>
<tr class="separator:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciScintilla.html">QsciScintilla</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a> () const</td></tr>
<tr class="separator:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a> (<a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *<a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>)</td></tr>
<tr class="separator:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a> (const QFont &amp;f)</td></tr>
<tr class="separator:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a1e81186b1f8f8bc2a4901a42cbca568a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>setEditor</b> (<a class="el" href="classQsciScintilla.html">QsciScintilla</a> *<a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>)</td></tr>
<tr class="separator:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td></tr>
<tr class="separator:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae508c3ab4ce1f338dfff3ddf5ee7e34c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ae508c3ab4ce1f338dfff3ddf5ee7e34c">refreshProperties</a> ()</td></tr>
<tr class="separator:ae508c3ab4ce1f338dfff3ddf5ee7e34c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a> () const</td></tr>
<tr class="separator:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aace68e3dbcef9da1b031fb9cfd843c57 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aace68e3dbcef9da1b031fb9cfd843c57">wordCharacters</a> () const</td></tr>
<tr class="separator:aace68e3dbcef9da1b031fb9cfd843c57 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td></tr>
<tr class="separator:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_slots_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_slots_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Slots inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a> (int autoindentstyle)</td></tr>
<tr class="separator:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a> (bool eoffill, int style=-1)</td></tr>
<tr class="separator:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a> (const QFont &amp;f, int style=-1)</td></tr>
<tr class="separator:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header signals_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('signals_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Signals inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a> (bool eolfilled, int style)</td></tr>
<tr class="separator:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a> (const QFont &amp;f, int style)</td></tr>
<tr class="separator:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a> (const char *prop, const char *val)</td></tr>
<tr class="separator:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pro_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:ad472b16506a4cbc19634f07aa90f1ea6 inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ad472b16506a4cbc19634f07aa90f1ea6">readProperties</a> (QSettings &amp;qs, const QString &amp;prefix)</td></tr>
<tr class="separator:ad472b16506a4cbc19634f07aa90f1ea6 inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abccc4e010b724df1a7b5c5f3bce29501 inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abccc4e010b724df1a7b5c5f3bce29501">writeProperties</a> (QSettings &amp;qs, const QString &amp;prefix) const</td></tr>
<tr class="separator:abccc4e010b724df1a7b5c5f3bce29501 inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a41d4521504d63ee63d43fd7ed0c003ee"></a>
QByteArray&#160;</td><td class="memItemRight" valign="bottom"><b>textAsBytes</b> (const QString &amp;text) const</td></tr>
<tr class="separator:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a5261dd606c209a5c6a494e608a9a111a"></a>
QString&#160;</td><td class="memItemRight" valign="bottom"><b>bytesAsText</b> (const char *bytes, int size) const</td></tr>
<tr class="separator:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciLexerMarkdown.html" title="The QsciLexerMarkdown class encapsulates the Scintilla Markdown lexer.">QsciLexerMarkdown</a> class encapsulates the Scintilla Markdown lexer. </p>
</div><h2 class="groupheader">Member Enumeration Documentation</h2>
<a id="ad09694087faec9ff4f49ff5cc3388e54"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad09694087faec9ff4f49ff5cc3388e54">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the meanings of the different styles used by the Markdown lexer. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54a6682db85866de38b1b3b0a02749a05d4"></a>Default&#160;</td><td class="fielddoc"><p>The default. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54ade428e04a07f3c12bc49b3894ac9f308"></a>Special&#160;</td><td class="fielddoc"><p>Special (e.g. end-of-line codes if enabled). </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54a31fcb70a32415babac5b88241cb73623"></a>StrongEmphasisAsterisks&#160;</td><td class="fielddoc"><p>Strong emphasis using double asterisks. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54aaa8e9324e7aea282138de01e76f8f56c"></a>StrongEmphasisUnderscores&#160;</td><td class="fielddoc"><p>Strong emphasis using double underscores. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54a3b86f1f637bdd5a9f894bcf8d94039c4"></a>EmphasisAsterisks&#160;</td><td class="fielddoc"><p>Emphasis using single asterisks. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54a6d496317b63aabf2f94b7abd0681ffc7"></a>EmphasisUnderscores&#160;</td><td class="fielddoc"><p>Emphasis using single underscores. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54a72b1c31bd564177da4442c5c3ad29673"></a>Header1&#160;</td><td class="fielddoc"><p>A level 1 header. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54a2a8592f89e9ed96dc5284f7532fd4b5a"></a>Header2&#160;</td><td class="fielddoc"><p>A level 2 header. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54a2603a8da38b275a2a6663fb1a0be0013"></a>Header3&#160;</td><td class="fielddoc"><p>A level 3 header. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54a4b1050c5cdb3517a798c5cfeac8021d7"></a>Header4&#160;</td><td class="fielddoc"><p>A level 4 header. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54ac7776d56b4bf5a50f220408aa6f825a9"></a>Header5&#160;</td><td class="fielddoc"><p>A level 5 header. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54a7058cb8ab326921f8d2165c7031eaabe"></a>Header6&#160;</td><td class="fielddoc"><p>A level 6 header. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54a3b030c189a22e2cdad1db39f200048d0"></a>Prechar&#160;</td><td class="fielddoc"><p>Pre-char (up to three indent spaces, e.g. for a sub-list). </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54a4dee86f709fab3714e84cadbb9f6c4a3"></a>UnorderedListItem&#160;</td><td class="fielddoc"><p>An unordered list item. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54a59adacf9920946acf11060eb1c70a3ff"></a>OrderedListItem&#160;</td><td class="fielddoc"><p>An ordered list item. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54a802a025f696e2e1a7800f212e48da6fb"></a>BlockQuote&#160;</td><td class="fielddoc"><p>A block quote. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54a5d43deb58f0cb230bcb445b304b0127e"></a>StrikeOut&#160;</td><td class="fielddoc"><p>Strike out. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54a98d328ad6d4dd08d1cea6896c01d0bf1"></a>HorizontalRule&#160;</td><td class="fielddoc"><p>A horizontal rule. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54aba09f8253217b599639184da32985c54"></a>Link&#160;</td><td class="fielddoc"><p>A link. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54a24f1888af4753fb171b38ea00a6b4fd6"></a>CodeBackticks&#160;</td><td class="fielddoc"><p>Code between backticks. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54a4d8694cfbb7b37351d09d070bab264bc"></a>CodeDoubleBackticks&#160;</td><td class="fielddoc"><p>Code between double backticks. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad09694087faec9ff4f49ff5cc3388e54aec90058e8208d49ab7d8e226d69cd670"></a>CodeBlock&#160;</td><td class="fielddoc"><p>A code block. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="aeffb57391fe593ab01e6f257f95ad2f6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aeffb57391fe593ab01e6f257f95ad2f6">&#9670;&nbsp;</a></span>QsciLexerMarkdown()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciLexerMarkdown::QsciLexerMarkdown </td>
          <td>(</td>
          <td class="paramtype">QObject *&#160;</td>
          <td class="paramname"><em>parent</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Construct a <a class="el" href="classQsciLexerMarkdown.html" title="The QsciLexerMarkdown class encapsulates the Scintilla Markdown lexer.">QsciLexerMarkdown</a> with parent <em>parent</em>. <em>parent</em> is typically the <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> instance. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="af912a1a568b342c99f70fab70d89b178"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af912a1a568b342c99f70fab70d89b178">&#9670;&nbsp;</a></span>lexer()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerMarkdown::lexer </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the name of the lexer. Some lexers support a number of languages. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ac7c40b97187e23ab85f6d95113f91b39">QsciLexer</a>.</p>

</div>
</div>
<a id="a2d0cd9ae9bac9e8fc29477ce1f0b9ca1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2d0cd9ae9bac9e8fc29477ce1f0b9ca1">&#9670;&nbsp;</a></span>defaultColor()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerMarkdown::defaultColor </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the foreground colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerMarkdown.html#acecf54d7daf87ff9fc5464fac8f1d502">defaultPaper()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#af7508f1b816a2c9446d36141edc9b5ce">QsciLexer</a>.</p>

</div>
</div>
<a id="acecf54d7daf87ff9fc5464fac8f1d502"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acecf54d7daf87ff9fc5464fac8f1d502">&#9670;&nbsp;</a></span>defaultPaper()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerMarkdown::defaultPaper </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the background colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerMarkdown.html#a2d0cd9ae9bac9e8fc29477ce1f0b9ca1">defaultColor()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a7e5ab7f541d913760c32abedbdc72963">QsciLexer</a>.</p>

</div>
</div>
<a id="a2f1340e861947f7c8c4299b1c9ded5a5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2f1340e861947f7c8c4299b1c9ded5a5">&#9670;&nbsp;</a></span>description()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QString QsciLexerMarkdown::description </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the descriptive name for style number <em>style</em>. If the style is invalid for this language then an empty QString is returned. This is intended to be used in user preference dialogs. </p>

<p>Implements <a class="el" href="classQsciLexer.html#add9c20adb43bc38d1a0ca3083ac3e6fa">QsciLexer</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
