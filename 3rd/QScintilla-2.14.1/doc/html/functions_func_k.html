<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_k"></a>- k -</h3><ul>
<li>key()
: <a class="el" href="classQsciCommand.html#abf9dffcf6c222ecc02b28c3f6d17eb8e">QsciCommand</a>
</li>
<li>keyPressEvent()
: <a class="el" href="classQsciScintillaBase.html#a39f62b8e6cee02e86d7af508d20a191d">QsciScintillaBase</a>
</li>
<li>keywords()
: <a class="el" href="classQsciLexer.html#a013b7c1bf9846e231b97827dfd9540b0">QsciLexer</a>
, <a class="el" href="classQsciLexerAsm.html#ad4eae3482cf519fc237705b9cb1aa87d">QsciLexerAsm</a>
, <a class="el" href="classQsciLexerAVS.html#a9af4c417c88911b8c0ca653d643e3778">QsciLexerAVS</a>
, <a class="el" href="classQsciLexerBash.html#ac1665f22a91f143e6e6fb46b02e7b109">QsciLexerBash</a>
, <a class="el" href="classQsciLexerBatch.html#ac9329cbc86f1f1a915e548997af76a5f">QsciLexerBatch</a>
, <a class="el" href="classQsciLexerCMake.html#a90ed658a569976a68f1260901b7b3518">QsciLexerCMake</a>
, <a class="el" href="classQsciLexerCoffeeScript.html#a74867915ad9d609b9b516eff87101cc9">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#ac331bbae026859d8020ac5a6efd8fed1">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerCSharp.html#a07fcac621f1cba033bb0918cf9d35231">QsciLexerCSharp</a>
, <a class="el" href="classQsciLexerCSS.html#a41d04b17da9c84a94289e91323fb5206">QsciLexerCSS</a>
, <a class="el" href="classQsciLexerD.html#a9fc58fb17acc5e669780cb870d633514">QsciLexerD</a>
, <a class="el" href="classQsciLexerFortran77.html#a21724c1f53b67ec6bc72c7ceb1e03d8f">QsciLexerFortran77</a>
, <a class="el" href="classQsciLexerFortran.html#a56e0fd6b5d719677050a28ad0d5ae927">QsciLexerFortran</a>
, <a class="el" href="classQsciLexerHTML.html#a56b7f081e520f7660490e3d206d83a73">QsciLexerHTML</a>
, <a class="el" href="classQsciLexerIDL.html#a1fd3bee8279c7e3600ec0ed72dbc2d00">QsciLexerIDL</a>
, <a class="el" href="classQsciLexerJava.html#ad741254381ce4447588d190ad9c67783">QsciLexerJava</a>
, <a class="el" href="classQsciLexerJavaScript.html#af00e1d05374302fd4d2e2eeec1a829ee">QsciLexerJavaScript</a>
, <a class="el" href="classQsciLexerJSON.html#af4a9c85e527eda6c28663f055afa0be2">QsciLexerJSON</a>
, <a class="el" href="classQsciLexerLua.html#a136982546f34f83f5e3dd21f67074d4d">QsciLexerLua</a>
, <a class="el" href="classQsciLexerMatlab.html#a7afb79f0fec38396668dd52de7fc7c4b">QsciLexerMatlab</a>
, <a class="el" href="classQsciLexerOctave.html#a72ce450fad8282f4c02cf28fc6a4b9d2">QsciLexerOctave</a>
, <a class="el" href="classQsciLexerPascal.html#a9b6f6a462314471262e5f29057839b34">QsciLexerPascal</a>
, <a class="el" href="classQsciLexerPerl.html#a57958c564d4d3127e7ee6148d232bd4b">QsciLexerPerl</a>
, <a class="el" href="classQsciLexerPostScript.html#a981f7ababe1cc561b29617fad8aa29b5">QsciLexerPostScript</a>
, <a class="el" href="classQsciLexerPOV.html#a6b21e4498723f3a01fe468e03ebe04f4">QsciLexerPOV</a>
, <a class="el" href="classQsciLexerPython.html#a2467729449b6c78d63305b88b2f62789">QsciLexerPython</a>
, <a class="el" href="classQsciLexerRuby.html#abd6f026e6cb154c64c581f6e5f7f2fed">QsciLexerRuby</a>
, <a class="el" href="classQsciLexerSpice.html#ac4a5d52373228003f7bd51dade64fc85">QsciLexerSpice</a>
, <a class="el" href="classQsciLexerSQL.html#ac74a6288e07e20f18ad04e900b48851b">QsciLexerSQL</a>
, <a class="el" href="classQsciLexerTCL.html#a8739852ad69fa4686f0fabd61d18b214">QsciLexerTCL</a>
, <a class="el" href="classQsciLexerTeX.html#aed0f87e43716cf9894e27e0b90396a98">QsciLexerTeX</a>
, <a class="el" href="classQsciLexerVerilog.html#aebb96727a845f9547a60848f6163d461">QsciLexerVerilog</a>
, <a class="el" href="classQsciLexerVHDL.html#aed2f3934c2fe336324d6e79526c2f7a8">QsciLexerVHDL</a>
, <a class="el" href="classQsciLexerXML.html#ab78937576c3c727f073921059ac87a59">QsciLexerXML</a>
, <a class="el" href="classQsciLexerYAML.html#add226b6ffbaee63c29a1f0da7de25784">QsciLexerYAML</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
