<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciLexerCoffeeScript Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="classQsciLexerCoffeeScript-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciLexerCoffeeScript Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscilexercoffeescript.h&gt;</code></p>

<p>Inherits <a class="el" href="classQsciLexer.html">QsciLexer</a>.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a3e2bfca47ca0666b7acb6a451d203fa8"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a2fb70f93de6a55714777a4fa55916d03">Default</a> = 0, 
<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a908ae06d736d3add37f734a255ceeaa3">Comment</a> = 1, 
<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a379b349ef6edd66b752af87472fe41b4">CommentLine</a> = 2, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a5edc735d0127917185abed1f637a49f7">CommentDoc</a> = 3, 
<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8aba8f2217a58a7603d2a69ea1edeb1bc4">Number</a> = 4, 
<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a4a6519d9d7b7e0e068d6ce8b777a87d2">Keyword</a> = 5, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a9eb257127f3fd60ea6ee7ef126419f7d">DoubleQuotedString</a> = 6, 
<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8ad64eca43c5aa797920a0b5db86c7ebb7">SingleQuotedString</a> = 7, 
<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a063d7d591aa18ae40fcde793e1be1f01">UUID</a> = 8, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a84af89605b0d39edc60401dee749d076">PreProcessor</a> = 9, 
<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8afd477421bbc3829c44d0ceda25ef07ec">Operator</a> = 10, 
<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a6fada5b4984531d13a0f03cf9bd082f8">Identifier</a> = 11, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8ad66f62da8384e226b3c0e33455d93bd4">UnclosedString</a> = 12, 
<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a6f653903645cf19e5ea1c7e870ae9efb">VerbatimString</a> = 13, 
<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8aca5c64a4bc75adb3be878a492906cfba">Regex</a> = 14, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a406ac7ec4b5186a2d33b7a9074f6fa02">CommentLineDoc</a> = 15, 
<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8ae9524bc4d07a86f58eb88d57c1291083">KeywordSet2</a> = 16, 
<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a7e8b105503aff566abe10b78bfff1575">CommentDocKeyword</a> = 17, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a48d773f3fce4500a8700b6d76f2ecf24">CommentDocKeywordError</a> = 18, 
<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8ab4d4a5d44c3c3584609996183880c179">GlobalClass</a> = 19, 
<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a2050935e0699ccd6660987e5b6f42c32">CommentBlock</a> = 22, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a32607d1ce220881542a049d83406ce65">BlockRegex</a> = 23, 
<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8acf3a1887749e806e04bf6a0097f724bb">BlockRegexComment</a> = 24, 
<a class="el" href="classQsciLexerCoffeeScript.html#a3e2bfca47ca0666b7acb6a451d203fa8a67579947345d4b8bca1317e697fe46d3">InstanceProperty</a> = 25
<br />
 }</td></tr>
<tr class="separator:a3e2bfca47ca0666b7acb6a451d203fa8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a73e71780818247bf678616a25cd13e90"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#a73e71780818247bf678616a25cd13e90">QsciLexerCoffeeScript</a> (QObject *parent=0)</td></tr>
<tr class="separator:a73e71780818247bf678616a25cd13e90"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7715c2c90861e2601587b8a3a6732fd"><td class="memItemLeft" align="right" valign="top"><a id="ab7715c2c90861e2601587b8a3a6732fd"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#ab7715c2c90861e2601587b8a3a6732fd">~QsciLexerCoffeeScript</a> ()</td></tr>
<tr class="separator:ab7715c2c90861e2601587b8a3a6732fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a126d81ec982782507eafae1af5d0d856"><td class="memItemLeft" align="right" valign="top"><a id="a126d81ec982782507eafae1af5d0d856"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#a126d81ec982782507eafae1af5d0d856">language</a> () const</td></tr>
<tr class="separator:a126d81ec982782507eafae1af5d0d856"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac009a767572be4b4489a0613611cbdb"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#aac009a767572be4b4489a0613611cbdb">lexer</a> () const</td></tr>
<tr class="separator:aac009a767572be4b4489a0613611cbdb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0741fad0b942deb73642be16c3159eb1"><td class="memItemLeft" align="right" valign="top">QStringList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#a0741fad0b942deb73642be16c3159eb1">autoCompletionWordSeparators</a> () const</td></tr>
<tr class="separator:a0741fad0b942deb73642be16c3159eb1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1796c98b07ec6cfc3d5953c225cc1f37"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#a1796c98b07ec6cfc3d5953c225cc1f37">blockEnd</a> (int *style=0) const</td></tr>
<tr class="separator:a1796c98b07ec6cfc3d5953c225cc1f37"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a293f0a5c39990ec1db6de249dc618901"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#a293f0a5c39990ec1db6de249dc618901">blockStart</a> (int *style=0) const</td></tr>
<tr class="separator:a293f0a5c39990ec1db6de249dc618901"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a497144db9b43beba78cd405a795e08ac"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#a497144db9b43beba78cd405a795e08ac">blockStartKeyword</a> (int *style=0) const</td></tr>
<tr class="separator:a497144db9b43beba78cd405a795e08ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add9b1d85d9da1c250f570482cd47eb39"><td class="memItemLeft" align="right" valign="top"><a id="add9b1d85d9da1c250f570482cd47eb39"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#add9b1d85d9da1c250f570482cd47eb39">braceStyle</a> () const</td></tr>
<tr class="separator:add9b1d85d9da1c250f570482cd47eb39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e5910796ca5a3f369258718bb75c1d8"><td class="memItemLeft" align="right" valign="top"><a id="a2e5910796ca5a3f369258718bb75c1d8"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#a2e5910796ca5a3f369258718bb75c1d8">wordCharacters</a> () const</td></tr>
<tr class="separator:a2e5910796ca5a3f369258718bb75c1d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab2e7d189deabf8e5e20434e32346742c"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#ab2e7d189deabf8e5e20434e32346742c">defaultColor</a> (int style) const</td></tr>
<tr class="separator:ab2e7d189deabf8e5e20434e32346742c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad8c778b4c9ef2014e5a508f0ee52021"><td class="memItemLeft" align="right" valign="top"><a id="aad8c778b4c9ef2014e5a508f0ee52021"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#aad8c778b4c9ef2014e5a508f0ee52021">defaultEolFill</a> (int style) const</td></tr>
<tr class="separator:aad8c778b4c9ef2014e5a508f0ee52021"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac84577ad9cdb480293fe6001e71085a8"><td class="memItemLeft" align="right" valign="top"><a id="ac84577ad9cdb480293fe6001e71085a8"></a>
QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#ac84577ad9cdb480293fe6001e71085a8">defaultFont</a> (int style) const</td></tr>
<tr class="separator:ac84577ad9cdb480293fe6001e71085a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a402a849f5eed391f0c4cd3aac9beb075"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#a402a849f5eed391f0c4cd3aac9beb075">defaultPaper</a> (int style) const</td></tr>
<tr class="separator:a402a849f5eed391f0c4cd3aac9beb075"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a74867915ad9d609b9b516eff87101cc9"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#a74867915ad9d609b9b516eff87101cc9">keywords</a> (int set) const</td></tr>
<tr class="separator:a74867915ad9d609b9b516eff87101cc9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ace6bf74522c57e70f2c3ac525e1fd830"><td class="memItemLeft" align="right" valign="top">QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#ace6bf74522c57e70f2c3ac525e1fd830">description</a> (int style) const</td></tr>
<tr class="separator:ace6bf74522c57e70f2c3ac525e1fd830"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aefae6df689f1d3dad66d1f2fc141cc39"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#aefae6df689f1d3dad66d1f2fc141cc39">refreshProperties</a> ()</td></tr>
<tr class="separator:aefae6df689f1d3dad66d1f2fc141cc39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5b95ed33711b09385c92fbfb9f1d2a5d"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#a5b95ed33711b09385c92fbfb9f1d2a5d">dollarsAllowed</a> () const</td></tr>
<tr class="separator:a5b95ed33711b09385c92fbfb9f1d2a5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:affaec4d14f7908f7d24d16937df00c93"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#affaec4d14f7908f7d24d16937df00c93">setDollarsAllowed</a> (bool allowed)</td></tr>
<tr class="separator:affaec4d14f7908f7d24d16937df00c93"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6865962a5df72e37f4ba49c6e5e539b6"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#a6865962a5df72e37f4ba49c6e5e539b6">foldComments</a> () const</td></tr>
<tr class="separator:a6865962a5df72e37f4ba49c6e5e539b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a57f1f1164f3719b4b855a3a163a78764"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#a57f1f1164f3719b4b855a3a163a78764">setFoldComments</a> (bool fold)</td></tr>
<tr class="separator:a57f1f1164f3719b4b855a3a163a78764"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16d546ecc7d16a609e368a4d2d557605"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#a16d546ecc7d16a609e368a4d2d557605">foldCompact</a> () const</td></tr>
<tr class="separator:a16d546ecc7d16a609e368a4d2d557605"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8bc6aee27994356e61fc6b030e23a62f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#a8bc6aee27994356e61fc6b030e23a62f">setFoldCompact</a> (bool fold)</td></tr>
<tr class="separator:a8bc6aee27994356e61fc6b030e23a62f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba02f4e299dd7f25cea762e9c21b48b2"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#aba02f4e299dd7f25cea762e9c21b48b2">stylePreprocessor</a> () const</td></tr>
<tr class="separator:aba02f4e299dd7f25cea762e9c21b48b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa1949e1c7fd18507f664babab7b3c56c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#aa1949e1c7fd18507f664babab7b3c56c">setStylePreprocessor</a> (bool style)</td></tr>
<tr class="separator:aa1949e1c7fd18507f664babab7b3c56c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a> (QObject *parent=0)</td></tr>
<tr class="separator:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="af6cc5bb9d9421d806e9941d018030068"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a> ()</td></tr>
<tr class="separator:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a> () const</td></tr>
<tr class="separator:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a> () const</td></tr>
<tr class="separator:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a6504a6fff35af16fbfd97889048db2a5"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a> () const</td></tr>
<tr class="separator:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a> ()</td></tr>
<tr class="separator:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a> () const</td></tr>
<tr class="separator:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="afccca7eb1aed463f89ac442d99135839"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a> () const</td></tr>
<tr class="separator:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a> (int style) const</td></tr>
<tr class="separator:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a> (int style) const</td></tr>
<tr class="separator:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a> (int style) const</td></tr>
<tr class="separator:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="aff4735542e937c5e35ecb2eb82e8f875"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a> () const</td></tr>
<tr class="separator:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a> () const</td></tr>
<tr class="separator:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a> (int style) const</td></tr>
<tr class="separator:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">defaultColor</a> () const</td></tr>
<tr class="separator:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">defaultFont</a> () const</td></tr>
<tr class="separator:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">defaultPaper</a> () const</td></tr>
<tr class="separator:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciScintilla.html">QsciScintilla</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a> () const</td></tr>
<tr class="separator:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a> (<a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *<a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>)</td></tr>
<tr class="separator:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a> (const QFont &amp;f)</td></tr>
<tr class="separator:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a1e81186b1f8f8bc2a4901a42cbca568a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>setEditor</b> (<a class="el" href="classQsciScintilla.html">QsciScintilla</a> *<a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>)</td></tr>
<tr class="separator:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td></tr>
<tr class="separator:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a> () const</td></tr>
<tr class="separator:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td></tr>
<tr class="separator:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:ae15b25b5d6705a850f6c93ee1013bea7"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#ae15b25b5d6705a850f6c93ee1013bea7">readProperties</a> (QSettings &amp;qs, const QString &amp;prefix)</td></tr>
<tr class="separator:ae15b25b5d6705a850f6c93ee1013bea7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af2acfd7b7a9012577aed90f136ad3fb1"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerCoffeeScript.html#af2acfd7b7a9012577aed90f136ad3fb1">writeProperties</a> (QSettings &amp;qs, const QString &amp;prefix) const</td></tr>
<tr class="separator:af2acfd7b7a9012577aed90f136ad3fb1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pro_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a41d4521504d63ee63d43fd7ed0c003ee"></a>
QByteArray&#160;</td><td class="memItemRight" valign="bottom"><b>textAsBytes</b> (const QString &amp;text) const</td></tr>
<tr class="separator:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a5261dd606c209a5c6a494e608a9a111a"></a>
QString&#160;</td><td class="memItemRight" valign="bottom"><b>bytesAsText</b> (const char *bytes, int size) const</td></tr>
<tr class="separator:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_slots_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_slots_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Slots inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a> (int autoindentstyle)</td></tr>
<tr class="separator:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a> (bool eoffill, int style=-1)</td></tr>
<tr class="separator:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a> (const QFont &amp;f, int style=-1)</td></tr>
<tr class="separator:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header signals_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('signals_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Signals inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a> (bool eolfilled, int style)</td></tr>
<tr class="separator:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a> (const QFont &amp;f, int style)</td></tr>
<tr class="separator:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a> (const char *prop, const char *val)</td></tr>
<tr class="separator:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciLexerCoffeeScript.html" title="The QsciLexerCoffeeScript class encapsulates the Scintilla CoffeeScript lexer.">QsciLexerCoffeeScript</a> class encapsulates the Scintilla CoffeeScript lexer. </p>
</div><h2 class="groupheader">Member Enumeration Documentation</h2>
<a id="a3e2bfca47ca0666b7acb6a451d203fa8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3e2bfca47ca0666b7acb6a451d203fa8">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the meanings of the different styles used by the C++ lexer. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8a2fb70f93de6a55714777a4fa55916d03"></a>Default&#160;</td><td class="fielddoc"><p>The default. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8a908ae06d736d3add37f734a255ceeaa3"></a>Comment&#160;</td><td class="fielddoc"><p>A C-style comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8a379b349ef6edd66b752af87472fe41b4"></a>CommentLine&#160;</td><td class="fielddoc"><p>A C++-style comment line. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8a5edc735d0127917185abed1f637a49f7"></a>CommentDoc&#160;</td><td class="fielddoc"><p>A JavaDoc/Doxygen C-style comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8aba8f2217a58a7603d2a69ea1edeb1bc4"></a>Number&#160;</td><td class="fielddoc"><p>A number. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8a4a6519d9d7b7e0e068d6ce8b777a87d2"></a>Keyword&#160;</td><td class="fielddoc"><p>A keyword. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8a9eb257127f3fd60ea6ee7ef126419f7d"></a>DoubleQuotedString&#160;</td><td class="fielddoc"><p>A double-quoted string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8ad64eca43c5aa797920a0b5db86c7ebb7"></a>SingleQuotedString&#160;</td><td class="fielddoc"><p>A single-quoted string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8a063d7d591aa18ae40fcde793e1be1f01"></a>UUID&#160;</td><td class="fielddoc"><p>An IDL UUID. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8a84af89605b0d39edc60401dee749d076"></a>PreProcessor&#160;</td><td class="fielddoc"><p>A pre-processor block. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8afd477421bbc3829c44d0ceda25ef07ec"></a>Operator&#160;</td><td class="fielddoc"><p>An operator. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8a6fada5b4984531d13a0f03cf9bd082f8"></a>Identifier&#160;</td><td class="fielddoc"><p>An identifier. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8ad66f62da8384e226b3c0e33455d93bd4"></a>UnclosedString&#160;</td><td class="fielddoc"><p>The end of a line where a string is not closed. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8a6f653903645cf19e5ea1c7e870ae9efb"></a>VerbatimString&#160;</td><td class="fielddoc"><p>A C# verbatim string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8aca5c64a4bc75adb3be878a492906cfba"></a>Regex&#160;</td><td class="fielddoc"><p>A regular expression. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8a406ac7ec4b5186a2d33b7a9074f6fa02"></a>CommentLineDoc&#160;</td><td class="fielddoc"><p>A JavaDoc/Doxygen C++-style comment line. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8ae9524bc4d07a86f58eb88d57c1291083"></a>KeywordSet2&#160;</td><td class="fielddoc"><p>A keyword defined in keyword set number 2. The class must be sub-classed and re-implement <a class="el" href="classQsciLexerCoffeeScript.html#a74867915ad9d609b9b516eff87101cc9">keywords()</a> to make use of this style. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8a7e8b105503aff566abe10b78bfff1575"></a>CommentDocKeyword&#160;</td><td class="fielddoc"><p>A JavaDoc/Doxygen keyword. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8a48d773f3fce4500a8700b6d76f2ecf24"></a>CommentDocKeywordError&#160;</td><td class="fielddoc"><p>A JavaDoc/Doxygen keyword error defined in keyword set number 3. The class must be sub-classed and re-implement <a class="el" href="classQsciLexerCoffeeScript.html#a74867915ad9d609b9b516eff87101cc9">keywords()</a> to make use of this style. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8ab4d4a5d44c3c3584609996183880c179"></a>GlobalClass&#160;</td><td class="fielddoc"><p>A global class defined in keyword set number 4. The class must be sub-classed and re-implement <a class="el" href="classQsciLexerCoffeeScript.html#a74867915ad9d609b9b516eff87101cc9">keywords()</a> to make use of this style. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8a2050935e0699ccd6660987e5b6f42c32"></a>CommentBlock&#160;</td><td class="fielddoc"><p>A block comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8a32607d1ce220881542a049d83406ce65"></a>BlockRegex&#160;</td><td class="fielddoc"><p>A block regular expression. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8acf3a1887749e806e04bf6a0097f724bb"></a>BlockRegexComment&#160;</td><td class="fielddoc"><p>A block regular expression comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="a3e2bfca47ca0666b7acb6a451d203fa8a67579947345d4b8bca1317e697fe46d3"></a>InstanceProperty&#160;</td><td class="fielddoc"><p>An instance property. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a73e71780818247bf678616a25cd13e90"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a73e71780818247bf678616a25cd13e90">&#9670;&nbsp;</a></span>QsciLexerCoffeeScript()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciLexerCoffeeScript::QsciLexerCoffeeScript </td>
          <td>(</td>
          <td class="paramtype">QObject *&#160;</td>
          <td class="paramname"><em>parent</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Construct a <a class="el" href="classQsciLexerCoffeeScript.html" title="The QsciLexerCoffeeScript class encapsulates the Scintilla CoffeeScript lexer.">QsciLexerCoffeeScript</a> with parent <em>parent</em>. <em>parent</em> is typically the <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> instance. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="aac009a767572be4b4489a0613611cbdb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aac009a767572be4b4489a0613611cbdb">&#9670;&nbsp;</a></span>lexer()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerCoffeeScript::lexer </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the name of the lexer. Some lexers support a number of languages. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ac7c40b97187e23ab85f6d95113f91b39">QsciLexer</a>.</p>

</div>
</div>
<a id="a0741fad0b942deb73642be16c3159eb1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0741fad0b942deb73642be16c3159eb1">&#9670;&nbsp;</a></span>autoCompletionWordSeparators()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QStringList QsciLexerCoffeeScript::autoCompletionWordSeparators </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the list of character sequences that can separate auto-completion words. The first in the list is assumed to be the sequence used to separate words in the lexer's API files. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">QsciLexer</a>.</p>

</div>
</div>
<a id="a1796c98b07ec6cfc3d5953c225cc1f37"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1796c98b07ec6cfc3d5953c225cc1f37">&#9670;&nbsp;</a></span>blockEnd()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerCoffeeScript::blockEnd </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of words or characters in a particular style that define the end of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">QsciLexer</a>.</p>

</div>
</div>
<a id="a293f0a5c39990ec1db6de249dc618901"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a293f0a5c39990ec1db6de249dc618901">&#9670;&nbsp;</a></span>blockStart()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerCoffeeScript::blockStart </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of words or characters in a particular style that define the start of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">QsciLexer</a>.</p>

</div>
</div>
<a id="a497144db9b43beba78cd405a795e08ac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a497144db9b43beba78cd405a795e08ac">&#9670;&nbsp;</a></span>blockStartKeyword()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerCoffeeScript::blockStartKeyword </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of keywords in a particular style that define the start of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">QsciLexer</a>.</p>

</div>
</div>
<a id="ab2e7d189deabf8e5e20434e32346742c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab2e7d189deabf8e5e20434e32346742c">&#9670;&nbsp;</a></span>defaultColor()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerCoffeeScript::defaultColor </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the foreground colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCoffeeScript.html#a402a849f5eed391f0c4cd3aac9beb075">defaultPaper()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#af7508f1b816a2c9446d36141edc9b5ce">QsciLexer</a>.</p>

</div>
</div>
<a id="a402a849f5eed391f0c4cd3aac9beb075"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a402a849f5eed391f0c4cd3aac9beb075">&#9670;&nbsp;</a></span>defaultPaper()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerCoffeeScript::defaultPaper </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the background colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCoffeeScript.html#ab2e7d189deabf8e5e20434e32346742c">defaultColor()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a7e5ab7f541d913760c32abedbdc72963">QsciLexer</a>.</p>

</div>
</div>
<a id="a74867915ad9d609b9b516eff87101cc9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a74867915ad9d609b9b516eff87101cc9">&#9670;&nbsp;</a></span>keywords()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerCoffeeScript::keywords </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>set</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the set of keywords for the keyword set <em>set</em> recognised by the lexer as a space separated string. Set 1 is normally used for primary keywords and identifiers. Set 2 is normally used for secondary keywords and identifiers. Set 3 is normally used for documentation comment keywords. Set 4 is normally used for global classes and typedefs. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a013b7c1bf9846e231b97827dfd9540b0">QsciLexer</a>.</p>

</div>
</div>
<a id="ace6bf74522c57e70f2c3ac525e1fd830"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ace6bf74522c57e70f2c3ac525e1fd830">&#9670;&nbsp;</a></span>description()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QString QsciLexerCoffeeScript::description </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the descriptive name for style number <em>style</em>. If the style is invalid for this language then an empty QString is returned. This is intended to be used in user preference dialogs. </p>

<p>Implements <a class="el" href="classQsciLexer.html#add9c20adb43bc38d1a0ca3083ac3e6fa">QsciLexer</a>.</p>

</div>
</div>
<a id="aefae6df689f1d3dad66d1f2fc141cc39"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aefae6df689f1d3dad66d1f2fc141cc39">&#9670;&nbsp;</a></span>refreshProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerCoffeeScript::refreshProperties </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Causes all properties to be refreshed by emitting the <a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged()</a> signal as required. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ae508c3ab4ce1f338dfff3ddf5ee7e34c">QsciLexer</a>.</p>

</div>
</div>
<a id="a5b95ed33711b09385c92fbfb9f1d2a5d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5b95ed33711b09385c92fbfb9f1d2a5d">&#9670;&nbsp;</a></span>dollarsAllowed()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerCoffeeScript::dollarsAllowed </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if '$' characters are allowed in identifier names.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCoffeeScript.html#affaec4d14f7908f7d24d16937df00c93">setDollarsAllowed()</a> </dd></dl>

</div>
</div>
<a id="affaec4d14f7908f7d24d16937df00c93"></a>
<h2 class="memtitle"><span class="permalink"><a href="#affaec4d14f7908f7d24d16937df00c93">&#9670;&nbsp;</a></span>setDollarsAllowed()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerCoffeeScript::setDollarsAllowed </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>allowed</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>allowed</em> is true then '$' characters are allowed in identifier names. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCoffeeScript.html#a5b95ed33711b09385c92fbfb9f1d2a5d">dollarsAllowed()</a> </dd></dl>

</div>
</div>
<a id="a6865962a5df72e37f4ba49c6e5e539b6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6865962a5df72e37f4ba49c6e5e539b6">&#9670;&nbsp;</a></span>foldComments()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerCoffeeScript::foldComments </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if multi-line comment blocks can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCoffeeScript.html#a57f1f1164f3719b4b855a3a163a78764">setFoldComments()</a> </dd></dl>

</div>
</div>
<a id="a57f1f1164f3719b4b855a3a163a78764"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a57f1f1164f3719b4b855a3a163a78764">&#9670;&nbsp;</a></span>setFoldComments()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerCoffeeScript::setFoldComments </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then multi-line comment blocks can be folded. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCoffeeScript.html#a6865962a5df72e37f4ba49c6e5e539b6">foldComments()</a> </dd></dl>

</div>
</div>
<a id="a16d546ecc7d16a609e368a4d2d557605"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a16d546ecc7d16a609e368a4d2d557605">&#9670;&nbsp;</a></span>foldCompact()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerCoffeeScript::foldCompact </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if trailing blank lines are included in a fold block.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCoffeeScript.html#a8bc6aee27994356e61fc6b030e23a62f">setFoldCompact()</a> </dd></dl>

</div>
</div>
<a id="a8bc6aee27994356e61fc6b030e23a62f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8bc6aee27994356e61fc6b030e23a62f">&#9670;&nbsp;</a></span>setFoldCompact()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerCoffeeScript::setFoldCompact </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then trailing blank lines are included in a fold block. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCoffeeScript.html#a16d546ecc7d16a609e368a4d2d557605">foldCompact()</a> </dd></dl>

</div>
</div>
<a id="aba02f4e299dd7f25cea762e9c21b48b2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aba02f4e299dd7f25cea762e9c21b48b2">&#9670;&nbsp;</a></span>stylePreprocessor()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerCoffeeScript::stylePreprocessor </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if preprocessor lines (after the preprocessor directive) are styled.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCoffeeScript.html#aa1949e1c7fd18507f664babab7b3c56c">setStylePreprocessor()</a> </dd></dl>

</div>
</div>
<a id="aa1949e1c7fd18507f664babab7b3c56c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa1949e1c7fd18507f664babab7b3c56c">&#9670;&nbsp;</a></span>setStylePreprocessor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerCoffeeScript::setStylePreprocessor </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>style</em> is true then preprocessor lines (after the preprocessor directive) are styled. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCoffeeScript.html#aba02f4e299dd7f25cea762e9c21b48b2">stylePreprocessor()</a> </dd></dl>

</div>
</div>
<a id="ae15b25b5d6705a850f6c93ee1013bea7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae15b25b5d6705a850f6c93ee1013bea7">&#9670;&nbsp;</a></span>readProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerCoffeeScript::readProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are read from the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCoffeeScript.html#af2acfd7b7a9012577aed90f136ad3fb1">writeProperties()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ad472b16506a4cbc19634f07aa90f1ea6">QsciLexer</a>.</p>

</div>
</div>
<a id="af2acfd7b7a9012577aed90f136ad3fb1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af2acfd7b7a9012577aed90f136ad3fb1">&#9670;&nbsp;</a></span>writeProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerCoffeeScript::writeProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are written to the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerCoffeeScript.html#ae15b25b5d6705a850f6c93ee1013bea7">readProperties()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#abccc4e010b724df1a7b5c5f3bce29501">QsciLexer</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
