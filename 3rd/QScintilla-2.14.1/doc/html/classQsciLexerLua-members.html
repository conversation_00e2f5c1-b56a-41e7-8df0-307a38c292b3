<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciLexerLua Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciLexerLua.html">QsciLexerLua</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#aff715db68554a1022792135e8edd0dba">autoCompletionWordSeparators</a>() const</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a39efcf7df417b4566cace55874fb668e">BasicFunctions</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">blockEnd</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a157c462625b4826a5d7fb9eec42cfc78">blockStart</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a30570eca6c21ea302b1c6c0bd733dc14">braceStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bytesAsText</b>(const char *bytes, int size) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a95cd8dc67acc900b870665a61009b731">Character</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25abf2c37dbd9d2f0f761e4c75b9a916c7f">Comment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a1da860131fdcc821dbd51a25f65175ac">CoroutinesIOSystemFacilities</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a60c35a89c49dabb959c8433fc053295b">Default</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#a1412f4f04885bf9b315fbb371c54dc7c">defaultColor</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">QsciLexer::defaultColor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#a4d20a72f3087068af5840042d9beeca7">defaultEolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a2db5e63ff4667a3f8e9df24a0accdf3d">defaultFont</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">QsciLexer::defaultFont</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#aec007c8c5c374ca94b71d3eb0f47f467">defaultPaper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">QsciLexer::defaultPaper</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#ad77fd8b1e9ed6bac617f194306de2ea8">description</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a>(bool eolfilled, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#a27383c2def3f59e903aec9537ef43d2c">foldCompact</a>() const</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a>(const QFont &amp;f, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a03fe51d98c72f4af37b148cfb2a1319f">Identifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25ae5d52051b1b6459a13bff6db572c0dce">Keyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#a136982546f34f83f5e3dd21f67074d4d">keywords</a>(int set) const</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25ae99b87ba3113da81b9b8a7b675ac5abd">KeywordSet5</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a8b505486e278e80d9caef2ef9769544b">KeywordSet6</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a39080fdb9bcf558f8aca25ebbc5877cb">KeywordSet7</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25ae15e71bd786c060d6412ce31551f3e42">KeywordSet8</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a64681c9d0cf443f6073155989512f8b9">Label</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#a942c993effc83d0dedec2fc20d8a741f">language</a>() const</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a8124ec8b5b96d95bb225cbb4e95f55cb">lexer</a>() const</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a9aa357d7b337b9aabcb7f0566aff3aa9">LineComment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a78b4fea76183ad94c31e18f744495e94">LiteralString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25aa7100ee56315d914482fbd97843d98f5">Number</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a5267c1cdd34c280a959cd7df49b16ab2">Operator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25af9f702c766041919da2c7dac8fc11901">Preprocessor</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a>(const char *prop, const char *val)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#a8932efc560175dc70a88e23b8136bb8f">QsciLexerLua</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a928315606c0bd973c59e0b6d9641c3cd">readProperties</a>(QSettings &amp;qs, const QString &amp;prefix)</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a628efb828569208d6219a88f1fc6a1a7">refreshProperties</a>()</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a>(QsciAbstractAPIs *apis)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a>(int autoindentstyle)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a>(const QFont &amp;f)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>setEditor</b>(QsciScintilla *editor) (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a>(bool eoffill, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#a2f54e561f646da5ff20c5e85b2f377ea">setFoldCompact</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a>(const QFont &amp;f, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25a351f36d3635a5f3af4815f6a74863eae">String</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25af4a7065e3246c398a68f9af4ad839eb7">StringTableMathsFunctions</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>textAsBytes</b>(const QString &amp;text) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#a34427b01d36d42008727d7cdc41d8d25afcd79271430a9af1fba6b94ee41cdf38">UnclosedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aace68e3dbcef9da1b031fb9cfd843c57">wordCharacters</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerLua.html#af0fffa0361bad4a3a007c09a1811db9c">writeProperties</a>(QSettings &amp;qs, const QString &amp;prefix) const</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerLua.html#adbeb88fef8346b6543d6ef8b2154e763">~QsciLexerLua</a>()</td><td class="entry"><a class="el" href="classQsciLexerLua.html">QsciLexerLua</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
