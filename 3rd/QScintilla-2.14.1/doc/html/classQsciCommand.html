<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciCommand Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="classQsciCommand-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciCommand Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscicommand.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:aeaecb067c0834ba132e204a09dd942c7"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7">Command</a> { <br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7aff3d71de76b3948d3576bf6f6164d435">LineDown</a> = QsciScintillaBase::SCI_LINEDOWN, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a12027f7b5ad1f98b9d7f5b20a1b19856">LineDownExtend</a> = QsciScintillaBase::SCI_LINEDOWNEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a4e166fc8f33e759313cf124be9dc960e">LineDownRectExtend</a> = QsciScintillaBase::SCI_LINEDOWNRECTEXTEND, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ac48505ac24f80eabcb61b93e6eb20f0d">LineScrollDown</a> = QsciScintillaBase::SCI_LINESCROLLDOWN, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a6108080747db44fba6676a90721edf3b">LineUp</a> = QsciScintillaBase::SCI_LINEUP, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a620ae67b9bdb35d46d0fadd66d578725">LineUpExtend</a> = QsciScintillaBase::SCI_LINEUPEXTEND, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a25673ac6266e5d5400328cb32c50064c">LineUpRectExtend</a> = QsciScintillaBase::SCI_LINEUPRECTEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7abd1999204d26dee95623a1a4926d1694">LineScrollUp</a> = QsciScintillaBase::SCI_LINESCROLLUP, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ad32c75499899527ccb721d6221e0b1f1">ScrollToStart</a> = QsciScintillaBase::SCI_SCROLLTOSTART, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a16b9e2cd58ea3f6d094bf870e1e9e083">ScrollToEnd</a> = QsciScintillaBase::SCI_SCROLLTOEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a593083e106606bf5fb3d2322068c455f">VerticalCentreCaret</a> = QsciScintillaBase::SCI_VERTICALCENTRECARET, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a0b695afb34e16591b039b9a318729838">ParaDown</a> = QsciScintillaBase::SCI_PARADOWN, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a343a73f6732c17f02d9e8158c935abde">ParaDownExtend</a> = QsciScintillaBase::SCI_PARADOWNEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a2bd7f7cdb9537b4b7b9bdf58aeae3733">ParaUp</a> = QsciScintillaBase::SCI_PARAUP, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ac121331a789c14d2d8ee4065877c2127">ParaUpExtend</a> = QsciScintillaBase::SCI_PARAUPEXTEND, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a88dc0fc0d4642486fb54dce5045a5b8b">CharLeft</a> = QsciScintillaBase::SCI_CHARLEFT, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7af23e0b934931581f6b383a4b3de10b48">CharLeftExtend</a> = QsciScintillaBase::SCI_CHARLEFTEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7aa62e9ab460a49ff8b9c3c55219f98abb">CharLeftRectExtend</a> = QsciScintillaBase::SCI_CHARLEFTRECTEXTEND, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a3ce1411c8761d1562fa8e8b5d7609df7">CharRight</a> = QsciScintillaBase::SCI_CHARRIGHT, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7aae6afb296e30c48ae1c4992817d673bf">CharRightExtend</a> = QsciScintillaBase::SCI_CHARRIGHTEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7aedd92e9ae8401fb13ab6d01667949938">CharRightRectExtend</a> = QsciScintillaBase::SCI_CHARRIGHTRECTEXTEND, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7aec027d50c71ee8604872c5cc839250cd">WordLeft</a> = QsciScintillaBase::SCI_WORDLEFT, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a5b4b9629de523b97fcbad43c21dc37bb">WordLeftExtend</a> = QsciScintillaBase::SCI_WORDLEFTEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a8e61252df5bfefd5081d9cb4170844d3">WordRight</a> = QsciScintillaBase::SCI_WORDRIGHT, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ac69665982f4477d00509573ceea8a8f3">WordRightExtend</a> = QsciScintillaBase::SCI_WORDRIGHTEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ac3e19af04d3083f26408dcb6ad3c73b7">WordLeftEnd</a> = QsciScintillaBase::SCI_WORDLEFTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ab2f7d005b37a7b61964e456b26d4a3f6">WordLeftEndExtend</a> = QsciScintillaBase::SCI_WORDLEFTENDEXTEND, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a1099ee8200e0d0799a2721ff9828fe48">WordRightEnd</a> = QsciScintillaBase::SCI_WORDRIGHTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a455d6eda859feb8aed088f22587d5aa3">WordRightEndExtend</a> = QsciScintillaBase::SCI_WORDRIGHTENDEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ac1b4586de16ecd614e34d98c7ca360ec">WordPartLeft</a> = QsciScintillaBase::SCI_WORDPARTLEFT, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a0e5436d5c8bd4e42f0e542cbb852645d">WordPartLeftExtend</a> = QsciScintillaBase::SCI_WORDPARTLEFTEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a78c1a79cb762dc96072ef5bc1d90b20b">WordPartRight</a> = QsciScintillaBase::SCI_WORDPARTRIGHT, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a1ad48872dc33ca01b0502594c6dd6df1">WordPartRightExtend</a> = QsciScintillaBase::SCI_WORDPARTRIGHTEXTEND, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a607f851f9833e13dbf335009ebd2ca37">Home</a> = QsciScintillaBase::SCI_HOME, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7acbb515f305f0dc715372708d91be80aa">HomeExtend</a> = QsciScintillaBase::SCI_HOMEEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a3cf62acaae368a7a1e88a6300fbf1de4">HomeRectExtend</a> = QsciScintillaBase::SCI_HOMERECTEXTEND, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a71d0019b185b1d65e9d3574651a1b55f">HomeDisplay</a> = QsciScintillaBase::SCI_HOMEDISPLAY, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a82851feb57f239b98cfa52fb2307fe66">HomeDisplayExtend</a> = QsciScintillaBase::SCI_HOMEDISPLAYEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ab67874e23a95cc208bcbb0de0cf16d90">HomeWrap</a> = QsciScintillaBase::SCI_HOMEWRAP, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7aa5afb3470010c1075e61cd4216a1714c">HomeWrapExtend</a> = QsciScintillaBase::SCI_HOMEWRAPEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ac443339cd46d0646cd97870506e91110">VCHome</a> = QsciScintillaBase::SCI_VCHOME, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a1e0ff1255da4ea0a77750d55a9aaaef4">VCHomeExtend</a> = QsciScintillaBase::SCI_VCHOMEEXTEND, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7adbf224a91e83518a244bb5a726c69bed">VCHomeRectExtend</a> = QsciScintillaBase::SCI_VCHOMERECTEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7affdd507c7e4221726f980f95910ed5a5">VCHomeWrap</a> = QsciScintillaBase::SCI_VCHOMEWRAP, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7af1db27f7f04534cc2071e71c422e4a45">VCHomeWrapExtend</a> = QsciScintillaBase::SCI_VCHOMEWRAPEXTEND, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a175bcd4e973a6ae4553827db95d987f6">LineEnd</a> = QsciScintillaBase::SCI_LINEEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a4f42905e1e54f6eb4e91eb832c07e387">LineEndExtend</a> = QsciScintillaBase::SCI_LINEENDEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a6d942d99ab734f6b5c1160cbe18a6197">LineEndRectExtend</a> = QsciScintillaBase::SCI_LINEENDRECTEXTEND, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a77dc6d96d21c32e61b8e3809759eec37">LineEndDisplay</a> = QsciScintillaBase::SCI_LINEENDDISPLAY, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7acfe625def4c875c4f3ed4011c1d30f30">LineEndDisplayExtend</a> = QsciScintillaBase::SCI_LINEENDDISPLAYEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a2bcecd03bc30e56d92035364f1c4d3aa">LineEndWrap</a> = QsciScintillaBase::SCI_LINEENDWRAP, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a8528ef0f7030d4eaa9cc93c1fb0f00d2">LineEndWrapExtend</a> = QsciScintillaBase::SCI_LINEENDWRAPEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ae0d2fa78fc42366a578b50cae1c44a8f">DocumentStart</a> = QsciScintillaBase::SCI_DOCUMENTSTART, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a368bc9e6e89a0f9504a49fc97477618b">DocumentStartExtend</a> = QsciScintillaBase::SCI_DOCUMENTSTARTEXTEND, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a8e059c91d4d3d5037d0dce9c8fa735a0">DocumentEnd</a> = QsciScintillaBase::SCI_DOCUMENTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ac6b77e65e9d026dd2a3af831ddfcc664">DocumentEndExtend</a> = QsciScintillaBase::SCI_DOCUMENTENDEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a69b8ec474437f655c93b019729093b82">PageUp</a> = QsciScintillaBase::SCI_PAGEUP, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ac9c27eb69dde18a38bc1cbc84c9ee430">PageUpExtend</a> = QsciScintillaBase::SCI_PAGEUPEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a31667dadedf4289250d3e1b5a1e9bf36">PageUpRectExtend</a> = QsciScintillaBase::SCI_PAGEUPRECTEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7acbd164907353acc3e218943d86d03b23">PageDown</a> = QsciScintillaBase::SCI_PAGEDOWN, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ac6febe41f0669f54f4c14f2c32329569">PageDownExtend</a> = QsciScintillaBase::SCI_PAGEDOWNEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a70472783731018e17e0943ee14e4ca6d">PageDownRectExtend</a> = QsciScintillaBase::SCI_PAGEDOWNRECTEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a9be0a9fe5bfc0864f0f40987a4806a62">StutteredPageUp</a> = QsciScintillaBase::SCI_STUTTEREDPAGEUP, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a26d878df5382e38843e754078aa8f44f">StutteredPageUpExtend</a> = QsciScintillaBase::SCI_STUTTEREDPAGEUPEXTEND, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ab05b31fae6958a99166222cc3efd076a">StutteredPageDown</a> = QsciScintillaBase::SCI_STUTTEREDPAGEDOWN, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ad07964451843f3c910b7228dfb589857">StutteredPageDownExtend</a> = QsciScintillaBase::SCI_STUTTEREDPAGEDOWNEXTEND, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ad763b356ba37cf93b78201baea5aa00d">Delete</a> = QsciScintillaBase::SCI_CLEAR, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a82626bce8a0acdd6c4c196865629e81b">DeleteBack</a> = QsciScintillaBase::SCI_DELETEBACK, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7adadf0fa77a7ce5496fce517bc9e0a723">DeleteBackNotLine</a> = QsciScintillaBase::SCI_DELETEBACKNOTLINE, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a66566eb3ac1ad83cc6ef2913d449d193">DeleteWordLeft</a> = QsciScintillaBase::SCI_DELWORDLEFT, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a0a5ea33d56c6c45fe80f5b1f66975ffa">DeleteWordRight</a> = QsciScintillaBase::SCI_DELWORDRIGHT, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ae1a8da5398be3fb7c5e32f868bf4af14">DeleteWordRightEnd</a> = QsciScintillaBase::SCI_DELWORDRIGHTEND, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a0b9644d959806dd50a8dce00bf521e13">DeleteLineLeft</a> = QsciScintillaBase::SCI_DELLINELEFT, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a21407e4871585bcfb0d76dbf7be87650">DeleteLineRight</a> = QsciScintillaBase::SCI_DELLINERIGHT, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a291afea2c733abf34e20b0c25814dc5c">LineDelete</a> = QsciScintillaBase::SCI_LINEDELETE, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a91f9ce105ac6ead565f6f8e00ec0e9a6">LineCut</a> = QsciScintillaBase::SCI_LINECUT, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7aa762bf03d4d23f764de57c146c9a658d">LineCopy</a> = QsciScintillaBase::SCI_LINECOPY, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ad6895dbe64da12bd7749e9c7bf4d8e75">LineTranspose</a> = QsciScintillaBase::SCI_LINETRANSPOSE, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a8cc01e22c3d5cc697f87b00dcedb33f5">LineDuplicate</a> = QsciScintillaBase::SCI_LINEDUPLICATE, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a8ddbd8f97e85bbef9e728a1293a94983">SelectAll</a> = QsciScintillaBase::SCI_SELECTALL, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7af5335bf501eb458872740c610db3617b">MoveSelectedLinesUp</a> = QsciScintillaBase::SCI_MOVESELECTEDLINESUP, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7aae256fc63ff21305b2a1d93d7f05bee5">MoveSelectedLinesDown</a> = QsciScintillaBase::SCI_MOVESELECTEDLINESDOWN, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ad10ac67847b362c169d7e3b0b3463290">SelectionDuplicate</a> = QsciScintillaBase::SCI_SELECTIONDUPLICATE, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7aff49104707e447f73d08afd744b1f68d">SelectionLowerCase</a> = QsciScintillaBase::SCI_LOWERCASE, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a5df7c07cf8cf1eee546837ece594dcaa">SelectionUpperCase</a> = QsciScintillaBase::SCI_UPPERCASE, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a11cd9c83a7a1b74dc2a936e324ecf99e">SelectionCut</a> = QsciScintillaBase::SCI_CUT, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a200b4ecea5a65b8690e8393b8ad3d512">SelectionCopy</a> = QsciScintillaBase::SCI_COPY, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ac0877b23ce38bba85fc30eecb347a662">Paste</a> = QsciScintillaBase::SCI_PASTE, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ab6c8c98a6027e8a88783f18dbca2bdf4">EditToggleOvertype</a> = QsciScintillaBase::SCI_EDITTOGGLEOVERTYPE, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ae71d9db2e7cf1f6ca4b731675e1d63a1">Newline</a> = QsciScintillaBase::SCI_NEWLINE, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a3b46d96af1feddb3560236b9e75c39c2">Formfeed</a> = QsciScintillaBase::SCI_FORMFEED, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ab89051fd7c64cea84abec8d21809d2ee">Tab</a> = QsciScintillaBase::SCI_TAB, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7aeae07e48b489c0cc937bf83bef4f0c9c">Backtab</a> = QsciScintillaBase::SCI_BACKTAB, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a3905c749d29761ae2a594c14e1fb26c9">Cancel</a> = QsciScintillaBase::SCI_CANCEL, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a0089e5295b999464b699fb736a449b4f">Undo</a> = QsciScintillaBase::SCI_UNDO, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ad12c4dd6bcaefc08fcff8fe3d80b3b66">Redo</a> = QsciScintillaBase::SCI_REDO, 
<br />
&#160;&#160;<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a5e12affbccb0a63c1bd78ca5617b0289">ZoomIn</a> = QsciScintillaBase::SCI_ZOOMIN, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ae5d11d096795f1a1c7b4d56e41c9b1af">ZoomOut</a> = QsciScintillaBase::SCI_ZOOMOUT, 
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a740c74f61e7d91060ccc9e0945318787">ReverseLines</a> = QsciScintillaBase::SCI_LINEREVERSE
<br />
 }</td></tr>
<tr class="separator:aeaecb067c0834ba132e204a09dd942c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aebfa962809b16312fa03f7526cc60f07"><td class="memItemLeft" align="right" valign="top"><a id="aebfa962809b16312fa03f7526cc60f07"></a>
<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7">Command</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciCommand.html#aebfa962809b16312fa03f7526cc60f07">command</a> () const</td></tr>
<tr class="separator:aebfa962809b16312fa03f7526cc60f07"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa0bf23ebd61dd46a4eb59447e43c4cab"><td class="memItemLeft" align="right" valign="top"><a id="aa0bf23ebd61dd46a4eb59447e43c4cab"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciCommand.html#aa0bf23ebd61dd46a4eb59447e43c4cab">execute</a> ()</td></tr>
<tr class="separator:aa0bf23ebd61dd46a4eb59447e43c4cab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6488ddf82659fcf42d704f787b6cb522"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciCommand.html#a6488ddf82659fcf42d704f787b6cb522">setKey</a> (int <a class="el" href="classQsciCommand.html#abf9dffcf6c222ecc02b28c3f6d17eb8e">key</a>)</td></tr>
<tr class="separator:a6488ddf82659fcf42d704f787b6cb522"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8c00e5f08abe7ad05fe54653c0f040ae"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciCommand.html#a8c00e5f08abe7ad05fe54653c0f040ae">setAlternateKey</a> (int altkey)</td></tr>
<tr class="separator:a8c00e5f08abe7ad05fe54653c0f040ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf9dffcf6c222ecc02b28c3f6d17eb8e"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciCommand.html#abf9dffcf6c222ecc02b28c3f6d17eb8e">key</a> () const</td></tr>
<tr class="separator:abf9dffcf6c222ecc02b28c3f6d17eb8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6949756a800e31f1d279aa753060966"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciCommand.html#ae6949756a800e31f1d279aa753060966">alternateKey</a> () const</td></tr>
<tr class="separator:ae6949756a800e31f1d279aa753060966"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a76ed201e9e7309084795ddbc8f6e5b49"><td class="memItemLeft" align="right" valign="top"><a id="a76ed201e9e7309084795ddbc8f6e5b49"></a>
QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciCommand.html#a76ed201e9e7309084795ddbc8f6e5b49">description</a> () const</td></tr>
<tr class="separator:a76ed201e9e7309084795ddbc8f6e5b49"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:aeb517d586cb9569d072fcd8a9658911b"><td class="memItemLeft" align="right" valign="top"><a id="aeb517d586cb9569d072fcd8a9658911b"></a>
static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciCommand.html#aeb517d586cb9569d072fcd8a9658911b">validKey</a> (int <a class="el" href="classQsciCommand.html#abf9dffcf6c222ecc02b28c3f6d17eb8e">key</a>)</td></tr>
<tr class="separator:aeb517d586cb9569d072fcd8a9658911b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciCommand.html" title="The QsciCommand class represents an internal editor command that may have one or two keys bound to it...">QsciCommand</a> class represents an internal editor command that may have one or two keys bound to it. </p>
<p>Methods are provided to change the keys bound to the command and to remove a key binding. Each command has a user friendly description of the command for use in key mapping dialogs. </p>
</div><h2 class="groupheader">Member Enumeration Documentation</h2>
<a id="aeaecb067c0834ba132e204a09dd942c7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aeaecb067c0834ba132e204a09dd942c7">&#9670;&nbsp;</a></span>Command</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7">QsciCommand::Command</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>This enum defines the different commands that can be assigned to a key. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7aff3d71de76b3948d3576bf6f6164d435"></a>LineDown&#160;</td><td class="fielddoc"><p>Move down one line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a12027f7b5ad1f98b9d7f5b20a1b19856"></a>LineDownExtend&#160;</td><td class="fielddoc"><p>Extend the selection down one line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a4e166fc8f33e759313cf124be9dc960e"></a>LineDownRectExtend&#160;</td><td class="fielddoc"><p>Extend the rectangular selection down one line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ac48505ac24f80eabcb61b93e6eb20f0d"></a>LineScrollDown&#160;</td><td class="fielddoc"><p>Scroll the view down one line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a6108080747db44fba6676a90721edf3b"></a>LineUp&#160;</td><td class="fielddoc"><p>Move up one line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a620ae67b9bdb35d46d0fadd66d578725"></a>LineUpExtend&#160;</td><td class="fielddoc"><p>Extend the selection up one line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a25673ac6266e5d5400328cb32c50064c"></a>LineUpRectExtend&#160;</td><td class="fielddoc"><p>Extend the rectangular selection up one line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7abd1999204d26dee95623a1a4926d1694"></a>LineScrollUp&#160;</td><td class="fielddoc"><p>Scroll the view up one line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ad32c75499899527ccb721d6221e0b1f1"></a>ScrollToStart&#160;</td><td class="fielddoc"><p>Scroll to the start of the document. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a16b9e2cd58ea3f6d094bf870e1e9e083"></a>ScrollToEnd&#160;</td><td class="fielddoc"><p>Scroll to the end of the document. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a593083e106606bf5fb3d2322068c455f"></a>VerticalCentreCaret&#160;</td><td class="fielddoc"><p>Scroll vertically to centre the current line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a0b695afb34e16591b039b9a318729838"></a>ParaDown&#160;</td><td class="fielddoc"><p>Move down one paragraph. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a343a73f6732c17f02d9e8158c935abde"></a>ParaDownExtend&#160;</td><td class="fielddoc"><p>Extend the selection down one paragraph. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a2bd7f7cdb9537b4b7b9bdf58aeae3733"></a>ParaUp&#160;</td><td class="fielddoc"><p>Move up one paragraph. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ac121331a789c14d2d8ee4065877c2127"></a>ParaUpExtend&#160;</td><td class="fielddoc"><p>Extend the selection up one paragraph. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a88dc0fc0d4642486fb54dce5045a5b8b"></a>CharLeft&#160;</td><td class="fielddoc"><p>Move left one character. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7af23e0b934931581f6b383a4b3de10b48"></a>CharLeftExtend&#160;</td><td class="fielddoc"><p>Extend the selection left one character. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7aa62e9ab460a49ff8b9c3c55219f98abb"></a>CharLeftRectExtend&#160;</td><td class="fielddoc"><p>Extend the rectangular selection left one character. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a3ce1411c8761d1562fa8e8b5d7609df7"></a>CharRight&#160;</td><td class="fielddoc"><p>Move right one character. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7aae6afb296e30c48ae1c4992817d673bf"></a>CharRightExtend&#160;</td><td class="fielddoc"><p>Extend the selection right one character. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7aedd92e9ae8401fb13ab6d01667949938"></a>CharRightRectExtend&#160;</td><td class="fielddoc"><p>Extend the rectangular selection right one character. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7aec027d50c71ee8604872c5cc839250cd"></a>WordLeft&#160;</td><td class="fielddoc"><p>Move left one word. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a5b4b9629de523b97fcbad43c21dc37bb"></a>WordLeftExtend&#160;</td><td class="fielddoc"><p>Extend the selection left one word. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a8e61252df5bfefd5081d9cb4170844d3"></a>WordRight&#160;</td><td class="fielddoc"><p>Move right one word. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ac69665982f4477d00509573ceea8a8f3"></a>WordRightExtend&#160;</td><td class="fielddoc"><p>Extend the selection right one word. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ac3e19af04d3083f26408dcb6ad3c73b7"></a>WordLeftEnd&#160;</td><td class="fielddoc"><p>Move to the end of the previous word. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ab2f7d005b37a7b61964e456b26d4a3f6"></a>WordLeftEndExtend&#160;</td><td class="fielddoc"><p>Extend the selection to the end of the previous word. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a1099ee8200e0d0799a2721ff9828fe48"></a>WordRightEnd&#160;</td><td class="fielddoc"><p>Move to the end of the next word. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a455d6eda859feb8aed088f22587d5aa3"></a>WordRightEndExtend&#160;</td><td class="fielddoc"><p>Extend the selection to the end of the next word. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ac1b4586de16ecd614e34d98c7ca360ec"></a>WordPartLeft&#160;</td><td class="fielddoc"><p>Move left one word part. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a0e5436d5c8bd4e42f0e542cbb852645d"></a>WordPartLeftExtend&#160;</td><td class="fielddoc"><p>Extend the selection left one word part. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a78c1a79cb762dc96072ef5bc1d90b20b"></a>WordPartRight&#160;</td><td class="fielddoc"><p>Move right one word part. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a1ad48872dc33ca01b0502594c6dd6df1"></a>WordPartRightExtend&#160;</td><td class="fielddoc"><p>Extend the selection right one word part. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a607f851f9833e13dbf335009ebd2ca37"></a>Home&#160;</td><td class="fielddoc"><p>Move to the start of the document line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7acbb515f305f0dc715372708d91be80aa"></a>HomeExtend&#160;</td><td class="fielddoc"><p>Extend the selection to the start of the document line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a3cf62acaae368a7a1e88a6300fbf1de4"></a>HomeRectExtend&#160;</td><td class="fielddoc"><p>Extend the rectangular selection to the start of the document line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a71d0019b185b1d65e9d3574651a1b55f"></a>HomeDisplay&#160;</td><td class="fielddoc"><p>Move to the start of the displayed line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a82851feb57f239b98cfa52fb2307fe66"></a>HomeDisplayExtend&#160;</td><td class="fielddoc"><p>Extend the selection to the start of the displayed line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ab67874e23a95cc208bcbb0de0cf16d90"></a>HomeWrap&#160;</td><td class="fielddoc"><p>Move to the start of the displayed or document line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7aa5afb3470010c1075e61cd4216a1714c"></a>HomeWrapExtend&#160;</td><td class="fielddoc"><p>Extend the selection to the start of the displayed or document line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ac443339cd46d0646cd97870506e91110"></a>VCHome&#160;</td><td class="fielddoc"><p>Move to the first visible character in the document line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a1e0ff1255da4ea0a77750d55a9aaaef4"></a>VCHomeExtend&#160;</td><td class="fielddoc"><p>Extend the selection to the first visible character in the document line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7adbf224a91e83518a244bb5a726c69bed"></a>VCHomeRectExtend&#160;</td><td class="fielddoc"><p>Extend the rectangular selection to the first visible character in the document line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7affdd507c7e4221726f980f95910ed5a5"></a>VCHomeWrap&#160;</td><td class="fielddoc"><p>Move to the first visible character of the displayed or document line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7af1db27f7f04534cc2071e71c422e4a45"></a>VCHomeWrapExtend&#160;</td><td class="fielddoc"><p>Extend the selection to the first visible character of the displayed or document line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a175bcd4e973a6ae4553827db95d987f6"></a>LineEnd&#160;</td><td class="fielddoc"><p>Move to the end of the document line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a4f42905e1e54f6eb4e91eb832c07e387"></a>LineEndExtend&#160;</td><td class="fielddoc"><p>Extend the selection to the end of the document line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a6d942d99ab734f6b5c1160cbe18a6197"></a>LineEndRectExtend&#160;</td><td class="fielddoc"><p>Extend the rectangular selection to the end of the document line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a77dc6d96d21c32e61b8e3809759eec37"></a>LineEndDisplay&#160;</td><td class="fielddoc"><p>Move to the end of the displayed line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7acfe625def4c875c4f3ed4011c1d30f30"></a>LineEndDisplayExtend&#160;</td><td class="fielddoc"><p>Extend the selection to the end of the displayed line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a2bcecd03bc30e56d92035364f1c4d3aa"></a>LineEndWrap&#160;</td><td class="fielddoc"><p>Move to the end of the displayed or document line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a8528ef0f7030d4eaa9cc93c1fb0f00d2"></a>LineEndWrapExtend&#160;</td><td class="fielddoc"><p>Extend the selection to the end of the displayed or document line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ae0d2fa78fc42366a578b50cae1c44a8f"></a>DocumentStart&#160;</td><td class="fielddoc"><p>Move to the start of the document. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a368bc9e6e89a0f9504a49fc97477618b"></a>DocumentStartExtend&#160;</td><td class="fielddoc"><p>Extend the selection to the start of the document. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a8e059c91d4d3d5037d0dce9c8fa735a0"></a>DocumentEnd&#160;</td><td class="fielddoc"><p>Move to the end of the document. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ac6b77e65e9d026dd2a3af831ddfcc664"></a>DocumentEndExtend&#160;</td><td class="fielddoc"><p>Extend the selection to the end of the document. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a69b8ec474437f655c93b019729093b82"></a>PageUp&#160;</td><td class="fielddoc"><p>Move up one page. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ac9c27eb69dde18a38bc1cbc84c9ee430"></a>PageUpExtend&#160;</td><td class="fielddoc"><p>Extend the selection up one page. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a31667dadedf4289250d3e1b5a1e9bf36"></a>PageUpRectExtend&#160;</td><td class="fielddoc"><p>Extend the rectangular selection up one page. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7acbd164907353acc3e218943d86d03b23"></a>PageDown&#160;</td><td class="fielddoc"><p>Move down one page. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ac6febe41f0669f54f4c14f2c32329569"></a>PageDownExtend&#160;</td><td class="fielddoc"><p>Extend the selection down one page. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a70472783731018e17e0943ee14e4ca6d"></a>PageDownRectExtend&#160;</td><td class="fielddoc"><p>Extend the rectangular selection down one page. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a9be0a9fe5bfc0864f0f40987a4806a62"></a>StutteredPageUp&#160;</td><td class="fielddoc"><p>Stuttered move up one page. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a26d878df5382e38843e754078aa8f44f"></a>StutteredPageUpExtend&#160;</td><td class="fielddoc"><p>Stuttered extend the selection up one page. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ab05b31fae6958a99166222cc3efd076a"></a>StutteredPageDown&#160;</td><td class="fielddoc"><p>Stuttered move down one page. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ad07964451843f3c910b7228dfb589857"></a>StutteredPageDownExtend&#160;</td><td class="fielddoc"><p>Stuttered extend the selection down one page. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ad763b356ba37cf93b78201baea5aa00d"></a>Delete&#160;</td><td class="fielddoc"><p>Delete the current character. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a82626bce8a0acdd6c4c196865629e81b"></a>DeleteBack&#160;</td><td class="fielddoc"><p>Delete the previous character. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7adadf0fa77a7ce5496fce517bc9e0a723"></a>DeleteBackNotLine&#160;</td><td class="fielddoc"><p>Delete the previous character if not at start of line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a66566eb3ac1ad83cc6ef2913d449d193"></a>DeleteWordLeft&#160;</td><td class="fielddoc"><p>Delete the word to the left. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a0a5ea33d56c6c45fe80f5b1f66975ffa"></a>DeleteWordRight&#160;</td><td class="fielddoc"><p>Delete the word to the right. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ae1a8da5398be3fb7c5e32f868bf4af14"></a>DeleteWordRightEnd&#160;</td><td class="fielddoc"><p>Delete right to the end of the next word. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a0b9644d959806dd50a8dce00bf521e13"></a>DeleteLineLeft&#160;</td><td class="fielddoc"><p>Delete the line to the left. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a21407e4871585bcfb0d76dbf7be87650"></a>DeleteLineRight&#160;</td><td class="fielddoc"><p>Delete the line to the right. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a291afea2c733abf34e20b0c25814dc5c"></a>LineDelete&#160;</td><td class="fielddoc"><p>Delete the current line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a91f9ce105ac6ead565f6f8e00ec0e9a6"></a>LineCut&#160;</td><td class="fielddoc"><p>Cut the current line to the clipboard. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7aa762bf03d4d23f764de57c146c9a658d"></a>LineCopy&#160;</td><td class="fielddoc"><p>Copy the current line to the clipboard. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ad6895dbe64da12bd7749e9c7bf4d8e75"></a>LineTranspose&#160;</td><td class="fielddoc"><p>Transpose the current and previous lines. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a8cc01e22c3d5cc697f87b00dcedb33f5"></a>LineDuplicate&#160;</td><td class="fielddoc"><p>Duplicate the current line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a8ddbd8f97e85bbef9e728a1293a94983"></a>SelectAll&#160;</td><td class="fielddoc"><p>Select the whole document. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7af5335bf501eb458872740c610db3617b"></a>MoveSelectedLinesUp&#160;</td><td class="fielddoc"><p>Move the selected lines up one line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7aae256fc63ff21305b2a1d93d7f05bee5"></a>MoveSelectedLinesDown&#160;</td><td class="fielddoc"><p>Move the selected lines down one line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ad10ac67847b362c169d7e3b0b3463290"></a>SelectionDuplicate&#160;</td><td class="fielddoc"><p>Duplicate the selection. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7aff49104707e447f73d08afd744b1f68d"></a>SelectionLowerCase&#160;</td><td class="fielddoc"><p>Convert the selection to lower case. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a5df7c07cf8cf1eee546837ece594dcaa"></a>SelectionUpperCase&#160;</td><td class="fielddoc"><p>Convert the selection to upper case. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a11cd9c83a7a1b74dc2a936e324ecf99e"></a>SelectionCut&#160;</td><td class="fielddoc"><p>Cut the selection to the clipboard. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a200b4ecea5a65b8690e8393b8ad3d512"></a>SelectionCopy&#160;</td><td class="fielddoc"><p>Copy the selection to the clipboard. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ac0877b23ce38bba85fc30eecb347a662"></a>Paste&#160;</td><td class="fielddoc"><p>Paste from the clipboard. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ab6c8c98a6027e8a88783f18dbca2bdf4"></a>EditToggleOvertype&#160;</td><td class="fielddoc"><p>Toggle insert/overtype. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ae71d9db2e7cf1f6ca4b731675e1d63a1"></a>Newline&#160;</td><td class="fielddoc"><p>Insert a platform dependent newline. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a3b46d96af1feddb3560236b9e75c39c2"></a>Formfeed&#160;</td><td class="fielddoc"><p>Insert a formfeed. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ab89051fd7c64cea84abec8d21809d2ee"></a>Tab&#160;</td><td class="fielddoc"><p>Indent one level. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7aeae07e48b489c0cc937bf83bef4f0c9c"></a>Backtab&#160;</td><td class="fielddoc"><p>De-indent one level. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a3905c749d29761ae2a594c14e1fb26c9"></a>Cancel&#160;</td><td class="fielddoc"><p>Cancel any current operation. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a0089e5295b999464b699fb736a449b4f"></a>Undo&#160;</td><td class="fielddoc"><p>Undo the last command. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ad12c4dd6bcaefc08fcff8fe3d80b3b66"></a>Redo&#160;</td><td class="fielddoc"><p>Redo the last command. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a5e12affbccb0a63c1bd78ca5617b0289"></a>ZoomIn&#160;</td><td class="fielddoc"><p>Zoom in. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7ae5d11d096795f1a1c7b4d56e41c9b1af"></a>ZoomOut&#160;</td><td class="fielddoc"><p>Zoom out. </p>
</td></tr>
<tr><td class="fieldname"><a id="aeaecb067c0834ba132e204a09dd942c7a740c74f61e7d91060ccc9e0945318787"></a>ReverseLines&#160;</td><td class="fielddoc"><p>Reverse the selected lines. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a6488ddf82659fcf42d704f787b6cb522"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6488ddf82659fcf42d704f787b6cb522">&#9670;&nbsp;</a></span>setKey()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciCommand::setKey </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>key</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Binds the key <em>key</em> to the command. If <em>key</em> is 0 then the key binding is removed. If <em>key</em> is invalid then the key binding is unchanged. Valid keys are any visible or control character or any of <code>Qt::Key_Down</code>, <code>Qt::Key_Up</code>, <code>Qt::Key_Left</code>, <code>Qt::Key_Right</code>, <code>Qt::Key_Home</code>, <code>Qt::Key_End</code>, <code>Qt::Key_PageUp</code>, <code>Qt::Key_PageDown</code>, <code>Qt::Key_Delete</code>, <code>Qt::Key_Insert</code>, <code>Qt::Key_Escape</code>, <code>Qt::Key_Backspace</code>, <code>Qt::Key_Tab</code>, <code>Qt::Key_Backtab</code>, <code>Qt::Key_Return</code>, <code>Qt::Key_Enter</code>, <code>Qt::Key_Super_L</code>, <code>Qt::Key_Super_R</code> or <code>Qt::Key_Menu</code>. Keys may be modified with any combination of <code>Qt::ShiftModifier</code>, <code>Qt::ControlModifier</code>, <code>Qt::AltModifier</code> and <code>Qt::MetaModifier</code>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciCommand.html#abf9dffcf6c222ecc02b28c3f6d17eb8e">key()</a>, <a class="el" href="classQsciCommand.html#a8c00e5f08abe7ad05fe54653c0f040ae">setAlternateKey()</a>, <a class="el" href="classQsciCommand.html#aeb517d586cb9569d072fcd8a9658911b" title="If the key key is valid then true is returned.">validKey()</a> </dd></dl>

</div>
</div>
<a id="a8c00e5f08abe7ad05fe54653c0f040ae"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8c00e5f08abe7ad05fe54653c0f040ae">&#9670;&nbsp;</a></span>setAlternateKey()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciCommand::setAlternateKey </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>altkey</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Binds the alternate key <em>altkey</em> to the command. If <em>key</em> is 0 then the alternate key binding is removed.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciCommand.html#ae6949756a800e31f1d279aa753060966">alternateKey()</a>, <a class="el" href="classQsciCommand.html#a6488ddf82659fcf42d704f787b6cb522">setKey()</a>, <a class="el" href="classQsciCommand.html#aeb517d586cb9569d072fcd8a9658911b" title="If the key key is valid then true is returned.">validKey()</a> </dd></dl>

</div>
</div>
<a id="abf9dffcf6c222ecc02b28c3f6d17eb8e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abf9dffcf6c222ecc02b28c3f6d17eb8e">&#9670;&nbsp;</a></span>key()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int QsciCommand::key </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The key that is currently bound to the command is returned.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciCommand.html#a6488ddf82659fcf42d704f787b6cb522">setKey()</a>, <a class="el" href="classQsciCommand.html#ae6949756a800e31f1d279aa753060966">alternateKey()</a> </dd></dl>

</div>
</div>
<a id="ae6949756a800e31f1d279aa753060966"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae6949756a800e31f1d279aa753060966">&#9670;&nbsp;</a></span>alternateKey()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int QsciCommand::alternateKey </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The alternate key that is currently bound to the command is returned.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciCommand.html#a8c00e5f08abe7ad05fe54653c0f040ae">setAlternateKey()</a>, <a class="el" href="classQsciCommand.html#abf9dffcf6c222ecc02b28c3f6d17eb8e">key()</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
