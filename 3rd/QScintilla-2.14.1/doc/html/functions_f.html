<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_f"></a>- f -</h3><ul>
<li>fillIndicatorRange()
: <a class="el" href="classQsciScintilla.html#a44d1c322098eb0cf44cf78e866ed80cb">QsciScintilla</a>
</li>
<li>Filter
: <a class="el" href="classQsciLexerAVS.html#a97b5e23dfd7e31204d054c97f8522a3caa78ba1174c6df16aceae243269933062">QsciLexerAVS</a>
</li>
<li>findFirst()
: <a class="el" href="classQsciScintilla.html#a04780d47f799c56b6af0a10b91875045">QsciScintilla</a>
</li>
<li>findFirstInSelection()
: <a class="el" href="classQsciScintilla.html#a437d58cc225880e34560b65f8c0b3b50">QsciScintilla</a>
</li>
<li>findMatchingBrace()
: <a class="el" href="classQsciScintilla.html#a5b4b113d57b73a7b531f137a98ecc1bc">QsciScintilla</a>
</li>
<li>findNext()
: <a class="el" href="classQsciScintilla.html#adc2560a55f77a14329db8409dba11c54">QsciScintilla</a>
</li>
<li>firstVisibleLine()
: <a class="el" href="classQsciScintilla.html#a689eed3f6219e20924bcffdb95f27526">QsciScintilla</a>
</li>
<li>Flags
: <a class="el" href="classQsciLexerPO.html#a9ccf3e0f2138e708eb3d4cf05311d53aa8189b733270e2d02ea3986e9bbfbe0d0">QsciLexerPO</a>
</li>
<li>focusInEvent()
: <a class="el" href="classQsciScintillaBase.html#a2c339fd90e92408440230ee9d84cabcf">QsciScintillaBase</a>
</li>
<li>focusNextPrevChild()
: <a class="el" href="classQsciScintillaBase.html#ad6fb5a9621640080fc9909f94b6c0213">QsciScintillaBase</a>
</li>
<li>focusOutEvent()
: <a class="el" href="classQsciScintillaBase.html#a1aec9b47eeaf611687eeeef0d1aa3a00">QsciScintillaBase</a>
</li>
<li>foldAll()
: <a class="el" href="classQsciScintilla.html#afea26881f9979a1769ba85c668351cee">QsciScintilla</a>
</li>
<li>foldAtBegin()
: <a class="el" href="classQsciLexerVHDL.html#a6443ca03dcf722445e6627e9991bb10c">QsciLexerVHDL</a>
</li>
<li>foldAtElse()
: <a class="el" href="classQsciLexerCMake.html#aa3c50f5bd00d091303665066c8f4d741">QsciLexerCMake</a>
, <a class="el" href="classQsciLexerCPP.html#aad7b42963ca382afb23eb000b727de12">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerD.html#a0e5afa1027b99648caeb70ed8423af2d">QsciLexerD</a>
, <a class="el" href="classQsciLexerPerl.html#a696abf6da5415e772e5ade8752eac3b2">QsciLexerPerl</a>
, <a class="el" href="classQsciLexerPostScript.html#aa86a61cd082e2e9fd76e878e8d6a096a">QsciLexerPostScript</a>
, <a class="el" href="classQsciLexerSQL.html#a4cf0c0ab9cb0628c515910c67fab9950">QsciLexerSQL</a>
, <a class="el" href="classQsciLexerVerilog.html#a1bb598533be61e117a252d06cf5e4a4b">QsciLexerVerilog</a>
, <a class="el" href="classQsciLexerVHDL.html#a46e8e5909bfc92669cf155317ecb6fe9">QsciLexerVHDL</a>
</li>
<li>foldAtModule()
: <a class="el" href="classQsciLexerVerilog.html#a8a4389bd37a806046a7c0b51cc1a6ead">QsciLexerVerilog</a>
</li>
<li>foldAtParenthesis()
: <a class="el" href="classQsciLexerVHDL.html#afd8eebb9ee14760d1529f614f18a2e52">QsciLexerVHDL</a>
</li>
<li>foldComments()
: <a class="el" href="classQsciLexerAsm.html#aca20aadf0e0bc419b20eeb8ee4e7e261">QsciLexerAsm</a>
, <a class="el" href="classQsciLexerAVS.html#a42a1cdec7111af0685a9d89419a821bd">QsciLexerAVS</a>
, <a class="el" href="classQsciLexerBash.html#a27f6ffff6c6020126b5318ed8ba76c54">QsciLexerBash</a>
, <a class="el" href="classQsciLexerCoffeeScript.html#a6865962a5df72e37f4ba49c6e5e539b6">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#adc253f08156cde45b331c5a7ed07cfd7">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerCSS.html#ae32591385112bb3d33de8c1c40888190">QsciLexerCSS</a>
, <a class="el" href="classQsciLexerD.html#a8c74012833091c1f71e2bea9d1a2a5d5">QsciLexerD</a>
, <a class="el" href="classQsciLexerPascal.html#a30d1fae97aaef0b3fafab8e790caf130">QsciLexerPascal</a>
, <a class="el" href="classQsciLexerPerl.html#ae9881257bbcc887cdbe21e74bbb8ea65">QsciLexerPerl</a>
, <a class="el" href="classQsciLexerPO.html#a40b4351699e48f7b34faa84c5dbc4704">QsciLexerPO</a>
, <a class="el" href="classQsciLexerPOV.html#a7a08d9dcb4935d7e2c99696bdcfd8e7a">QsciLexerPOV</a>
, <a class="el" href="classQsciLexerPython.html#a1a7acaa356fdbefd26cfe0f30264c43a">QsciLexerPython</a>
, <a class="el" href="classQsciLexerRuby.html#a846ebeb36f0847cee3599860f787bcde">QsciLexerRuby</a>
, <a class="el" href="classQsciLexerSQL.html#add132f5762831171fdee856172a0a5dc">QsciLexerSQL</a>
, <a class="el" href="classQsciLexerTCL.html#a71b5b892a7a30a4f57b9efa64fdf5f32">QsciLexerTCL</a>
, <a class="el" href="classQsciLexerTeX.html#a06f4a0a490680e76ba0edb0fe4fe4dc5">QsciLexerTeX</a>
, <a class="el" href="classQsciLexerVerilog.html#a7e9725132ec5521255eb7d9ac81ae853">QsciLexerVerilog</a>
, <a class="el" href="classQsciLexerVHDL.html#ae2d2e6936f7b0f6f9b891ac14dff7bc0">QsciLexerVHDL</a>
, <a class="el" href="classQsciLexerYAML.html#adfb332858ee86dc00f58f309f394d9d8">QsciLexerYAML</a>
</li>
<li>foldCompact()
: <a class="el" href="classQsciLexerAsm.html#a76f950916c4638019fa3bd8c4ab601a3">QsciLexerAsm</a>
, <a class="el" href="classQsciLexerAVS.html#acee212ef3dceca125cadb16ae9cc5fc3">QsciLexerAVS</a>
, <a class="el" href="classQsciLexerBash.html#a554d4c9b25ad66e23092adf6f9b0460e">QsciLexerBash</a>
, <a class="el" href="classQsciLexerCoffeeScript.html#a16d546ecc7d16a609e368a4d2d557605">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#ad0939852605ee45ce62f70647d47147b">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerCSS.html#aecbd103b6dff15873e60cdf48e634a4a">QsciLexerCSS</a>
, <a class="el" href="classQsciLexerD.html#a02cb3518d6145815b22359d8d5aa2cf1">QsciLexerD</a>
, <a class="el" href="classQsciLexerFortran77.html#a855940eae63985a7ff92ffd545d88bd4">QsciLexerFortran77</a>
, <a class="el" href="classQsciLexerHTML.html#af861d2c565994b427514715fe95a80b7">QsciLexerHTML</a>
, <a class="el" href="classQsciLexerJSON.html#a16a2faffdfcc5893a6fd08d270d69798">QsciLexerJSON</a>
, <a class="el" href="classQsciLexerLua.html#a27383c2def3f59e903aec9537ef43d2c">QsciLexerLua</a>
, <a class="el" href="classQsciLexerPascal.html#ae88783c3de2f0c4a0129e5bec77cc5ca">QsciLexerPascal</a>
, <a class="el" href="classQsciLexerPerl.html#a0c3e05e1bbdc4614fc7e76e508178592">QsciLexerPerl</a>
, <a class="el" href="classQsciLexerPO.html#a1ec44d2dfe2f40d6267f3067f66c9a3d">QsciLexerPO</a>
, <a class="el" href="classQsciLexerPostScript.html#accb7e71496e6817503ea1c081ffdbab4">QsciLexerPostScript</a>
, <a class="el" href="classQsciLexerPOV.html#a9a4ec081c6812ffb3ebc5082c08bf0db">QsciLexerPOV</a>
, <a class="el" href="classQsciLexerProperties.html#a2e2317346a85697a98a2e19d1c596a48">QsciLexerProperties</a>
, <a class="el" href="classQsciLexerPython.html#a21891669bab4719e8e7cf482e3bf5a51">QsciLexerPython</a>
, <a class="el" href="classQsciLexerRuby.html#acbaa96d72ad071768acc25d7d56b6324">QsciLexerRuby</a>
, <a class="el" href="classQsciLexerSQL.html#a1d73603ec19f317dd0d6271ec852c0fc">QsciLexerSQL</a>
, <a class="el" href="classQsciLexerTeX.html#a01e79cce2d3e498bc5473db51e1d0bc6">QsciLexerTeX</a>
, <a class="el" href="classQsciLexerVerilog.html#ae1f192b93ad970cb792b5dcac4aa22d8">QsciLexerVerilog</a>
, <a class="el" href="classQsciLexerVHDL.html#a043411367c3fa915c8f4797cc51d0c8c">QsciLexerVHDL</a>
</li>
<li>foldDirectives()
: <a class="el" href="classQsciLexerPOV.html#ad16788518def261f1ce55b35141642ad">QsciLexerPOV</a>
</li>
<li>folding()
: <a class="el" href="classQsciScintilla.html#a0e4576d83d7604b24bd13be91011c5f7">QsciScintilla</a>
</li>
<li>foldLine()
: <a class="el" href="classQsciScintilla.html#ad6557ee0ca58413e8d3e76b942f25a7f">QsciScintilla</a>
</li>
<li>foldOnlyBegin()
: <a class="el" href="classQsciLexerSQL.html#a9a7c5fb256df97053fbe3203aaf3a93a">QsciLexerSQL</a>
</li>
<li>foldPackages()
: <a class="el" href="classQsciLexerPerl.html#a5ea4eb1e65b2cee23a09f143074790b4">QsciLexerPerl</a>
</li>
<li>foldPODBlocks()
: <a class="el" href="classQsciLexerPerl.html#ab58e1e9d037d280fc74792ace83936d4">QsciLexerPerl</a>
</li>
<li>foldPreprocessor()
: <a class="el" href="classQsciLexerCPP.html#a3c93f14b36897ecb3f902b5e5de91ad6">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerHTML.html#a2f3d753794280bfc09719e3ca521be83">QsciLexerHTML</a>
, <a class="el" href="classQsciLexerPascal.html#adae268febf025354165c88afa2414c73">QsciLexerPascal</a>
, <a class="el" href="classQsciLexerVerilog.html#ad70da8e3f3695cfc277d02ab9c0396d3">QsciLexerVerilog</a>
</li>
<li>foldQuotes()
: <a class="el" href="classQsciLexerPython.html#a167dbdb42a4c0ed65229a3418153d4dd">QsciLexerPython</a>
</li>
<li>foldScriptComments()
: <a class="el" href="classQsciLexerHTML.html#aaf885bb9d07591114c2a2cc5769bb02a">QsciLexerHTML</a>
</li>
<li>foldScriptHeredocs()
: <a class="el" href="classQsciLexerHTML.html#aabad8cc171c34609ee0d6c600a534334">QsciLexerHTML</a>
</li>
<li>FoldStyle
: <a class="el" href="classQsciScintilla.html#ae478a896ae32a30e8a375049a3d477e0">QsciScintilla</a>
</li>
<li>foldSyntaxBased()
: <a class="el" href="classQsciLexerAsm.html#ade18380cb4440beca5452b908d66f5c9">QsciLexerAsm</a>
</li>
<li>font()
: <a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">QsciLexer</a>
, <a class="el" href="classQsciStyle.html#af45628c04ab5488fc13b61a2356346ec">QsciStyle</a>
</li>
<li>fontChanged()
: <a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">QsciLexer</a>
</li>
<li>FormatBody
: <a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9adbc0e3fbe443a92d03f444a1f66b1d5c">QsciLexerPerl</a>
</li>
<li>FormatIdentifier
: <a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9af1b0ae17967c8b101eea3d9e4cc173cd">QsciLexerPerl</a>
</li>
<li>formatPage()
: <a class="el" href="classQsciPrinter.html#a420e136529a8d49551eb8af0f5cdce03">QsciPrinter</a>
</li>
<li>Formfeed
: <a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7a3b46d96af1feddb3560236b9e75c39c2">QsciCommand</a>
</li>
<li>FPUInstruction
: <a class="el" href="classQsciLexerAsm.html#a59ba5e0645fb67d5ad54c1e5fafcb360a37d0bde0ceb5da20b12e0dbd9fad5819">QsciLexerAsm</a>
</li>
<li>fromMimeData()
: <a class="el" href="classQsciScintillaBase.html#a5f105b9ec17cd73a0cd601ac9be82dd4">QsciScintillaBase</a>
</li>
<li>FullBoxIndicator
: <a class="el" href="classQsciScintilla.html#a3333f3a47163153c1bd7db1a362b8974aeb5cb655e2f3e61e8df54655b11695f1">QsciScintilla</a>
</li>
<li>FullRectangle
: <a class="el" href="classQsciScintilla.html#a08467ef528d3048db763979f42664496a37179bb1a189664cf22b25168ae0dc2b">QsciScintilla</a>
</li>
<li>Function
: <a class="el" href="classQsciLexerAVS.html#a97b5e23dfd7e31204d054c97f8522a3ca4235f8fb2c5f08d64d9564c53c9b716d">QsciLexerAVS</a>
, <a class="el" href="classQsciLexerCMake.html#a66895a601b7ef292c78a2ad73305cde5acd122c7f7d43a9a7757982877d7e791c">QsciLexerCMake</a>
, <a class="el" href="classQsciLexerSpice.html#a99b1b104224cab9d85ef6cf254ae631bab2ac7b9071a9551de2302ac670e591fb">QsciLexerSpice</a>
</li>
<li>FunctionMethodName
: <a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301af182efea8f041750b774f01071af8b10">QsciLexerPython</a>
, <a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafdafc1f84fb3175d37e1a12822cdea2aabf">QsciLexerRuby</a>
</li>
<li>Fuzzy
: <a class="el" href="classQsciLexerPO.html#a9ccf3e0f2138e708eb3d4cf05311d53aabc0289fe1ca5de9e2fc729b46e7fca8b">QsciLexerPO</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
