<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_a"></a>- a -</h3><ul>
<li>AcsAll
: <a class="el" href="classQsciScintilla.html#ac466f32c3d7e51790b6b25c864783179a98ee0a7e261ec6cac6d89ba869117546">QsciScintilla</a>
</li>
<li>AcsAPIs
: <a class="el" href="classQsciScintilla.html#ac466f32c3d7e51790b6b25c864783179accaaf618ca9c7889b9899c4da6e9f17f">QsciScintilla</a>
</li>
<li>AcsDocument
: <a class="el" href="classQsciScintilla.html#ac466f32c3d7e51790b6b25c864783179a8ce5fd7a52d924d7e519eb7beccab5a1">QsciScintilla</a>
</li>
<li>AcsNone
: <a class="el" href="classQsciScintilla.html#ac466f32c3d7e51790b6b25c864783179ac66171d5698c13fb78053b1cccc4024a">QsciScintilla</a>
</li>
<li>AcusAlways
: <a class="el" href="classQsciScintilla.html#ae628d46489efa3db3b0c42336a1bf8d3ad3646f7c7cfe985480477c7c303af9e6">QsciScintilla</a>
</li>
<li>AcusExplicit
: <a class="el" href="classQsciScintilla.html#ae628d46489efa3db3b0c42336a1bf8d3a58a27ab02bc6a09334b2e91a16443ed4">QsciScintilla</a>
</li>
<li>AcusNever
: <a class="el" href="classQsciScintilla.html#ae628d46489efa3db3b0c42336a1bf8d3afd235254a43ffd260648079f71a31f7e">QsciScintilla</a>
</li>
<li>add()
: <a class="el" href="classQsciAPIs.html#af46ca05571eb676d3aa65b080fb406c5">QsciAPIs</a>
</li>
<li>addEdgeColumn()
: <a class="el" href="classQsciScintilla.html#ac46502c93651ec7a6642afe5dca86ffc">QsciScintilla</a>
</li>
<li>AddingPatchAdded
: <a class="el" href="classQsciLexerDiff.html#a331f318fc5d294a19044a748f9b8053eabb7b5d1995a5678ccba9bbb49aeea808">QsciLexerDiff</a>
</li>
<li>AddingPatchRemoved
: <a class="el" href="classQsciLexerDiff.html#a331f318fc5d294a19044a748f9b8053ea065844824b738df0d7ada07783ff9123">QsciLexerDiff</a>
</li>
<li>AiClosing
: <a class="el" href="classQsciScintilla.html#a486adb3348b30c80f53cc1f00c4ed978acae08c8d6e6cc73fcd5492d46e2432eb">QsciScintilla</a>
</li>
<li>AiMaintain
: <a class="el" href="classQsciScintilla.html#a486adb3348b30c80f53cc1f00c4ed978a63083d9a621b8dc11de24e63f2ccdef6">QsciScintilla</a>
</li>
<li>AiOpening
: <a class="el" href="classQsciScintilla.html#a486adb3348b30c80f53cc1f00c4ed978a4644ed0f2bb211f82d6ceec31cf0b1ad">QsciScintilla</a>
</li>
<li>alternateKey()
: <a class="el" href="classQsciCommand.html#ae6949756a800e31f1d279aa753060966">QsciCommand</a>
</li>
<li>annotate()
: <a class="el" href="classQsciScintilla.html#adacd79ec5e25430f0cbff34e6584afe7">QsciScintilla</a>
</li>
<li>annotation()
: <a class="el" href="classQsciScintilla.html#a6a577313664af6dc63885f03e88d03af">QsciScintilla</a>
</li>
<li>AnnotationBoxed
: <a class="el" href="classQsciScintilla.html#a3793111b6e2a86351c798c68deda7d0ca3b77a53cb3bc889bb98c4d0b79884709">QsciScintilla</a>
</li>
<li>AnnotationDisplay
: <a class="el" href="classQsciScintilla.html#a3793111b6e2a86351c798c68deda7d0c">QsciScintilla</a>
</li>
<li>annotationDisplay()
: <a class="el" href="classQsciScintilla.html#a3045ab135148ca52330ad233703a57f1">QsciScintilla</a>
</li>
<li>AnnotationHidden
: <a class="el" href="classQsciScintilla.html#a3793111b6e2a86351c798c68deda7d0caf708fc8c6c5b70daf1e03d5286a699f6">QsciScintilla</a>
</li>
<li>AnnotationIndented
: <a class="el" href="classQsciScintilla.html#a3793111b6e2a86351c798c68deda7d0ca180ea6ab83ef2cfb3e8a31b01f3c31c7">QsciScintilla</a>
</li>
<li>AnnotationStandard
: <a class="el" href="classQsciScintilla.html#a3793111b6e2a86351c798c68deda7d0ca9127adc556d20a38d87055264228b121">QsciScintilla</a>
</li>
<li>apiContext()
: <a class="el" href="classQsciScintilla.html#ab8bfeae44abd61659d207a86660b100c">QsciScintilla</a>
</li>
<li>apiPreparationCancelled()
: <a class="el" href="classQsciAPIs.html#aaa47506820a2596004688e241fc4cd9f">QsciAPIs</a>
</li>
<li>apiPreparationFinished()
: <a class="el" href="classQsciAPIs.html#adf779559d29fed004ec65ef560483e3c">QsciAPIs</a>
</li>
<li>apiPreparationStarted()
: <a class="el" href="classQsciAPIs.html#a8fc5db618546fcfcc5bdc46e6d062995">QsciAPIs</a>
</li>
<li>apis()
: <a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">QsciLexer</a>
</li>
<li>append()
: <a class="el" href="classQsciScintilla.html#ae7310729b1be2aa937a22036f5d95b51">QsciScintilla</a>
</li>
<li>Array
: <a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9aebe8e5c6b96783494e61c8cd03975570">QsciLexerPerl</a>
</li>
<li>ArrayParenthesis
: <a class="el" href="classQsciLexerPostScript.html#a129e6281661808e7d4d53025706abc77a68508fc322ed18c4eef49d6f3c562dcc">QsciLexerPostScript</a>
</li>
<li>Asm
: <a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfa03866519c0b3f2113793dead5db53daa">QsciLexerPascal</a>
</li>
<li>ASPAtStart
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ae5c90edfb9068eaea785bf14f2371120">QsciLexerHTML</a>
</li>
<li>ASPJavaScriptComment
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aa14057c2c5ef886e2f72cddeb2914afb">QsciLexerHTML</a>
</li>
<li>ASPJavaScriptCommentDoc
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a803c5ffa1134c3623ade6d4bb683c8e8">QsciLexerHTML</a>
</li>
<li>ASPJavaScriptCommentLine
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a86cd63314aba32adc7926e6e47a4395d">QsciLexerHTML</a>
</li>
<li>ASPJavaScriptDefault
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a9f89a8d7bd5e2d2855eb957f9ba9c87b">QsciLexerHTML</a>
</li>
<li>ASPJavaScriptDoubleQuotedString
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42adb4788e2364a6860cf7248c72a457736">QsciLexerHTML</a>
</li>
<li>ASPJavaScriptKeyword
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a342baec8e1e79525b30e887321e60b99">QsciLexerHTML</a>
</li>
<li>ASPJavaScriptNumber
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a472fbd596cdf4ace8183bb4f050c8b2c">QsciLexerHTML</a>
</li>
<li>ASPJavaScriptRegex
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a0391cd592ce195d67507404f2a6f7cc1">QsciLexerHTML</a>
</li>
<li>ASPJavaScriptSingleQuotedString
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a54a7f4bfb454ab5c0c94e11a0767d3af">QsciLexerHTML</a>
</li>
<li>ASPJavaScriptStart
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42af5583e1cd3c0f89d89a9500274412702">QsciLexerHTML</a>
</li>
<li>ASPJavaScriptSymbol
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a5c87399fc70041dd85ce718d94c6139e">QsciLexerHTML</a>
</li>
<li>ASPJavaScriptUnclosedString
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ab9e69127f9a571ab7bff1bc87c052776">QsciLexerHTML</a>
</li>
<li>ASPJavaScriptWord
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ab388e2836763fec9ba15e7a1b3743e6d">QsciLexerHTML</a>
</li>
<li>ASPPythonClassName
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a07bad38a70450a58da8bd7ebebc9f4d4">QsciLexerHTML</a>
</li>
<li>ASPPythonComment
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a9f91ee5b30f04252a226410118f87cbb">QsciLexerHTML</a>
</li>
<li>ASPPythonDefault
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a24d9181cb4ffca0ec889f64d32e27302">QsciLexerHTML</a>
</li>
<li>ASPPythonDoubleQuotedString
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ac410213e6afae932c50c5d7386180a82">QsciLexerHTML</a>
</li>
<li>ASPPythonFunctionMethodName
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a3ef7c5d90b7885f79a9200e8144d461c">QsciLexerHTML</a>
</li>
<li>ASPPythonIdentifier
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a2ba2b64ee2400dce3ee221aef187e524">QsciLexerHTML</a>
</li>
<li>ASPPythonKeyword
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a9fc4d4e4fc20ca395d19a52e6e29453e">QsciLexerHTML</a>
</li>
<li>ASPPythonNumber
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ac1973f076c1eb88d6ab71aab19ee839d">QsciLexerHTML</a>
</li>
<li>ASPPythonOperator
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aa38331046f91ae174bed6bed7d1c1154">QsciLexerHTML</a>
</li>
<li>ASPPythonSingleQuotedString
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aeba824b1c987b60d06c7bdd6c77858a5">QsciLexerHTML</a>
</li>
<li>ASPPythonStart
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aae87de6d2d1f768e5e09e1b6d7d8e2c5">QsciLexerHTML</a>
</li>
<li>ASPPythonTripleDoubleQuotedString
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42abf69f7d37a77f061868d594516f21b75">QsciLexerHTML</a>
</li>
<li>ASPPythonTripleSingleQuotedString
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42af3dd65a9a5b6e685630ead91aebdd994">QsciLexerHTML</a>
</li>
<li>ASPStart
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a292d607fdb907de9d5901c90b01f64a5">QsciLexerHTML</a>
</li>
<li>ASPVBScriptComment
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ac93712a2bf29f750c9b8629ba1aa6a8d">QsciLexerHTML</a>
</li>
<li>ASPVBScriptDefault
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a37226b834916114680ba667ef9615293">QsciLexerHTML</a>
</li>
<li>ASPVBScriptIdentifier
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ac3a6e8ea35f788fd08bd245ab1238709">QsciLexerHTML</a>
</li>
<li>ASPVBScriptKeyword
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a99e48d7de494bb0a1ea1b5503014a50e">QsciLexerHTML</a>
</li>
<li>ASPVBScriptNumber
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42aa60810f36db9d4690903279530d2f93e">QsciLexerHTML</a>
</li>
<li>ASPVBScriptStart
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a0af21eed628c46b93d8f46d78af3e18e">QsciLexerHTML</a>
</li>
<li>ASPVBScriptString
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42ab5af85bbbcc82ee3bd6e3f60dfc6e43c">QsciLexerHTML</a>
</li>
<li>ASPVBScriptUnclosedString
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a70448ba15dedb0dea1a6e10d806ac03d">QsciLexerHTML</a>
</li>
<li>ASPXCComment
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a93b7ceec0b76249d6c4ef8caeb8a1c6e">QsciLexerHTML</a>
</li>
<li>Assignment
: <a class="el" href="classQsciLexerProperties.html#a7e63bce5cf5dafed391333a8dfdf9d1da2dce9dbe1d758936b321ee9d77343be6">QsciLexerProperties</a>
</li>
<li>AtRule
: <a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0ab66090eb95d05ecb61cb8a4822ab94d8">QsciLexerCSS</a>
</li>
<li>Attribute
: <a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0ac1a1825aa643819ef0ed1c3a23ce48ee">QsciLexerCSS</a>
, <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42acc9fa3017024877e48e2e4bdc139243c">QsciLexerHTML</a>
, <a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300ae259747dcdaff0bffe5da604d93ee4a5">QsciLexerVHDL</a>
</li>
<li>autoCompleteFromAll()
: <a class="el" href="classQsciScintilla.html#a5f10e95e76c2b38b9d20f6f728243e65">QsciScintilla</a>
</li>
<li>autoCompleteFromAPIs()
: <a class="el" href="classQsciScintilla.html#ae89d58a67f46efad7136bead41232fd6">QsciScintilla</a>
</li>
<li>autoCompleteFromDocument()
: <a class="el" href="classQsciScintilla.html#ae4d479c640e2ea4444aa905f69495321">QsciScintilla</a>
</li>
<li>autoCompletionCaseSensitivity()
: <a class="el" href="classQsciScintilla.html#a136a17a59a3800c40619a768ffff8d7a">QsciScintilla</a>
</li>
<li>autoCompletionFillups()
: <a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">QsciLexer</a>
, <a class="el" href="classQsciLexerHTML.html#ae6ef21c59fd374d1d4893adcc1e3ee9b">QsciLexerHTML</a>
</li>
<li>autoCompletionFillupsEnabled()
: <a class="el" href="classQsciScintilla.html#a47d0cdb5df5fa6c860b4e840184fe585">QsciScintilla</a>
</li>
<li>autoCompletionReplaceWord()
: <a class="el" href="classQsciScintilla.html#a527b309bfaf28b870c15d87a5af7c005">QsciScintilla</a>
</li>
<li>autoCompletionSelected()
: <a class="el" href="classQsciAbstractAPIs.html#a90fa0f912b748b707967ccb722f04ddc">QsciAbstractAPIs</a>
, <a class="el" href="classQsciAPIs.html#adff0073d1f4ee2e0ea8b3bf234ff2dd3">QsciAPIs</a>
</li>
<li>autoCompletionShowSingle()
: <a class="el" href="classQsciScintilla.html#aff3b4e47fcbadeb0cf2556cf6ad164e0">QsciScintilla</a>
</li>
<li>AutoCompletionSource
: <a class="el" href="classQsciScintilla.html#ac466f32c3d7e51790b6b25c864783179">QsciScintilla</a>
</li>
<li>autoCompletionSource()
: <a class="el" href="classQsciScintilla.html#a6c06ccce022c08674e24a96093902b49">QsciScintilla</a>
</li>
<li>autoCompletionThreshold()
: <a class="el" href="classQsciScintilla.html#a5178b3fd3cb946ffd4b2e52df9bb1483">QsciScintilla</a>
</li>
<li>autoCompletionUseSingle()
: <a class="el" href="classQsciScintilla.html#aaeb4a9e6d4e2822524c84da5318a7f1e">QsciScintilla</a>
</li>
<li>AutoCompletionUseSingle
: <a class="el" href="classQsciScintilla.html#ae628d46489efa3db3b0c42336a1bf8d3">QsciScintilla</a>
</li>
<li>autoCompletionWordSeparators()
: <a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">QsciLexer</a>
, <a class="el" href="classQsciLexerCoffeeScript.html#a0741fad0b942deb73642be16c3159eb1">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#af0ccf94585e15b87a18f12ab9de1c977">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerD.html#a9080d0a47d2cbd972d5f2e6c737ba7fa">QsciLexerD</a>
, <a class="el" href="classQsciLexerLua.html#aff715db68554a1022792135e8edd0dba">QsciLexerLua</a>
, <a class="el" href="classQsciLexerPascal.html#aa28fa3e32d5d4a4efccdad6655fb28c8">QsciLexerPascal</a>
, <a class="el" href="classQsciLexerPerl.html#a0e4df63d7d5714b1bdb71c1975f7f99c">QsciLexerPerl</a>
, <a class="el" href="classQsciLexerPython.html#a305ec320aa2357947cbeb1608b95d840">QsciLexerPython</a>
</li>
<li>autoIndent()
: <a class="el" href="classQsciScintilla.html#a828f1b56453686ccd423e979e55fbbae">QsciScintilla</a>
</li>
<li>autoIndentStyle()
: <a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">QsciLexer</a>
</li>
<li>QsciAbstractAPIs()
: <a class="el" href="classQsciAbstractAPIs.html#a9db5ebe8adda3f58892af676f5295e3a">QsciAbstractAPIs</a>
</li>
<li>QsciAPIs()
: <a class="el" href="classQsciAPIs.html#aaf185d65d1034087b77995d8490b6475">QsciAPIs</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
