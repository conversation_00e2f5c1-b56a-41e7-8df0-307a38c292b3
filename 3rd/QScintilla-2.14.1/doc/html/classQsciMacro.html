<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciMacro Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-slots">Public Slots</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classQsciMacro-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciMacro Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscimacro.h&gt;</code></p>

<p>Inherits QObject.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-slots"></a>
Public Slots</h2></td></tr>
<tr class="memitem:a3de5fbc4e99be9cb9d10d90dd8b1059d"><td class="memItemLeft" align="right" valign="top"><a id="a3de5fbc4e99be9cb9d10d90dd8b1059d"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciMacro.html#a3de5fbc4e99be9cb9d10d90dd8b1059d">play</a> ()</td></tr>
<tr class="separator:a3de5fbc4e99be9cb9d10d90dd8b1059d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4a5648ea6c1e35aaaa55f9aaf83e7eda"><td class="memItemLeft" align="right" valign="top"><a id="a4a5648ea6c1e35aaaa55f9aaf83e7eda"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciMacro.html#a4a5648ea6c1e35aaaa55f9aaf83e7eda">startRecording</a> ()</td></tr>
<tr class="separator:a4a5648ea6c1e35aaaa55f9aaf83e7eda"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a783f17e12ca844655568b5718aa26a35"><td class="memItemLeft" align="right" valign="top"><a id="a783f17e12ca844655568b5718aa26a35"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciMacro.html#a783f17e12ca844655568b5718aa26a35">endRecording</a> ()</td></tr>
<tr class="separator:a783f17e12ca844655568b5718aa26a35"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a168da9e3a890906f229505cd253eec4b"><td class="memItemLeft" align="right" valign="top"><a id="a168da9e3a890906f229505cd253eec4b"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciMacro.html#a168da9e3a890906f229505cd253eec4b">QsciMacro</a> (<a class="el" href="classQsciScintilla.html">QsciScintilla</a> *parent)</td></tr>
<tr class="separator:a168da9e3a890906f229505cd253eec4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a933a97fb937d67dbe86a4abe229c755f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciMacro.html#a933a97fb937d67dbe86a4abe229c755f">QsciMacro</a> (const QString &amp;asc, <a class="el" href="classQsciScintilla.html">QsciScintilla</a> *parent)</td></tr>
<tr class="separator:a933a97fb937d67dbe86a4abe229c755f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a17533fc70491bd7752d4a8ead5facf01"><td class="memItemLeft" align="right" valign="top"><a id="a17533fc70491bd7752d4a8ead5facf01"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciMacro.html#a17533fc70491bd7752d4a8ead5facf01">~QsciMacro</a> ()</td></tr>
<tr class="separator:a17533fc70491bd7752d4a8ead5facf01"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4387e4fa992c8671dd508c0c2651e34f"><td class="memItemLeft" align="right" valign="top"><a id="a4387e4fa992c8671dd508c0c2651e34f"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciMacro.html#a4387e4fa992c8671dd508c0c2651e34f">clear</a> ()</td></tr>
<tr class="separator:a4387e4fa992c8671dd508c0c2651e34f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c342b5db46e12a8d73567fac9959543"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciMacro.html#a1c342b5db46e12a8d73567fac9959543">load</a> (const QString &amp;asc)</td></tr>
<tr class="separator:a1c342b5db46e12a8d73567fac9959543"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6af9c876a10d746177790189067aaf6a"><td class="memItemLeft" align="right" valign="top">QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciMacro.html#a6af9c876a10d746177790189067aaf6a">save</a> () const</td></tr>
<tr class="separator:a6af9c876a10d746177790189067aaf6a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciMacro.html" title="The QsciMacro class represents a sequence of recordable editor commands.">QsciMacro</a> class represents a sequence of recordable editor commands. </p>
<p>Methods are provided to convert convert a macro to and from a textual representation so that they can be easily written to and read from permanent storage. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a933a97fb937d67dbe86a4abe229c755f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a933a97fb937d67dbe86a4abe229c755f">&#9670;&nbsp;</a></span>QsciMacro()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciMacro::QsciMacro </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>asc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classQsciScintilla.html">QsciScintilla</a> *&#160;</td>
          <td class="paramname"><em>parent</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Construct a <a class="el" href="classQsciMacro.html" title="The QsciMacro class represents a sequence of recordable editor commands.">QsciMacro</a> from the printable ASCII representation <em>asc</em>, with parent <em>parent</em>. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a1c342b5db46e12a8d73567fac9959543"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1c342b5db46e12a8d73567fac9959543">&#9670;&nbsp;</a></span>load()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciMacro::load </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>asc</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Load the macro from the printable ASCII representation <em>asc</em>. Returns true if there was no error.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciMacro.html#a6af9c876a10d746177790189067aaf6a">save()</a> </dd></dl>

</div>
</div>
<a id="a6af9c876a10d746177790189067aaf6a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6af9c876a10d746177790189067aaf6a">&#9670;&nbsp;</a></span>save()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QString QsciMacro::save </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Return a printable ASCII representation of the macro. It is guaranteed that only printable ASCII characters are used and that double quote characters will not be used.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciMacro.html#a1c342b5db46e12a8d73567fac9959543">load()</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
