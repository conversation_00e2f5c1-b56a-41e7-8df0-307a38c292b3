<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciLexerPascal Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-slots">Public Slots</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="classQsciLexerPascal-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciLexerPascal Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscilexerpascal.h&gt;</code></p>

<p>Inherits <a class="el" href="classQsciLexer.html">QsciLexer</a>.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a0c7562ea6d7a9d8a794daf47228c22df"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfa71c490d79223177530ef9415edf52747">Default</a> = 0, 
<a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfa51dbc500bd8cc6a05ca0a04e9220e9b8">Identifier</a> = 1, 
<a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfab48837000308dc11499d7e96f302db6a">Comment</a> = 2, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfabd390815063a649b2cab3f5da6d4b113">CommentParenthesis</a> = 3, 
<a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfab4771b618f3f481962bc73d7d1e63cc5">CommentLine</a> = 4, 
<a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfacfd2039caa846a92af36182615b36777">PreProcessor</a> = 5, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfa414d215a01c3d205a300976cf7f81556">PreProcessorParenthesis</a> = 6, 
<a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfac3c433f5759c8996553c5e203077fe0d">Number</a> = 7, 
<a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfa12bdf1e4458d0e5aa6ba031eee1c81b2">HexNumber</a> = 8, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfa0df50778af3ef8ecbd584fca00d5337a">Keyword</a> = 9, 
<a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfae325b4d8dbeeb693c7b76b746ee81e81">SingleQuotedString</a> = 10, 
<a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfa19098093aa64ac0fdca24cacb30983cf">UnclosedString</a> = 11, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfae68a19ab0843b318915c86f7d353590c">Character</a> = 12, 
<a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfae83775614e9f03b1bb41d78023f0121a">Operator</a> = 13, 
<a class="el" href="classQsciLexerPascal.html#a0c7562ea6d7a9d8a794daf47228c22dfa03866519c0b3f2113793dead5db53daa">Asm</a> = 14
<br />
 }</td></tr>
<tr class="separator:a0c7562ea6d7a9d8a794daf47228c22df"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-slots"></a>
Public Slots</h2></td></tr>
<tr class="memitem:a05d880fd1451f6a757fd21a7bd43a358"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a05d880fd1451f6a757fd21a7bd43a358">setFoldComments</a> (bool fold)</td></tr>
<tr class="separator:a05d880fd1451f6a757fd21a7bd43a358"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d183c40c276dadd3bbb994b0c0f26ce"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a2d183c40c276dadd3bbb994b0c0f26ce">setFoldCompact</a> (bool fold)</td></tr>
<tr class="separator:a2d183c40c276dadd3bbb994b0c0f26ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a85c009c5ccf84fc64726bb2c3b11bdec"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a85c009c5ccf84fc64726bb2c3b11bdec">setFoldPreprocessor</a> (bool fold)</td></tr>
<tr class="separator:a85c009c5ccf84fc64726bb2c3b11bdec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_slots_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_slots_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Slots inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a> (int autoindentstyle)</td></tr>
<tr class="separator:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a> (bool eoffill, int style=-1)</td></tr>
<tr class="separator:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a> (const QFont &amp;f, int style=-1)</td></tr>
<tr class="separator:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a9688ce1d302666e492900d3cdfcbbaab"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a9688ce1d302666e492900d3cdfcbbaab">QsciLexerPascal</a> (QObject *parent=0)</td></tr>
<tr class="separator:a9688ce1d302666e492900d3cdfcbbaab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6ec94de07f31c33a6b08c297259e6b01"><td class="memItemLeft" align="right" valign="top"><a id="a6ec94de07f31c33a6b08c297259e6b01"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a6ec94de07f31c33a6b08c297259e6b01">~QsciLexerPascal</a> ()</td></tr>
<tr class="separator:a6ec94de07f31c33a6b08c297259e6b01"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a072c10d35abc0e56e09806eeb78ab66f"><td class="memItemLeft" align="right" valign="top"><a id="a072c10d35abc0e56e09806eeb78ab66f"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a072c10d35abc0e56e09806eeb78ab66f">language</a> () const</td></tr>
<tr class="separator:a072c10d35abc0e56e09806eeb78ab66f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aebc02afb8158d445c4369efa287cc2ac"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#aebc02afb8158d445c4369efa287cc2ac">lexer</a> () const</td></tr>
<tr class="separator:aebc02afb8158d445c4369efa287cc2ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa28fa3e32d5d4a4efccdad6655fb28c8"><td class="memItemLeft" align="right" valign="top">QStringList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#aa28fa3e32d5d4a4efccdad6655fb28c8">autoCompletionWordSeparators</a> () const</td></tr>
<tr class="separator:aa28fa3e32d5d4a4efccdad6655fb28c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9914377426e5e464f6d93ce2b64423a0"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a9914377426e5e464f6d93ce2b64423a0">blockEnd</a> (int *style=0) const</td></tr>
<tr class="separator:a9914377426e5e464f6d93ce2b64423a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a68d8b422b0d733592cc896086ca23652"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a68d8b422b0d733592cc896086ca23652">blockStart</a> (int *style=0) const</td></tr>
<tr class="separator:a68d8b422b0d733592cc896086ca23652"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe045873399199ba05d26e94c0e28aae"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#abe045873399199ba05d26e94c0e28aae">blockStartKeyword</a> (int *style=0) const</td></tr>
<tr class="separator:abe045873399199ba05d26e94c0e28aae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4bd5b007424a8e88db37a326c0f154b5"><td class="memItemLeft" align="right" valign="top"><a id="a4bd5b007424a8e88db37a326c0f154b5"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a4bd5b007424a8e88db37a326c0f154b5">braceStyle</a> () const</td></tr>
<tr class="separator:a4bd5b007424a8e88db37a326c0f154b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad09e8331b90feeab761f845ac80e0b6d"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#ad09e8331b90feeab761f845ac80e0b6d">defaultColor</a> (int style) const</td></tr>
<tr class="separator:ad09e8331b90feeab761f845ac80e0b6d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a45679bbf510fa7e0b264eb9654183f16"><td class="memItemLeft" align="right" valign="top"><a id="a45679bbf510fa7e0b264eb9654183f16"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a45679bbf510fa7e0b264eb9654183f16">defaultEolFill</a> (int style) const</td></tr>
<tr class="separator:a45679bbf510fa7e0b264eb9654183f16"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9c18ede5b5271ee1885b38083271aa9e"><td class="memItemLeft" align="right" valign="top"><a id="a9c18ede5b5271ee1885b38083271aa9e"></a>
QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a9c18ede5b5271ee1885b38083271aa9e">defaultFont</a> (int style) const</td></tr>
<tr class="separator:a9c18ede5b5271ee1885b38083271aa9e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a5b06231766e0f9a7364606a991c879"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a1a5b06231766e0f9a7364606a991c879">defaultPaper</a> (int style) const</td></tr>
<tr class="separator:a1a5b06231766e0f9a7364606a991c879"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b6f6a462314471262e5f29057839b34"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a9b6f6a462314471262e5f29057839b34">keywords</a> (int set) const</td></tr>
<tr class="separator:a9b6f6a462314471262e5f29057839b34"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab47735b5b8b7961044bb9adf111c06bc"><td class="memItemLeft" align="right" valign="top">QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#ab47735b5b8b7961044bb9adf111c06bc">description</a> (int style) const</td></tr>
<tr class="separator:ab47735b5b8b7961044bb9adf111c06bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a92cb96a2f9d373ed5a91546c42ec0905"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a92cb96a2f9d373ed5a91546c42ec0905">refreshProperties</a> ()</td></tr>
<tr class="separator:a92cb96a2f9d373ed5a91546c42ec0905"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a30d1fae97aaef0b3fafab8e790caf130"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a30d1fae97aaef0b3fafab8e790caf130">foldComments</a> () const</td></tr>
<tr class="separator:a30d1fae97aaef0b3fafab8e790caf130"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae88783c3de2f0c4a0129e5bec77cc5ca"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#ae88783c3de2f0c4a0129e5bec77cc5ca">foldCompact</a> () const</td></tr>
<tr class="separator:ae88783c3de2f0c4a0129e5bec77cc5ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adae268febf025354165c88afa2414c73"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#adae268febf025354165c88afa2414c73">foldPreprocessor</a> () const</td></tr>
<tr class="separator:adae268febf025354165c88afa2414c73"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a64f021f45d10f2cfca72fda0c1d28e1f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a64f021f45d10f2cfca72fda0c1d28e1f">setSmartHighlighting</a> (bool enabled)</td></tr>
<tr class="separator:a64f021f45d10f2cfca72fda0c1d28e1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a71fd025ad904aa51a6127f43099805ad"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a71fd025ad904aa51a6127f43099805ad">smartHighlighting</a> () const</td></tr>
<tr class="separator:a71fd025ad904aa51a6127f43099805ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a> (QObject *parent=0)</td></tr>
<tr class="separator:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="af6cc5bb9d9421d806e9941d018030068"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a> ()</td></tr>
<tr class="separator:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a> () const</td></tr>
<tr class="separator:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a> () const</td></tr>
<tr class="separator:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a6504a6fff35af16fbfd97889048db2a5"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a> () const</td></tr>
<tr class="separator:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a> ()</td></tr>
<tr class="separator:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a> () const</td></tr>
<tr class="separator:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="afccca7eb1aed463f89ac442d99135839"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a> () const</td></tr>
<tr class="separator:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a> (int style) const</td></tr>
<tr class="separator:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a> (int style) const</td></tr>
<tr class="separator:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a> (int style) const</td></tr>
<tr class="separator:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="aff4735542e937c5e35ecb2eb82e8f875"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a> () const</td></tr>
<tr class="separator:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a> () const</td></tr>
<tr class="separator:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a> (int style) const</td></tr>
<tr class="separator:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">defaultColor</a> () const</td></tr>
<tr class="separator:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">defaultFont</a> () const</td></tr>
<tr class="separator:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">defaultPaper</a> () const</td></tr>
<tr class="separator:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciScintilla.html">QsciScintilla</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a> () const</td></tr>
<tr class="separator:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a> (<a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *<a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>)</td></tr>
<tr class="separator:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a> (const QFont &amp;f)</td></tr>
<tr class="separator:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a1e81186b1f8f8bc2a4901a42cbca568a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>setEditor</b> (<a class="el" href="classQsciScintilla.html">QsciScintilla</a> *<a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>)</td></tr>
<tr class="separator:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td></tr>
<tr class="separator:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a> () const</td></tr>
<tr class="separator:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aace68e3dbcef9da1b031fb9cfd843c57 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aace68e3dbcef9da1b031fb9cfd843c57">wordCharacters</a> () const</td></tr>
<tr class="separator:aace68e3dbcef9da1b031fb9cfd843c57 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td></tr>
<tr class="separator:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:a2a2beba3b365e2e0e1f21109079f0ffd"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a2a2beba3b365e2e0e1f21109079f0ffd">readProperties</a> (QSettings &amp;qs, const QString &amp;prefix)</td></tr>
<tr class="separator:a2a2beba3b365e2e0e1f21109079f0ffd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6a5b21a2ba8b43a2f6b3747af365156f"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPascal.html#a6a5b21a2ba8b43a2f6b3747af365156f">writeProperties</a> (QSettings &amp;qs, const QString &amp;prefix) const</td></tr>
<tr class="separator:a6a5b21a2ba8b43a2f6b3747af365156f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pro_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a41d4521504d63ee63d43fd7ed0c003ee"></a>
QByteArray&#160;</td><td class="memItemRight" valign="bottom"><b>textAsBytes</b> (const QString &amp;text) const</td></tr>
<tr class="separator:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a5261dd606c209a5c6a494e608a9a111a"></a>
QString&#160;</td><td class="memItemRight" valign="bottom"><b>bytesAsText</b> (const char *bytes, int size) const</td></tr>
<tr class="separator:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header signals_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('signals_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Signals inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a> (bool eolfilled, int style)</td></tr>
<tr class="separator:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a> (const QFont &amp;f, int style)</td></tr>
<tr class="separator:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a> (const char *prop, const char *val)</td></tr>
<tr class="separator:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciLexerPascal.html" title="The QsciLexerPascal class encapsulates the Scintilla Pascal lexer.">QsciLexerPascal</a> class encapsulates the Scintilla Pascal lexer. </p>
</div><h2 class="groupheader">Member Enumeration Documentation</h2>
<a id="a0c7562ea6d7a9d8a794daf47228c22df"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0c7562ea6d7a9d8a794daf47228c22df">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the meanings of the different styles used by the C++ lexer. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a0c7562ea6d7a9d8a794daf47228c22dfa71c490d79223177530ef9415edf52747"></a>Default&#160;</td><td class="fielddoc"><p>The default. </p>
</td></tr>
<tr><td class="fieldname"><a id="a0c7562ea6d7a9d8a794daf47228c22dfa51dbc500bd8cc6a05ca0a04e9220e9b8"></a>Identifier&#160;</td><td class="fielddoc"><p>An identifier. </p>
</td></tr>
<tr><td class="fieldname"><a id="a0c7562ea6d7a9d8a794daf47228c22dfab48837000308dc11499d7e96f302db6a"></a>Comment&#160;</td><td class="fielddoc"><p>A '{ ... }' style comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="a0c7562ea6d7a9d8a794daf47228c22dfabd390815063a649b2cab3f5da6d4b113"></a>CommentParenthesis&#160;</td><td class="fielddoc"><p>A '(* ... *)' style comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="a0c7562ea6d7a9d8a794daf47228c22dfab4771b618f3f481962bc73d7d1e63cc5"></a>CommentLine&#160;</td><td class="fielddoc"><p>A comment line. </p>
</td></tr>
<tr><td class="fieldname"><a id="a0c7562ea6d7a9d8a794daf47228c22dfacfd2039caa846a92af36182615b36777"></a>PreProcessor&#160;</td><td class="fielddoc"><p>A '{$ ... }' style pre-processor block. </p>
</td></tr>
<tr><td class="fieldname"><a id="a0c7562ea6d7a9d8a794daf47228c22dfa414d215a01c3d205a300976cf7f81556"></a>PreProcessorParenthesis&#160;</td><td class="fielddoc"><p>A '(*$ ... *)' style pre-processor block. </p>
</td></tr>
<tr><td class="fieldname"><a id="a0c7562ea6d7a9d8a794daf47228c22dfac3c433f5759c8996553c5e203077fe0d"></a>Number&#160;</td><td class="fielddoc"><p>A number. </p>
</td></tr>
<tr><td class="fieldname"><a id="a0c7562ea6d7a9d8a794daf47228c22dfa12bdf1e4458d0e5aa6ba031eee1c81b2"></a>HexNumber&#160;</td><td class="fielddoc"><p>A hexadecimal number. </p>
</td></tr>
<tr><td class="fieldname"><a id="a0c7562ea6d7a9d8a794daf47228c22dfa0df50778af3ef8ecbd584fca00d5337a"></a>Keyword&#160;</td><td class="fielddoc"><p>A keyword. </p>
</td></tr>
<tr><td class="fieldname"><a id="a0c7562ea6d7a9d8a794daf47228c22dfae325b4d8dbeeb693c7b76b746ee81e81"></a>SingleQuotedString&#160;</td><td class="fielddoc"><p>A single-quoted string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a0c7562ea6d7a9d8a794daf47228c22dfa19098093aa64ac0fdca24cacb30983cf"></a>UnclosedString&#160;</td><td class="fielddoc"><p>The end of a line where a string is not closed. </p>
</td></tr>
<tr><td class="fieldname"><a id="a0c7562ea6d7a9d8a794daf47228c22dfae68a19ab0843b318915c86f7d353590c"></a>Character&#160;</td><td class="fielddoc"><p>A character. </p>
</td></tr>
<tr><td class="fieldname"><a id="a0c7562ea6d7a9d8a794daf47228c22dfae83775614e9f03b1bb41d78023f0121a"></a>Operator&#160;</td><td class="fielddoc"><p>An operator. </p>
</td></tr>
<tr><td class="fieldname"><a id="a0c7562ea6d7a9d8a794daf47228c22dfa03866519c0b3f2113793dead5db53daa"></a>Asm&#160;</td><td class="fielddoc"><p>Inline Asm. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a9688ce1d302666e492900d3cdfcbbaab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9688ce1d302666e492900d3cdfcbbaab">&#9670;&nbsp;</a></span>QsciLexerPascal()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciLexerPascal::QsciLexerPascal </td>
          <td>(</td>
          <td class="paramtype">QObject *&#160;</td>
          <td class="paramname"><em>parent</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Construct a <a class="el" href="classQsciLexerPascal.html" title="The QsciLexerPascal class encapsulates the Scintilla Pascal lexer.">QsciLexerPascal</a> with parent <em>parent</em>. <em>parent</em> is typically the <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> instance. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="aebc02afb8158d445c4369efa287cc2ac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aebc02afb8158d445c4369efa287cc2ac">&#9670;&nbsp;</a></span>lexer()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerPascal::lexer </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the name of the lexer. Some lexers support a number of languages. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ac7c40b97187e23ab85f6d95113f91b39">QsciLexer</a>.</p>

</div>
</div>
<a id="aa28fa3e32d5d4a4efccdad6655fb28c8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa28fa3e32d5d4a4efccdad6655fb28c8">&#9670;&nbsp;</a></span>autoCompletionWordSeparators()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QStringList QsciLexerPascal::autoCompletionWordSeparators </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the list of character sequences that can separate auto-completion words. The first in the list is assumed to be the sequence used to separate words in the lexer's API files. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">QsciLexer</a>.</p>

</div>
</div>
<a id="a9914377426e5e464f6d93ce2b64423a0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9914377426e5e464f6d93ce2b64423a0">&#9670;&nbsp;</a></span>blockEnd()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerPascal::blockEnd </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of words or characters in a particular style that define the end of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">QsciLexer</a>.</p>

</div>
</div>
<a id="a68d8b422b0d733592cc896086ca23652"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a68d8b422b0d733592cc896086ca23652">&#9670;&nbsp;</a></span>blockStart()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerPascal::blockStart </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of words or characters in a particular style that define the start of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">QsciLexer</a>.</p>

</div>
</div>
<a id="abe045873399199ba05d26e94c0e28aae"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abe045873399199ba05d26e94c0e28aae">&#9670;&nbsp;</a></span>blockStartKeyword()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerPascal::blockStartKeyword </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of keywords in a particular style that define the start of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">QsciLexer</a>.</p>

</div>
</div>
<a id="ad09e8331b90feeab761f845ac80e0b6d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad09e8331b90feeab761f845ac80e0b6d">&#9670;&nbsp;</a></span>defaultColor()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerPascal::defaultColor </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the foreground colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPascal.html#a1a5b06231766e0f9a7364606a991c879">defaultPaper()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#af7508f1b816a2c9446d36141edc9b5ce">QsciLexer</a>.</p>

</div>
</div>
<a id="a1a5b06231766e0f9a7364606a991c879"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1a5b06231766e0f9a7364606a991c879">&#9670;&nbsp;</a></span>defaultPaper()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerPascal::defaultPaper </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the background colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPascal.html#ad09e8331b90feeab761f845ac80e0b6d">defaultColor()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a7e5ab7f541d913760c32abedbdc72963">QsciLexer</a>.</p>

</div>
</div>
<a id="a9b6f6a462314471262e5f29057839b34"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9b6f6a462314471262e5f29057839b34">&#9670;&nbsp;</a></span>keywords()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerPascal::keywords </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>set</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the set of keywords for the keyword set <em>set</em> recognised by the lexer as a space separated string. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a013b7c1bf9846e231b97827dfd9540b0">QsciLexer</a>.</p>

</div>
</div>
<a id="ab47735b5b8b7961044bb9adf111c06bc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab47735b5b8b7961044bb9adf111c06bc">&#9670;&nbsp;</a></span>description()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QString QsciLexerPascal::description </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the descriptive name for style number <em>style</em>. If the style is invalid for this language then an empty QString is returned. This is intended to be used in user preference dialogs. </p>

<p>Implements <a class="el" href="classQsciLexer.html#add9c20adb43bc38d1a0ca3083ac3e6fa">QsciLexer</a>.</p>

</div>
</div>
<a id="a92cb96a2f9d373ed5a91546c42ec0905"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a92cb96a2f9d373ed5a91546c42ec0905">&#9670;&nbsp;</a></span>refreshProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerPascal::refreshProperties </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Causes all properties to be refreshed by emitting the <a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged()</a> signal as required. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ae508c3ab4ce1f338dfff3ddf5ee7e34c">QsciLexer</a>.</p>

</div>
</div>
<a id="a30d1fae97aaef0b3fafab8e790caf130"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a30d1fae97aaef0b3fafab8e790caf130">&#9670;&nbsp;</a></span>foldComments()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPascal::foldComments </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if multi-line comment blocks can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPascal.html#a05d880fd1451f6a757fd21a7bd43a358">setFoldComments()</a> </dd></dl>

</div>
</div>
<a id="ae88783c3de2f0c4a0129e5bec77cc5ca"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae88783c3de2f0c4a0129e5bec77cc5ca">&#9670;&nbsp;</a></span>foldCompact()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPascal::foldCompact </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if trailing blank lines are included in a fold block.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPascal.html#a2d183c40c276dadd3bbb994b0c0f26ce">setFoldCompact()</a> </dd></dl>

</div>
</div>
<a id="adae268febf025354165c88afa2414c73"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adae268febf025354165c88afa2414c73">&#9670;&nbsp;</a></span>foldPreprocessor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPascal::foldPreprocessor </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if preprocessor blocks can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPascal.html#a85c009c5ccf84fc64726bb2c3b11bdec">setFoldPreprocessor()</a> </dd></dl>

</div>
</div>
<a id="a64f021f45d10f2cfca72fda0c1d28e1f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a64f021f45d10f2cfca72fda0c1d28e1f">&#9670;&nbsp;</a></span>setSmartHighlighting()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerPascal::setSmartHighlighting </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>enabled</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>enabled</em> is true then some keywords will only be highlighted in an appropriate context (similar to how the Delphi IDE works). The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPascal.html#a71fd025ad904aa51a6127f43099805ad">smartHighlighting()</a> </dd></dl>

</div>
</div>
<a id="a71fd025ad904aa51a6127f43099805ad"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a71fd025ad904aa51a6127f43099805ad">&#9670;&nbsp;</a></span>smartHighlighting()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPascal::smartHighlighting </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if some keywords will only be highlighted in an appropriate context (similar to how the Delphi IDE works).</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPascal.html#a64f021f45d10f2cfca72fda0c1d28e1f">setSmartHighlighting()</a> </dd></dl>

</div>
</div>
<a id="a05d880fd1451f6a757fd21a7bd43a358"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a05d880fd1451f6a757fd21a7bd43a358">&#9670;&nbsp;</a></span>setFoldComments</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerPascal::setFoldComments </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then multi-line comment blocks can be folded. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPascal.html#a30d1fae97aaef0b3fafab8e790caf130">foldComments()</a> </dd></dl>

</div>
</div>
<a id="a2d183c40c276dadd3bbb994b0c0f26ce"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2d183c40c276dadd3bbb994b0c0f26ce">&#9670;&nbsp;</a></span>setFoldCompact</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerPascal::setFoldCompact </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then trailing blank lines are included in a fold block. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPascal.html#ae88783c3de2f0c4a0129e5bec77cc5ca">foldCompact()</a> </dd></dl>

</div>
</div>
<a id="a85c009c5ccf84fc64726bb2c3b11bdec"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a85c009c5ccf84fc64726bb2c3b11bdec">&#9670;&nbsp;</a></span>setFoldPreprocessor</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerPascal::setFoldPreprocessor </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then preprocessor blocks can be folded. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPascal.html#adae268febf025354165c88afa2414c73">foldPreprocessor()</a> </dd></dl>

</div>
</div>
<a id="a2a2beba3b365e2e0e1f21109079f0ffd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2a2beba3b365e2e0e1f21109079f0ffd">&#9670;&nbsp;</a></span>readProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPascal::readProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are read from the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPascal.html#a6a5b21a2ba8b43a2f6b3747af365156f">writeProperties()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ad472b16506a4cbc19634f07aa90f1ea6">QsciLexer</a>.</p>

</div>
</div>
<a id="a6a5b21a2ba8b43a2f6b3747af365156f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6a5b21a2ba8b43a2f6b3747af365156f">&#9670;&nbsp;</a></span>writeProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPascal::writeProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are written to the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPascal.html#a2a2beba3b365e2e0e1f21109079f0ffd">readProperties()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#abccc4e010b724df1a7b5c5f3bce29501">QsciLexer</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
