<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciLexerVHDL Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-slots">Public Slots</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="classQsciLexerVHDL-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciLexerVHDL Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscilexervhdl.h&gt;</code></p>

<p>Inherits <a class="el" href="classQsciLexer.html">QsciLexer</a>.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:aab5145bfdabbf4713c171f037424d300"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300a58b45bf1904760b66784b6193100237b">Default</a> = 0, 
<a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300ac22bd7eac094ca7e6f5ba2b0f65124ad">Comment</a> = 1, 
<a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300a9e1bb162045d720665c7d463e3824476">CommentLine</a> = 2, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300a9b0cba57f797ebaf8d98eda980c171d7">Number</a> = 3, 
<a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300ad62daedcc5bd7ae90562a1f95a982f09">String</a> = 4, 
<a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300ab027e96558ca9bd809cb4032b1aeb1ce">Operator</a> = 5, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300ab72a8603cda3b24dfa6eeed5c6a7ca93">Identifier</a> = 6, 
<a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300af2479b7be15f744baac9ef19fa7bfc7e">UnclosedString</a> = 7, 
<a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300a0e6155fe6a0e10f1301072cb73d5ecc5">Keyword</a> = 8, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300a5aa1000c3189173cae05443b809e1471">StandardOperator</a> = 9, 
<a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300ae259747dcdaff0bffe5da604d93ee4a5">Attribute</a> = 10, 
<a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300ac0e0b6c72ddc65750f5f0e347a212543">StandardFunction</a> = 11, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300a54f69bab09ed1818a5aab51fd3569531">StandardPackage</a> = 12, 
<a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300a341ea56a3223fe36e9d89157c6e3b1d5">StandardType</a> = 13, 
<a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300ab519e29e6205cdeeb66d8d5e1e90a4d0">KeywordSet7</a> = 14, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerVHDL.html#aab5145bfdabbf4713c171f037424d300a4e88c5013c5e1a80ecd777322b07d4ab">CommentBlock</a> = 15
<br />
 }</td></tr>
<tr class="separator:aab5145bfdabbf4713c171f037424d300"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-slots"></a>
Public Slots</h2></td></tr>
<tr class="memitem:af41d62ccd061b840e3eb2e9e2b26d6f5"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#af41d62ccd061b840e3eb2e9e2b26d6f5">setFoldComments</a> (bool fold)</td></tr>
<tr class="separator:af41d62ccd061b840e3eb2e9e2b26d6f5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40d1ca24b672c13e9e7e69add2f5ee42"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#a40d1ca24b672c13e9e7e69add2f5ee42">setFoldCompact</a> (bool fold)</td></tr>
<tr class="separator:a40d1ca24b672c13e9e7e69add2f5ee42"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae8c0599c4eb74db6caa8624bcc416a8b"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#ae8c0599c4eb74db6caa8624bcc416a8b">setFoldAtElse</a> (bool fold)</td></tr>
<tr class="separator:ae8c0599c4eb74db6caa8624bcc416a8b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f8da8d7fe8301cd49926b896bf5e286"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#a7f8da8d7fe8301cd49926b896bf5e286">setFoldAtBegin</a> (bool fold)</td></tr>
<tr class="separator:a7f8da8d7fe8301cd49926b896bf5e286"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad6328325f4c46dce0226712e9db3bba7"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#ad6328325f4c46dce0226712e9db3bba7">setFoldAtParenthesis</a> (bool fold)</td></tr>
<tr class="separator:ad6328325f4c46dce0226712e9db3bba7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_slots_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_slots_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Slots inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a> (int autoindentstyle)</td></tr>
<tr class="separator:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a> (bool eoffill, int style=-1)</td></tr>
<tr class="separator:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a> (const QFont &amp;f, int style=-1)</td></tr>
<tr class="separator:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a2260bd1206a91b7f9487e9ffe366732f"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#a2260bd1206a91b7f9487e9ffe366732f">QsciLexerVHDL</a> (QObject *parent=0)</td></tr>
<tr class="separator:a2260bd1206a91b7f9487e9ffe366732f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab4dd4635d954113eecb698c46395d0b"><td class="memItemLeft" align="right" valign="top"><a id="aab4dd4635d954113eecb698c46395d0b"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#aab4dd4635d954113eecb698c46395d0b">~QsciLexerVHDL</a> ()</td></tr>
<tr class="separator:aab4dd4635d954113eecb698c46395d0b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a8cd041dea81adb54a869c17ee4c8ba"><td class="memItemLeft" align="right" valign="top"><a id="a2a8cd041dea81adb54a869c17ee4c8ba"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#a2a8cd041dea81adb54a869c17ee4c8ba">language</a> () const</td></tr>
<tr class="separator:a2a8cd041dea81adb54a869c17ee4c8ba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf1a8dc25c7bd5d272c119d3c3e9e369"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#abf1a8dc25c7bd5d272c119d3c3e9e369">lexer</a> () const</td></tr>
<tr class="separator:abf1a8dc25c7bd5d272c119d3c3e9e369"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad362dd8a212974c01e61d12c8991b7f"><td class="memItemLeft" align="right" valign="top"><a id="aad362dd8a212974c01e61d12c8991b7f"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#aad362dd8a212974c01e61d12c8991b7f">braceStyle</a> () const</td></tr>
<tr class="separator:aad362dd8a212974c01e61d12c8991b7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6dbcaf590be7759f18699593c95c69e6"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#a6dbcaf590be7759f18699593c95c69e6">defaultColor</a> (int style) const</td></tr>
<tr class="separator:a6dbcaf590be7759f18699593c95c69e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8ab227fcb9ba5da466b2d8eded96af70"><td class="memItemLeft" align="right" valign="top"><a id="a8ab227fcb9ba5da466b2d8eded96af70"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#a8ab227fcb9ba5da466b2d8eded96af70">defaultEolFill</a> (int style) const</td></tr>
<tr class="separator:a8ab227fcb9ba5da466b2d8eded96af70"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ea95f77a5a0ae539b306473c3b808db"><td class="memItemLeft" align="right" valign="top"><a id="a7ea95f77a5a0ae539b306473c3b808db"></a>
QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#a7ea95f77a5a0ae539b306473c3b808db">defaultFont</a> (int style) const</td></tr>
<tr class="separator:a7ea95f77a5a0ae539b306473c3b808db"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a1215dab25c15adf3c1bd6a5b063f91"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#a2a1215dab25c15adf3c1bd6a5b063f91">defaultPaper</a> (int style) const</td></tr>
<tr class="separator:a2a1215dab25c15adf3c1bd6a5b063f91"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed2f3934c2fe336324d6e79526c2f7a8"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#aed2f3934c2fe336324d6e79526c2f7a8">keywords</a> (int set) const</td></tr>
<tr class="separator:aed2f3934c2fe336324d6e79526c2f7a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a367d2a52388bd2602642f4b5dc01bba2"><td class="memItemLeft" align="right" valign="top">QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#a367d2a52388bd2602642f4b5dc01bba2">description</a> (int style) const</td></tr>
<tr class="separator:a367d2a52388bd2602642f4b5dc01bba2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa60e141b7b1a7aac51d79ad2c27c4c93"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#aa60e141b7b1a7aac51d79ad2c27c4c93">refreshProperties</a> ()</td></tr>
<tr class="separator:aa60e141b7b1a7aac51d79ad2c27c4c93"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae2d2e6936f7b0f6f9b891ac14dff7bc0"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#ae2d2e6936f7b0f6f9b891ac14dff7bc0">foldComments</a> () const</td></tr>
<tr class="separator:ae2d2e6936f7b0f6f9b891ac14dff7bc0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a043411367c3fa915c8f4797cc51d0c8c"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#a043411367c3fa915c8f4797cc51d0c8c">foldCompact</a> () const</td></tr>
<tr class="separator:a043411367c3fa915c8f4797cc51d0c8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a46e8e5909bfc92669cf155317ecb6fe9"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#a46e8e5909bfc92669cf155317ecb6fe9">foldAtElse</a> () const</td></tr>
<tr class="separator:a46e8e5909bfc92669cf155317ecb6fe9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6443ca03dcf722445e6627e9991bb10c"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#a6443ca03dcf722445e6627e9991bb10c">foldAtBegin</a> () const</td></tr>
<tr class="separator:a6443ca03dcf722445e6627e9991bb10c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd8eebb9ee14760d1529f614f18a2e52"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#afd8eebb9ee14760d1529f614f18a2e52">foldAtParenthesis</a> () const</td></tr>
<tr class="separator:afd8eebb9ee14760d1529f614f18a2e52"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a> (QObject *parent=0)</td></tr>
<tr class="separator:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="af6cc5bb9d9421d806e9941d018030068"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a> ()</td></tr>
<tr class="separator:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a> () const</td></tr>
<tr class="separator:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a> () const</td></tr>
<tr class="separator:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a6504a6fff35af16fbfd97889048db2a5"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a> () const</td></tr>
<tr class="separator:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e10650b0e9ad137062ad5c17ad33e76 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QStringList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">autoCompletionWordSeparators</a> () const</td></tr>
<tr class="separator:a4e10650b0e9ad137062ad5c17ad33e76 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a> ()</td></tr>
<tr class="separator:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e294eba77713f516acbcebc10af1493 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">blockEnd</a> (int *style=0) const</td></tr>
<tr class="separator:a8e294eba77713f516acbcebc10af1493 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a> () const</td></tr>
<tr class="separator:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a340eafe726fd6964c0adba956fe3428c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">blockStart</a> (int *style=0) const</td></tr>
<tr class="separator:a340eafe726fd6964c0adba956fe3428c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf12117a142b6f68479ea425d80a4196 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a> (int *style=0) const</td></tr>
<tr class="separator:abf12117a142b6f68479ea425d80a4196 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="afccca7eb1aed463f89ac442d99135839"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a> () const</td></tr>
<tr class="separator:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a> (int style) const</td></tr>
<tr class="separator:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a> (int style) const</td></tr>
<tr class="separator:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a> (int style) const</td></tr>
<tr class="separator:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="aff4735542e937c5e35ecb2eb82e8f875"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a> () const</td></tr>
<tr class="separator:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a> () const</td></tr>
<tr class="separator:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a> (int style) const</td></tr>
<tr class="separator:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">defaultColor</a> () const</td></tr>
<tr class="separator:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">defaultFont</a> () const</td></tr>
<tr class="separator:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">defaultPaper</a> () const</td></tr>
<tr class="separator:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciScintilla.html">QsciScintilla</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a> () const</td></tr>
<tr class="separator:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a> (<a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *<a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>)</td></tr>
<tr class="separator:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a> (const QFont &amp;f)</td></tr>
<tr class="separator:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a1e81186b1f8f8bc2a4901a42cbca568a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>setEditor</b> (<a class="el" href="classQsciScintilla.html">QsciScintilla</a> *<a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>)</td></tr>
<tr class="separator:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td></tr>
<tr class="separator:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a> () const</td></tr>
<tr class="separator:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aace68e3dbcef9da1b031fb9cfd843c57 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aace68e3dbcef9da1b031fb9cfd843c57">wordCharacters</a> () const</td></tr>
<tr class="separator:aace68e3dbcef9da1b031fb9cfd843c57 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td></tr>
<tr class="separator:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:a46a01d03d516e909c8696fa3f9910c1f"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#a46a01d03d516e909c8696fa3f9910c1f">readProperties</a> (QSettings &amp;qs, const QString &amp;prefix)</td></tr>
<tr class="separator:a46a01d03d516e909c8696fa3f9910c1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0ed58ff3726deb2215eaff2c1892bc9b"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVHDL.html#a0ed58ff3726deb2215eaff2c1892bc9b">writeProperties</a> (QSettings &amp;qs, const QString &amp;prefix) const</td></tr>
<tr class="separator:a0ed58ff3726deb2215eaff2c1892bc9b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pro_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a41d4521504d63ee63d43fd7ed0c003ee"></a>
QByteArray&#160;</td><td class="memItemRight" valign="bottom"><b>textAsBytes</b> (const QString &amp;text) const</td></tr>
<tr class="separator:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a5261dd606c209a5c6a494e608a9a111a"></a>
QString&#160;</td><td class="memItemRight" valign="bottom"><b>bytesAsText</b> (const char *bytes, int size) const</td></tr>
<tr class="separator:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header signals_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('signals_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Signals inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a> (bool eolfilled, int style)</td></tr>
<tr class="separator:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a> (const QFont &amp;f, int style)</td></tr>
<tr class="separator:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a> (const char *prop, const char *val)</td></tr>
<tr class="separator:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciLexerVHDL.html" title="The QsciLexerVHDL class encapsulates the Scintilla VHDL lexer.">QsciLexerVHDL</a> class encapsulates the Scintilla VHDL lexer. </p>
</div><h2 class="groupheader">Member Enumeration Documentation</h2>
<a id="aab5145bfdabbf4713c171f037424d300"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aab5145bfdabbf4713c171f037424d300">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the meanings of the different styles used by the VHDL lexer. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="aab5145bfdabbf4713c171f037424d300a58b45bf1904760b66784b6193100237b"></a>Default&#160;</td><td class="fielddoc"><p>The default. </p>
</td></tr>
<tr><td class="fieldname"><a id="aab5145bfdabbf4713c171f037424d300ac22bd7eac094ca7e6f5ba2b0f65124ad"></a>Comment&#160;</td><td class="fielddoc"><p>A comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="aab5145bfdabbf4713c171f037424d300a9e1bb162045d720665c7d463e3824476"></a>CommentLine&#160;</td><td class="fielddoc"><p>A comment line. </p>
</td></tr>
<tr><td class="fieldname"><a id="aab5145bfdabbf4713c171f037424d300a9b0cba57f797ebaf8d98eda980c171d7"></a>Number&#160;</td><td class="fielddoc"><p>A number. </p>
</td></tr>
<tr><td class="fieldname"><a id="aab5145bfdabbf4713c171f037424d300ad62daedcc5bd7ae90562a1f95a982f09"></a>String&#160;</td><td class="fielddoc"><p>A string. </p>
</td></tr>
<tr><td class="fieldname"><a id="aab5145bfdabbf4713c171f037424d300ab027e96558ca9bd809cb4032b1aeb1ce"></a>Operator&#160;</td><td class="fielddoc"><p>An operator. </p>
</td></tr>
<tr><td class="fieldname"><a id="aab5145bfdabbf4713c171f037424d300ab72a8603cda3b24dfa6eeed5c6a7ca93"></a>Identifier&#160;</td><td class="fielddoc"><p>An identifier. </p>
</td></tr>
<tr><td class="fieldname"><a id="aab5145bfdabbf4713c171f037424d300af2479b7be15f744baac9ef19fa7bfc7e"></a>UnclosedString&#160;</td><td class="fielddoc"><p>The end of a line where a string is not closed. </p>
</td></tr>
<tr><td class="fieldname"><a id="aab5145bfdabbf4713c171f037424d300a0e6155fe6a0e10f1301072cb73d5ecc5"></a>Keyword&#160;</td><td class="fielddoc"><p>A keyword. </p>
</td></tr>
<tr><td class="fieldname"><a id="aab5145bfdabbf4713c171f037424d300a5aa1000c3189173cae05443b809e1471"></a>StandardOperator&#160;</td><td class="fielddoc"><p>A standard operator. </p>
</td></tr>
<tr><td class="fieldname"><a id="aab5145bfdabbf4713c171f037424d300ae259747dcdaff0bffe5da604d93ee4a5"></a>Attribute&#160;</td><td class="fielddoc"><p>An attribute. </p>
</td></tr>
<tr><td class="fieldname"><a id="aab5145bfdabbf4713c171f037424d300ac0e0b6c72ddc65750f5f0e347a212543"></a>StandardFunction&#160;</td><td class="fielddoc"><p>A standard function. </p>
</td></tr>
<tr><td class="fieldname"><a id="aab5145bfdabbf4713c171f037424d300a54f69bab09ed1818a5aab51fd3569531"></a>StandardPackage&#160;</td><td class="fielddoc"><p>A standard package. </p>
</td></tr>
<tr><td class="fieldname"><a id="aab5145bfdabbf4713c171f037424d300a341ea56a3223fe36e9d89157c6e3b1d5"></a>StandardType&#160;</td><td class="fielddoc"><p>A standard type. </p>
</td></tr>
<tr><td class="fieldname"><a id="aab5145bfdabbf4713c171f037424d300ab519e29e6205cdeeb66d8d5e1e90a4d0"></a>KeywordSet7&#160;</td><td class="fielddoc"><p>A keyword defined in keyword set number 7. The class must be sub-classed and re-implement <a class="el" href="classQsciLexerVHDL.html#aed2f3934c2fe336324d6e79526c2f7a8">keywords()</a> to make use of this style. </p>
</td></tr>
<tr><td class="fieldname"><a id="aab5145bfdabbf4713c171f037424d300a4e88c5013c5e1a80ecd777322b07d4ab"></a>CommentBlock&#160;</td><td class="fielddoc"><p>A comment block. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a2260bd1206a91b7f9487e9ffe366732f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2260bd1206a91b7f9487e9ffe366732f">&#9670;&nbsp;</a></span>QsciLexerVHDL()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciLexerVHDL::QsciLexerVHDL </td>
          <td>(</td>
          <td class="paramtype">QObject *&#160;</td>
          <td class="paramname"><em>parent</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Construct a <a class="el" href="classQsciLexerVHDL.html" title="The QsciLexerVHDL class encapsulates the Scintilla VHDL lexer.">QsciLexerVHDL</a> with parent <em>parent</em>. <em>parent</em> is typically the <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> instance. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="abf1a8dc25c7bd5d272c119d3c3e9e369"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abf1a8dc25c7bd5d272c119d3c3e9e369">&#9670;&nbsp;</a></span>lexer()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerVHDL::lexer </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the name of the lexer. Some lexers support a number of languages. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ac7c40b97187e23ab85f6d95113f91b39">QsciLexer</a>.</p>

</div>
</div>
<a id="a6dbcaf590be7759f18699593c95c69e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6dbcaf590be7759f18699593c95c69e6">&#9670;&nbsp;</a></span>defaultColor()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerVHDL::defaultColor </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the foreground colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVHDL.html#a2a1215dab25c15adf3c1bd6a5b063f91">defaultPaper()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#af7508f1b816a2c9446d36141edc9b5ce">QsciLexer</a>.</p>

</div>
</div>
<a id="a2a1215dab25c15adf3c1bd6a5b063f91"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2a1215dab25c15adf3c1bd6a5b063f91">&#9670;&nbsp;</a></span>defaultPaper()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerVHDL::defaultPaper </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the background colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVHDL.html#a6dbcaf590be7759f18699593c95c69e6">defaultColor()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a7e5ab7f541d913760c32abedbdc72963">QsciLexer</a>.</p>

</div>
</div>
<a id="aed2f3934c2fe336324d6e79526c2f7a8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aed2f3934c2fe336324d6e79526c2f7a8">&#9670;&nbsp;</a></span>keywords()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerVHDL::keywords </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>set</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the set of keywords for the keyword set <em>set</em> recognised by the lexer as a space separated string. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a013b7c1bf9846e231b97827dfd9540b0">QsciLexer</a>.</p>

</div>
</div>
<a id="a367d2a52388bd2602642f4b5dc01bba2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a367d2a52388bd2602642f4b5dc01bba2">&#9670;&nbsp;</a></span>description()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QString QsciLexerVHDL::description </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the descriptive name for style number <em>style</em>. If the style is invalid for this language then an empty QString is returned. This is intended to be used in user preference dialogs. </p>

<p>Implements <a class="el" href="classQsciLexer.html#add9c20adb43bc38d1a0ca3083ac3e6fa">QsciLexer</a>.</p>

</div>
</div>
<a id="aa60e141b7b1a7aac51d79ad2c27c4c93"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa60e141b7b1a7aac51d79ad2c27c4c93">&#9670;&nbsp;</a></span>refreshProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerVHDL::refreshProperties </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Causes all properties to be refreshed by emitting the <a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged()</a> signal as required. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ae508c3ab4ce1f338dfff3ddf5ee7e34c">QsciLexer</a>.</p>

</div>
</div>
<a id="ae2d2e6936f7b0f6f9b891ac14dff7bc0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae2d2e6936f7b0f6f9b891ac14dff7bc0">&#9670;&nbsp;</a></span>foldComments()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerVHDL::foldComments </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if multi-line comment blocks can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVHDL.html#af41d62ccd061b840e3eb2e9e2b26d6f5">setFoldComments()</a> </dd></dl>

</div>
</div>
<a id="a043411367c3fa915c8f4797cc51d0c8c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a043411367c3fa915c8f4797cc51d0c8c">&#9670;&nbsp;</a></span>foldCompact()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerVHDL::foldCompact </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if trailing blank lines are included in a fold block.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVHDL.html#a40d1ca24b672c13e9e7e69add2f5ee42">setFoldCompact()</a> </dd></dl>

</div>
</div>
<a id="a46e8e5909bfc92669cf155317ecb6fe9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a46e8e5909bfc92669cf155317ecb6fe9">&#9670;&nbsp;</a></span>foldAtElse()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerVHDL::foldAtElse </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if else blocks can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVHDL.html#ae8c0599c4eb74db6caa8624bcc416a8b">setFoldAtElse()</a> </dd></dl>

</div>
</div>
<a id="a6443ca03dcf722445e6627e9991bb10c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6443ca03dcf722445e6627e9991bb10c">&#9670;&nbsp;</a></span>foldAtBegin()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerVHDL::foldAtBegin </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if begin blocks can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVHDL.html#a7f8da8d7fe8301cd49926b896bf5e286">setFoldAtBegin()</a> </dd></dl>

</div>
</div>
<a id="afd8eebb9ee14760d1529f614f18a2e52"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afd8eebb9ee14760d1529f614f18a2e52">&#9670;&nbsp;</a></span>foldAtParenthesis()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerVHDL::foldAtParenthesis </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns true if blocks can be folded at a parenthesis.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVHDL.html#ad6328325f4c46dce0226712e9db3bba7">setFoldAtParenthesis()</a> </dd></dl>

</div>
</div>
<a id="af41d62ccd061b840e3eb2e9e2b26d6f5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af41d62ccd061b840e3eb2e9e2b26d6f5">&#9670;&nbsp;</a></span>setFoldComments</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerVHDL::setFoldComments </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then multi-line comment blocks can be folded. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVHDL.html#ae2d2e6936f7b0f6f9b891ac14dff7bc0">foldComments()</a> </dd></dl>

</div>
</div>
<a id="a40d1ca24b672c13e9e7e69add2f5ee42"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a40d1ca24b672c13e9e7e69add2f5ee42">&#9670;&nbsp;</a></span>setFoldCompact</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerVHDL::setFoldCompact </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then trailing blank lines are included in a fold block. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVHDL.html#a043411367c3fa915c8f4797cc51d0c8c">foldCompact()</a> </dd></dl>

</div>
</div>
<a id="ae8c0599c4eb74db6caa8624bcc416a8b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae8c0599c4eb74db6caa8624bcc416a8b">&#9670;&nbsp;</a></span>setFoldAtElse</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerVHDL::setFoldAtElse </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then else blocks can be folded. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVHDL.html#a46e8e5909bfc92669cf155317ecb6fe9">foldAtElse()</a> </dd></dl>

</div>
</div>
<a id="a7f8da8d7fe8301cd49926b896bf5e286"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7f8da8d7fe8301cd49926b896bf5e286">&#9670;&nbsp;</a></span>setFoldAtBegin</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerVHDL::setFoldAtBegin </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then begin blocks can be folded. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVHDL.html#a6443ca03dcf722445e6627e9991bb10c">foldAtBegin()</a> </dd></dl>

</div>
</div>
<a id="ad6328325f4c46dce0226712e9db3bba7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad6328325f4c46dce0226712e9db3bba7">&#9670;&nbsp;</a></span>setFoldAtParenthesis</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerVHDL::setFoldAtParenthesis </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then blocks can be folded at a parenthesis. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVHDL.html#afd8eebb9ee14760d1529f614f18a2e52">foldAtParenthesis()</a> </dd></dl>

</div>
</div>
<a id="a46a01d03d516e909c8696fa3f9910c1f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a46a01d03d516e909c8696fa3f9910c1f">&#9670;&nbsp;</a></span>readProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerVHDL::readProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are read from the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ad472b16506a4cbc19634f07aa90f1ea6">QsciLexer</a>.</p>

</div>
</div>
<a id="a0ed58ff3726deb2215eaff2c1892bc9b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0ed58ff3726deb2215eaff2c1892bc9b">&#9670;&nbsp;</a></span>writeProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerVHDL::writeProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are written to the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#abccc4e010b724df1a7b5c5f3bce29501">QsciLexer</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
