<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciLexerSQL Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">autoCompletionWordSeparators</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#abf07dc83c19a3925e3cb977bf883b04c">backslashEscapes</a>() const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">blockEnd</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">blockStart</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#ac97e486c8c1f2233c0b35e744ef5a393">braceStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bytesAsText</b>(const char *bytes, int size) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a017c8dd95b8abe00000ef18a3af7cc1f">Comment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a129874afa8759225a097854ebd2af353">CommentDoc</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a6108257694cfbb092d132383f517ea99">CommentDocKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a1a7d323994693912a361e2f2f6c5e88e">CommentDocKeywordError</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a150cbe6dc7ab6815e15c0b45d5209032">CommentLine</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82aaa8e45cf7c33cc5498e4f78cbd946585">CommentLineHash</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a38347f6c3056908532db562ca232971c">Default</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#a830b832b87182332b9dbaa0a69c6a145">defaultColor</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">QsciLexer::defaultColor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#a8c0952bb621cdf048b00191674824a87">defaultEolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#a4272087bb0000cf8fd5dfa17a9b71383">defaultFont</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">QsciLexer::defaultFont</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#a5668132073d8c3d97ea56dc7131c2def">defaultPaper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">QsciLexer::defaultPaper</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#a5b2c0f0e93a1e35b0fb42f2dc1abea29">description</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#a4f0a73894fc542ffc420113046c82f41">dottedWords</a>() const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a6be60f1a8d6511e543462e9d65ee806e">DoubleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a>(bool eolfilled, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#a4cf0c0ab9cb0628c515910c67fab9950">foldAtElse</a>() const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#add132f5762831171fdee856172a0a5dc">foldComments</a>() const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#a1d73603ec19f317dd0d6271ec852c0fc">foldCompact</a>() const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#a9a7c5fb256df97053fbe3203aaf3a93a">foldOnlyBegin</a>() const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a>(const QFont &amp;f, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#a24d7e487c0493f7164cb5bcce51d403d">hashComments</a>() const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a65248832536a73057c5ff9c1b4109ef7">Identifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a3bdb3154d0b6e8fdc9c1ec46c6da29f9">Keyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#ac74a6288e07e20f18ad04e900b48851b">keywords</a>(int set) const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82ae83b44ed53686acc7e65d1336901ca8d">KeywordSet5</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a964752ac32b2980192e27552fffd4b12">KeywordSet6</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a82c5dbd57b06e88f195eb7eefb1f6e32">KeywordSet7</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a76e2eea32f91918b7a5c330284dfae2d">KeywordSet8</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#a0b1959541108a437dcb0b104a46f1444">language</a>() const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#abd8d636e4717ed65e4ea77eca3c28df1">lexer</a>() const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a59137622ad830ab0474796e475df4f29">Number</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a910cc4182b94906c29f7764382c0458e">Operator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a18673427116b1edbb055fe5ee7df8016">PlusComment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a52cefb7860ec4c58e77b235075b7d03b">PlusKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a535f8aef24004cc85bda1a8dfda7d0dd">PlusPrompt</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a>(const char *prop, const char *val)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#ab86225b96219799a77f77600f145042a">QsciLexerSQL</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a5f3f93632cd25dfa0a0349f7aa0927a5">QuotedIdentifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#ad5f7fc89705dd0588937b1565a6e5a26">quotedIdentifiers</a>() const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a5777f32072b41fa90018fbeff82b3ef1">QuotedOperator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#a377b83523f800cc4598126417d80f74c">readProperties</a>(QSettings &amp;qs, const QString &amp;prefix)</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#a9d05744ee6d4c653a7e3976d9f71df23">refreshProperties</a>()</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a>(QsciAbstractAPIs *apis)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a>(int autoindentstyle)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#ab64e768ab8e7af6af93ce95db074c90a">setBackslashEscapes</a>(bool enable)</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a>(const QFont &amp;f)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#aba150bef5f977fb65d66fcaec9c6664c">setDottedWords</a>(bool enable)</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>setEditor</b>(QsciScintilla *editor) (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a>(bool eoffill, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#a35dfbbd04762b0450232c14862ec3ea6">setFoldAtElse</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#a6efb8e98287c21ec5175a466d7e5cc55">setFoldComments</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#a302b9b881fdc5dca82c5dea5fca5cd3e">setFoldCompact</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#a680cba1b994603e73da00610e81debfe">setFoldOnlyBegin</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a>(const QFont &amp;f, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#acc91bd455ff72d93d0bb73b553afbbb8">setHashComments</a>(bool enable)</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae6e5819a3ddec15ac6926b5e19927bff">setQuotedIdentifiers</a>(bool enable)</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a03b0ae83ccbc6a4f885418d25b4ace87">SingleQuotedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>textAsBytes</b>(const QString &amp;text) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aace68e3dbcef9da1b031fb9cfd843c57">wordCharacters</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerSQL.html#a338a09c79011b57a842c581aa2556b4c">writeProperties</a>(QSettings &amp;qs, const QString &amp;prefix) const</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerSQL.html#a0afd856aa4add375643659eace2238fa">~QsciLexerSQL</a>()</td><td class="entry"><a class="el" href="classQsciLexerSQL.html">QsciLexerSQL</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
