<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciLexerSQL Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-slots">Public Slots</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="classQsciLexerSQL-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciLexerSQL Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscilexersql.h&gt;</code></p>

<p>Inherits <a class="el" href="classQsciLexer.html">QsciLexer</a>.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:ae179714d1deeef75b6e08081bc223f82"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a38347f6c3056908532db562ca232971c">Default</a> = 0, 
<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a017c8dd95b8abe00000ef18a3af7cc1f">Comment</a> = 1, 
<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a150cbe6dc7ab6815e15c0b45d5209032">CommentLine</a> = 2, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a129874afa8759225a097854ebd2af353">CommentDoc</a> = 3, 
<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a59137622ad830ab0474796e475df4f29">Number</a> = 4, 
<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a3bdb3154d0b6e8fdc9c1ec46c6da29f9">Keyword</a> = 5, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a6be60f1a8d6511e543462e9d65ee806e">DoubleQuotedString</a> = 6, 
<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a03b0ae83ccbc6a4f885418d25b4ace87">SingleQuotedString</a> = 7, 
<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a52cefb7860ec4c58e77b235075b7d03b">PlusKeyword</a> = 8, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a535f8aef24004cc85bda1a8dfda7d0dd">PlusPrompt</a> = 9, 
<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a910cc4182b94906c29f7764382c0458e">Operator</a> = 10, 
<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a65248832536a73057c5ff9c1b4109ef7">Identifier</a> = 11, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a18673427116b1edbb055fe5ee7df8016">PlusComment</a> = 13, 
<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82aaa8e45cf7c33cc5498e4f78cbd946585">CommentLineHash</a> = 15, 
<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a6108257694cfbb092d132383f517ea99">CommentDocKeyword</a> = 17, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a1a7d323994693912a361e2f2f6c5e88e">CommentDocKeywordError</a> = 18, 
<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82ae83b44ed53686acc7e65d1336901ca8d">KeywordSet5</a> = 19, 
<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a964752ac32b2980192e27552fffd4b12">KeywordSet6</a> = 20, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a82c5dbd57b06e88f195eb7eefb1f6e32">KeywordSet7</a> = 21, 
<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a76e2eea32f91918b7a5c330284dfae2d">KeywordSet8</a> = 22, 
<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a5f3f93632cd25dfa0a0349f7aa0927a5">QuotedIdentifier</a> = 23, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerSQL.html#ae179714d1deeef75b6e08081bc223f82a5777f32072b41fa90018fbeff82b3ef1">QuotedOperator</a> = 24
<br />
 }</td></tr>
<tr class="separator:ae179714d1deeef75b6e08081bc223f82"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-slots"></a>
Public Slots</h2></td></tr>
<tr class="memitem:ab64e768ab8e7af6af93ce95db074c90a"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#ab64e768ab8e7af6af93ce95db074c90a">setBackslashEscapes</a> (bool enable)</td></tr>
<tr class="separator:ab64e768ab8e7af6af93ce95db074c90a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6efb8e98287c21ec5175a466d7e5cc55"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a6efb8e98287c21ec5175a466d7e5cc55">setFoldComments</a> (bool fold)</td></tr>
<tr class="separator:a6efb8e98287c21ec5175a466d7e5cc55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a302b9b881fdc5dca82c5dea5fca5cd3e"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a302b9b881fdc5dca82c5dea5fca5cd3e">setFoldCompact</a> (bool fold)</td></tr>
<tr class="separator:a302b9b881fdc5dca82c5dea5fca5cd3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_slots_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_slots_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Slots inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a> (int autoindentstyle)</td></tr>
<tr class="separator:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a> (bool eoffill, int style=-1)</td></tr>
<tr class="separator:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a> (const QFont &amp;f, int style=-1)</td></tr>
<tr class="separator:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ab86225b96219799a77f77600f145042a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#ab86225b96219799a77f77600f145042a">QsciLexerSQL</a> (QObject *parent=0)</td></tr>
<tr class="separator:ab86225b96219799a77f77600f145042a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0afd856aa4add375643659eace2238fa"><td class="memItemLeft" align="right" valign="top"><a id="a0afd856aa4add375643659eace2238fa"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a0afd856aa4add375643659eace2238fa">~QsciLexerSQL</a> ()</td></tr>
<tr class="separator:a0afd856aa4add375643659eace2238fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b1959541108a437dcb0b104a46f1444"><td class="memItemLeft" align="right" valign="top"><a id="a0b1959541108a437dcb0b104a46f1444"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a0b1959541108a437dcb0b104a46f1444">language</a> () const</td></tr>
<tr class="separator:a0b1959541108a437dcb0b104a46f1444"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd8d636e4717ed65e4ea77eca3c28df1"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#abd8d636e4717ed65e4ea77eca3c28df1">lexer</a> () const</td></tr>
<tr class="separator:abd8d636e4717ed65e4ea77eca3c28df1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac97e486c8c1f2233c0b35e744ef5a393"><td class="memItemLeft" align="right" valign="top"><a id="ac97e486c8c1f2233c0b35e744ef5a393"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#ac97e486c8c1f2233c0b35e744ef5a393">braceStyle</a> () const</td></tr>
<tr class="separator:ac97e486c8c1f2233c0b35e744ef5a393"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a830b832b87182332b9dbaa0a69c6a145"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a830b832b87182332b9dbaa0a69c6a145">defaultColor</a> (int style) const</td></tr>
<tr class="separator:a830b832b87182332b9dbaa0a69c6a145"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8c0952bb621cdf048b00191674824a87"><td class="memItemLeft" align="right" valign="top"><a id="a8c0952bb621cdf048b00191674824a87"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a8c0952bb621cdf048b00191674824a87">defaultEolFill</a> (int style) const</td></tr>
<tr class="separator:a8c0952bb621cdf048b00191674824a87"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4272087bb0000cf8fd5dfa17a9b71383"><td class="memItemLeft" align="right" valign="top"><a id="a4272087bb0000cf8fd5dfa17a9b71383"></a>
QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a4272087bb0000cf8fd5dfa17a9b71383">defaultFont</a> (int style) const</td></tr>
<tr class="separator:a4272087bb0000cf8fd5dfa17a9b71383"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5668132073d8c3d97ea56dc7131c2def"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a5668132073d8c3d97ea56dc7131c2def">defaultPaper</a> (int style) const</td></tr>
<tr class="separator:a5668132073d8c3d97ea56dc7131c2def"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac74a6288e07e20f18ad04e900b48851b"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#ac74a6288e07e20f18ad04e900b48851b">keywords</a> (int set) const</td></tr>
<tr class="separator:ac74a6288e07e20f18ad04e900b48851b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5b2c0f0e93a1e35b0fb42f2dc1abea29"><td class="memItemLeft" align="right" valign="top">QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a5b2c0f0e93a1e35b0fb42f2dc1abea29">description</a> (int style) const</td></tr>
<tr class="separator:a5b2c0f0e93a1e35b0fb42f2dc1abea29"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9d05744ee6d4c653a7e3976d9f71df23"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a9d05744ee6d4c653a7e3976d9f71df23">refreshProperties</a> ()</td></tr>
<tr class="separator:a9d05744ee6d4c653a7e3976d9f71df23"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf07dc83c19a3925e3cb977bf883b04c"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#abf07dc83c19a3925e3cb977bf883b04c">backslashEscapes</a> () const</td></tr>
<tr class="separator:abf07dc83c19a3925e3cb977bf883b04c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba150bef5f977fb65d66fcaec9c6664c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#aba150bef5f977fb65d66fcaec9c6664c">setDottedWords</a> (bool enable)</td></tr>
<tr class="separator:aba150bef5f977fb65d66fcaec9c6664c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f0a73894fc542ffc420113046c82f41"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a4f0a73894fc542ffc420113046c82f41">dottedWords</a> () const</td></tr>
<tr class="separator:a4f0a73894fc542ffc420113046c82f41"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a35dfbbd04762b0450232c14862ec3ea6"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a35dfbbd04762b0450232c14862ec3ea6">setFoldAtElse</a> (bool fold)</td></tr>
<tr class="separator:a35dfbbd04762b0450232c14862ec3ea6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4cf0c0ab9cb0628c515910c67fab9950"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a4cf0c0ab9cb0628c515910c67fab9950">foldAtElse</a> () const</td></tr>
<tr class="separator:a4cf0c0ab9cb0628c515910c67fab9950"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add132f5762831171fdee856172a0a5dc"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#add132f5762831171fdee856172a0a5dc">foldComments</a> () const</td></tr>
<tr class="separator:add132f5762831171fdee856172a0a5dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1d73603ec19f317dd0d6271ec852c0fc"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a1d73603ec19f317dd0d6271ec852c0fc">foldCompact</a> () const</td></tr>
<tr class="separator:a1d73603ec19f317dd0d6271ec852c0fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a680cba1b994603e73da00610e81debfe"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a680cba1b994603e73da00610e81debfe">setFoldOnlyBegin</a> (bool fold)</td></tr>
<tr class="separator:a680cba1b994603e73da00610e81debfe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9a7c5fb256df97053fbe3203aaf3a93a"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a9a7c5fb256df97053fbe3203aaf3a93a">foldOnlyBegin</a> () const</td></tr>
<tr class="separator:a9a7c5fb256df97053fbe3203aaf3a93a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acc91bd455ff72d93d0bb73b553afbbb8"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#acc91bd455ff72d93d0bb73b553afbbb8">setHashComments</a> (bool enable)</td></tr>
<tr class="separator:acc91bd455ff72d93d0bb73b553afbbb8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a24d7e487c0493f7164cb5bcce51d403d"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a24d7e487c0493f7164cb5bcce51d403d">hashComments</a> () const</td></tr>
<tr class="separator:a24d7e487c0493f7164cb5bcce51d403d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6e5819a3ddec15ac6926b5e19927bff"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#ae6e5819a3ddec15ac6926b5e19927bff">setQuotedIdentifiers</a> (bool enable)</td></tr>
<tr class="separator:ae6e5819a3ddec15ac6926b5e19927bff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad5f7fc89705dd0588937b1565a6e5a26"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#ad5f7fc89705dd0588937b1565a6e5a26">quotedIdentifiers</a> () const</td></tr>
<tr class="separator:ad5f7fc89705dd0588937b1565a6e5a26"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a> (QObject *parent=0)</td></tr>
<tr class="separator:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="af6cc5bb9d9421d806e9941d018030068"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a> ()</td></tr>
<tr class="separator:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a> () const</td></tr>
<tr class="separator:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a> () const</td></tr>
<tr class="separator:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a6504a6fff35af16fbfd97889048db2a5"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a> () const</td></tr>
<tr class="separator:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e10650b0e9ad137062ad5c17ad33e76 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QStringList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">autoCompletionWordSeparators</a> () const</td></tr>
<tr class="separator:a4e10650b0e9ad137062ad5c17ad33e76 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a> ()</td></tr>
<tr class="separator:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e294eba77713f516acbcebc10af1493 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">blockEnd</a> (int *style=0) const</td></tr>
<tr class="separator:a8e294eba77713f516acbcebc10af1493 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a> () const</td></tr>
<tr class="separator:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a340eafe726fd6964c0adba956fe3428c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">blockStart</a> (int *style=0) const</td></tr>
<tr class="separator:a340eafe726fd6964c0adba956fe3428c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf12117a142b6f68479ea425d80a4196 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a> (int *style=0) const</td></tr>
<tr class="separator:abf12117a142b6f68479ea425d80a4196 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="afccca7eb1aed463f89ac442d99135839"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a> () const</td></tr>
<tr class="separator:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a> (int style) const</td></tr>
<tr class="separator:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a> (int style) const</td></tr>
<tr class="separator:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a> (int style) const</td></tr>
<tr class="separator:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="aff4735542e937c5e35ecb2eb82e8f875"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a> () const</td></tr>
<tr class="separator:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a> () const</td></tr>
<tr class="separator:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a> (int style) const</td></tr>
<tr class="separator:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">defaultColor</a> () const</td></tr>
<tr class="separator:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">defaultFont</a> () const</td></tr>
<tr class="separator:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">defaultPaper</a> () const</td></tr>
<tr class="separator:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciScintilla.html">QsciScintilla</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a> () const</td></tr>
<tr class="separator:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a> (<a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *<a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>)</td></tr>
<tr class="separator:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a> (const QFont &amp;f)</td></tr>
<tr class="separator:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a1e81186b1f8f8bc2a4901a42cbca568a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>setEditor</b> (<a class="el" href="classQsciScintilla.html">QsciScintilla</a> *<a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>)</td></tr>
<tr class="separator:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td></tr>
<tr class="separator:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a> () const</td></tr>
<tr class="separator:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aace68e3dbcef9da1b031fb9cfd843c57 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aace68e3dbcef9da1b031fb9cfd843c57">wordCharacters</a> () const</td></tr>
<tr class="separator:aace68e3dbcef9da1b031fb9cfd843c57 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td></tr>
<tr class="separator:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:a377b83523f800cc4598126417d80f74c"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a377b83523f800cc4598126417d80f74c">readProperties</a> (QSettings &amp;qs, const QString &amp;prefix)</td></tr>
<tr class="separator:a377b83523f800cc4598126417d80f74c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a338a09c79011b57a842c581aa2556b4c"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerSQL.html#a338a09c79011b57a842c581aa2556b4c">writeProperties</a> (QSettings &amp;qs, const QString &amp;prefix) const</td></tr>
<tr class="separator:a338a09c79011b57a842c581aa2556b4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pro_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a41d4521504d63ee63d43fd7ed0c003ee"></a>
QByteArray&#160;</td><td class="memItemRight" valign="bottom"><b>textAsBytes</b> (const QString &amp;text) const</td></tr>
<tr class="separator:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a5261dd606c209a5c6a494e608a9a111a"></a>
QString&#160;</td><td class="memItemRight" valign="bottom"><b>bytesAsText</b> (const char *bytes, int size) const</td></tr>
<tr class="separator:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header signals_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('signals_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Signals inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a> (bool eolfilled, int style)</td></tr>
<tr class="separator:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a> (const QFont &amp;f, int style)</td></tr>
<tr class="separator:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a> (const char *prop, const char *val)</td></tr>
<tr class="separator:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciLexerSQL.html" title="The QsciLexerSQL class encapsulates the Scintilla SQL lexer.">QsciLexerSQL</a> class encapsulates the Scintilla SQL lexer. </p>
</div><h2 class="groupheader">Member Enumeration Documentation</h2>
<a id="ae179714d1deeef75b6e08081bc223f82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae179714d1deeef75b6e08081bc223f82">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the meanings of the different styles used by the SQL lexer. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a38347f6c3056908532db562ca232971c"></a>Default&#160;</td><td class="fielddoc"><p>The default. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a017c8dd95b8abe00000ef18a3af7cc1f"></a>Comment&#160;</td><td class="fielddoc"><p>A comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a150cbe6dc7ab6815e15c0b45d5209032"></a>CommentLine&#160;</td><td class="fielddoc"><p>A line comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a129874afa8759225a097854ebd2af353"></a>CommentDoc&#160;</td><td class="fielddoc"><p>A JavaDoc/Doxygen style comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a59137622ad830ab0474796e475df4f29"></a>Number&#160;</td><td class="fielddoc"><p>A number. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a3bdb3154d0b6e8fdc9c1ec46c6da29f9"></a>Keyword&#160;</td><td class="fielddoc"><p>A keyword. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a6be60f1a8d6511e543462e9d65ee806e"></a>DoubleQuotedString&#160;</td><td class="fielddoc"><p>A double-quoted string. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a03b0ae83ccbc6a4f885418d25b4ace87"></a>SingleQuotedString&#160;</td><td class="fielddoc"><p>A single-quoted string. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a52cefb7860ec4c58e77b235075b7d03b"></a>PlusKeyword&#160;</td><td class="fielddoc"><p>An SQL*Plus keyword. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a535f8aef24004cc85bda1a8dfda7d0dd"></a>PlusPrompt&#160;</td><td class="fielddoc"><p>An SQL*Plus prompt. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a910cc4182b94906c29f7764382c0458e"></a>Operator&#160;</td><td class="fielddoc"><p>An operator. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a65248832536a73057c5ff9c1b4109ef7"></a>Identifier&#160;</td><td class="fielddoc"><p>An identifier. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a18673427116b1edbb055fe5ee7df8016"></a>PlusComment&#160;</td><td class="fielddoc"><p>An SQL*Plus comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82aaa8e45cf7c33cc5498e4f78cbd946585"></a>CommentLineHash&#160;</td><td class="fielddoc"><p>A '#' line comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a6108257694cfbb092d132383f517ea99"></a>CommentDocKeyword&#160;</td><td class="fielddoc"><p>A JavaDoc/Doxygen keyword. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a1a7d323994693912a361e2f2f6c5e88e"></a>CommentDocKeywordError&#160;</td><td class="fielddoc"><p>A JavaDoc/Doxygen keyword error. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82ae83b44ed53686acc7e65d1336901ca8d"></a>KeywordSet5&#160;</td><td class="fielddoc"><p>A keyword defined in keyword set number 5. The class must be sub-classed and re-implement <a class="el" href="classQsciLexerSQL.html#ac74a6288e07e20f18ad04e900b48851b">keywords()</a> to make use of this style. Note that keywords must be defined using lower case. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a964752ac32b2980192e27552fffd4b12"></a>KeywordSet6&#160;</td><td class="fielddoc"><p>A keyword defined in keyword set number 6. The class must be sub-classed and re-implement <a class="el" href="classQsciLexerSQL.html#ac74a6288e07e20f18ad04e900b48851b">keywords()</a> to make use of this style. Note that keywords must be defined using lower case. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a82c5dbd57b06e88f195eb7eefb1f6e32"></a>KeywordSet7&#160;</td><td class="fielddoc"><p>A keyword defined in keyword set number 7. The class must be sub-classed and re-implement <a class="el" href="classQsciLexerSQL.html#ac74a6288e07e20f18ad04e900b48851b">keywords()</a> to make use of this style. Note that keywords must be defined using lower case. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a76e2eea32f91918b7a5c330284dfae2d"></a>KeywordSet8&#160;</td><td class="fielddoc"><p>A keyword defined in keyword set number 8. The class must be sub-classed and re-implement <a class="el" href="classQsciLexerSQL.html#ac74a6288e07e20f18ad04e900b48851b">keywords()</a> to make use of this style. Note that keywords must be defined using lower case. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a5f3f93632cd25dfa0a0349f7aa0927a5"></a>QuotedIdentifier&#160;</td><td class="fielddoc"><p>A quoted identifier. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae179714d1deeef75b6e08081bc223f82a5777f32072b41fa90018fbeff82b3ef1"></a>QuotedOperator&#160;</td><td class="fielddoc"><p>A quoted operator. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="ab86225b96219799a77f77600f145042a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab86225b96219799a77f77600f145042a">&#9670;&nbsp;</a></span>QsciLexerSQL()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciLexerSQL::QsciLexerSQL </td>
          <td>(</td>
          <td class="paramtype">QObject *&#160;</td>
          <td class="paramname"><em>parent</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Construct a <a class="el" href="classQsciLexerSQL.html" title="The QsciLexerSQL class encapsulates the Scintilla SQL lexer.">QsciLexerSQL</a> with parent <em>parent</em>. <em>parent</em> is typically the <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> instance. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="abd8d636e4717ed65e4ea77eca3c28df1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abd8d636e4717ed65e4ea77eca3c28df1">&#9670;&nbsp;</a></span>lexer()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerSQL::lexer </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the name of the lexer. Some lexers support a number of languages. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ac7c40b97187e23ab85f6d95113f91b39">QsciLexer</a>.</p>

</div>
</div>
<a id="a830b832b87182332b9dbaa0a69c6a145"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a830b832b87182332b9dbaa0a69c6a145">&#9670;&nbsp;</a></span>defaultColor()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerSQL::defaultColor </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the foreground colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#a5668132073d8c3d97ea56dc7131c2def">defaultPaper()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#af7508f1b816a2c9446d36141edc9b5ce">QsciLexer</a>.</p>

</div>
</div>
<a id="a5668132073d8c3d97ea56dc7131c2def"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5668132073d8c3d97ea56dc7131c2def">&#9670;&nbsp;</a></span>defaultPaper()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerSQL::defaultPaper </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the background colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#a830b832b87182332b9dbaa0a69c6a145">defaultColor()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a7e5ab7f541d913760c32abedbdc72963">QsciLexer</a>.</p>

</div>
</div>
<a id="ac74a6288e07e20f18ad04e900b48851b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac74a6288e07e20f18ad04e900b48851b">&#9670;&nbsp;</a></span>keywords()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerSQL::keywords </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>set</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the set of keywords for the keyword set <em>set</em> recognised by the lexer as a space separated string. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a013b7c1bf9846e231b97827dfd9540b0">QsciLexer</a>.</p>

</div>
</div>
<a id="a5b2c0f0e93a1e35b0fb42f2dc1abea29"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5b2c0f0e93a1e35b0fb42f2dc1abea29">&#9670;&nbsp;</a></span>description()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QString QsciLexerSQL::description </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the descriptive name for style number <em>style</em>. If the style is invalid for this language then an empty QString is returned. This is intended to be used in user preference dialogs. </p>

<p>Implements <a class="el" href="classQsciLexer.html#add9c20adb43bc38d1a0ca3083ac3e6fa">QsciLexer</a>.</p>

</div>
</div>
<a id="a9d05744ee6d4c653a7e3976d9f71df23"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9d05744ee6d4c653a7e3976d9f71df23">&#9670;&nbsp;</a></span>refreshProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerSQL::refreshProperties </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Causes all properties to be refreshed by emitting the <a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged()</a> signal as required. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ae508c3ab4ce1f338dfff3ddf5ee7e34c">QsciLexer</a>.</p>

</div>
</div>
<a id="abf07dc83c19a3925e3cb977bf883b04c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abf07dc83c19a3925e3cb977bf883b04c">&#9670;&nbsp;</a></span>backslashEscapes()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerSQL::backslashEscapes </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if backslash escapes are enabled.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#ab64e768ab8e7af6af93ce95db074c90a">setBackslashEscapes()</a> </dd></dl>

</div>
</div>
<a id="aba150bef5f977fb65d66fcaec9c6664c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aba150bef5f977fb65d66fcaec9c6664c">&#9670;&nbsp;</a></span>setDottedWords()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerSQL::setDottedWords </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>enable</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>enable</em> is true then words may contain dots (i.e. periods or full stops). The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#a4f0a73894fc542ffc420113046c82f41">dottedWords()</a> </dd></dl>

</div>
</div>
<a id="a4f0a73894fc542ffc420113046c82f41"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4f0a73894fc542ffc420113046c82f41">&#9670;&nbsp;</a></span>dottedWords()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerSQL::dottedWords </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if words may contain dots (i.e. periods or full stops).</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#aba150bef5f977fb65d66fcaec9c6664c">setDottedWords()</a> </dd></dl>

</div>
</div>
<a id="a35dfbbd04762b0450232c14862ec3ea6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a35dfbbd04762b0450232c14862ec3ea6">&#9670;&nbsp;</a></span>setFoldAtElse()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerSQL::setFoldAtElse </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then ELSE blocks can be folded. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#a4cf0c0ab9cb0628c515910c67fab9950">foldAtElse()</a> </dd></dl>

</div>
</div>
<a id="a4cf0c0ab9cb0628c515910c67fab9950"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4cf0c0ab9cb0628c515910c67fab9950">&#9670;&nbsp;</a></span>foldAtElse()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerSQL::foldAtElse </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if ELSE blocks can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#a35dfbbd04762b0450232c14862ec3ea6">setFoldAtElse()</a> </dd></dl>

</div>
</div>
<a id="add132f5762831171fdee856172a0a5dc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#add132f5762831171fdee856172a0a5dc">&#9670;&nbsp;</a></span>foldComments()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerSQL::foldComments </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if multi-line comment blocks can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#a6efb8e98287c21ec5175a466d7e5cc55">setFoldComments()</a> </dd></dl>

</div>
</div>
<a id="a1d73603ec19f317dd0d6271ec852c0fc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1d73603ec19f317dd0d6271ec852c0fc">&#9670;&nbsp;</a></span>foldCompact()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerSQL::foldCompact </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if trailing blank lines are included in a fold block.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#a302b9b881fdc5dca82c5dea5fca5cd3e">setFoldCompact()</a> </dd></dl>

</div>
</div>
<a id="a680cba1b994603e73da00610e81debfe"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a680cba1b994603e73da00610e81debfe">&#9670;&nbsp;</a></span>setFoldOnlyBegin()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerSQL::setFoldOnlyBegin </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then only BEGIN blocks can be folded. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#a9a7c5fb256df97053fbe3203aaf3a93a">foldOnlyBegin()</a> </dd></dl>

</div>
</div>
<a id="a9a7c5fb256df97053fbe3203aaf3a93a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9a7c5fb256df97053fbe3203aaf3a93a">&#9670;&nbsp;</a></span>foldOnlyBegin()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerSQL::foldOnlyBegin </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if BEGIN blocks only can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#a680cba1b994603e73da00610e81debfe">setFoldOnlyBegin()</a> </dd></dl>

</div>
</div>
<a id="acc91bd455ff72d93d0bb73b553afbbb8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acc91bd455ff72d93d0bb73b553afbbb8">&#9670;&nbsp;</a></span>setHashComments()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerSQL::setHashComments </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>enable</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>enable</em> is true then '#' is used as a comment character. It is typically enabled for MySQL and disabled for Oracle. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#a24d7e487c0493f7164cb5bcce51d403d">hashComments()</a> </dd></dl>

</div>
</div>
<a id="a24d7e487c0493f7164cb5bcce51d403d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a24d7e487c0493f7164cb5bcce51d403d">&#9670;&nbsp;</a></span>hashComments()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerSQL::hashComments </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if '#' is used as a comment character.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#acc91bd455ff72d93d0bb73b553afbbb8">setHashComments()</a> </dd></dl>

</div>
</div>
<a id="ae6e5819a3ddec15ac6926b5e19927bff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae6e5819a3ddec15ac6926b5e19927bff">&#9670;&nbsp;</a></span>setQuotedIdentifiers()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerSQL::setQuotedIdentifiers </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>enable</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>enable</em> is true then quoted identifiers are enabled. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#ad5f7fc89705dd0588937b1565a6e5a26">quotedIdentifiers()</a> </dd></dl>

</div>
</div>
<a id="ad5f7fc89705dd0588937b1565a6e5a26"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad5f7fc89705dd0588937b1565a6e5a26">&#9670;&nbsp;</a></span>quotedIdentifiers()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerSQL::quotedIdentifiers </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if quoted identifiers are enabled.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#ae6e5819a3ddec15ac6926b5e19927bff">setQuotedIdentifiers()</a> </dd></dl>

</div>
</div>
<a id="ab64e768ab8e7af6af93ce95db074c90a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab64e768ab8e7af6af93ce95db074c90a">&#9670;&nbsp;</a></span>setBackslashEscapes</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerSQL::setBackslashEscapes </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>enable</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>enable</em> is true then backslash escapes are enabled. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#abf07dc83c19a3925e3cb977bf883b04c">backslashEscapes()</a> </dd></dl>

</div>
</div>
<a id="a6efb8e98287c21ec5175a466d7e5cc55"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6efb8e98287c21ec5175a466d7e5cc55">&#9670;&nbsp;</a></span>setFoldComments</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerSQL::setFoldComments </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then multi-line comment blocks can be folded. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#add132f5762831171fdee856172a0a5dc">foldComments()</a> </dd></dl>

</div>
</div>
<a id="a302b9b881fdc5dca82c5dea5fca5cd3e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a302b9b881fdc5dca82c5dea5fca5cd3e">&#9670;&nbsp;</a></span>setFoldCompact</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerSQL::setFoldCompact </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then trailing blank lines are included in a fold block. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerSQL.html#a1d73603ec19f317dd0d6271ec852c0fc">foldCompact()</a> </dd></dl>

</div>
</div>
<a id="a377b83523f800cc4598126417d80f74c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a377b83523f800cc4598126417d80f74c">&#9670;&nbsp;</a></span>readProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerSQL::readProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are read from the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ad472b16506a4cbc19634f07aa90f1ea6">QsciLexer</a>.</p>

</div>
</div>
<a id="a338a09c79011b57a842c581aa2556b4c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a338a09c79011b57a842c581aa2556b4c">&#9670;&nbsp;</a></span>writeProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerSQL::writeProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are written to the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#abccc4e010b724df1a7b5c5f3bce29501">QsciLexer</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
