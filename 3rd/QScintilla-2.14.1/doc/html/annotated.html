<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Class List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">Class List</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock">Here are the classes, structs, unions and interfaces with brief descriptions:</div><div class="directory">
<table class="directory">
<tr id="row_0_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciAbstractAPIs.html" target="_self">QsciAbstractAPIs</a></td><td class="desc">Interface to the textual API information used in call tips and for auto-completion. A sub-class will provide the actual implementation of the interface </td></tr>
<tr id="row_1_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciAPIs.html" target="_self">QsciAPIs</a></td><td class="desc">Provies an implementation of the textual API information used in call tips and for auto-completion </td></tr>
<tr id="row_2_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciCommand.html" target="_self">QsciCommand</a></td><td class="desc">Internal editor command that may have one or two keys bound to it </td></tr>
<tr id="row_3_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciCommandSet.html" target="_self">QsciCommandSet</a></td><td class="desc">Set of all internal editor commands that may have keys bound </td></tr>
<tr id="row_4_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciDocument.html" target="_self">QsciDocument</a></td><td class="desc">Document to be edited </td></tr>
<tr id="row_5_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexer.html" target="_self">QsciLexer</a></td><td class="desc">Abstract class used as a base for language lexers </td></tr>
<tr id="row_6_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerAsm.html" target="_self">QsciLexerAsm</a></td><td class="desc">The abstract <a class="el" href="classQsciLexerAsm.html" title="The abstract QsciLexerAsm class encapsulates the Scintilla Asm lexer.">QsciLexerAsm</a> class encapsulates the Scintilla Asm lexer </td></tr>
<tr id="row_7_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerAVS.html" target="_self">QsciLexerAVS</a></td><td class="desc">Encapsulates the Scintilla AVS lexer </td></tr>
<tr id="row_8_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerBash.html" target="_self">QsciLexerBash</a></td><td class="desc">Encapsulates the Scintilla Bash lexer </td></tr>
<tr id="row_9_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerBatch.html" target="_self">QsciLexerBatch</a></td><td class="desc">Encapsulates the Scintilla batch file lexer </td></tr>
<tr id="row_10_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerCMake.html" target="_self">QsciLexerCMake</a></td><td class="desc">Encapsulates the Scintilla CMake lexer </td></tr>
<tr id="row_11_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerCoffeeScript.html" target="_self">QsciLexerCoffeeScript</a></td><td class="desc">Encapsulates the Scintilla CoffeeScript lexer </td></tr>
<tr id="row_12_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerCPP.html" target="_self">QsciLexerCPP</a></td><td class="desc">Encapsulates the Scintilla C++ lexer </td></tr>
<tr id="row_13_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerCSharp.html" target="_self">QsciLexerCSharp</a></td><td class="desc">Encapsulates the Scintilla C# lexer </td></tr>
<tr id="row_14_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerCSS.html" target="_self">QsciLexerCSS</a></td><td class="desc">Encapsulates the Scintilla CSS lexer </td></tr>
<tr id="row_15_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerCustom.html" target="_self">QsciLexerCustom</a></td><td class="desc">Abstract class used as a base for new language lexers </td></tr>
<tr id="row_16_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerD.html" target="_self">QsciLexerD</a></td><td class="desc">Encapsulates the Scintilla D lexer </td></tr>
<tr id="row_17_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerDiff.html" target="_self">QsciLexerDiff</a></td><td class="desc">Encapsulates the Scintilla Diff lexer </td></tr>
<tr id="row_18_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerEDIFACT.html" target="_self">QsciLexerEDIFACT</a></td><td class="desc">Encapsulates the Scintilla EDIFACT lexer </td></tr>
<tr id="row_19_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerFortran.html" target="_self">QsciLexerFortran</a></td><td class="desc">Encapsulates the Scintilla Fortran lexer </td></tr>
<tr id="row_20_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerFortran77.html" target="_self">QsciLexerFortran77</a></td><td class="desc">Encapsulates the Scintilla Fortran77 lexer </td></tr>
<tr id="row_21_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerHex.html" target="_self">QsciLexerHex</a></td><td class="desc">The abstract <a class="el" href="classQsciLexerHex.html" title="The abstract QsciLexerHex class encapsulates the Scintilla Hex lexer.">QsciLexerHex</a> class encapsulates the Scintilla Hex lexer </td></tr>
<tr id="row_22_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerHTML.html" target="_self">QsciLexerHTML</a></td><td class="desc">Encapsulates the Scintilla HTML lexer </td></tr>
<tr id="row_23_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerIDL.html" target="_self">QsciLexerIDL</a></td><td class="desc">Encapsulates the Scintilla IDL lexer </td></tr>
<tr id="row_24_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerIntelHex.html" target="_self">QsciLexerIntelHex</a></td><td class="desc">Encapsulates the Scintilla Intel Hex lexer </td></tr>
<tr id="row_25_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerJava.html" target="_self">QsciLexerJava</a></td><td class="desc">Encapsulates the Scintilla Java lexer </td></tr>
<tr id="row_26_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerJavaScript.html" target="_self">QsciLexerJavaScript</a></td><td class="desc">Encapsulates the Scintilla JavaScript lexer </td></tr>
<tr id="row_27_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerJSON.html" target="_self">QsciLexerJSON</a></td><td class="desc">Encapsulates the Scintilla JSON lexer </td></tr>
<tr id="row_28_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerLua.html" target="_self">QsciLexerLua</a></td><td class="desc">Encapsulates the Scintilla Lua lexer </td></tr>
<tr id="row_29_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerMakefile.html" target="_self">QsciLexerMakefile</a></td><td class="desc">Encapsulates the Scintilla Makefile lexer </td></tr>
<tr id="row_30_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerMarkdown.html" target="_self">QsciLexerMarkdown</a></td><td class="desc">Encapsulates the Scintilla Markdown lexer </td></tr>
<tr id="row_31_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerMASM.html" target="_self">QsciLexerMASM</a></td><td class="desc">Encapsulates the Scintilla MASM lexer </td></tr>
<tr id="row_32_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerMatlab.html" target="_self">QsciLexerMatlab</a></td><td class="desc">Encapsulates the Scintilla Matlab file lexer </td></tr>
<tr id="row_33_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerNASM.html" target="_self">QsciLexerNASM</a></td><td class="desc">Encapsulates the Scintilla NASM lexer </td></tr>
<tr id="row_34_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerOctave.html" target="_self">QsciLexerOctave</a></td><td class="desc">Encapsulates the Scintilla Octave file lexer </td></tr>
<tr id="row_35_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerPascal.html" target="_self">QsciLexerPascal</a></td><td class="desc">Encapsulates the Scintilla Pascal lexer </td></tr>
<tr id="row_36_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerPerl.html" target="_self">QsciLexerPerl</a></td><td class="desc">Encapsulates the Scintilla Perl lexer </td></tr>
<tr id="row_37_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerPO.html" target="_self">QsciLexerPO</a></td><td class="desc">Encapsulates the Scintilla PO lexer </td></tr>
<tr id="row_38_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerPostScript.html" target="_self">QsciLexerPostScript</a></td><td class="desc">Encapsulates the Scintilla PostScript lexer </td></tr>
<tr id="row_39_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerPOV.html" target="_self">QsciLexerPOV</a></td><td class="desc">Encapsulates the Scintilla POV lexer </td></tr>
<tr id="row_40_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerProperties.html" target="_self">QsciLexerProperties</a></td><td class="desc">Encapsulates the Scintilla Properties lexer </td></tr>
<tr id="row_41_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerPython.html" target="_self">QsciLexerPython</a></td><td class="desc">Encapsulates the Scintilla Python lexer </td></tr>
<tr id="row_42_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerRuby.html" target="_self">QsciLexerRuby</a></td><td class="desc">Encapsulates the Scintilla Ruby lexer </td></tr>
<tr id="row_43_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerSpice.html" target="_self">QsciLexerSpice</a></td><td class="desc">Encapsulates the Scintilla Spice lexer </td></tr>
<tr id="row_44_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerSQL.html" target="_self">QsciLexerSQL</a></td><td class="desc">Encapsulates the Scintilla SQL lexer </td></tr>
<tr id="row_45_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerSRec.html" target="_self">QsciLexerSRec</a></td><td class="desc">Encapsulates the Scintilla S-Record lexer </td></tr>
<tr id="row_46_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerTCL.html" target="_self">QsciLexerTCL</a></td><td class="desc">Encapsulates the Scintilla TCL lexer </td></tr>
<tr id="row_47_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerTekHex.html" target="_self">QsciLexerTekHex</a></td><td class="desc">Encapsulates the Scintilla Tektronix Hex lexer </td></tr>
<tr id="row_48_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerTeX.html" target="_self">QsciLexerTeX</a></td><td class="desc">Encapsulates the Scintilla TeX lexer </td></tr>
<tr id="row_49_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerVerilog.html" target="_self">QsciLexerVerilog</a></td><td class="desc">Encapsulates the Scintilla Verilog lexer </td></tr>
<tr id="row_50_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerVHDL.html" target="_self">QsciLexerVHDL</a></td><td class="desc">Encapsulates the Scintilla VHDL lexer </td></tr>
<tr id="row_51_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerXML.html" target="_self">QsciLexerXML</a></td><td class="desc">Encapsulates the Scintilla XML lexer </td></tr>
<tr id="row_52_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciLexerYAML.html" target="_self">QsciLexerYAML</a></td><td class="desc">Encapsulates the Scintilla YAML lexer </td></tr>
<tr id="row_53_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciMacro.html" target="_self">QsciMacro</a></td><td class="desc">Sequence of recordable editor commands </td></tr>
<tr id="row_54_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciPrinter.html" target="_self">QsciPrinter</a></td><td class="desc">Sub-class of the Qt QPrinter class that is able to print the text of a Scintilla document </td></tr>
<tr id="row_55_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciScintilla.html" target="_self">QsciScintilla</a></td><td class="desc">Implements a higher level, more Qt-like, API to the Scintilla editor widget </td></tr>
<tr id="row_56_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciScintillaBase.html" target="_self">QsciScintillaBase</a></td><td class="desc">Implements the Scintilla editor widget and its low-level API </td></tr>
<tr id="row_57_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciStyle.html" target="_self">QsciStyle</a></td><td class="desc">Encapsulates all the attributes of a style </td></tr>
<tr id="row_58_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="classQsciStyledText.html" target="_self">QsciStyledText</a></td><td class="desc">Container for a piece of text and the style used to display the text </td></tr>
</table>
</div><!-- directory -->
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
