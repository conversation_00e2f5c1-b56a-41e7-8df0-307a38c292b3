<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciScintillaBase Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#signals">Signals</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="classQsciScintillaBase-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciScintillaBase Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qsciscintillabase.h&gt;</code></p>

<p>Inherits QAbstractScrollArea.</p>

<p>Inherited by <a class="el" href="classQsciScintilla.html">QsciScintilla</a>.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:ad9c35f7540b2457103db9cf8c877784a"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SCI_START</b> = 2000, 
<b>SCI_OPTIONAL_START</b> = 3000, 
<b>SCI_LEXER_START</b> = 4000, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaada4cbb31d6583ed80804e4b94cd4023">SCI_ADDTEXT</a> = 2001, 
<b>SCI_ADDSTYLEDTEXT</b> = 2002, 
<b>SCI_INSERTTEXT</b> = 2003, 
<br />
&#160;&#160;<b>SCI_CLEARALL</b> = 2004, 
<b>SCI_CLEARDOCUMENTSTYLE</b> = 2005, 
<b>SCI_GETLENGTH</b> = 2006, 
<br />
&#160;&#160;<b>SCI_GETCHARAT</b> = 2007, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaf1289f2530afb81cc99e2b7e2e2cad28">SCI_GETCURRENTPOS</a> = 2008, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aafbdd81cde4931224e6f87aceba707a04">SCI_GETANCHOR</a> = 2009, 
<br />
&#160;&#160;<b>SCI_GETSTYLEAT</b> = 2010, 
<b>SCI_REDO</b> = 2011, 
<b>SCI_SETUNDOCOLLECTION</b> = 2012, 
<br />
&#160;&#160;<b>SCI_SELECTALL</b> = 2013, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa20e9df7da839e5b9e2edd2366a7ecb97">SCI_SETSAVEPOINT</a> = 2014, 
<b>SCI_GETSTYLEDTEXT</b> = 2015, 
<br />
&#160;&#160;<b>SCI_CANREDO</b> = 2016, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa4db578069d526ece8c0a9d08869a3033">SCI_MARKERLINEFROMHANDLE</a> = 2017, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa007cbfb293accfd436ea9443b2678327">SCI_MARKERDELETEHANDLE</a> = 2018, 
<br />
&#160;&#160;<b>SCI_GETUNDOCOLLECTION</b> = 2019, 
<b>SCI_GETVIEWWS</b> = 2020, 
<b>SCI_SETVIEWWS</b> = 2021, 
<br />
&#160;&#160;<b>SCI_POSITIONFROMPOINT</b> = 2022, 
<b>SCI_POSITIONFROMPOINTCLOSE</b> = 2023, 
<b>SCI_GOTOLINE</b> = 2024, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa3c6da358d0bc87040b30811bbcbf8cf7">SCI_GOTOPOS</a> = 2025, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa9b577390896af25454459e1a9e08ad2e">SCI_SETANCHOR</a> = 2026, 
<b>SCI_GETCURLINE</b> = 2027, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa820d8d563cb319ff42e5b9ea709d839d">SCI_GETENDSTYLED</a> = 2028, 
<b>SCI_CONVERTEOLS</b> = 2029, 
<b>SCI_GETEOLMODE</b> = 2030, 
<br />
&#160;&#160;<b>SCI_SETEOLMODE</b> = 2031, 
<b>SCI_STARTSTYLING</b> = 2032, 
<b>SCI_SETSTYLING</b> = 2033, 
<br />
&#160;&#160;<b>SCI_GETBUFFEREDDRAW</b> = 2034, 
<b>SCI_SETBUFFEREDDRAW</b> = 2035, 
<b>SCI_SETTABWIDTH</b> = 2036, 
<br />
&#160;&#160;<b>SCI_GETTABWIDTH</b> = 2121, 
<b>SCI_SETCODEPAGE</b> = 2037, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa370a2c2674421348d23ecb97ff981b2a">SCI_MARKERDEFINE</a> = 2040, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1fb7b42e9fbbe27b662b0edb21ac2d2f">SCI_MARKERSETFORE</a> = 2041, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa0419ba42e62661c245af25007bac3bfe">SCI_MARKERSETBACK</a> = 2042, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1be8617303dc15428758e22749267263">SCI_MARKERADD</a> = 2043, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aae19516cd9746dbec20598773ad354d4e">SCI_MARKERDELETE</a> = 2044, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa010af0bf4fe497e1b68fe1fb56580770">SCI_MARKERDELETEALL</a> = 2045, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaaee02f504dec75c8b349150805440fd7">SCI_MARKERGET</a> = 2046, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1f843331fd750110c6f97fa443567b22">SCI_MARKERNEXT</a> = 2047, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1e455f05b605c2ba82be3baf05e3abe4">SCI_MARKERPREVIOUS</a> = 2048, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaffe2901cffbccede9b0b5d1636bb5e9f">SCI_MARKERDEFINEPIXMAP</a> = 2049, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa0ee188f4bfe7289f454f99af191d1523">SCI_SETMARGINTYPEN</a> = 2240, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa919cf8a6d08d570e00ece099ff62010c">SCI_GETMARGINTYPEN</a> = 2241, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1195d46181a565b14806d94595fc7aa6">SCI_SETMARGINWIDTHN</a> = 2242, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa3654140951ae95d75f21c43cdcd91a43">SCI_GETMARGINWIDTHN</a> = 2243, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aad1cec6e4e0ad45ce7d7edad7acb8a3b5">SCI_SETMARGINMASKN</a> = 2244, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aadbd2eceef7f59bcda7d7db01a4aa7c7b">SCI_GETMARGINMASKN</a> = 2245, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa64b07178255dc95b19a7c8feabaac1b2">SCI_SETMARGINSENSITIVEN</a> = 2246, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaffc41be0dbc2eb4b00438f0b489c7c88">SCI_GETMARGINSENSITIVEN</a> = 2247, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aac5d3e4edd15f65d5e500d90590e443a9">SCI_SETMARGINCURSORN</a> = 2248, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa282bc83043fda7837568925243fcb384">SCI_GETMARGINCURSORN</a> = 2249, 
<b>SCI_STYLECLEARALL</b> = 2050, 
<b>SCI_STYLESETFORE</b> = 2051, 
<br />
&#160;&#160;<b>SCI_STYLESETBACK</b> = 2052, 
<b>SCI_STYLESETBOLD</b> = 2053, 
<b>SCI_STYLESETITALIC</b> = 2054, 
<br />
&#160;&#160;<b>SCI_STYLESETSIZE</b> = 2055, 
<b>SCI_STYLESETFONT</b> = 2056, 
<b>SCI_STYLESETEOLFILLED</b> = 2057, 
<br />
&#160;&#160;<b>SCI_STYLERESETDEFAULT</b> = 2058, 
<b>SCI_STYLESETUNDERLINE</b> = 2059, 
<b>SCI_STYLESETCASE</b> = 2060, 
<br />
&#160;&#160;<b>SCI_STYLESETSIZEFRACTIONAL</b> = 2061, 
<b>SCI_STYLEGETSIZEFRACTIONAL</b> = 2062, 
<b>SCI_STYLESETWEIGHT</b> = 2063, 
<br />
&#160;&#160;<b>SCI_STYLEGETWEIGHT</b> = 2064, 
<b>SCI_STYLESETCHARACTERSET</b> = 2066, 
<b>SCI_SETSELFORE</b> = 2067, 
<br />
&#160;&#160;<b>SCI_SETSELBACK</b> = 2068, 
<b>SCI_SETCARETFORE</b> = 2069, 
<b>SCI_ASSIGNCMDKEY</b> = 2070, 
<br />
&#160;&#160;<b>SCI_CLEARCMDKEY</b> = 2071, 
<b>SCI_CLEARALLCMDKEYS</b> = 2072, 
<b>SCI_SETSTYLINGEX</b> = 2073, 
<br />
&#160;&#160;<b>SCI_STYLESETVISIBLE</b> = 2074, 
<b>SCI_GETCARETPERIOD</b> = 2075, 
<b>SCI_SETCARETPERIOD</b> = 2076, 
<br />
&#160;&#160;<b>SCI_SETWORDCHARS</b> = 2077, 
<b>SCI_BEGINUNDOACTION</b> = 2078, 
<b>SCI_ENDUNDOACTION</b> = 2079, 
<br />
&#160;&#160;<b>SCI_INDICSETSTYLE</b> = 2080, 
<b>SCI_INDICGETSTYLE</b> = 2081, 
<b>SCI_INDICSETFORE</b> = 2082, 
<br />
&#160;&#160;<b>SCI_INDICGETFORE</b> = 2083, 
<b>SCI_SETWHITESPACEFORE</b> = 2084, 
<b>SCI_SETWHITESPACEBACK</b> = 2085, 
<br />
&#160;&#160;<b>SCI_SETWHITESPACESIZE</b> = 2086, 
<b>SCI_GETWHITESPACESIZE</b> = 2087, 
<b>SCI_SETSTYLEBITS</b> = 2090, 
<br />
&#160;&#160;<b>SCI_GETSTYLEBITS</b> = 2091, 
<b>SCI_SETLINESTATE</b> = 2092, 
<b>SCI_GETLINESTATE</b> = 2093, 
<br />
&#160;&#160;<b>SCI_GETMAXLINESTATE</b> = 2094, 
<b>SCI_GETCARETLINEVISIBLE</b> = 2095, 
<b>SCI_SETCARETLINEVISIBLE</b> = 2096, 
<br />
&#160;&#160;<b>SCI_GETCARETLINEBACK</b> = 2097, 
<b>SCI_SETCARETLINEBACK</b> = 2098, 
<b>SCI_STYLESETCHANGEABLE</b> = 2099, 
<br />
&#160;&#160;<b>SCI_AUTOCSHOW</b> = 2100, 
<b>SCI_AUTOCCANCEL</b> = 2101, 
<b>SCI_AUTOCACTIVE</b> = 2102, 
<br />
&#160;&#160;<b>SCI_AUTOCPOSSTART</b> = 2103, 
<b>SCI_AUTOCCOMPLETE</b> = 2104, 
<b>SCI_AUTOCSTOPS</b> = 2105, 
<br />
&#160;&#160;<b>SCI_AUTOCSETSEPARATOR</b> = 2106, 
<b>SCI_AUTOCGETSEPARATOR</b> = 2107, 
<b>SCI_AUTOCSELECT</b> = 2108, 
<br />
&#160;&#160;<b>SCI_AUTOCSETCANCELATSTART</b> = 2110, 
<b>SCI_AUTOCGETCANCELATSTART</b> = 2111, 
<b>SCI_AUTOCSETFILLUPS</b> = 2112, 
<br />
&#160;&#160;<b>SCI_AUTOCSETCHOOSESINGLE</b> = 2113, 
<b>SCI_AUTOCGETCHOOSESINGLE</b> = 2114, 
<b>SCI_AUTOCSETIGNORECASE</b> = 2115, 
<br />
&#160;&#160;<b>SCI_AUTOCGETIGNORECASE</b> = 2116, 
<b>SCI_USERLISTSHOW</b> = 2117, 
<b>SCI_AUTOCSETAUTOHIDE</b> = 2118, 
<br />
&#160;&#160;<b>SCI_AUTOCGETAUTOHIDE</b> = 2119, 
<b>SCI_AUTOCSETDROPRESTOFWORD</b> = 2270, 
<b>SCI_AUTOCGETDROPRESTOFWORD</b> = 2271, 
<br />
&#160;&#160;<b>SCI_SETINDENT</b> = 2122, 
<b>SCI_GETINDENT</b> = 2123, 
<b>SCI_SETUSETABS</b> = 2124, 
<br />
&#160;&#160;<b>SCI_GETUSETABS</b> = 2125, 
<b>SCI_SETLINEINDENTATION</b> = 2126, 
<b>SCI_GETLINEINDENTATION</b> = 2127, 
<br />
&#160;&#160;<b>SCI_GETLINEINDENTPOSITION</b> = 2128, 
<b>SCI_GETCOLUMN</b> = 2129, 
<b>SCI_SETHSCROLLBAR</b> = 2130, 
<br />
&#160;&#160;<b>SCI_GETHSCROLLBAR</b> = 2131, 
<b>SCI_SETINDENTATIONGUIDES</b> = 2132, 
<b>SCI_GETINDENTATIONGUIDES</b> = 2133, 
<br />
&#160;&#160;<b>SCI_SETHIGHLIGHTGUIDE</b> = 2134, 
<b>SCI_GETHIGHLIGHTGUIDE</b> = 2135, 
<b>SCI_GETLINEENDPOSITION</b> = 2136, 
<br />
&#160;&#160;<b>SCI_GETCODEPAGE</b> = 2137, 
<b>SCI_GETCARETFORE</b> = 2138, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa98efd72455b4555e6d4cbd9cd79d2a5b">SCI_GETREADONLY</a> = 2140, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aabcd34a065f08d19b10ca6caaa78d3e78">SCI_SETCURRENTPOS</a> = 2141, 
<b>SCI_SETSELECTIONSTART</b> = 2142, 
<b>SCI_GETSELECTIONSTART</b> = 2143, 
<br />
&#160;&#160;<b>SCI_SETSELECTIONEND</b> = 2144, 
<b>SCI_GETSELECTIONEND</b> = 2145, 
<b>SCI_SETPRINTMAGNIFICATION</b> = 2146, 
<br />
&#160;&#160;<b>SCI_GETPRINTMAGNIFICATION</b> = 2147, 
<b>SCI_SETPRINTCOLOURMODE</b> = 2148, 
<b>SCI_GETPRINTCOLOURMODE</b> = 2149, 
<br />
&#160;&#160;<b>SCI_FINDTEXT</b> = 2150, 
<b>SCI_FORMATRANGE</b> = 2151, 
<b>SCI_GETFIRSTVISIBLELINE</b> = 2152, 
<br />
&#160;&#160;<b>SCI_GETLINE</b> = 2153, 
<b>SCI_GETLINECOUNT</b> = 2154, 
<b>SCI_SETMARGINLEFT</b> = 2155, 
<br />
&#160;&#160;<b>SCI_GETMARGINLEFT</b> = 2156, 
<b>SCI_SETMARGINRIGHT</b> = 2157, 
<b>SCI_GETMARGINRIGHT</b> = 2158, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaa5af597c3c35c97cbe9f6dd98462594c">SCI_GETMODIFY</a> = 2159, 
<b>SCI_SETSEL</b> = 2160, 
<b>SCI_GETSELTEXT</b> = 2161, 
<br />
&#160;&#160;<b>SCI_GETTEXTRANGE</b> = 2162, 
<b>SCI_HIDESELECTION</b> = 2163, 
<b>SCI_POINTXFROMPOSITION</b> = 2164, 
<br />
&#160;&#160;<b>SCI_POINTYFROMPOSITION</b> = 2165, 
<b>SCI_LINEFROMPOSITION</b> = 2166, 
<b>SCI_POSITIONFROMLINE</b> = 2167, 
<br />
&#160;&#160;<b>SCI_LINESCROLL</b> = 2168, 
<b>SCI_SCROLLCARET</b> = 2169, 
<b>SCI_REPLACESEL</b> = 2170, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaa07157383b442ab2d2be8c2d03078fc2">SCI_SETREADONLY</a> = 2171, 
<b>SCI_NULL</b> = 2172, 
<b>SCI_CANPASTE</b> = 2173, 
<br />
&#160;&#160;<b>SCI_CANUNDO</b> = 2174, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aac8f52176e19feec95c354452b6358d93">SCI_EMPTYUNDOBUFFER</a> = 2175, 
<b>SCI_UNDO</b> = 2176, 
<br />
&#160;&#160;<b>SCI_CUT</b> = 2177, 
<b>SCI_COPY</b> = 2178, 
<b>SCI_PASTE</b> = 2179, 
<br />
&#160;&#160;<b>SCI_CLEAR</b> = 2180, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaeadc6fabc9859b2e52f9cfa23732f004">SCI_SETTEXT</a> = 2181, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa4bc0cd151979992bc5015852c5dbfbfe">SCI_GETTEXT</a> = 2182, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aadd626735df321c6b994c887cfad61ed4">SCI_GETTEXTLENGTH</a> = 2183, 
<b>SCI_GETDIRECTFUNCTION</b> = 2184, 
<b>SCI_GETDIRECTPOINTER</b> = 2185, 
<br />
&#160;&#160;<b>SCI_SETOVERTYPE</b> = 2186, 
<b>SCI_GETOVERTYPE</b> = 2187, 
<b>SCI_SETCARETWIDTH</b> = 2188, 
<br />
&#160;&#160;<b>SCI_GETCARETWIDTH</b> = 2189, 
<b>SCI_SETTARGETSTART</b> = 2190, 
<b>SCI_GETTARGETSTART</b> = 2191, 
<br />
&#160;&#160;<b>SCI_SETTARGETEND</b> = 2192, 
<b>SCI_GETTARGETEND</b> = 2193, 
<b>SCI_REPLACETARGET</b> = 2194, 
<br />
&#160;&#160;<b>SCI_REPLACETARGETRE</b> = 2195, 
<b>SCI_SEARCHINTARGET</b> = 2197, 
<b>SCI_SETSEARCHFLAGS</b> = 2198, 
<br />
&#160;&#160;<b>SCI_GETSEARCHFLAGS</b> = 2199, 
<b>SCI_CALLTIPSHOW</b> = 2200, 
<b>SCI_CALLTIPCANCEL</b> = 2201, 
<br />
&#160;&#160;<b>SCI_CALLTIPACTIVE</b> = 2202, 
<b>SCI_CALLTIPPOSSTART</b> = 2203, 
<b>SCI_CALLTIPSETHLT</b> = 2204, 
<br />
&#160;&#160;<b>SCI_CALLTIPSETBACK</b> = 2205, 
<b>SCI_CALLTIPSETFORE</b> = 2206, 
<b>SCI_CALLTIPSETFOREHLT</b> = 2207, 
<br />
&#160;&#160;<b>SCI_AUTOCSETMAXWIDTH</b> = 2208, 
<b>SCI_AUTOCGETMAXWIDTH</b> = 2209, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa99467be94f4357e1be0ddf72bde6ae5a">SCI_AUTOCSETMAXHEIGHT</a> = 2210, 
<br />
&#160;&#160;<b>SCI_AUTOCGETMAXHEIGHT</b> = 2211, 
<b>SCI_CALLTIPUSESTYLE</b> = 2212, 
<b>SCI_CALLTIPSETPOSITION</b> = 2213, 
<br />
&#160;&#160;<b>SCI_CALLTIPSETPOSSTART</b> = 2214, 
<b>SCI_VISIBLEFROMDOCLINE</b> = 2220, 
<b>SCI_DOCLINEFROMVISIBLE</b> = 2221, 
<br />
&#160;&#160;<b>SCI_SETFOLDLEVEL</b> = 2222, 
<b>SCI_GETFOLDLEVEL</b> = 2223, 
<b>SCI_GETLASTCHILD</b> = 2224, 
<br />
&#160;&#160;<b>SCI_GETFOLDPARENT</b> = 2225, 
<b>SCI_SHOWLINES</b> = 2226, 
<b>SCI_HIDELINES</b> = 2227, 
<br />
&#160;&#160;<b>SCI_GETLINEVISIBLE</b> = 2228, 
<b>SCI_SETFOLDEXPANDED</b> = 2229, 
<b>SCI_GETFOLDEXPANDED</b> = 2230, 
<br />
&#160;&#160;<b>SCI_TOGGLEFOLD</b> = 2231, 
<b>SCI_ENSUREVISIBLE</b> = 2232, 
<b>SCI_SETFOLDFLAGS</b> = 2233, 
<br />
&#160;&#160;<b>SCI_ENSUREVISIBLEENFORCEPOLICY</b> = 2234, 
<b>SCI_WRAPCOUNT</b> = 2235, 
<b>SCI_GETALLLINESVISIBLE</b> = 2236, 
<br />
&#160;&#160;<b>SCI_FOLDLINE</b> = 2237, 
<b>SCI_FOLDCHILDREN</b> = 2238, 
<b>SCI_EXPANDCHILDREN</b> = 2239, 
<br />
&#160;&#160;<b>SCI_SETMARGINBACKN</b> = 2250, 
<b>SCI_GETMARGINBACKN</b> = 2251, 
<b>SCI_SETMARGINS</b> = 2252, 
<br />
&#160;&#160;<b>SCI_GETMARGINS</b> = 2253, 
<b>SCI_SETTABINDENTS</b> = 2260, 
<b>SCI_GETTABINDENTS</b> = 2261, 
<br />
&#160;&#160;<b>SCI_SETBACKSPACEUNINDENTS</b> = 2262, 
<b>SCI_GETBACKSPACEUNINDENTS</b> = 2263, 
<b>SCI_SETMOUSEDWELLTIME</b> = 2264, 
<br />
&#160;&#160;<b>SCI_GETMOUSEDWELLTIME</b> = 2265, 
<b>SCI_WORDSTARTPOSITION</b> = 2266, 
<b>SCI_WORDENDPOSITION</b> = 2267, 
<br />
&#160;&#160;<b>SCI_SETWRAPMODE</b> = 2268, 
<b>SCI_GETWRAPMODE</b> = 2269, 
<b>SCI_SETLAYOUTCACHE</b> = 2272, 
<br />
&#160;&#160;<b>SCI_GETLAYOUTCACHE</b> = 2273, 
<b>SCI_SETSCROLLWIDTH</b> = 2274, 
<b>SCI_GETSCROLLWIDTH</b> = 2275, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa5158fc6bdc2ceb345246b7f4ca45de04">SCI_TEXTWIDTH</a> = 2276, 
<b>SCI_SETENDATLASTLINE</b> = 2277, 
<b>SCI_GETENDATLASTLINE</b> = 2278, 
<br />
&#160;&#160;<b>SCI_TEXTHEIGHT</b> = 2279, 
<b>SCI_SETVSCROLLBAR</b> = 2280, 
<b>SCI_GETVSCROLLBAR</b> = 2281, 
<br />
&#160;&#160;<b>SCI_APPENDTEXT</b> = 2282, 
<b>SCI_GETTWOPHASEDRAW</b> = 2283, 
<b>SCI_SETTWOPHASEDRAW</b> = 2284, 
<br />
&#160;&#160;<b>SCI_AUTOCGETTYPESEPARATOR</b> = 2285, 
<b>SCI_AUTOCSETTYPESEPARATOR</b> = 2286, 
<b>SCI_TARGETFROMSELECTION</b> = 2287, 
<br />
&#160;&#160;<b>SCI_LINESJOIN</b> = 2288, 
<b>SCI_LINESSPLIT</b> = 2289, 
<b>SCI_SETFOLDMARGINCOLOUR</b> = 2290, 
<br />
&#160;&#160;<b>SCI_SETFOLDMARGINHICOLOUR</b> = 2291, 
<b>SCI_MARKERSETBACKSELECTED</b> = 2292, 
<b>SCI_MARKERENABLEHIGHLIGHT</b> = 2293, 
<br />
&#160;&#160;<b>SCI_LINEDOWN</b> = 2300, 
<b>SCI_LINEDOWNEXTEND</b> = 2301, 
<b>SCI_LINEUP</b> = 2302, 
<br />
&#160;&#160;<b>SCI_LINEUPEXTEND</b> = 2303, 
<b>SCI_CHARLEFT</b> = 2304, 
<b>SCI_CHARLEFTEXTEND</b> = 2305, 
<br />
&#160;&#160;<b>SCI_CHARRIGHT</b> = 2306, 
<b>SCI_CHARRIGHTEXTEND</b> = 2307, 
<b>SCI_WORDLEFT</b> = 2308, 
<br />
&#160;&#160;<b>SCI_WORDLEFTEXTEND</b> = 2309, 
<b>SCI_WORDRIGHT</b> = 2310, 
<b>SCI_WORDRIGHTEXTEND</b> = 2311, 
<br />
&#160;&#160;<b>SCI_HOME</b> = 2312, 
<b>SCI_HOMEEXTEND</b> = 2313, 
<b>SCI_LINEEND</b> = 2314, 
<br />
&#160;&#160;<b>SCI_LINEENDEXTEND</b> = 2315, 
<b>SCI_DOCUMENTSTART</b> = 2316, 
<b>SCI_DOCUMENTSTARTEXTEND</b> = 2317, 
<br />
&#160;&#160;<b>SCI_DOCUMENTEND</b> = 2318, 
<b>SCI_DOCUMENTENDEXTEND</b> = 2319, 
<b>SCI_PAGEUP</b> = 2320, 
<br />
&#160;&#160;<b>SCI_PAGEUPEXTEND</b> = 2321, 
<b>SCI_PAGEDOWN</b> = 2322, 
<b>SCI_PAGEDOWNEXTEND</b> = 2323, 
<br />
&#160;&#160;<b>SCI_EDITTOGGLEOVERTYPE</b> = 2324, 
<b>SCI_CANCEL</b> = 2325, 
<b>SCI_DELETEBACK</b> = 2326, 
<br />
&#160;&#160;<b>SCI_TAB</b> = 2327, 
<b>SCI_BACKTAB</b> = 2328, 
<b>SCI_NEWLINE</b> = 2329, 
<br />
&#160;&#160;<b>SCI_FORMFEED</b> = 2330, 
<b>SCI_VCHOME</b> = 2331, 
<b>SCI_VCHOMEEXTEND</b> = 2332, 
<br />
&#160;&#160;<b>SCI_ZOOMIN</b> = 2333, 
<b>SCI_ZOOMOUT</b> = 2334, 
<b>SCI_DELWORDLEFT</b> = 2335, 
<br />
&#160;&#160;<b>SCI_DELWORDRIGHT</b> = 2336, 
<b>SCI_LINECUT</b> = 2337, 
<b>SCI_LINEDELETE</b> = 2338, 
<br />
&#160;&#160;<b>SCI_LINETRANSPOSE</b> = 2339, 
<b>SCI_LOWERCASE</b> = 2340, 
<b>SCI_UPPERCASE</b> = 2341, 
<br />
&#160;&#160;<b>SCI_LINESCROLLDOWN</b> = 2342, 
<b>SCI_LINESCROLLUP</b> = 2343, 
<b>SCI_DELETEBACKNOTLINE</b> = 2344, 
<br />
&#160;&#160;<b>SCI_HOMEDISPLAY</b> = 2345, 
<b>SCI_HOMEDISPLAYEXTEND</b> = 2346, 
<b>SCI_LINEENDDISPLAY</b> = 2347, 
<br />
&#160;&#160;<b>SCI_LINEENDDISPLAYEXTEND</b> = 2348, 
<b>SCI_MOVECARETINSIDEVIEW</b> = 2401, 
<b>SCI_LINELENGTH</b> = 2350, 
<br />
&#160;&#160;<b>SCI_BRACEHIGHLIGHT</b> = 2351, 
<b>SCI_BRACEBADLIGHT</b> = 2352, 
<b>SCI_BRACEMATCH</b> = 2353, 
<br />
&#160;&#160;<b>SCI_LINEREVERSE</b> = 2354, 
<b>SCI_GETVIEWEOL</b> = 2355, 
<b>SCI_SETVIEWEOL</b> = 2356, 
<br />
&#160;&#160;<b>SCI_GETDOCPOINTER</b> = 2357, 
<b>SCI_SETDOCPOINTER</b> = 2358, 
<b>SCI_SETMODEVENTMASK</b> = 2359, 
<br />
&#160;&#160;<b>SCI_GETEDGECOLUMN</b> = 2360, 
<b>SCI_SETEDGECOLUMN</b> = 2361, 
<b>SCI_GETEDGEMODE</b> = 2362, 
<br />
&#160;&#160;<b>SCI_SETEDGEMODE</b> = 2363, 
<b>SCI_GETEDGECOLOUR</b> = 2364, 
<b>SCI_SETEDGECOLOUR</b> = 2365, 
<br />
&#160;&#160;<b>SCI_SEARCHANCHOR</b> = 2366, 
<b>SCI_SEARCHNEXT</b> = 2367, 
<b>SCI_SEARCHPREV</b> = 2368, 
<br />
&#160;&#160;<b>SCI_LINESONSCREEN</b> = 2370, 
<b>SCI_USEPOPUP</b> = 2371, 
<b>SCI_SELECTIONISRECTANGLE</b> = 2372, 
<br />
&#160;&#160;<b>SCI_SETZOOM</b> = 2373, 
<b>SCI_GETZOOM</b> = 2374, 
<b>SCI_CREATEDOCUMENT</b> = 2375, 
<br />
&#160;&#160;<b>SCI_ADDREFDOCUMENT</b> = 2376, 
<b>SCI_RELEASEDOCUMENT</b> = 2377, 
<b>SCI_GETMODEVENTMASK</b> = 2378, 
<br />
&#160;&#160;<b>SCI_SETFOCUS</b> = 2380, 
<b>SCI_GETFOCUS</b> = 2381, 
<b>SCI_SETSTATUS</b> = 2382, 
<br />
&#160;&#160;<b>SCI_GETSTATUS</b> = 2383, 
<b>SCI_SETMOUSEDOWNCAPTURES</b> = 2384, 
<b>SCI_GETMOUSEDOWNCAPTURES</b> = 2385, 
<br />
&#160;&#160;<b>SCI_SETCURSOR</b> = 2386, 
<b>SCI_GETCURSOR</b> = 2387, 
<b>SCI_SETCONTROLCHARSYMBOL</b> = 2388, 
<br />
&#160;&#160;<b>SCI_GETCONTROLCHARSYMBOL</b> = 2389, 
<b>SCI_WORDPARTLEFT</b> = 2390, 
<b>SCI_WORDPARTLEFTEXTEND</b> = 2391, 
<br />
&#160;&#160;<b>SCI_WORDPARTRIGHT</b> = 2392, 
<b>SCI_WORDPARTRIGHTEXTEND</b> = 2393, 
<b>SCI_SETVISIBLEPOLICY</b> = 2394, 
<br />
&#160;&#160;<b>SCI_DELLINELEFT</b> = 2395, 
<b>SCI_DELLINERIGHT</b> = 2396, 
<b>SCI_SETXOFFSET</b> = 2397, 
<br />
&#160;&#160;<b>SCI_GETXOFFSET</b> = 2398, 
<b>SCI_CHOOSECARETX</b> = 2399, 
<b>SCI_GRABFOCUS</b> = 2400, 
<br />
&#160;&#160;<b>SCI_SETXCARETPOLICY</b> = 2402, 
<b>SCI_SETYCARETPOLICY</b> = 2403, 
<b>SCI_LINEDUPLICATE</b> = 2404, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa5c17528701e5a34fc8b685be0914d4a8">SCI_REGISTERIMAGE</a> = 2405, 
<b>SCI_SETPRINTWRAPMODE</b> = 2406, 
<b>SCI_GETPRINTWRAPMODE</b> = 2407, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa4eca65e764b9d0ef2fb23d22bc872bcb">SCI_CLEARREGISTEREDIMAGES</a> = 2408, 
<b>SCI_STYLESETHOTSPOT</b> = 2409, 
<b>SCI_SETHOTSPOTACTIVEFORE</b> = 2410, 
<br />
&#160;&#160;<b>SCI_SETHOTSPOTACTIVEBACK</b> = 2411, 
<b>SCI_SETHOTSPOTACTIVEUNDERLINE</b> = 2412, 
<b>SCI_PARADOWN</b> = 2413, 
<br />
&#160;&#160;<b>SCI_PARADOWNEXTEND</b> = 2414, 
<b>SCI_PARAUP</b> = 2415, 
<b>SCI_PARAUPEXTEND</b> = 2416, 
<br />
&#160;&#160;<b>SCI_POSITIONBEFORE</b> = 2417, 
<b>SCI_POSITIONAFTER</b> = 2418, 
<b>SCI_COPYRANGE</b> = 2419, 
<br />
&#160;&#160;<b>SCI_COPYTEXT</b> = 2420, 
<b>SCI_SETHOTSPOTSINGLELINE</b> = 2421, 
<b>SCI_SETSELECTIONMODE</b> = 2422, 
<br />
&#160;&#160;<b>SCI_GETSELECTIONMODE</b> = 2423, 
<b>SCI_GETLINESELSTARTPOSITION</b> = 2424, 
<b>SCI_GETLINESELENDPOSITION</b> = 2425, 
<br />
&#160;&#160;<b>SCI_LINEDOWNRECTEXTEND</b> = 2426, 
<b>SCI_LINEUPRECTEXTEND</b> = 2427, 
<b>SCI_CHARLEFTRECTEXTEND</b> = 2428, 
<br />
&#160;&#160;<b>SCI_CHARRIGHTRECTEXTEND</b> = 2429, 
<b>SCI_HOMERECTEXTEND</b> = 2430, 
<b>SCI_VCHOMERECTEXTEND</b> = 2431, 
<br />
&#160;&#160;<b>SCI_LINEENDRECTEXTEND</b> = 2432, 
<b>SCI_PAGEUPRECTEXTEND</b> = 2433, 
<b>SCI_PAGEDOWNRECTEXTEND</b> = 2434, 
<br />
&#160;&#160;<b>SCI_STUTTEREDPAGEUP</b> = 2435, 
<b>SCI_STUTTEREDPAGEUPEXTEND</b> = 2436, 
<b>SCI_STUTTEREDPAGEDOWN</b> = 2437, 
<br />
&#160;&#160;<b>SCI_STUTTEREDPAGEDOWNEXTEND</b> = 2438, 
<b>SCI_WORDLEFTEND</b> = 2439, 
<b>SCI_WORDLEFTENDEXTEND</b> = 2440, 
<br />
&#160;&#160;<b>SCI_WORDRIGHTEND</b> = 2441, 
<b>SCI_WORDRIGHTENDEXTEND</b> = 2442, 
<b>SCI_SETWHITESPACECHARS</b> = 2443, 
<br />
&#160;&#160;<b>SCI_SETCHARSDEFAULT</b> = 2444, 
<b>SCI_AUTOCGETCURRENT</b> = 2445, 
<b>SCI_ALLOCATE</b> = 2446, 
<br />
&#160;&#160;<b>SCI_HOMEWRAP</b> = 2349, 
<b>SCI_HOMEWRAPEXTEND</b> = 2450, 
<b>SCI_LINEENDWRAP</b> = 2451, 
<br />
&#160;&#160;<b>SCI_LINEENDWRAPEXTEND</b> = 2452, 
<b>SCI_VCHOMEWRAP</b> = 2453, 
<b>SCI_VCHOMEWRAPEXTEND</b> = 2454, 
<br />
&#160;&#160;<b>SCI_LINECOPY</b> = 2455, 
<b>SCI_FINDCOLUMN</b> = 2456, 
<b>SCI_GETCARETSTICKY</b> = 2457, 
<br />
&#160;&#160;<b>SCI_SETCARETSTICKY</b> = 2458, 
<b>SCI_TOGGLECARETSTICKY</b> = 2459, 
<b>SCI_SETWRAPVISUALFLAGS</b> = 2460, 
<br />
&#160;&#160;<b>SCI_GETWRAPVISUALFLAGS</b> = 2461, 
<b>SCI_SETWRAPVISUALFLAGSLOCATION</b> = 2462, 
<b>SCI_GETWRAPVISUALFLAGSLOCATION</b> = 2463, 
<br />
&#160;&#160;<b>SCI_SETWRAPSTARTINDENT</b> = 2464, 
<b>SCI_GETWRAPSTARTINDENT</b> = 2465, 
<b>SCI_MARKERADDSET</b> = 2466, 
<br />
&#160;&#160;<b>SCI_SETPASTECONVERTENDINGS</b> = 2467, 
<b>SCI_GETPASTECONVERTENDINGS</b> = 2468, 
<b>SCI_SELECTIONDUPLICATE</b> = 2469, 
<br />
&#160;&#160;<b>SCI_SETCARETLINEBACKALPHA</b> = 2470, 
<b>SCI_GETCARETLINEBACKALPHA</b> = 2471, 
<b>SCI_SETWRAPINDENTMODE</b> = 2472, 
<br />
&#160;&#160;<b>SCI_GETWRAPINDENTMODE</b> = 2473, 
<b>SCI_MARKERSETALPHA</b> = 2476, 
<b>SCI_GETSELALPHA</b> = 2477, 
<br />
&#160;&#160;<b>SCI_SETSELALPHA</b> = 2478, 
<b>SCI_GETSELEOLFILLED</b> = 2479, 
<b>SCI_SETSELEOLFILLED</b> = 2480, 
<br />
&#160;&#160;<b>SCI_STYLEGETFORE</b> = 2481, 
<b>SCI_STYLEGETBACK</b> = 2482, 
<b>SCI_STYLEGETBOLD</b> = 2483, 
<br />
&#160;&#160;<b>SCI_STYLEGETITALIC</b> = 2484, 
<b>SCI_STYLEGETSIZE</b> = 2485, 
<b>SCI_STYLEGETFONT</b> = 2486, 
<br />
&#160;&#160;<b>SCI_STYLEGETEOLFILLED</b> = 2487, 
<b>SCI_STYLEGETUNDERLINE</b> = 2488, 
<b>SCI_STYLEGETCASE</b> = 2489, 
<br />
&#160;&#160;<b>SCI_STYLEGETCHARACTERSET</b> = 2490, 
<b>SCI_STYLEGETVISIBLE</b> = 2491, 
<b>SCI_STYLEGETCHANGEABLE</b> = 2492, 
<br />
&#160;&#160;<b>SCI_STYLEGETHOTSPOT</b> = 2493, 
<b>SCI_GETHOTSPOTACTIVEFORE</b> = 2494, 
<b>SCI_GETHOTSPOTACTIVEBACK</b> = 2495, 
<br />
&#160;&#160;<b>SCI_GETHOTSPOTACTIVEUNDERLINE</b> = 2496, 
<b>SCI_GETHOTSPOTSINGLELINE</b> = 2497, 
<b>SCI_BRACEHIGHLIGHTINDICATOR</b> = 2498, 
<br />
&#160;&#160;<b>SCI_BRACEBADLIGHTINDICATOR</b> = 2499, 
<b>SCI_SETINDICATORCURRENT</b> = 2500, 
<b>SCI_GETINDICATORCURRENT</b> = 2501, 
<br />
&#160;&#160;<b>SCI_SETINDICATORVALUE</b> = 2502, 
<b>SCI_GETINDICATORVALUE</b> = 2503, 
<b>SCI_INDICATORFILLRANGE</b> = 2504, 
<br />
&#160;&#160;<b>SCI_INDICATORCLEARRANGE</b> = 2505, 
<b>SCI_INDICATORALLONFOR</b> = 2506, 
<b>SCI_INDICATORVALUEAT</b> = 2507, 
<br />
&#160;&#160;<b>SCI_INDICATORSTART</b> = 2508, 
<b>SCI_INDICATOREND</b> = 2509, 
<b>SCI_INDICSETUNDER</b> = 2510, 
<br />
&#160;&#160;<b>SCI_INDICGETUNDER</b> = 2511, 
<b>SCI_SETCARETSTYLE</b> = 2512, 
<b>SCI_GETCARETSTYLE</b> = 2513, 
<br />
&#160;&#160;<b>SCI_SETPOSITIONCACHE</b> = 2514, 
<b>SCI_GETPOSITIONCACHE</b> = 2515, 
<b>SCI_SETSCROLLWIDTHTRACKING</b> = 2516, 
<br />
&#160;&#160;<b>SCI_GETSCROLLWIDTHTRACKING</b> = 2517, 
<b>SCI_DELWORDRIGHTEND</b> = 2518, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa0fd449316fa24a3cb53721cf17b9f684">SCI_COPYALLOWLINE</a> = 2519, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa4e6fc6b70c85f83622c9a17516bb2675">SCI_GETCHARACTERPOINTER</a> = 2520, 
<b>SCI_INDICSETALPHA</b> = 2523, 
<b>SCI_INDICGETALPHA</b> = 2524, 
<br />
&#160;&#160;<b>SCI_SETEXTRAASCENT</b> = 2525, 
<b>SCI_GETEXTRAASCENT</b> = 2526, 
<b>SCI_SETEXTRADESCENT</b> = 2527, 
<br />
&#160;&#160;<b>SCI_GETEXTRADESCENT</b> = 2528, 
<b>SCI_MARKERSYMBOLDEFINED</b> = 2529, 
<b>SCI_MARGINSETTEXT</b> = 2530, 
<br />
&#160;&#160;<b>SCI_MARGINGETTEXT</b> = 2531, 
<b>SCI_MARGINSETSTYLE</b> = 2532, 
<b>SCI_MARGINGETSTYLE</b> = 2533, 
<br />
&#160;&#160;<b>SCI_MARGINSETSTYLES</b> = 2534, 
<b>SCI_MARGINGETSTYLES</b> = 2535, 
<b>SCI_MARGINTEXTCLEARALL</b> = 2536, 
<br />
&#160;&#160;<b>SCI_MARGINSETSTYLEOFFSET</b> = 2537, 
<b>SCI_MARGINGETSTYLEOFFSET</b> = 2538, 
<b>SCI_SETMARGINOPTIONS</b> = 2539, 
<br />
&#160;&#160;<b>SCI_ANNOTATIONSETTEXT</b> = 2540, 
<b>SCI_ANNOTATIONGETTEXT</b> = 2541, 
<b>SCI_ANNOTATIONSETSTYLE</b> = 2542, 
<br />
&#160;&#160;<b>SCI_ANNOTATIONGETSTYLE</b> = 2543, 
<b>SCI_ANNOTATIONSETSTYLES</b> = 2544, 
<b>SCI_ANNOTATIONGETSTYLES</b> = 2545, 
<br />
&#160;&#160;<b>SCI_ANNOTATIONGETLINES</b> = 2546, 
<b>SCI_ANNOTATIONCLEARALL</b> = 2547, 
<b>SCI_ANNOTATIONSETVISIBLE</b> = 2548, 
<br />
&#160;&#160;<b>SCI_ANNOTATIONGETVISIBLE</b> = 2549, 
<b>SCI_ANNOTATIONSETSTYLEOFFSET</b> = 2550, 
<b>SCI_ANNOTATIONGETSTYLEOFFSET</b> = 2551, 
<br />
&#160;&#160;<b>SCI_RELEASEALLEXTENDEDSTYLES</b> = 2552, 
<b>SCI_ALLOCATEEXTENDEDSTYLES</b> = 2553, 
<b>SCI_SETEMPTYSELECTION</b> = 2556, 
<br />
&#160;&#160;<b>SCI_GETMARGINOPTIONS</b> = 2557, 
<b>SCI_INDICSETOUTLINEALPHA</b> = 2558, 
<b>SCI_INDICGETOUTLINEALPHA</b> = 2559, 
<br />
&#160;&#160;<b>SCI_ADDUNDOACTION</b> = 2560, 
<b>SCI_CHARPOSITIONFROMPOINT</b> = 2561, 
<b>SCI_CHARPOSITIONFROMPOINTCLOSE</b> = 2562, 
<br />
&#160;&#160;<b>SCI_SETMULTIPLESELECTION</b> = 2563, 
<b>SCI_GETMULTIPLESELECTION</b> = 2564, 
<b>SCI_SETADDITIONALSELECTIONTYPING</b> = 2565, 
<br />
&#160;&#160;<b>SCI_GETADDITIONALSELECTIONTYPING</b> = 2566, 
<b>SCI_SETADDITIONALCARETSBLINK</b> = 2567, 
<b>SCI_GETADDITIONALCARETSBLINK</b> = 2568, 
<br />
&#160;&#160;<b>SCI_SCROLLRANGE</b> = 2569, 
<b>SCI_GETSELECTIONS</b> = 2570, 
<b>SCI_CLEARSELECTIONS</b> = 2571, 
<br />
&#160;&#160;<b>SCI_SETSELECTION</b> = 2572, 
<b>SCI_ADDSELECTION</b> = 2573, 
<b>SCI_SETMAINSELECTION</b> = 2574, 
<br />
&#160;&#160;<b>SCI_GETMAINSELECTION</b> = 2575, 
<b>SCI_SETSELECTIONNCARET</b> = 2576, 
<b>SCI_GETSELECTIONNCARET</b> = 2577, 
<br />
&#160;&#160;<b>SCI_SETSELECTIONNANCHOR</b> = 2578, 
<b>SCI_GETSELECTIONNANCHOR</b> = 2579, 
<b>SCI_SETSELECTIONNCARETVIRTUALSPACE</b> = 2580, 
<br />
&#160;&#160;<b>SCI_GETSELECTIONNCARETVIRTUALSPACE</b> = 2581, 
<b>SCI_SETSELECTIONNANCHORVIRTUALSPACE</b> = 2582, 
<b>SCI_GETSELECTIONNANCHORVIRTUALSPACE</b> = 2583, 
<br />
&#160;&#160;<b>SCI_SETSELECTIONNSTART</b> = 2584, 
<b>SCI_GETSELECTIONNSTART</b> = 2585, 
<b>SCI_SETSELECTIONNEND</b> = 2586, 
<br />
&#160;&#160;<b>SCI_GETSELECTIONNEND</b> = 2587, 
<b>SCI_SETRECTANGULARSELECTIONCARET</b> = 2588, 
<b>SCI_GETRECTANGULARSELECTIONCARET</b> = 2589, 
<br />
&#160;&#160;<b>SCI_SETRECTANGULARSELECTIONANCHOR</b> = 2590, 
<b>SCI_GETRECTANGULARSELECTIONANCHOR</b> = 2591, 
<b>SCI_SETRECTANGULARSELECTIONCARETVIRTUALSPACE</b> = 2592, 
<br />
&#160;&#160;<b>SCI_GETRECTANGULARSELECTIONCARETVIRTUALSPACE</b> = 2593, 
<b>SCI_SETRECTANGULARSELECTIONANCHORVIRTUALSPACE</b> = 2594, 
<b>SCI_GETRECTANGULARSELECTIONANCHORVIRTUALSPACE</b> = 2595, 
<br />
&#160;&#160;<b>SCI_SETVIRTUALSPACEOPTIONS</b> = 2596, 
<b>SCI_GETVIRTUALSPACEOPTIONS</b> = 2597, 
<b>SCI_SETRECTANGULARSELECTIONMODIFIER</b> = 2598, 
<br />
&#160;&#160;<b>SCI_GETRECTANGULARSELECTIONMODIFIER</b> = 2599, 
<b>SCI_SETADDITIONALSELFORE</b> = 2600, 
<b>SCI_SETADDITIONALSELBACK</b> = 2601, 
<br />
&#160;&#160;<b>SCI_SETADDITIONALSELALPHA</b> = 2602, 
<b>SCI_GETADDITIONALSELALPHA</b> = 2603, 
<b>SCI_SETADDITIONALCARETFORE</b> = 2604, 
<br />
&#160;&#160;<b>SCI_GETADDITIONALCARETFORE</b> = 2605, 
<b>SCI_ROTATESELECTION</b> = 2606, 
<b>SCI_SWAPMAINANCHORCARET</b> = 2607, 
<br />
&#160;&#160;<b>SCI_SETADDITIONALCARETSVISIBLE</b> = 2608, 
<b>SCI_GETADDITIONALCARETSVISIBLE</b> = 2609, 
<b>SCI_AUTOCGETCURRENTTEXT</b> = 2610, 
<br />
&#160;&#160;<b>SCI_SETFONTQUALITY</b> = 2611, 
<b>SCI_GETFONTQUALITY</b> = 2612, 
<b>SCI_SETFIRSTVISIBLELINE</b> = 2613, 
<br />
&#160;&#160;<b>SCI_SETMULTIPASTE</b> = 2614, 
<b>SCI_GETMULTIPASTE</b> = 2615, 
<b>SCI_GETTAG</b> = 2616, 
<br />
&#160;&#160;<b>SCI_CHANGELEXERSTATE</b> = 2617, 
<b>SCI_CONTRACTEDFOLDNEXT</b> = 2618, 
<b>SCI_VERTICALCENTRECARET</b> = 2619, 
<br />
&#160;&#160;<b>SCI_MOVESELECTEDLINESUP</b> = 2620, 
<b>SCI_MOVESELECTEDLINESDOWN</b> = 2621, 
<b>SCI_SETIDENTIFIER</b> = 2622, 
<br />
&#160;&#160;<b>SCI_GETIDENTIFIER</b> = 2623, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa93791e94e6f6a9382f1f7e29f341f342">SCI_RGBAIMAGESETWIDTH</a> = 2624, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aabf4b5d1cf3d1aa52c010b489c2ccffc6">SCI_RGBAIMAGESETHEIGHT</a> = 2625, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa8ff17201e6d0cb9fe6e738a7a2e81932">SCI_MARKERDEFINERGBAIMAGE</a> = 2626, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaf1e4de8ebec57382f46449112d4f6821">SCI_REGISTERRGBAIMAGE</a> = 2627, 
<b>SCI_SCROLLTOSTART</b> = 2628, 
<br />
&#160;&#160;<b>SCI_SCROLLTOEND</b> = 2629, 
<b>SCI_SETTECHNOLOGY</b> = 2630, 
<b>SCI_GETTECHNOLOGY</b> = 2631, 
<br />
&#160;&#160;<b>SCI_CREATELOADER</b> = 2632, 
<b>SCI_COUNTCHARACTERS</b> = 2633, 
<b>SCI_AUTOCSETCASEINSENSITIVEBEHAVIOUR</b> = 2634, 
<br />
&#160;&#160;<b>SCI_AUTOCGETCASEINSENSITIVEBEHAVIOUR</b> = 2635, 
<b>SCI_AUTOCSETMULTI</b> = 2636, 
<b>SCI_AUTOCGETMULTI</b> = 2637, 
<br />
&#160;&#160;<b>SCI_FINDINDICATORSHOW</b> = 2640, 
<b>SCI_FINDINDICATORFLASH</b> = 2641, 
<b>SCI_FINDINDICATORHIDE</b> = 2642, 
<br />
&#160;&#160;<b>SCI_GETRANGEPOINTER</b> = 2643, 
<b>SCI_GETGAPPOSITION</b> = 2644, 
<b>SCI_DELETERANGE</b> = 2645, 
<br />
&#160;&#160;<b>SCI_GETWORDCHARS</b> = 2646, 
<b>SCI_GETWHITESPACECHARS</b> = 2647, 
<b>SCI_SETPUNCTUATIONCHARS</b> = 2648, 
<br />
&#160;&#160;<b>SCI_GETPUNCTUATIONCHARS</b> = 2649, 
<b>SCI_GETSELECTIONEMPTY</b> = 2650, 
<b>SCI_RGBAIMAGESETSCALE</b> = 2651, 
<br />
&#160;&#160;<b>SCI_VCHOMEDISPLAY</b> = 2652, 
<b>SCI_VCHOMEDISPLAYEXTEND</b> = 2653, 
<b>SCI_GETCARETLINEVISIBLEALWAYS</b> = 2654, 
<br />
&#160;&#160;<b>SCI_SETCARETLINEVISIBLEALWAYS</b> = 2655, 
<b>SCI_SETLINEENDTYPESALLOWED</b> = 2656, 
<b>SCI_GETLINEENDTYPESALLOWED</b> = 2657, 
<br />
&#160;&#160;<b>SCI_GETLINEENDTYPESACTIVE</b> = 2658, 
<b>SCI_AUTOCSETORDER</b> = 2660, 
<b>SCI_AUTOCGETORDER</b> = 2661, 
<br />
&#160;&#160;<b>SCI_FOLDALL</b> = 2662, 
<b>SCI_SETAUTOMATICFOLD</b> = 2663, 
<b>SCI_GETAUTOMATICFOLD</b> = 2664, 
<br />
&#160;&#160;<b>SCI_SETREPRESENTATION</b> = 2665, 
<b>SCI_GETREPRESENTATION</b> = 2666, 
<b>SCI_CLEARREPRESENTATION</b> = 2667, 
<br />
&#160;&#160;<b>SCI_SETMOUSESELECTIONRECTANGULARSWITCH</b> = 2668, 
<b>SCI_GETMOUSESELECTIONRECTANGULARSWITCH</b> = 2669, 
<b>SCI_POSITIONRELATIVE</b> = 2670, 
<br />
&#160;&#160;<b>SCI_DROPSELECTIONN</b> = 2671, 
<b>SCI_CHANGEINSERTION</b> = 2672, 
<b>SCI_GETPHASESDRAW</b> = 2673, 
<br />
&#160;&#160;<b>SCI_SETPHASESDRAW</b> = 2674, 
<b>SCI_CLEARTABSTOPS</b> = 2675, 
<b>SCI_ADDTABSTOP</b> = 2676, 
<br />
&#160;&#160;<b>SCI_GETNEXTTABSTOP</b> = 2677, 
<b>SCI_GETIMEINTERACTION</b> = 2678, 
<b>SCI_SETIMEINTERACTION</b> = 2679, 
<br />
&#160;&#160;<b>SCI_INDICSETHOVERSTYLE</b> = 2680, 
<b>SCI_INDICGETHOVERSTYLE</b> = 2681, 
<b>SCI_INDICSETHOVERFORE</b> = 2682, 
<br />
&#160;&#160;<b>SCI_INDICGETHOVERFORE</b> = 2683, 
<b>SCI_INDICSETFLAGS</b> = 2684, 
<b>SCI_INDICGETFLAGS</b> = 2685, 
<br />
&#160;&#160;<b>SCI_SETTARGETRANGE</b> = 2686, 
<b>SCI_GETTARGETTEXT</b> = 2687, 
<b>SCI_MULTIPLESELECTADDNEXT</b> = 2688, 
<br />
&#160;&#160;<b>SCI_MULTIPLESELECTADDEACH</b> = 2689, 
<b>SCI_TARGETWHOLEDOCUMENT</b> = 2690, 
<b>SCI_ISRANGEWORD</b> = 2691, 
<br />
&#160;&#160;<b>SCI_SETIDLESTYLING</b> = 2692, 
<b>SCI_GETIDLESTYLING</b> = 2693, 
<b>SCI_MULTIEDGEADDLINE</b> = 2694, 
<br />
&#160;&#160;<b>SCI_MULTIEDGECLEARALL</b> = 2695, 
<b>SCI_SETMOUSEWHEELCAPTURES</b> = 2696, 
<b>SCI_GETMOUSEWHEELCAPTURES</b> = 2697, 
<br />
&#160;&#160;<b>SCI_GETTABDRAWMODE</b> = 2698, 
<b>SCI_SETTABDRAWMODE</b> = 2699, 
<b>SCI_TOGGLEFOLDSHOWTEXT</b> = 2700, 
<br />
&#160;&#160;<b>SCI_FOLDDISPLAYTEXTSETSTYLE</b> = 2701, 
<b>SCI_SETACCESSIBILITY</b> = 2702, 
<b>SCI_GETACCESSIBILITY</b> = 2703, 
<br />
&#160;&#160;<b>SCI_GETCARETLINEFRAME</b> = 2704, 
<b>SCI_SETCARETLINEFRAME</b> = 2705, 
<b>SCI_STARTRECORD</b> = 3001, 
<br />
&#160;&#160;<b>SCI_STOPRECORD</b> = 3002, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa29b928416c21edb11e32d4325764fcc7">SCI_SETLEXER</a> = 4001, 
<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaf625e0ecead2e1d0bc3e0cefe2e8954a">SCI_GETLEXER</a> = 4002, 
<br />
&#160;&#160;<b>SCI_COLOURISE</b> = 4003, 
<b>SCI_SETPROPERTY</b> = 4004, 
<b>SCI_SETKEYWORDS</b> = 4005, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa6309b2b8bf3813c1041d31cb54ce3feb">SCI_SETLEXERLANGUAGE</a> = 4006, 
<b>SCI_LOADLEXERLIBRARY</b> = 4007, 
<b>SCI_GETPROPERTY</b> = 4008, 
<br />
&#160;&#160;<b>SCI_GETPROPERTYEXPANDED</b> = 4009, 
<b>SCI_GETPROPERTYINT</b> = 4010, 
<b>SCI_GETSTYLEBITSNEEDED</b> = 4011, 
<br />
&#160;&#160;<b>SCI_GETLEXERLANGUAGE</b> = 4012, 
<b>SCI_PRIVATELEXERCALL</b> = 4013, 
<b>SCI_PROPERTYNAMES</b> = 4014, 
<br />
&#160;&#160;<b>SCI_PROPERTYTYPE</b> = 4015, 
<b>SCI_DESCRIBEPROPERTY</b> = 4016, 
<b>SCI_DESCRIBEKEYWORDSETS</b> = 4017, 
<br />
&#160;&#160;<b>SCI_GETLINEENDTYPESSUPPORTED</b> = 4018, 
<b>SCI_ALLOCATESUBSTYLES</b> = 4020, 
<b>SCI_GETSUBSTYLESSTART</b> = 4021, 
<br />
&#160;&#160;<b>SCI_GETSUBSTYLESLENGTH</b> = 4022, 
<b>SCI_GETSTYLEFROMSUBSTYLE</b> = 4027, 
<b>SCI_GETPRIMARYSTYLEFROMSTYLE</b> = 4028, 
<br />
&#160;&#160;<b>SCI_FREESUBSTYLES</b> = 4023, 
<b>SCI_SETIDENTIFIERS</b> = 4024, 
<b>SCI_DISTANCETOSECONDARYSTYLES</b> = 4025, 
<br />
&#160;&#160;<b>SCI_GETSUBSTYLEBASES</b> = 4026, 
<b>SCI_GETLINECHARACTERINDEX</b> = 2710, 
<b>SCI_ALLOCATELINECHARACTERINDEX</b> = 2711, 
<br />
&#160;&#160;<b>SCI_RELEASELINECHARACTERINDEX</b> = 2712, 
<b>SCI_LINEFROMINDEXPOSITION</b> = 2713, 
<b>SCI_INDEXPOSITIONFROMLINE</b> = 2714, 
<br />
&#160;&#160;<b>SCI_COUNTCODEUNITS</b> = 2715, 
<b>SCI_POSITIONRELATIVECODEUNITS</b> = 2716, 
<b>SCI_GETNAMEDSTYLES</b> = 4029, 
<br />
&#160;&#160;<b>SCI_NAMEOFSTYLE</b> = 4030, 
<b>SCI_TAGSOFSTYLE</b> = 4031, 
<b>SCI_DESCRIPTIONOFSTYLE</b> = 4032, 
<br />
&#160;&#160;<b>SCI_GETMOVEEXTENDSSELECTION</b> = 2706, 
<b>SCI_SETCOMMANDEVENTS</b> = 2717, 
<b>SCI_GETCOMMANDEVENTS</b> = 2718, 
<br />
&#160;&#160;<b>SCI_GETDOCUMENTOPTIONS</b> = 2379
<br />
 }</td></tr>
<tr class="separator:ad9c35f7540b2457103db9cf8c877784a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a940b9c0cd2c56db6cd810597a8197ebc"><td class="memItemLeft" align="right" valign="top"><a id="a940b9c0cd2c56db6cd810597a8197ebc"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_AC_FILLUP</b> = 1, 
<b>SC_AC_DOUBLECLICK</b> = 2, 
<b>SC_AC_TAB</b> = 3, 
<br />
&#160;&#160;<b>SC_AC_NEWLINE</b> = 4, 
<b>SC_AC_COMMAND</b> = 5
<br />
 }</td></tr>
<tr class="separator:a940b9c0cd2c56db6cd810597a8197ebc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5cd5f6e631ca6d6e242f178f8b4582fd"><td class="memItemLeft" align="right" valign="top"><a id="a5cd5f6e631ca6d6e242f178f8b4582fd"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_ALPHA_TRANSPARENT</b> = 0, 
<b>SC_ALPHA_OPAQUE</b> = 255, 
<b>SC_ALPHA_NOALPHA</b> = 256
 }</td></tr>
<tr class="separator:a5cd5f6e631ca6d6e242f178f8b4582fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a306e03d6bdd11d645a88ef7284ebb8d3"><td class="memItemLeft" align="right" valign="top"><a id="a306e03d6bdd11d645a88ef7284ebb8d3"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_CARETSTICKY_OFF</b> = 0, 
<b>SC_CARETSTICKY_ON</b> = 1, 
<b>SC_CARETSTICKY_WHITESPACE</b> = 2
 }</td></tr>
<tr class="separator:a306e03d6bdd11d645a88ef7284ebb8d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9e54839f8ef0a92fde4e81c139f81567"><td class="memItemLeft" align="right" valign="top"><a id="a9e54839f8ef0a92fde4e81c139f81567"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_DOCUMENTOPTION_DEFAULT</b> = 0x0000, 
<b>SC_DOCUMENTOPTION_STYLES_NONE</b> = 0x0001, 
<b>SC_DOCUMENTOPTION_TEXT_LARGE</b> = 0x0100
 }</td></tr>
<tr class="separator:a9e54839f8ef0a92fde4e81c139f81567"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adec2f0c26f1def79e14675e3a2db561e"><td class="memItemLeft" align="right" valign="top"><a id="adec2f0c26f1def79e14675e3a2db561e"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_EFF_QUALITY_MASK</b> = 0x0f, 
<b>SC_EFF_QUALITY_DEFAULT</b> = 0, 
<b>SC_EFF_QUALITY_NON_ANTIALIASED</b> = 1, 
<br />
&#160;&#160;<b>SC_EFF_QUALITY_ANTIALIASED</b> = 2, 
<b>SC_EFF_QUALITY_LCD_OPTIMIZED</b> = 3
<br />
 }</td></tr>
<tr class="separator:adec2f0c26f1def79e14675e3a2db561e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a33b43b49dd2702a5518628b021e99550"><td class="memItemLeft" align="right" valign="top"><a id="a33b43b49dd2702a5518628b021e99550"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_IDLESTYLING_NONE</b> = 0, 
<b>SC_IDLESTYLING_TOVISIBLE</b> = 1, 
<b>SC_IDLESTYLING_AFTERVISIBLE</b> = 2, 
<br />
&#160;&#160;<b>SC_IDLESTYLING_ALL</b> = 3
<br />
 }</td></tr>
<tr class="separator:a33b43b49dd2702a5518628b021e99550"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac3b47eb7780e99799b367f2bb76c55ac"><td class="memItemLeft" align="right" valign="top"><a id="ac3b47eb7780e99799b367f2bb76c55ac"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_IME_WINDOWED</b> = 0, 
<b>SC_IME_INLINE</b> = 1
 }</td></tr>
<tr class="separator:ac3b47eb7780e99799b367f2bb76c55ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a80a28afa239736b78d5d1e95229dc56e"><td class="memItemLeft" align="right" valign="top"><a id="a80a28afa239736b78d5d1e95229dc56e"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_LINECHARACTERINDEX_NONE</b> = 0, 
<b>SC_LINECHARACTERINDEX_UTF32</b> = 1, 
<b>SC_LINECHARACTERINDEX_UTF16</b> = 2
 }</td></tr>
<tr class="separator:a80a28afa239736b78d5d1e95229dc56e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0604bf30b2d505eaf86f61b89f8d25b3"><td class="memItemLeft" align="right" valign="top"><a id="a0604bf30b2d505eaf86f61b89f8d25b3"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_MARGINOPTION_NONE</b> = 0x00, 
<b>SC_MARGINOPTION_SUBLINESELECT</b> = 0x01
 }</td></tr>
<tr class="separator:a0604bf30b2d505eaf86f61b89f8d25b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae47504bece542b5230e78e74a7c481f6"><td class="memItemLeft" align="right" valign="top"><a id="ae47504bece542b5230e78e74a7c481f6"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_MULTIAUTOC_ONCE</b> = 0, 
<b>SC_MULTIAUTOC_EACH</b> = 1
 }</td></tr>
<tr class="separator:ae47504bece542b5230e78e74a7c481f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aabe379c165d6e569e684834adb98ec58"><td class="memItemLeft" align="right" valign="top"><a id="aabe379c165d6e569e684834adb98ec58"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_MULTIPASTE_ONCE</b> = 0, 
<b>SC_MULTIPASTE_EACH</b> = 1
 }</td></tr>
<tr class="separator:aabe379c165d6e569e684834adb98ec58"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a46b22a650111e20d5c3fc18fdf693342"><td class="memItemLeft" align="right" valign="top"><a id="a46b22a650111e20d5c3fc18fdf693342"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_POPUP_NEVER</b> = 0, 
<b>SC_POPUP_ALL</b> = 1, 
<b>SC_POPUP_TEXT</b> = 2
 }</td></tr>
<tr class="separator:a46b22a650111e20d5c3fc18fdf693342"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad0a6f5e5ad05455480c3530435dd6aa"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_SEL_STREAM</b> = 0, 
<b>SC_SEL_RECTANGLE</b> = 1, 
<b>SC_SEL_LINES</b> = 2, 
<br />
&#160;&#160;<b>SC_SEL_THIN</b> = 3
<br />
 }</td></tr>
<tr class="separator:aad0a6f5e5ad05455480c3530435dd6aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab577311795830783d5b1dc6a0ffa11e8"><td class="memItemLeft" align="right" valign="top"><a id="ab577311795830783d5b1dc6a0ffa11e8"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_STATUS_OK</b> = 0, 
<b>SC_STATUS_FAILURE</b> = 1, 
<b>SC_STATUS_BADALLOC</b> = 2, 
<br />
&#160;&#160;<b>SC_STATUS_WARN_START</b> = 1000, 
<b>SC_STATUS_WARNREGEX</b> = 1001
<br />
 }</td></tr>
<tr class="separator:ab577311795830783d5b1dc6a0ffa11e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab30e53e4a12ff687e76089331892ec7f"><td class="memItemLeft" align="right" valign="top"><a id="ab30e53e4a12ff687e76089331892ec7f"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_TYPE_BOOLEAN</b> = 0, 
<b>SC_TYPE_INTEGER</b> = 1, 
<b>SC_TYPE_STRING</b> = 2
 }</td></tr>
<tr class="separator:ab30e53e4a12ff687e76089331892ec7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5161ccc0c1bcd30e9f607dcbd6120007"><td class="memItemLeft" align="right" valign="top"><a id="a5161ccc0c1bcd30e9f607dcbd6120007"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_UPDATE_CONTENT</b> = 0x01, 
<b>SC_UPDATE_SELECTION</b> = 0x02, 
<b>SC_UPDATE_V_SCROLL</b> = 0x04, 
<br />
&#160;&#160;<b>SC_UPDATE_H_SCROLL</b> = 0x08
<br />
 }</td></tr>
<tr class="separator:a5161ccc0c1bcd30e9f607dcbd6120007"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba7442e8abfc6e2645ffda04cff6354e"><td class="memItemLeft" align="right" valign="top"><a id="aba7442e8abfc6e2645ffda04cff6354e"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_WRAPVISUALFLAG_NONE</b> = 0x0000, 
<b>SC_WRAPVISUALFLAG_END</b> = 0x0001, 
<b>SC_WRAPVISUALFLAG_START</b> = 0x0002, 
<br />
&#160;&#160;<b>SC_WRAPVISUALFLAG_MARGIN</b> = 0x0004
<br />
 }</td></tr>
<tr class="separator:aba7442e8abfc6e2645ffda04cff6354e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a36708d50e92db731fc6ef4883a2e3a54"><td class="memItemLeft" align="right" valign="top"><a id="a36708d50e92db731fc6ef4883a2e3a54"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_WRAPVISUALFLAGLOC_DEFAULT</b> = 0x0000, 
<b>SC_WRAPVISUALFLAGLOC_END_BY_TEXT</b> = 0x0001, 
<b>SC_WRAPVISUALFLAGLOC_START_BY_TEXT</b> = 0x0002
 }</td></tr>
<tr class="separator:a36708d50e92db731fc6ef4883a2e3a54"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a24fcbfa6a5b9d4ec51cc3df2bc80b7fb"><td class="memItemLeft" align="right" valign="top"><a id="a24fcbfa6a5b9d4ec51cc3df2bc80b7fb"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SCTD_LONGARROW</b> = 0, 
<b>SCTD_STRIKEOUT</b> = 1
 }</td></tr>
<tr class="separator:a24fcbfa6a5b9d4ec51cc3df2bc80b7fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0d96e140124f18e23b27f32f67001f1"><td class="memItemLeft" align="right" valign="top"><a id="ae0d96e140124f18e23b27f32f67001f1"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SCVS_NONE</b> = 0, 
<b>SCVS_RECTANGULARSELECTION</b> = 1, 
<b>SCVS_USERACCESSIBLE</b> = 2, 
<br />
&#160;&#160;<b>SCVS_NOWRAPLINESTART</b> = 4
<br />
 }</td></tr>
<tr class="separator:ae0d96e140124f18e23b27f32f67001f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9e453519ca9312f3b8c4cd4abe9f86d0"><td class="memItemLeft" align="right" valign="top"><a id="a9e453519ca9312f3b8c4cd4abe9f86d0"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SCWS_INVISIBLE</b> = 0, 
<b>SCWS_VISIBLEALWAYS</b> = 1, 
<b>SCWS_VISIBLEAFTERINDENT</b> = 2, 
<br />
&#160;&#160;<b>SCWS_VISIBLEONLYININDENT</b> = 3
<br />
 }</td></tr>
<tr class="separator:a9e453519ca9312f3b8c4cd4abe9f86d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7af2638f7d483966ac82628045884c55"><td class="memItemLeft" align="right" valign="top"><a id="a7af2638f7d483966ac82628045884c55"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_EOL_CRLF</b> = 0, 
<b>SC_EOL_CR</b> = 1, 
<b>SC_EOL_LF</b> = 2
 }</td></tr>
<tr class="separator:a7af2638f7d483966ac82628045884c55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abfcc7ea6119f3dca1a9726228f55f3f9"><td class="memItemLeft" align="right" valign="top"><a id="abfcc7ea6119f3dca1a9726228f55f3f9"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_CP_DBCS</b> = 1, 
<b>SC_CP_UTF8</b> = 65001
 }</td></tr>
<tr class="separator:abfcc7ea6119f3dca1a9726228f55f3f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad004786b74db7858f6642c23447a214c"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cabf08078081d1fb79be98e1b5a6401ec3">SC_MARK_CIRCLE</a> = 0, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca39e5d4cae13901613bcfae619cd496b5">SC_MARK_ROUNDRECT</a> = 1, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca940ced5307e462959ce165d8717a31d4">SC_MARK_ARROW</a> = 2, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cafa1a0af509be0284f7c69df8134d85ca">SC_MARK_SMALLRECT</a> = 3, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca60f9340f78d0c386cb2df238849f121d">SC_MARK_SHORTARROW</a> = 4, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cacf6e7cca56bcd03b660b3590153d1075">SC_MARK_EMPTY</a> = 5, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca6f1caf375a3079d67c36998c1bd453a4">SC_MARK_ARROWDOWN</a> = 6, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca7baf71a4e105fbebbaa7803a3f722b0f">SC_MARK_MINUS</a> = 7, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cae324f61ed2740c6be760489cbaa69fb8">SC_MARK_PLUS</a> = 8, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca8770dbe317581062d5d1bcb85592b784">SC_MARK_VLINE</a> = 9, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214caf591d473d118d6fa98adf5e73fd9c61d">SC_MARK_LCORNER</a> = 10, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214caa9ae33880a1ee19ce4db6544bb61a84d">SC_MARK_TCORNER</a> = 11, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca6b210dd7ba9ae1b8c503965b6e9ada9a">SC_MARK_BOXPLUS</a> = 12, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca3582c3195c0533bca604a874ee1ecab8">SC_MARK_BOXPLUSCONNECTED</a> = 13, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cacebb22ccb805fa137c65eda743d32e0a">SC_MARK_BOXMINUS</a> = 14, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca8d928f238170d0765acb492d0e8f0f65">SC_MARK_BOXMINUSCONNECTED</a> = 15, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca85a6c4d68e4c936c46c8711f656d95ca">SC_MARK_LCORNERCURVE</a> = 16, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca787e7e471b489bda535116b75765acad">SC_MARK_TCORNERCURVE</a> = 17, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca222004d413ee607197204f26950f3a0c">SC_MARK_CIRCLEPLUS</a> = 18, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca5293176024a0bba9eeb54b061a2930f9">SC_MARK_CIRCLEPLUSCONNECTED</a> = 19, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca58fc2ba342cf7cc9e5f5e9a59d4319bc">SC_MARK_CIRCLEMINUS</a> = 20, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca2bbb4d3dea9e0b75ef9374c1c2b23c65">SC_MARK_CIRCLEMINUSCONNECTED</a> = 21, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca8a44e2cee896ee89527e1d026e8cd9ff">SC_MARK_BACKGROUND</a> = 22, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca961e0562a26ab763fba1bc1e92123b85">SC_MARK_DOTDOTDOT</a> = 23, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca6f07eeddc235e313c4ca597220c71a0c">SC_MARK_ARROWS</a> = 24, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca464ae044427aa620a0668510ff1430b9">SC_MARK_PIXMAP</a> = 25, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca4f29b2c933a525fe0a80f0a58ba7eb61">SC_MARK_FULLRECT</a> = 26, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca08a00107e2110dce658fe7cb10f75e58">SC_MARK_LEFTRECT</a> = 27, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca53a38f2234ab3e6df8d6cec09ecd7318">SC_MARK_AVAILABLE</a> = 28, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cadf0e9f9a2980c5e693c67819a64f132e">SC_MARK_UNDERLINE</a> = 29, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214caa1866931fbc9dea971e6ae3f5be83abd">SC_MARK_RGBAIMAGE</a> = 30, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca500892fc4eef318262b009f6eddc9eda">SC_MARK_BOOKMARK</a> = 31, 
<a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca8c649856f102e81a5caa5a92f28b38fd">SC_MARK_CHARACTER</a> = 10000
<br />
 }</td></tr>
<tr class="separator:ad004786b74db7858f6642c23447a214c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab3b56c8c14ecc496e46e2ee1dc26738"><td class="memItemLeft" align="right" valign="top"><a id="aab3b56c8c14ecc496e46e2ee1dc26738"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_MARKNUM_FOLDEREND</b> = 25, 
<b>SC_MARKNUM_FOLDEROPENMID</b> = 26, 
<b>SC_MARKNUM_FOLDERMIDTAIL</b> = 27, 
<br />
&#160;&#160;<b>SC_MARKNUM_FOLDERTAIL</b> = 28, 
<b>SC_MARKNUM_FOLDERSUB</b> = 29, 
<b>SC_MARKNUM_FOLDER</b> = 30, 
<br />
&#160;&#160;<b>SC_MARKNUM_FOLDEROPEN</b> = 31, 
<b>SC_MASK_FOLDERS</b> = 0xfe000000
<br />
 }</td></tr>
<tr class="separator:aab3b56c8c14ecc496e46e2ee1dc26738"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7ed107d6ace096e9026c31145c48b41"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41a7776c14d3a1424576a26a8da304b96bf">SC_MARGIN_SYMBOL</a> = 0, 
<a class="el" href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41a55a92a7661156a126c48237234251e1d">SC_MARGIN_NUMBER</a> = 1, 
<a class="el" href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41a68a70615f89282762ba21aa6ec629dac">SC_MARGIN_BACK</a> = 2, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41a2f3ac8cfede54b81db88e29b7f81e19c">SC_MARGIN_FORE</a> = 3, 
<a class="el" href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41aa29598ff9ba1349daee66560cdd692bd">SC_MARGIN_TEXT</a> = 4, 
<a class="el" href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41af99d2ba5aa3873f646a8eac1a889de6a">SC_MARGIN_RTEXT</a> = 5, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41aabd3cb3735935f9be890931a34d07989">SC_MARGIN_COLOUR</a> = 6
<br />
 }</td></tr>
<tr class="separator:ab7ed107d6ace096e9026c31145c48b41"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5435e1717ef698d5c0d689214d4b0f34"><td class="memItemLeft" align="right" valign="top"><a id="a5435e1717ef698d5c0d689214d4b0f34"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>STYLE_DEFAULT</b> = 32, 
<b>STYLE_LINENUMBER</b> = 33, 
<b>STYLE_BRACELIGHT</b> = 34, 
<br />
&#160;&#160;<b>STYLE_BRACEBAD</b> = 35, 
<b>STYLE_CONTROLCHAR</b> = 36, 
<b>STYLE_INDENTGUIDE</b> = 37, 
<br />
&#160;&#160;<b>STYLE_CALLTIP</b> = 38, 
<b>STYLE_FOLDDISPLAYTEXT</b> = 39, 
<b>STYLE_LASTPREDEFINED</b> = 39, 
<br />
&#160;&#160;<b>STYLE_MAX</b> = 255
<br />
 }</td></tr>
<tr class="separator:a5435e1717ef698d5c0d689214d4b0f34"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab2179a2af3123f8e759086f07be80cd2"><td class="memItemLeft" align="right" valign="top"><a id="ab2179a2af3123f8e759086f07be80cd2"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_CHARSET_ANSI</b> = 0, 
<b>SC_CHARSET_DEFAULT</b> = 1, 
<b>SC_CHARSET_BALTIC</b> = 186, 
<br />
&#160;&#160;<b>SC_CHARSET_CHINESEBIG5</b> = 136, 
<b>SC_CHARSET_EASTEUROPE</b> = 238, 
<b>SC_CHARSET_GB2312</b> = 134, 
<br />
&#160;&#160;<b>SC_CHARSET_GREEK</b> = 161, 
<b>SC_CHARSET_HANGUL</b> = 129, 
<b>SC_CHARSET_MAC</b> = 77, 
<br />
&#160;&#160;<b>SC_CHARSET_OEM</b> = 255, 
<b>SC_CHARSET_RUSSIAN</b> = 204, 
<b>SC_CHARSET_OEM866</b> = 866, 
<br />
&#160;&#160;<b>SC_CHARSET_CYRILLIC</b> = 1251, 
<b>SC_CHARSET_SHIFTJIS</b> = 128, 
<b>SC_CHARSET_SYMBOL</b> = 2, 
<br />
&#160;&#160;<b>SC_CHARSET_TURKISH</b> = 162, 
<b>SC_CHARSET_JOHAB</b> = 130, 
<b>SC_CHARSET_HEBREW</b> = 177, 
<br />
&#160;&#160;<b>SC_CHARSET_ARABIC</b> = 178, 
<b>SC_CHARSET_VIETNAMESE</b> = 163, 
<b>SC_CHARSET_THAI</b> = 222, 
<br />
&#160;&#160;<b>SC_CHARSET_8859_15</b> = 1000
<br />
 }</td></tr>
<tr class="separator:ab2179a2af3123f8e759086f07be80cd2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a864b0a91be0b1eb3143b30849474d9ee"><td class="memItemLeft" align="right" valign="top"><a id="a864b0a91be0b1eb3143b30849474d9ee"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_CASE_MIXED</b> = 0, 
<b>SC_CASE_UPPER</b> = 1, 
<b>SC_CASE_LOWER</b> = 2, 
<br />
&#160;&#160;<b>SC_CASE_CAMEL</b> = 3
<br />
 }</td></tr>
<tr class="separator:a864b0a91be0b1eb3143b30849474d9ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae92e21c6957f026dbfd00008348e8b50"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ae92e21c6957f026dbfd00008348e8b50ad814f18251426f392498fd2969e11d65">SC_IV_NONE</a> = 0, 
<a class="el" href="classQsciScintillaBase.html#ae92e21c6957f026dbfd00008348e8b50aa8b077f3d8af29de8fae45dfd0885298">SC_IV_REAL</a> = 1, 
<a class="el" href="classQsciScintillaBase.html#ae92e21c6957f026dbfd00008348e8b50a63693e8e8da215430f8b94630cbad3c0">SC_IV_LOOKFORWARD</a> = 2, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#ae92e21c6957f026dbfd00008348e8b50ac3746adc7ec9881c9a46be88e26417d1">SC_IV_LOOKBOTH</a> = 3
<br />
 }</td></tr>
<tr class="separator:ae92e21c6957f026dbfd00008348e8b50"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7599500a82c87cf303643005ea78e79"><td class="memItemLeft" align="right" valign="top"><a id="ab7599500a82c87cf303643005ea78e79"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>INDIC_PLAIN</b> = 0, 
<b>INDIC_SQUIGGLE</b> = 1, 
<b>INDIC_TT</b> = 2, 
<br />
&#160;&#160;<b>INDIC_DIAGONAL</b> = 3, 
<b>INDIC_STRIKE</b> = 4, 
<b>INDIC_HIDDEN</b> = 5, 
<br />
&#160;&#160;<b>INDIC_BOX</b> = 6, 
<b>INDIC_ROUNDBOX</b> = 7, 
<b>INDIC_STRAIGHTBOX</b> = 8, 
<br />
&#160;&#160;<b>INDIC_DASH</b> = 9, 
<b>INDIC_DOTS</b> = 10, 
<b>INDIC_SQUIGGLELOW</b> = 11, 
<br />
&#160;&#160;<b>INDIC_DOTBOX</b> = 12, 
<b>INDIC_SQUIGGLEPIXMAP</b> = 13, 
<b>INDIC_COMPOSITIONTHICK</b> = 14, 
<br />
&#160;&#160;<b>INDIC_COMPOSITIONTHIN</b> = 15, 
<b>INDIC_FULLBOX</b> = 16, 
<b>INDIC_TEXTFORE</b> = 17, 
<br />
&#160;&#160;<b>INDIC_POINT</b> = 18, 
<b>INDIC_POINTCHARACTER</b> = 19, 
<b>INDIC_GRADIENT</b> = 20, 
<br />
&#160;&#160;<b>INDIC_GRADIENTCENTRE</b> = 21, 
<b>INDIC_IME</b> = 32, 
<b>INDIC_IME_MAX</b> = 35, 
<br />
&#160;&#160;<b>INDIC_CONTAINER</b> = 8, 
<b>INDIC_MAX</b> = 35, 
<b>INDIC0_MASK</b> = 0x20, 
<br />
&#160;&#160;<b>INDIC1_MASK</b> = 0x40, 
<b>INDIC2_MASK</b> = 0x80, 
<b>INDICS_MASK</b> = 0xe0, 
<br />
&#160;&#160;<b>SC_INDICVALUEBIT</b> = 0x01000000, 
<b>SC_INDICVALUEMASK</b> = 0x00ffffff, 
<b>SC_INDICFLAG_VALUEBEFORE</b> = 1
<br />
 }</td></tr>
<tr class="separator:ab7599500a82c87cf303643005ea78e79"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8abc450a2a7d8a4d92b768b85d72bf14"><td class="memItemLeft" align="right" valign="top"><a id="a8abc450a2a7d8a4d92b768b85d72bf14"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_PRINT_NORMAL</b> = 0, 
<b>SC_PRINT_INVERTLIGHT</b> = 1, 
<b>SC_PRINT_BLACKONWHITE</b> = 2, 
<br />
&#160;&#160;<b>SC_PRINT_COLOURONWHITE</b> = 3, 
<b>SC_PRINT_COLOURONWHITEDEFAULTBG</b> = 4, 
<b>SC_PRINT_SCREENCOLOURS</b> = 5
<br />
 }</td></tr>
<tr class="separator:a8abc450a2a7d8a4d92b768b85d72bf14"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a449c8199d94b497f5afd855341d64229"><td class="memItemLeft" align="right" valign="top"><a id="a449c8199d94b497f5afd855341d64229"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SCFIND_WHOLEWORD</b> = 2, 
<b>SCFIND_MATCHCASE</b> = 4, 
<b>SCFIND_WORDSTART</b> = 0x00100000, 
<br />
&#160;&#160;<b>SCFIND_REGEXP</b> = 0x00200000, 
<b>SCFIND_POSIX</b> = 0x00400000, 
<b>SCFIND_CXX11REGEX</b> = 0x00800000
<br />
 }</td></tr>
<tr class="separator:a449c8199d94b497f5afd855341d64229"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09d85c486690a5380792405faaaa220c"><td class="memItemLeft" align="right" valign="top"><a id="a09d85c486690a5380792405faaaa220c"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_FOLDDISPLAYTEXT_HIDDEN</b> = 0, 
<b>SC_FOLDDISPLAYTEXT_STANDARD</b> = 1, 
<b>SC_FOLDDISPLAYTEXT_BOXED</b> = 2
 }</td></tr>
<tr class="separator:a09d85c486690a5380792405faaaa220c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab5d42aeb0ff1f1319327636928704f11"><td class="memItemLeft" align="right" valign="top"><a id="ab5d42aeb0ff1f1319327636928704f11"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_FOLDLEVELBASE</b> = 0x00400, 
<b>SC_FOLDLEVELWHITEFLAG</b> = 0x01000, 
<b>SC_FOLDLEVELHEADERFLAG</b> = 0x02000, 
<br />
&#160;&#160;<b>SC_FOLDLEVELNUMBERMASK</b> = 0x00fff
<br />
 }</td></tr>
<tr class="separator:ab5d42aeb0ff1f1319327636928704f11"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6eb62a631a3a04dd6d5c98286c143231"><td class="memItemLeft" align="right" valign="top"><a id="a6eb62a631a3a04dd6d5c98286c143231"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_FOLDFLAG_LINEBEFORE_EXPANDED</b> = 0x0002, 
<b>SC_FOLDFLAG_LINEBEFORE_CONTRACTED</b> = 0x0004, 
<b>SC_FOLDFLAG_LINEAFTER_EXPANDED</b> = 0x0008, 
<br />
&#160;&#160;<b>SC_FOLDFLAG_LINEAFTER_CONTRACTED</b> = 0x0010, 
<b>SC_FOLDFLAG_LEVELNUMBERS</b> = 0x0040, 
<b>SC_FOLDFLAG_LINESTATE</b> = 0x0080
<br />
 }</td></tr>
<tr class="separator:a6eb62a631a3a04dd6d5c98286c143231"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3477f4d751d01b39b5e7e38d93856fef"><td class="memItemLeft" align="right" valign="top"><a id="a3477f4d751d01b39b5e7e38d93856fef"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_LINE_END_TYPE_DEFAULT</b> = 0, 
<b>SC_LINE_END_TYPE_UNICODE</b> = 1
 }</td></tr>
<tr class="separator:a3477f4d751d01b39b5e7e38d93856fef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a299f0d0556673ceff9aae3e11099bb68"><td class="memItemLeft" align="right" valign="top"><a id="a299f0d0556673ceff9aae3e11099bb68"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_TIME_FOREVER</b> = 10000000
 }</td></tr>
<tr class="separator:a299f0d0556673ceff9aae3e11099bb68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ae69b065d2ac2dfaab53656df932306"><td class="memItemLeft" align="right" valign="top"><a id="a7ae69b065d2ac2dfaab53656df932306"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_WRAP_NONE</b> = 0, 
<b>SC_WRAP_WORD</b> = 1, 
<b>SC_WRAP_CHAR</b> = 2, 
<br />
&#160;&#160;<b>SC_WRAP_WHITESPACE</b> = 3
<br />
 }</td></tr>
<tr class="separator:a7ae69b065d2ac2dfaab53656df932306"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a36b85609dd203b3b27c14a16e24321b9"><td class="memItemLeft" align="right" valign="top"><a id="a36b85609dd203b3b27c14a16e24321b9"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_WRAPINDENT_FIXED</b> = 0, 
<b>SC_WRAPINDENT_SAME</b> = 1, 
<b>SC_WRAPINDENT_INDENT</b> = 2, 
<br />
&#160;&#160;<b>SC_WRAPINDENT_DEEPINDENT</b> = 3
<br />
 }</td></tr>
<tr class="separator:a36b85609dd203b3b27c14a16e24321b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b6243d26078b6ee34246844721d2ad9"><td class="memItemLeft" align="right" valign="top"><a id="a6b6243d26078b6ee34246844721d2ad9"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_CACHE_NONE</b> = 0, 
<b>SC_CACHE_CARET</b> = 1, 
<b>SC_CACHE_PAGE</b> = 2, 
<br />
&#160;&#160;<b>SC_CACHE_DOCUMENT</b> = 3
<br />
 }</td></tr>
<tr class="separator:a6b6243d26078b6ee34246844721d2ad9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac9677f1e758e2aaa368dc70c8eac011b"><td class="memItemLeft" align="right" valign="top"><a id="ac9677f1e758e2aaa368dc70c8eac011b"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_PHASES_ONE</b> = 0, 
<b>SC_PHASES_TWO</b> = 1, 
<b>SC_PHASES_MULTIPLE</b> = 2
 }</td></tr>
<tr class="separator:ac9677f1e758e2aaa368dc70c8eac011b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a387e5fb00390c98f574c1a63e5cc6990"><td class="memItemLeft" align="right" valign="top"><a id="a387e5fb00390c98f574c1a63e5cc6990"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>ANNOTATION_HIDDEN</b> = 0, 
<b>ANNOTATION_STANDARD</b> = 1, 
<b>ANNOTATION_BOXED</b> = 2, 
<br />
&#160;&#160;<b>ANNOTATION_INDENTED</b> = 3
<br />
 }</td></tr>
<tr class="separator:a387e5fb00390c98f574c1a63e5cc6990"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a74a237b74599034b49c7eb219976143f"><td class="memItemLeft" align="right" valign="top"><a id="a74a237b74599034b49c7eb219976143f"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>EDGE_NONE</b> = 0, 
<b>EDGE_LINE</b> = 1, 
<b>EDGE_BACKGROUND</b> = 2, 
<br />
&#160;&#160;<b>EDGE_MULTILINE</b> = 3
<br />
 }</td></tr>
<tr class="separator:a74a237b74599034b49c7eb219976143f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad04a7f86e07a9e8bcc8b3d835abe9f5d"><td class="memItemLeft" align="right" valign="top"><a id="ad04a7f86e07a9e8bcc8b3d835abe9f5d"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_CURSORNORMAL</b> = -1, 
<b>SC_CURSORARROW</b> = 2, 
<b>SC_CURSORWAIT</b> = 4, 
<br />
&#160;&#160;<b>SC_CURSORREVERSEARROW</b> = 7
<br />
 }</td></tr>
<tr class="separator:ad04a7f86e07a9e8bcc8b3d835abe9f5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1ffda257161a3e02664b8e78edc75255"><td class="memItemLeft" align="right" valign="top"><a id="a1ffda257161a3e02664b8e78edc75255"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>UNDO_MAY_COALESCE</b> = 1
 }</td></tr>
<tr class="separator:a1ffda257161a3e02664b8e78edc75255"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad239e77cce3abfe60c72f70460dd70ca"><td class="memItemLeft" align="right" valign="top"><a id="ad239e77cce3abfe60c72f70460dd70ca"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>VISIBLE_SLOP</b> = 0x01, 
<b>VISIBLE_STRICT</b> = 0x04
 }</td></tr>
<tr class="separator:ad239e77cce3abfe60c72f70460dd70ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c80c96e6a7f6a37bf1b355d6be1b4f6"><td class="memItemLeft" align="right" valign="top"><a id="a2c80c96e6a7f6a37bf1b355d6be1b4f6"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>CARET_SLOP</b> = 0x01, 
<b>CARET_STRICT</b> = 0x04, 
<b>CARET_JUMPS</b> = 0x10, 
<br />
&#160;&#160;<b>CARET_EVEN</b> = 0x08
<br />
 }</td></tr>
<tr class="separator:a2c80c96e6a7f6a37bf1b355d6be1b4f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9f68a3ed6fdd9fc9fe3113bf51e06916"><td class="memItemLeft" align="right" valign="top"><a id="a9f68a3ed6fdd9fc9fe3113bf51e06916"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>CARETSTYLE_INVISIBLE</b> = 0, 
<b>CARETSTYLE_LINE</b> = 1, 
<b>CARETSTYLE_BLOCK</b> = 2
 }</td></tr>
<tr class="separator:a9f68a3ed6fdd9fc9fe3113bf51e06916"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5c2dea0c8bd3f89ee89586d5e5a89889"><td class="memItemLeft" align="right" valign="top"><a id="a5c2dea0c8bd3f89ee89586d5e5a89889"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_MOD_INSERTTEXT</b> = 0x1, 
<b>SC_MOD_DELETETEXT</b> = 0x2, 
<b>SC_MOD_CHANGESTYLE</b> = 0x4, 
<br />
&#160;&#160;<b>SC_MOD_CHANGEFOLD</b> = 0x8, 
<b>SC_PERFORMED_USER</b> = 0x10, 
<b>SC_PERFORMED_UNDO</b> = 0x20, 
<br />
&#160;&#160;<b>SC_PERFORMED_REDO</b> = 0x40, 
<b>SC_MULTISTEPUNDOREDO</b> = 0x80, 
<b>SC_LASTSTEPINUNDOREDO</b> = 0x100, 
<br />
&#160;&#160;<b>SC_MOD_CHANGEMARKER</b> = 0x200, 
<b>SC_MOD_BEFOREINSERT</b> = 0x400, 
<b>SC_MOD_BEFOREDELETE</b> = 0x800, 
<br />
&#160;&#160;<b>SC_MULTILINEUNDOREDO</b> = 0x1000, 
<b>SC_STARTACTION</b> = 0x2000, 
<b>SC_MOD_CHANGEINDICATOR</b> = 0x4000, 
<br />
&#160;&#160;<b>SC_MOD_CHANGELINESTATE</b> = 0x8000, 
<b>SC_MOD_CHANGEMARGIN</b> = 0x10000, 
<b>SC_MOD_CHANGEANNOTATION</b> = 0x20000, 
<br />
&#160;&#160;<b>SC_MOD_CONTAINER</b> = 0x40000, 
<b>SC_MOD_LEXERSTATE</b> = 0x80000, 
<b>SC_MOD_INSERTCHECK</b> = 0x100000, 
<br />
&#160;&#160;<b>SC_MOD_CHANGETABSTOPS</b> = 0x200000, 
<b>SC_MODEVENTMASKALL</b> = 0x3fffff
<br />
 }</td></tr>
<tr class="separator:a5c2dea0c8bd3f89ee89586d5e5a89889"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aae100b109f91ace045feded7e5ca7267"><td class="memItemLeft" align="right" valign="top"><a id="aae100b109f91ace045feded7e5ca7267"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SCK_DOWN</b> = 300, 
<b>SCK_UP</b> = 301, 
<b>SCK_LEFT</b> = 302, 
<br />
&#160;&#160;<b>SCK_RIGHT</b> = 303, 
<b>SCK_HOME</b> = 304, 
<b>SCK_END</b> = 305, 
<br />
&#160;&#160;<b>SCK_PRIOR</b> = 306, 
<b>SCK_NEXT</b> = 307, 
<b>SCK_DELETE</b> = 308, 
<br />
&#160;&#160;<b>SCK_INSERT</b> = 309, 
<b>SCK_ESCAPE</b> = 7, 
<b>SCK_BACK</b> = 8, 
<br />
&#160;&#160;<b>SCK_TAB</b> = 9, 
<b>SCK_RETURN</b> = 13, 
<b>SCK_ADD</b> = 310, 
<br />
&#160;&#160;<b>SCK_SUBTRACT</b> = 311, 
<b>SCK_DIVIDE</b> = 312, 
<b>SCK_WIN</b> = 313, 
<br />
&#160;&#160;<b>SCK_RWIN</b> = 314, 
<b>SCK_MENU</b> = 315
<br />
 }</td></tr>
<tr class="separator:aae100b109f91ace045feded7e5ca7267"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a76f793f6e6ce5b6f14b3925e78ea2aa6"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#a76f793f6e6ce5b6f14b3925e78ea2aa6a6097124d46dc23dbb028fb340b4aa17e">SCMOD_NORM</a> = 0, 
<a class="el" href="classQsciScintillaBase.html#a76f793f6e6ce5b6f14b3925e78ea2aa6ad3e496f1bdcc19e0b87c83c624e9f184">SCMOD_SHIFT</a> = 1, 
<a class="el" href="classQsciScintillaBase.html#a76f793f6e6ce5b6f14b3925e78ea2aa6a944d24d92f0c62a4f519936199d74198">SCMOD_CTRL</a> = 2, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#a76f793f6e6ce5b6f14b3925e78ea2aa6aaa78aa9b07d1b2afe030262223eba11a">SCMOD_ALT</a> = 4, 
<a class="el" href="classQsciScintillaBase.html#a76f793f6e6ce5b6f14b3925e78ea2aa6a92a9efa0e26fb75aa9d6584c715aa465">SCMOD_SUPER</a> = 8, 
<a class="el" href="classQsciScintillaBase.html#a76f793f6e6ce5b6f14b3925e78ea2aa6add02edfef385cd3b3020235bc752eda7">SCMOD_META</a> = 16
<br />
 }</td></tr>
<tr class="separator:a76f793f6e6ce5b6f14b3925e78ea2aa6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa4ab44fd6a7374eb16d07762aa51c7c0"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a62931496707b79f9d5b348aacbd51a6e">SCLEX_CONTAINER</a> = 0, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a8a264dd8fe734630be400388fac8f588">SCLEX_NULL</a> = 1, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aaa0d7fa0c2396811b59b5e6ba6c811f1">SCLEX_PYTHON</a> = 2, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a504f72d70f56dcb53fb908fe79452138">SCLEX_CPP</a> = 3, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a00ae3e9a26cdc1afac630a91f3b3c7ec">SCLEX_HTML</a> = 4, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a1d7fee124bcdc3de441f5051c53eff92">SCLEX_XML</a> = 5, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a9ef2274168be1be3f691d59aa142f170">SCLEX_PERL</a> = 6, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0af1a6b060c43736ae87b701da137aaf51">SCLEX_SQL</a> = 7, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a552be64626f5d82c3d77e27ed485124f">SCLEX_VB</a> = 8, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ae51ad6d026758e0fde01d796d72d0815">SCLEX_PROPERTIES</a> = 9, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a1beef12bbc9c9835a6791267c8fcb10a">SCLEX_ERRORLIST</a> = 10, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a990148a08b2f7a1585691ee984876863">SCLEX_MAKEFILE</a> = 11, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ae894213f20cdd7eae927718c87cbfae4">SCLEX_BATCH</a> = 12, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6b110854fbef28d60067b82faf5ed229">SCLEX_LATEX</a> = 14, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a9975c914f242664d8225e3692f88ac31">SCLEX_LUA</a> = 15, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a254b0148fea4c8f6e170ef09bae369e7">SCLEX_DIFF</a> = 16, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a477ce8c2cdaac994e2ec4022e67ee185">SCLEX_CONF</a> = 17, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aa81b3ab54ed446bd82fd8e47bb716efe">SCLEX_PASCAL</a> = 18, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a2c30b22ff5f0f07f8ccf96eb0c0eb5d6">SCLEX_AVE</a> = 19, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a56c1b1e39b9c7e20faa9b7420d54e7a5">SCLEX_ADA</a> = 20, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a1b4009430261f11f17487ad843007d04">SCLEX_LISP</a> = 21, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ab271a6111144900d2d93de516b1035eb">SCLEX_RUBY</a> = 22, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a4e7d76804b75f4b89f1b315bfc52972f">SCLEX_EIFFEL</a> = 23, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a26b6e157b2a4595977de9c31c44c5f36">SCLEX_EIFFELKW</a> = 24, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a3d423d890cfc3b987d62d48ede1ec887">SCLEX_TCL</a> = 25, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a3c92b60cfd0065deb73541166ab412cd">SCLEX_NNCRONTAB</a> = 26, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ac26190310f45bf026d031fd52729f310">SCLEX_BULLANT</a> = 27, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a0efcb32e5b56db847054c8b5f4778581">SCLEX_VBSCRIPT</a> = 28, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ac6732578f1c51e3a2757dddb839d7b5d">SCLEX_ASP</a> = SCLEX_HTML, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a4a9d8ecf3ccab78020f78ad4efb510d6">SCLEX_PHP</a> = SCLEX_HTML, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a207b1e77e47550f0b0787a107a206b71">SCLEX_BAAN</a> = 31, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a457d5320bb0deebd765830974964c4ca">SCLEX_MATLAB</a> = 32, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a12d07a2dd6cc648226ecdbc41ef0d169">SCLEX_SCRIPTOL</a> = 33, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a286421d44d37e6eade78481e3d063540">SCLEX_ASM</a> = 34, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a8dd4142d9043b7b15c235c038a8abf0f">SCLEX_CPPNOCASE</a> = 35, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a14a8a752af012a2c9444b5b291108574">SCLEX_FORTRAN</a> = 36, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a94cdec496a332379e7cb47c116c318c6">SCLEX_F77</a> = 37, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a9c08d490101986eb17aab67a1fb7159f">SCLEX_CSS</a> = 38, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0af510951e33b06ef3e995d69c53a94fdc">SCLEX_POV</a> = 39, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6f07bc63049dc24bd3afc9e8ebac18ce">SCLEX_LOUT</a> = 40, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a1d30f511ae4cc23f0bc43fd1ca6cda12">SCLEX_ESCRIPT</a> = 41, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a053e8c790c607f826c933729ada1a6c2">SCLEX_PS</a> = 42, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a592ddfd7bb2d792a42e44a6a04640247">SCLEX_NSIS</a> = 43, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6d6709e5e960072a7c91b3e5b01a020a">SCLEX_MMIXAL</a> = 44, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a7d602388b550d67454b1c11de9fac04e">SCLEX_CLW</a> = 45, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a8a1b12c769eced15a1a54a87e7521a47">SCLEX_CLWNOCASE</a> = 46, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a95c696054c8774351078fb670b591028">SCLEX_LOT</a> = 47, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a7c021577e03000be86d0acd1ec6c502b">SCLEX_YAML</a> = 48, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aff435fbce318cd18cadeae1be877bd41">SCLEX_TEX</a> = 49, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a345b6b3ec51466394faec02ecdb8dc2f">SCLEX_METAPOST</a> = 50, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0acc275c470d9bfe05754cdf4e42a54741">SCLEX_POWERBASIC</a> = 51, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a44c24c20cdec1b6e482f69ed721a4077">SCLEX_FORTH</a> = 52, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aa5ac4a29460ebae1edb850c87473a52c">SCLEX_ERLANG</a> = 53, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a0bfd1f7f3d9ec8b9ea24bb00eb199704">SCLEX_OCTAVE</a> = 54, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a9cd5b9812fe0fb143740c8a5ac15431a">SCLEX_MSSQL</a> = 55, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aa419285333430eff62c7d44b79786a3d">SCLEX_VERILOG</a> = 56, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a67ce3e5d9bdff0bdb44d1a5aff3e69c4">SCLEX_KIX</a> = 57, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ada00900f5ad22e170d494790194dfdcf">SCLEX_GUI4CLI</a> = 58, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a826c7c9b1bbf5079ff818003bbcdf78e">SCLEX_SPECMAN</a> = 59, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a05181d968abb3e1fec89869dd14e2bae">SCLEX_AU3</a> = 60, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a176475983f8e4985ca616779de3be8db">SCLEX_APDL</a> = 61, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ab05738aa98869eb2b998fb6d063d9dbc">SCLEX_BASH</a> = 62, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6bc934ce8070f4cd38f4c2619b165b01">SCLEX_ASN1</a> = 63, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a893d2e14e9c835c6b1e52d43aaf8c577">SCLEX_VHDL</a> = 64, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aaf2da832f0698fe3cea0693f57d4b7d4">SCLEX_CAML</a> = 65, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a8ca1aa2873729ccadcc0c952d574299f">SCLEX_BLITZBASIC</a> = 66, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ae15512b5a294a4d9d87423e256a14874">SCLEX_PUREBASIC</a> = 67, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ae0b332697a33770b6f1ba537b942a87d">SCLEX_HASKELL</a> = 68, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a582a3951e713f3e804e312345c120571">SCLEX_PHPSCRIPT</a> = 69, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a41b0513b5d681c2e8a5d76ca8ef8752d">SCLEX_TADS3</a> = 70, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a0c4f62b4ba53206637593684c27fed7f">SCLEX_REBOL</a> = 71, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a13ce10215a927235a20b5b54739b6442">SCLEX_SMALLTALK</a> = 72, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0af2efe26c56f871a45383153193e4e9e0">SCLEX_FLAGSHIP</a> = 73, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aec034e1adf57a7349ed47f4848bb40c4">SCLEX_CSOUND</a> = 74, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0af7c6976f83643ba89841ad2eaf62c678">SCLEX_FREEBASIC</a> = 75, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a1e8ba9f543d28f5470b3284c377caaef">SCLEX_INNOSETUP</a> = 76, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0acce1c850472e07587f12f668d3b541e5">SCLEX_OPAL</a> = 77, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6dde6455441154c518c71d14cbc384e8">SCLEX_SPICE</a> = 78, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0af89b580de6d8a9bffac12bf14b58489d">SCLEX_D</a> = 79, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a28cf4c57f311aa17f2dbf0f03761ce99">SCLEX_CMAKE</a> = 80, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ae20ac3b9f61ea931c3b12e0c462b1dd0">SCLEX_GAP</a> = 81, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aa9a5c4cac509bcde1ea71e3fcc44c664">SCLEX_PLM</a> = 82, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a93e8366c515df085823e843354a8b3cd">SCLEX_PROGRESS</a> = 83, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a94d6c2b2fa424cbb4c8eb3749a9f934b">SCLEX_ABAQUS</a> = 84, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a5a68f1f87e9069301116d328e30f63c7">SCLEX_ASYMPTOTE</a> = 85, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a370fc35c7da9d2bdd2ab7088da3d7afe">SCLEX_R</a> = 86, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aea0c54b674130c1ce336667af1468011">SCLEX_MAGIK</a> = 87, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ad32bd9c8bb2d41cfcf26a8ab7605cee8">SCLEX_POWERSHELL</a> = 88, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a705102c40d1227a12afd8da13b43ab00">SCLEX_MYSQL</a> = 89, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a0d2bf09afa633715605a3305777dfc83">SCLEX_PO</a> = 90, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a9add9caf532d347948e1c8038ab671e1">SCLEX_TAL</a> = 91, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ab87e1d9e6edd4f9ee6627d837c6152b3">SCLEX_COBOL</a> = 92, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ad5f7ed0033d057fc7d84a3c80c5640be">SCLEX_TACL</a> = 93, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a1aa37e96400fba08b571e6f17100bb23">SCLEX_SORCUS</a> = 94, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a36d2c47f406db754feb03b7c530be79f">SCLEX_POWERPRO</a> = 95, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a8d42ad47b0a03f3a27c047401f3cb080">SCLEX_NIMROD</a> = 96, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6b2b7135756e6e63afaab29e1ce69e5d">SCLEX_SML</a> = 97, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a735f6a745c132f34910668c6f221dbef">SCLEX_MARKDOWN</a> = 98, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a1eb34afacf50e022bc6d8d3ac92384d1">SCLEX_TXT2TAGS</a> = 99, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a5f5a499292d10817ab864bb61fc952bb">SCLEX_A68K</a> = 100, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a030fcaf06a85c39c4f57a828ef354d11">SCLEX_MODULA</a> = 101, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ab4dd20651eeac848ec8a1586b3da3c8c">SCLEX_COFFEESCRIPT</a> = 102, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aa9ea73b5b40de75ed54ea356f13a7b47">SCLEX_TCMD</a> = 103, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ad63b11d786d32c7101682682bf49c063">SCLEX_AVS</a> = 104, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a7ed6ed45706f72a25396e7cea6f179fc">SCLEX_ECL</a> = 105, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a96e54938b672128818b2c8201833993a">SCLEX_OSCRIPT</a> = 106, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6505e2018707a20252ef8eefc6b25fb3">SCLEX_VISUALPROLOG</a> = 107, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a96b2db4f5bb0191b81dd536b0b8b13e2">SCLEX_LITERATEHASKELL</a> = 108, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6e8fa194daac20f1860a30910cd77ad2">SCLEX_STTXT</a> = 109, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ad0cd24eef0f5650d775d4dd05bd82df8">SCLEX_KVIRC</a> = 110, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a7dedcc3f7467a77cf25eff297aad55c1">SCLEX_RUST</a> = 111, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a426868e898ad88df600d7a0cba7ed000">SCLEX_DMAP</a> = 112, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6fee40e395ba28044ccd9cbbc1db48d5">SCLEX_AS</a> = 113, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ad9e8188110135d6897add3becb30995f">SCLEX_DMIS</a> = 114, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ab196b886d720b528c06981f3162edcfe">SCLEX_REGISTRY</a> = 115, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0abc6e7a4f3fbf502b080d443f4f779ea9">SCLEX_BIBTEX</a> = 116, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a833ab9e759135def757131a8bd0196fe">SCLEX_SREC</a> = 117, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a60d40cf6110071d3ae0ff271ea00fca6">SCLEX_IHEX</a> = 118, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a7f81fc1ce2e254d399b858b08362e0bf">SCLEX_TEHEX</a> = 119, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a7fbfc36f6ecf328b50efe1d29fa7be89">SCLEX_JSON</a> = 120, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a07565bccfb3046478e918086c75fd2d0">SCLEX_EDIFACT</a> = 121, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ace65638f1fc7df156cb5fd7e13e40b39">SCLEX_INDENT</a> = 122, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ad030153b23920c60fd4c57a63a1992ad">SCLEX_MAXIMA</a> = 123, 
<br />
&#160;&#160;<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aa3c5a8b4e7b66cfd26eeadc24049c268">SCLEX_STATA</a> = 124, 
<a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a163ba76db43420149ae4ed4456426d7c">SCLEX_SAS</a> = 125
<br />
 }</td></tr>
<tr class="separator:aa4ab44fd6a7374eb16d07762aa51c7c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a80ff71d78cd014abb3ba387d190547df"><td class="memItemLeft" align="right" valign="top"><a id="a80ff71d78cd014abb3ba387d190547df"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_WEIGHT_NORMAL</b> = 400, 
<b>SC_WEIGHT_SEMIBOLD</b> = 600, 
<b>SC_WEIGHT_BOLD</b> = 700
 }</td></tr>
<tr class="separator:a80ff71d78cd014abb3ba387d190547df"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab69a4898ecba5b929124e7a11d504cb9"><td class="memItemLeft" align="right" valign="top"><a id="ab69a4898ecba5b929124e7a11d504cb9"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<b>SC_TECHNOLOGY_DEFAULT</b> = 0, 
<b>SC_TECHNOLOGY_DIRECTWRITE</b> = 1, 
<b>SC_TECHNOLOGY_DIRECTWRITERETAIN</b> = 2, 
<br />
&#160;&#160;<b>SC_TECHNOLOGY_DIRECTWRITEDC</b> = 3
<br />
 }</td></tr>
<tr class="separator:ab69a4898ecba5b929124e7a11d504cb9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7bfd9660119d4ff15969a312874e3f0d"><td class="memItemLeft" align="right" valign="top"><a id="a7bfd9660119d4ff15969a312874e3f0d"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_CASEINSENSITIVEBEHAVIOUR_RESPECTCASE</b> = 0, 
<b>SC_CASEINSENSITIVEBEHAVIOUR_IGNORECASE</b> = 1
 }</td></tr>
<tr class="separator:a7bfd9660119d4ff15969a312874e3f0d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3c275507899f6fd247db63d1dcd83003"><td class="memItemLeft" align="right" valign="top"><a id="a3c275507899f6fd247db63d1dcd83003"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_FONT_SIZE_MULTIPLIER</b> = 100
 }</td></tr>
<tr class="separator:a3c275507899f6fd247db63d1dcd83003"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2236594c42c6b16ae3a048eef7a88624"><td class="memItemLeft" align="right" valign="top"><a id="a2236594c42c6b16ae3a048eef7a88624"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_FOLDACTION_CONTRACT</b> = 0, 
<b>SC_FOLDACTION_EXPAND</b> = 1, 
<b>SC_FOLDACTION_TOGGLE</b> = 2
 }</td></tr>
<tr class="separator:a2236594c42c6b16ae3a048eef7a88624"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d8147b1c9082f33f6b41eefdb249f9e"><td class="memItemLeft" align="right" valign="top"><a id="a2d8147b1c9082f33f6b41eefdb249f9e"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_AUTOMATICFOLD_SHOW</b> = 0x0001, 
<b>SC_AUTOMATICFOLD_CLICK</b> = 0x0002, 
<b>SC_AUTOMATICFOLD_CHANGE</b> = 0x0004
 }</td></tr>
<tr class="separator:a2d8147b1c9082f33f6b41eefdb249f9e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a78dba0cc167992b097c30ea331f2db"><td class="memItemLeft" align="right" valign="top"><a id="a2a78dba0cc167992b097c30ea331f2db"></a>enum &#160;</td><td class="memItemRight" valign="bottom">{ <b>SC_ORDER_PRESORTED</b> = 0, 
<b>SC_ORDER_PERFORMSORT</b> = 1, 
<b>SC_ORDER_CUSTOM</b> = 2
 }</td></tr>
<tr class="separator:a2a78dba0cc167992b097c30ea331f2db"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="signals"></a>
Signals</h2></td></tr>
<tr class="memitem:a747feb07236c1beccadd446562b53b84"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a747feb07236c1beccadd446562b53b84">QSCN_SELCHANGED</a> (bool yes)</td></tr>
<tr class="separator:a747feb07236c1beccadd446562b53b84"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1719fba80d9e60cf9fce1bb75f304568"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a1719fba80d9e60cf9fce1bb75f304568">SCN_AUTOCCANCELLED</a> ()</td></tr>
<tr class="separator:a1719fba80d9e60cf9fce1bb75f304568"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aabab23e5653c35dae8a6f144d73c4657"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#aabab23e5653c35dae8a6f144d73c4657">SCN_AUTOCCHARDELETED</a> ()</td></tr>
<tr class="separator:aabab23e5653c35dae8a6f144d73c4657"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41e738411112b8f509e0b49b6fc3e318"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a41e738411112b8f509e0b49b6fc3e318">SCN_AUTOCCOMPLETED</a> (const char *selection, int position, int ch, int method)</td></tr>
<tr class="separator:a41e738411112b8f509e0b49b6fc3e318"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a61c43c53a753272c51c5c5ac14bda136"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a61c43c53a753272c51c5c5ac14bda136">SCN_AUTOCSELECTION</a> (const char *selection, int position, int ch, int method)</td></tr>
<tr class="separator:a61c43c53a753272c51c5c5ac14bda136"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1ad82492f8015a60dea97f6ebd712d64"><td class="memItemLeft" align="right" valign="top"><a id="a1ad82492f8015a60dea97f6ebd712d64"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a1ad82492f8015a60dea97f6ebd712d64">SCN_AUTOCSELECTION</a> (const char *selection, int position)</td></tr>
<tr class="separator:a1ad82492f8015a60dea97f6ebd712d64"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a721a1879cabaa76883ae1a02a34a76e8"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a721a1879cabaa76883ae1a02a34a76e8">SCN_AUTOCSELECTIONCHANGE</a> (const char *selection, int id, int position)</td></tr>
<tr class="separator:a721a1879cabaa76883ae1a02a34a76e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af2cc3652d35b4d0ec1d8c9ac18e2225e"><td class="memItemLeft" align="right" valign="top"><a id="af2cc3652d35b4d0ec1d8c9ac18e2225e"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#af2cc3652d35b4d0ec1d8c9ac18e2225e">SCEN_CHANGE</a> ()</td></tr>
<tr class="separator:af2cc3652d35b4d0ec1d8c9ac18e2225e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a13f22ec5a59e2e8e97a27ac24967f74d"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a13f22ec5a59e2e8e97a27ac24967f74d">SCN_CALLTIPCLICK</a> (int direction)</td></tr>
<tr class="separator:a13f22ec5a59e2e8e97a27ac24967f74d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae8d8fa5d5f063a7c7d37d527f86b5fe8"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#ae8d8fa5d5f063a7c7d37d527f86b5fe8">SCN_CHARADDED</a> (int charadded)</td></tr>
<tr class="separator:ae8d8fa5d5f063a7c7d37d527f86b5fe8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad3ca5787399ed886cb9000c8feab3c08"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#ad3ca5787399ed886cb9000c8feab3c08">SCN_DOUBLECLICK</a> (int position, int line, int modifiers)</td></tr>
<tr class="separator:ad3ca5787399ed886cb9000c8feab3c08"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ecd605284870ddbf703cf4c8c995ca6"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a9ecd605284870ddbf703cf4c8c995ca6">SCN_DWELLEND</a> (int position, int x, int y)</td></tr>
<tr class="separator:a9ecd605284870ddbf703cf4c8c995ca6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adfd788dce5c1a91d1fcd5e6fdd2fca59"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#adfd788dce5c1a91d1fcd5e6fdd2fca59">SCN_DWELLSTART</a> (int position, int x, int y)</td></tr>
<tr class="separator:adfd788dce5c1a91d1fcd5e6fdd2fca59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae53947625062cec64a212dc68877ddc3"><td class="memItemLeft" align="right" valign="top"><a id="ae53947625062cec64a212dc68877ddc3"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#ae53947625062cec64a212dc68877ddc3">SCN_FOCUSIN</a> ()</td></tr>
<tr class="separator:ae53947625062cec64a212dc68877ddc3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2febc4ea74d45d6a8bc9c758635dd99d"><td class="memItemLeft" align="right" valign="top"><a id="a2febc4ea74d45d6a8bc9c758635dd99d"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a2febc4ea74d45d6a8bc9c758635dd99d">SCN_FOCUSOUT</a> ()</td></tr>
<tr class="separator:a2febc4ea74d45d6a8bc9c758635dd99d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5eff383e6fa96cbbaba6a2558b076c0b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a5eff383e6fa96cbbaba6a2558b076c0b">SCN_HOTSPOTCLICK</a> (int position, int modifiers)</td></tr>
<tr class="separator:a5eff383e6fa96cbbaba6a2558b076c0b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a682cc736272338433efdc86bc936e0e8"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a682cc736272338433efdc86bc936e0e8">SCN_HOTSPOTDOUBLECLICK</a> (int position, int modifiers)</td></tr>
<tr class="separator:a682cc736272338433efdc86bc936e0e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a906faecb0defd2d5a14cac54f8415dcf"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a906faecb0defd2d5a14cac54f8415dcf">SCN_HOTSPOTRELEASECLICK</a> (int position, int modifiers)</td></tr>
<tr class="separator:a906faecb0defd2d5a14cac54f8415dcf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeec8d7e585e93451307df88ff2fc2b87"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#aeec8d7e585e93451307df88ff2fc2b87">SCN_INDICATORCLICK</a> (int position, int modifiers)</td></tr>
<tr class="separator:aeec8d7e585e93451307df88ff2fc2b87"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a93d1e96c88745ca7f2737602e80dc76a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a93d1e96c88745ca7f2737602e80dc76a">SCN_INDICATORRELEASE</a> (int position, int modifiers)</td></tr>
<tr class="separator:a93d1e96c88745ca7f2737602e80dc76a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abdae368f2b81955c4927dc6f26fc2c77"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#abdae368f2b81955c4927dc6f26fc2c77">SCN_MACRORECORD</a> (unsigned int, unsigned long, void *)</td></tr>
<tr class="separator:abdae368f2b81955c4927dc6f26fc2c77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a722a2f16b67ef5f46def6914a6e178c3"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a722a2f16b67ef5f46def6914a6e178c3">SCN_MARGINCLICK</a> (int position, int modifiers, int margin)</td></tr>
<tr class="separator:a722a2f16b67ef5f46def6914a6e178c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a39e90958ae903d2f6198ec0c58f56ed9"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a39e90958ae903d2f6198ec0c58f56ed9">SCN_MARGINRIGHTCLICK</a> (int position, int modifiers, int margin)</td></tr>
<tr class="separator:a39e90958ae903d2f6198ec0c58f56ed9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a63e6aadaa0f86a927a591604b3d7bdd2"><td class="memItemLeft" align="right" valign="top"><a id="a63e6aadaa0f86a927a591604b3d7bdd2"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>SCN_MODIFIED</b> (int, int, const char *, int, int, int, int, int, int, int)</td></tr>
<tr class="separator:a63e6aadaa0f86a927a591604b3d7bdd2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adb5bad7d1dad9ab3fe74adb3e0812969"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#adb5bad7d1dad9ab3fe74adb3e0812969">SCN_MODIFYATTEMPTRO</a> ()</td></tr>
<tr class="separator:adb5bad7d1dad9ab3fe74adb3e0812969"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b6b9e6220051496233ae604cb2d1643"><td class="memItemLeft" align="right" valign="top"><a id="a8b6b9e6220051496233ae604cb2d1643"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>SCN_NEEDSHOWN</b> (int, int)</td></tr>
<tr class="separator:a8b6b9e6220051496233ae604cb2d1643"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a94a1cff08b2ef6558d054177fa88ea47"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a94a1cff08b2ef6558d054177fa88ea47">SCN_PAINTED</a> ()</td></tr>
<tr class="separator:a94a1cff08b2ef6558d054177fa88ea47"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af3a619a5e59cef000f0b550e809c94de"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#af3a619a5e59cef000f0b550e809c94de">SCN_SAVEPOINTLEFT</a> ()</td></tr>
<tr class="separator:af3a619a5e59cef000f0b550e809c94de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0db8c3ad0764a96f3ccf0fec71de0d26"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a0db8c3ad0764a96f3ccf0fec71de0d26">SCN_SAVEPOINTREACHED</a> ()</td></tr>
<tr class="separator:a0db8c3ad0764a96f3ccf0fec71de0d26"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a72c0bc1c83fd675714626cd786ca4fb9"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a72c0bc1c83fd675714626cd786ca4fb9">SCN_STYLENEEDED</a> (int position)</td></tr>
<tr class="separator:a72c0bc1c83fd675714626cd786ca4fb9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42cb45ea05c71180a594e0cc8041c07d"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a42cb45ea05c71180a594e0cc8041c07d">SCN_URIDROPPED</a> (const QUrl &amp;url)</td></tr>
<tr class="separator:a42cb45ea05c71180a594e0cc8041c07d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad88db21d86df33667c234d00af1fdf94"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#ad88db21d86df33667c234d00af1fdf94">SCN_UPDATEUI</a> (int updated)</td></tr>
<tr class="separator:ad88db21d86df33667c234d00af1fdf94"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8225643b25dc6f1dedc48b4a7af4b83d"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a8225643b25dc6f1dedc48b4a7af4b83d">SCN_USERLISTSELECTION</a> (const char *selection, int id, int ch, int method, int position)</td></tr>
<tr class="separator:a8225643b25dc6f1dedc48b4a7af4b83d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9208cc0aaf2e0a32239924fc6d0b67b7"><td class="memItemLeft" align="right" valign="top"><a id="a9208cc0aaf2e0a32239924fc6d0b67b7"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a9208cc0aaf2e0a32239924fc6d0b67b7">SCN_USERLISTSELECTION</a> (const char *selection, int id, int ch, int method)</td></tr>
<tr class="separator:a9208cc0aaf2e0a32239924fc6d0b67b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0045744463119646a5fe33ecc4d104fb"><td class="memItemLeft" align="right" valign="top"><a id="a0045744463119646a5fe33ecc4d104fb"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a0045744463119646a5fe33ecc4d104fb">SCN_USERLISTSELECTION</a> (const char *selection, int id)</td></tr>
<tr class="separator:a0045744463119646a5fe33ecc4d104fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa114b43a6593dceeb1b50efccc9533f6"><td class="memItemLeft" align="right" valign="top"><a id="aa114b43a6593dceeb1b50efccc9533f6"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>SCN_ZOOM</b> ()</td></tr>
<tr class="separator:aa114b43a6593dceeb1b50efccc9533f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a425344ca700d69b60ffeb3f8122f7ff9"><td class="memItemLeft" align="right" valign="top"><a id="a425344ca700d69b60ffeb3f8122f7ff9"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a425344ca700d69b60ffeb3f8122f7ff9">QsciScintillaBase</a> (QWidget *parent=0)</td></tr>
<tr class="separator:a425344ca700d69b60ffeb3f8122f7ff9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a965242ee4392b838cc182c823de54ff6"><td class="memItemLeft" align="right" valign="top"><a id="a965242ee4392b838cc182c823de54ff6"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a965242ee4392b838cc182c823de54ff6">~QsciScintillaBase</a> ()</td></tr>
<tr class="separator:a965242ee4392b838cc182c823de54ff6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1efa4394b588d27fd2a3bd40163a2342"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a1efa4394b588d27fd2a3bd40163a2342">replaceHorizontalScrollBar</a> (QScrollBar *scrollBar)</td></tr>
<tr class="separator:a1efa4394b588d27fd2a3bd40163a2342"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a900e3a0287e262fe65c51162e562fc5d"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a900e3a0287e262fe65c51162e562fc5d">replaceVerticalScrollBar</a> (QScrollBar *scrollBar)</td></tr>
<tr class="separator:a900e3a0287e262fe65c51162e562fc5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8820ab8d7563bd7ed24ce6384846079e"><td class="memItemLeft" align="right" valign="top">long&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a8820ab8d7563bd7ed24ce6384846079e">SendScintilla</a> (unsigned int msg, unsigned long wParam=0, long lParam=0) const</td></tr>
<tr class="separator:a8820ab8d7563bd7ed24ce6384846079e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf01efe3951a727dab9c7a1c35d29e0f"><td class="memItemLeft" align="right" valign="top"><a id="adf01efe3951a727dab9c7a1c35d29e0f"></a>
long&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#adf01efe3951a727dab9c7a1c35d29e0f">SendScintilla</a> (unsigned int msg, unsigned long wParam, void *lParam) const</td></tr>
<tr class="separator:adf01efe3951a727dab9c7a1c35d29e0f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab2b37b2f67991e9c083d9412cba2264"><td class="memItemLeft" align="right" valign="top"><a id="aab2b37b2f67991e9c083d9412cba2264"></a>
long&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#aab2b37b2f67991e9c083d9412cba2264">SendScintilla</a> (unsigned int msg, uintptr_t wParam, const char *lParam) const</td></tr>
<tr class="separator:aab2b37b2f67991e9c083d9412cba2264"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adb45fb04c1ad8c6459fea750d8788584"><td class="memItemLeft" align="right" valign="top"><a id="adb45fb04c1ad8c6459fea750d8788584"></a>
long&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#adb45fb04c1ad8c6459fea750d8788584">SendScintilla</a> (unsigned int msg, const char *lParam) const</td></tr>
<tr class="separator:adb45fb04c1ad8c6459fea750d8788584"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9fa4dc539be7b199e91d6ff0f83e5f8d"><td class="memItemLeft" align="right" valign="top"><a id="a9fa4dc539be7b199e91d6ff0f83e5f8d"></a>
long&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a9fa4dc539be7b199e91d6ff0f83e5f8d">SendScintilla</a> (unsigned int msg, const char *wParam, const char *lParam) const</td></tr>
<tr class="separator:a9fa4dc539be7b199e91d6ff0f83e5f8d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a94a66b0c8459f5a407eef6783cd6d80b"><td class="memItemLeft" align="right" valign="top"><a id="a94a66b0c8459f5a407eef6783cd6d80b"></a>
long&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a94a66b0c8459f5a407eef6783cd6d80b">SendScintilla</a> (unsigned int msg, long wParam) const</td></tr>
<tr class="separator:a94a66b0c8459f5a407eef6783cd6d80b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add035b6176dbe36b3c975f05573c0e61"><td class="memItemLeft" align="right" valign="top"><a id="add035b6176dbe36b3c975f05573c0e61"></a>
long&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#add035b6176dbe36b3c975f05573c0e61">SendScintilla</a> (unsigned int msg, int wParam) const</td></tr>
<tr class="separator:add035b6176dbe36b3c975f05573c0e61"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6c892370b4ee3afd2eef080ee8c25fde"><td class="memItemLeft" align="right" valign="top"><a id="a6c892370b4ee3afd2eef080ee8c25fde"></a>
long&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a6c892370b4ee3afd2eef080ee8c25fde">SendScintilla</a> (unsigned int msg, long cpMin, long cpMax, char *lpstrText) const</td></tr>
<tr class="separator:a6c892370b4ee3afd2eef080ee8c25fde"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1d84e61ba19c5177386ba30ba512345f"><td class="memItemLeft" align="right" valign="top"><a id="a1d84e61ba19c5177386ba30ba512345f"></a>
long&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a1d84e61ba19c5177386ba30ba512345f">SendScintilla</a> (unsigned int msg, unsigned long wParam, const QColor &amp;col) const</td></tr>
<tr class="separator:a1d84e61ba19c5177386ba30ba512345f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af935c2c5d8eeb3aeb25ba9b48539f879"><td class="memItemLeft" align="right" valign="top"><a id="af935c2c5d8eeb3aeb25ba9b48539f879"></a>
long&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#af935c2c5d8eeb3aeb25ba9b48539f879">SendScintilla</a> (unsigned int msg, const QColor &amp;col) const</td></tr>
<tr class="separator:af935c2c5d8eeb3aeb25ba9b48539f879"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aacfd4923492644933902b278efea1787"><td class="memItemLeft" align="right" valign="top"><a id="aacfd4923492644933902b278efea1787"></a>
long&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#aacfd4923492644933902b278efea1787">SendScintilla</a> (unsigned int msg, unsigned long wParam, QPainter *hdc, const QRect &amp;rc, long cpMin, long cpMax) const</td></tr>
<tr class="separator:aacfd4923492644933902b278efea1787"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2cef89549882e03a6290af8cbbf1a3ce"><td class="memItemLeft" align="right" valign="top"><a id="a2cef89549882e03a6290af8cbbf1a3ce"></a>
long&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a2cef89549882e03a6290af8cbbf1a3ce">SendScintilla</a> (unsigned int msg, unsigned long wParam, const QPixmap &amp;lParam) const</td></tr>
<tr class="separator:a2cef89549882e03a6290af8cbbf1a3ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a56542fe5a9e5790eab29936b73ef0fa3"><td class="memItemLeft" align="right" valign="top"><a id="a56542fe5a9e5790eab29936b73ef0fa3"></a>
long&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a56542fe5a9e5790eab29936b73ef0fa3">SendScintilla</a> (unsigned int msg, unsigned long wParam, const QImage &amp;lParam) const</td></tr>
<tr class="separator:a56542fe5a9e5790eab29936b73ef0fa3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f140c587d361cf8539814d820d680f4"><td class="memItemLeft" align="right" valign="top"><a id="a5f140c587d361cf8539814d820d680f4"></a>
void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a5f140c587d361cf8539814d820d680f4">SendScintillaPtrResult</a> (unsigned int msg) const</td></tr>
<tr class="separator:a5f140c587d361cf8539814d820d680f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a0f69249f4e97b96f09ea70f546df7464"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a0f69249f4e97b96f09ea70f546df7464">pool</a> ()</td></tr>
<tr class="separator:a0f69249f4e97b96f09ea70f546df7464"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad70482ce059c5cb74a014b5eb4f597d6"><td class="memItemLeft" align="right" valign="top"><a id="ad70482ce059c5cb74a014b5eb4f597d6"></a>
static int&#160;</td><td class="memItemRight" valign="bottom"><b>commandKey</b> (int qt_key, int &amp;modifiers)</td></tr>
<tr class="separator:ad70482ce059c5cb74a014b5eb4f597d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:abce274ef71035c67baadaa167a1fe5a7"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#abce274ef71035c67baadaa167a1fe5a7">canInsertFromMimeData</a> (const QMimeData *source) const</td></tr>
<tr class="separator:abce274ef71035c67baadaa167a1fe5a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f105b9ec17cd73a0cd601ac9be82dd4"><td class="memItemLeft" align="right" valign="top">virtual QByteArray&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a5f105b9ec17cd73a0cd601ac9be82dd4">fromMimeData</a> (const QMimeData *source, bool &amp;rectangular) const</td></tr>
<tr class="separator:a5f105b9ec17cd73a0cd601ac9be82dd4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7e1e146787204eba48aa5376287de41f"><td class="memItemLeft" align="right" valign="top">virtual QMimeData *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a7e1e146787204eba48aa5376287de41f">toMimeData</a> (const QByteArray &amp;text, bool rectangular) const</td></tr>
<tr class="separator:a7e1e146787204eba48aa5376287de41f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac8a72227fc8efff78505733d1663f927"><td class="memItemLeft" align="right" valign="top"><a id="ac8a72227fc8efff78505733d1663f927"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#ac8a72227fc8efff78505733d1663f927">changeEvent</a> (QEvent *e)</td></tr>
<tr class="separator:ac8a72227fc8efff78505733d1663f927"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adb8531cdc862f79cce9fa4d970bc13a2"><td class="memItemLeft" align="right" valign="top"><a id="adb8531cdc862f79cce9fa4d970bc13a2"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#adb8531cdc862f79cce9fa4d970bc13a2">contextMenuEvent</a> (QContextMenuEvent *e)</td></tr>
<tr class="separator:adb8531cdc862f79cce9fa4d970bc13a2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad7b8480681e4b4d5689d0e6d822dc3c0"><td class="memItemLeft" align="right" valign="top"><a id="ad7b8480681e4b4d5689d0e6d822dc3c0"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#ad7b8480681e4b4d5689d0e6d822dc3c0">dragEnterEvent</a> (QDragEnterEvent *e)</td></tr>
<tr class="separator:ad7b8480681e4b4d5689d0e6d822dc3c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a67c4a9da730c69a2b9fda0a1a02348f1"><td class="memItemLeft" align="right" valign="top"><a id="a67c4a9da730c69a2b9fda0a1a02348f1"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a67c4a9da730c69a2b9fda0a1a02348f1">dragLeaveEvent</a> (QDragLeaveEvent *e)</td></tr>
<tr class="separator:a67c4a9da730c69a2b9fda0a1a02348f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af25249a8e4e0f0966395b5006a5362d9"><td class="memItemLeft" align="right" valign="top"><a id="af25249a8e4e0f0966395b5006a5362d9"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#af25249a8e4e0f0966395b5006a5362d9">dragMoveEvent</a> (QDragMoveEvent *e)</td></tr>
<tr class="separator:af25249a8e4e0f0966395b5006a5362d9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a33c8d6d9915a1375c0d7c24beaceb951"><td class="memItemLeft" align="right" valign="top"><a id="a33c8d6d9915a1375c0d7c24beaceb951"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a33c8d6d9915a1375c0d7c24beaceb951">dropEvent</a> (QDropEvent *e)</td></tr>
<tr class="separator:a33c8d6d9915a1375c0d7c24beaceb951"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c339fd90e92408440230ee9d84cabcf"><td class="memItemLeft" align="right" valign="top"><a id="a2c339fd90e92408440230ee9d84cabcf"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a2c339fd90e92408440230ee9d84cabcf">focusInEvent</a> (QFocusEvent *e)</td></tr>
<tr class="separator:a2c339fd90e92408440230ee9d84cabcf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1aec9b47eeaf611687eeeef0d1aa3a00"><td class="memItemLeft" align="right" valign="top"><a id="a1aec9b47eeaf611687eeeef0d1aa3a00"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a1aec9b47eeaf611687eeeef0d1aa3a00">focusOutEvent</a> (QFocusEvent *e)</td></tr>
<tr class="separator:a1aec9b47eeaf611687eeeef0d1aa3a00"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad6fb5a9621640080fc9909f94b6c0213"><td class="memItemLeft" align="right" valign="top"><a id="ad6fb5a9621640080fc9909f94b6c0213"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#ad6fb5a9621640080fc9909f94b6c0213">focusNextPrevChild</a> (bool next)</td></tr>
<tr class="separator:ad6fb5a9621640080fc9909f94b6c0213"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a39f62b8e6cee02e86d7af508d20a191d"><td class="memItemLeft" align="right" valign="top"><a id="a39f62b8e6cee02e86d7af508d20a191d"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a39f62b8e6cee02e86d7af508d20a191d">keyPressEvent</a> (QKeyEvent *e)</td></tr>
<tr class="separator:a39f62b8e6cee02e86d7af508d20a191d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acb05eb7e7c7cac07547a08d0628013fe"><td class="memItemLeft" align="right" valign="top"><a id="acb05eb7e7c7cac07547a08d0628013fe"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#acb05eb7e7c7cac07547a08d0628013fe">inputMethodEvent</a> (QInputMethodEvent *event)</td></tr>
<tr class="separator:acb05eb7e7c7cac07547a08d0628013fe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ade2821d031ae57c4f52a40e0589baba7"><td class="memItemLeft" align="right" valign="top"><a id="ade2821d031ae57c4f52a40e0589baba7"></a>
virtual QVariant&#160;</td><td class="memItemRight" valign="bottom"><b>inputMethodQuery</b> (Qt::InputMethodQuery query) const</td></tr>
<tr class="separator:ade2821d031ae57c4f52a40e0589baba7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab16e9b7ca9a17af2af3b7ca7f14c8c4"><td class="memItemLeft" align="right" valign="top"><a id="aab16e9b7ca9a17af2af3b7ca7f14c8c4"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#aab16e9b7ca9a17af2af3b7ca7f14c8c4">mouseDoubleClickEvent</a> (QMouseEvent *e)</td></tr>
<tr class="separator:aab16e9b7ca9a17af2af3b7ca7f14c8c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a4a2c5466d1b4f7d6e835c253cb1730"><td class="memItemLeft" align="right" valign="top"><a id="a5a4a2c5466d1b4f7d6e835c253cb1730"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a5a4a2c5466d1b4f7d6e835c253cb1730">mouseMoveEvent</a> (QMouseEvent *e)</td></tr>
<tr class="separator:a5a4a2c5466d1b4f7d6e835c253cb1730"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6d6f13610560a2c281f638f3a40046f6"><td class="memItemLeft" align="right" valign="top"><a id="a6d6f13610560a2c281f638f3a40046f6"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a6d6f13610560a2c281f638f3a40046f6">mousePressEvent</a> (QMouseEvent *e)</td></tr>
<tr class="separator:a6d6f13610560a2c281f638f3a40046f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6a5b949013c382c1b5f341137cd37752"><td class="memItemLeft" align="right" valign="top"><a id="a6a5b949013c382c1b5f341137cd37752"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a6a5b949013c382c1b5f341137cd37752">mouseReleaseEvent</a> (QMouseEvent *e)</td></tr>
<tr class="separator:a6a5b949013c382c1b5f341137cd37752"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2ea74fb61f3d2d983d142a6ec8c3cc9d"><td class="memItemLeft" align="right" valign="top"><a id="a2ea74fb61f3d2d983d142a6ec8c3cc9d"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a2ea74fb61f3d2d983d142a6ec8c3cc9d">paintEvent</a> (QPaintEvent *e)</td></tr>
<tr class="separator:a2ea74fb61f3d2d983d142a6ec8c3cc9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6d0427b93e05876c9a2b541eae08ddab"><td class="memItemLeft" align="right" valign="top"><a id="a6d0427b93e05876c9a2b541eae08ddab"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciScintillaBase.html#a6d0427b93e05876c9a2b541eae08ddab">resizeEvent</a> (QResizeEvent *e)</td></tr>
<tr class="separator:a6d0427b93e05876c9a2b541eae08ddab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad685ecbca2d58ef561256b8439eb0daa"><td class="memItemLeft" align="right" valign="top"><a id="ad685ecbca2d58ef561256b8439eb0daa"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>scrollContentsBy</b> (int dx, int dy)</td></tr>
<tr class="separator:ad685ecbca2d58ef561256b8439eb0daa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1628992bb476d4b0939a0349a30930f4"><td class="memItemLeft" align="right" valign="top"><a id="a1628992bb476d4b0939a0349a30930f4"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>setScrollBars</b> ()</td></tr>
<tr class="separator:a1628992bb476d4b0939a0349a30930f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afe7bb81cd95463b8e93396ab5c35d639"><td class="memItemLeft" align="right" valign="top"><a id="afe7bb81cd95463b8e93396ab5c35d639"></a>
QByteArray&#160;</td><td class="memItemRight" valign="bottom"><b>textAsBytes</b> (const QString &amp;text) const</td></tr>
<tr class="separator:afe7bb81cd95463b8e93396ab5c35d639"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e82b5e3dccc56c382fb4c8ced25e849"><td class="memItemLeft" align="right" valign="top"><a id="a1e82b5e3dccc56c382fb4c8ced25e849"></a>
QString&#160;</td><td class="memItemRight" valign="bottom"><b>bytesAsText</b> (const char *bytes, int size) const</td></tr>
<tr class="separator:a1e82b5e3dccc56c382fb4c8ced25e849"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a408cf3a7df3dce50504b83b51ed44b83"><td class="memItemLeft" align="right" valign="top"><a id="a408cf3a7df3dce50504b83b51ed44b83"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>contextMenuNeeded</b> (int x, int y) const</td></tr>
<tr class="separator:a408cf3a7df3dce50504b83b51ed44b83"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciScintillaBase.html" title="The QsciScintillaBase class implements the Scintilla editor widget and its low-level API.">QsciScintillaBase</a> class implements the Scintilla editor widget and its low-level API. </p>
<p>Scintilla (<a href="http://www.scintilla.org">http://www.scintilla.org</a>) is a powerful C++ editor class that supports many features including syntax styling, error indicators, code completion and call tips. It is particularly useful as a programmer's editor.</p>
<p><a class="el" href="classQsciScintillaBase.html" title="The QsciScintillaBase class implements the Scintilla editor widget and its low-level API.">QsciScintillaBase</a> is a port to Qt of Scintilla. It implements the standard Scintilla API which consists of a number of messages each taking up to two arguments.</p>
<p>See <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> for the implementation of a higher level API that is more consistent with the rest of the Qt toolkit. </p>
</div><h2 class="groupheader">Member Enumeration Documentation</h2>
<a id="ad9c35f7540b2457103db9cf8c877784a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad9c35f7540b2457103db9cf8c877784a">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The low-level Scintilla API is implemented as a set of messages each of which takes up to two parameters (<em>wParam</em> and <em>lParam</em>) and optionally return a value. This enum defines all the possible messages. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aaada4cbb31d6583ed80804e4b94cd4023"></a>SCI_ADDTEXT&#160;</td><td class="fielddoc"><p>This message appends some text to the end of the document. <em>wParam</em> is the length of the text. <em>lParam</em> is the text to be appended. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aaf1289f2530afb81cc99e2b7e2e2cad28"></a>SCI_GETCURRENTPOS&#160;</td><td class="fielddoc"><p>This message returns the current position. </p><pre class="fragment">    \sa SCI_SETCURRENTPOS 
</pre> </td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aafbdd81cde4931224e6f87aceba707a04"></a>SCI_GETANCHOR&#160;</td><td class="fielddoc"><p>This message returns the anchor. </p><pre class="fragment">    \sa SCI_SETANCHOR 
</pre> </td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa20e9df7da839e5b9e2edd2366a7ecb97"></a>SCI_SETSAVEPOINT&#160;</td><td class="fielddoc"><p>This message marks the current state of the text as the the save point. This is usually done when the text is saved or loaded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#a0db8c3ad0764a96f3ccf0fec71de0d26">SCN_SAVEPOINTREACHED()</a>, <a class="el" href="classQsciScintillaBase.html#af3a619a5e59cef000f0b550e809c94de">SCN_SAVEPOINTLEFT()</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa4db578069d526ece8c0a9d08869a3033"></a>SCI_MARKERLINEFROMHANDLE&#160;</td><td class="fielddoc"><p>This message returns the line that contains a particular instance of a marker. <em>wParam</em> is the handle of the marker.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1be8617303dc15428758e22749267263">SCI_MARKERADD</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa007cbfb293accfd436ea9443b2678327"></a>SCI_MARKERDELETEHANDLE&#160;</td><td class="fielddoc"><p>This message removes a particular instance of a marker. <em>wParam</em> is the handle of the marker.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1be8617303dc15428758e22749267263">SCI_MARKERADD</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa3c6da358d0bc87040b30811bbcbf8cf7"></a>SCI_GOTOPOS&#160;</td><td class="fielddoc"><p>This message clears the current selection and sets the current position. <em>wParam</em> is the new current position.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aabcd34a065f08d19b10ca6caaa78d3e78">SCI_SETCURRENTPOS</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa9b577390896af25454459e1a9e08ad2e"></a>SCI_SETANCHOR&#160;</td><td class="fielddoc"><p>This message sets the anchor. <em>wParam</em> is the new anchor.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aafbdd81cde4931224e6f87aceba707a04">SCI_GETANCHOR</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa820d8d563cb319ff42e5b9ea709d839d"></a>SCI_GETENDSTYLED&#160;</td><td class="fielddoc"><p>This message returns the character position of the start of the text that needs to be syntax styled.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#a72c0bc1c83fd675714626cd786ca4fb9">SCN_STYLENEEDED()</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa370a2c2674421348d23ecb97ff981b2a"></a>SCI_MARKERDEFINE&#160;</td><td class="fielddoc"><p>This message sets the symbol used to draw one of 32 markers. Some markers have pre-defined uses, see the SC_MARKNUM_* values. <em>wParam</em> is the number of the marker. <em>lParam</em> is the marker symbol and is one of the SC_MARK_* values.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1be8617303dc15428758e22749267263">SCI_MARKERADD</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaffe2901cffbccede9b0b5d1636bb5e9f">SCI_MARKERDEFINEPIXMAP</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa8ff17201e6d0cb9fe6e738a7a2e81932">SCI_MARKERDEFINERGBAIMAGE</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa1fb7b42e9fbbe27b662b0edb21ac2d2f"></a>SCI_MARKERSETFORE&#160;</td><td class="fielddoc"><p>This message sets the foreground colour used to draw a marker. A colour is represented as a 24 bit value. The 8 least significant bits correspond to red, the middle 8 bits correspond to green, and the 8 most significant bits correspond to blue. The default value is 0x000000. <em>wParam</em> is the number of the marker. <em>lParam</em> is the colour.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa0419ba42e62661c245af25007bac3bfe">SCI_MARKERSETBACK</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa0419ba42e62661c245af25007bac3bfe"></a>SCI_MARKERSETBACK&#160;</td><td class="fielddoc"><p>This message sets the background colour used to draw a marker. A colour is represented as a 24 bit value. The 8 least significant bits correspond to red, the middle 8 bits correspond to green, and the 8 most significant bits correspond to blue. The default value is 0xffffff. <em>wParam</em> is the number of the marker. <em>lParam</em> is the colour.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1fb7b42e9fbbe27b662b0edb21ac2d2f">SCI_MARKERSETFORE</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa1be8617303dc15428758e22749267263"></a>SCI_MARKERADD&#160;</td><td class="fielddoc"><p>This message adds a marker to a line. A handle for the marker is returned which can be used to track the marker's position. <em>wParam</em> is the line number. <em>lParam</em> is the number of the marker.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aae19516cd9746dbec20598773ad354d4e">SCI_MARKERDELETE</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa010af0bf4fe497e1b68fe1fb56580770">SCI_MARKERDELETEALL</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa007cbfb293accfd436ea9443b2678327">SCI_MARKERDELETEHANDLE</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aae19516cd9746dbec20598773ad354d4e"></a>SCI_MARKERDELETE&#160;</td><td class="fielddoc"><p>This message deletes a marker from a line. <em>wParam</em> is the line number. <em>lParam</em> is the number of the marker.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1be8617303dc15428758e22749267263">SCI_MARKERADD</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa010af0bf4fe497e1b68fe1fb56580770">SCI_MARKERDELETEALL</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa010af0bf4fe497e1b68fe1fb56580770"></a>SCI_MARKERDELETEALL&#160;</td><td class="fielddoc"><p>This message deletes all occurences of a marker. <em>wParam</em> is the number of the marker. If <em>wParam</em> is -1 then all markers are removed.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1be8617303dc15428758e22749267263">SCI_MARKERADD</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aae19516cd9746dbec20598773ad354d4e">SCI_MARKERDELETE</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aaaee02f504dec75c8b349150805440fd7"></a>SCI_MARKERGET&#160;</td><td class="fielddoc"><p>This message returns the 32 bit mask of markers at a line. <em>wParam</em> is the line number. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa1f843331fd750110c6f97fa443567b22"></a>SCI_MARKERNEXT&#160;</td><td class="fielddoc"><p>This message looks for the next line to contain at least one marker contained in a 32 bit mask of markers and returns the line number. <em>wParam</em> is the line number to start the search from. <em>lParam</em> is the mask of markers to search for.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1e455f05b605c2ba82be3baf05e3abe4">SCI_MARKERPREVIOUS</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa1e455f05b605c2ba82be3baf05e3abe4"></a>SCI_MARKERPREVIOUS&#160;</td><td class="fielddoc"><p>This message looks for the previous line to contain at least one marker contained in a 32 bit mask of markers and returns the line number. <em>wParam</em> is the line number to start the search from. <em>lParam</em> is the mask of markers to search for.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1f843331fd750110c6f97fa443567b22">SCI_MARKERNEXT</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aaffe2901cffbccede9b0b5d1636bb5e9f"></a>SCI_MARKERDEFINEPIXMAP&#160;</td><td class="fielddoc"><p>This message sets the symbol used to draw one of the 32 markers to a pixmap. Pixmaps use the SC_MARK_PIXMAP marker symbol. <em>wParam</em> is the number of the marker. <em>lParam</em> is a pointer to a QPixmap instance. Note that in other ports of Scintilla this is a pointer to either raw or textual XPM image data.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa370a2c2674421348d23ecb97ff981b2a">SCI_MARKERDEFINE</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa8ff17201e6d0cb9fe6e738a7a2e81932">SCI_MARKERDEFINERGBAIMAGE</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa0ee188f4bfe7289f454f99af191d1523"></a>SCI_SETMARGINTYPEN&#160;</td><td class="fielddoc"><p>This message sets what can be displayed in a margin. <em>wParam</em> is the number of the margin. <em>lParam</em> is the logical or of the SC_MARGIN_* values.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa919cf8a6d08d570e00ece099ff62010c">SCI_GETMARGINTYPEN</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa919cf8a6d08d570e00ece099ff62010c"></a>SCI_GETMARGINTYPEN&#160;</td><td class="fielddoc"><p>This message returns what can be displayed in a margin. <em>wParam</em> is the number of the margin.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa0ee188f4bfe7289f454f99af191d1523">SCI_SETMARGINTYPEN</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa1195d46181a565b14806d94595fc7aa6"></a>SCI_SETMARGINWIDTHN&#160;</td><td class="fielddoc"><p>This message sets the width of a margin in pixels. <em>wParam</em> is the number of the margin. <em>lParam</em> is the new margin width.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa3654140951ae95d75f21c43cdcd91a43">SCI_GETMARGINWIDTHN</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa3654140951ae95d75f21c43cdcd91a43"></a>SCI_GETMARGINWIDTHN&#160;</td><td class="fielddoc"><p>This message returns the width of a margin in pixels. <em>wParam</em> is the number of the margin.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1195d46181a565b14806d94595fc7aa6">SCI_SETMARGINWIDTHN</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aad1cec6e4e0ad45ce7d7edad7acb8a3b5"></a>SCI_SETMARGINMASKN&#160;</td><td class="fielddoc"><p>This message sets the mask of a margin. The mask is a 32 value with one bit for each possible marker. If a bit is set then the corresponding marker is displayed. By default, all markers are displayed. <em>wParam</em> is the number of the margin. <em>lParam</em> is the new margin mask.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aadbd2eceef7f59bcda7d7db01a4aa7c7b">SCI_GETMARGINMASKN</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa370a2c2674421348d23ecb97ff981b2a">SCI_MARKERDEFINE</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aadbd2eceef7f59bcda7d7db01a4aa7c7b"></a>SCI_GETMARGINMASKN&#160;</td><td class="fielddoc"><p>This message returns the mask of a margin. <em>wParam</em> is the number of the margin.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aad1cec6e4e0ad45ce7d7edad7acb8a3b5">SCI_SETMARGINMASKN</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa64b07178255dc95b19a7c8feabaac1b2"></a>SCI_SETMARGINSENSITIVEN&#160;</td><td class="fielddoc"><p>This message sets the sensitivity of a margin to mouse clicks. <em>wParam</em> is the number of the margin. <em>lParam</em> is non-zero to make the margin sensitive to mouse clicks. When the mouse is clicked the <a class="el" href="classQsciScintillaBase.html#a722a2f16b67ef5f46def6914a6e178c3">SCN_MARGINCLICK()</a> signal is emitted.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaffc41be0dbc2eb4b00438f0b489c7c88">SCI_GETMARGINSENSITIVEN</a>, <a class="el" href="classQsciScintillaBase.html#a722a2f16b67ef5f46def6914a6e178c3">SCN_MARGINCLICK()</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aaffc41be0dbc2eb4b00438f0b489c7c88"></a>SCI_GETMARGINSENSITIVEN&#160;</td><td class="fielddoc"><p>This message returns the sensitivity of a margin to mouse clicks. <em>wParam</em> is the number of the margin.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa64b07178255dc95b19a7c8feabaac1b2">SCI_SETMARGINSENSITIVEN</a>, <a class="el" href="classQsciScintillaBase.html#a722a2f16b67ef5f46def6914a6e178c3">SCN_MARGINCLICK()</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aac5d3e4edd15f65d5e500d90590e443a9"></a>SCI_SETMARGINCURSORN&#160;</td><td class="fielddoc"><p>This message sets the cursor shape displayed over a margin. <em>wParam</em> is the number of the margin. <em>lParam</em> is the cursor shape, normally either SC_CURSORARROW or SC_CURSORREVERSEARROW. Note that, currently, QScintilla implements both of these as Qt::ArrowCursor.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa282bc83043fda7837568925243fcb384">SCI_GETMARGINCURSORN</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa282bc83043fda7837568925243fcb384"></a>SCI_GETMARGINCURSORN&#160;</td><td class="fielddoc"><p>This message returns the cursor shape displayed over a margin. <em>wParam</em> is the number of the margin.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aac5d3e4edd15f65d5e500d90590e443a9">SCI_SETMARGINCURSORN</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa98efd72455b4555e6d4cbd9cd79d2a5b"></a>SCI_GETREADONLY&#160;</td><td class="fielddoc"><p>This message returns a non-zero value if the document is read-only. </p><pre class="fragment">    \sa SCI_SETREADONLY 
</pre> </td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aabcd34a065f08d19b10ca6caaa78d3e78"></a>SCI_SETCURRENTPOS&#160;</td><td class="fielddoc"><p>This message sets the current position. <em>wParam</em> is the new current position.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaf1289f2530afb81cc99e2b7e2e2cad28">SCI_GETCURRENTPOS</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aaa5af597c3c35c97cbe9f6dd98462594c"></a>SCI_GETMODIFY&#160;</td><td class="fielddoc"><p>This message returns a non-zero value if the document has been modified. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aaa07157383b442ab2d2be8c2d03078fc2"></a>SCI_SETREADONLY&#160;</td><td class="fielddoc"><p>This message sets the read-only state of the document. <em>wParam</em> is the new read-only state of the document.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa98efd72455b4555e6d4cbd9cd79d2a5b">SCI_GETREADONLY</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aac8f52176e19feec95c354452b6358d93"></a>SCI_EMPTYUNDOBUFFER&#160;</td><td class="fielddoc"><p>This message empties the undo buffer. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aaeadc6fabc9859b2e52f9cfa23732f004"></a>SCI_SETTEXT&#160;</td><td class="fielddoc"><p>This message sets the text of the document. <em>wParam</em> is unused. <em>lParam</em> is the new text of the document.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa4bc0cd151979992bc5015852c5dbfbfe">SCI_GETTEXT</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa4bc0cd151979992bc5015852c5dbfbfe"></a>SCI_GETTEXT&#160;</td><td class="fielddoc"><p>This message gets the text of the document. <em>wParam</em> is size of the buffer that the text is copied to. <em>lParam</em> is the address of the buffer that the text is copied to.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaeadc6fabc9859b2e52f9cfa23732f004">SCI_SETTEXT</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aadd626735df321c6b994c887cfad61ed4"></a>SCI_GETTEXTLENGTH&#160;</td><td class="fielddoc"><p>This message returns the length of the document. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa99467be94f4357e1be0ddf72bde6ae5a"></a>SCI_AUTOCSETMAXHEIGHT&#160;</td><td class="fielddoc"><p>This message is not implemented. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa5158fc6bdc2ceb345246b7f4ca45de04"></a>SCI_TEXTWIDTH&#160;</td><td class="fielddoc"><p>This message returns the width of some text when rendered in a particular style. <em>wParam</em> is the style number and is one of the STYLE_* values or one of the styles defined by a lexer. <em>lParam</em> is a pointer to the text. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa5c17528701e5a34fc8b685be0914d4a8"></a>SCI_REGISTERIMAGE&#160;</td><td class="fielddoc"><p>This message takes a copy of an image and registers it so that it can be refered to by a unique integer identifier. <em>wParam</em> is the image's identifier. <em>lParam</em> is a pointer to a QPixmap instance. Note that in other ports of Scintilla this is a pointer to either raw or textual XPM image data.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa4eca65e764b9d0ef2fb23d22bc872bcb">SCI_CLEARREGISTEREDIMAGES</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaf1e4de8ebec57382f46449112d4f6821">SCI_REGISTERRGBAIMAGE</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa4eca65e764b9d0ef2fb23d22bc872bcb"></a>SCI_CLEARREGISTEREDIMAGES&#160;</td><td class="fielddoc"><p>This message de-registers all currently registered images. </p><pre class="fragment">    \sa SCI_REGISTERIMAGE, SCI_REGISTERRGBAIMAGE 
</pre> </td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa0fd449316fa24a3cb53721cf17b9f684"></a>SCI_COPYALLOWLINE&#160;</td><td class="fielddoc"><p>This message copies the selection. If the selection is empty then copy the line with the caret. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa4e6fc6b70c85f83622c9a17516bb2675"></a>SCI_GETCHARACTERPOINTER&#160;</td><td class="fielddoc"><p>This message returns a pointer to the document text. Any subsequent message will invalidate the pointer. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa93791e94e6f6a9382f1f7e29f341f342"></a>SCI_RGBAIMAGESETWIDTH&#160;</td><td class="fielddoc"><p>This message sets the width of an RGBA image specified by a future call to SCI_MARKERDEFINERGBAIMAGE or SCI_REGISTERRGBAIMAGE.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aabf4b5d1cf3d1aa52c010b489c2ccffc6">SCI_RGBAIMAGESETHEIGHT</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa8ff17201e6d0cb9fe6e738a7a2e81932">SCI_MARKERDEFINERGBAIMAGE</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaf1e4de8ebec57382f46449112d4f6821">SCI_REGISTERRGBAIMAGE</a>. </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aabf4b5d1cf3d1aa52c010b489c2ccffc6"></a>SCI_RGBAIMAGESETHEIGHT&#160;</td><td class="fielddoc"><p>This message sets the height of an RGBA image specified by a future call to SCI_MARKERDEFINERGBAIMAGE or SCI_REGISTERRGBAIMAGE.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa93791e94e6f6a9382f1f7e29f341f342">SCI_RGBAIMAGESETWIDTH</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa8ff17201e6d0cb9fe6e738a7a2e81932">SCI_MARKERDEFINERGBAIMAGE</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaf1e4de8ebec57382f46449112d4f6821">SCI_REGISTERRGBAIMAGE</a>. </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa8ff17201e6d0cb9fe6e738a7a2e81932"></a>SCI_MARKERDEFINERGBAIMAGE&#160;</td><td class="fielddoc"><p>This message sets the symbol used to draw one of the 32 markers to an RGBA image. RGBA images use the SC_MARK_RGBAIMAGE marker symbol. <em>wParam</em> is the number of the marker. <em>lParam</em> is a pointer to a QImage instance. Note that in other ports of Scintilla this is a pointer to raw RGBA image data.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa370a2c2674421348d23ecb97ff981b2a">SCI_MARKERDEFINE</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaffe2901cffbccede9b0b5d1636bb5e9f">SCI_MARKERDEFINEPIXMAP</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aaf1e4de8ebec57382f46449112d4f6821"></a>SCI_REGISTERRGBAIMAGE&#160;</td><td class="fielddoc"><p>This message takes a copy of an image and registers it so that it can be refered to by a unique integer identifier. <em>wParam</em> is the image's identifier. <em>lParam</em> is a pointer to a QImage instance. Note that in other ports of Scintilla this is a pointer to raw RGBA image data.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa4eca65e764b9d0ef2fb23d22bc872bcb">SCI_CLEARREGISTEREDIMAGES</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa5c17528701e5a34fc8b685be0914d4a8">SCI_REGISTERIMAGE</a> </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa29b928416c21edb11e32d4325764fcc7"></a>SCI_SETLEXER&#160;</td><td class="fielddoc"><p>This message sets the number of the lexer to use for syntax styling. <em>wParam</em> is the number of the lexer and is one of the SCLEX_* values. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aaf625e0ecead2e1d0bc3e0cefe2e8954a"></a>SCI_GETLEXER&#160;</td><td class="fielddoc"><p>This message returns the number of the lexer being used for syntax styling. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad9c35f7540b2457103db9cf8c877784aa6309b2b8bf3813c1041d31cb54ce3feb"></a>SCI_SETLEXERLANGUAGE&#160;</td><td class="fielddoc"><p>This message sets the name of the lexer to use for syntax styling. <em>wParam</em> is unused. <em>lParam</em> is the name of the lexer. </p>
</td></tr>
</table>

</div>
</div>
<a id="aad0a6f5e5ad05455480c3530435dd6aa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aad0a6f5e5ad05455480c3530435dd6aa">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the different selection modes.</p>
<dl class="section see"><dt>See also</dt><dd>SCI_GETSELECTIONMODE, SCI_SETSELECTIONMODE </dd></dl>

</div>
</div>
<a id="ad004786b74db7858f6642c23447a214c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad004786b74db7858f6642c23447a214c">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the different marker symbols.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa370a2c2674421348d23ecb97ff981b2a">SCI_MARKERDEFINE</a> </dd></dl>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214cabf08078081d1fb79be98e1b5a6401ec3"></a>SC_MARK_CIRCLE&#160;</td><td class="fielddoc"><p>A circle. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca39e5d4cae13901613bcfae619cd496b5"></a>SC_MARK_ROUNDRECT&#160;</td><td class="fielddoc"><p>A rectangle. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca940ced5307e462959ce165d8717a31d4"></a>SC_MARK_ARROW&#160;</td><td class="fielddoc"><p>A triangle pointing to the right. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214cafa1a0af509be0284f7c69df8134d85ca"></a>SC_MARK_SMALLRECT&#160;</td><td class="fielddoc"><p>A smaller rectangle. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca60f9340f78d0c386cb2df238849f121d"></a>SC_MARK_SHORTARROW&#160;</td><td class="fielddoc"><p>An arrow pointing to the right. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214cacf6e7cca56bcd03b660b3590153d1075"></a>SC_MARK_EMPTY&#160;</td><td class="fielddoc"><p>An invisible marker that allows code to track the movement of lines. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca6f1caf375a3079d67c36998c1bd453a4"></a>SC_MARK_ARROWDOWN&#160;</td><td class="fielddoc"><p>A triangle pointing down. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca7baf71a4e105fbebbaa7803a3f722b0f"></a>SC_MARK_MINUS&#160;</td><td class="fielddoc"><p>A drawn minus sign. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214cae324f61ed2740c6be760489cbaa69fb8"></a>SC_MARK_PLUS&#160;</td><td class="fielddoc"><p>A drawn plus sign. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca8770dbe317581062d5d1bcb85592b784"></a>SC_MARK_VLINE&#160;</td><td class="fielddoc"><p>A vertical line drawn in the background colour. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214caf591d473d118d6fa98adf5e73fd9c61d"></a>SC_MARK_LCORNER&#160;</td><td class="fielddoc"><p>A bottom left corner drawn in the background colour. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214caa9ae33880a1ee19ce4db6544bb61a84d"></a>SC_MARK_TCORNER&#160;</td><td class="fielddoc"><p>A vertical line with a centre right horizontal line drawn in the background colour. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca6b210dd7ba9ae1b8c503965b6e9ada9a"></a>SC_MARK_BOXPLUS&#160;</td><td class="fielddoc"><p>A drawn plus sign in a box. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca3582c3195c0533bca604a874ee1ecab8"></a>SC_MARK_BOXPLUSCONNECTED&#160;</td><td class="fielddoc"><p>A drawn plus sign in a connected box. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214cacebb22ccb805fa137c65eda743d32e0a"></a>SC_MARK_BOXMINUS&#160;</td><td class="fielddoc"><p>A drawn minus sign in a box. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca8d928f238170d0765acb492d0e8f0f65"></a>SC_MARK_BOXMINUSCONNECTED&#160;</td><td class="fielddoc"><p>A drawn minus sign in a connected box. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca85a6c4d68e4c936c46c8711f656d95ca"></a>SC_MARK_LCORNERCURVE&#160;</td><td class="fielddoc"><p>A rounded bottom left corner drawn in the background colour. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca787e7e471b489bda535116b75765acad"></a>SC_MARK_TCORNERCURVE&#160;</td><td class="fielddoc"><p>A vertical line with a centre right curved line drawn in the background colour. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca222004d413ee607197204f26950f3a0c"></a>SC_MARK_CIRCLEPLUS&#160;</td><td class="fielddoc"><p>A drawn plus sign in a circle. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca5293176024a0bba9eeb54b061a2930f9"></a>SC_MARK_CIRCLEPLUSCONNECTED&#160;</td><td class="fielddoc"><p>A drawn plus sign in a connected box. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca58fc2ba342cf7cc9e5f5e9a59d4319bc"></a>SC_MARK_CIRCLEMINUS&#160;</td><td class="fielddoc"><p>A drawn minus sign in a circle. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca2bbb4d3dea9e0b75ef9374c1c2b23c65"></a>SC_MARK_CIRCLEMINUSCONNECTED&#160;</td><td class="fielddoc"><p>A drawn minus sign in a connected circle. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca8a44e2cee896ee89527e1d026e8cd9ff"></a>SC_MARK_BACKGROUND&#160;</td><td class="fielddoc"><p>No symbol is drawn but the line is drawn with the same background color as the marker's. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca961e0562a26ab763fba1bc1e92123b85"></a>SC_MARK_DOTDOTDOT&#160;</td><td class="fielddoc"><p>Three drawn dots. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca6f07eeddc235e313c4ca597220c71a0c"></a>SC_MARK_ARROWS&#160;</td><td class="fielddoc"><p>Three drawn arrows pointing right. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca464ae044427aa620a0668510ff1430b9"></a>SC_MARK_PIXMAP&#160;</td><td class="fielddoc"><p>An XPM format pixmap. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca4f29b2c933a525fe0a80f0a58ba7eb61"></a>SC_MARK_FULLRECT&#160;</td><td class="fielddoc"><p>A full rectangle (ie. the margin background) using the marker's background color. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca08a00107e2110dce658fe7cb10f75e58"></a>SC_MARK_LEFTRECT&#160;</td><td class="fielddoc"><p>A left rectangle (ie. the left part of the margin background) using the marker's background color. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca53a38f2234ab3e6df8d6cec09ecd7318"></a>SC_MARK_AVAILABLE&#160;</td><td class="fielddoc"><p>The value is available for plugins to use. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214cadf0e9f9a2980c5e693c67819a64f132e"></a>SC_MARK_UNDERLINE&#160;</td><td class="fielddoc"><p>The line is underlined using the marker's background color. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214caa1866931fbc9dea971e6ae3f5be83abd"></a>SC_MARK_RGBAIMAGE&#160;</td><td class="fielddoc"><p>A RGBA format image. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca500892fc4eef318262b009f6eddc9eda"></a>SC_MARK_BOOKMARK&#160;</td><td class="fielddoc"><p>A bookmark. </p>
</td></tr>
<tr><td class="fieldname"><a id="ad004786b74db7858f6642c23447a214ca8c649856f102e81a5caa5a92f28b38fd"></a>SC_MARK_CHARACTER&#160;</td><td class="fielddoc"><p>Characters can be used as symbols by adding this to the ASCII value of the character. </p>
</td></tr>
</table>

</div>
</div>
<a id="ab7ed107d6ace096e9026c31145c48b41"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab7ed107d6ace096e9026c31145c48b41">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines what can be displayed in a margin.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa919cf8a6d08d570e00ece099ff62010c">SCI_GETMARGINTYPEN</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa0ee188f4bfe7289f454f99af191d1523">SCI_SETMARGINTYPEN</a> </dd></dl>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="ab7ed107d6ace096e9026c31145c48b41a7776c14d3a1424576a26a8da304b96bf"></a>SC_MARGIN_SYMBOL&#160;</td><td class="fielddoc"><p>The margin can display symbols. Note that all margins can display symbols. </p>
</td></tr>
<tr><td class="fieldname"><a id="ab7ed107d6ace096e9026c31145c48b41a55a92a7661156a126c48237234251e1d"></a>SC_MARGIN_NUMBER&#160;</td><td class="fielddoc"><p>The margin will display line numbers. </p>
</td></tr>
<tr><td class="fieldname"><a id="ab7ed107d6ace096e9026c31145c48b41a68a70615f89282762ba21aa6ec629dac"></a>SC_MARGIN_BACK&#160;</td><td class="fielddoc"><p>The margin's background color will be set to the default background color. </p>
</td></tr>
<tr><td class="fieldname"><a id="ab7ed107d6ace096e9026c31145c48b41a2f3ac8cfede54b81db88e29b7f81e19c"></a>SC_MARGIN_FORE&#160;</td><td class="fielddoc"><p>The margin's background color will be set to the default foreground color. </p>
</td></tr>
<tr><td class="fieldname"><a id="ab7ed107d6ace096e9026c31145c48b41aa29598ff9ba1349daee66560cdd692bd"></a>SC_MARGIN_TEXT&#160;</td><td class="fielddoc"><p>The margin will display text. </p>
</td></tr>
<tr><td class="fieldname"><a id="ab7ed107d6ace096e9026c31145c48b41af99d2ba5aa3873f646a8eac1a889de6a"></a>SC_MARGIN_RTEXT&#160;</td><td class="fielddoc"><p>The margin will display right justified text. </p>
</td></tr>
<tr><td class="fieldname"><a id="ab7ed107d6ace096e9026c31145c48b41aabd3cb3735935f9be890931a34d07989"></a>SC_MARGIN_COLOUR&#160;</td><td class="fielddoc"><p>The margin's background color will be set to the color set by SCI_SETMARGINBACKN. </p>
</td></tr>
</table>

</div>
</div>
<a id="ae92e21c6957f026dbfd00008348e8b50"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae92e21c6957f026dbfd00008348e8b50">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the different indentation guide views.</p>
<dl class="section see"><dt>See also</dt><dd>SCI_GETINDENTATIONGUIDES, SCI_SETINDENTATIONGUIDES </dd></dl>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="ae92e21c6957f026dbfd00008348e8b50ad814f18251426f392498fd2969e11d65"></a>SC_IV_NONE&#160;</td><td class="fielddoc"><p>No indentation guides are shown. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae92e21c6957f026dbfd00008348e8b50aa8b077f3d8af29de8fae45dfd0885298"></a>SC_IV_REAL&#160;</td><td class="fielddoc"><p>Indentation guides are shown inside real indentation white space. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae92e21c6957f026dbfd00008348e8b50a63693e8e8da215430f8b94630cbad3c0"></a>SC_IV_LOOKFORWARD&#160;</td><td class="fielddoc"><p>Indentation guides are shown beyond the actual indentation up to the level of the next non-empty line. If the previous non-empty line was a fold header then indentation guides are shown for one more level of indent than that line. This setting is good for Python. </p>
</td></tr>
<tr><td class="fieldname"><a id="ae92e21c6957f026dbfd00008348e8b50ac3746adc7ec9881c9a46be88e26417d1"></a>SC_IV_LOOKBOTH&#160;</td><td class="fielddoc"><p>Indentation guides are shown beyond the actual indentation up to the level of the next non-empty line or previous non-empty line whichever is the greater. This setting is good for most languages. </p>
</td></tr>
</table>

</div>
</div>
<a id="a76f793f6e6ce5b6f14b3925e78ea2aa6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a76f793f6e6ce5b6f14b3925e78ea2aa6">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>This enum defines the different modifier keys. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a76f793f6e6ce5b6f14b3925e78ea2aa6a6097124d46dc23dbb028fb340b4aa17e"></a>SCMOD_NORM&#160;</td><td class="fielddoc"><p>No modifier key. </p>
</td></tr>
<tr><td class="fieldname"><a id="a76f793f6e6ce5b6f14b3925e78ea2aa6ad3e496f1bdcc19e0b87c83c624e9f184"></a>SCMOD_SHIFT&#160;</td><td class="fielddoc"><p>Shift key. </p>
</td></tr>
<tr><td class="fieldname"><a id="a76f793f6e6ce5b6f14b3925e78ea2aa6a944d24d92f0c62a4f519936199d74198"></a>SCMOD_CTRL&#160;</td><td class="fielddoc"><p>Control key (the Command key on OS/X, the Ctrl key on other platforms). </p>
</td></tr>
<tr><td class="fieldname"><a id="a76f793f6e6ce5b6f14b3925e78ea2aa6aaa78aa9b07d1b2afe030262223eba11a"></a>SCMOD_ALT&#160;</td><td class="fielddoc"><p>Alt key. </p>
</td></tr>
<tr><td class="fieldname"><a id="a76f793f6e6ce5b6f14b3925e78ea2aa6a92a9efa0e26fb75aa9d6584c715aa465"></a>SCMOD_SUPER&#160;</td><td class="fielddoc"><p>This is the same as SCMOD_META on all platforms. </p>
</td></tr>
<tr><td class="fieldname"><a id="a76f793f6e6ce5b6f14b3925e78ea2aa6add02edfef385cd3b3020235bc752eda7"></a>SCMOD_META&#160;</td><td class="fielddoc"><p>Meta key (the Ctrl key on OS/X, the Windows key on other platforms). </p>
</td></tr>
</table>

</div>
</div>
<a id="aa4ab44fd6a7374eb16d07762aa51c7c0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa4ab44fd6a7374eb16d07762aa51c7c0">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the different language lexers.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaf625e0ecead2e1d0bc3e0cefe2e8954a">SCI_GETLEXER</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa29b928416c21edb11e32d4325764fcc7">SCI_SETLEXER</a> </dd></dl>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a62931496707b79f9d5b348aacbd51a6e"></a>SCLEX_CONTAINER&#160;</td><td class="fielddoc"><p>No lexer is selected and the SCN_STYLENEEDED signal is emitted so that the application can style the text as needed. This is the default. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a8a264dd8fe734630be400388fac8f588"></a>SCLEX_NULL&#160;</td><td class="fielddoc"><p>Select the null lexer that does no syntax styling. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0aaa0d7fa0c2396811b59b5e6ba6c811f1"></a>SCLEX_PYTHON&#160;</td><td class="fielddoc"><p>Select the Python lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a504f72d70f56dcb53fb908fe79452138"></a>SCLEX_CPP&#160;</td><td class="fielddoc"><p>Select the C++ lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a00ae3e9a26cdc1afac630a91f3b3c7ec"></a>SCLEX_HTML&#160;</td><td class="fielddoc"><p>Select the HTML lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a1d7fee124bcdc3de441f5051c53eff92"></a>SCLEX_XML&#160;</td><td class="fielddoc"><p>Select the XML lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a9ef2274168be1be3f691d59aa142f170"></a>SCLEX_PERL&#160;</td><td class="fielddoc"><p>Select the Perl lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0af1a6b060c43736ae87b701da137aaf51"></a>SCLEX_SQL&#160;</td><td class="fielddoc"><p>Select the SQL lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a552be64626f5d82c3d77e27ed485124f"></a>SCLEX_VB&#160;</td><td class="fielddoc"><p>Select the Visual Basic lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ae51ad6d026758e0fde01d796d72d0815"></a>SCLEX_PROPERTIES&#160;</td><td class="fielddoc"><p>Select the lexer for properties style files. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a1beef12bbc9c9835a6791267c8fcb10a"></a>SCLEX_ERRORLIST&#160;</td><td class="fielddoc"><p>Select the lexer for error list style files. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a990148a08b2f7a1585691ee984876863"></a>SCLEX_MAKEFILE&#160;</td><td class="fielddoc"><p>Select the Makefile lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ae894213f20cdd7eae927718c87cbfae4"></a>SCLEX_BATCH&#160;</td><td class="fielddoc"><p>Select the Windows batch file lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a6b110854fbef28d60067b82faf5ed229"></a>SCLEX_LATEX&#160;</td><td class="fielddoc"><p>Select the LaTex lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a9975c914f242664d8225e3692f88ac31"></a>SCLEX_LUA&#160;</td><td class="fielddoc"><p>Select the Lua lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a254b0148fea4c8f6e170ef09bae369e7"></a>SCLEX_DIFF&#160;</td><td class="fielddoc"><p>Select the lexer for diff output. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a477ce8c2cdaac994e2ec4022e67ee185"></a>SCLEX_CONF&#160;</td><td class="fielddoc"><p>Select the lexer for Apache configuration files. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0aa81b3ab54ed446bd82fd8e47bb716efe"></a>SCLEX_PASCAL&#160;</td><td class="fielddoc"><p>Select the Pascal lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a2c30b22ff5f0f07f8ccf96eb0c0eb5d6"></a>SCLEX_AVE&#160;</td><td class="fielddoc"><p>Select the Avenue lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a56c1b1e39b9c7e20faa9b7420d54e7a5"></a>SCLEX_ADA&#160;</td><td class="fielddoc"><p>Select the Ada lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a1b4009430261f11f17487ad843007d04"></a>SCLEX_LISP&#160;</td><td class="fielddoc"><p>Select the Lisp lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ab271a6111144900d2d93de516b1035eb"></a>SCLEX_RUBY&#160;</td><td class="fielddoc"><p>Select the Ruby lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a4e7d76804b75f4b89f1b315bfc52972f"></a>SCLEX_EIFFEL&#160;</td><td class="fielddoc"><p>Select the Eiffel lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a26b6e157b2a4595977de9c31c44c5f36"></a>SCLEX_EIFFELKW&#160;</td><td class="fielddoc"><p>Select the Eiffel lexer folding at keywords. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a3d423d890cfc3b987d62d48ede1ec887"></a>SCLEX_TCL&#160;</td><td class="fielddoc"><p>Select the Tcl lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a3c92b60cfd0065deb73541166ab412cd"></a>SCLEX_NNCRONTAB&#160;</td><td class="fielddoc"><p>Select the lexer for nnCron files. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ac26190310f45bf026d031fd52729f310"></a>SCLEX_BULLANT&#160;</td><td class="fielddoc"><p>Select the Bullant lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a0efcb32e5b56db847054c8b5f4778581"></a>SCLEX_VBSCRIPT&#160;</td><td class="fielddoc"><p>Select the VBScript lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ac6732578f1c51e3a2757dddb839d7b5d"></a>SCLEX_ASP&#160;</td><td class="fielddoc"><p>Select the ASP lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a4a9d8ecf3ccab78020f78ad4efb510d6"></a>SCLEX_PHP&#160;</td><td class="fielddoc"><p>Select the PHP lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a207b1e77e47550f0b0787a107a206b71"></a>SCLEX_BAAN&#160;</td><td class="fielddoc"><p>Select the Baan lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a457d5320bb0deebd765830974964c4ca"></a>SCLEX_MATLAB&#160;</td><td class="fielddoc"><p>Select the Matlab lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a12d07a2dd6cc648226ecdbc41ef0d169"></a>SCLEX_SCRIPTOL&#160;</td><td class="fielddoc"><p>Select the Scriptol lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a286421d44d37e6eade78481e3d063540"></a>SCLEX_ASM&#160;</td><td class="fielddoc"><p>Select the assembler lexer (';' comment character). </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a8dd4142d9043b7b15c235c038a8abf0f"></a>SCLEX_CPPNOCASE&#160;</td><td class="fielddoc"><p>Select the C++ lexer with case insensitive keywords. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a14a8a752af012a2c9444b5b291108574"></a>SCLEX_FORTRAN&#160;</td><td class="fielddoc"><p>Select the FORTRAN lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a94cdec496a332379e7cb47c116c318c6"></a>SCLEX_F77&#160;</td><td class="fielddoc"><p>Select the FORTRAN77 lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a9c08d490101986eb17aab67a1fb7159f"></a>SCLEX_CSS&#160;</td><td class="fielddoc"><p>Select the CSS lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0af510951e33b06ef3e995d69c53a94fdc"></a>SCLEX_POV&#160;</td><td class="fielddoc"><p>Select the POV lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a6f07bc63049dc24bd3afc9e8ebac18ce"></a>SCLEX_LOUT&#160;</td><td class="fielddoc"><p>Select the Basser Lout typesetting language lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a1d30f511ae4cc23f0bc43fd1ca6cda12"></a>SCLEX_ESCRIPT&#160;</td><td class="fielddoc"><p>Select the EScript lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a053e8c790c607f826c933729ada1a6c2"></a>SCLEX_PS&#160;</td><td class="fielddoc"><p>Select the PostScript lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a592ddfd7bb2d792a42e44a6a04640247"></a>SCLEX_NSIS&#160;</td><td class="fielddoc"><p>Select the NSIS lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a6d6709e5e960072a7c91b3e5b01a020a"></a>SCLEX_MMIXAL&#160;</td><td class="fielddoc"><p>Select the MMIX assembly language lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a7d602388b550d67454b1c11de9fac04e"></a>SCLEX_CLW&#160;</td><td class="fielddoc"><p>Select the Clarion lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a8a1b12c769eced15a1a54a87e7521a47"></a>SCLEX_CLWNOCASE&#160;</td><td class="fielddoc"><p>Select the Clarion lexer with case insensitive keywords. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a95c696054c8774351078fb670b591028"></a>SCLEX_LOT&#160;</td><td class="fielddoc"><p>Select the MPT text log file lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a7c021577e03000be86d0acd1ec6c502b"></a>SCLEX_YAML&#160;</td><td class="fielddoc"><p>Select the YAML lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0aff435fbce318cd18cadeae1be877bd41"></a>SCLEX_TEX&#160;</td><td class="fielddoc"><p>Select the TeX lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a345b6b3ec51466394faec02ecdb8dc2f"></a>SCLEX_METAPOST&#160;</td><td class="fielddoc"><p>Select the Metapost lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0acc275c470d9bfe05754cdf4e42a54741"></a>SCLEX_POWERBASIC&#160;</td><td class="fielddoc"><p>Select the PowerBASIC lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a44c24c20cdec1b6e482f69ed721a4077"></a>SCLEX_FORTH&#160;</td><td class="fielddoc"><p>Select the Forth lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0aa5ac4a29460ebae1edb850c87473a52c"></a>SCLEX_ERLANG&#160;</td><td class="fielddoc"><p>Select the Erlang lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a0bfd1f7f3d9ec8b9ea24bb00eb199704"></a>SCLEX_OCTAVE&#160;</td><td class="fielddoc"><p>Select the Octave lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a9cd5b9812fe0fb143740c8a5ac15431a"></a>SCLEX_MSSQL&#160;</td><td class="fielddoc"><p>Select the MS SQL lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0aa419285333430eff62c7d44b79786a3d"></a>SCLEX_VERILOG&#160;</td><td class="fielddoc"><p>Select the Verilog lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a67ce3e5d9bdff0bdb44d1a5aff3e69c4"></a>SCLEX_KIX&#160;</td><td class="fielddoc"><p>Select the KIX-Scripts lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ada00900f5ad22e170d494790194dfdcf"></a>SCLEX_GUI4CLI&#160;</td><td class="fielddoc"><p>Select the Gui4Cli lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a826c7c9b1bbf5079ff818003bbcdf78e"></a>SCLEX_SPECMAN&#160;</td><td class="fielddoc"><p>Select the Specman E lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a05181d968abb3e1fec89869dd14e2bae"></a>SCLEX_AU3&#160;</td><td class="fielddoc"><p>Select the AutoIt3 lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a176475983f8e4985ca616779de3be8db"></a>SCLEX_APDL&#160;</td><td class="fielddoc"><p>Select the APDL lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ab05738aa98869eb2b998fb6d063d9dbc"></a>SCLEX_BASH&#160;</td><td class="fielddoc"><p>Select the Bash lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a6bc934ce8070f4cd38f4c2619b165b01"></a>SCLEX_ASN1&#160;</td><td class="fielddoc"><p>Select the ASN.1 lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a893d2e14e9c835c6b1e52d43aaf8c577"></a>SCLEX_VHDL&#160;</td><td class="fielddoc"><p>Select the VHDL lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0aaf2da832f0698fe3cea0693f57d4b7d4"></a>SCLEX_CAML&#160;</td><td class="fielddoc"><p>Select the Caml lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a8ca1aa2873729ccadcc0c952d574299f"></a>SCLEX_BLITZBASIC&#160;</td><td class="fielddoc"><p>Select the BlitzBasic lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ae15512b5a294a4d9d87423e256a14874"></a>SCLEX_PUREBASIC&#160;</td><td class="fielddoc"><p>Select the PureBasic lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ae0b332697a33770b6f1ba537b942a87d"></a>SCLEX_HASKELL&#160;</td><td class="fielddoc"><p>Select the Haskell lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a582a3951e713f3e804e312345c120571"></a>SCLEX_PHPSCRIPT&#160;</td><td class="fielddoc"><p>Select the PHPScript lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a41b0513b5d681c2e8a5d76ca8ef8752d"></a>SCLEX_TADS3&#160;</td><td class="fielddoc"><p>Select the TADS3 lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a0c4f62b4ba53206637593684c27fed7f"></a>SCLEX_REBOL&#160;</td><td class="fielddoc"><p>Select the REBOL lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a13ce10215a927235a20b5b54739b6442"></a>SCLEX_SMALLTALK&#160;</td><td class="fielddoc"><p>Select the Smalltalk lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0af2efe26c56f871a45383153193e4e9e0"></a>SCLEX_FLAGSHIP&#160;</td><td class="fielddoc"><p>Select the FlagShip lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0aec034e1adf57a7349ed47f4848bb40c4"></a>SCLEX_CSOUND&#160;</td><td class="fielddoc"><p>Select the Csound lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0af7c6976f83643ba89841ad2eaf62c678"></a>SCLEX_FREEBASIC&#160;</td><td class="fielddoc"><p>Select the FreeBasic lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a1e8ba9f543d28f5470b3284c377caaef"></a>SCLEX_INNOSETUP&#160;</td><td class="fielddoc"><p>Select the InnoSetup lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0acce1c850472e07587f12f668d3b541e5"></a>SCLEX_OPAL&#160;</td><td class="fielddoc"><p>Select the Opal lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a6dde6455441154c518c71d14cbc384e8"></a>SCLEX_SPICE&#160;</td><td class="fielddoc"><p>Select the Spice lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0af89b580de6d8a9bffac12bf14b58489d"></a>SCLEX_D&#160;</td><td class="fielddoc"><p>Select the D lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a28cf4c57f311aa17f2dbf0f03761ce99"></a>SCLEX_CMAKE&#160;</td><td class="fielddoc"><p>Select the CMake lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ae20ac3b9f61ea931c3b12e0c462b1dd0"></a>SCLEX_GAP&#160;</td><td class="fielddoc"><p>Select the GAP lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0aa9a5c4cac509bcde1ea71e3fcc44c664"></a>SCLEX_PLM&#160;</td><td class="fielddoc"><p>Select the PLM lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a93e8366c515df085823e843354a8b3cd"></a>SCLEX_PROGRESS&#160;</td><td class="fielddoc"><p>Select the Progress lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a94d6c2b2fa424cbb4c8eb3749a9f934b"></a>SCLEX_ABAQUS&#160;</td><td class="fielddoc"><p>Select the Abaqus lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a5a68f1f87e9069301116d328e30f63c7"></a>SCLEX_ASYMPTOTE&#160;</td><td class="fielddoc"><p>Select the Asymptote lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a370fc35c7da9d2bdd2ab7088da3d7afe"></a>SCLEX_R&#160;</td><td class="fielddoc"><p>Select the R lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0aea0c54b674130c1ce336667af1468011"></a>SCLEX_MAGIK&#160;</td><td class="fielddoc"><p>Select the MagikSF lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ad32bd9c8bb2d41cfcf26a8ab7605cee8"></a>SCLEX_POWERSHELL&#160;</td><td class="fielddoc"><p>Select the PowerShell lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a705102c40d1227a12afd8da13b43ab00"></a>SCLEX_MYSQL&#160;</td><td class="fielddoc"><p>Select the MySQL lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a0d2bf09afa633715605a3305777dfc83"></a>SCLEX_PO&#160;</td><td class="fielddoc"><p>Select the gettext .po file lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a9add9caf532d347948e1c8038ab671e1"></a>SCLEX_TAL&#160;</td><td class="fielddoc"><p>Select the TAL lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ab87e1d9e6edd4f9ee6627d837c6152b3"></a>SCLEX_COBOL&#160;</td><td class="fielddoc"><p>Select the COBOL lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ad5f7ed0033d057fc7d84a3c80c5640be"></a>SCLEX_TACL&#160;</td><td class="fielddoc"><p>Select the TACL lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a1aa37e96400fba08b571e6f17100bb23"></a>SCLEX_SORCUS&#160;</td><td class="fielddoc"><p>Select the Sorcus lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a36d2c47f406db754feb03b7c530be79f"></a>SCLEX_POWERPRO&#160;</td><td class="fielddoc"><p>Select the PowerPro lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a8d42ad47b0a03f3a27c047401f3cb080"></a>SCLEX_NIMROD&#160;</td><td class="fielddoc"><p>Select the Nimrod lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a6b2b7135756e6e63afaab29e1ce69e5d"></a>SCLEX_SML&#160;</td><td class="fielddoc"><p>Select the SML lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a735f6a745c132f34910668c6f221dbef"></a>SCLEX_MARKDOWN&#160;</td><td class="fielddoc"><p>Select the Markdown lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a1eb34afacf50e022bc6d8d3ac92384d1"></a>SCLEX_TXT2TAGS&#160;</td><td class="fielddoc"><p>Select the txt2tags lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a5f5a499292d10817ab864bb61fc952bb"></a>SCLEX_A68K&#160;</td><td class="fielddoc"><p>Select the 68000 assembler lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a030fcaf06a85c39c4f57a828ef354d11"></a>SCLEX_MODULA&#160;</td><td class="fielddoc"><p>Select the Modula 3 lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ab4dd20651eeac848ec8a1586b3da3c8c"></a>SCLEX_COFFEESCRIPT&#160;</td><td class="fielddoc"><p>Select the CoffeeScript lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0aa9ea73b5b40de75ed54ea356f13a7b47"></a>SCLEX_TCMD&#160;</td><td class="fielddoc"><p>Select the Take Command lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ad63b11d786d32c7101682682bf49c063"></a>SCLEX_AVS&#160;</td><td class="fielddoc"><p>Select the AviSynth lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a7ed6ed45706f72a25396e7cea6f179fc"></a>SCLEX_ECL&#160;</td><td class="fielddoc"><p>Select the ECL lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a96e54938b672128818b2c8201833993a"></a>SCLEX_OSCRIPT&#160;</td><td class="fielddoc"><p>Select the OScript lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a6505e2018707a20252ef8eefc6b25fb3"></a>SCLEX_VISUALPROLOG&#160;</td><td class="fielddoc"><p>Select the Visual Prolog lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a96b2db4f5bb0191b81dd536b0b8b13e2"></a>SCLEX_LITERATEHASKELL&#160;</td><td class="fielddoc"><p>Select the Literal Haskell lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a6e8fa194daac20f1860a30910cd77ad2"></a>SCLEX_STTXT&#160;</td><td class="fielddoc"><p>Select the Structured Text lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ad0cd24eef0f5650d775d4dd05bd82df8"></a>SCLEX_KVIRC&#160;</td><td class="fielddoc"><p>Select the KVIrc lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a7dedcc3f7467a77cf25eff297aad55c1"></a>SCLEX_RUST&#160;</td><td class="fielddoc"><p>Select the Rust lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a426868e898ad88df600d7a0cba7ed000"></a>SCLEX_DMAP&#160;</td><td class="fielddoc"><p>Select the MSC Nastran DMAP lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a6fee40e395ba28044ccd9cbbc1db48d5"></a>SCLEX_AS&#160;</td><td class="fielddoc"><p>Select the assembler lexer ('#' comment character). </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ad9e8188110135d6897add3becb30995f"></a>SCLEX_DMIS&#160;</td><td class="fielddoc"><p>Select the DMIS lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ab196b886d720b528c06981f3162edcfe"></a>SCLEX_REGISTRY&#160;</td><td class="fielddoc"><p>Select the lexer for Windows registry files. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0abc6e7a4f3fbf502b080d443f4f779ea9"></a>SCLEX_BIBTEX&#160;</td><td class="fielddoc"><p>Select the BibTex lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a833ab9e759135def757131a8bd0196fe"></a>SCLEX_SREC&#160;</td><td class="fielddoc"><p>Select the Motorola S-Record hex lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a60d40cf6110071d3ae0ff271ea00fca6"></a>SCLEX_IHEX&#160;</td><td class="fielddoc"><p>Select the Intel hex lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a7f81fc1ce2e254d399b858b08362e0bf"></a>SCLEX_TEHEX&#160;</td><td class="fielddoc"><p>Select the Tektronix extended hex lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a7fbfc36f6ecf328b50efe1d29fa7be89"></a>SCLEX_JSON&#160;</td><td class="fielddoc"><p>Select the JSON hex lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a07565bccfb3046478e918086c75fd2d0"></a>SCLEX_EDIFACT&#160;</td><td class="fielddoc"><p>Select the EDIFACT lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ace65638f1fc7df156cb5fd7e13e40b39"></a>SCLEX_INDENT&#160;</td><td class="fielddoc"><p>Select the pseudo-lexer used for the indentation-based folding of files. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0ad030153b23920c60fd4c57a63a1992ad"></a>SCLEX_MAXIMA&#160;</td><td class="fielddoc"><p>Select the Maxima lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0aa3c5a8b4e7b66cfd26eeadc24049c268"></a>SCLEX_STATA&#160;</td><td class="fielddoc"><p>Select the Stata lexer. </p>
</td></tr>
<tr><td class="fieldname"><a id="aa4ab44fd6a7374eb16d07762aa51c7c0a163ba76db43420149ae4ed4456426d7c"></a>SCLEX_SAS&#160;</td><td class="fielddoc"><p>Select the SAS lexer. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a0f69249f4e97b96f09ea70f546df7464"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0f69249f4e97b96f09ea70f546df7464">&#9670;&nbsp;</a></span>pool()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>* QsciScintillaBase::pool </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a pointer to a <a class="el" href="classQsciScintillaBase.html" title="The QsciScintillaBase class implements the Scintilla editor widget and its low-level API.">QsciScintillaBase</a> instance, or 0 if there isn't one. This can be used by the higher level API to send messages that aren't associated with a particular instance. </p>

</div>
</div>
<a id="a1efa4394b588d27fd2a3bd40163a2342"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1efa4394b588d27fd2a3bd40163a2342">&#9670;&nbsp;</a></span>replaceHorizontalScrollBar()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::replaceHorizontalScrollBar </td>
          <td>(</td>
          <td class="paramtype">QScrollBar *&#160;</td>
          <td class="paramname"><em>scrollBar</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Replaces the existing horizontal scroll bar with <em>scrollBar</em>. The existing scroll bar is deleted. This should be called instead of QAbstractScrollArea::setHorizontalScrollBar(). </p>

</div>
</div>
<a id="a900e3a0287e262fe65c51162e562fc5d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a900e3a0287e262fe65c51162e562fc5d">&#9670;&nbsp;</a></span>replaceVerticalScrollBar()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::replaceVerticalScrollBar </td>
          <td>(</td>
          <td class="paramtype">QScrollBar *&#160;</td>
          <td class="paramname"><em>scrollBar</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Replaces the existing vertical scroll bar with <em>scrollBar</em>. The existing scroll bar is deleted. This should be called instead of QAbstractScrollArea::setHorizontalScrollBar(). </p>

</div>
</div>
<a id="a8820ab8d7563bd7ed24ce6384846079e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8820ab8d7563bd7ed24ce6384846079e">&#9670;&nbsp;</a></span>SendScintilla()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">long QsciScintillaBase::SendScintilla </td>
          <td>(</td>
          <td class="paramtype">unsigned int&#160;</td>
          <td class="paramname"><em>msg</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">unsigned long&#160;</td>
          <td class="paramname"><em>wParam</em> = <code>0</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">long&#160;</td>
          <td class="paramname"><em>lParam</em> = <code>0</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Send the Scintilla message <em>msg</em> with the optional parameters <em>wParam</em> and <em>lParam</em>. </p>

</div>
</div>
<a id="a747feb07236c1beccadd446562b53b84"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a747feb07236c1beccadd446562b53b84">&#9670;&nbsp;</a></span>QSCN_SELCHANGED</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::QSCN_SELCHANGED </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>yes</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when text is selected or de-selected. <em>yes</em> is true if text has been selected and false if text has been deselected. </p>

</div>
</div>
<a id="a1719fba80d9e60cf9fce1bb75f304568"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1719fba80d9e60cf9fce1bb75f304568">&#9670;&nbsp;</a></span>SCN_AUTOCCANCELLED</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_AUTOCCANCELLED </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the user cancels an auto-completion list.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#a61c43c53a753272c51c5c5ac14bda136">SCN_AUTOCSELECTION()</a> </dd></dl>

</div>
</div>
<a id="aabab23e5653c35dae8a6f144d73c4657"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aabab23e5653c35dae8a6f144d73c4657">&#9670;&nbsp;</a></span>SCN_AUTOCCHARDELETED</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_AUTOCCHARDELETED </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the user deletes a character when an auto-completion list is active. </p>

</div>
</div>
<a id="a41e738411112b8f509e0b49b6fc3e318"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a41e738411112b8f509e0b49b6fc3e318">&#9670;&nbsp;</a></span>SCN_AUTOCCOMPLETED</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_AUTOCCOMPLETED </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>selection</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>position</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>ch</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>method</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted after an auto-completion has inserted its text. <em>selection</em> is the text of the selection. <em>position</em> is the start position of the word being completed. <em>ch</em> is the fillup character that triggered the selection if method is SC_AC_FILLUP. <em>method</em> is the method used to trigger the selection.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#a1719fba80d9e60cf9fce1bb75f304568">SCN_AUTOCCANCELLED()</a>, <a class="el" href="classQsciScintillaBase.html#a61c43c53a753272c51c5c5ac14bda136">SCN_AUTOCSELECTION()</a> </dd></dl>

</div>
</div>
<a id="a61c43c53a753272c51c5c5ac14bda136"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a61c43c53a753272c51c5c5ac14bda136">&#9670;&nbsp;</a></span>SCN_AUTOCSELECTION</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_AUTOCSELECTION </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>selection</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>position</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>ch</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>method</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the user selects an item in an auto-completion list. It is emitted before the selection is inserted. The insertion can be cancelled by sending an SCI_AUTOCANCEL message from a connected slot. <em>selection</em> is the text of the selection. <em>position</em> is the start position of the word being completed. <em>ch</em> is the fillup character that triggered the selection if method is SC_AC_FILLUP. <em>method</em> is the method used to trigger the selection.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#a1719fba80d9e60cf9fce1bb75f304568">SCN_AUTOCCANCELLED()</a>, <a class="el" href="classQsciScintillaBase.html#a41e738411112b8f509e0b49b6fc3e318">SCN_AUTOCCOMPLETED()</a> </dd></dl>

</div>
</div>
<a id="a721a1879cabaa76883ae1a02a34a76e8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a721a1879cabaa76883ae1a02a34a76e8">&#9670;&nbsp;</a></span>SCN_AUTOCSELECTIONCHANGE</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_AUTOCSELECTIONCHANGE </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>selection</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>id</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>position</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the user highlights an item in an auto-completion or user list. <em>selection</em> is the text of the selection. <em>id</em> is an identifier for the list which was passed as an argument to the SCI_USERLISTSHOW message or 0 if the list is an auto-completion list. <em>position</em> is the position that the list was displayed at. </p>

</div>
</div>
<a id="a13f22ec5a59e2e8e97a27ac24967f74d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a13f22ec5a59e2e8e97a27ac24967f74d">&#9670;&nbsp;</a></span>SCN_CALLTIPCLICK</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_CALLTIPCLICK </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>direction</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the user clicks on a calltip. <em>direction</em> is 1 if the user clicked on the up arrow, 2 if the user clicked on the down arrow, and 0 if the user clicked elsewhere. </p>

</div>
</div>
<a id="ae8d8fa5d5f063a7c7d37d527f86b5fe8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae8d8fa5d5f063a7c7d37d527f86b5fe8">&#9670;&nbsp;</a></span>SCN_CHARADDED</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_CHARADDED </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>charadded</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted whenever the user enters an ordinary character into the text. <em>charadded</em> is the character. It can be used to decide to display a call tip or an auto-completion list. </p>

</div>
</div>
<a id="ad3ca5787399ed886cb9000c8feab3c08"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad3ca5787399ed886cb9000c8feab3c08">&#9670;&nbsp;</a></span>SCN_DOUBLECLICK</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_DOUBLECLICK </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>position</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>line</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>modifiers</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the user double clicks. <em>position</em> is the position in the text where the click occured. <em>line</em> is the number of the line in the text where the click occured. <em>modifiers</em> is the logical or of the modifier keys that were pressed when the user double clicked. </p>

</div>
</div>
<a id="a9ecd605284870ddbf703cf4c8c995ca6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9ecd605284870ddbf703cf4c8c995ca6">&#9670;&nbsp;</a></span>SCN_DWELLEND</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_DWELLEND </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>position</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the user moves the mouse (or presses a key) after keeping it in one position for the dwell period. <em>position</em> is the position in the text where the mouse dwells. <em>x</em> is the x-coordinate where the mouse dwells. <em>y</em> is the y-coordinate where the mouse dwells.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#adfd788dce5c1a91d1fcd5e6fdd2fca59">SCN_DWELLSTART</a>, SCI_SETMOUSEDWELLTIME </dd></dl>

</div>
</div>
<a id="adfd788dce5c1a91d1fcd5e6fdd2fca59"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adfd788dce5c1a91d1fcd5e6fdd2fca59">&#9670;&nbsp;</a></span>SCN_DWELLSTART</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_DWELLSTART </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>position</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the user keeps the mouse in one position for the dwell period. <em>position</em> is the position in the text where the mouse dwells. <em>x</em> is the x-coordinate where the mouse dwells. <em>y</em> is the y-coordinate where the mouse dwells.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#a9ecd605284870ddbf703cf4c8c995ca6">SCN_DWELLEND</a>, SCI_SETMOUSEDWELLTIME </dd></dl>

</div>
</div>
<a id="a5eff383e6fa96cbbaba6a2558b076c0b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5eff383e6fa96cbbaba6a2558b076c0b">&#9670;&nbsp;</a></span>SCN_HOTSPOTCLICK</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_HOTSPOTCLICK </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>position</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>modifiers</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the user clicks on text in a style with the hotspot attribute set. <em>position</em> is the position in the text where the click occured. <em>modifiers</em> is the logical or of the modifier keys that were pressed when the user clicked. </p>

</div>
</div>
<a id="a682cc736272338433efdc86bc936e0e8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a682cc736272338433efdc86bc936e0e8">&#9670;&nbsp;</a></span>SCN_HOTSPOTDOUBLECLICK</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_HOTSPOTDOUBLECLICK </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>position</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>modifiers</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the user double clicks on text in a style with the hotspot attribute set. <em>position</em> is the position in the text where the double click occured. <em>modifiers</em> is the logical or of the modifier keys that were pressed when the user double clicked. </p>

</div>
</div>
<a id="a906faecb0defd2d5a14cac54f8415dcf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a906faecb0defd2d5a14cac54f8415dcf">&#9670;&nbsp;</a></span>SCN_HOTSPOTRELEASECLICK</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_HOTSPOTRELEASECLICK </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>position</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>modifiers</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the user releases the mouse button on text in a style with the hotspot attribute set. <em>position</em> is the position in the text where the release occured. <em>modifiers</em> is the logical or of the modifier keys that were pressed when the user released the button. </p>

</div>
</div>
<a id="aeec8d7e585e93451307df88ff2fc2b87"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aeec8d7e585e93451307df88ff2fc2b87">&#9670;&nbsp;</a></span>SCN_INDICATORCLICK</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_INDICATORCLICK </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>position</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>modifiers</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the user clicks on text that has an indicator. <em>position</em> is the position in the text where the click occured. <em>modifiers</em> is the logical or of the modifier keys that were pressed when the user clicked. </p>

</div>
</div>
<a id="a93d1e96c88745ca7f2737602e80dc76a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a93d1e96c88745ca7f2737602e80dc76a">&#9670;&nbsp;</a></span>SCN_INDICATORRELEASE</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_INDICATORRELEASE </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>position</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>modifiers</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the user releases the mouse button on text that has an indicator. <em>position</em> is the position in the text where the release occured. <em>modifiers</em> is the logical or of the modifier keys that were pressed when the user released. </p>

</div>
</div>
<a id="abdae368f2b81955c4927dc6f26fc2c77"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abdae368f2b81955c4927dc6f26fc2c77">&#9670;&nbsp;</a></span>SCN_MACRORECORD</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_MACRORECORD </td>
          <td>(</td>
          <td class="paramtype">unsigned int&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">unsigned long&#160;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when a recordable editor command has been executed. </p>

</div>
</div>
<a id="a722a2f16b67ef5f46def6914a6e178c3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a722a2f16b67ef5f46def6914a6e178c3">&#9670;&nbsp;</a></span>SCN_MARGINCLICK</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_MARGINCLICK </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>position</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>modifiers</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>margin</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the user clicks on a sensitive margin. <em>position</em> is the position of the start of the line against which the user clicked. <em>modifiers</em> is the logical or of the modifier keys that were pressed when the user clicked. <em>margin</em> is the number of the margin the user clicked in: 0, 1 or 2.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaffc41be0dbc2eb4b00438f0b489c7c88">SCI_GETMARGINSENSITIVEN</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa64b07178255dc95b19a7c8feabaac1b2">SCI_SETMARGINSENSITIVEN</a> </dd></dl>

</div>
</div>
<a id="a39e90958ae903d2f6198ec0c58f56ed9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a39e90958ae903d2f6198ec0c58f56ed9">&#9670;&nbsp;</a></span>SCN_MARGINRIGHTCLICK</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_MARGINRIGHTCLICK </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>position</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>modifiers</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>margin</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the user right-clicks on a sensitive margin. <em>position</em> is the position of the start of the line against which the user clicked. <em>modifiers</em> is the logical or of the modifier keys that were pressed when the user clicked. <em>margin</em> is the number of the margin the user clicked in: 0, 1 or 2.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaffc41be0dbc2eb4b00438f0b489c7c88">SCI_GETMARGINSENSITIVEN</a>, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa64b07178255dc95b19a7c8feabaac1b2">SCI_SETMARGINSENSITIVEN</a> </dd></dl>

</div>
</div>
<a id="adb5bad7d1dad9ab3fe74adb3e0812969"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adb5bad7d1dad9ab3fe74adb3e0812969">&#9670;&nbsp;</a></span>SCN_MODIFYATTEMPTRO</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_MODIFYATTEMPTRO </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the user attempts to modify read-only text. </p>

</div>
</div>
<a id="a94a1cff08b2ef6558d054177fa88ea47"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a94a1cff08b2ef6558d054177fa88ea47">&#9670;&nbsp;</a></span>SCN_PAINTED</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_PAINTED </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when painting has been completed. It is useful to trigger some other change but to have the paint be done first to appear more reponsive to the user. </p>

</div>
</div>
<a id="af3a619a5e59cef000f0b550e809c94de"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af3a619a5e59cef000f0b550e809c94de">&#9670;&nbsp;</a></span>SCN_SAVEPOINTLEFT</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_SAVEPOINTLEFT </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the current state of the text no longer corresponds to the state of the text at the save point.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa20e9df7da839e5b9e2edd2366a7ecb97">SCI_SETSAVEPOINT</a>, <a class="el" href="classQsciScintillaBase.html#a0db8c3ad0764a96f3ccf0fec71de0d26">SCN_SAVEPOINTREACHED()</a> </dd></dl>

</div>
</div>
<a id="a0db8c3ad0764a96f3ccf0fec71de0d26"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0db8c3ad0764a96f3ccf0fec71de0d26">&#9670;&nbsp;</a></span>SCN_SAVEPOINTREACHED</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_SAVEPOINTREACHED </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the current state of the text corresponds to the state of the text at the save point. This allows feedback to be given to the user as to whether the text has been modified since it was last saved.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa20e9df7da839e5b9e2edd2366a7ecb97">SCI_SETSAVEPOINT</a>, <a class="el" href="classQsciScintillaBase.html#af3a619a5e59cef000f0b550e809c94de">SCN_SAVEPOINTLEFT()</a> </dd></dl>

</div>
</div>
<a id="a72c0bc1c83fd675714626cd786ca4fb9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a72c0bc1c83fd675714626cd786ca4fb9">&#9670;&nbsp;</a></span>SCN_STYLENEEDED</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_STYLENEEDED </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>position</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when a range of text needs to be syntax styled. The range is from the value returned by the SCI_GETENDSTYLED message and <em>position</em>. It is only emitted if the currently selected lexer is SCLEX_CONTAINER.</p>
<dl class="section see"><dt>See also</dt><dd>SCI_COLOURISE, <a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa820d8d563cb319ff42e5b9ea709d839d">SCI_GETENDSTYLED</a> </dd></dl>

</div>
</div>
<a id="a42cb45ea05c71180a594e0cc8041c07d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a42cb45ea05c71180a594e0cc8041c07d">&#9670;&nbsp;</a></span>SCN_URIDROPPED</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_URIDROPPED </td>
          <td>(</td>
          <td class="paramtype">const QUrl &amp;&#160;</td>
          <td class="paramname"><em>url</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when a URI is dropped. <em>url</em> is the value of the URI. </p>

</div>
</div>
<a id="ad88db21d86df33667c234d00af1fdf94"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad88db21d86df33667c234d00af1fdf94">&#9670;&nbsp;</a></span>SCN_UPDATEUI</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_UPDATEUI </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>updated</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when either the text or styling of the text has changed or the selection range or scroll position has changed. <em>updated</em> contains the set of SC_UPDATE_* flags describing the changes since the signal was last emitted. </p>

</div>
</div>
<a id="a8225643b25dc6f1dedc48b4a7af4b83d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8225643b25dc6f1dedc48b4a7af4b83d">&#9670;&nbsp;</a></span>SCN_USERLISTSELECTION</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciScintillaBase::SCN_USERLISTSELECTION </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>selection</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>id</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>ch</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>method</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>position</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">signal</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>This signal is emitted when the user selects an item in a user list. <em>selection</em> is the text of the selection. <em>id</em> is an identifier for the list which was passed as an argument to the SCI_USERLISTSHOW message and must be at least 1. <em>ch</em> is the fillup character that triggered the selection if method is SC_AC_FILLUP. <em>method</em> is the method used to trigger the selection. <em>position</em> is the position that the list was displayed at.</p>
<dl class="section see"><dt>See also</dt><dd>SCI_USERLISTSHOW, <a class="el" href="classQsciScintillaBase.html#a61c43c53a753272c51c5c5ac14bda136">SCN_AUTOCSELECTION()</a> </dd></dl>

</div>
</div>
<a id="abce274ef71035c67baadaa167a1fe5a7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abce274ef71035c67baadaa167a1fe5a7">&#9670;&nbsp;</a></span>canInsertFromMimeData()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool QsciScintillaBase::canInsertFromMimeData </td>
          <td>(</td>
          <td class="paramtype">const QMimeData *&#160;</td>
          <td class="paramname"><em>source</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if the contents of a MIME data object can be decoded and inserted into the document. It is called during drag and paste operations. <em>source</em> is the MIME data object.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#a5f105b9ec17cd73a0cd601ac9be82dd4">fromMimeData()</a>, <a class="el" href="classQsciScintillaBase.html#a7e1e146787204eba48aa5376287de41f">toMimeData()</a> </dd></dl>

</div>
</div>
<a id="a5f105b9ec17cd73a0cd601ac9be82dd4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5f105b9ec17cd73a0cd601ac9be82dd4">&#9670;&nbsp;</a></span>fromMimeData()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual QByteArray QsciScintillaBase::fromMimeData </td>
          <td>(</td>
          <td class="paramtype">const QMimeData *&#160;</td>
          <td class="paramname"><em>source</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool &amp;&#160;</td>
          <td class="paramname"><em>rectangular</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the text of a MIME data object. It is called when a drag and drop is completed and when text is pasted from the clipboard. <em>source</em> is the MIME data object. On return <em>rectangular</em> is set if the text corresponds to a rectangular selection.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#abce274ef71035c67baadaa167a1fe5a7">canInsertFromMimeData()</a>, <a class="el" href="classQsciScintillaBase.html#a7e1e146787204eba48aa5376287de41f">toMimeData()</a> </dd></dl>

</div>
</div>
<a id="a7e1e146787204eba48aa5376287de41f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7e1e146787204eba48aa5376287de41f">&#9670;&nbsp;</a></span>toMimeData()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual QMimeData* QsciScintillaBase::toMimeData </td>
          <td>(</td>
          <td class="paramtype">const QByteArray &amp;&#160;</td>
          <td class="paramname"><em>text</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>rectangular</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a new MIME data object containing some text and whether it corresponds to a rectangular selection. It is called when a drag and drop is started and when the selection is copied to the clipboard. Ownership of the object is passed to the caller. <em>text</em> is the text. <em>rectangular</em> is set if the text corresponds to a rectangular selection.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciScintillaBase.html#abce274ef71035c67baadaa167a1fe5a7">canInsertFromMimeData()</a>, <a class="el" href="classQsciScintillaBase.html#a5f105b9ec17cd73a0cd601ac9be82dd4">fromMimeData()</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
