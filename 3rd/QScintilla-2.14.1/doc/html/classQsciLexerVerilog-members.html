<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciLexerVerilog Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">autoCompletionWordSeparators</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">blockEnd</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">blockStart</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a>(int *style=0) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#a003413f4436ff46553e10db632496288">braceStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>bytesAsText</b>(const char *bytes, int size) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa728da173f7b8baae14eae147d5f9825c">Comment</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa35bfbe7dfa56b39c896d3058ea913045">CommentBang</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baaa73908fe2dc83b644b9b15aec0a6d65f">CommentKeyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa40b38f212ceb6dd21a31b474ac524b28">CommentLine</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baaf1faad6166ac7eb8ec701ee8e075d73f">DeclareInputOutputPort</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa87953315b5bfbecd097e6cd2a5545deb">DeclareInputPort</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baaa82c4530efbc0152d96e4f9f2c8b5922">DeclareOutputPort</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa063bcfc2ad0162efe4015fec0f50dea8">Default</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#a410bcada9eb227aa5689304b861c9997">defaultColor</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">QsciLexer::defaultColor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#a59ad64688b9fb852792b3fa15c2b125d">defaultEolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#aaeb3a87a051af9cc20b5319ed8cd6ca1">defaultFont</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">QsciLexer::defaultFont</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#adbaf4979024f12f9382df61cba0e75e8">defaultPaper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">QsciLexer::defaultPaper</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#ac6d9fdf26d30d14707e0b0778f80d54d">description</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a>(bool eolfilled, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#a1bb598533be61e117a252d06cf5e4a4b">foldAtElse</a>() const</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#a8a4389bd37a806046a7c0b51cc1a6ead">foldAtModule</a>() const</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#a7e9725132ec5521255eb7d9ac81ae853">foldComments</a>() const</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#ae1f192b93ad970cb792b5dcac4aa22d8">foldCompact</a>() const</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#ad70da8e3f3695cfc277d02ab9c0396d3">foldPreprocessor</a>() const</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a>(const QFont &amp;f, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa5b940ddc4db712f69dbf6753cd362ebf">Identifier</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveComment</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveCommentBang</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveCommentKeyword</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveCommentLine</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveDeclareInputOutputPort</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveDeclareInputPort</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveDeclareOutputPort</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveDefault</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveIdentifier</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveKeyword</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveKeywordSet2</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveNumber</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveOperator</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactivePortConnection</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactivePreprocessor</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveString</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveSystemTask</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>InactiveUnclosedString</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>InactiveUserKeywordSet</b> enum value (defined in <a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a>)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa2e3ce56242b141b7666f3f2afae71e9e">Keyword</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#aebb96727a845f9547a60848f6163d461">keywords</a>(int set) const</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baaacda550e099f252c45c8bb1523a1640d">KeywordSet2</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#a79e3ff22e68d54f640bd2f7747a7a193">language</a>() const</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#abc3666027fe7f0b8ae78ee34e3276069">lexer</a>() const</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa43313b93162231653a6aa703c53c5f23">Number</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa24c57f9c1818421c5f65a8c0c02efb04">Operator</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a>(int style) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a>(const QColor &amp;c, int style)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa3dd763156bf7395a20a62d80cebe1e89">PortConnection</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa3e547dfc36110bbc544486aa98400c78">Preprocessor</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a>(const char *prop, const char *val)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#a3360bca839d08fdd2acf546b19b2fddd">QsciLexerVerilog</a>(QObject *parent=0)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#aa1bd0effe3ed23e2bb3334b778efb74a">readProperties</a>(QSettings &amp;qs, const QString &amp;prefix)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#ad476092b3970fe44068dd023f8becc96">refreshProperties</a>()</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a>(QsciAbstractAPIs *apis)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a>(int autoindentstyle)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a>(const QFont &amp;f)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a>(const QColor &amp;c)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>setEditor</b>(QsciScintilla *editor) (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a>(bool eoffill, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#a7b84f78b170cec259efb2f367c54ce4b">setFoldAtElse</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af57050a2bcb9d1d285199159da0ba6e0">setFoldAtModule</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#ac79b616c3ba0872856d90b119bfd81b8">setFoldComments</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#a17ff342a5c1d94ce760a3dc02cfcda1d">setFoldCompact</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#ab7c13e959940db389fe0daeb96267d8e">setFoldPreprocessor</a>(bool fold)</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a>(const QFont &amp;f, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a>(const QColor &amp;c, int style=-1)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span><span class="mlabel">slot</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa493689dbaca8a280da2285e1d85e8bc1">String</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a>() const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa6cc28d1f75a45d11566a7f19947e4cf1">SystemTask</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>textAsBytes</b>(const QString &amp;text) const (defined in <a class="el" href="classQsciLexer.html">QsciLexer</a>)</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baaf996ceeff4869191f640d2dc18d7c016">UnclosedString</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa080431ca218b5ae5653c49c9caf55727">UserKeywordSet</a> enum value</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#abb1544042444a8147123b5597e096ea2">wordCharacters</a>() const</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexerVerilog.html#a97b418522a5866d04d9553931dd1c7f4">writeProperties</a>(QSettings &amp;qs, const QString &amp;prefix) const</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a>(QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a>()</td><td class="entry"><a class="el" href="classQsciLexer.html">QsciLexer</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciLexerVerilog.html#a7fd3e6c1faee7c7986db2ec4c0b793ae">~QsciLexerVerilog</a>()</td><td class="entry"><a class="el" href="classQsciLexerVerilog.html">QsciLexerVerilog</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
