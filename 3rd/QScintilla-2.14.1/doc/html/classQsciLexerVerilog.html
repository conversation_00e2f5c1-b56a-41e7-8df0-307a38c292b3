<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciLexerVerilog Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="classQsciLexerVerilog-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciLexerVerilog Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscilexerverilog.h&gt;</code></p>

<p>Inherits <a class="el" href="classQsciLexer.html">QsciLexer</a>.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:af0b4c89d35f5e39bcb7c5b25a6c3c7ba"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa063bcfc2ad0162efe4015fec0f50dea8">Default</a> = 0, 
<b>InactiveDefault</b> = Default + 64, 
<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa728da173f7b8baae14eae147d5f9825c">Comment</a> = 1, 
<br />
&#160;&#160;<b>InactiveComment</b> = Comment + 64, 
<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa40b38f212ceb6dd21a31b474ac524b28">CommentLine</a> = 2, 
<b>InactiveCommentLine</b> = CommentLine + 64, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa35bfbe7dfa56b39c896d3058ea913045">CommentBang</a> = 3, 
<b>InactiveCommentBang</b> = CommentBang + 64, 
<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa43313b93162231653a6aa703c53c5f23">Number</a> = 4, 
<br />
&#160;&#160;<b>InactiveNumber</b> = Number + 64, 
<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa2e3ce56242b141b7666f3f2afae71e9e">Keyword</a> = 5, 
<b>InactiveKeyword</b> = Keyword + 64, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa493689dbaca8a280da2285e1d85e8bc1">String</a> = 6, 
<b>InactiveString</b> = String + 64, 
<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baaacda550e099f252c45c8bb1523a1640d">KeywordSet2</a> = 7, 
<br />
&#160;&#160;<b>InactiveKeywordSet2</b> = KeywordSet2 + 64, 
<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa6cc28d1f75a45d11566a7f19947e4cf1">SystemTask</a> = 8, 
<b>InactiveSystemTask</b> = SystemTask + 64, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa3e547dfc36110bbc544486aa98400c78">Preprocessor</a> = 9, 
<b>InactivePreprocessor</b> = Preprocessor + 64, 
<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa24c57f9c1818421c5f65a8c0c02efb04">Operator</a> = 10, 
<br />
&#160;&#160;<b>InactiveOperator</b> = Operator + 64, 
<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa5b940ddc4db712f69dbf6753cd362ebf">Identifier</a> = 11, 
<b>InactiveIdentifier</b> = Identifier + 64, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baaf996ceeff4869191f640d2dc18d7c016">UnclosedString</a> = 12, 
<b>InactiveUnclosedString</b> = UnclosedString + 64, 
<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa080431ca218b5ae5653c49c9caf55727">UserKeywordSet</a> = 19, 
<br />
&#160;&#160;<b>InactiveUserKeywordSet</b> = UserKeywordSet + 64, 
<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baaa73908fe2dc83b644b9b15aec0a6d65f">CommentKeyword</a> = 20, 
<b>InactiveCommentKeyword</b> = CommentKeyword + 64, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa87953315b5bfbecd097e6cd2a5545deb">DeclareInputPort</a> = 21, 
<b>InactiveDeclareInputPort</b> = DeclareInputPort + 64, 
<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baaa82c4530efbc0152d96e4f9f2c8b5922">DeclareOutputPort</a> = 22, 
<br />
&#160;&#160;<b>InactiveDeclareOutputPort</b> = DeclareOutputPort + 64, 
<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baaf1faad6166ac7eb8ec701ee8e075d73f">DeclareInputOutputPort</a> = 23, 
<b>InactiveDeclareInputOutputPort</b> = DeclareInputOutputPort + 64, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerVerilog.html#af0b4c89d35f5e39bcb7c5b25a6c3c7baa3dd763156bf7395a20a62d80cebe1e89">PortConnection</a> = 24, 
<b>InactivePortConnection</b> = PortConnection + 64
<br />
 }</td></tr>
<tr class="separator:af0b4c89d35f5e39bcb7c5b25a6c3c7ba"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a3360bca839d08fdd2acf546b19b2fddd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#a3360bca839d08fdd2acf546b19b2fddd">QsciLexerVerilog</a> (QObject *parent=0)</td></tr>
<tr class="separator:a3360bca839d08fdd2acf546b19b2fddd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7fd3e6c1faee7c7986db2ec4c0b793ae"><td class="memItemLeft" align="right" valign="top"><a id="a7fd3e6c1faee7c7986db2ec4c0b793ae"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#a7fd3e6c1faee7c7986db2ec4c0b793ae">~QsciLexerVerilog</a> ()</td></tr>
<tr class="separator:a7fd3e6c1faee7c7986db2ec4c0b793ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79e3ff22e68d54f640bd2f7747a7a193"><td class="memItemLeft" align="right" valign="top"><a id="a79e3ff22e68d54f640bd2f7747a7a193"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#a79e3ff22e68d54f640bd2f7747a7a193">language</a> () const</td></tr>
<tr class="separator:a79e3ff22e68d54f640bd2f7747a7a193"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc3666027fe7f0b8ae78ee34e3276069"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#abc3666027fe7f0b8ae78ee34e3276069">lexer</a> () const</td></tr>
<tr class="separator:abc3666027fe7f0b8ae78ee34e3276069"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a003413f4436ff46553e10db632496288"><td class="memItemLeft" align="right" valign="top"><a id="a003413f4436ff46553e10db632496288"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#a003413f4436ff46553e10db632496288">braceStyle</a> () const</td></tr>
<tr class="separator:a003413f4436ff46553e10db632496288"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abb1544042444a8147123b5597e096ea2"><td class="memItemLeft" align="right" valign="top"><a id="abb1544042444a8147123b5597e096ea2"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#abb1544042444a8147123b5597e096ea2">wordCharacters</a> () const</td></tr>
<tr class="separator:abb1544042444a8147123b5597e096ea2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a410bcada9eb227aa5689304b861c9997"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#a410bcada9eb227aa5689304b861c9997">defaultColor</a> (int style) const</td></tr>
<tr class="separator:a410bcada9eb227aa5689304b861c9997"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a59ad64688b9fb852792b3fa15c2b125d"><td class="memItemLeft" align="right" valign="top"><a id="a59ad64688b9fb852792b3fa15c2b125d"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#a59ad64688b9fb852792b3fa15c2b125d">defaultEolFill</a> (int style) const</td></tr>
<tr class="separator:a59ad64688b9fb852792b3fa15c2b125d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaeb3a87a051af9cc20b5319ed8cd6ca1"><td class="memItemLeft" align="right" valign="top"><a id="aaeb3a87a051af9cc20b5319ed8cd6ca1"></a>
QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#aaeb3a87a051af9cc20b5319ed8cd6ca1">defaultFont</a> (int style) const</td></tr>
<tr class="separator:aaeb3a87a051af9cc20b5319ed8cd6ca1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adbaf4979024f12f9382df61cba0e75e8"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#adbaf4979024f12f9382df61cba0e75e8">defaultPaper</a> (int style) const</td></tr>
<tr class="separator:adbaf4979024f12f9382df61cba0e75e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aebb96727a845f9547a60848f6163d461"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#aebb96727a845f9547a60848f6163d461">keywords</a> (int set) const</td></tr>
<tr class="separator:aebb96727a845f9547a60848f6163d461"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac6d9fdf26d30d14707e0b0778f80d54d"><td class="memItemLeft" align="right" valign="top">QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#ac6d9fdf26d30d14707e0b0778f80d54d">description</a> (int style) const</td></tr>
<tr class="separator:ac6d9fdf26d30d14707e0b0778f80d54d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad476092b3970fe44068dd023f8becc96"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#ad476092b3970fe44068dd023f8becc96">refreshProperties</a> ()</td></tr>
<tr class="separator:ad476092b3970fe44068dd023f8becc96"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7b84f78b170cec259efb2f367c54ce4b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#a7b84f78b170cec259efb2f367c54ce4b">setFoldAtElse</a> (bool fold)</td></tr>
<tr class="separator:a7b84f78b170cec259efb2f367c54ce4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1bb598533be61e117a252d06cf5e4a4b"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#a1bb598533be61e117a252d06cf5e4a4b">foldAtElse</a> () const</td></tr>
<tr class="separator:a1bb598533be61e117a252d06cf5e4a4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac79b616c3ba0872856d90b119bfd81b8"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#ac79b616c3ba0872856d90b119bfd81b8">setFoldComments</a> (bool fold)</td></tr>
<tr class="separator:ac79b616c3ba0872856d90b119bfd81b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7e9725132ec5521255eb7d9ac81ae853"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#a7e9725132ec5521255eb7d9ac81ae853">foldComments</a> () const</td></tr>
<tr class="separator:a7e9725132ec5521255eb7d9ac81ae853"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a17ff342a5c1d94ce760a3dc02cfcda1d"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#a17ff342a5c1d94ce760a3dc02cfcda1d">setFoldCompact</a> (bool fold)</td></tr>
<tr class="separator:a17ff342a5c1d94ce760a3dc02cfcda1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae1f192b93ad970cb792b5dcac4aa22d8"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#ae1f192b93ad970cb792b5dcac4aa22d8">foldCompact</a> () const</td></tr>
<tr class="separator:ae1f192b93ad970cb792b5dcac4aa22d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7c13e959940db389fe0daeb96267d8e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#ab7c13e959940db389fe0daeb96267d8e">setFoldPreprocessor</a> (bool fold)</td></tr>
<tr class="separator:ab7c13e959940db389fe0daeb96267d8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad70da8e3f3695cfc277d02ab9c0396d3"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#ad70da8e3f3695cfc277d02ab9c0396d3">foldPreprocessor</a> () const</td></tr>
<tr class="separator:ad70da8e3f3695cfc277d02ab9c0396d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af57050a2bcb9d1d285199159da0ba6e0"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#af57050a2bcb9d1d285199159da0ba6e0">setFoldAtModule</a> (bool fold)</td></tr>
<tr class="separator:af57050a2bcb9d1d285199159da0ba6e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a4389bd37a806046a7c0b51cc1a6ead"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#a8a4389bd37a806046a7c0b51cc1a6ead">foldAtModule</a> () const</td></tr>
<tr class="separator:a8a4389bd37a806046a7c0b51cc1a6ead"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a> (QObject *parent=0)</td></tr>
<tr class="separator:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="af6cc5bb9d9421d806e9941d018030068"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a> ()</td></tr>
<tr class="separator:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a> () const</td></tr>
<tr class="separator:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a> () const</td></tr>
<tr class="separator:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a6504a6fff35af16fbfd97889048db2a5"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a> () const</td></tr>
<tr class="separator:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e10650b0e9ad137062ad5c17ad33e76 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QStringList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">autoCompletionWordSeparators</a> () const</td></tr>
<tr class="separator:a4e10650b0e9ad137062ad5c17ad33e76 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a> ()</td></tr>
<tr class="separator:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e294eba77713f516acbcebc10af1493 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">blockEnd</a> (int *style=0) const</td></tr>
<tr class="separator:a8e294eba77713f516acbcebc10af1493 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">blockLookback</a> () const</td></tr>
<tr class="separator:a8b1bb1261e7b9701c62bbe4f1d171e06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a340eafe726fd6964c0adba956fe3428c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">blockStart</a> (int *style=0) const</td></tr>
<tr class="separator:a340eafe726fd6964c0adba956fe3428c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf12117a142b6f68479ea425d80a4196 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a> (int *style=0) const</td></tr>
<tr class="separator:abf12117a142b6f68479ea425d80a4196 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="afccca7eb1aed463f89ac442d99135839"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a> () const</td></tr>
<tr class="separator:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a> (int style) const</td></tr>
<tr class="separator:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a> (int style) const</td></tr>
<tr class="separator:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a> (int style) const</td></tr>
<tr class="separator:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="aff4735542e937c5e35ecb2eb82e8f875"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aff4735542e937c5e35ecb2eb82e8f875">indentationGuideView</a> () const</td></tr>
<tr class="separator:aff4735542e937c5e35ecb2eb82e8f875 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a> () const</td></tr>
<tr class="separator:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a> (int style) const</td></tr>
<tr class="separator:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">defaultColor</a> () const</td></tr>
<tr class="separator:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">defaultFont</a> () const</td></tr>
<tr class="separator:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">defaultPaper</a> () const</td></tr>
<tr class="separator:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciScintilla.html">QsciScintilla</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a> () const</td></tr>
<tr class="separator:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a> (<a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *<a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>)</td></tr>
<tr class="separator:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a> (const QFont &amp;f)</td></tr>
<tr class="separator:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a1e81186b1f8f8bc2a4901a42cbca568a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>setEditor</b> (<a class="el" href="classQsciScintilla.html">QsciScintilla</a> *<a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>)</td></tr>
<tr class="separator:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td></tr>
<tr class="separator:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a> () const</td></tr>
<tr class="separator:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td></tr>
<tr class="separator:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:aa1bd0effe3ed23e2bb3334b778efb74a"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#aa1bd0effe3ed23e2bb3334b778efb74a">readProperties</a> (QSettings &amp;qs, const QString &amp;prefix)</td></tr>
<tr class="separator:aa1bd0effe3ed23e2bb3334b778efb74a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a97b418522a5866d04d9553931dd1c7f4"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerVerilog.html#a97b418522a5866d04d9553931dd1c7f4">writeProperties</a> (QSettings &amp;qs, const QString &amp;prefix) const</td></tr>
<tr class="separator:a97b418522a5866d04d9553931dd1c7f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pro_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a41d4521504d63ee63d43fd7ed0c003ee"></a>
QByteArray&#160;</td><td class="memItemRight" valign="bottom"><b>textAsBytes</b> (const QString &amp;text) const</td></tr>
<tr class="separator:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a5261dd606c209a5c6a494e608a9a111a"></a>
QString&#160;</td><td class="memItemRight" valign="bottom"><b>bytesAsText</b> (const char *bytes, int size) const</td></tr>
<tr class="separator:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_slots_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_slots_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Slots inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a> (int autoindentstyle)</td></tr>
<tr class="separator:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a> (bool eoffill, int style=-1)</td></tr>
<tr class="separator:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a> (const QFont &amp;f, int style=-1)</td></tr>
<tr class="separator:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header signals_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('signals_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Signals inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a> (bool eolfilled, int style)</td></tr>
<tr class="separator:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a> (const QFont &amp;f, int style)</td></tr>
<tr class="separator:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a> (const char *prop, const char *val)</td></tr>
<tr class="separator:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciLexerVerilog.html" title="The QsciLexerVerilog class encapsulates the Scintilla Verilog lexer.">QsciLexerVerilog</a> class encapsulates the Scintilla Verilog lexer. </p>
</div><h2 class="groupheader">Member Enumeration Documentation</h2>
<a id="af0b4c89d35f5e39bcb7c5b25a6c3c7ba"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af0b4c89d35f5e39bcb7c5b25a6c3c7ba">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the meanings of the different styles used by the Verilog lexer. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baa063bcfc2ad0162efe4015fec0f50dea8"></a>Default&#160;</td><td class="fielddoc"><p>The default. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baa728da173f7b8baae14eae147d5f9825c"></a>Comment&#160;</td><td class="fielddoc"><p>A comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baa40b38f212ceb6dd21a31b474ac524b28"></a>CommentLine&#160;</td><td class="fielddoc"><p>A line comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baa35bfbe7dfa56b39c896d3058ea913045"></a>CommentBang&#160;</td><td class="fielddoc"><p>A bang comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baa43313b93162231653a6aa703c53c5f23"></a>Number&#160;</td><td class="fielddoc"><p>A number. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baa2e3ce56242b141b7666f3f2afae71e9e"></a>Keyword&#160;</td><td class="fielddoc"><p>A keyword. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baa493689dbaca8a280da2285e1d85e8bc1"></a>String&#160;</td><td class="fielddoc"><p>A string. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baaacda550e099f252c45c8bb1523a1640d"></a>KeywordSet2&#160;</td><td class="fielddoc"><p>A keyword defined in keyword set number 2. The class must be sub-classed and re-implement <a class="el" href="classQsciLexerVerilog.html#aebb96727a845f9547a60848f6163d461">keywords()</a> to make use of this style. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baa6cc28d1f75a45d11566a7f19947e4cf1"></a>SystemTask&#160;</td><td class="fielddoc"><p>A system task. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baa3e547dfc36110bbc544486aa98400c78"></a>Preprocessor&#160;</td><td class="fielddoc"><p>A pre-processor block. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baa24c57f9c1818421c5f65a8c0c02efb04"></a>Operator&#160;</td><td class="fielddoc"><p>An operator. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baa5b940ddc4db712f69dbf6753cd362ebf"></a>Identifier&#160;</td><td class="fielddoc"><p>An identifier. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baaf996ceeff4869191f640d2dc18d7c016"></a>UnclosedString&#160;</td><td class="fielddoc"><p>The end of a line where a string is not closed. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baa080431ca218b5ae5653c49c9caf55727"></a>UserKeywordSet&#160;</td><td class="fielddoc"><p>A keyword defined in keyword set number 4. The class must be sub-classed and re-implement <a class="el" href="classQsciLexerVerilog.html#aebb96727a845f9547a60848f6163d461">keywords()</a> to make use of this style. This set is intended to be used for user defined identifiers and tasks. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baaa73908fe2dc83b644b9b15aec0a6d65f"></a>CommentKeyword&#160;</td><td class="fielddoc"><p>A keyword comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baa87953315b5bfbecd097e6cd2a5545deb"></a>DeclareInputPort&#160;</td><td class="fielddoc"><p>An input port declaration. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baaa82c4530efbc0152d96e4f9f2c8b5922"></a>DeclareOutputPort&#160;</td><td class="fielddoc"><p>An output port declaration. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baaf1faad6166ac7eb8ec701ee8e075d73f"></a>DeclareInputOutputPort&#160;</td><td class="fielddoc"><p>An input/output port declaration. </p>
</td></tr>
<tr><td class="fieldname"><a id="af0b4c89d35f5e39bcb7c5b25a6c3c7baa3dd763156bf7395a20a62d80cebe1e89"></a>PortConnection&#160;</td><td class="fielddoc"><p>A port connection. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a3360bca839d08fdd2acf546b19b2fddd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3360bca839d08fdd2acf546b19b2fddd">&#9670;&nbsp;</a></span>QsciLexerVerilog()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciLexerVerilog::QsciLexerVerilog </td>
          <td>(</td>
          <td class="paramtype">QObject *&#160;</td>
          <td class="paramname"><em>parent</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Construct a <a class="el" href="classQsciLexerVerilog.html" title="The QsciLexerVerilog class encapsulates the Scintilla Verilog lexer.">QsciLexerVerilog</a> with parent <em>parent</em>. <em>parent</em> is typically the <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> instance. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="abc3666027fe7f0b8ae78ee34e3276069"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc3666027fe7f0b8ae78ee34e3276069">&#9670;&nbsp;</a></span>lexer()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerVerilog::lexer </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the name of the lexer. Some lexers support a number of languages. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ac7c40b97187e23ab85f6d95113f91b39">QsciLexer</a>.</p>

</div>
</div>
<a id="a410bcada9eb227aa5689304b861c9997"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a410bcada9eb227aa5689304b861c9997">&#9670;&nbsp;</a></span>defaultColor()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerVerilog::defaultColor </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the foreground colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVerilog.html#adbaf4979024f12f9382df61cba0e75e8">defaultPaper()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#af7508f1b816a2c9446d36141edc9b5ce">QsciLexer</a>.</p>

</div>
</div>
<a id="adbaf4979024f12f9382df61cba0e75e8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adbaf4979024f12f9382df61cba0e75e8">&#9670;&nbsp;</a></span>defaultPaper()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerVerilog::defaultPaper </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the background colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVerilog.html#a410bcada9eb227aa5689304b861c9997">defaultColor()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a7e5ab7f541d913760c32abedbdc72963">QsciLexer</a>.</p>

</div>
</div>
<a id="aebb96727a845f9547a60848f6163d461"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aebb96727a845f9547a60848f6163d461">&#9670;&nbsp;</a></span>keywords()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerVerilog::keywords </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>set</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the set of keywords for the keyword set <em>set</em> recognised by the lexer as a space separated string. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a013b7c1bf9846e231b97827dfd9540b0">QsciLexer</a>.</p>

</div>
</div>
<a id="ac6d9fdf26d30d14707e0b0778f80d54d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac6d9fdf26d30d14707e0b0778f80d54d">&#9670;&nbsp;</a></span>description()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QString QsciLexerVerilog::description </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the descriptive name for style number <em>style</em>. If the style is invalid for this language then an empty QString is returned. This is intended to be used in user preference dialogs. </p>

<p>Implements <a class="el" href="classQsciLexer.html#add9c20adb43bc38d1a0ca3083ac3e6fa">QsciLexer</a>.</p>

</div>
</div>
<a id="ad476092b3970fe44068dd023f8becc96"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad476092b3970fe44068dd023f8becc96">&#9670;&nbsp;</a></span>refreshProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerVerilog::refreshProperties </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Causes all properties to be refreshed by emitting the <a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged()</a> signal as required. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ae508c3ab4ce1f338dfff3ddf5ee7e34c">QsciLexer</a>.</p>

</div>
</div>
<a id="a7b84f78b170cec259efb2f367c54ce4b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7b84f78b170cec259efb2f367c54ce4b">&#9670;&nbsp;</a></span>setFoldAtElse()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerVerilog::setFoldAtElse </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then "} else {" lines can be folded. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVerilog.html#a1bb598533be61e117a252d06cf5e4a4b">foldAtElse()</a> </dd></dl>

</div>
</div>
<a id="a1bb598533be61e117a252d06cf5e4a4b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1bb598533be61e117a252d06cf5e4a4b">&#9670;&nbsp;</a></span>foldAtElse()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerVerilog::foldAtElse </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if "} else {" lines can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVerilog.html#a7b84f78b170cec259efb2f367c54ce4b">setFoldAtElse()</a> </dd></dl>

</div>
</div>
<a id="ac79b616c3ba0872856d90b119bfd81b8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac79b616c3ba0872856d90b119bfd81b8">&#9670;&nbsp;</a></span>setFoldComments()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerVerilog::setFoldComments </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then multi-line comment blocks can be folded. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVerilog.html#a7e9725132ec5521255eb7d9ac81ae853">foldComments()</a> </dd></dl>

</div>
</div>
<a id="a7e9725132ec5521255eb7d9ac81ae853"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7e9725132ec5521255eb7d9ac81ae853">&#9670;&nbsp;</a></span>foldComments()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerVerilog::foldComments </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if multi-line comment blocks can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVerilog.html#ac79b616c3ba0872856d90b119bfd81b8">setFoldComments()</a> </dd></dl>

</div>
</div>
<a id="a17ff342a5c1d94ce760a3dc02cfcda1d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a17ff342a5c1d94ce760a3dc02cfcda1d">&#9670;&nbsp;</a></span>setFoldCompact()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerVerilog::setFoldCompact </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then trailing blank lines are included in a fold block. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVerilog.html#ae1f192b93ad970cb792b5dcac4aa22d8">foldCompact()</a> </dd></dl>

</div>
</div>
<a id="ae1f192b93ad970cb792b5dcac4aa22d8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae1f192b93ad970cb792b5dcac4aa22d8">&#9670;&nbsp;</a></span>foldCompact()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerVerilog::foldCompact </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if trailing blank lines are included in a fold block.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVerilog.html#a17ff342a5c1d94ce760a3dc02cfcda1d">setFoldCompact()</a> </dd></dl>

</div>
</div>
<a id="ab7c13e959940db389fe0daeb96267d8e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab7c13e959940db389fe0daeb96267d8e">&#9670;&nbsp;</a></span>setFoldPreprocessor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerVerilog::setFoldPreprocessor </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then preprocessor blocks can be folded. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVerilog.html#ad70da8e3f3695cfc277d02ab9c0396d3">foldPreprocessor()</a> </dd></dl>

</div>
</div>
<a id="ad70da8e3f3695cfc277d02ab9c0396d3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad70da8e3f3695cfc277d02ab9c0396d3">&#9670;&nbsp;</a></span>foldPreprocessor()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerVerilog::foldPreprocessor </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if preprocessor blocks can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVerilog.html#ab7c13e959940db389fe0daeb96267d8e">setFoldPreprocessor()</a> </dd></dl>

</div>
</div>
<a id="af57050a2bcb9d1d285199159da0ba6e0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af57050a2bcb9d1d285199159da0ba6e0">&#9670;&nbsp;</a></span>setFoldAtModule()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerVerilog::setFoldAtModule </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then modules can be folded. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVerilog.html#a8a4389bd37a806046a7c0b51cc1a6ead">foldAtModule()</a> </dd></dl>

</div>
</div>
<a id="a8a4389bd37a806046a7c0b51cc1a6ead"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8a4389bd37a806046a7c0b51cc1a6ead">&#9670;&nbsp;</a></span>foldAtModule()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerVerilog::foldAtModule </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if modules can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVerilog.html#af57050a2bcb9d1d285199159da0ba6e0">setFoldAtModule()</a> </dd></dl>

</div>
</div>
<a id="aa1bd0effe3ed23e2bb3334b778efb74a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa1bd0effe3ed23e2bb3334b778efb74a">&#9670;&nbsp;</a></span>readProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerVerilog::readProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are read from the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVerilog.html#a97b418522a5866d04d9553931dd1c7f4">writeProperties()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ad472b16506a4cbc19634f07aa90f1ea6">QsciLexer</a>.</p>

</div>
</div>
<a id="a97b418522a5866d04d9553931dd1c7f4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a97b418522a5866d04d9553931dd1c7f4">&#9670;&nbsp;</a></span>writeProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerVerilog::writeProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are written to the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerVerilog.html#aa1bd0effe3ed23e2bb3334b778efb74a">readProperties()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#abccc4e010b724df1a7b5c5f3bce29501">QsciLexer</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
