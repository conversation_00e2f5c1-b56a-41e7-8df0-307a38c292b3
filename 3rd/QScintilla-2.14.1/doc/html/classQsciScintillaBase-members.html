<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">QsciScintillaBase Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>, including all inherited members.</p>
<table class="directory">
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>ANNOTATION_BOXED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>ANNOTATION_HIDDEN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>ANNOTATION_INDENTED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>ANNOTATION_STANDARD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bytesAsText</b>(const char *bytes, int size) const (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#abce274ef71035c67baadaa167a1fe5a7">canInsertFromMimeData</a>(const QMimeData *source) const</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>CARET_EVEN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>CARET_JUMPS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>CARET_SLOP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>CARET_STRICT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>CARETSTYLE_BLOCK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>CARETSTYLE_INVISIBLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>CARETSTYLE_LINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ac8a72227fc8efff78505733d1663f927">changeEvent</a>(QEvent *e)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>commandKey</b>(int qt_key, int &amp;modifiers) (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#adb8531cdc862f79cce9fa4d970bc13a2">contextMenuEvent</a>(QContextMenuEvent *e)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>contextMenuNeeded</b>(int x, int y) const (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad7b8480681e4b4d5689d0e6d822dc3c0">dragEnterEvent</a>(QDragEnterEvent *e)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a67c4a9da730c69a2b9fda0a1a02348f1">dragLeaveEvent</a>(QDragLeaveEvent *e)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#af25249a8e4e0f0966395b5006a5362d9">dragMoveEvent</a>(QDragMoveEvent *e)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a33c8d6d9915a1375c0d7c24beaceb951">dropEvent</a>(QDropEvent *e)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>EDGE_BACKGROUND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>EDGE_LINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>EDGE_MULTILINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>EDGE_NONE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a2c339fd90e92408440230ee9d84cabcf">focusInEvent</a>(QFocusEvent *e)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad6fb5a9621640080fc9909f94b6c0213">focusNextPrevChild</a>(bool next)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a1aec9b47eeaf611687eeeef0d1aa3a00">focusOutEvent</a>(QFocusEvent *e)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a5f105b9ec17cd73a0cd601ac9be82dd4">fromMimeData</a>(const QMimeData *source, bool &amp;rectangular) const</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>INDIC0_MASK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>INDIC1_MASK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>INDIC2_MASK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>INDIC_BOX</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>INDIC_COMPOSITIONTHICK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>INDIC_COMPOSITIONTHIN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>INDIC_CONTAINER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>INDIC_DASH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>INDIC_DIAGONAL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>INDIC_DOTBOX</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>INDIC_DOTS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>INDIC_FULLBOX</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>INDIC_GRADIENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>INDIC_GRADIENTCENTRE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>INDIC_HIDDEN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>INDIC_IME</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>INDIC_IME_MAX</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>INDIC_MAX</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>INDIC_PLAIN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>INDIC_POINT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>INDIC_POINTCHARACTER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>INDIC_ROUNDBOX</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>INDIC_SQUIGGLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>INDIC_SQUIGGLELOW</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>INDIC_SQUIGGLEPIXMAP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>INDIC_STRAIGHTBOX</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>INDIC_STRIKE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>INDIC_TEXTFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>INDIC_TT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>INDICS_MASK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#acb05eb7e7c7cac07547a08d0628013fe">inputMethodEvent</a>(QInputMethodEvent *event)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>inputMethodQuery</b>(Qt::InputMethodQuery query) const (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a39f62b8e6cee02e86d7af508d20a191d">keyPressEvent</a>(QKeyEvent *e)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aab16e9b7ca9a17af2af3b7ca7f14c8c4">mouseDoubleClickEvent</a>(QMouseEvent *e)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a5a4a2c5466d1b4f7d6e835c253cb1730">mouseMoveEvent</a>(QMouseEvent *e)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a6d6f13610560a2c281f638f3a40046f6">mousePressEvent</a>(QMouseEvent *e)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a6a5b949013c382c1b5f341137cd37752">mouseReleaseEvent</a>(QMouseEvent *e)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a2ea74fb61f3d2d983d142a6ec8c3cc9d">paintEvent</a>(QPaintEvent *e)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a0f69249f4e97b96f09ea70f546df7464">pool</a>()</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a425344ca700d69b60ffeb3f8122f7ff9">QsciScintillaBase</a>(QWidget *parent=0)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">explicit</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a747feb07236c1beccadd446562b53b84">QSCN_SELCHANGED</a>(bool yes)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a1efa4394b588d27fd2a3bd40163a2342">replaceHorizontalScrollBar</a>(QScrollBar *scrollBar)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a900e3a0287e262fe65c51162e562fc5d">replaceVerticalScrollBar</a>(QScrollBar *scrollBar)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a6d0427b93e05876c9a2b541eae08ddab">resizeEvent</a>(QResizeEvent *e)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_AC_COMMAND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_AC_DOUBLECLICK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_AC_FILLUP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_AC_NEWLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_AC_TAB</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_ALPHA_NOALPHA</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_ALPHA_OPAQUE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_ALPHA_TRANSPARENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_AUTOMATICFOLD_CHANGE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_AUTOMATICFOLD_CLICK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_AUTOMATICFOLD_SHOW</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CACHE_CARET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CACHE_DOCUMENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CACHE_NONE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CACHE_PAGE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CARETSTICKY_OFF</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CARETSTICKY_ON</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CARETSTICKY_WHITESPACE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CASE_CAMEL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CASE_LOWER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CASE_MIXED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CASE_UPPER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CASEINSENSITIVEBEHAVIOUR_IGNORECASE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CASEINSENSITIVEBEHAVIOUR_RESPECTCASE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CHARSET_8859_15</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CHARSET_ANSI</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CHARSET_ARABIC</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CHARSET_BALTIC</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CHARSET_CHINESEBIG5</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CHARSET_CYRILLIC</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CHARSET_DEFAULT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CHARSET_EASTEUROPE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CHARSET_GB2312</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CHARSET_GREEK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CHARSET_HANGUL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CHARSET_HEBREW</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CHARSET_JOHAB</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CHARSET_MAC</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CHARSET_OEM</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CHARSET_OEM866</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CHARSET_RUSSIAN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CHARSET_SHIFTJIS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CHARSET_SYMBOL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CHARSET_THAI</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CHARSET_TURKISH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CHARSET_VIETNAMESE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CP_DBCS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CP_UTF8</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CURSORARROW</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CURSORNORMAL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_CURSORREVERSEARROW</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_CURSORWAIT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_DOCUMENTOPTION_DEFAULT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_DOCUMENTOPTION_STYLES_NONE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_DOCUMENTOPTION_TEXT_LARGE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_EFF_QUALITY_ANTIALIASED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_EFF_QUALITY_DEFAULT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_EFF_QUALITY_LCD_OPTIMIZED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_EFF_QUALITY_MASK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_EFF_QUALITY_NON_ANTIALIASED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_EOL_CR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_EOL_CRLF</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_EOL_LF</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_FOLDACTION_CONTRACT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_FOLDACTION_EXPAND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_FOLDACTION_TOGGLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_FOLDDISPLAYTEXT_BOXED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_FOLDDISPLAYTEXT_HIDDEN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_FOLDDISPLAYTEXT_STANDARD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_FOLDFLAG_LEVELNUMBERS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_FOLDFLAG_LINEAFTER_CONTRACTED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_FOLDFLAG_LINEAFTER_EXPANDED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_FOLDFLAG_LINEBEFORE_CONTRACTED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_FOLDFLAG_LINEBEFORE_EXPANDED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_FOLDFLAG_LINESTATE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_FOLDLEVELBASE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_FOLDLEVELHEADERFLAG</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_FOLDLEVELNUMBERMASK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_FOLDLEVELWHITEFLAG</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_FONT_SIZE_MULTIPLIER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_IDLESTYLING_AFTERVISIBLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_IDLESTYLING_ALL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_IDLESTYLING_NONE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_IDLESTYLING_TOVISIBLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_IME_INLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_IME_WINDOWED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_INDICFLAG_VALUEBEFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_INDICVALUEBIT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_INDICVALUEMASK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ae92e21c6957f026dbfd00008348e8b50ac3746adc7ec9881c9a46be88e26417d1">SC_IV_LOOKBOTH</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ae92e21c6957f026dbfd00008348e8b50a63693e8e8da215430f8b94630cbad3c0">SC_IV_LOOKFORWARD</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ae92e21c6957f026dbfd00008348e8b50ad814f18251426f392498fd2969e11d65">SC_IV_NONE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ae92e21c6957f026dbfd00008348e8b50aa8b077f3d8af29de8fae45dfd0885298">SC_IV_REAL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_LASTSTEPINUNDOREDO</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_LINE_END_TYPE_DEFAULT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_LINE_END_TYPE_UNICODE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_LINECHARACTERINDEX_NONE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_LINECHARACTERINDEX_UTF16</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_LINECHARACTERINDEX_UTF32</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41a68a70615f89282762ba21aa6ec629dac">SC_MARGIN_BACK</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41aabd3cb3735935f9be890931a34d07989">SC_MARGIN_COLOUR</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41a2f3ac8cfede54b81db88e29b7f81e19c">SC_MARGIN_FORE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41a55a92a7661156a126c48237234251e1d">SC_MARGIN_NUMBER</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41af99d2ba5aa3873f646a8eac1a889de6a">SC_MARGIN_RTEXT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41a7776c14d3a1424576a26a8da304b96bf">SC_MARGIN_SYMBOL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ab7ed107d6ace096e9026c31145c48b41aa29598ff9ba1349daee66560cdd692bd">SC_MARGIN_TEXT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_MARGINOPTION_NONE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_MARGINOPTION_SUBLINESELECT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca940ced5307e462959ce165d8717a31d4">SC_MARK_ARROW</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca6f1caf375a3079d67c36998c1bd453a4">SC_MARK_ARROWDOWN</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca6f07eeddc235e313c4ca597220c71a0c">SC_MARK_ARROWS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca53a38f2234ab3e6df8d6cec09ecd7318">SC_MARK_AVAILABLE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca8a44e2cee896ee89527e1d026e8cd9ff">SC_MARK_BACKGROUND</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca500892fc4eef318262b009f6eddc9eda">SC_MARK_BOOKMARK</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cacebb22ccb805fa137c65eda743d32e0a">SC_MARK_BOXMINUS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca8d928f238170d0765acb492d0e8f0f65">SC_MARK_BOXMINUSCONNECTED</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca6b210dd7ba9ae1b8c503965b6e9ada9a">SC_MARK_BOXPLUS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca3582c3195c0533bca604a874ee1ecab8">SC_MARK_BOXPLUSCONNECTED</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca8c649856f102e81a5caa5a92f28b38fd">SC_MARK_CHARACTER</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cabf08078081d1fb79be98e1b5a6401ec3">SC_MARK_CIRCLE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca58fc2ba342cf7cc9e5f5e9a59d4319bc">SC_MARK_CIRCLEMINUS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca2bbb4d3dea9e0b75ef9374c1c2b23c65">SC_MARK_CIRCLEMINUSCONNECTED</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca222004d413ee607197204f26950f3a0c">SC_MARK_CIRCLEPLUS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca5293176024a0bba9eeb54b061a2930f9">SC_MARK_CIRCLEPLUSCONNECTED</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca961e0562a26ab763fba1bc1e92123b85">SC_MARK_DOTDOTDOT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cacf6e7cca56bcd03b660b3590153d1075">SC_MARK_EMPTY</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca4f29b2c933a525fe0a80f0a58ba7eb61">SC_MARK_FULLRECT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214caf591d473d118d6fa98adf5e73fd9c61d">SC_MARK_LCORNER</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca85a6c4d68e4c936c46c8711f656d95ca">SC_MARK_LCORNERCURVE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca08a00107e2110dce658fe7cb10f75e58">SC_MARK_LEFTRECT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca7baf71a4e105fbebbaa7803a3f722b0f">SC_MARK_MINUS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca464ae044427aa620a0668510ff1430b9">SC_MARK_PIXMAP</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cae324f61ed2740c6be760489cbaa69fb8">SC_MARK_PLUS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214caa1866931fbc9dea971e6ae3f5be83abd">SC_MARK_RGBAIMAGE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca39e5d4cae13901613bcfae619cd496b5">SC_MARK_ROUNDRECT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca60f9340f78d0c386cb2df238849f121d">SC_MARK_SHORTARROW</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cafa1a0af509be0284f7c69df8134d85ca">SC_MARK_SMALLRECT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214caa9ae33880a1ee19ce4db6544bb61a84d">SC_MARK_TCORNER</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca787e7e471b489bda535116b75765acad">SC_MARK_TCORNERCURVE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214cadf0e9f9a2980c5e693c67819a64f132e">SC_MARK_UNDERLINE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad004786b74db7858f6642c23447a214ca8770dbe317581062d5d1bcb85592b784">SC_MARK_VLINE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_MARKNUM_FOLDER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_MARKNUM_FOLDEREND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_MARKNUM_FOLDERMIDTAIL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_MARKNUM_FOLDEROPEN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_MARKNUM_FOLDEROPENMID</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_MARKNUM_FOLDERSUB</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_MARKNUM_FOLDERTAIL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_MASK_FOLDERS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_MOD_BEFOREDELETE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_MOD_BEFOREINSERT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_MOD_CHANGEANNOTATION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_MOD_CHANGEFOLD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_MOD_CHANGEINDICATOR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_MOD_CHANGELINESTATE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_MOD_CHANGEMARGIN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_MOD_CHANGEMARKER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_MOD_CHANGESTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_MOD_CHANGETABSTOPS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_MOD_CONTAINER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_MOD_DELETETEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_MOD_INSERTCHECK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_MOD_INSERTTEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_MOD_LEXERSTATE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_MODEVENTMASKALL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_MULTIAUTOC_EACH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_MULTIAUTOC_ONCE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_MULTILINEUNDOREDO</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_MULTIPASTE_EACH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_MULTIPASTE_ONCE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_MULTISTEPUNDOREDO</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_ORDER_CUSTOM</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_ORDER_PERFORMSORT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_ORDER_PRESORTED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_PERFORMED_REDO</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_PERFORMED_UNDO</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_PERFORMED_USER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_PHASES_MULTIPLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_PHASES_ONE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_PHASES_TWO</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_POPUP_ALL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_POPUP_NEVER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_POPUP_TEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_PRINT_BLACKONWHITE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_PRINT_COLOURONWHITE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_PRINT_COLOURONWHITEDEFAULTBG</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_PRINT_INVERTLIGHT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_PRINT_NORMAL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_PRINT_SCREENCOLOURS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_SEL_LINES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_SEL_RECTANGLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_SEL_STREAM</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_SEL_THIN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_STARTACTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_STATUS_BADALLOC</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_STATUS_FAILURE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_STATUS_OK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_STATUS_WARN_START</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_STATUS_WARNREGEX</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_TECHNOLOGY_DEFAULT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_TECHNOLOGY_DIRECTWRITE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_TECHNOLOGY_DIRECTWRITEDC</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_TECHNOLOGY_DIRECTWRITERETAIN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_TIME_FOREVER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_TYPE_BOOLEAN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_TYPE_INTEGER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_TYPE_STRING</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_UPDATE_CONTENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_UPDATE_H_SCROLL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_UPDATE_SELECTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_UPDATE_V_SCROLL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_WEIGHT_BOLD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_WEIGHT_NORMAL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_WEIGHT_SEMIBOLD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_WRAP_CHAR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_WRAP_NONE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_WRAP_WHITESPACE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_WRAP_WORD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_WRAPINDENT_DEEPINDENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_WRAPINDENT_FIXED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_WRAPINDENT_INDENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_WRAPINDENT_SAME</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_WRAPVISUALFLAG_END</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_WRAPVISUALFLAG_MARGIN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_WRAPVISUALFLAG_NONE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_WRAPVISUALFLAG_START</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_WRAPVISUALFLAGLOC_DEFAULT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SC_WRAPVISUALFLAGLOC_END_BY_TEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SC_WRAPVISUALFLAGLOC_START_BY_TEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#af2cc3652d35b4d0ec1d8c9ac18e2225e">SCEN_CHANGE</a>()</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCFIND_CXX11REGEX</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCFIND_MATCHCASE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCFIND_POSIX</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCFIND_REGEXP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCFIND_WHOLEWORD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCFIND_WORDSTART</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_ADDREFDOCUMENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_ADDSELECTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_ADDSTYLEDTEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_ADDTABSTOP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaada4cbb31d6583ed80804e4b94cd4023">SCI_ADDTEXT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_ADDUNDOACTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_ALLOCATE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_ALLOCATEEXTENDEDSTYLES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_ALLOCATELINECHARACTERINDEX</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_ALLOCATESUBSTYLES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_ANNOTATIONCLEARALL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_ANNOTATIONGETLINES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_ANNOTATIONGETSTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_ANNOTATIONGETSTYLEOFFSET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_ANNOTATIONGETSTYLES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_ANNOTATIONGETTEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_ANNOTATIONGETVISIBLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_ANNOTATIONSETSTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_ANNOTATIONSETSTYLEOFFSET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_ANNOTATIONSETSTYLES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_ANNOTATIONSETTEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_ANNOTATIONSETVISIBLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_APPENDTEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_ASSIGNCMDKEY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_AUTOCACTIVE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_AUTOCCANCEL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_AUTOCCOMPLETE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_AUTOCGETAUTOHIDE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_AUTOCGETCANCELATSTART</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_AUTOCGETCASEINSENSITIVEBEHAVIOUR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_AUTOCGETCHOOSESINGLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_AUTOCGETCURRENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_AUTOCGETCURRENTTEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_AUTOCGETDROPRESTOFWORD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_AUTOCGETIGNORECASE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_AUTOCGETMAXHEIGHT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_AUTOCGETMAXWIDTH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_AUTOCGETMULTI</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_AUTOCGETORDER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_AUTOCGETSEPARATOR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_AUTOCGETTYPESEPARATOR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_AUTOCPOSSTART</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_AUTOCSELECT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_AUTOCSETAUTOHIDE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_AUTOCSETCANCELATSTART</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_AUTOCSETCASEINSENSITIVEBEHAVIOUR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_AUTOCSETCHOOSESINGLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_AUTOCSETDROPRESTOFWORD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_AUTOCSETFILLUPS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_AUTOCSETIGNORECASE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa99467be94f4357e1be0ddf72bde6ae5a">SCI_AUTOCSETMAXHEIGHT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_AUTOCSETMAXWIDTH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_AUTOCSETMULTI</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_AUTOCSETORDER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_AUTOCSETSEPARATOR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_AUTOCSETTYPESEPARATOR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_AUTOCSHOW</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_AUTOCSTOPS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_BACKTAB</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_BEGINUNDOACTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_BRACEBADLIGHT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_BRACEBADLIGHTINDICATOR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_BRACEHIGHLIGHT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_BRACEHIGHLIGHTINDICATOR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_BRACEMATCH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CALLTIPACTIVE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CALLTIPCANCEL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CALLTIPPOSSTART</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CALLTIPSETBACK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CALLTIPSETFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CALLTIPSETFOREHLT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CALLTIPSETHLT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CALLTIPSETPOSITION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CALLTIPSETPOSSTART</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CALLTIPSHOW</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CALLTIPUSESTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CANCEL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CANPASTE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CANREDO</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CANUNDO</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CHANGEINSERTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CHANGELEXERSTATE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CHARLEFT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CHARLEFTEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CHARLEFTRECTEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CHARPOSITIONFROMPOINT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CHARPOSITIONFROMPOINTCLOSE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CHARRIGHT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CHARRIGHTEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CHARRIGHTRECTEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CHOOSECARETX</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CLEAR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CLEARALL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CLEARALLCMDKEYS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CLEARCMDKEY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CLEARDOCUMENTSTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa4eca65e764b9d0ef2fb23d22bc872bcb">SCI_CLEARREGISTEREDIMAGES</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CLEARREPRESENTATION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CLEARSELECTIONS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CLEARTABSTOPS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_COLOURISE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CONTRACTEDFOLDNEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CONVERTEOLS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_COPY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa0fd449316fa24a3cb53721cf17b9f684">SCI_COPYALLOWLINE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_COPYRANGE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_COPYTEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_COUNTCHARACTERS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_COUNTCODEUNITS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CREATEDOCUMENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_CREATELOADER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_CUT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_DELETEBACK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_DELETEBACKNOTLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_DELETERANGE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_DELLINELEFT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_DELLINERIGHT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_DELWORDLEFT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_DELWORDRIGHT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_DELWORDRIGHTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_DESCRIBEKEYWORDSETS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_DESCRIBEPROPERTY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_DESCRIPTIONOFSTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_DISTANCETOSECONDARYSTYLES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_DOCLINEFROMVISIBLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_DOCUMENTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_DOCUMENTENDEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_DOCUMENTSTART</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_DOCUMENTSTARTEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_DROPSELECTIONN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_EDITTOGGLEOVERTYPE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aac8f52176e19feec95c354452b6358d93">SCI_EMPTYUNDOBUFFER</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_ENDUNDOACTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_ENSUREVISIBLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_ENSUREVISIBLEENFORCEPOLICY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_EXPANDCHILDREN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_FINDCOLUMN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_FINDINDICATORFLASH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_FINDINDICATORHIDE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_FINDINDICATORSHOW</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_FINDTEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_FOLDALL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_FOLDCHILDREN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_FOLDDISPLAYTEXTSETSTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_FOLDLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_FORMATRANGE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_FORMFEED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_FREESUBSTYLES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETACCESSIBILITY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETADDITIONALCARETFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETADDITIONALCARETSBLINK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETADDITIONALCARETSVISIBLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETADDITIONALSELALPHA</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETADDITIONALSELECTIONTYPING</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETALLLINESVISIBLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aafbdd81cde4931224e6f87aceba707a04">SCI_GETANCHOR</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETAUTOMATICFOLD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETBACKSPACEUNINDENTS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETBUFFEREDDRAW</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETCARETFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETCARETLINEBACK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETCARETLINEBACKALPHA</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETCARETLINEFRAME</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETCARETLINEVISIBLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETCARETLINEVISIBLEALWAYS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETCARETPERIOD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETCARETSTICKY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETCARETSTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETCARETWIDTH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa4e6fc6b70c85f83622c9a17516bb2675">SCI_GETCHARACTERPOINTER</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETCHARAT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETCODEPAGE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETCOLUMN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETCOMMANDEVENTS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETCONTROLCHARSYMBOL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETCURLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaf1289f2530afb81cc99e2b7e2e2cad28">SCI_GETCURRENTPOS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETCURSOR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETDIRECTFUNCTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETDIRECTPOINTER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETDOCPOINTER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETDOCUMENTOPTIONS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETEDGECOLOUR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETEDGECOLUMN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETEDGEMODE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETENDATLASTLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa820d8d563cb319ff42e5b9ea709d839d">SCI_GETENDSTYLED</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETEOLMODE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETEXTRAASCENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETEXTRADESCENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETFIRSTVISIBLELINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETFOCUS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETFOLDEXPANDED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETFOLDLEVEL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETFOLDPARENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETFONTQUALITY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETGAPPOSITION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETHIGHLIGHTGUIDE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETHOTSPOTACTIVEBACK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETHOTSPOTACTIVEFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETHOTSPOTACTIVEUNDERLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETHOTSPOTSINGLELINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETHSCROLLBAR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETIDENTIFIER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETIDLESTYLING</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETIMEINTERACTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETINDENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETINDENTATIONGUIDES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETINDICATORCURRENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETINDICATORVALUE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETLASTCHILD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETLAYOUTCACHE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETLENGTH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaf625e0ecead2e1d0bc3e0cefe2e8954a">SCI_GETLEXER</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETLEXERLANGUAGE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETLINECHARACTERINDEX</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETLINECOUNT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETLINEENDPOSITION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETLINEENDTYPESACTIVE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETLINEENDTYPESALLOWED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETLINEENDTYPESSUPPORTED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETLINEINDENTATION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETLINEINDENTPOSITION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETLINESELENDPOSITION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETLINESELSTARTPOSITION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETLINESTATE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETLINEVISIBLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETMAINSELECTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETMARGINBACKN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa282bc83043fda7837568925243fcb384">SCI_GETMARGINCURSORN</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETMARGINLEFT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aadbd2eceef7f59bcda7d7db01a4aa7c7b">SCI_GETMARGINMASKN</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETMARGINOPTIONS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETMARGINRIGHT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETMARGINS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaffc41be0dbc2eb4b00438f0b489c7c88">SCI_GETMARGINSENSITIVEN</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa919cf8a6d08d570e00ece099ff62010c">SCI_GETMARGINTYPEN</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa3654140951ae95d75f21c43cdcd91a43">SCI_GETMARGINWIDTHN</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETMAXLINESTATE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETMODEVENTMASK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaa5af597c3c35c97cbe9f6dd98462594c">SCI_GETMODIFY</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETMOUSEDOWNCAPTURES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETMOUSEDWELLTIME</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETMOUSESELECTIONRECTANGULARSWITCH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETMOUSEWHEELCAPTURES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETMOVEEXTENDSSELECTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETMULTIPASTE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETMULTIPLESELECTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETNAMEDSTYLES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETNEXTTABSTOP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETOVERTYPE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETPASTECONVERTENDINGS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETPHASESDRAW</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETPOSITIONCACHE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETPRIMARYSTYLEFROMSTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETPRINTCOLOURMODE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETPRINTMAGNIFICATION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETPRINTWRAPMODE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETPROPERTY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETPROPERTYEXPANDED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETPROPERTYINT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETPUNCTUATIONCHARS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETRANGEPOINTER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa98efd72455b4555e6d4cbd9cd79d2a5b">SCI_GETREADONLY</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETRECTANGULARSELECTIONANCHOR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETRECTANGULARSELECTIONANCHORVIRTUALSPACE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETRECTANGULARSELECTIONCARET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETRECTANGULARSELECTIONCARETVIRTUALSPACE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETRECTANGULARSELECTIONMODIFIER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETREPRESENTATION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETSCROLLWIDTH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETSCROLLWIDTHTRACKING</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETSEARCHFLAGS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETSELALPHA</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETSELECTIONEMPTY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETSELECTIONEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETSELECTIONMODE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETSELECTIONNANCHOR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETSELECTIONNANCHORVIRTUALSPACE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETSELECTIONNCARET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETSELECTIONNCARETVIRTUALSPACE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETSELECTIONNEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETSELECTIONNSTART</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETSELECTIONS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETSELECTIONSTART</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETSELEOLFILLED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETSELTEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETSTATUS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETSTYLEAT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETSTYLEBITS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETSTYLEBITSNEEDED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETSTYLEDTEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETSTYLEFROMSUBSTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETSUBSTYLEBASES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETSUBSTYLESLENGTH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETSUBSTYLESSTART</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETTABDRAWMODE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETTABINDENTS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETTABWIDTH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETTAG</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETTARGETEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETTARGETSTART</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETTARGETTEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETTECHNOLOGY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa4bc0cd151979992bc5015852c5dbfbfe">SCI_GETTEXT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aadd626735df321c6b994c887cfad61ed4">SCI_GETTEXTLENGTH</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETTEXTRANGE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETTWOPHASEDRAW</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETUNDOCOLLECTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETUSETABS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETVIEWEOL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETVIEWWS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETVIRTUALSPACEOPTIONS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETVSCROLLBAR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETWHITESPACECHARS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETWHITESPACESIZE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETWORDCHARS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETWRAPINDENTMODE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETWRAPMODE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETWRAPSTARTINDENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETWRAPVISUALFLAGS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETWRAPVISUALFLAGSLOCATION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GETXOFFSET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_GETZOOM</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GOTOLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa3c6da358d0bc87040b30811bbcbf8cf7">SCI_GOTOPOS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_GRABFOCUS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_HIDELINES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_HIDESELECTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_HOME</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_HOMEDISPLAY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_HOMEDISPLAYEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_HOMEEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_HOMERECTEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_HOMEWRAP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_HOMEWRAPEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_INDEXPOSITIONFROMLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_INDICATORALLONFOR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_INDICATORCLEARRANGE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_INDICATOREND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_INDICATORFILLRANGE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_INDICATORSTART</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_INDICATORVALUEAT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_INDICGETALPHA</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_INDICGETFLAGS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_INDICGETFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_INDICGETHOVERFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_INDICGETHOVERSTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_INDICGETOUTLINEALPHA</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_INDICGETSTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_INDICGETUNDER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_INDICSETALPHA</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_INDICSETFLAGS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_INDICSETFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_INDICSETHOVERFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_INDICSETHOVERSTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_INDICSETOUTLINEALPHA</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_INDICSETSTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_INDICSETUNDER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_INSERTTEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_ISRANGEWORD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_LEXER_START</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_LINECOPY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_LINECUT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_LINEDELETE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_LINEDOWN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_LINEDOWNEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_LINEDOWNRECTEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_LINEDUPLICATE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_LINEEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_LINEENDDISPLAY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_LINEENDDISPLAYEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_LINEENDEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_LINEENDRECTEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_LINEENDWRAP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_LINEENDWRAPEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_LINEFROMINDEXPOSITION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_LINEFROMPOSITION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_LINELENGTH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_LINEREVERSE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_LINESCROLL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_LINESCROLLDOWN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_LINESCROLLUP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_LINESJOIN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_LINESONSCREEN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_LINESSPLIT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_LINETRANSPOSE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_LINEUP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_LINEUPEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_LINEUPRECTEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_LOADLEXERLIBRARY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_LOWERCASE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_MARGINGETSTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_MARGINGETSTYLEOFFSET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_MARGINGETSTYLES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_MARGINGETTEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_MARGINSETSTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_MARGINSETSTYLEOFFSET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_MARGINSETSTYLES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_MARGINSETTEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_MARGINTEXTCLEARALL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1be8617303dc15428758e22749267263">SCI_MARKERADD</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_MARKERADDSET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa370a2c2674421348d23ecb97ff981b2a">SCI_MARKERDEFINE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaffe2901cffbccede9b0b5d1636bb5e9f">SCI_MARKERDEFINEPIXMAP</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa8ff17201e6d0cb9fe6e738a7a2e81932">SCI_MARKERDEFINERGBAIMAGE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aae19516cd9746dbec20598773ad354d4e">SCI_MARKERDELETE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa010af0bf4fe497e1b68fe1fb56580770">SCI_MARKERDELETEALL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa007cbfb293accfd436ea9443b2678327">SCI_MARKERDELETEHANDLE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_MARKERENABLEHIGHLIGHT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaaee02f504dec75c8b349150805440fd7">SCI_MARKERGET</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa4db578069d526ece8c0a9d08869a3033">SCI_MARKERLINEFROMHANDLE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1f843331fd750110c6f97fa443567b22">SCI_MARKERNEXT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1e455f05b605c2ba82be3baf05e3abe4">SCI_MARKERPREVIOUS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_MARKERSETALPHA</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa0419ba42e62661c245af25007bac3bfe">SCI_MARKERSETBACK</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_MARKERSETBACKSELECTED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1fb7b42e9fbbe27b662b0edb21ac2d2f">SCI_MARKERSETFORE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_MARKERSYMBOLDEFINED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_MOVECARETINSIDEVIEW</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_MOVESELECTEDLINESDOWN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_MOVESELECTEDLINESUP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_MULTIEDGEADDLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_MULTIEDGECLEARALL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_MULTIPLESELECTADDEACH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_MULTIPLESELECTADDNEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_NAMEOFSTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_NEWLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_NULL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_OPTIONAL_START</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_PAGEDOWN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_PAGEDOWNEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_PAGEDOWNRECTEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_PAGEUP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_PAGEUPEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_PAGEUPRECTEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_PARADOWN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_PARADOWNEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_PARAUP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_PARAUPEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_PASTE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_POINTXFROMPOSITION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_POINTYFROMPOSITION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_POSITIONAFTER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_POSITIONBEFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_POSITIONFROMLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_POSITIONFROMPOINT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_POSITIONFROMPOINTCLOSE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_POSITIONRELATIVE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_POSITIONRELATIVECODEUNITS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_PRIVATELEXERCALL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_PROPERTYNAMES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_PROPERTYTYPE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_REDO</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa5c17528701e5a34fc8b685be0914d4a8">SCI_REGISTERIMAGE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaf1e4de8ebec57382f46449112d4f6821">SCI_REGISTERRGBAIMAGE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_RELEASEALLEXTENDEDSTYLES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_RELEASEDOCUMENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_RELEASELINECHARACTERINDEX</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_REPLACESEL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_REPLACETARGET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_REPLACETARGETRE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aabf4b5d1cf3d1aa52c010b489c2ccffc6">SCI_RGBAIMAGESETHEIGHT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_RGBAIMAGESETSCALE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa93791e94e6f6a9382f1f7e29f341f342">SCI_RGBAIMAGESETWIDTH</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_ROTATESELECTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SCROLLCARET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SCROLLRANGE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SCROLLTOEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SCROLLTOSTART</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SEARCHANCHOR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SEARCHINTARGET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SEARCHNEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SEARCHPREV</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SELECTALL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SELECTIONDUPLICATE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SELECTIONISRECTANGLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETACCESSIBILITY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETADDITIONALCARETFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETADDITIONALCARETSBLINK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETADDITIONALCARETSVISIBLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETADDITIONALSELALPHA</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETADDITIONALSELBACK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETADDITIONALSELECTIONTYPING</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETADDITIONALSELFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa9b577390896af25454459e1a9e08ad2e">SCI_SETANCHOR</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETAUTOMATICFOLD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETBACKSPACEUNINDENTS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETBUFFEREDDRAW</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETCARETFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETCARETLINEBACK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETCARETLINEBACKALPHA</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETCARETLINEFRAME</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETCARETLINEVISIBLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETCARETLINEVISIBLEALWAYS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETCARETPERIOD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETCARETSTICKY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETCARETSTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETCARETWIDTH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETCHARSDEFAULT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETCODEPAGE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETCOMMANDEVENTS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETCONTROLCHARSYMBOL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aabcd34a065f08d19b10ca6caaa78d3e78">SCI_SETCURRENTPOS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETCURSOR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETDOCPOINTER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETEDGECOLOUR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETEDGECOLUMN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETEDGEMODE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETEMPTYSELECTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETENDATLASTLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETEOLMODE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETEXTRAASCENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETEXTRADESCENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETFIRSTVISIBLELINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETFOCUS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETFOLDEXPANDED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETFOLDFLAGS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETFOLDLEVEL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETFOLDMARGINCOLOUR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETFOLDMARGINHICOLOUR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETFONTQUALITY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETHIGHLIGHTGUIDE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETHOTSPOTACTIVEBACK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETHOTSPOTACTIVEFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETHOTSPOTACTIVEUNDERLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETHOTSPOTSINGLELINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETHSCROLLBAR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETIDENTIFIER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETIDENTIFIERS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETIDLESTYLING</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETIMEINTERACTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETINDENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETINDENTATIONGUIDES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETINDICATORCURRENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETINDICATORVALUE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETKEYWORDS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETLAYOUTCACHE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa29b928416c21edb11e32d4325764fcc7">SCI_SETLEXER</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa6309b2b8bf3813c1041d31cb54ce3feb">SCI_SETLEXERLANGUAGE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETLINEENDTYPESALLOWED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETLINEINDENTATION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETLINESTATE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETMAINSELECTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETMARGINBACKN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aac5d3e4edd15f65d5e500d90590e443a9">SCI_SETMARGINCURSORN</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETMARGINLEFT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aad1cec6e4e0ad45ce7d7edad7acb8a3b5">SCI_SETMARGINMASKN</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETMARGINOPTIONS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETMARGINRIGHT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETMARGINS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa64b07178255dc95b19a7c8feabaac1b2">SCI_SETMARGINSENSITIVEN</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa0ee188f4bfe7289f454f99af191d1523">SCI_SETMARGINTYPEN</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa1195d46181a565b14806d94595fc7aa6">SCI_SETMARGINWIDTHN</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETMODEVENTMASK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETMOUSEDOWNCAPTURES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETMOUSEDWELLTIME</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETMOUSESELECTIONRECTANGULARSWITCH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETMOUSEWHEELCAPTURES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETMULTIPASTE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETMULTIPLESELECTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETOVERTYPE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETPASTECONVERTENDINGS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETPHASESDRAW</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETPOSITIONCACHE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETPRINTCOLOURMODE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETPRINTMAGNIFICATION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETPRINTWRAPMODE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETPROPERTY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETPUNCTUATIONCHARS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaa07157383b442ab2d2be8c2d03078fc2">SCI_SETREADONLY</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETRECTANGULARSELECTIONANCHOR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETRECTANGULARSELECTIONANCHORVIRTUALSPACE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETRECTANGULARSELECTIONCARET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETRECTANGULARSELECTIONCARETVIRTUALSPACE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETRECTANGULARSELECTIONMODIFIER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETREPRESENTATION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa20e9df7da839e5b9e2edd2366a7ecb97">SCI_SETSAVEPOINT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETSCROLLWIDTH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETSCROLLWIDTHTRACKING</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETSEARCHFLAGS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETSEL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETSELALPHA</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETSELBACK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETSELECTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETSELECTIONEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETSELECTIONMODE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETSELECTIONNANCHOR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETSELECTIONNANCHORVIRTUALSPACE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETSELECTIONNCARET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETSELECTIONNCARETVIRTUALSPACE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETSELECTIONNEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETSELECTIONNSTART</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETSELECTIONSTART</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETSELEOLFILLED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETSELFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETSTATUS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETSTYLEBITS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETSTYLING</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETSTYLINGEX</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETTABDRAWMODE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETTABINDENTS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETTABWIDTH</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETTARGETEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETTARGETRANGE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETTARGETSTART</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETTECHNOLOGY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aaeadc6fabc9859b2e52f9cfa23732f004">SCI_SETTEXT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETTWOPHASEDRAW</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETUNDOCOLLECTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETUSETABS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETVIEWEOL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETVIEWWS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETVIRTUALSPACEOPTIONS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETVISIBLEPOLICY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETVSCROLLBAR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETWHITESPACEBACK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETWHITESPACECHARS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETWHITESPACEFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETWHITESPACESIZE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETWORDCHARS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETWRAPINDENTMODE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETWRAPMODE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETWRAPSTARTINDENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETWRAPVISUALFLAGS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETWRAPVISUALFLAGSLOCATION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETXCARETPOLICY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETXOFFSET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SETYCARETPOLICY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SETZOOM</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_SHOWLINES</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_START</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STARTRECORD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STARTSTYLING</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STOPRECORD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STUTTEREDPAGEDOWN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STUTTEREDPAGEDOWNEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STUTTEREDPAGEUP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STUTTEREDPAGEUPEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STYLECLEARALL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STYLEGETBACK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STYLEGETBOLD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STYLEGETCASE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STYLEGETCHANGEABLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STYLEGETCHARACTERSET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STYLEGETEOLFILLED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STYLEGETFONT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STYLEGETFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STYLEGETHOTSPOT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STYLEGETITALIC</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STYLEGETSIZE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STYLEGETSIZEFRACTIONAL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STYLEGETUNDERLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STYLEGETVISIBLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STYLEGETWEIGHT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STYLERESETDEFAULT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STYLESETBACK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STYLESETBOLD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STYLESETCASE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STYLESETCHANGEABLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STYLESETCHARACTERSET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STYLESETEOLFILLED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STYLESETFONT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STYLESETFORE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STYLESETHOTSPOT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STYLESETITALIC</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STYLESETSIZE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STYLESETSIZEFRACTIONAL</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STYLESETUNDERLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_STYLESETVISIBLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_STYLESETWEIGHT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_SWAPMAINANCHORCARET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_TAB</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_TAGSOFSTYLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_TARGETFROMSELECTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_TARGETWHOLEDOCUMENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_TEXTHEIGHT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad9c35f7540b2457103db9cf8c877784aa5158fc6bdc2ceb345246b7f4ca45de04">SCI_TEXTWIDTH</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_TOGGLECARETSTICKY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_TOGGLEFOLD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_TOGGLEFOLDSHOWTEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_UNDO</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_UPPERCASE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_USEPOPUP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_USERLISTSHOW</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_VCHOME</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_VCHOMEDISPLAY</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_VCHOMEDISPLAYEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_VCHOMEEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_VCHOMERECTEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_VCHOMEWRAP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_VCHOMEWRAPEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_VERTICALCENTRECARET</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_VISIBLEFROMDOCLINE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_WORDENDPOSITION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_WORDLEFT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_WORDLEFTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_WORDLEFTENDEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_WORDLEFTEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_WORDPARTLEFT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_WORDPARTLEFTEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_WORDPARTRIGHT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_WORDPARTRIGHTEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_WORDRIGHT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_WORDRIGHTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_WORDRIGHTENDEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_WORDRIGHTEXTEND</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_WORDSTARTPOSITION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_WRAPCOUNT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCI_ZOOMIN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCI_ZOOMOUT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCK_ADD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCK_BACK</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCK_DELETE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCK_DIVIDE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCK_DOWN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCK_END</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCK_ESCAPE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCK_HOME</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCK_INSERT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCK_LEFT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCK_MENU</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCK_NEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCK_PRIOR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCK_RETURN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCK_RIGHT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCK_RWIN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCK_SUBTRACT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCK_TAB</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCK_UP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCK_WIN</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a5f5a499292d10817ab864bb61fc952bb">SCLEX_A68K</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a94d6c2b2fa424cbb4c8eb3749a9f934b">SCLEX_ABAQUS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a56c1b1e39b9c7e20faa9b7420d54e7a5">SCLEX_ADA</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a176475983f8e4985ca616779de3be8db">SCLEX_APDL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6fee40e395ba28044ccd9cbbc1db48d5">SCLEX_AS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a286421d44d37e6eade78481e3d063540">SCLEX_ASM</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6bc934ce8070f4cd38f4c2619b165b01">SCLEX_ASN1</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ac6732578f1c51e3a2757dddb839d7b5d">SCLEX_ASP</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a5a68f1f87e9069301116d328e30f63c7">SCLEX_ASYMPTOTE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a05181d968abb3e1fec89869dd14e2bae">SCLEX_AU3</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a2c30b22ff5f0f07f8ccf96eb0c0eb5d6">SCLEX_AVE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ad63b11d786d32c7101682682bf49c063">SCLEX_AVS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a207b1e77e47550f0b0787a107a206b71">SCLEX_BAAN</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ab05738aa98869eb2b998fb6d063d9dbc">SCLEX_BASH</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ae894213f20cdd7eae927718c87cbfae4">SCLEX_BATCH</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0abc6e7a4f3fbf502b080d443f4f779ea9">SCLEX_BIBTEX</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a8ca1aa2873729ccadcc0c952d574299f">SCLEX_BLITZBASIC</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ac26190310f45bf026d031fd52729f310">SCLEX_BULLANT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aaf2da832f0698fe3cea0693f57d4b7d4">SCLEX_CAML</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a7d602388b550d67454b1c11de9fac04e">SCLEX_CLW</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a8a1b12c769eced15a1a54a87e7521a47">SCLEX_CLWNOCASE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a28cf4c57f311aa17f2dbf0f03761ce99">SCLEX_CMAKE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ab87e1d9e6edd4f9ee6627d837c6152b3">SCLEX_COBOL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ab4dd20651eeac848ec8a1586b3da3c8c">SCLEX_COFFEESCRIPT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a477ce8c2cdaac994e2ec4022e67ee185">SCLEX_CONF</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a62931496707b79f9d5b348aacbd51a6e">SCLEX_CONTAINER</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a504f72d70f56dcb53fb908fe79452138">SCLEX_CPP</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a8dd4142d9043b7b15c235c038a8abf0f">SCLEX_CPPNOCASE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aec034e1adf57a7349ed47f4848bb40c4">SCLEX_CSOUND</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a9c08d490101986eb17aab67a1fb7159f">SCLEX_CSS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0af89b580de6d8a9bffac12bf14b58489d">SCLEX_D</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a254b0148fea4c8f6e170ef09bae369e7">SCLEX_DIFF</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a426868e898ad88df600d7a0cba7ed000">SCLEX_DMAP</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ad9e8188110135d6897add3becb30995f">SCLEX_DMIS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a7ed6ed45706f72a25396e7cea6f179fc">SCLEX_ECL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a07565bccfb3046478e918086c75fd2d0">SCLEX_EDIFACT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a4e7d76804b75f4b89f1b315bfc52972f">SCLEX_EIFFEL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a26b6e157b2a4595977de9c31c44c5f36">SCLEX_EIFFELKW</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aa5ac4a29460ebae1edb850c87473a52c">SCLEX_ERLANG</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a1beef12bbc9c9835a6791267c8fcb10a">SCLEX_ERRORLIST</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a1d30f511ae4cc23f0bc43fd1ca6cda12">SCLEX_ESCRIPT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a94cdec496a332379e7cb47c116c318c6">SCLEX_F77</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0af2efe26c56f871a45383153193e4e9e0">SCLEX_FLAGSHIP</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a44c24c20cdec1b6e482f69ed721a4077">SCLEX_FORTH</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a14a8a752af012a2c9444b5b291108574">SCLEX_FORTRAN</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0af7c6976f83643ba89841ad2eaf62c678">SCLEX_FREEBASIC</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ae20ac3b9f61ea931c3b12e0c462b1dd0">SCLEX_GAP</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ada00900f5ad22e170d494790194dfdcf">SCLEX_GUI4CLI</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ae0b332697a33770b6f1ba537b942a87d">SCLEX_HASKELL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a00ae3e9a26cdc1afac630a91f3b3c7ec">SCLEX_HTML</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a60d40cf6110071d3ae0ff271ea00fca6">SCLEX_IHEX</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ace65638f1fc7df156cb5fd7e13e40b39">SCLEX_INDENT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a1e8ba9f543d28f5470b3284c377caaef">SCLEX_INNOSETUP</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a7fbfc36f6ecf328b50efe1d29fa7be89">SCLEX_JSON</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a67ce3e5d9bdff0bdb44d1a5aff3e69c4">SCLEX_KIX</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ad0cd24eef0f5650d775d4dd05bd82df8">SCLEX_KVIRC</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6b110854fbef28d60067b82faf5ed229">SCLEX_LATEX</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a1b4009430261f11f17487ad843007d04">SCLEX_LISP</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a96b2db4f5bb0191b81dd536b0b8b13e2">SCLEX_LITERATEHASKELL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a95c696054c8774351078fb670b591028">SCLEX_LOT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6f07bc63049dc24bd3afc9e8ebac18ce">SCLEX_LOUT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a9975c914f242664d8225e3692f88ac31">SCLEX_LUA</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aea0c54b674130c1ce336667af1468011">SCLEX_MAGIK</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a990148a08b2f7a1585691ee984876863">SCLEX_MAKEFILE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a735f6a745c132f34910668c6f221dbef">SCLEX_MARKDOWN</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a457d5320bb0deebd765830974964c4ca">SCLEX_MATLAB</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ad030153b23920c60fd4c57a63a1992ad">SCLEX_MAXIMA</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a345b6b3ec51466394faec02ecdb8dc2f">SCLEX_METAPOST</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6d6709e5e960072a7c91b3e5b01a020a">SCLEX_MMIXAL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a030fcaf06a85c39c4f57a828ef354d11">SCLEX_MODULA</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a9cd5b9812fe0fb143740c8a5ac15431a">SCLEX_MSSQL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a705102c40d1227a12afd8da13b43ab00">SCLEX_MYSQL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a8d42ad47b0a03f3a27c047401f3cb080">SCLEX_NIMROD</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a3c92b60cfd0065deb73541166ab412cd">SCLEX_NNCRONTAB</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a592ddfd7bb2d792a42e44a6a04640247">SCLEX_NSIS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a8a264dd8fe734630be400388fac8f588">SCLEX_NULL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a0bfd1f7f3d9ec8b9ea24bb00eb199704">SCLEX_OCTAVE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0acce1c850472e07587f12f668d3b541e5">SCLEX_OPAL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a96e54938b672128818b2c8201833993a">SCLEX_OSCRIPT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aa81b3ab54ed446bd82fd8e47bb716efe">SCLEX_PASCAL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a9ef2274168be1be3f691d59aa142f170">SCLEX_PERL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a4a9d8ecf3ccab78020f78ad4efb510d6">SCLEX_PHP</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a582a3951e713f3e804e312345c120571">SCLEX_PHPSCRIPT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aa9a5c4cac509bcde1ea71e3fcc44c664">SCLEX_PLM</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a0d2bf09afa633715605a3305777dfc83">SCLEX_PO</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0af510951e33b06ef3e995d69c53a94fdc">SCLEX_POV</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0acc275c470d9bfe05754cdf4e42a54741">SCLEX_POWERBASIC</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a36d2c47f406db754feb03b7c530be79f">SCLEX_POWERPRO</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ad32bd9c8bb2d41cfcf26a8ab7605cee8">SCLEX_POWERSHELL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a93e8366c515df085823e843354a8b3cd">SCLEX_PROGRESS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ae51ad6d026758e0fde01d796d72d0815">SCLEX_PROPERTIES</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a053e8c790c607f826c933729ada1a6c2">SCLEX_PS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ae15512b5a294a4d9d87423e256a14874">SCLEX_PUREBASIC</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aaa0d7fa0c2396811b59b5e6ba6c811f1">SCLEX_PYTHON</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a370fc35c7da9d2bdd2ab7088da3d7afe">SCLEX_R</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a0c4f62b4ba53206637593684c27fed7f">SCLEX_REBOL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ab196b886d720b528c06981f3162edcfe">SCLEX_REGISTRY</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ab271a6111144900d2d93de516b1035eb">SCLEX_RUBY</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a7dedcc3f7467a77cf25eff297aad55c1">SCLEX_RUST</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a163ba76db43420149ae4ed4456426d7c">SCLEX_SAS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a12d07a2dd6cc648226ecdbc41ef0d169">SCLEX_SCRIPTOL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a13ce10215a927235a20b5b54739b6442">SCLEX_SMALLTALK</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6b2b7135756e6e63afaab29e1ce69e5d">SCLEX_SML</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a1aa37e96400fba08b571e6f17100bb23">SCLEX_SORCUS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a826c7c9b1bbf5079ff818003bbcdf78e">SCLEX_SPECMAN</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6dde6455441154c518c71d14cbc384e8">SCLEX_SPICE</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0af1a6b060c43736ae87b701da137aaf51">SCLEX_SQL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a833ab9e759135def757131a8bd0196fe">SCLEX_SREC</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aa3c5a8b4e7b66cfd26eeadc24049c268">SCLEX_STATA</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6e8fa194daac20f1860a30910cd77ad2">SCLEX_STTXT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0ad5f7ed0033d057fc7d84a3c80c5640be">SCLEX_TACL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a41b0513b5d681c2e8a5d76ca8ef8752d">SCLEX_TADS3</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a9add9caf532d347948e1c8038ab671e1">SCLEX_TAL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a3d423d890cfc3b987d62d48ede1ec887">SCLEX_TCL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aa9ea73b5b40de75ed54ea356f13a7b47">SCLEX_TCMD</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a7f81fc1ce2e254d399b858b08362e0bf">SCLEX_TEHEX</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aff435fbce318cd18cadeae1be877bd41">SCLEX_TEX</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a1eb34afacf50e022bc6d8d3ac92384d1">SCLEX_TXT2TAGS</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a552be64626f5d82c3d77e27ed485124f">SCLEX_VB</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a0efcb32e5b56db847054c8b5f4778581">SCLEX_VBSCRIPT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0aa419285333430eff62c7d44b79786a3d">SCLEX_VERILOG</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a893d2e14e9c835c6b1e52d43aaf8c577">SCLEX_VHDL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a6505e2018707a20252ef8eefc6b25fb3">SCLEX_VISUALPROLOG</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a1d7fee124bcdc3de441f5051c53eff92">SCLEX_XML</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aa4ab44fd6a7374eb16d07762aa51c7c0a7c021577e03000be86d0acd1ec6c502b">SCLEX_YAML</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a76f793f6e6ce5b6f14b3925e78ea2aa6aaa78aa9b07d1b2afe030262223eba11a">SCMOD_ALT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a76f793f6e6ce5b6f14b3925e78ea2aa6a944d24d92f0c62a4f519936199d74198">SCMOD_CTRL</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a76f793f6e6ce5b6f14b3925e78ea2aa6add02edfef385cd3b3020235bc752eda7">SCMOD_META</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a76f793f6e6ce5b6f14b3925e78ea2aa6a6097124d46dc23dbb028fb340b4aa17e">SCMOD_NORM</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a76f793f6e6ce5b6f14b3925e78ea2aa6ad3e496f1bdcc19e0b87c83c624e9f184">SCMOD_SHIFT</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a76f793f6e6ce5b6f14b3925e78ea2aa6a92a9efa0e26fb75aa9d6584c715aa465">SCMOD_SUPER</a> enum value</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a1719fba80d9e60cf9fce1bb75f304568">SCN_AUTOCCANCELLED</a>()</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aabab23e5653c35dae8a6f144d73c4657">SCN_AUTOCCHARDELETED</a>()</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a41e738411112b8f509e0b49b6fc3e318">SCN_AUTOCCOMPLETED</a>(const char *selection, int position, int ch, int method)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a61c43c53a753272c51c5c5ac14bda136">SCN_AUTOCSELECTION</a>(const char *selection, int position, int ch, int method)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a1ad82492f8015a60dea97f6ebd712d64">SCN_AUTOCSELECTION</a>(const char *selection, int position)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a721a1879cabaa76883ae1a02a34a76e8">SCN_AUTOCSELECTIONCHANGE</a>(const char *selection, int id, int position)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a13f22ec5a59e2e8e97a27ac24967f74d">SCN_CALLTIPCLICK</a>(int direction)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ae8d8fa5d5f063a7c7d37d527f86b5fe8">SCN_CHARADDED</a>(int charadded)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad3ca5787399ed886cb9000c8feab3c08">SCN_DOUBLECLICK</a>(int position, int line, int modifiers)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a9ecd605284870ddbf703cf4c8c995ca6">SCN_DWELLEND</a>(int position, int x, int y)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#adfd788dce5c1a91d1fcd5e6fdd2fca59">SCN_DWELLSTART</a>(int position, int x, int y)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#ae53947625062cec64a212dc68877ddc3">SCN_FOCUSIN</a>()</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a2febc4ea74d45d6a8bc9c758635dd99d">SCN_FOCUSOUT</a>()</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a5eff383e6fa96cbbaba6a2558b076c0b">SCN_HOTSPOTCLICK</a>(int position, int modifiers)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a682cc736272338433efdc86bc936e0e8">SCN_HOTSPOTDOUBLECLICK</a>(int position, int modifiers)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a906faecb0defd2d5a14cac54f8415dcf">SCN_HOTSPOTRELEASECLICK</a>(int position, int modifiers)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#aeec8d7e585e93451307df88ff2fc2b87">SCN_INDICATORCLICK</a>(int position, int modifiers)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a93d1e96c88745ca7f2737602e80dc76a">SCN_INDICATORRELEASE</a>(int position, int modifiers)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#abdae368f2b81955c4927dc6f26fc2c77">SCN_MACRORECORD</a>(unsigned int, unsigned long, void *)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a722a2f16b67ef5f46def6914a6e178c3">SCN_MARGINCLICK</a>(int position, int modifiers, int margin)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a39e90958ae903d2f6198ec0c58f56ed9">SCN_MARGINRIGHTCLICK</a>(int position, int modifiers, int margin)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCN_MODIFIED</b>(int, int, const char *, int, int, int, int, int, int, int) (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#adb5bad7d1dad9ab3fe74adb3e0812969">SCN_MODIFYATTEMPTRO</a>()</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCN_NEEDSHOWN</b>(int, int) (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a94a1cff08b2ef6558d054177fa88ea47">SCN_PAINTED</a>()</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#af3a619a5e59cef000f0b550e809c94de">SCN_SAVEPOINTLEFT</a>()</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a0db8c3ad0764a96f3ccf0fec71de0d26">SCN_SAVEPOINTREACHED</a>()</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a72c0bc1c83fd675714626cd786ca4fb9">SCN_STYLENEEDED</a>(int position)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#ad88db21d86df33667c234d00af1fdf94">SCN_UPDATEUI</a>(int updated)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a42cb45ea05c71180a594e0cc8041c07d">SCN_URIDROPPED</a>(const QUrl &amp;url)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a8225643b25dc6f1dedc48b4a7af4b83d">SCN_USERLISTSELECTION</a>(const char *selection, int id, int ch, int method, int position)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a9208cc0aaf2e0a32239924fc6d0b67b7">SCN_USERLISTSELECTION</a>(const char *selection, int id, int ch, int method)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a0045744463119646a5fe33ecc4d104fb">SCN_USERLISTSELECTION</a>(const char *selection, int id)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCN_ZOOM</b>() (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">signal</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>scrollContentsBy</b>(int dx, int dy) (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCTD_LONGARROW</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCTD_STRIKEOUT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCVS_NONE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCVS_NOWRAPLINESTART</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCVS_RECTANGULARSELECTION</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCVS_USERACCESSIBLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCWS_INVISIBLE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCWS_VISIBLEAFTERINDENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>SCWS_VISIBLEALWAYS</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>SCWS_VISIBLEONLYININDENT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a8820ab8d7563bd7ed24ce6384846079e">SendScintilla</a>(unsigned int msg, unsigned long wParam=0, long lParam=0) const</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#adf01efe3951a727dab9c7a1c35d29e0f">SendScintilla</a>(unsigned int msg, unsigned long wParam, void *lParam) const</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aab2b37b2f67991e9c083d9412cba2264">SendScintilla</a>(unsigned int msg, uintptr_t wParam, const char *lParam) const</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#adb45fb04c1ad8c6459fea750d8788584">SendScintilla</a>(unsigned int msg, const char *lParam) const</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a9fa4dc539be7b199e91d6ff0f83e5f8d">SendScintilla</a>(unsigned int msg, const char *wParam, const char *lParam) const</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a94a66b0c8459f5a407eef6783cd6d80b">SendScintilla</a>(unsigned int msg, long wParam) const</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#add035b6176dbe36b3c975f05573c0e61">SendScintilla</a>(unsigned int msg, int wParam) const</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a6c892370b4ee3afd2eef080ee8c25fde">SendScintilla</a>(unsigned int msg, long cpMin, long cpMax, char *lpstrText) const</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a1d84e61ba19c5177386ba30ba512345f">SendScintilla</a>(unsigned int msg, unsigned long wParam, const QColor &amp;col) const</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#af935c2c5d8eeb3aeb25ba9b48539f879">SendScintilla</a>(unsigned int msg, const QColor &amp;col) const</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#aacfd4923492644933902b278efea1787">SendScintilla</a>(unsigned int msg, unsigned long wParam, QPainter *hdc, const QRect &amp;rc, long cpMin, long cpMax) const</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a2cef89549882e03a6290af8cbbf1a3ce">SendScintilla</a>(unsigned int msg, unsigned long wParam, const QPixmap &amp;lParam) const</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a56542fe5a9e5790eab29936b73ef0fa3">SendScintilla</a>(unsigned int msg, unsigned long wParam, const QImage &amp;lParam) const</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classQsciScintillaBase.html#a5f140c587d361cf8539814d820d680f4">SendScintillaPtrResult</a>(unsigned int msg) const</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>setScrollBars</b>() (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>STYLE_BRACEBAD</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>STYLE_BRACELIGHT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>STYLE_CALLTIP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>STYLE_CONTROLCHAR</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>STYLE_DEFAULT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>STYLE_FOLDDISPLAYTEXT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>STYLE_INDENTGUIDE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>STYLE_LASTPREDEFINED</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>STYLE_LINENUMBER</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>STYLE_MAX</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>textAsBytes</b>(const QString &amp;text) const (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a7e1e146787204eba48aa5376287de41f">toMimeData</a>(const QByteArray &amp;text, bool rectangular) const</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>UNDO_MAY_COALESCE</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>VISIBLE_SLOP</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>VISIBLE_STRICT</b> enum value (defined in <a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a>)</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classQsciScintillaBase.html#a965242ee4392b838cc182c823de54ff6">~QsciScintillaBase</a>()</td><td class="entry"><a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
