<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciCommandSet Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classQsciCommandSet-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciCommandSet Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscicommandset.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a44fd78a640f59309862d868d04f34e49"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciCommandSet.html#a44fd78a640f59309862d868d04f34e49">readSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td></tr>
<tr class="separator:a44fd78a640f59309862d868d04f34e49"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7933fbb5a8b5cb234c4e48b472adc4a3"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciCommandSet.html#a7933fbb5a8b5cb234c4e48b472adc4a3">writeSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td></tr>
<tr class="separator:a7933fbb5a8b5cb234c4e48b472adc4a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f46a38f9fd309442aacfb7ad2b87143"><td class="memItemLeft" align="right" valign="top"><a id="a7f46a38f9fd309442aacfb7ad2b87143"></a>
QList&lt; <a class="el" href="classQsciCommand.html">QsciCommand</a> * &gt; &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciCommandSet.html#a7f46a38f9fd309442aacfb7ad2b87143">commands</a> ()</td></tr>
<tr class="separator:a7f46a38f9fd309442aacfb7ad2b87143"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7a15e4a269b804a830c881edda1563f7"><td class="memItemLeft" align="right" valign="top"><a id="a7a15e4a269b804a830c881edda1563f7"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciCommandSet.html#a7a15e4a269b804a830c881edda1563f7">clearKeys</a> ()</td></tr>
<tr class="separator:a7a15e4a269b804a830c881edda1563f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af244d8499c10c569b9924c25af17655a"><td class="memItemLeft" align="right" valign="top"><a id="af244d8499c10c569b9924c25af17655a"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciCommandSet.html#af244d8499c10c569b9924c25af17655a">clearAlternateKeys</a> ()</td></tr>
<tr class="separator:af244d8499c10c569b9924c25af17655a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e5f630a0a3b65b2a732531b7eaac196"><td class="memItemLeft" align="right" valign="top"><a id="a2e5f630a0a3b65b2a732531b7eaac196"></a>
<a class="el" href="classQsciCommand.html">QsciCommand</a> *&#160;</td><td class="memItemRight" valign="bottom"><b>boundTo</b> (int key) const</td></tr>
<tr class="separator:a2e5f630a0a3b65b2a732531b7eaac196"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab706e66e604cabb73695a43b8137b29e"><td class="memItemLeft" align="right" valign="top"><a id="ab706e66e604cabb73695a43b8137b29e"></a>
<a class="el" href="classQsciCommand.html">QsciCommand</a> *&#160;</td><td class="memItemRight" valign="bottom"><b>find</b> (<a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7">QsciCommand::Command</a> command) const</td></tr>
<tr class="separator:ab706e66e604cabb73695a43b8137b29e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciCommandSet.html" title="The QsciCommandSet class represents the set of all internal editor commands that may have keys bound.">QsciCommandSet</a> class represents the set of all internal editor commands that may have keys bound. </p>
<p>Methods are provided to access the individual commands and to read and write the current bindings from and to settings files. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a44fd78a640f59309862d868d04f34e49"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a44fd78a640f59309862d868d04f34e49">&#9670;&nbsp;</a></span>readSettings()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciCommandSet::readSettings </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>prefix</em> = <code>&quot;/Scintilla&quot;</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The key bindings for each command in the set are read from the settings <em>qs</em>. <em>prefix</em> is prepended to the key of each entry. true is returned if there was no error.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciCommandSet.html#a7933fbb5a8b5cb234c4e48b472adc4a3">writeSettings()</a> </dd></dl>

</div>
</div>
<a id="a7933fbb5a8b5cb234c4e48b472adc4a3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7933fbb5a8b5cb234c4e48b472adc4a3">&#9670;&nbsp;</a></span>writeSettings()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciCommandSet::writeSettings </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>prefix</em> = <code>&quot;/Scintilla&quot;</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The key bindings for each command in the set are written to the settings <em>qs</em>. <em>prefix</em> is prepended to the key of each entry. true is returned if there was no error.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciCommandSet.html#a44fd78a640f59309862d868d04f34e49">readSettings()</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
