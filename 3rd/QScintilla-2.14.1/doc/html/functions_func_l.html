<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_l"></a>- l -</h3><ul>
<li>language()
: <a class="el" href="classQsciLexer.html#a8a3adc7b5c8926e097e6be4340bee920">QsciLexer</a>
, <a class="el" href="classQsciLexerAVS.html#a1ef24398e95c23a8b3c858179e5eb564">QsciLexerAVS</a>
, <a class="el" href="classQsciLexerBash.html#a1bc505b1b0f41472062f86b205ea2860">QsciLexerBash</a>
, <a class="el" href="classQsciLexerBatch.html#a18341dcb06d1b74269ed1f33c002b2a9">QsciLexerBatch</a>
, <a class="el" href="classQsciLexerCMake.html#a0aa2f537e70f47e6a3e1bcf6d383a480">QsciLexerCMake</a>
, <a class="el" href="classQsciLexerCoffeeScript.html#a126d81ec982782507eafae1af5d0d856">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#a7c5dafabba34ff3e6120d9f3606cade0">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerCSharp.html#a92e6554430736b20b147b7290d4bfe16">QsciLexerCSharp</a>
, <a class="el" href="classQsciLexerCSS.html#a2c29f0bbe4d09c159040b5676c8143d2">QsciLexerCSS</a>
, <a class="el" href="classQsciLexerD.html#a667febcf6234a15b7ca6d4ddbfb97bc6">QsciLexerD</a>
, <a class="el" href="classQsciLexerDiff.html#a795af727d45974e6581ed01bf812b63e">QsciLexerDiff</a>
, <a class="el" href="classQsciLexerEDIFACT.html#a77024f83fb756608060b105d3f21ae34">QsciLexerEDIFACT</a>
, <a class="el" href="classQsciLexerFortran77.html#ae3ef35311f24a24300140512dd005f54">QsciLexerFortran77</a>
, <a class="el" href="classQsciLexerFortran.html#a83d7d8209efca06d10870607c9db3c72">QsciLexerFortran</a>
, <a class="el" href="classQsciLexerHTML.html#a336165187c8ab4cc5e51912033316943">QsciLexerHTML</a>
, <a class="el" href="classQsciLexerIDL.html#a56afa4275e743eeff3dc693d9da85fd3">QsciLexerIDL</a>
, <a class="el" href="classQsciLexerIntelHex.html#acb62449f90ad9ebb39a3c261bbe1e3ca">QsciLexerIntelHex</a>
, <a class="el" href="classQsciLexerJava.html#af0f0dd1756ceb60bd8f404f3b48f470c">QsciLexerJava</a>
, <a class="el" href="classQsciLexerJavaScript.html#aa7bd1f345699cc97fac25cf29ae98a4e">QsciLexerJavaScript</a>
, <a class="el" href="classQsciLexerJSON.html#a7a2271db1a39037a429faaa5ff8e399f">QsciLexerJSON</a>
, <a class="el" href="classQsciLexerLua.html#a942c993effc83d0dedec2fc20d8a741f">QsciLexerLua</a>
, <a class="el" href="classQsciLexerMakefile.html#a561482313e4c6597b8c4627ec38e4d54">QsciLexerMakefile</a>
, <a class="el" href="classQsciLexerMarkdown.html#a7a06bdb17ebde731368ec204404ed0ee">QsciLexerMarkdown</a>
, <a class="el" href="classQsciLexerMASM.html#ac9f1def4274bef684308ca40cd1c997b">QsciLexerMASM</a>
, <a class="el" href="classQsciLexerMatlab.html#a1b26669dd868d97d8a04837aada5549f">QsciLexerMatlab</a>
, <a class="el" href="classQsciLexerNASM.html#a41bba5ae9f9e264acf1a4e9ef1e443f6">QsciLexerNASM</a>
, <a class="el" href="classQsciLexerOctave.html#acca6b44f3f90599d119fb05f375cb2b8">QsciLexerOctave</a>
, <a class="el" href="classQsciLexerPascal.html#a072c10d35abc0e56e09806eeb78ab66f">QsciLexerPascal</a>
, <a class="el" href="classQsciLexerPerl.html#a16fb82e08452dc260bdda610817c79ea">QsciLexerPerl</a>
, <a class="el" href="classQsciLexerPO.html#ace592f4a2d86db6be6c6f363227c00ee">QsciLexerPO</a>
, <a class="el" href="classQsciLexerPostScript.html#ab2d6a4d13e15769bf1110012b491ad90">QsciLexerPostScript</a>
, <a class="el" href="classQsciLexerPOV.html#a02880268227d380ef25a72af2605ef0f">QsciLexerPOV</a>
, <a class="el" href="classQsciLexerProperties.html#a5e15c53d398d9d7e9ef7e0df41bc3f62">QsciLexerProperties</a>
, <a class="el" href="classQsciLexerPython.html#ae96690293b8128bea9cedf9b55b92ad6">QsciLexerPython</a>
, <a class="el" href="classQsciLexerRuby.html#a700754468352f673157d08d4ff222e79">QsciLexerRuby</a>
, <a class="el" href="classQsciLexerSpice.html#ae41f7a78d82f09b4d5176ec2a709ac67">QsciLexerSpice</a>
, <a class="el" href="classQsciLexerSQL.html#a0b1959541108a437dcb0b104a46f1444">QsciLexerSQL</a>
, <a class="el" href="classQsciLexerSRec.html#a3e37c3d7527369901bcb28bba3b823e6">QsciLexerSRec</a>
, <a class="el" href="classQsciLexerTCL.html#a4a13fa4667146e0dca9d8c15255280a9">QsciLexerTCL</a>
, <a class="el" href="classQsciLexerTekHex.html#ad9666a08ae0b9143a1d3f155ce563819">QsciLexerTekHex</a>
, <a class="el" href="classQsciLexerTeX.html#ad94fbbd156020166afddb8a0a55eba6f">QsciLexerTeX</a>
, <a class="el" href="classQsciLexerVerilog.html#a79e3ff22e68d54f640bd2f7747a7a193">QsciLexerVerilog</a>
, <a class="el" href="classQsciLexerVHDL.html#a2a8cd041dea81adb54a869c17ee4c8ba">QsciLexerVHDL</a>
, <a class="el" href="classQsciLexerXML.html#a801d7cef474dcf23d93e2f2f53034abe">QsciLexerXML</a>
, <a class="el" href="classQsciLexerYAML.html#a0cfceb4470cde4184e76076ac34dea29">QsciLexerYAML</a>
</li>
<li>length()
: <a class="el" href="classQsciScintilla.html#aedf7c8532be11bcfb8d38411bea88792">QsciScintilla</a>
</li>
<li>LessLanguage()
: <a class="el" href="classQsciLexerCSS.html#a26df830be43cb0ca067b5e0ad037171a">QsciLexerCSS</a>
</li>
<li>level()
: <a class="el" href="classQsciLexerPostScript.html#a78834f5080f50c01ba5ec1094114bf40">QsciLexerPostScript</a>
</li>
<li>lexer()
: <a class="el" href="classQsciAbstractAPIs.html#a90452ab6f4d40314ec519913f9e78ccc">QsciAbstractAPIs</a>
, <a class="el" href="classQsciLexer.html#ac7c40b97187e23ab85f6d95113f91b39">QsciLexer</a>
, <a class="el" href="classQsciLexerAVS.html#af462fb11c1cb7d3a5d99cc66d2a4bc6b">QsciLexerAVS</a>
, <a class="el" href="classQsciLexerBash.html#a21f1bb849edbfbc0cf58bc55cc75e8a3">QsciLexerBash</a>
, <a class="el" href="classQsciLexerBatch.html#acf33e60d28291147562860b824ccd74d">QsciLexerBatch</a>
, <a class="el" href="classQsciLexerCMake.html#a99fc9415c35eeef2b0f45f066101736b">QsciLexerCMake</a>
, <a class="el" href="classQsciLexerCoffeeScript.html#aac009a767572be4b4489a0613611cbdb">QsciLexerCoffeeScript</a>
, <a class="el" href="classQsciLexerCPP.html#a3f7076535f370759450ec1243088c7f1">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerCSS.html#a1fa70c8e86dd88d34508fc652d30f3f7">QsciLexerCSS</a>
, <a class="el" href="classQsciLexerD.html#a62032a66c22767af46af4611fb672cb3">QsciLexerD</a>
, <a class="el" href="classQsciLexerDiff.html#aec71281020211f0e693143520f232079">QsciLexerDiff</a>
, <a class="el" href="classQsciLexerEDIFACT.html#a55d08e564f88f40f5167c52bd686b61c">QsciLexerEDIFACT</a>
, <a class="el" href="classQsciLexerFortran77.html#a1ef7534c295a6323be9176fca79b1cbe">QsciLexerFortran77</a>
, <a class="el" href="classQsciLexerFortran.html#ad18b58e4e78b74f1c1cc0db18a2d74ca">QsciLexerFortran</a>
, <a class="el" href="classQsciLexerHTML.html#a48e7f3a456fcb347ee96a2c6a1f07231">QsciLexerHTML</a>
, <a class="el" href="classQsciLexerIntelHex.html#ac555cadb9fbc0dec91f7630d019f1100">QsciLexerIntelHex</a>
, <a class="el" href="classQsciLexerJSON.html#a04a2eaa1d93a2266bd170d392b70860b">QsciLexerJSON</a>
, <a class="el" href="classQsciLexerLua.html#a8124ec8b5b96d95bb225cbb4e95f55cb">QsciLexerLua</a>
, <a class="el" href="classQsciLexerMakefile.html#a42e28c95e4f32374ffb7b47a85239d34">QsciLexerMakefile</a>
, <a class="el" href="classQsciLexerMarkdown.html#af912a1a568b342c99f70fab70d89b178">QsciLexerMarkdown</a>
, <a class="el" href="classQsciLexerMASM.html#a4b6d26169eca1609b3fd1ef979cb6e8f">QsciLexerMASM</a>
, <a class="el" href="classQsciLexerMatlab.html#a62234f5c4dfbeec23fd43dd6651d65e4">QsciLexerMatlab</a>
, <a class="el" href="classQsciLexerNASM.html#a314bdc56c9b06fe0a99b484b142c541e">QsciLexerNASM</a>
, <a class="el" href="classQsciLexerOctave.html#aa39859b74adb5cca0470d488186eb6af">QsciLexerOctave</a>
, <a class="el" href="classQsciLexerPascal.html#aebc02afb8158d445c4369efa287cc2ac">QsciLexerPascal</a>
, <a class="el" href="classQsciLexerPerl.html#aae9e42584c6466a8b859d56218eaf28c">QsciLexerPerl</a>
, <a class="el" href="classQsciLexerPO.html#a8eb17be2a61d63249564be87b7d777d8">QsciLexerPO</a>
, <a class="el" href="classQsciLexerPostScript.html#a8f6156730e68c15fb63e120c53ce7832">QsciLexerPostScript</a>
, <a class="el" href="classQsciLexerPOV.html#af41ceced7bf5eb12aefb77f81240b1eb">QsciLexerPOV</a>
, <a class="el" href="classQsciLexerProperties.html#a76890c95abff2bb6f5eebe7a2cb5a0a3">QsciLexerProperties</a>
, <a class="el" href="classQsciLexerPython.html#a9fe6e18dbb7ef4cad7f370286d7db0b7">QsciLexerPython</a>
, <a class="el" href="classQsciLexerRuby.html#a14f1638b2f668fb7d98791cda719f8a0">QsciLexerRuby</a>
, <a class="el" href="classQsciLexerSpice.html#a0e389df0054dbbafafe6196c71d50738">QsciLexerSpice</a>
, <a class="el" href="classQsciLexerSQL.html#abd8d636e4717ed65e4ea77eca3c28df1">QsciLexerSQL</a>
, <a class="el" href="classQsciLexerSRec.html#ab53b28ee54f0463f5e5d499d86e81462">QsciLexerSRec</a>
, <a class="el" href="classQsciLexerTCL.html#a15ec40b8e6b208521e08d44400eb56f8">QsciLexerTCL</a>
, <a class="el" href="classQsciLexerTekHex.html#abf8cd7722d9bd5f7d5f7b03987ba15b8">QsciLexerTekHex</a>
, <a class="el" href="classQsciLexerTeX.html#a409c5a8e561b153aca122ad5e0bedc82">QsciLexerTeX</a>
, <a class="el" href="classQsciLexerVerilog.html#abc3666027fe7f0b8ae78ee34e3276069">QsciLexerVerilog</a>
, <a class="el" href="classQsciLexerVHDL.html#abf1a8dc25c7bd5d272c119d3c3e9e369">QsciLexerVHDL</a>
, <a class="el" href="classQsciLexerXML.html#ae0bb41012d9d98366b435f9a534ce7a1">QsciLexerXML</a>
, <a class="el" href="classQsciLexerYAML.html#ab946b8c9f34872b69c31e6e9cd0b0e57">QsciLexerYAML</a>
, <a class="el" href="classQsciScintilla.html#aff5e2abd10fd64752adc1a89fc626e1e">QsciScintilla</a>
</li>
<li>lexerId()
: <a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">QsciLexer</a>
</li>
<li>lineAt()
: <a class="el" href="classQsciScintilla.html#a8a8a5b9bcb9df18089b9fe2650e701db">QsciScintilla</a>
</li>
<li>lineIndexFromPosition()
: <a class="el" href="classQsciScintilla.html#ad309f6cb931b47d67e67a59b3a66ea84">QsciScintilla</a>
</li>
<li>lineLength()
: <a class="el" href="classQsciScintilla.html#ab5ecfa6bee9e78e5306986367e1194af">QsciScintilla</a>
</li>
<li>lines()
: <a class="el" href="classQsciScintilla.html#a85f97649b701717e65b2390d57f5ad03">QsciScintilla</a>
</li>
<li>linesChanged()
: <a class="el" href="classQsciScintilla.html#a9da0038950253b116ecd3863a4f63928">QsciScintilla</a>
</li>
<li>load()
: <a class="el" href="classQsciAPIs.html#a3084b749e4eb1c741fc1004e8a84a631">QsciAPIs</a>
, <a class="el" href="classQsciMacro.html#a1c342b5db46e12a8d73567fac9959543">QsciMacro</a>
</li>
<li>loadPrepared()
: <a class="el" href="classQsciAPIs.html#af42a26a050bfeb4249d35ab61567ea9e">QsciAPIs</a>
</li>
<li>QsciLexer()
: <a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a>
</li>
<li>QsciLexerAsm()
: <a class="el" href="classQsciLexerAsm.html#a5f3257db4289b4581958fd8f36b7fe90">QsciLexerAsm</a>
</li>
<li>QsciLexerAVS()
: <a class="el" href="classQsciLexerAVS.html#a15390924acb08542856527f5d0101dab">QsciLexerAVS</a>
</li>
<li>QsciLexerBash()
: <a class="el" href="classQsciLexerBash.html#a1ba5b1e505b4f6fe7d7b12ce69dee9a8">QsciLexerBash</a>
</li>
<li>QsciLexerBatch()
: <a class="el" href="classQsciLexerBatch.html#a9f0ad3d0c52cc81d43a0b733558e2392">QsciLexerBatch</a>
</li>
<li>QsciLexerCMake()
: <a class="el" href="classQsciLexerCMake.html#af84de1838a68d08ef99be3aa666dc09f">QsciLexerCMake</a>
</li>
<li>QsciLexerCoffeeScript()
: <a class="el" href="classQsciLexerCoffeeScript.html#a73e71780818247bf678616a25cd13e90">QsciLexerCoffeeScript</a>
</li>
<li>QsciLexerCPP()
: <a class="el" href="classQsciLexerCPP.html#ae1451be7e7c6a57f28f361c72eb68a5f">QsciLexerCPP</a>
</li>
<li>QsciLexerCSharp()
: <a class="el" href="classQsciLexerCSharp.html#a897d4004ebe6faefcb97e27713e4a8cc">QsciLexerCSharp</a>
</li>
<li>QsciLexerCSS()
: <a class="el" href="classQsciLexerCSS.html#acbf55e58ad04813101573146ecc43c67">QsciLexerCSS</a>
</li>
<li>QsciLexerCustom()
: <a class="el" href="classQsciLexerCustom.html#a8dbdaca7dffe587e442d09f5b780fab5">QsciLexerCustom</a>
</li>
<li>QsciLexerD()
: <a class="el" href="classQsciLexerD.html#a8b64cf1677896ea7966338b3f10be14b">QsciLexerD</a>
</li>
<li>QsciLexerDiff()
: <a class="el" href="classQsciLexerDiff.html#af1c70ba1dfa8ccf5fe2106069041cd5e">QsciLexerDiff</a>
</li>
<li>QsciLexerEDIFACT()
: <a class="el" href="classQsciLexerEDIFACT.html#aec9604240be31cf15709b0c0f2942d6e">QsciLexerEDIFACT</a>
</li>
<li>QsciLexerFortran()
: <a class="el" href="classQsciLexerFortran.html#a6e896d1a75f43c0e021326a29a07be67">QsciLexerFortran</a>
</li>
<li>QsciLexerFortran77()
: <a class="el" href="classQsciLexerFortran77.html#a2784362f75607dc575d42a3ddf43bcf9">QsciLexerFortran77</a>
</li>
<li>QsciLexerHex()
: <a class="el" href="classQsciLexerHex.html#a6397d2675439df63b7d1e789185ad224">QsciLexerHex</a>
</li>
<li>QsciLexerHTML()
: <a class="el" href="classQsciLexerHTML.html#a4c5ae7bc7d27946b1b07b940ef30a093">QsciLexerHTML</a>
</li>
<li>QsciLexerIDL()
: <a class="el" href="classQsciLexerIDL.html#af929c0ded0d4d822d7b31d00103262de">QsciLexerIDL</a>
</li>
<li>QsciLexerIntelHex()
: <a class="el" href="classQsciLexerIntelHex.html#adb2e1ab6e6bf1dc17b8d027ba57dbd16">QsciLexerIntelHex</a>
</li>
<li>QsciLexerJava()
: <a class="el" href="classQsciLexerJava.html#abaa737931800774e8067765d6cdc9a5d">QsciLexerJava</a>
</li>
<li>QsciLexerJavaScript()
: <a class="el" href="classQsciLexerJavaScript.html#a94cbc22361a55fe0681ad7fe5425dfb5">QsciLexerJavaScript</a>
</li>
<li>QsciLexerJSON()
: <a class="el" href="classQsciLexerJSON.html#a287cf2adecde291abab55095227864a9">QsciLexerJSON</a>
</li>
<li>QsciLexerLua()
: <a class="el" href="classQsciLexerLua.html#a8932efc560175dc70a88e23b8136bb8f">QsciLexerLua</a>
</li>
<li>QsciLexerMakefile()
: <a class="el" href="classQsciLexerMakefile.html#a4568ee117191969976b674227b16c860">QsciLexerMakefile</a>
</li>
<li>QsciLexerMarkdown()
: <a class="el" href="classQsciLexerMarkdown.html#aeffb57391fe593ab01e6f257f95ad2f6">QsciLexerMarkdown</a>
</li>
<li>QsciLexerMASM()
: <a class="el" href="classQsciLexerMASM.html#a00763c84274386e27be1994cc3404fd4">QsciLexerMASM</a>
</li>
<li>QsciLexerMatlab()
: <a class="el" href="classQsciLexerMatlab.html#a382aa49629299c9694d3b225bace0c16">QsciLexerMatlab</a>
</li>
<li>QsciLexerNASM()
: <a class="el" href="classQsciLexerNASM.html#a252023a8e12a490e52c5bb6fd9a37556">QsciLexerNASM</a>
</li>
<li>QsciLexerOctave()
: <a class="el" href="classQsciLexerOctave.html#aca3644f3fed3a83716d794cca822549a">QsciLexerOctave</a>
</li>
<li>QsciLexerPascal()
: <a class="el" href="classQsciLexerPascal.html#a9688ce1d302666e492900d3cdfcbbaab">QsciLexerPascal</a>
</li>
<li>QsciLexerPerl()
: <a class="el" href="classQsciLexerPerl.html#a36359d3e1cb6037b561f95fccf16881e">QsciLexerPerl</a>
</li>
<li>QsciLexerPO()
: <a class="el" href="classQsciLexerPO.html#a4d8011ef7e9d6401597d3a4012a809a7">QsciLexerPO</a>
</li>
<li>QsciLexerPostScript()
: <a class="el" href="classQsciLexerPostScript.html#a7670a50d4dce21461de96844235b4242">QsciLexerPostScript</a>
</li>
<li>QsciLexerPOV()
: <a class="el" href="classQsciLexerPOV.html#a4f286fb01fbf71a895a6a6ca2424b9c5">QsciLexerPOV</a>
</li>
<li>QsciLexerProperties()
: <a class="el" href="classQsciLexerProperties.html#a81437ae22cb610108f29d8d367ce6faa">QsciLexerProperties</a>
</li>
<li>QsciLexerPython()
: <a class="el" href="classQsciLexerPython.html#a158b80fd7ee649cbb618b1df33491bab">QsciLexerPython</a>
</li>
<li>QsciLexerRuby()
: <a class="el" href="classQsciLexerRuby.html#aa3bb000261e4752d89e06afe69d665f0">QsciLexerRuby</a>
</li>
<li>QsciLexerSpice()
: <a class="el" href="classQsciLexerSpice.html#a7d38d89680d55b7f77463e67634f84be">QsciLexerSpice</a>
</li>
<li>QsciLexerSQL()
: <a class="el" href="classQsciLexerSQL.html#ab86225b96219799a77f77600f145042a">QsciLexerSQL</a>
</li>
<li>QsciLexerSRec()
: <a class="el" href="classQsciLexerSRec.html#ad149f20b5fafc86c33706857ac0248f5">QsciLexerSRec</a>
</li>
<li>QsciLexerTCL()
: <a class="el" href="classQsciLexerTCL.html#a6a108be4899959ffcb262f59de538964">QsciLexerTCL</a>
</li>
<li>QsciLexerTekHex()
: <a class="el" href="classQsciLexerTekHex.html#a5844e0809729932ab2e3702990b756a4">QsciLexerTekHex</a>
</li>
<li>QsciLexerTeX()
: <a class="el" href="classQsciLexerTeX.html#a4bf7cce95e65755a221fd75bd731a3cd">QsciLexerTeX</a>
</li>
<li>QsciLexerVerilog()
: <a class="el" href="classQsciLexerVerilog.html#a3360bca839d08fdd2acf546b19b2fddd">QsciLexerVerilog</a>
</li>
<li>QsciLexerVHDL()
: <a class="el" href="classQsciLexerVHDL.html#a2260bd1206a91b7f9487e9ffe366732f">QsciLexerVHDL</a>
</li>
<li>QsciLexerXML()
: <a class="el" href="classQsciLexerXML.html#a9fc5fef8f86ef0f1162a18ca4cc88aa1">QsciLexerXML</a>
</li>
<li>QsciLexerYAML()
: <a class="el" href="classQsciLexerYAML.html#a5e1785141798faf81dcff567b8df651f">QsciLexerYAML</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
