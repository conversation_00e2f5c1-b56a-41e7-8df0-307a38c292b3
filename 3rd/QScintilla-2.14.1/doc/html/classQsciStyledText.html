<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciStyledText Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classQsciStyledText-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciStyledText Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscistyledtext.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a72dbd9d847a577fe5c438d1582920887"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyledText.html#a72dbd9d847a577fe5c438d1582920887">QsciStyledText</a> (const QString &amp;<a class="el" href="classQsciStyledText.html#a526eff4f40349af1913dd4cfa3464131">text</a>, int <a class="el" href="classQsciStyledText.html#a6a5f837ca80d54322b70aa4b8465afa1">style</a>)</td></tr>
<tr class="separator:a72dbd9d847a577fe5c438d1582920887"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9f84f97932caaa6481cea79f48c70647"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyledText.html#a9f84f97932caaa6481cea79f48c70647">QsciStyledText</a> (const QString &amp;<a class="el" href="classQsciStyledText.html#a526eff4f40349af1913dd4cfa3464131">text</a>, const <a class="el" href="classQsciStyle.html">QsciStyle</a> &amp;<a class="el" href="classQsciStyledText.html#a6a5f837ca80d54322b70aa4b8465afa1">style</a>)</td></tr>
<tr class="separator:a9f84f97932caaa6481cea79f48c70647"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a448f366f55c995dfd5edc566fd4724c7"><td class="memItemLeft" align="right" valign="top"><a id="a448f366f55c995dfd5edc566fd4724c7"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>apply</b> (<a class="el" href="classQsciScintillaBase.html">QsciScintillaBase</a> *sci) const</td></tr>
<tr class="separator:a448f366f55c995dfd5edc566fd4724c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a526eff4f40349af1913dd4cfa3464131"><td class="memItemLeft" align="right" valign="top"><a id="a526eff4f40349af1913dd4cfa3464131"></a>
const QString &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyledText.html#a526eff4f40349af1913dd4cfa3464131">text</a> () const</td></tr>
<tr class="separator:a526eff4f40349af1913dd4cfa3464131"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6a5f837ca80d54322b70aa4b8465afa1"><td class="memItemLeft" align="right" valign="top"><a id="a6a5f837ca80d54322b70aa4b8465afa1"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciStyledText.html#a6a5f837ca80d54322b70aa4b8465afa1">style</a> () const</td></tr>
<tr class="separator:a6a5f837ca80d54322b70aa4b8465afa1"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciStyledText.html" title="The QsciStyledText class is a container for a piece of text and the style used to display the text.">QsciStyledText</a> class is a container for a piece of text and the style used to display the text. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a72dbd9d847a577fe5c438d1582920887"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a72dbd9d847a577fe5c438d1582920887">&#9670;&nbsp;</a></span>QsciStyledText() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciStyledText::QsciStyledText </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>text</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Constructs a <a class="el" href="classQsciStyledText.html" title="The QsciStyledText class is a container for a piece of text and the style used to display the text.">QsciStyledText</a> instance for text <em>text</em> and style number <em>style</em>. </p>

</div>
</div>
<a id="a9f84f97932caaa6481cea79f48c70647"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9f84f97932caaa6481cea79f48c70647">&#9670;&nbsp;</a></span>QsciStyledText() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciStyledText::QsciStyledText </td>
          <td>(</td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>text</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="classQsciStyle.html">QsciStyle</a> &amp;&#160;</td>
          <td class="paramname"><em>style</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Constructs a <a class="el" href="classQsciStyledText.html" title="The QsciStyledText class is a container for a piece of text and the style used to display the text.">QsciStyledText</a> instance for text <em>text</em> and style <em>style</em>. </p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
