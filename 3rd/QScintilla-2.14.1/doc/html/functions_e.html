<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_e"></a>- e -</h3><ul>
<li>EdgeBackground
: <a class="el" href="classQsciScintilla.html#a40b8ec37e068b12d9c83ee497929a00ea46d9f957fe5b30a681ebaecc3ba159d5">QsciScintilla</a>
</li>
<li>edgeColor()
: <a class="el" href="classQsciScintilla.html#aee2887fadd0ddac9cee9dcdaee8d8696">QsciScintilla</a>
</li>
<li>edgeColumn()
: <a class="el" href="classQsciScintilla.html#a044b772f07f8d5218170a11db05cc3d5">QsciScintilla</a>
</li>
<li>EdgeLine
: <a class="el" href="classQsciScintilla.html#a40b8ec37e068b12d9c83ee497929a00ea44fdad06b183b02f5d54a3fecd5ac98d">QsciScintilla</a>
</li>
<li>EdgeMode
: <a class="el" href="classQsciScintilla.html#a40b8ec37e068b12d9c83ee497929a00e">QsciScintilla</a>
</li>
<li>edgeMode()
: <a class="el" href="classQsciScintilla.html#a6fca34c3778ad8b4c067d577816ebc2b">QsciScintilla</a>
</li>
<li>EdgeMultipleLines
: <a class="el" href="classQsciScintilla.html#a40b8ec37e068b12d9c83ee497929a00ea2a310e227fd42221c2ad9b81fbdb7654">QsciScintilla</a>
</li>
<li>EdgeNone
: <a class="el" href="classQsciScintilla.html#a40b8ec37e068b12d9c83ee497929a00ead6c5f91845329bb32ff37e3f1325078c">QsciScintilla</a>
</li>
<li>editor()
: <a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">QsciLexer</a>
</li>
<li>EditToggleOvertype
: <a class="el" href="classQsciCommand.html#aeaecb067c0834ba132e204a09dd942c7ab6c8c98a6027e8a88783f18dbca2bdf4">QsciCommand</a>
</li>
<li>ElementSeparator
: <a class="el" href="classQsciLexerEDIFACT.html#a5b0c61756ec9e9987be5d83bdeb18d88ad3349f5ca362b8086feaee6465adaf76">QsciLexerEDIFACT</a>
</li>
<li>EmphasisAsterisks
: <a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a3b86f1f637bdd5a9f894bcf8d94039c4">QsciLexerMarkdown</a>
</li>
<li>EmphasisUnderscores
: <a class="el" href="classQsciLexerMarkdown.html#ad09694087faec9ff4f49ff5cc3388e54a6d496317b63aabf2f94b7abd0681ffc7">QsciLexerMarkdown</a>
</li>
<li>endRecording()
: <a class="el" href="classQsciMacro.html#a783f17e12ca844655568b5718aa26a35">QsciMacro</a>
</li>
<li>endUndoAction()
: <a class="el" href="classQsciScintilla.html#a5d08214cc5bab0a03b383809f5c626f7">QsciScintilla</a>
</li>
<li>ensureCursorVisible()
: <a class="el" href="classQsciScintilla.html#aa85349be1012a11e4198949c5537a6b2">QsciScintilla</a>
</li>
<li>ensureLineVisible()
: <a class="el" href="classQsciScintilla.html#abe7fcae4d84483ecd934c60bfdaee4a8">QsciScintilla</a>
</li>
<li>Entity
: <a class="el" href="classQsciLexerHTML.html#af11a926b7f7329c39f6c029fec89ea42a2cc0d9178c847cbde2bed0c104fe0c91">QsciLexerHTML</a>
</li>
<li>eolFill()
: <a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">QsciLexer</a>
, <a class="el" href="classQsciStyle.html#adcc34134da3341f1f07a847b09f6565b">QsciStyle</a>
</li>
<li>eolFillChanged()
: <a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">QsciLexer</a>
</li>
<li>EolMac
: <a class="el" href="classQsciScintilla.html#ab4b6b4286a74e173a86de0a7f55241d5a884e225089dc5181bf661a13a5d12c99">QsciScintilla</a>
</li>
<li>eolMode()
: <a class="el" href="classQsciScintilla.html#ac629ee3f5ca0741d4590f6aef59611c8">QsciScintilla</a>
</li>
<li>EolMode
: <a class="el" href="classQsciScintilla.html#ab4b6b4286a74e173a86de0a7f55241d5">QsciScintilla</a>
</li>
<li>EolUnix
: <a class="el" href="classQsciScintilla.html#ab4b6b4286a74e173a86de0a7f55241d5a66683da125a4e780a672edae2781df89">QsciScintilla</a>
</li>
<li>eolVisibility()
: <a class="el" href="classQsciScintilla.html#a5e4372bd9b4249d7e574402935444b84">QsciScintilla</a>
</li>
<li>EolWindows
: <a class="el" href="classQsciScintilla.html#ab4b6b4286a74e173a86de0a7f55241d5a355072d24e408bb7fbb2be3ed5389d30">QsciScintilla</a>
</li>
<li>Error
: <a class="el" href="classQsciLexerBash.html#a19b5c93bf139293c9575bcb891709200aa077709e423acaff53b593bd170fa8e0">QsciLexerBash</a>
, <a class="el" href="classQsciLexerJSON.html#ae663f0d422d93ebde5347086be37248faa8312a1f93350c33c4d7dd3748a8910f">QsciLexerJSON</a>
, <a class="el" href="classQsciLexerMakefile.html#a77e8da2d368723364f5e2df432ce7874afecda9ed8daf8374c99aeaedfd83ffe9">QsciLexerMakefile</a>
, <a class="el" href="classQsciLexerPerl.html#a69516e9f701fceec0231cc3050b41da9a99a1c9873cd83852da55023a2420f5a8">QsciLexerPerl</a>
, <a class="el" href="classQsciLexerRuby.html#a11f87d89b2ff7aae3066ae57b0addafda96bb579511d0e7c816783f3740de5aec">QsciLexerRuby</a>
</li>
<li>EscapeSequence
: <a class="el" href="classQsciLexerCPP.html#a30c13b0ea8b55b3204ea4e9f49a313b1a96681c59afac878c90c821403c472903">QsciLexerCPP</a>
, <a class="el" href="classQsciLexerJSON.html#ae663f0d422d93ebde5347086be37248fa0bb7d6615b8af0c1aef5d28e63f2e55c">QsciLexerJSON</a>
</li>
<li>EvenData
: <a class="el" href="classQsciLexerHex.html#a61791f2aba3a3722e16e90aef56b2736acfddb8c87191259f4689f8b5a09c4a35">QsciLexerHex</a>
</li>
<li>event()
: <a class="el" href="classQsciScintilla.html#a84ab3eb3c8e81cc58bbf2d3f472e757c">QsciScintilla</a>
</li>
<li>execute()
: <a class="el" href="classQsciCommand.html#aa0bf23ebd61dd46a4eb59447e43c4cab">QsciCommand</a>
</li>
<li>ExpandKeyword
: <a class="el" href="classQsciLexerTCL.html#a25ac7663e96a6d6da069a3d6697706c8a1f6ee7e1310318ce54cbcf9a1a50f144">QsciLexerTCL</a>
</li>
<li>ExtendedAddress
: <a class="el" href="classQsciLexerHex.html#a61791f2aba3a3722e16e90aef56b2736a6b9eff67696b389fd5df910dab88f32f">QsciLexerHex</a>
</li>
<li>ExtendedCSSProperty
: <a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0aea3a8406f16545255905240042c4954b">QsciLexerCSS</a>
</li>
<li>ExtendedFunction
: <a class="el" href="classQsciLexerFortran77.html#aeb3260480e9b88f6e465b1bd1bcca0c7a0c79af742525a5c8e1728e17fcc6968e">QsciLexerFortran77</a>
</li>
<li>ExtendedInstruction
: <a class="el" href="classQsciLexerAsm.html#a59ba5e0645fb67d5ad54c1e5fafcb360ae86a4bc5121aabe1e3da244f618b215b">QsciLexerAsm</a>
</li>
<li>ExtendedPseudoClass
: <a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0aeee973f3f30ab97b97903de6de2dfca5">QsciLexerCSS</a>
</li>
<li>ExtendedPseudoElement
: <a class="el" href="classQsciLexerCSS.html#a8f38d12d56564b95f6f1f6b1834ca3e0a220432c41649811f99607624ddb568e6">QsciLexerCSS</a>
</li>
<li>ExternalCommand
: <a class="el" href="classQsciLexerBatch.html#a2e13faf432e7c61bee9cbe433b7451f4ab6c8b3d3175be5f62e770f2dc637bb61">QsciLexerBatch</a>
</li>
<li>extraAscent()
: <a class="el" href="classQsciScintilla.html#a37a46d7dbbb88374f4651feb64f55926">QsciScintilla</a>
</li>
<li>extraDescent()
: <a class="el" href="classQsciScintilla.html#aa44a01f20a2d88c06c561f0043b4f83b">QsciScintilla</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
