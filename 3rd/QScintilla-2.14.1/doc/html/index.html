<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QScintilla - a Port to Qt v5 and Qt v6 of Scintilla</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="PageDoc"><div class="header">
  <div class="headertitle">
<div class="title">QScintilla - a Port to Qt v5 and Qt v6 of Scintilla </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><h2>Introduction</h2>
<p><a href="http://www.riverbankcomputing.com/software/qscintilla/">QScintilla</a> is a port to Qt of the <a href="http://www.scintilla.org/">Scintilla</a> editing component.</p>
<p>As well as features found in standard text editing components, Scintilla includes features especially useful when editing and debugging source code:</p>
<ul>
<li>
syntax styling with support for over 70 languages </li>
<li>
error indicators </li>
<li>
code completion </li>
<li>
call tips </li>
<li>
code folding </li>
<li>
margins can contain markers like those used in debuggers to indicate breakpoints and the current line. </li>
<li>
recordable macros </li>
<li>
multiple views </li>
<li>
printing. </li>
</ul>
<p>QScintilla is a port or Scintilla to the Qt GUI toolkit from <a href="http://www.qt.io">The Qt Company</a> and runs on any operating system supported by Qt (eg. Windows, Linux, macOS, iOS and Android). QScintilla works with Qt v5 and v6.</p>
<p>QScintilla also includes language bindings for <a href="https://www.python.org">Python</a>. These require that <a href="https://www.riverbankcomputing.com/software/pyqt/">PyQt</a> v5 or v6 is also installed.</p>
<p>This version of QScintilla is based on Scintilla v3.10.1.</p>
<h2>Licensing</h2>
<p>QScintilla is available under the <a href="http://www.gnu.org/licenses/gpl.html">GNU General Public License v3</a> and the Riverbank Commercial License.</p>
<p>The commercial license allows closed source applications using QScintilla to be developed and distributed. At the moment the commercial version of QScintilla is bundled with, but packaged separately from, the commercial version of <a href="http://www.riverbankcomputing.com/software/pyqt/">PyQt</a>.</p>
<p>The Scintilla code within QScintilla is released under the following license:</p>
<pre>
 License for Scintilla and SciTE</pre><pre> Copyright 1998-2003 by Neil Hodgson <a href="#" onclick="location.href='mai'+'lto:'+'nei'+'lh'+'@sc'+'in'+'til'+'la'+'.or'+'g'; return false;">neilh<span style="display: none;">.nosp@m.</span>@sci<span style="display: none;">.nosp@m.</span>ntill<span style="display: none;">.nosp@m.</span>a.or<span style="display: none;">.nosp@m.</span>g</a></pre><pre> All Rights Reserved</pre><pre> Permission to use, copy, modify, and distribute this software and its
 documentation for any purpose and without fee is hereby granted,
 provided that the above copyright notice appear in all copies and that
 both that copyright notice and this permission notice appear in
 supporting documentation.</pre><pre> NEIL HODGSON DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS
 SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
 AND FITNESS, IN NO EVENT SHALL NEIL HODGSON BE LIABLE FOR ANY
 SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
 WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
 TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE
 OR PERFORMANCE OF THIS SOFTWARE.
</pre><h2>Installation</h2>
<p>As supplied QScintilla will be built as a shared library/DLL and installed in the same directories as the Qt libraries and include files.</p>
<p>If you wish to build a static version of the library then pass <code>CONFIG+=staticlib</code> on the <code>qmake</code> command line.</p>
<p>On macOS, if you wish to build a dynamic version of the library that supports both <code>x86_64</code> and <code>arm64</code> architectures then edit the file <code>qscintilla.pro</code> in the <code>src</code> directory and comment in the definition of <code>QMAKE_APPLE_DEVICE_ARCHS</code>. Similar changes can be made to the <code>.pro</code> files for the Designer plugin and the example application.</p>
<p>If you want to make more significant changes to the configuration then edit the file <code>qscintilla.pro</code> in the <code>src</code> directory.</p>
<p>If you do make changes, specifically to the names of the installation directories or the name of the library, then you may also need to update the <code>src/features/qscintilla2.prf</code> file.</p>
<p>See your <code>qmake</code> documentation for more details.</p>
<p>To build and install QScintilla, run:</p>
<pre>
    cd src
    qmake
    make
    make install
</pre><p>If you have multiple versions of Qt installed then make sure you use the correct version of <code>qmake</code>.</p>
<p>The underlying Scintilla code may support additional compile-time options. These can be configured by passing appropriate arguments to <code>qmake</code>. For example, if you have an old C++ compiler that does not have a working <code>std::regex</code> then invoke <code>qmake</code> as follows:</p>
<pre>
    qmake DEFINES+=NO_CXX11_REGEX=1
</pre><h3>Installation on Windows</h3>
<p>Before compiling QScintilla on Windows you should remove the <code>Qsci</code> directory containing the QScintilla header files from any previous installation. This is because the <code>Makefile</code> generated by <code>qmake</code> will find these older header files instead of the new ones.</p>
<p>Depending on the compiler you are using you may need to run <code>nmake</code> rather than <code>make</code>.</p>
<p>If you have built a Windows DLL then you probably also want to run:</p>
<pre>
    copy %QTDIR%\lib\qscintilla2.dll %QTDIR%\bin
</pre><h2>Integration with <code>qmake</code></h2>
<p>To configure <code>qmake</code> to find your QScintilla installation, add the following line to your application's <code>.pro</code> file:</p>
<pre>
    CONFIG += qscintilla2
</pre><h2>Qt Designer Plugin</h2>
<p>QScintilla includes an optional plugin for Qt Designer that allows QScintilla instances to be included in GUI designs just like any other Qt widget.</p>
<p>To build the plugin on all platforms, make sure QScintilla is installed and then run (as root or administrator):</p>
<pre>
    cd designer
    qmake
    make
    make install
</pre><p>On Windows (and depending on the compiler you are using) you may need to run <code>nmake</code> rather than <code>make</code>.</p>
<h2>Example Application</h2>
<p>The example application provided is a port of the standard Qt <code>application</code> example with the <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> class being used instead of Qt's QTextEdit class.</p>
<p>The example does not demonstrate all of the extra features of QScintilla.</p>
<p>To build the example, run:</p>
<pre>
    cd example
    qmake
    make
</pre><p>On Windows (and depending on the compiler you are using) you may need to run <code>nmake</code> rather than <code>make</code>.</p>
<h2>Python Bindings</h2>
<p>The Python bindings are in the <code>Python</code> sub-directory. You must have either PyQt5 or PyQt6 already installed and PyQt-builder. QScintilla must also already be built and installed.</p>
<p>The <code>Python</code> sub-directory contains a <code>pyproject-qt5.toml</code> file and a <code>pyproject-qt6.toml</code> file. If you are building for PyQt5 and Qt v5 then you must copy the <code>pyproject-qt5.toml</code> file to <code>pyproject.toml</code>. If instead you are building for PyQt6 and Qt v6 then you must copy the <code>pyproject-qt6.toml</code> file to <code>pyproject.toml</code>.</p>
<p>To build and install the bindings, run:</p>
<pre>
    cd Python
    sip-install
</pre> </div></div><!-- PageDoc -->
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
