<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.20"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>QScintilla: QsciLexerPython Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">QScintilla
   &#160;<span id="projectnumber">2.14.1</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.20 -->
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',false,false,'search.php','Search');
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-slots">Public Slots</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="classQsciLexerPython-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">QsciLexerPython Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;qscilexerpython.h&gt;</code></p>

<p>Inherits <a class="el" href="classQsciLexer.html">QsciLexer</a>.</p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a53a5337d46bed7e115df4be1d344f301"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301ac55b65493dace8925090544c401e8556">Default</a> = 0, 
<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a3ae64eb6b01ecf28c28cfa568456018e">Comment</a> = 1, 
<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a828776730e146194bdc38d5baecd99b6">Number</a> = 2, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a53439291c4ec1556fa2143b582b21457">DoubleQuotedString</a> = 3, 
<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301aacabc0f11d5b649fb4b4814018fbc2d7">SingleQuotedString</a> = 4, 
<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a5afb6ff3eda4e10420bc19d8cfce6697">Keyword</a> = 5, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301ad85722ad55aee4bf1966db4a7cfd2b32">TripleSingleQuotedString</a> = 6, 
<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a1495ec67c855b00c949a8bd8672aa1bc">TripleDoubleQuotedString</a> = 7, 
<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a13a264a4745f895d9b8218a5eb834cab">ClassName</a> = 8, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301af182efea8f041750b774f01071af8b10">FunctionMethodName</a> = 9, 
<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a7a4293d091128563c2b51f4eaade7ff2">Operator</a> = 10, 
<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301ad3c089de016a822c21aadf0760842dbe">Identifier</a> = 11, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301af439b3e4c0ee6762c95d318c457e9396">CommentBlock</a> = 12, 
<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301ac5ef1d9860f88a3f84521ff88dca3878">UnclosedString</a> = 13, 
<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a6b6db1e2f565b7945e40fa3b13d2ce5a">HighlightedIdentifier</a> = 14, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a40d923760d674229ffb146233d1cefae">Decorator</a> = 15, 
<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301ac25e8cbcb38d2022ec108d2e22d97910">DoubleQuotedFString</a> = 16, 
<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301ace53a2a59f95bc733101f4e7e57d1974">SingleQuotedFString</a> = 17, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301a25b848383c93ca55b77d6ef1cc6b0fbf">TripleSingleQuotedFString</a> = 18, 
<a class="el" href="classQsciLexerPython.html#a53a5337d46bed7e115df4be1d344f301ad1142de6be72ec89e7ce114412c97f2e">TripleDoubleQuotedFString</a> = 19
<br />
 }</td></tr>
<tr class="separator:a53a5337d46bed7e115df4be1d344f301"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a84118aff26655dcc4313d26d57d5f4fc"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fc">IndentationWarning</a> { <br />
&#160;&#160;<a class="el" href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fcaaf2844c26e0b5337e85653ca39584182">NoWarning</a> = 0, 
<a class="el" href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fca205cf6f451c495acbe224d2479c9b512">Inconsistent</a> = 1, 
<a class="el" href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fcaee65363fac7fe11d24582a68c3864686">TabsAfterSpaces</a> = 2, 
<br />
&#160;&#160;<a class="el" href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fcac76a1a962494e9526e70eabaa648c75e">Spaces</a> = 3, 
<a class="el" href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fca5be72dba23dedfc6d0b0f796c3ba163d">Tabs</a> = 4
<br />
 }</td></tr>
<tr class="separator:a84118aff26655dcc4313d26d57d5f4fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-slots"></a>
Public Slots</h2></td></tr>
<tr class="memitem:a35e71b31d8d197052c7c5250ff21f094"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a35e71b31d8d197052c7c5250ff21f094">setFoldComments</a> (bool fold)</td></tr>
<tr class="separator:a35e71b31d8d197052c7c5250ff21f094"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afc0aaf4300e9ca02eb8fa49328bbe8d8"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#afc0aaf4300e9ca02eb8fa49328bbe8d8">setFoldQuotes</a> (bool fold)</td></tr>
<tr class="separator:afc0aaf4300e9ca02eb8fa49328bbe8d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a421ab12187730bc0686dc72710867ec3"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a421ab12187730bc0686dc72710867ec3">setIndentationWarning</a> (<a class="el" href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fc">QsciLexerPython::IndentationWarning</a> warn)</td></tr>
<tr class="separator:a421ab12187730bc0686dc72710867ec3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_slots_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_slots_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Slots inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a793e592d3ac100ff81ae09eefbaa74ef">setAutoIndentStyle</a> (int autoindentstyle)</td></tr>
<tr class="separator:a793e592d3ac100ff81ae09eefbaa74ef inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0e4235e0bd33f64431a9c6e8c35038d4">setColor</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:a0e4235e0bd33f64431a9c6e8c35038d4 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3fccdb7cb8f6524ecdeb3ff364ae5a49">setEolFill</a> (bool eoffill, int style=-1)</td></tr>
<tr class="separator:a3fccdb7cb8f6524ecdeb3ff364ae5a49 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a3484599b6db81b8392ab6cd4f50ab291">setFont</a> (const QFont &amp;f, int style=-1)</td></tr>
<tr class="separator:a3484599b6db81b8392ab6cd4f50ab291 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#addbc923c938f946180a15d494d17b567">setPaper</a> (const QColor &amp;c, int style=-1)</td></tr>
<tr class="separator:addbc923c938f946180a15d494d17b567 inherit pub_slots_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a158b80fd7ee649cbb618b1df33491bab"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a158b80fd7ee649cbb618b1df33491bab">QsciLexerPython</a> (QObject *parent=0)</td></tr>
<tr class="separator:a158b80fd7ee649cbb618b1df33491bab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf0e76eca3bc604650cc20d4fc110c7f"><td class="memItemLeft" align="right" valign="top"><a id="abf0e76eca3bc604650cc20d4fc110c7f"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#abf0e76eca3bc604650cc20d4fc110c7f">~QsciLexerPython</a> ()</td></tr>
<tr class="separator:abf0e76eca3bc604650cc20d4fc110c7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae96690293b8128bea9cedf9b55b92ad6"><td class="memItemLeft" align="right" valign="top"><a id="ae96690293b8128bea9cedf9b55b92ad6"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#ae96690293b8128bea9cedf9b55b92ad6">language</a> () const</td></tr>
<tr class="separator:ae96690293b8128bea9cedf9b55b92ad6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9fe6e18dbb7ef4cad7f370286d7db0b7"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a9fe6e18dbb7ef4cad7f370286d7db0b7">lexer</a> () const</td></tr>
<tr class="separator:a9fe6e18dbb7ef4cad7f370286d7db0b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a305ec320aa2357947cbeb1608b95d840"><td class="memItemLeft" align="right" valign="top">QStringList&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a305ec320aa2357947cbeb1608b95d840">autoCompletionWordSeparators</a> () const</td></tr>
<tr class="separator:a305ec320aa2357947cbeb1608b95d840"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afe42ac5a09816340d4bec920b523aed6"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#afe42ac5a09816340d4bec920b523aed6">blockLookback</a> () const</td></tr>
<tr class="separator:afe42ac5a09816340d4bec920b523aed6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc66ee4b78453d245ac1b4dff45490f4"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#adc66ee4b78453d245ac1b4dff45490f4">blockStart</a> (int *style=0) const</td></tr>
<tr class="separator:adc66ee4b78453d245ac1b4dff45490f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab30fa749a26490888fe18f2fcea47b02"><td class="memItemLeft" align="right" valign="top"><a id="ab30fa749a26490888fe18f2fcea47b02"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#ab30fa749a26490888fe18f2fcea47b02">braceStyle</a> () const</td></tr>
<tr class="separator:ab30fa749a26490888fe18f2fcea47b02"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7320152a6d9098d07bba3da6c99a232e"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a7320152a6d9098d07bba3da6c99a232e">defaultColor</a> (int style) const</td></tr>
<tr class="separator:a7320152a6d9098d07bba3da6c99a232e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a855939c35d62798c00b0361a0edc41da"><td class="memItemLeft" align="right" valign="top"><a id="a855939c35d62798c00b0361a0edc41da"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a855939c35d62798c00b0361a0edc41da">defaultEolFill</a> (int style) const</td></tr>
<tr class="separator:a855939c35d62798c00b0361a0edc41da"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ea1d7ae4594027f8b565380f3fffbb4"><td class="memItemLeft" align="right" valign="top"><a id="a7ea1d7ae4594027f8b565380f3fffbb4"></a>
QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a7ea1d7ae4594027f8b565380f3fffbb4">defaultFont</a> (int style) const</td></tr>
<tr class="separator:a7ea1d7ae4594027f8b565380f3fffbb4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5e9de211c7e94a22da5c0d599a9e494b"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a5e9de211c7e94a22da5c0d599a9e494b">defaultPaper</a> (int style) const</td></tr>
<tr class="separator:a5e9de211c7e94a22da5c0d599a9e494b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acb5ec792032e6108b3c2d6ec6e565f49"><td class="memItemLeft" align="right" valign="top"><a id="acb5ec792032e6108b3c2d6ec6e565f49"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#acb5ec792032e6108b3c2d6ec6e565f49">indentationGuideView</a> () const</td></tr>
<tr class="separator:acb5ec792032e6108b3c2d6ec6e565f49"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2467729449b6c78d63305b88b2f62789"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a2467729449b6c78d63305b88b2f62789">keywords</a> (int set) const</td></tr>
<tr class="separator:a2467729449b6c78d63305b88b2f62789"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa3454a4c643cd0d479da8412341f1206"><td class="memItemLeft" align="right" valign="top">QString&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#aa3454a4c643cd0d479da8412341f1206">description</a> (int style) const</td></tr>
<tr class="separator:aa3454a4c643cd0d479da8412341f1206"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abed099316dd95a6289c76d151a37c264"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#abed099316dd95a6289c76d151a37c264">refreshProperties</a> ()</td></tr>
<tr class="separator:abed099316dd95a6289c76d151a37c264"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a7acaa356fdbefd26cfe0f30264c43a"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a1a7acaa356fdbefd26cfe0f30264c43a">foldComments</a> () const</td></tr>
<tr class="separator:a1a7acaa356fdbefd26cfe0f30264c43a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27dcfdcac480d0360029d1f12b14f724"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a27dcfdcac480d0360029d1f12b14f724">setFoldCompact</a> (bool fold)</td></tr>
<tr class="separator:a27dcfdcac480d0360029d1f12b14f724"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a21891669bab4719e8e7cf482e3bf5a51"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a21891669bab4719e8e7cf482e3bf5a51">foldCompact</a> () const</td></tr>
<tr class="separator:a21891669bab4719e8e7cf482e3bf5a51"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a167dbdb42a4c0ed65229a3418153d4dd"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a167dbdb42a4c0ed65229a3418153d4dd">foldQuotes</a> () const</td></tr>
<tr class="separator:a167dbdb42a4c0ed65229a3418153d4dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff624320c72fa3b433d82d6a558238e8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fc">QsciLexerPython::IndentationWarning</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#aff624320c72fa3b433d82d6a558238e8">indentationWarning</a> () const</td></tr>
<tr class="separator:aff624320c72fa3b433d82d6a558238e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ade07472f3cc8a4cccbb0bb6b964f0356"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#ade07472f3cc8a4cccbb0bb6b964f0356">setHighlightSubidentifiers</a> (bool enabled)</td></tr>
<tr class="separator:ade07472f3cc8a4cccbb0bb6b964f0356"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af8c28786c65b23583d92030ac606d07c"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#af8c28786c65b23583d92030ac606d07c">highlightSubidentifiers</a> () const</td></tr>
<tr class="separator:af8c28786c65b23583d92030ac606d07c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5887a36e4a8d6ff54f4c796b33bc2eef"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a5887a36e4a8d6ff54f4c796b33bc2eef">setStringsOverNewlineAllowed</a> (bool allowed)</td></tr>
<tr class="separator:a5887a36e4a8d6ff54f4c796b33bc2eef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa4abeabae54373d536961d0aabb5ecdf"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#aa4abeabae54373d536961d0aabb5ecdf">stringsOverNewlineAllowed</a> () const</td></tr>
<tr class="separator:aa4abeabae54373d536961d0aabb5ecdf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:accc3cd3ccf7d62840ded955400695b9d"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#accc3cd3ccf7d62840ded955400695b9d">setV2UnicodeAllowed</a> (bool allowed)</td></tr>
<tr class="separator:accc3cd3ccf7d62840ded955400695b9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add088b1bd36b0d5eb0f3b87e403cec10"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#add088b1bd36b0d5eb0f3b87e403cec10">v2UnicodeAllowed</a> () const</td></tr>
<tr class="separator:add088b1bd36b0d5eb0f3b87e403cec10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6bc53fc7e6dc90a80a26e22f6f49acb"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#ae6bc53fc7e6dc90a80a26e22f6f49acb">setV3BinaryOctalAllowed</a> (bool allowed)</td></tr>
<tr class="separator:ae6bc53fc7e6dc90a80a26e22f6f49acb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a02ad644d3bc229939e57d5e9f665a6b9"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a02ad644d3bc229939e57d5e9f665a6b9">v3BinaryOctalAllowed</a> () const</td></tr>
<tr class="separator:a02ad644d3bc229939e57d5e9f665a6b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a856785e000203b1da8fa6f295daad13e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a856785e000203b1da8fa6f295daad13e">setV3BytesAllowed</a> (bool allowed)</td></tr>
<tr class="separator:a856785e000203b1da8fa6f295daad13e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a67308885b201ef6e21f0a273bf0b3c31"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a67308885b201ef6e21f0a273bf0b3c31">v3BytesAllowed</a> () const</td></tr>
<tr class="separator:a67308885b201ef6e21f0a273bf0b3c31"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a49fc2fb49ed07f1cb5f8b0a96e07d0d4">QsciLexer</a> (QObject *parent=0)</td></tr>
<tr class="separator:a49fc2fb49ed07f1cb5f8b0a96e07d0d4 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="af6cc5bb9d9421d806e9941d018030068"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#af6cc5bb9d9421d806e9941d018030068">~QsciLexer</a> ()</td></tr>
<tr class="separator:af6cc5bb9d9421d806e9941d018030068 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a9576dd2ce748647abe981724ee76c1ce">lexerId</a> () const</td></tr>
<tr class="separator:a9576dd2ce748647abe981724ee76c1ce inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a> () const</td></tr>
<tr class="separator:a0aa2c7bc939d793db01bbc1863b15d63 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a6504a6fff35af16fbfd97889048db2a5"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a6504a6fff35af16fbfd97889048db2a5">autoCompletionFillups</a> () const</td></tr>
<tr class="separator:a6504a6fff35af16fbfd97889048db2a5 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a79c27285b6033c553b3f54cb6c56b338">autoIndentStyle</a> ()</td></tr>
<tr class="separator:a79c27285b6033c553b3f54cb6c56b338 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e294eba77713f516acbcebc10af1493 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a8e294eba77713f516acbcebc10af1493">blockEnd</a> (int *style=0) const</td></tr>
<tr class="separator:a8e294eba77713f516acbcebc10af1493 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf12117a142b6f68479ea425d80a4196 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abf12117a142b6f68479ea425d80a4196">blockStartKeyword</a> (int *style=0) const</td></tr>
<tr class="separator:abf12117a142b6f68479ea425d80a4196 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="afccca7eb1aed463f89ac442d99135839"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#afccca7eb1aed463f89ac442d99135839">caseSensitive</a> () const</td></tr>
<tr class="separator:afccca7eb1aed463f89ac442d99135839 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acff58ba06195b9458a61d7ef3573c701">color</a> (int style) const</td></tr>
<tr class="separator:acff58ba06195b9458a61d7ef3573c701 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aa6ed26c11f54f71a305d3ee03d685f06">eolFill</a> (int style) const</td></tr>
<tr class="separator:aa6ed26c11f54f71a305d3ee03d685f06 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#abd34f0d3055b8c7b52f0156f92244e8c">font</a> (int style) const</td></tr>
<tr class="separator:abd34f0d3055b8c7b52f0156f92244e8c inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2447139ff781bf55c74177881ac023ac">defaultStyle</a> () const</td></tr>
<tr class="separator:a2447139ff781bf55c74177881ac023ac inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a519df98c9e7d9d26734a38ea9bed744a">paper</a> (int style) const</td></tr>
<tr class="separator:a519df98c9e7d9d26734a38ea9bed744a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a31f12624858cbb8abdc59af34b5a85c7">defaultColor</a> () const</td></tr>
<tr class="separator:a31f12624858cbb8abdc59af34b5a85c7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QFont&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac7cf70f76eb03d6d475985cc4b884b0e">defaultFont</a> () const</td></tr>
<tr class="separator:ac7cf70f76eb03d6d475985cc4b884b0e inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">QColor&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a2dce337026551b6440e1dcdafa95b7d7">defaultPaper</a> () const</td></tr>
<tr class="separator:a2dce337026551b6440e1dcdafa95b7d7 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classQsciScintilla.html">QsciScintilla</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a> () const</td></tr>
<tr class="separator:ad892735ca7ad0bad9b7fafdcb44eeaa8 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac2e1ada934a5dc7685c1ee6a464de5fd">setAPIs</a> (<a class="el" href="classQsciAbstractAPIs.html">QsciAbstractAPIs</a> *<a class="el" href="classQsciLexer.html#a0aa2c7bc939d793db01bbc1863b15d63">apis</a>)</td></tr>
<tr class="separator:ac2e1ada934a5dc7685c1ee6a464de5fd inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a32b16ee95c3dabbc7de61541dd110521">setDefaultColor</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a32b16ee95c3dabbc7de61541dd110521 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a19f0b390b5594d0dff5e4d4b484e43d2">setDefaultFont</a> (const QFont &amp;f)</td></tr>
<tr class="separator:a19f0b390b5594d0dff5e4d4b484e43d2 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a7ebaedee6979d4cb17399361b37e33e0">setDefaultPaper</a> (const QColor &amp;c)</td></tr>
<tr class="separator:a7ebaedee6979d4cb17399361b37e33e0 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a1e81186b1f8f8bc2a4901a42cbca568a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>setEditor</b> (<a class="el" href="classQsciScintilla.html">QsciScintilla</a> *<a class="el" href="classQsciLexer.html#ad892735ca7ad0bad9b7fafdcb44eeaa8">editor</a>)</td></tr>
<tr class="separator:a1e81186b1f8f8bc2a4901a42cbca568a inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a27728e4e361c5f4bf87690d34d83057d">readSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;)</td></tr>
<tr class="separator:a27728e4e361c5f4bf87690d34d83057d inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ab222fbddb7eb72261153d1bebb5a01ee">styleBitsNeeded</a> () const</td></tr>
<tr class="separator:ab222fbddb7eb72261153d1bebb5a01ee inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aace68e3dbcef9da1b031fb9cfd843c57 inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#aace68e3dbcef9da1b031fb9cfd843c57">wordCharacters</a> () const</td></tr>
<tr class="separator:aace68e3dbcef9da1b031fb9cfd843c57 inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a619ee93cb512755e3f946fe61ee097de">writeSettings</a> (QSettings &amp;qs, const char *prefix=&quot;/Scintilla&quot;) const</td></tr>
<tr class="separator:a619ee93cb512755e3f946fe61ee097de inherit pub_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:a1b8f36843f4abe6ec3ee75205b5b0111"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a1b8f36843f4abe6ec3ee75205b5b0111">readProperties</a> (QSettings &amp;qs, const QString &amp;prefix)</td></tr>
<tr class="separator:a1b8f36843f4abe6ec3ee75205b5b0111"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8921849dce20c65c0fc024bc27255873"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexerPython.html#a8921849dce20c65c0fc024bc27255873">writeProperties</a> (QSettings &amp;qs, const QString &amp;prefix) const</td></tr>
<tr class="separator:a8921849dce20c65c0fc024bc27255873"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pro_methods_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('pro_methods_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Protected Member Functions inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a41d4521504d63ee63d43fd7ed0c003ee"></a>
QByteArray&#160;</td><td class="memItemRight" valign="bottom"><b>textAsBytes</b> (const QString &amp;text) const</td></tr>
<tr class="separator:a41d4521504d63ee63d43fd7ed0c003ee inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memItemLeft" align="right" valign="top"><a id="a5261dd606c209a5c6a494e608a9a111a"></a>
QString&#160;</td><td class="memItemRight" valign="bottom"><b>bytesAsText</b> (const char *bytes, int size) const</td></tr>
<tr class="separator:a5261dd606c209a5c6a494e608a9a111a inherit pro_methods_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header signals_classQsciLexer"><td colspan="2" onclick="javascript:toggleInherit('signals_classQsciLexer')"><img src="closed.png" alt="-"/>&#160;Signals inherited from <a class="el" href="classQsciLexer.html">QsciLexer</a></td></tr>
<tr class="memitem:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a901cf93072b3db3ffe503eab78ae6954">colorChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:a901cf93072b3db3ffe503eab78ae6954 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#a66c01f0c9470164d4575c2b64f0e4220">eolFillChanged</a> (bool eolfilled, int style)</td></tr>
<tr class="separator:a66c01f0c9470164d4575c2b64f0e4220 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#ac04ade8be901b67af681e5e3516c0946">fontChanged</a> (const QFont &amp;f, int style)</td></tr>
<tr class="separator:ac04ade8be901b67af681e5e3516c0946 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#adf8de1727583e902c7cae673673a78a1">paperChanged</a> (const QColor &amp;c, int style)</td></tr>
<tr class="separator:adf8de1727583e902c7cae673673a78a1 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged</a> (const char *prop, const char *val)</td></tr>
<tr class="separator:acd8475f0da36449dc6b1189a587d7a83 inherit signals_classQsciLexer"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="classQsciLexerPython.html" title="The QsciLexerPython class encapsulates the Scintilla Python lexer.">QsciLexerPython</a> class encapsulates the Scintilla Python lexer. </p>
</div><h2 class="groupheader">Member Enumeration Documentation</h2>
<a id="a53a5337d46bed7e115df4be1d344f301"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a53a5337d46bed7e115df4be1d344f301">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">anonymous enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the meanings of the different styles used by the Python lexer. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301ac55b65493dace8925090544c401e8556"></a>Default&#160;</td><td class="fielddoc"><p>The default. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301a3ae64eb6b01ecf28c28cfa568456018e"></a>Comment&#160;</td><td class="fielddoc"><p>A comment. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301a828776730e146194bdc38d5baecd99b6"></a>Number&#160;</td><td class="fielddoc"><p>A number. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301a53439291c4ec1556fa2143b582b21457"></a>DoubleQuotedString&#160;</td><td class="fielddoc"><p>A double-quoted string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301aacabc0f11d5b649fb4b4814018fbc2d7"></a>SingleQuotedString&#160;</td><td class="fielddoc"><p>A single-quoted string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301a5afb6ff3eda4e10420bc19d8cfce6697"></a>Keyword&#160;</td><td class="fielddoc"><p>A keyword. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301ad85722ad55aee4bf1966db4a7cfd2b32"></a>TripleSingleQuotedString&#160;</td><td class="fielddoc"><p>A triple single-quoted string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301a1495ec67c855b00c949a8bd8672aa1bc"></a>TripleDoubleQuotedString&#160;</td><td class="fielddoc"><p>A triple double-quoted string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301a13a264a4745f895d9b8218a5eb834cab"></a>ClassName&#160;</td><td class="fielddoc"><p>The name of a class. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301af182efea8f041750b774f01071af8b10"></a>FunctionMethodName&#160;</td><td class="fielddoc"><p>The name of a function or method. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301a7a4293d091128563c2b51f4eaade7ff2"></a>Operator&#160;</td><td class="fielddoc"><p>An operator. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301ad3c089de016a822c21aadf0760842dbe"></a>Identifier&#160;</td><td class="fielddoc"><p>An identifier. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301af439b3e4c0ee6762c95d318c457e9396"></a>CommentBlock&#160;</td><td class="fielddoc"><p>A comment block. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301ac5ef1d9860f88a3f84521ff88dca3878"></a>UnclosedString&#160;</td><td class="fielddoc"><p>The end of a line where a string is not closed. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301a6b6db1e2f565b7945e40fa3b13d2ce5a"></a>HighlightedIdentifier&#160;</td><td class="fielddoc"><p>A highlighted identifier. These are defined by keyword set</p><ol type="1">
<li>Reimplement <a class="el" href="classQsciLexerPython.html#a2467729449b6c78d63305b88b2f62789">keywords()</a> to define keyword set 2. </li>
</ol>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301a40d923760d674229ffb146233d1cefae"></a>Decorator&#160;</td><td class="fielddoc"><p>A decorator. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301ac25e8cbcb38d2022ec108d2e22d97910"></a>DoubleQuotedFString&#160;</td><td class="fielddoc"><p>A double-quoted f-string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301ace53a2a59f95bc733101f4e7e57d1974"></a>SingleQuotedFString&#160;</td><td class="fielddoc"><p>A single-quoted f-string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301a25b848383c93ca55b77d6ef1cc6b0fbf"></a>TripleSingleQuotedFString&#160;</td><td class="fielddoc"><p>A triple single-quoted f-string. </p>
</td></tr>
<tr><td class="fieldname"><a id="a53a5337d46bed7e115df4be1d344f301ad1142de6be72ec89e7ce114412c97f2e"></a>TripleDoubleQuotedFString&#160;</td><td class="fielddoc"><p>A triple double-quoted f-string. </p>
</td></tr>
</table>

</div>
</div>
<a id="a84118aff26655dcc4313d26d57d5f4fc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a84118aff26655dcc4313d26d57d5f4fc">&#9670;&nbsp;</a></span>IndentationWarning</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fc">QsciLexerPython::IndentationWarning</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>This enum defines the different conditions that can cause indentations to be displayed as being bad. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a84118aff26655dcc4313d26d57d5f4fcaaf2844c26e0b5337e85653ca39584182"></a>NoWarning&#160;</td><td class="fielddoc"><p>Bad indentation is not displayed differently. </p>
</td></tr>
<tr><td class="fieldname"><a id="a84118aff26655dcc4313d26d57d5f4fca205cf6f451c495acbe224d2479c9b512"></a>Inconsistent&#160;</td><td class="fielddoc"><p>The indentation is inconsistent when compared to the previous line, ie. it is made up of a different combination of tabs and/or spaces. </p>
</td></tr>
<tr><td class="fieldname"><a id="a84118aff26655dcc4313d26d57d5f4fcaee65363fac7fe11d24582a68c3864686"></a>TabsAfterSpaces&#160;</td><td class="fielddoc"><p>The indentation is made up of spaces followed by tabs. </p>
</td></tr>
<tr><td class="fieldname"><a id="a84118aff26655dcc4313d26d57d5f4fcac76a1a962494e9526e70eabaa648c75e"></a>Spaces&#160;</td><td class="fielddoc"><p>The indentation contains spaces. </p>
</td></tr>
<tr><td class="fieldname"><a id="a84118aff26655dcc4313d26d57d5f4fca5be72dba23dedfc6d0b0f796c3ba163d"></a>Tabs&#160;</td><td class="fielddoc"><p>The indentation contains tabs. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a158b80fd7ee649cbb618b1df33491bab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a158b80fd7ee649cbb618b1df33491bab">&#9670;&nbsp;</a></span>QsciLexerPython()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">QsciLexerPython::QsciLexerPython </td>
          <td>(</td>
          <td class="paramtype">QObject *&#160;</td>
          <td class="paramname"><em>parent</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Construct a <a class="el" href="classQsciLexerPython.html" title="The QsciLexerPython class encapsulates the Scintilla Python lexer.">QsciLexerPython</a> with parent <em>parent</em>. <em>parent</em> is typically the <a class="el" href="classQsciScintilla.html" title="The QsciScintilla class implements a higher level, more Qt-like, API to the Scintilla editor widget.">QsciScintilla</a> instance. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a9fe6e18dbb7ef4cad7f370286d7db0b7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9fe6e18dbb7ef4cad7f370286d7db0b7">&#9670;&nbsp;</a></span>lexer()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerPython::lexer </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the name of the lexer. Some lexers support a number of languages. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ac7c40b97187e23ab85f6d95113f91b39">QsciLexer</a>.</p>

</div>
</div>
<a id="a305ec320aa2357947cbeb1608b95d840"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a305ec320aa2357947cbeb1608b95d840">&#9670;&nbsp;</a></span>autoCompletionWordSeparators()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QStringList QsciLexerPython::autoCompletionWordSeparators </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the list of character sequences that can separate auto-completion words. The first in the list is assumed to be the sequence used to separate words in the lexer's API files. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a4e10650b0e9ad137062ad5c17ad33e76">QsciLexer</a>.</p>

</div>
</div>
<a id="afe42ac5a09816340d4bec920b523aed6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afe42ac5a09816340d4bec920b523aed6">&#9670;&nbsp;</a></span>blockLookback()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int QsciLexerPython::blockLookback </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the number of lines prior to the current one when determining the scope of a block when auto-indenting. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a8b1bb1261e7b9701c62bbe4f1d171e06">QsciLexer</a>.</p>

</div>
</div>
<a id="adc66ee4b78453d245ac1b4dff45490f4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adc66ee4b78453d245ac1b4dff45490f4">&#9670;&nbsp;</a></span>blockStart()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerPython::blockStart </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>style</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns a space separated list of words or characters in a particular style that define the start of a block for auto-indentation. The style is returned via <em>style</em>. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a340eafe726fd6964c0adba956fe3428c">QsciLexer</a>.</p>

</div>
</div>
<a id="a7320152a6d9098d07bba3da6c99a232e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7320152a6d9098d07bba3da6c99a232e">&#9670;&nbsp;</a></span>defaultColor()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerPython::defaultColor </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the foreground colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#a5e9de211c7e94a22da5c0d599a9e494b">defaultPaper()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#af7508f1b816a2c9446d36141edc9b5ce">QsciLexer</a>.</p>

</div>
</div>
<a id="a5e9de211c7e94a22da5c0d599a9e494b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5e9de211c7e94a22da5c0d599a9e494b">&#9670;&nbsp;</a></span>defaultPaper()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QColor QsciLexerPython::defaultPaper </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the background colour of the text for style number <em>style</em>.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#a7320152a6d9098d07bba3da6c99a232e">defaultColor()</a> </dd></dl>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a7e5ab7f541d913760c32abedbdc72963">QsciLexer</a>.</p>

</div>
</div>
<a id="a2467729449b6c78d63305b88b2f62789"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2467729449b6c78d63305b88b2f62789">&#9670;&nbsp;</a></span>keywords()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* QsciLexerPython::keywords </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>set</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the set of keywords for the keyword set <em>set</em> recognised by the lexer as a space separated string. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#a013b7c1bf9846e231b97827dfd9540b0">QsciLexer</a>.</p>

</div>
</div>
<a id="aa3454a4c643cd0d479da8412341f1206"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa3454a4c643cd0d479da8412341f1206">&#9670;&nbsp;</a></span>description()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">QString QsciLexerPython::description </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>style</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the descriptive name for style number <em>style</em>. If the style is invalid for this language then an empty QString is returned. This is intended to be used in user preference dialogs. </p>

<p>Implements <a class="el" href="classQsciLexer.html#add9c20adb43bc38d1a0ca3083ac3e6fa">QsciLexer</a>.</p>

</div>
</div>
<a id="abed099316dd95a6289c76d151a37c264"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abed099316dd95a6289c76d151a37c264">&#9670;&nbsp;</a></span>refreshProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerPython::refreshProperties </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Causes all properties to be refreshed by emitting the <a class="el" href="classQsciLexer.html#acd8475f0da36449dc6b1189a587d7a83">propertyChanged()</a> signal as required. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ae508c3ab4ce1f338dfff3ddf5ee7e34c">QsciLexer</a>.</p>

</div>
</div>
<a id="a1a7acaa356fdbefd26cfe0f30264c43a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1a7acaa356fdbefd26cfe0f30264c43a">&#9670;&nbsp;</a></span>foldComments()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPython::foldComments </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if indented comment blocks can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#a35e71b31d8d197052c7c5250ff21f094">setFoldComments()</a> </dd></dl>

</div>
</div>
<a id="a27dcfdcac480d0360029d1f12b14f724"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a27dcfdcac480d0360029d1f12b14f724">&#9670;&nbsp;</a></span>setFoldCompact()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerPython::setFoldCompact </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then trailing blank lines are included in a fold block. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#a21891669bab4719e8e7cf482e3bf5a51">foldCompact()</a> </dd></dl>

</div>
</div>
<a id="a21891669bab4719e8e7cf482e3bf5a51"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a21891669bab4719e8e7cf482e3bf5a51">&#9670;&nbsp;</a></span>foldCompact()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPython::foldCompact </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if trailing blank lines are included in a fold block.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#a27dcfdcac480d0360029d1f12b14f724">setFoldCompact()</a> </dd></dl>

</div>
</div>
<a id="a167dbdb42a4c0ed65229a3418153d4dd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a167dbdb42a4c0ed65229a3418153d4dd">&#9670;&nbsp;</a></span>foldQuotes()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPython::foldQuotes </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if triple quoted strings can be folded.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#afc0aaf4300e9ca02eb8fa49328bbe8d8">setFoldQuotes()</a> </dd></dl>

</div>
</div>
<a id="aff624320c72fa3b433d82d6a558238e8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aff624320c72fa3b433d82d6a558238e8">&#9670;&nbsp;</a></span>indentationWarning()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fc">QsciLexerPython::IndentationWarning</a> QsciLexerPython::indentationWarning </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the condition that will cause bad indentations to be displayed.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#a421ab12187730bc0686dc72710867ec3">setIndentationWarning()</a> </dd></dl>

</div>
</div>
<a id="ade07472f3cc8a4cccbb0bb6b964f0356"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ade07472f3cc8a4cccbb0bb6b964f0356">&#9670;&nbsp;</a></span>setHighlightSubidentifiers()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerPython::setHighlightSubidentifiers </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>enabled</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>enabled</em> is true then sub-identifiers defined in keyword set 2 will be highlighted. For example, if it is false and "open" is defined in keyword set 2 then "foo.open" will not be highlighted. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#af8c28786c65b23583d92030ac606d07c">highlightSubidentifiers()</a> </dd></dl>

</div>
</div>
<a id="af8c28786c65b23583d92030ac606d07c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af8c28786c65b23583d92030ac606d07c">&#9670;&nbsp;</a></span>highlightSubidentifiers()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPython::highlightSubidentifiers </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if string literals are allowed to span newline characters.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#ade07472f3cc8a4cccbb0bb6b964f0356">setHighlightSubidentifiers()</a> </dd></dl>

</div>
</div>
<a id="a5887a36e4a8d6ff54f4c796b33bc2eef"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5887a36e4a8d6ff54f4c796b33bc2eef">&#9670;&nbsp;</a></span>setStringsOverNewlineAllowed()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerPython::setStringsOverNewlineAllowed </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>allowed</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>allowed</em> is true then string literals are allowed to span newline characters. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#aa4abeabae54373d536961d0aabb5ecdf">stringsOverNewlineAllowed()</a> </dd></dl>

</div>
</div>
<a id="aa4abeabae54373d536961d0aabb5ecdf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa4abeabae54373d536961d0aabb5ecdf">&#9670;&nbsp;</a></span>stringsOverNewlineAllowed()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPython::stringsOverNewlineAllowed </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if string literals are allowed to span newline characters.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#a5887a36e4a8d6ff54f4c796b33bc2eef">setStringsOverNewlineAllowed()</a> </dd></dl>

</div>
</div>
<a id="accc3cd3ccf7d62840ded955400695b9d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#accc3cd3ccf7d62840ded955400695b9d">&#9670;&nbsp;</a></span>setV2UnicodeAllowed()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerPython::setV2UnicodeAllowed </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>allowed</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>allowed</em> is true then Python v2 unicode string literals (e.g. u"utf8") are allowed. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#add088b1bd36b0d5eb0f3b87e403cec10">v2UnicodeAllowed()</a> </dd></dl>

</div>
</div>
<a id="add088b1bd36b0d5eb0f3b87e403cec10"></a>
<h2 class="memtitle"><span class="permalink"><a href="#add088b1bd36b0d5eb0f3b87e403cec10">&#9670;&nbsp;</a></span>v2UnicodeAllowed()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPython::v2UnicodeAllowed </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if Python v2 unicode string literals (e.g. u"utf8") are allowed.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#accc3cd3ccf7d62840ded955400695b9d">setV2UnicodeAllowed()</a> </dd></dl>

</div>
</div>
<a id="ae6bc53fc7e6dc90a80a26e22f6f49acb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae6bc53fc7e6dc90a80a26e22f6f49acb">&#9670;&nbsp;</a></span>setV3BinaryOctalAllowed()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerPython::setV3BinaryOctalAllowed </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>allowed</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>allowed</em> is true then Python v3 binary and octal literals (e.g. 0b1011, 0o712) are allowed. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#a02ad644d3bc229939e57d5e9f665a6b9">v3BinaryOctalAllowed()</a> </dd></dl>

</div>
</div>
<a id="a02ad644d3bc229939e57d5e9f665a6b9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a02ad644d3bc229939e57d5e9f665a6b9">&#9670;&nbsp;</a></span>v3BinaryOctalAllowed()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPython::v3BinaryOctalAllowed </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if Python v3 binary and octal literals (e.g. 0b1011, 0o712) are allowed.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#ae6bc53fc7e6dc90a80a26e22f6f49acb">setV3BinaryOctalAllowed()</a> </dd></dl>

</div>
</div>
<a id="a856785e000203b1da8fa6f295daad13e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a856785e000203b1da8fa6f295daad13e">&#9670;&nbsp;</a></span>setV3BytesAllowed()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void QsciLexerPython::setV3BytesAllowed </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>allowed</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If <em>allowed</em> is true then Python v3 bytes string literals (e.g. b"bytes") are allowed. The default is true.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#a67308885b201ef6e21f0a273bf0b3c31">v3BytesAllowed()</a> </dd></dl>

</div>
</div>
<a id="a67308885b201ef6e21f0a273bf0b3c31"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a67308885b201ef6e21f0a273bf0b3c31">&#9670;&nbsp;</a></span>v3BytesAllowed()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPython::v3BytesAllowed </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if Python v3 bytes string literals (e.g. b"bytes") are allowed.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#a856785e000203b1da8fa6f295daad13e">setV3BytesAllowed()</a> </dd></dl>

</div>
</div>
<a id="a35e71b31d8d197052c7c5250ff21f094"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a35e71b31d8d197052c7c5250ff21f094">&#9670;&nbsp;</a></span>setFoldComments</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerPython::setFoldComments </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then indented comment blocks can be folded. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#a1a7acaa356fdbefd26cfe0f30264c43a">foldComments()</a> </dd></dl>

</div>
</div>
<a id="afc0aaf4300e9ca02eb8fa49328bbe8d8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afc0aaf4300e9ca02eb8fa49328bbe8d8">&#9670;&nbsp;</a></span>setFoldQuotes</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerPython::setFoldQuotes </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>fold</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If <em>fold</em> is true then triple quoted strings can be folded. The default is false.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#a167dbdb42a4c0ed65229a3418153d4dd">foldQuotes()</a> </dd></dl>

</div>
</div>
<a id="a421ab12187730bc0686dc72710867ec3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a421ab12187730bc0686dc72710867ec3">&#9670;&nbsp;</a></span>setIndentationWarning</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void QsciLexerPython::setIndentationWarning </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classQsciLexerPython.html#a84118aff26655dcc4313d26d57d5f4fc">QsciLexerPython::IndentationWarning</a>&#160;</td>
          <td class="paramname"><em>warn</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">slot</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sets the condition that will cause bad indentations to be displayed.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classQsciLexerPython.html#aff624320c72fa3b433d82d6a558238e8">indentationWarning()</a> </dd></dl>

</div>
</div>
<a id="a1b8f36843f4abe6ec3ee75205b5b0111"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1b8f36843f4abe6ec3ee75205b5b0111">&#9670;&nbsp;</a></span>readProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPython::readProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are read from the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#ad472b16506a4cbc19634f07aa90f1ea6">QsciLexer</a>.</p>

</div>
</div>
<a id="a8921849dce20c65c0fc024bc27255873"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8921849dce20c65c0fc024bc27255873">&#9670;&nbsp;</a></span>writeProperties()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool QsciLexerPython::writeProperties </td>
          <td>(</td>
          <td class="paramtype">QSettings &amp;&#160;</td>
          <td class="paramname"><em>qs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const QString &amp;&#160;</td>
          <td class="paramname"><em>prefix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The lexer's properties are written to the settings <em>qs</em>. <em>prefix</em> (which has a trailing '/') should be used as a prefix to the key of each setting. true is returned if there is no error. </p>

<p>Reimplemented from <a class="el" href="classQsciLexer.html#abccc4e010b724df1a7b5c5f3bce29501">QsciLexer</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.20
</small></address>
</body>
</html>
