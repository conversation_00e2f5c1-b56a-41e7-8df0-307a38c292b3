/*
 @licstart  The following is the entire license notice for the JavaScript code in this file.

 The MIT License (MIT)

 Copyright (C) 1997-2020 by <PERSON>

 Permission is hereby granted, free of charge, to any person obtaining a copy of this software
 and associated documentation files (the "Software"), to deal in the Software without restriction,
 including without limitation the rights to use, copy, modify, merge, publish, distribute,
 sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in all copies or
 substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING
 BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
 DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

 @licend  The above is the entire license notice for the JavaScript code in this file
*/
var menudata={children:[
{text:"Main Page",url:"index.html"},
{text:"Classes",url:"annotated.html",children:[
{text:"Class List",url:"annotated.html"},
{text:"Class Hierarchy",url:"hierarchy.html"},
{text:"Class Members",url:"functions.html",children:[
{text:"All",url:"functions.html",children:[
{text:"a",url:"functions.html#index_a"},
{text:"b",url:"functions_b.html#index_b"},
{text:"c",url:"functions_c.html#index_c"},
{text:"d",url:"functions_d.html#index_d"},
{text:"e",url:"functions_e.html#index_e"},
{text:"f",url:"functions_f.html#index_f"},
{text:"g",url:"functions_g.html#index_g"},
{text:"h",url:"functions_h.html#index_h"},
{text:"i",url:"functions_i.html#index_i"},
{text:"j",url:"functions_j.html#index_j"},
{text:"k",url:"functions_k.html#index_k"},
{text:"l",url:"functions_l.html#index_l"},
{text:"m",url:"functions_m.html#index_m"},
{text:"n",url:"functions_n.html#index_n"},
{text:"o",url:"functions_o.html#index_o"},
{text:"p",url:"functions_p.html#index_p"},
{text:"q",url:"functions_q.html#index_q"},
{text:"r",url:"functions_r.html#index_r"},
{text:"s",url:"functions_s.html#index_s"},
{text:"t",url:"functions_t.html#index_t"},
{text:"u",url:"functions_u.html#index_u"},
{text:"v",url:"functions_v.html#index_v"},
{text:"w",url:"functions_w.html#index_w"},
{text:"x",url:"functions_x.html#index_x"},
{text:"z",url:"functions_z.html#index_z"},
{text:"~",url:"functions_~.html#index__7E"}]},
{text:"Functions",url:"functions_func.html",children:[
{text:"a",url:"functions_func.html#index_a"},
{text:"b",url:"functions_func_b.html#index_b"},
{text:"c",url:"functions_func_c.html#index_c"},
{text:"d",url:"functions_func_d.html#index_d"},
{text:"e",url:"functions_func_e.html#index_e"},
{text:"f",url:"functions_func_f.html#index_f"},
{text:"g",url:"functions_func_g.html#index_g"},
{text:"h",url:"functions_func_h.html#index_h"},
{text:"i",url:"functions_func_i.html#index_i"},
{text:"k",url:"functions_func_k.html#index_k"},
{text:"l",url:"functions_func_l.html#index_l"},
{text:"m",url:"functions_func_m.html#index_m"},
{text:"o",url:"functions_func_o.html#index_o"},
{text:"p",url:"functions_func_p.html#index_p"},
{text:"q",url:"functions_func_q.html#index_q"},
{text:"r",url:"functions_func_r.html#index_r"},
{text:"s",url:"functions_func_s.html#index_s"},
{text:"t",url:"functions_func_t.html#index_t"},
{text:"u",url:"functions_func_u.html#index_u"},
{text:"v",url:"functions_func_v.html#index_v"},
{text:"w",url:"functions_func_w.html#index_w"},
{text:"z",url:"functions_func_z.html#index_z"},
{text:"~",url:"functions_func_~.html#index__7E"}]},
{text:"Enumerations",url:"functions_enum.html"},
{text:"Enumerator",url:"functions_eval.html",children:[
{text:"a",url:"functions_eval.html#index_a"},
{text:"b",url:"functions_eval_b.html#index_b"},
{text:"c",url:"functions_eval_c.html#index_c"},
{text:"d",url:"functions_eval_d.html#index_d"},
{text:"e",url:"functions_eval_e.html#index_e"},
{text:"f",url:"functions_eval_f.html#index_f"},
{text:"g",url:"functions_eval_g.html#index_g"},
{text:"h",url:"functions_eval_h.html#index_h"},
{text:"i",url:"functions_eval_i.html#index_i"},
{text:"j",url:"functions_eval_j.html#index_j"},
{text:"k",url:"functions_eval_k.html#index_k"},
{text:"l",url:"functions_eval_l.html#index_l"},
{text:"m",url:"functions_eval_m.html#index_m"},
{text:"n",url:"functions_eval_n.html#index_n"},
{text:"o",url:"functions_eval_o.html#index_o"},
{text:"p",url:"functions_eval_p.html#index_p"},
{text:"q",url:"functions_eval_q.html#index_q"},
{text:"r",url:"functions_eval_r.html#index_r"},
{text:"s",url:"functions_eval_s.html#index_s"},
{text:"t",url:"functions_eval_t.html#index_t"},
{text:"u",url:"functions_eval_u.html#index_u"},
{text:"v",url:"functions_eval_v.html#index_v"},
{text:"w",url:"functions_eval_w.html#index_w"},
{text:"x",url:"functions_eval_x.html#index_x"},
{text:"z",url:"functions_eval_z.html#index_z"}]}]}]}]}
