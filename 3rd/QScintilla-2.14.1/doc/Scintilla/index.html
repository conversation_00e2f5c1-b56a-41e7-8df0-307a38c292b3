<?xml version="1.0"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta name="generator" content="HTML Tidy, see www.w3.org" />
    <meta name="generator" content="SciTE" />
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <meta name="keywords" content="Scintilla, SciTE, Editing Component, Text Editor" />
    <meta name="Description"
    content="www.scintilla.org is the home of the Scintilla editing component and SciTE text editor application." />
    <meta name="Date.Modified" content="20180212" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type="text/css">
        #versionlist {
            margin: 0;
            padding: .5em;
            list-style-type: none;
            color: #FFCC99;
            background: #000000;
        }
        #versionlist li {
            margin-bottom: .5em;
        }
        #menu {
            margin: 0;
            padding: .5em 0;
            list-style-type: none;
            font-size: larger;
            background: #CCCCCC;
        }
        #menu li {
            margin: 0;
            padding: 0 .5em;
            display: inline;
        }
    </style>
    <script type="text/javascript">
   	function IsRemote() {
		var loc = '' + window.location;
		return (loc.indexOf('http:')) != -1 || (loc.indexOf('https:') != -1);
   	}
    </script>
     <title>
       Scintilla and SciTE
     </title>
  </head>
  <body bgcolor="#FFFFFF" text="#000000">
    <table bgcolor="#000000" width="100%" cellspacing="0" cellpadding="0" border="0">
      <tr>
        <td width="256">
          <img src="SciWord.jpg" height="78" width="256" alt="Scintilla" />
        </td>
        <td width="40%" align="left">
          <font color="#FFCC99" size="4"> A free source code editing component for Win32,
          GTK+, and OS X</font>
        </td>
        <td width="40%" align="right">
          <font color="#FFCC99" size="3"> Release version 3.10.1<br />
           Site last modified February 12 2018</font>
        </td>
        <td width="20%">
          &nbsp;
        </td>
      </tr>
    </table>
    <table bgcolor="#000000" width="100%" cellspacing="0" cellpadding="0" border="0">
      <tr>
        <td width="100%" style="background: url(https://www.scintilla.org/SciBreak.jpg) no-repeat;height:150px;">
          &nbsp;
        </td>
      </tr>
    </table>
    <ul id="versionlist">
      <li>Version 3.7.5 adds a Reverse Selected Lines command.
      MSVC 2013 is no longer supported.</li>
      <li>Version 3.7.4 restores performance on GTK+.
      C++11 now required to build and Windows NT 4 is no longer supported.</li>
      <li>Version 3.7.3 fixes problems with GTK+ on Wayland.</li>
      <li>Version 3.7.2 fixes some crashes on GTK+ and Cocoa, commonly at destruction.</li>
      <li>Version 3.7.1 supports accessibility on GTK+ and Cocoa.
      The Scintilla namespace is not exposed in Scintilla.h and some deprecated APIs were removed.</li>
    </ul>
    <ul id="menu">
      <li id="remote1"><a href="https://www.scintilla.org/SciTEImage.html">Screenshot</a></li>
      <li id="remote2"><a href="https://www.scintilla.org/ScintillaDownload.html">Download</a></li>
      <li><a href="https://www.scintilla.org/ScintillaDoc.html">Documentation</a></li>
      <li><a href="https://www.scintilla.org/ScintillaToDo.html">Bugs</a></li>
      <li id="remote3"><a href="https://www.scintilla.org/SciTE.html">SciTE</a></li>
      <li><a href="https://www.scintilla.org/ScintillaHistory.html">History</a></li>
      <li><a href="https://www.scintilla.org/ScintillaRelated.html">Related</a></li>
      <li id="remote4"><a href="https://www.scintilla.org/Privacy.html">Privacy</a></li>
    </ul>
<script type="text/javascript" language="JavaScript"><!--
if (!IsRemote()) { //if NOT remote...
    document.getElementById('remote1').style.display='none';
    document.getElementById('remote2').style.display='none';
    document.getElementById('remote3').style.display='none';
    document.getElementById('remote4').style.display='none';
}
//--></script>
    <p>
       <a href="https://www.scintilla.org/ScintillaDoc.html">Scintilla</a> is a free source code editing component.
       It comes with complete source code and a <a href="https://www.scintilla.org/License.txt">license</a> that
       permits use in any free project or commercial product.
    </p>
    <p>
       As well as features found in standard text editing components, Scintilla includes features
       especially useful when editing and debugging source code.
       These include support for syntax styling, error indicators, code completion and call tips.
       The selection margin can contain markers like those used in debuggers to indicate
       breakpoints and the current line. Styling choices are more open than with many editors,
       allowing the use of proportional fonts, bold and italics, multiple foreground and background
       colours and multiple fonts.
    </p>
    <p>
    <p>
       <a href="https://www.scintilla.org/SciTE.html">SciTE</a> is a SCIntilla based Text Editor. Originally built to
      demonstrate Scintilla, it has grown to be a generally useful editor with facilities for
      building and running programs. It is best used for jobs with simple configurations - I use it
      for building test and demonstration programs as well as SciTE and Scintilla, themselves.
    </p>
    <p>
       Development of Scintilla started as an effort to improve the text editor in PythonWin. After
      being frustrated by problems in the Richedit control used by PythonWin, it looked like the
      best way forward was to write a new edit control. The biggest problem with Richedit and other
      similar controls is that they treat styling changes as important persistent changes to the
      document so they are saved into the undo stack and set the document's dirty flag. For source
      code, styling should not be persisted as it can be mechanically recreated.
    </p>
    <p>
       Scintilla and SciTE are currently available for Intel Win32, OS X, and Linux compatible operating
      systems with GTK+. They have been run on Windows XP, Windows 7, OS X 10.7+, and on Ubuntu 14.04
      with GTK+ 2.24. <a href="https://www.scintilla.org/SciTEImage.html">Here is a screenshot of
      SciTE.</a><br />
    </p>
    <p>
       You can <a href="https://www.scintilla.org/ScintillaDownload.html">download Scintilla.</a>
    </p>
    <p>
       The source code can be downloaded via Mercurial at the Source Forge
	<a href="https://sourceforge.net/projects/scintilla/">Scintilla project page</a>.
    </p>
    <p>
       <a href="https://www.scintilla.org/ScintillaRelated.html">Related sites.</a>
    </p>
    <p>
       <a href="https://www.scintilla.org/ScintillaToDo.html">Bugs and To Do list.</a>
    </p>
    <p>
       <a href="https://www.scintilla.org/ScintillaHistory.html">History and contribution credits.</a>
    </p>
    <p>
       <a href="https://www.scintilla.org/Icons.html">Icons that can be used with Scintilla.</a>
    </p>
    <p>
       The <a href="https://scintilla.sourceforge.io/LongTermDownload.html">LongTerm3</a>
       branch of Scintilla avoids using features from C++14 or later in order to support older systems.
    </p>
    <p>
      Questions and comments about Scintilla should be directed to the
      <a href="https://groups.google.com/forum/#!forum/scintilla-interest">scintilla-interest</a>
      mailing list,
      which is for discussion of Scintilla and related projects, their bugs and future features.
      This is a low traffic list, averaging less than 20 messages per week.
      To avoid spam, only list members can write to the list.
      New versions of Scintilla are announced on scintilla-interest and may also be received by SourceForge
      members by clicking on the Monitor column icon for "scintilla" on
      <a href="https://sourceforge.net/project/showfiles.php?group_id=2439">the downloads page</a>.
      Messages sent to my personal email address that could have been sent to the list
      may receive no response.
      <br />
    </p>
There is a <a href="https://sourceforge.net/projects/scintilla/">Scintilla project page</a>
hosted on
<script type="text/javascript" language="JavaScript">
<!--
if (IsRemote()) {
    document.write('<a href="https://sourceforge.net/projects/scintilla/">');
    document.write('<img src="https://sflogo.sourceforge.net/sflogo.php?group_id=2439&amp;type=8" width="80" height="15" alt="Get Scintilla at SourceForge.net. Fast, secure and Free Open Source software downloads" /></a> ');
} else {
    document.write('<a href="https://sourceforge.net/projects/scintilla/">SourceForge<\/a>');
}
//-->
</script>
<noscript>
<a href="https://sourceforge.net/projects/scintilla/">
<img src="https://sflogo.sourceforge.net/sflogo.php?group_id=2439&amp;type=8" width="80" height="15" alt="Get Scintilla at SourceForge.net. Fast, secure and Free Open Source software downloads" /></a>
</noscript>
  </body>
</html>

