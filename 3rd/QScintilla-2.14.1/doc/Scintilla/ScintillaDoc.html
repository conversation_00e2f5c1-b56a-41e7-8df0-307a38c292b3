<?xml version="1.0"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta name="generator"
    content="HTML Tidy for Windows (vers 1st August 2002), see www.w3.org" />
    <meta name="generator" content="SciTE" />
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />

    <title>Scintilla Documentation</title>

    <style type="text/css">
<!--
/*<![CDATA[*/
	CODE { font-weight: bold; font-family: <PERSON><PERSON>,Consolas,Bitstream Vera Sans Mono,Courier New,monospace; }
	A:visited { color: blue; }
	A:hover { text-decoration: underline ! important; }
	A.message { text-decoration: none; font-weight: bold; font-family: <PERSON><PERSON>,<PERSON>,Bitstream Vera Sans Mono,Courier New,monospace; }
	<PERSON><PERSON>see<PERSON><PERSON> { text-decoration: none; font-weight: bold; font-family: <PERSON><PERSON>,<PERSON>,Bitstream Vera Sans Mono,Courier New,monospace; }
	A.toc { text-decoration: none; }
	A.jump { text-decoration: none; }
	LI.message { text-decoration: none; font-weight: bold; font-family: Menlo,Consolas,Bitstream Vera Sans Mono,Courier New,monospace; }
	H2 { background: #E0EAFF; }

	table {
		border: 0px;
		border-collapse: collapse;
	}

	table.categories {
		border: 0px;
		border-collapse: collapse;
	}
	table.categories td {
		padding: 4px 12px;
	}

	table.standard {
		border-collapse: collapse;
	}
	table.standard th {
		background: #404040;
		color: #FFFFFF;
		padding: 1px 5px 1px 5px;
	}
	table.standard tr:nth-child(odd) {background: #D7D7D7}
	table.standard tr:nth-child(even) {background: #F0F0F0}
	table.standard td {
		padding: 1px 5px 1px 5px;
	}

	.S0 {
		color: #808080;
	}
	.S2 {
		font-family: Georgia, 'DejaVu Serif';
		color: #007F00;
		font-size: 9pt;
	}
	.S3 {
		font-family: Georgia, 'DejaVu Serif';
		color: #3F703F;
		font-size: 9pt;
	}
	.S4 {
		color: #007F7F;
	}
	.S5 {
		font-weight: bold;
		color: #00007F;
	}
	.S9 {
		color: #7F7F00;
	}
	.S10 {
		font-weight: bold;
		color: #000000;
	}
	.S17 {
		font-family: Georgia, 'DejaVu Serif';
		color: #3060A0;
		font-size: 9pt;
	}
	DIV.highlighted {
		background: #F7FCF7;
		border: 1px solid #C0D7C0;
		margin: 0.3em 3em;
		padding: 0.3em 0.6em;
		font-family: 'Verdana';
		color: #000000;
		font-size: 10pt;
	}
	.provisional {
		background: #FFB000;
        }
	.parameter {
		font-style:italic;
        }
/*]]>*/
-->
    </style>
  </head>

  <body bgcolor="#FFFFFF" text="#000000">
    <table bgcolor="#000000" width="100%" cellspacing="0" cellpadding="0" border="0"
    summary="Banner">
      <tr>
        <td><img src="SciTEIco.png" border="3" height="64" width="64" alt="Scintilla icon" /></td>

        <td><a href="index.html"
        style="color:white;text-decoration:none;font-size:200%">Scintilla</a></td>
      </tr>
    </table>

    <h1>Scintilla Documentation</h1>

    <p>Last edited 6 June 2018 NH</p>

    <p>There is <a class="jump" href="Design.html">an overview of the internal design of
    Scintilla</a>.<br />
     <a class="jump" href="ScintillaUsage.html">Some notes on using Scintilla</a>.<br />
     <a class="jump" href="Steps.html">How to use the Scintilla Edit Control on Windows</a>.<br />
     <a class="jump" href="https://www.scintilla.org/dmapp.zip">A simple sample using Scintilla from
    C++ on Windows</a>.<br />
     <a class="jump" href="https://www.scintilla.org/SciTry.vb">A simple sample using Scintilla from
    Visual Basic</a>.<br />
     <a class="jump" href="https://www.scintilla.org/bait.zip">Bait is a tiny sample using Scintilla
     on GTK+</a>.<br />
     <a class="jump" href="Lexer.txt">A detailed description of how to write a lexer, including a
    discussion of folding</a>.<br />
     <a class="jump" href="http://sphere.sourceforge.net/flik/docs/scintilla-container_lexer.html">
     How to implement a lexer in the container</a>.<br />
     <a class="jump" href="http://sphere.sourceforge.net/flik/docs/scintilla-folding.html">
     How to implement folding</a>.<br />
     <a class="jump" href="https://bitbucket.org/StarFire/scintilla-doc/downloads/Scintilla-var'aq-Tutorial.pdf">
     Beginner's Guide to lexing and folding</a>.<br />
     <a class="jump" href="LPegLexer.html">How to write lexers in Lua</a>.<br />
     The <a class="jump" href="SciCoding.html">coding style</a> used in Scintilla and SciTE is
    worth following if you want to contribute code to Scintilla but is not compulsory.</p>

    <h2>Introduction</h2>

    <p>The Windows version of Scintilla is a Windows Control. As such, its primary programming
    interface is through Windows messages. Early versions of Scintilla emulated much of the API
    defined by the standard Windows Edit and RichEdit controls but those APIs are now deprecated in
    favour of Scintilla's own, more consistent API. In addition to messages performing the actions
    of a normal Edit control, Scintilla allows control of syntax styling, folding, markers, autocompletion
    and call tips.</p>

    <p>The GTK+ version also uses messages in a similar way to the Windows version. This is
    different to normal GTK+ practice but made it easier to implement rapidly.</p>

    <p>Scintilla also builds with Cocoa on OS X, with Qt, and with curses, and follows the conventions of
    those platforms.</p>

    <p>Scintilla does not properly support right-to-left languages like Arabic and Hebrew.
    While text in these languages may appear correct, it is not possible to interact with this text
    as is normal with other editing components.</p>

    <p>This documentation describes the individual messages and notifications used by Scintilla. It
    does not describe how to link them together to form a useful editor. For now, the best way to
    work out how to develop using Scintilla is to see how SciTE uses it. SciTE exercises most of
    Scintilla's facilities.</p>

    <p>In the descriptions that follow, the messages are described as function calls with zero, one
    or two arguments. These two arguments are the standard <code>wParam</code> and
    <code>lParam</code> familiar to Windows programmers. These parameters are integers that
    are large enough to hold pointers, and the return value is also an integer large enough to contain a
    pointer.
    Although the commands only use the
    arguments described, because all messages have two arguments whether Scintilla uses them or
    not, it is strongly recommended that any unused arguments are set to 0. This allows future
    enhancement of messages without the risk of breaking existing code. Common argument types
    are:</p>

    <table class="standard" summary="Common argument types">
      <tbody valign="top">
        <tr>
          <th align="left"><code>bool</code></th>

          <td>Arguments expect the values 0 for <code>false</code> and 1 for
          <code>true</code>.</td>
        </tr>

        <tr>
          <th align="left"><code>int</code></th>

          <td>Arguments are 32-bit or 64-bit signed integers depending on the platform.
          Equivalent to <code>intptr_t</code>.</td>
        </tr>

        <tr>
          <th align="left"><code>const&nbsp;char&nbsp;*</code></th>

          <td>Arguments point at text that is being passed to Scintilla but not modified. The text
          may be zero terminated or another argument may specify the character count, the
          description will make this clear.</td>
        </tr>

        <tr>
          <th align="left"><code>char *</code></th>

          <td>Arguments point at text buffers that Scintilla will fill with text. In some cases,
          another argument will tell Scintilla the buffer size. In others, you must make sure that
          the buffer is big enough to hold the requested text. If a NULL pointer (0) is passed
          then, for SCI_* calls, the length that should be allocated, not including any terminating
          NUL, is returned. Some calls (marked "NUL-terminated") add a NUL character to the result but other calls do
          not: to generically handle both types, allocate one more byte than indicated and set it to NUL.</td>
        </tr>

        <tr>
          <th align="left" id="colour"><code>colour</code></th>

          <td>Colours are set using the RGB format (Red, Green, Blue). The intensity of each colour
          is set in the range 0 to 255. If you have three such intensities, they are combined as:
          red | (green &lt;&lt; 8) | (blue &lt;&lt; 16). If you set all intensities to 255, the
          colour is white. If you set all intensities to 0, the colour is black. When you set a
          colour, you are making a request. What you will get depends on the capabilities of the
          system and the current screen mode.</td>
        </tr>

        <tr>
          <th align="left" id="alpha"><code>alpha</code></th>

          <td>Translucency is set using an alpha value.
                        Alpha ranges from 0 (SC_ALPHA_TRANSPARENT) which is completely transparent to
                               255 (SC_ALPHA_OPAQUE) which is opaque. The value 256 (SC_ALPHA_NOALPHA)
                               is opaque and uses code that is not alpha-aware and may be faster. Not all platforms support
                               translucency and only some Scintilla features implement translucency.
                               The default alpha value for most features is SC_ALPHA_NOALPHA.</td>
        </tr>

        <tr>
          <th align="left"><code>&lt;unused&gt;</code></th>

          <td>This is an unused argument. Setting it to 0 will ensure compatibility with future
          enhancements.</td>
        </tr>
      </tbody>
    </table>

    <h2 id="MessageCategories">Contents</h2>

    <table class="categories" summary="Message categories">
      <tbody>
        <tr>
          <td>&cir; <a class="toc" href="#TextRetrievalAndModification">Text retrieval and
          modification</a></td>

          <td>&cir; <a class="toc" href="#Searching">Searching and replacing</a></td>

          <td>&cir; <a class="toc" href="#Overtype">Overtype</a></td>

        </tr>

        <tr>
          <td>&cir; <a class="toc" href="#CutCopyAndPaste">Cut, copy and paste</a></td>

          <td>&cir; <a class="toc" href="#ErrorHandling">Error handling</a></td>

          <td>&cir; <a class="toc" href="#UndoAndRedo">Undo and Redo</a></td>

        </tr>

        <tr>
          <td>&cir; <a class="toc" href="#SelectionAndInformation">Selection and information</a></td>

          <td>&cir; <a class="toc" href="#ByCharacterOrCodeUnit">By character or UTF-16 code unit</a></td>

          <td>&cir; <a class="toc" href="#MultipleSelectionAndVirtualSpace">Multiple Selection and Virtual Space</a></td>
          scrolling</a></td>

        </tr>

        <tr>
          <td>&cir; <a class="toc" href="#ScrollingAndAutomaticScrolling">Scrolling and automatic

          <td>&cir; <a class="toc" href="#WhiteSpace">White space</a></td>

          <td>&cir; <a class="toc" href="#Cursor">Cursor</a></td>

        </tr>

        <tr>
          <td>&cir; <a class="toc" href="#MouseCapture">Mouse capture</a></td>

          <td>&cir; <a class="toc" href="#LineEndings">Line endings</a></td>

          <td>&cir; <a class="toc" href="#Words">Words</a></td>

        </tr>

        <tr>
          <td>&cir; <a class="toc" href="#Styling">Styling</a></td>

          <td>&cir; <a class="toc" href="#StyleDefinition">Style definition</a></td>

          <td>&cir; <a class="toc" href="#CaretAndSelectionStyles">Caret, selection, and hotspot styles</a></td>

        </tr>

        <tr>
          <td>&cir; <a class="toc" href="#CharacterRepresentations">Character representations</a></td>

          <td>&cir; <a class="toc" href="#Margins">Margins</a></td>

          <td>&cir; <a class="toc" href="#Annotations">Annotations</a></td>

        </tr>

        <tr>
          <td>&cir; <a class="toc" href="#OtherSettings">Other settings</a></td>

          <td>&cir; <a class="toc" href="#BraceHighlighting">Brace highlighting</a></td>

          <td>&cir; <a class="toc" href="#TabsAndIndentationGuides">Tabs and Indentation
          Guides</a></td>

        </tr>

        <tr>
          <td>&cir; <a class="toc" href="#Markers">Markers</a></td>

          <td>&cir; <a class="toc" href="#Indicators">Indicators</a></td>

          <td>&cir; <a class="toc" href="#Autocompletion">Autocompletion</a></td>

        </tr>

        <tr>
          <td>&cir; <a class="toc" href="#UserLists">User lists</a></td>

          <td>&cir; <a class="toc" href="#CallTips">Call tips</a></td>

          <td>&cir; <a class="toc" href="#KeyboardCommands">Keyboard commands</a></td>

        </tr>

        <tr>
          <td>&cir; <a class="toc" href="#KeyBindings">Key bindings</a></td>

          <td>&cir; <a class="toc" href="#PopupEditMenu">Popup edit menu</a></td>

          <td>&cir; <a class="toc" href="#MacroRecording">Macro recording</a></td>

        </tr>

        <tr>
          <td>&cir; <a class="toc" href="#Printing">Printing</a></td>

          <td>&cir; <a class="toc" href="#DirectAccess">Direct access</a></td>

          <td>&cir; <a class="toc" href="#MultipleViews">Multiple views</a></td>

        </tr>

        <tr>
          <td>&cir; <a class="toc" href="#BackgroundLoadSave">Background loading and saving</a></td>

          <td>&cir; <a class="toc" href="#Folding">Folding</a></td>

          <td>&cir; <a class="toc" href="#LineWrapping">Line wrapping</a></td>

        </tr>

        <tr>
          <td>&cir; <a class="toc" href="#Zooming">Zooming</a></td>

          <td>&cir; <a class="toc" href="#LongLines">Long lines</a></td>

          <td>&cir; <a class="toc" href="#Accessibility">Accessibility</a></td>

        </tr>

        <tr>
          <td>&cir; <a class="toc" href="#Lexer">Lexer</a></td>

          <td>&cir; <a class="toc" href="#LexerObjects">Lexer objects</a></td>

          <td>&cir; <a class="toc" href="#Notifications">Notifications</a></td>

        </tr>

        <tr>
          <td>&cir; <a class="toc" href="#Images">Images</a></td>

          <td>&cir; <a class="toc" href="#GTK">GTK+</a></td>

          <td>&cir; <a class="toc" href="#ProvisionalMessages"><span class="provisional">Provisional messages</span></a></td>

        </tr>
        <tr>
          <td>&cir; <a class="toc" href="#DeprecatedMessages">Deprecated messages</a></td>

          <td>&cir; <a class="toc" href="#EditMessagesNeverSupportedByScintilla">Edit messages never
          supported by Scintilla</a></td>

          <td>&cir; <a class="toc" href="#RemovedFeatures">Removed features</a></td>

        </tr>
        <tr>
          <td>&cir; <a class="toc" href="#BuildingScintilla">Building Scintilla</a></td>

        </tr>
      </tbody>
    </table>

    <p>Messages with names of the form <code>SCI_SETxxxxx</code> often have a companion
    <code>SCI_GETxxxxx</code>. To save tedious repetition, if the <code>SCI_GETxxxxx</code> message
    returns the value set by the <code>SCI_SETxxxxx</code> message, the <code>SET</code> routine is
    described and the <code>GET</code> routine is left to your imagination.</p>

    <h2 id="TextRetrievalAndModification">Text retrieval and modification</h2>

    <p>Each byte in a Scintilla document is associated with a byte of styling
    information. The combination of a character byte and a style byte is called a cell. Style bytes
    are interpreted an index into an array of styles.</p>

    <p>In this document, 'character' normally refers to a byte even when multi-byte characters are used.
    Lengths measure the numbers of bytes, not the amount of characters in those bytes.</p>

    <p>Positions within the Scintilla document refer to a character or the gap before that
    character. The first character in a document is 0, the second 1 and so on. If a document
    contains <code>nLen</code> characters, the last character is numbered <code>nLen</code>-1.
    The caret exists between character positions and can be located from before the first character (0)
    to after the last character (<code>nLen</code>).</p>

    <p>There are places where the caret can not go where two character bytes make up one character.
    This occurs when a DBCS character from a language like Japanese is included in the document or
    when line ends are marked with the CP/M standard of a carriage return followed by a line feed.
    The <code>INVALID_POSITION</code> constant (-1) represents an invalid position within the
    document.</p>

    <p>All lines of text in Scintilla are the same height, and this height is calculated from the
    largest font in any current style. This restriction is for performance; if lines differed in
    height then calculations involving positioning of text would require the text to be styled
    first.</p>
    <code><a class="message" href="#SCI_GETTEXT">SCI_GETTEXT(int length, char *text) &rarr; int</a><br />
     <a class="message" href="#SCI_SETTEXT">SCI_SETTEXT(&lt;unused&gt;, const char *text)</a><br />
     <a class="message" href="#SCI_SETSAVEPOINT">SCI_SETSAVEPOINT</a><br />
     <a class="message" href="#SCI_GETLINE">SCI_GETLINE(int line, char *text) &rarr; int</a><br />
     <a class="message" href="#SCI_REPLACESEL">SCI_REPLACESEL(&lt;unused&gt;, const char
    *text)</a><br />
     <a class="message" href="#SCI_SETREADONLY">SCI_SETREADONLY(bool readOnly)</a><br />
     <a class="message" href="#SCI_GETREADONLY">SCI_GETREADONLY &rarr; bool</a><br />
     <a class="message" href="#SCI_GETTEXTRANGE">SCI_GETTEXTRANGE(&lt;unused&gt;, Sci_TextRange *tr) &rarr; int</a><br />
     <a class="message" href="#SCI_ALLOCATE">SCI_ALLOCATE(int bytes)</a><br />
     <a class="message" href="#SCI_ADDTEXT">SCI_ADDTEXT(int length, const char *text)</a><br />
     <a class="message" href="#SCI_ADDSTYLEDTEXT">SCI_ADDSTYLEDTEXT(int length, cell *c)</a><br />
     <a class="message" href="#SCI_APPENDTEXT">SCI_APPENDTEXT(int length, const char *text)</a><br />
     <a class="message" href="#SCI_INSERTTEXT">SCI_INSERTTEXT(int pos, const char *text)</a><br />
     <a class="message" href="#SCI_CHANGEINSERTION">SCI_CHANGEINSERTION(int length, const char *text)</a><br />
     <a class="message" href="#SCI_CLEARALL">SCI_CLEARALL</a><br />
     <a class="message" href="#SCI_DELETERANGE">SCI_DELETERANGE(int start, int lengthDelete)</a><br />
     <a class="message" href="#SCI_CLEARDOCUMENTSTYLE">SCI_CLEARDOCUMENTSTYLE</a><br />
     <a class="message" href="#SCI_GETCHARAT">SCI_GETCHARAT(int pos) &rarr; int</a><br />
     <a class="message" href="#SCI_GETSTYLEAT">SCI_GETSTYLEAT(int pos) &rarr; int</a><br />
     <a class="message" href="#SCI_GETSTYLEDTEXT">SCI_GETSTYLEDTEXT(&lt;unused&gt;, Sci_TextRange *tr) &rarr; int</a><br />
     <a class="message" href="#SCI_RELEASEALLEXTENDEDSTYLES">SCI_RELEASEALLEXTENDEDSTYLES</a><br />
     <a class="message" href="#SCI_ALLOCATEEXTENDEDSTYLES">SCI_ALLOCATEEXTENDEDSTYLES(int numberStyles) &rarr; int</a><br />
     <a class="message" href="#SCI_TARGETASUTF8">SCI_TARGETASUTF8(&lt;unused&gt;, char *s) &rarr; int</a><br />
     <a class="message" href="#SCI_ENCODEDFROMUTF8">SCI_ENCODEDFROMUTF8(const char *utf8, char *encoded) &rarr; int</a><br />
     <a class="message" href="#SCI_SETLENGTHFORENCODE">SCI_SETLENGTHFORENCODE(int bytes)</a><br />
    </code>

    <p><b id="SCI_GETTEXT">SCI_GETTEXT(int length, char *text NUL-terminated) &rarr; int</b><br />
     This returns <code class="parameter">length</code>-1 characters of text from the start of the document plus one
    terminating 0 character. To collect all the text in a document, use <code>SCI_GETLENGTH</code>
    to get the number of characters in the document (<code>nLen</code>), allocate a character
    buffer of length <code>nLen+1</code> bytes, then call <code>SCI_GETTEXT(nLen+1, char
    *text)</code>. If the text argument is 0 then the length that should be allocated to store the
    entire document is returned.
    If you then save the text, you should use <code>SCI_SETSAVEPOINT</code> to mark
    the text as unmodified.</p>

    <p>See also: <code><a class="seealso" href="#SCI_GETSELTEXT">SCI_GETSELTEXT</a>,
    <a class="seealso" href="#SCI_GETCURLINE">SCI_GETCURLINE</a>,
    <a class="seealso" href="#SCI_GETLINE">SCI_GETLINE</a>,
    <a class="seealso "href="#SCI_GETSTYLEDTEXT">SCI_GETSTYLEDTEXT</a>,
    <a class="seealso" href="#SCI_GETTEXTRANGE">SCI_GETTEXTRANGE</a></code></p>

    <p><b id="SCI_SETTEXT">SCI_SETTEXT(&lt;unused&gt;, const char *text)</b><br />
     This replaces all the text in the document with the zero terminated text string you pass
    in.</p>

    <p><b id="SCI_SETSAVEPOINT">SCI_SETSAVEPOINT</b><br />
     This message tells Scintilla that the current state of the document is unmodified. This is
    usually done when the file is saved or loaded, hence the name "save point". As Scintilla
    performs undo and redo operations, it notifies the container that it has entered or left the
    save point with <code><a class="message"
    href="#SCN_SAVEPOINTREACHED">SCN_SAVEPOINTREACHED</a></code> and <code><a class="message"
    href="#SCN_SAVEPOINTLEFT">SCN_SAVEPOINTLEFT</a></code> <a class="jump"
    href="#Notifications">notification messages</a>, allowing the container to know if the file
    should be considered dirty or not.</p>

    <p>See also: <code><a class="message" href="#SCI_EMPTYUNDOBUFFER">SCI_EMPTYUNDOBUFFER</a>, <a
    class="message" href="#SCI_GETMODIFY">SCI_GETMODIFY</a></code></p>

    <p><b id="SCI_GETLINE">SCI_GETLINE(int line, char *text) &rarr; int</b><br />
     This fills the buffer defined by text with the contents of the nominated line (lines start at
    0). The buffer is not terminated by a 0 character. It is up to you to make sure that the buffer
    is long enough for the text, use <a class="message"
    href="#SCI_LINELENGTH"><code>SCI_LINELENGTH(int line)</code></a>. The returned value is the
    number of characters copied to the buffer. The returned text includes any end of line
    characters. If you ask for a line number outside the range of lines in the document, 0
    characters are copied. If the text argument is 0 then the length that should be allocated
    to store the entire line is returned.</p>

    <p>See also: <code><a class="seealso" href="#SCI_GETCURLINE">SCI_GETCURLINE</a>,
    <a class="seealso" href="#SCI_GETSELTEXT">SCI_GETSELTEXT</a>,
    <a class="seealso" href="#SCI_GETTEXTRANGE">SCI_GETTEXTRANGE</a>,
    <a class="seealso" href="#SCI_GETSTYLEDTEXT">SCI_GETSTYLEDTEXT</a>,
    <a class="seealso" href="#SCI_GETTEXT">SCI_GETTEXT</a></code></p>

    <p><b id="SCI_REPLACESEL">SCI_REPLACESEL(&lt;unused&gt;, const char *text)</b><br />
     The currently selected text between the <a class="jump" href="#SelectionAndInformation">anchor
    and the current position</a> is replaced by the 0 terminated text string. If the anchor and
    current position are the same, the text is inserted at the caret position. The caret is
    positioned after the inserted text and the caret is scrolled into view.</p>

    <p><b id="SCI_SETREADONLY">SCI_SETREADONLY(bool readOnly)</b><br />
     <b id="SCI_GETREADONLY">SCI_GETREADONLY &rarr; bool</b><br />
     These messages set and get the read-only flag for the document. If you mark a document as read
    only, attempts to modify the text cause the <a class="message"
    href="#SCN_MODIFYATTEMPTRO"><code>SCN_MODIFYATTEMPTRO</code></a> notification.</p>

    <p><b id="SCI_GETTEXTRANGE">SCI_GETTEXTRANGE(&lt;unused&gt;, <a class="jump" href="#Sci_TextRange">Sci_TextRange</a> *tr) &rarr; int</b><br />
     This collects the text between the positions <code>cpMin</code> and <code>cpMax</code> and
    copies it to <code>lpstrText</code> (see <code>struct Sci_TextRange</code> in
    <code>Scintilla.h</code>). If <code>cpMax</code> is -1, text is returned to the end of the
    document. The text is 0 terminated, so you must supply a buffer that is at least 1 character
    longer than the number of characters you wish to read. The return value is the length of the
    returned text not including the terminating 0.</p>

    <p>See also: <code><a class="seealso" href="#SCI_GETSELTEXT">SCI_GETSELTEXT</a>,
    <a class="seealso" href="#SCI_GETLINE">SCI_GETLINE</a>,
    <a class="seealso" href="#SCI_GETCURLINE">SCI_GETCURLINE</a>,
    <a class="seealso" href="#SCI_GETSTYLEDTEXT">SCI_GETSTYLEDTEXT</a>,
    <a class="seealso" href="#SCI_GETTEXT">SCI_GETTEXT</a></code></p>

    <p><b id="SCI_GETSTYLEDTEXT">SCI_GETSTYLEDTEXT(&lt;unused&gt;, <a class="jump" href="#Sci_TextRange">Sci_TextRange</a> *tr) &rarr; int</b><br />
     This collects styled text into a buffer using two bytes for each cell, with the character at
    the lower address of each pair and the style byte at the upper address. Characters between the
    positions <code>cpMin</code> and <code>cpMax</code> are copied to <code>lpstrText</code> (see
    <code>struct Sci_TextRange</code> in <code>Scintilla.h</code>). Two 0 bytes are added to the end of
    the text, so the buffer that <code>lpstrText</code> points at must be at least
    <code>2*(cpMax-cpMin)+2</code> bytes long. No check is made for sensible values of
    <code>cpMin</code> or <code>cpMax</code>. Positions outside the document return character codes
    and style bytes of 0.</p>

    <p>See also: <code><a class="seealso" href="#SCI_GETSELTEXT">SCI_GETSELTEXT</a>,
    <a class="seealso" href="#SCI_GETLINE">SCI_GETLINE</a>,
    <a class="seealso" href="#SCI_GETCURLINE">SCI_GETCURLINE</a>,
    <a class="seealso" href="#SCI_GETTEXTRANGE">SCI_GETTEXTRANGE</a>,
    <a class="seealso" href="#SCI_GETTEXT">SCI_GETTEXT</a></code></p>

    <p><b id="SCI_ALLOCATE">SCI_ALLOCATE(int bytes)</b><br />
     Allocate a document buffer large enough to store a given number of bytes.
     The document will not be made smaller than its current contents.</p>

    <p><b id="SCI_ADDTEXT">SCI_ADDTEXT(int length, const char *text)</b><br />
     This inserts the first <code class="parameter">length</code> characters from the string
     <code class="parameter">text</code>
    at the current position. This will include any 0's in the string that you might have expected
    to stop the insert operation. The current position is set at the end of the inserted text,
    but it is not scrolled into view.</p>

    <p><b id="SCI_ADDSTYLEDTEXT">SCI_ADDSTYLEDTEXT(int length, cell *c)</b><br />
     This behaves just like <code>SCI_ADDTEXT</code>, but inserts styled text.</p>

    <p><b id="SCI_APPENDTEXT">SCI_APPENDTEXT(int length, const char *text)</b><br />
     This adds the first <code class="parameter">length</code> characters from the string
     <code class="parameter">text</code> to the end
    of the document. This will include any 0's in the string that you might have expected to stop
    the operation. The current selection is not changed and the new text is not scrolled into
    view.</p>

    <p><b id="SCI_INSERTTEXT">SCI_INSERTTEXT(int pos, const char *text)</b><br />
     This inserts the zero terminated <code class="parameter">text</code> string at position <code class="parameter">pos</code> or at
    the current position if <code class="parameter">pos</code> is -1. If the current position is after the insertion point
    then it is moved along with its surrounding text but no scrolling is performed.</p>

    <p><b id="SCI_CHANGEINSERTION">SCI_CHANGEINSERTION(int length, const char *text)</b><br />
     This may only be called from a <a class="message" href="#SC_MOD_INSERTCHECK">SC_MOD_INSERTCHECK</a>
     notification handler and will change the text being inserted to that provided.</p>

    <p><b id="SCI_CLEARALL">SCI_CLEARALL</b><br />
     Unless the document is read-only, this deletes all the text.</p>

    <p><b id="SCI_DELETERANGE">SCI_DELETERANGE(int start, int lengthDelete)</b><br />
     Deletes a range of text in the document.</p>

    <p><b id="SCI_CLEARDOCUMENTSTYLE">SCI_CLEARDOCUMENTSTYLE</b><br />
     When wanting to completely restyle the document, for example after choosing a lexer, the
    <code>SCI_CLEARDOCUMENTSTYLE</code> can be used to clear all styling information and reset the
    folding state.</p>

    <p><b id="SCI_GETCHARAT">SCI_GETCHARAT(int pos) &rarr; int</b><br />
     This returns the character at <code class="parameter">pos</code> in the document or 0 if <code class="parameter">pos</code> is
    negative or past the end of the document.</p>

    <p><b id="SCI_GETSTYLEAT">SCI_GETSTYLEAT(int pos) &rarr; int</b><br />
     This returns the style at <code class="parameter">pos</code> in the document, or 0 if <code class="parameter">pos</code> is
    negative or past the end of the document.</p>

    <p><b id="SCI_RELEASEALLEXTENDEDSTYLES">SCI_RELEASEALLEXTENDEDSTYLES</b><br />
    <b id="SCI_ALLOCATEEXTENDEDSTYLES">SCI_ALLOCATEEXTENDEDSTYLES(int numberStyles) &rarr; int</b><br />
     Extended styles are used for features like textual margins and annotations as well as internally by Scintilla.
     They are outside the range 0..255 used for the styles bytes associated with document bytes.
     These functions manage the use of extended styles to ensures that components cooperate in defining styles.
     <code>SCI_RELEASEALLEXTENDEDSTYLES</code> releases any extended styles allocated by the container.
     <code>SCI_ALLOCATEEXTENDEDSTYLES</code> allocates a range of style numbers after the byte style values and returns
     the number of the first allocated style.
     Ranges for margin and annotation styles should be allocated before calling
     <a class="seealso" href="#SCI_MARGINSETSTYLEOFFSET">SCI_MARGINSETSTYLEOFFSET</a> or
     <a class="seealso" href="#SCI_ANNOTATIONSETSTYLEOFFSET">SCI_ANNOTATIONSETSTYLEOFFSET</a>.</p>

    <p><b id="Sci_TextRange">Sci_TextRange</b> and <b id="Sci_CharacterRange">Sci_CharacterRange</b><br />
     These structures are defined to be exactly the same shape as the Win32 <code>TEXTRANGE</code>
    and <code>CHARRANGE</code>, so that older code that treats Scintilla as a RichEdit will
    work.</p>
    <p>In a future release the type <code>Sci_PositionCR</code> will be redefined to be 64-bits when Scintilla is
    built for 64-bits on all platforms.</p>
<pre>
typedef long Sci_PositionCR;

struct Sci_CharacterRange {
    Sci_PositionCR cpMin;
    Sci_PositionCR cpMax;
};

struct Sci_TextRange {
    struct Sci_CharacterRange chrg;
    char *lpstrText;
};
</pre>

    <h3 id="EncodedAccess">Specific to GTK+, Cocoa and Windows only: Access to encoded text</h3>

    <p><b id="SCI_TARGETASUTF8">SCI_TARGETASUTF8(&lt;unused&gt;, char *s) &rarr; int</b><br />
     This method retrieves the value of the target encoded as UTF-8 which is the default
     encoding of GTK+ so is useful for retrieving text for use in other parts of the user interface,
     such as find and replace dialogs. The length of the encoded text in bytes is returned.
     Cocoa uses UTF-16 which is easily converted from UTF-8 so this method can be used to perform the
     more complex work of transcoding from the various encodings supported.
    </p>

    <p><b id="SCI_ENCODEDFROMUTF8">SCI_ENCODEDFROMUTF8(const char *utf8, char *encoded) &rarr; int</b><br />
     <b id="SCI_SETLENGTHFORENCODE">SCI_SETLENGTHFORENCODE(int bytes)</b><br />
     <code>SCI_ENCODEDFROMUTF8</code> converts a UTF-8 string into the document's
     encoding which is useful for taking the results of a find dialog, for example, and receiving
     a string of bytes that can be searched for in the document. Since the text can contain nul bytes,
     the <code>SCI_SETLENGTHFORENCODE</code> method can be used to set the
     length that will be converted. If set to -1, the length is determined by finding a nul byte.
     The length of the converted string is returned.
    </p>


    <h2 id="Searching">Searching</h2>
    <p>
    There are methods to search for text and for regular expressions.
    Most applications should use
    <a class="seealso" href="#SCI_SEARCHINTARGET">SCI_SEARCHINTARGET</a>
    as the basis for their search implementations.
    Other calls augment this or were implemented before <code>SCI_SEARCHINTARGET</code>.
    </p>
    <p>
    The base regular expression support
    is limited and should only be used for simple cases and initial development.
    The C++ runtime &lt;regex&gt; library may be used by setting the <code>SCFIND_CXX11REGEX</code> search flag.
    The C++11 &lt;regex&gt; support may be disabled by
    compiling Scintilla with <code>NO_CXX11_REGEX</code> defined.
    A different regular expression
    library can be <a class="jump" href="#AlternativeRegEx">integrated into Scintilla</a>
    or can be called from the container using direct access to the buffer contents through
    <a class="seealso" href="#SCI_GETCHARACTERPOINTER">SCI_GETCHARACTERPOINTER</a>.
    </p>

    <h3 id="SearchAndReplaceUsingTheTarget">Search and replace using the target</h3>

    <p>Searching can be performed within the target range with <code>SCI_SEARCHINTARGET</code>,
    which uses a counted string to allow searching for null characters. It returns the
    position of the start of the matching text range or -1 for failure, in which case the target is not moved. The flags used by
    <code>SCI_SEARCHINTARGET</code> such as <code>SCFIND_MATCHCASE</code>,
    <code>SCFIND_WHOLEWORD</code>, <code>SCFIND_WORDSTART</code>, and <code>SCFIND_REGEXP</code>
    can be set with <code>SCI_SETSEARCHFLAGS</code>.</p>
    <code><a class="message" href="#SCI_SETTARGETSTART">SCI_SETTARGETSTART(int start)</a><br />
     <a class="message" href="#SCI_GETTARGETSTART">SCI_GETTARGETSTART &rarr; position</a><br />
     <a class="message" href="#SCI_SETTARGETEND">SCI_SETTARGETEND(int end)</a><br />
     <a class="message" href="#SCI_GETTARGETEND">SCI_GETTARGETEND &rarr; position</a><br />
     <a class="message" href="#SCI_SETTARGETRANGE">SCI_SETTARGETRANGE(int start, int end)</a><br />
     <a class="message" href="#SCI_TARGETFROMSELECTION">SCI_TARGETFROMSELECTION</a><br />
     <a class="message" href="#SCI_TARGETWHOLEDOCUMENT">SCI_TARGETWHOLEDOCUMENT</a><br />
     <a class="message" href="#SCI_SETSEARCHFLAGS">SCI_SETSEARCHFLAGS(int searchFlags)</a><br />
     <a class="message" href="#SCI_GETSEARCHFLAGS">SCI_GETSEARCHFLAGS &rarr; int</a><br />
     <a class="message" href="#SCI_SEARCHINTARGET">SCI_SEARCHINTARGET(int length, const char *text) &rarr; int</a><br />
     <a class="message" href="#SCI_GETTARGETTEXT">SCI_GETTARGETTEXT(&lt;unused&gt;, char *text) &rarr; int</a><br />
     <a class="message" href="#SCI_REPLACETARGET">SCI_REPLACETARGET(int length, const char *text) &rarr; int</a><br />
     <a class="message" href="#SCI_REPLACETARGETRE">SCI_REPLACETARGETRE(int length, const char *text) &rarr; int</a><br />
     <a class="message" href="#SCI_GETTAG">SCI_GETTAG(int tagNumber, char *tagValue) &rarr; int</a><br />
    </code>

    <p><b id="SCI_SETTARGETSTART">SCI_SETTARGETSTART(int start)</b><br />
     <b id="SCI_GETTARGETSTART">SCI_GETTARGETSTART &rarr; position</b><br />
     <b id="SCI_SETTARGETEND">SCI_SETTARGETEND(int end)</b><br />
     <b id="SCI_GETTARGETEND">SCI_GETTARGETEND &rarr; position</b><br />
     <b id="SCI_SETTARGETRANGE">SCI_SETTARGETRANGE(int start, int end)</b><br />
     These functions set and return the start and end of the target. When searching
     you can set start greater than end to find the last matching text in the
    target rather than the first matching text. The target is also set by a successful
    <code>SCI_SEARCHINTARGET</code>.</p>

     <p><b id="SCI_TARGETFROMSELECTION">SCI_TARGETFROMSELECTION</b><br />
     Set the target start and end to the start and end positions of the selection.</p>

     <p><b id="SCI_TARGETWHOLEDOCUMENT">SCI_TARGETWHOLEDOCUMENT</b><br />
     Set the target start to the start of the document and target end to the end of the document.</p>

    <p><b id="SCI_SETSEARCHFLAGS">SCI_SETSEARCHFLAGS(int searchFlags)</b><br />
     <b id="SCI_GETSEARCHFLAGS">SCI_GETSEARCHFLAGS &rarr; int</b><br />
     These get and set the <a class="jump" href="#searchFlags"><code class="parameter">searchFlags</code></a> used by
    <code>SCI_SEARCHINTARGET</code>. There are several option flags including a simple regular
    expression search.</p>

    <p><b id="SCI_SEARCHINTARGET">SCI_SEARCHINTARGET(int length, const char *text) &rarr; int</b><br />
     This searches for the first occurrence of a text string in the target defined by
    <code>SCI_SETTARGETSTART</code> and <code>SCI_SETTARGETEND</code>. The text string is not zero
    terminated; the size is set by <code class="parameter">length</code>. The search is modified by the search flags
    set by <code>SCI_SETSEARCHFLAGS</code>. If the search succeeds, the target is set to the found
    text and the return value is the position of the start of the matching text. If the search
    fails, the result is -1.</p>

    <p><b id="SCI_GETTARGETTEXT">SCI_GETTARGETTEXT(&lt;unused&gt;, char *text) &rarr; int</b><br />
     Retrieve the value in the target.</p>

    <p><b id="SCI_REPLACETARGET">SCI_REPLACETARGET(int length, const char *text) &rarr; int</b><br />
     If <code class="parameter">length</code> is -1, <code class="parameter">text</code> is a zero terminated string, otherwise
    <code class="parameter">length</code> sets the number of character to replace the target with.
           After replacement, the target range refers to the replacement text.
           The return value
    is the length of the replacement string.<br />
    Note that the recommended way to delete text in the document is to set the target to the text to be removed,
    and to perform a replace target with an empty string.</p>

    <p><b id="SCI_REPLACETARGETRE">SCI_REPLACETARGETRE(int length, const char *text) &rarr; int</b><br />
     This replaces the target using regular expressions. If <code class="parameter">length</code> is -1,
    <code class="parameter">text</code> is a zero terminated string, otherwise <code class="parameter">length</code> is the number of
    characters to use. The replacement string is formed from the text string with any sequences of
    <code>\1</code> through <code>\9</code> replaced by tagged matches from the most recent regular
    expression search. <code>\0</code> is replaced with all the matched text from the most recent search.
           After replacement, the target range refers to the replacement text.
           The return value is the length of the replacement string.</p>

    <p><b id="SCI_GETTAG">SCI_GETTAG(int tagNumber, char *tagValue NUL-terminated) &rarr; int</b><br />
     Discover what text was matched by tagged expressions in a regular expression search.
     This is useful if the application wants to interpret the replacement string itself.</p>

    <p>See also: <a class="message" href="#SCI_FINDTEXT"><code>SCI_FINDTEXT</code></a></p>

    <p><b id="searchFlags"><code class="parameter">searchFlags</code></b><br />
     Several of the search routines use flag options, which include a simple regular expression
    search. Combine the flag options by adding them:</p>

    <table class="standard" summary="Search flags">
      <tbody>
        <tr>
          <td><code>SCFIND_MATCHCASE</code></td>

          <td>A match only occurs with text that matches the case of the search string.</td>
        </tr>

        <tr>
          <td><code>SCFIND_WHOLEWORD</code></td>

          <td>A match only occurs if the characters before and after are not word characters as defined
	  by <a class="message" href="#SCI_SETWORDCHARS"><code>SCI_SETWORDCHARS</code></a>.</td>
        </tr>

        <tr>
          <td><code>SCFIND_WORDSTART</code></td>

          <td>A match only occurs if the character before is not a word character as defined
	  by <a class="message" href="#SCI_SETWORDCHARS"><code>SCI_SETWORDCHARS</code></a>.</td>
        </tr>

        <tr>
          <td><code>SCFIND_REGEXP</code></td>

          <td>The search string should be interpreted as a regular expression.
            Uses Scintilla's base implementation unless combined with <code>SCFIND_CXX11REGEX</code>.</td>
        </tr>
        <tr>
          <td><code>SCFIND_POSIX</code></td>

          <td>Treat regular expression in a more POSIX compatible manner
            by interpreting bare ( and ) for tagged sections rather than \( and \).
            Has no effect when <code>SCFIND_CXX11REGEX</code> is set.</td>
        </tr>
        <tr>
          <td><code>SCFIND_CXX11REGEX</code></td>

          <td>This flag may be set to use C++11 &lt;regex&gt; instead of Scintilla's basic regular expressions.
            If the regular expression is invalid then -1 is returned and status is set to
            <code>SC_STATUS_WARN_REGEX</code>.
            The ECMAScript flag is set on the regex object and UTF-8 documents will exhibit Unicode-compliant
            behaviour. For MSVC, where wchar_t is 16-bits, the reular expression ".." will match a single
            astral-plane character. There may be other differences between compilers.
            Must also have <code>SCFIND_REGEXP</code> set.</td>
        </tr>
      </tbody>
    </table>

    <p>In a regular expression, using Scintilla's base implementation,
    special characters interpreted are:</p>

    <table class="standard" summary="Regular expression synopsis">
      <tbody>
        <tr>
          <td><code>.</code></td>

          <td>Matches any character</td>
        </tr>

        <tr>
          <td><code>\(</code></td>

          <td>This marks the start of a region for tagging a match.</td>
        </tr>

        <tr>
          <td><code>\)</code></td>

          <td>This marks the end of a tagged region.</td>
        </tr>

        <tr>
          <td><code>\n</code></td>

          <td>Where <code>n</code> is 1 through 9 refers to the first through ninth tagged region
          when replacing. For example, if the search string was <code>Fred\([1-9]\)XXX</code> and
          the replace string was <code>Sam\1YYY</code>, when applied to <code>Fred2XXX</code> this
          would generate <code>Sam2YYY</code>.
	  <code>\0</code> refers to all of the matching text.</td>
        </tr>

        <tr>
          <td><code>\&lt;</code></td>

          <td>This matches the start of a word using Scintilla's definitions of words.</td>
        </tr>

        <tr>
          <td><code>\&gt;</code></td>

          <td>This matches the end of a word using Scintilla's definition of words.</td>
        </tr>

        <tr>
          <td><code>\x</code></td>

          <td>This allows you to use a character x that would otherwise have a special meaning. For
          example, \[ would be interpreted as [ and not as the start of a character set.</td>
        </tr>

        <tr>
          <td><code>[...]</code></td>

          <td>This indicates a set of characters, for example, [abc] means any of the characters a,
          b or c. You can also use ranges, for example [a-z] for any lower case character.</td>
        </tr>

        <tr>
          <td><code>[^...]</code></td>

          <td>The complement of the characters in the set. For example, [^A-Za-z] means any
          character except an alphabetic character.</td>
        </tr>

        <tr>
          <td><code>^</code></td>

          <td>This matches the start of a line (unless used inside a set, see above).</td>
        </tr>

        <tr>
          <td><code>$</code></td>

          <td>This matches the end of a line.</td>
        </tr>

        <tr>
          <td><code>*</code></td>

          <td>This matches 0 or more times. For example, <code>Sa*m</code> matches <code>Sm</code>,
          <code>Sam</code>, <code>Saam</code>, <code>Saaam</code> and so on.</td>
        </tr>

        <tr>
          <td><code>+</code></td>

          <td>This matches 1 or more times. For example, <code>Sa+m</code> matches
          <code>Sam</code>, <code>Saam</code>, <code>Saaam</code> and so on.</td>
        </tr>
      </tbody>
    </table>

    <p>Regular expressions will only match ranges within a single line, never matching over multiple lines.</p>

    <p>When using <code>SCFIND_CXX11REGEX</code> more features are available,
    generally similar to regular expression support in JavaScript.
    See the documentation of your C++ runtime for details on what is supported.</p>

    <code><a class="message" href="#SCI_FINDTEXT">SCI_FINDTEXT(int searchFlags, Sci_TextToFind *ft) &rarr; position</a><br />
     <a class="message" href="#SCI_SEARCHANCHOR">SCI_SEARCHANCHOR</a><br />
     <a class="message" href="#SCI_SEARCHNEXT">SCI_SEARCHNEXT(int searchFlags, const char *text) &rarr; int</a><br />
     <a class="message" href="#SCI_SEARCHPREV">SCI_SEARCHPREV(int searchFlags, const char *text) &rarr; int</a><br />
    </code>

    <p><b id="SCI_FINDTEXT">SCI_FINDTEXT(int searchFlags, <a class="jump" href="#Sci_TextToFind">Sci_TextToFind</a> *ft) &rarr; position</b><br />
     This message searches for text in the document. It does not use or move the current selection.
    The <a class="jump" href="#searchFlags"><code class="parameter">searchFlags</code></a> argument controls the
    search type, which includes regular expression searches.</p>

    <p>You can
    search backwards to find the previous occurrence of a search string by setting the end of the
    search range before the start.</p>

    <p>The <code>Sci_TextToFind</code> structure is defined in <code>Scintilla.h</code>; set
    <code>chrg.cpMin</code> and <code>chrg.cpMax</code> with the range of positions in the document
    to search. You can search backwards by
    setting <code>chrg.cpMax</code> less than <code>chrg.cpMin</code>.
    Set the <code>lpstrText</code> member of <code>Sci_TextToFind</code> to point at a zero terminated
    text string holding the search pattern. If your language makes the use of <code>Sci_TextToFind</code>
    difficult, you should consider using <code>SCI_SEARCHINTARGET</code> instead.</p>

    <p>The return value is -1 if the search fails or the position of the start of the found text if
    it succeeds. The <code>chrgText.cpMin</code> and <code>chrgText.cpMax</code> members of
    <code>Sci_TextToFind</code> are filled in with the start and end positions of the found text.</p>

    <p>See also: <code><a class="message"
    href="#SCI_SEARCHINTARGET">SCI_SEARCHINTARGET</a></code></p>

    <p><b id="Sci_TextToFind">Sci_TextToFind</b><br />
     This structure is defined to have exactly the same shape as the Win32 structure
    <code>FINDTEXTEX</code> for old code that treated Scintilla as a RichEdit control.</p>
<pre>
struct Sci_TextToFind {
    struct <a class="jump" href="#Sci_CharacterRange">Sci_CharacterRange</a> chrg;     // range to search
    const char *lpstrText;                // the search pattern (zero terminated)
    struct Sci_CharacterRange chrgText; // returned as position of matching text
};
</pre>

    <p><b id="SCI_SEARCHANCHOR">SCI_SEARCHANCHOR</b><br />
     <b id="SCI_SEARCHNEXT">SCI_SEARCHNEXT(int searchFlags, const char *text) &rarr; int</b><br />
     <b id="SCI_SEARCHPREV">SCI_SEARCHPREV(int searchFlags, const char *text) &rarr; int</b><br />
     These messages provide relocatable search support. This allows multiple incremental
    interactive searches to be macro recorded while still setting the selection to found text so
    the find/select operation is self-contained. These three messages send <a class="message"
    href="#SCN_MACRORECORD"><code>SCN_MACRORECORD</code></a> <a class="jump"
    href="#Notifications">notifications</a> if macro recording is enabled.</p>

    <p><code>SCI_SEARCHANCHOR</code> sets the search start point used by
    <code>SCI_SEARCHNEXT</code> and <code>SCI_SEARCHPREV</code> to the start of the current
    selection, that is, the end of the selection that is nearer to the start of the document. You
    should always call this before calling either of <code>SCI_SEARCHNEXT</code> or
    <code>SCI_SEARCHPREV</code>.</p>

    <p><code>SCI_SEARCHNEXT</code> and <code>SCI_SEARCHPREV</code> search for the next and previous
    occurrence of the zero terminated search string pointed at by text. The search is modified by
    the <a class="jump" href="#searchFlags"><code class="parameter">searchFlags</code></a>. </p>

    <p>The return value is -1 if nothing is found, otherwise the return value is the start position
    of the matching text. The selection is updated to show the matched text, but is not scrolled
    into view.</p>

    <p>See also: <a class="message" href="#SCI_SEARCHINTARGET"><code>SCI_SEARCHINTARGET</code></a>,
    <a class="message" href="#SCI_FINDTEXT"><code>SCI_FINDTEXT</code></a></p>

    <h2 id="Overtype">Overtype</h2>

    <code><a class="message" href="#SCI_SETOVERTYPE">SCI_SETOVERTYPE(bool overType)</a><br />
     <a class="message" href="#SCI_GETOVERTYPE">SCI_GETOVERTYPE &rarr; bool</a><br />
    </code>

    <p><b id="SCI_SETOVERTYPE">SCI_SETOVERTYPE(bool overType)</b><br />
     <b id="SCI_GETOVERTYPE">SCI_GETOVERTYPE &rarr; bool</b><br />
     When overtype is enabled, each typed character replaces the character to the right of the text
    caret. When overtype is disabled, characters are inserted at the caret.
    <code>SCI_GETOVERTYPE</code> returns <code>true</code> (1) if overtyping is active, otherwise
    <code>false</code> (0) will be returned. Use <code>SCI_SETOVERTYPE</code> to set the overtype
    mode.</p>

    <h2 id="CutCopyAndPaste">Cut, copy and paste</h2>

    <code><a class="message" href="#SCI_CUT">SCI_CUT</a><br />
     <a class="message" href="#SCI_COPY">SCI_COPY</a><br />
     <a class="message" href="#SCI_PASTE">SCI_PASTE</a><br />
     <a class="message" href="#SCI_CLEAR">SCI_CLEAR</a><br />
     <a class="message" href="#SCI_CANPASTE">SCI_CANPASTE &rarr; bool</a><br />
     <a class="message" href="#SCI_COPYRANGE">SCI_COPYRANGE(int start, int end)</a><br />
     <a class="message" href="#SCI_COPYTEXT">SCI_COPYTEXT(int length, const char *text)</a><br />
     <a class="message" href="#SCI_COPYALLOWLINE">SCI_COPYALLOWLINE</a><br />
     <a class="message" href="#SCI_SETPASTECONVERTENDINGS">SCI_SETPASTECONVERTENDINGS(bool convert)</a><br />
     <a class="message" href="#SCI_GETPASTECONVERTENDINGS">SCI_GETPASTECONVERTENDINGS &rarr; bool</a><br />
    </code>

    <p><b id="SCI_CUT">SCI_CUT</b><br />
     <b id="SCI_COPY">SCI_COPY</b><br />
     <b id="SCI_PASTE">SCI_PASTE</b><br />
     <b id="SCI_CLEAR">SCI_CLEAR</b><br />
     <b id="SCI_CANPASTE">SCI_CANPASTE &rarr; bool</b><br />
     <b id="SCI_COPYALLOWLINE">SCI_COPYALLOWLINE</b><br />
     These commands perform the standard tasks of cutting and copying data to the clipboard,
    pasting from the clipboard into the document, and clearing the document.
    <code>SCI_CANPASTE</code> returns non-zero if the document isn't read-only and if the selection
    doesn't contain protected text. If you need a "can copy" or "can cut", use
    <code>SCI_GETSELECTIONEMPTY()</code>, which will be zero if there are any non-empty
    selection ranges implying that a copy or cut to the clipboard should work.</p>

    <p>GTK+ does not really support <code>SCI_CANPASTE</code> and always returns <code>true</code>
    unless the document is read-only.</p>

    <p>On X, the clipboard is asynchronous and may require several messages between
    the destination and source applications. Data from SCI_PASTE will not arrive in the
    document immediately.</p>

    <p><code>SCI_COPYALLOWLINE</code> works the same as SCI_COPY except that if the
    selection is empty then the current line is copied. On Windows, an extra "MSDEVLineSelect" marker
    is added to the clipboard which is then used in <code>SCI_PASTE</code> to paste
    the whole line before the current line.</p>

     <b id="SCI_COPYRANGE">SCI_COPYRANGE(int start, int end)</b><br />
     <b id="SCI_COPYTEXT">SCI_COPYTEXT(int length, const char *text)</b><br />
    <p><code>SCI_COPYRANGE</code> copies a range of text from the document to
    the system clipboard and <code>SCI_COPYTEXT</code> copies a supplied piece of
    text to the system clipboard.</p>

    <p><b id="SCI_SETPASTECONVERTENDINGS">SCI_SETPASTECONVERTENDINGS(bool convert)</b><br />
     <b id="SCI_GETPASTECONVERTENDINGS">SCI_GETPASTECONVERTENDINGS &rarr; bool</b><br />
     If this property is set then when text is pasted any line ends are converted to match the document's
     end of line mode as set with
     <a class="seealso" href="#SCI_SETEOLMODE">SCI_SETEOLMODE</a>.
     Defaults to true.</p>

    <h2 id="ErrorHandling">Error handling</h2>

    <code><a class="message" href="#SCI_SETSTATUS">SCI_SETSTATUS(int status)</a><br />
     <a class="message" href="#SCI_GETSTATUS">SCI_GETSTATUS &rarr; int</a><br />
    </code>

    <p><b id="SCI_SETSTATUS">SCI_SETSTATUS(int status)</b><br />
     <b id="SCI_GETSTATUS">SCI_GETSTATUS &rarr; int</b><br />
     If an error occurs, Scintilla may set an internal error number that can be retrieved with
    <code>SCI_GETSTATUS</code>.
    To clear the error status call <code>SCI_SETSTATUS(0)</code>.
    Status values from 1 to 999 are errors and status <code>SC_STATUS_WARN_START</code> (1000)
    and above are warnings.
    The currently defined statuses are:
    </p>

    <table class="standard" summary="Status values">
      <tbody valign="top">
        <tr>
          <th align="left"><code>SC_STATUS_OK</code></th>
          <td>0</td>
          <td>No failures</td>
        </tr>

        <tr>
          <th align="left"><code>SC_STATUS_FAILURE</code></th>
          <td>1</td>
          <td>Generic failure</td>
        </tr>

        <tr>
          <th align="left"><code>SC_STATUS_BADALLOC</code></th>
          <td>2</td>
          <td>Memory is exhausted</td>
        </tr>

        <tr>
          <th align="left"><code>SC_STATUS_WARN_REGEX</code></th>
          <td>1001</td>
          <td>Regular expression is invalid</td>
        </tr>

      </tbody>
    </table>

    <h2 id="UndoAndRedo">Undo and Redo</h2>

    <p>Scintilla has multiple level undo and redo. It will continue to collect undoable actions
    until memory runs out. Scintilla saves actions that change the document. Scintilla does not
    save caret and selection movements, view scrolling and the like. Sequences of typing or
    deleting are compressed into single transactions to make it easier to undo and redo at a sensible
    level of detail. Sequences of actions can be combined into transactions that are undone as a unit.
    These sequences occur between <code>SCI_BEGINUNDOACTION</code> and
    <code>SCI_ENDUNDOACTION</code> messages. These transactions can be nested and only the top-level
    sequences are undone as units.</p>
    <code><a class="message" href="#SCI_UNDO">SCI_UNDO</a><br />
     <a class="message" href="#SCI_CANUNDO">SCI_CANUNDO &rarr; bool</a><br />
     <a class="message" href="#SCI_EMPTYUNDOBUFFER">SCI_EMPTYUNDOBUFFER</a><br />
     <a class="message" href="#SCI_REDO">SCI_REDO</a><br />
     <a class="message" href="#SCI_CANREDO">SCI_CANREDO &rarr; bool</a><br />
     <a class="message" href="#SCI_SETUNDOCOLLECTION">SCI_SETUNDOCOLLECTION(bool collectUndo)</a><br />
     <a class="message" href="#SCI_GETUNDOCOLLECTION">SCI_GETUNDOCOLLECTION &rarr; bool</a><br />
     <a class="message" href="#SCI_BEGINUNDOACTION">SCI_BEGINUNDOACTION</a><br />
     <a class="message" href="#SCI_ENDUNDOACTION">SCI_ENDUNDOACTION</a><br />
     <a class="message" href="#SCI_ADDUNDOACTION">SCI_ADDUNDOACTION(int token, int flags)</a><br />
    </code>

    <p><b id="SCI_UNDO">SCI_UNDO</b><br />
     <b id="SCI_CANUNDO">SCI_CANUNDO &rarr; bool</b><br />
     <code>SCI_UNDO</code> undoes one action, or if the undo buffer has reached a
    <code>SCI_ENDUNDOACTION</code> point, all the actions back to the corresponding
    <code>SCI_BEGINUNDOACTION</code>.</p>

    <p><code>SCI_CANUNDO</code> returns 0 if there is nothing to undo, and 1 if there is. You would
    typically use the result of this message to enable/disable the Edit menu Undo command.</p>

    <p><b id="SCI_REDO">SCI_REDO</b><br />
     <b id="SCI_CANREDO">SCI_CANREDO &rarr; bool</b><br />
     <code>SCI_REDO</code> undoes the effect of the last <code>SCI_UNDO</code> operation.</p>

    <p><code>SCI_CANREDO</code> returns 0 if there is no action to redo and 1 if there are undo
    actions to redo. You could typically use the result of this message to enable/disable the Edit
    menu Redo command.</p>

    <p><b id="SCI_EMPTYUNDOBUFFER">SCI_EMPTYUNDOBUFFER</b><br />
     This command tells Scintilla to forget any saved undo or redo history. It also sets the save
    point to the start of the undo buffer, so the document will appear to be unmodified. This does
    not cause the <code><a class="message"
    href="#SCN_SAVEPOINTREACHED">SCN_SAVEPOINTREACHED</a></code> notification to be sent to the
    container.</p>

    <p>See also: <a class="message" href="#SCI_SETSAVEPOINT"><code>SCI_SETSAVEPOINT</code></a></p>

    <p><b id="SCI_SETUNDOCOLLECTION">SCI_SETUNDOCOLLECTION(bool collectUndo)</b><br />
     <b id="SCI_GETUNDOCOLLECTION">SCI_GETUNDOCOLLECTION &rarr; bool</b><br />
     You can control whether Scintilla collects undo information with
    <code>SCI_SETUNDOCOLLECTION</code>. Pass in <code>true</code> (1) to collect information and
    <code>false</code> (0) to stop collecting. If you stop collection, you should also use
    <code>SCI_EMPTYUNDOBUFFER</code> to avoid the undo buffer being unsynchronized with the data in
    the buffer.</p>

    <p>You might wish to turn off saving undo information if you use the Scintilla to store text
    generated by a program (a Log view) or in a display window where text is often deleted and
    regenerated.</p>

    <p><b id="SCI_BEGINUNDOACTION">SCI_BEGINUNDOACTION</b><br />
     <b id="SCI_ENDUNDOACTION">SCI_ENDUNDOACTION</b><br />
     Send these two messages to Scintilla to mark the beginning and end of a set of operations that
    you want to undo all as one operation but that you have to generate as several operations.
    Alternatively, you can use these to mark a set of operations that you do not want to have
    combined with the preceding or following operations if they are undone.</p>

    <p><b id="SCI_ADDUNDOACTION">SCI_ADDUNDOACTION(int token, int flags)</b><br />
     The container can add its own actions into the undo stack by calling
     <code>SCI_ADDUNDOACTION</code> and an <code>SCN_MODIFIED</code>
     notification will be sent to the container with the
     <a class="message" href="#SC_MOD_CONTAINER"><code>SC_MOD_CONTAINER</code></a>
     flag when it is time to undo (<code>SC_PERFORMED_UNDO</code>) or
     redo (<code>SC_PERFORMED_REDO</code>) the action. The token argument supplied is
     returned in the <code>token</code> field of the notification.</p>
     <p>For example, if the container wanted to allow undo and redo of a 'toggle bookmark' command then
     it could call <code>SCI_ADDUNDOACTION(line, 0)</code> each time the command is performed.
     Then when it receives a notification to undo or redo it toggles a bookmark on the line given by
     the token field. If there are different types of commands or parameters that need to be stored into the undo
     stack then the container should maintain a stack of its own for the document and use the current
     position in that stack as the argument to <code>SCI_ADDUNDOACTION(line)</code>.
     <code>SCI_ADDUNDOACTION</code> commands are not combined together
     into a single undo transaction unless grouped with <code>SCI_BEGINUNDOACTION</code>
     and <code>SCI_ENDUNDOACTION</code>.</p>

     <p>The flags argument can be <code>UNDO_MAY_COALESCE</code> (1) if the container action may be
     coalesced along with any insertion and deletion actions into a single compound action, otherwise 0.
     Coalescing treats coalescible container actions as transparent so will still only group together insertions that
     look like typing or deletions that look like multiple uses of the Backspace or Delete keys.
     </p>
    <h2 id="SelectionAndInformation">Selection and information</h2>

    <p>Scintilla maintains a selection that stretches between two points, the anchor and the
    current position. If the anchor and the current position are the same, there is no selected
    text. Positions in the document range from 0 (before the first character), to the document size
    (after the last character). If you use messages, there is nothing to stop you setting a
    position that is in the middle of a CRLF pair, or in the middle of a 2 byte character. However,
    keyboard commands will not move the caret into such positions.</p>
    <code><a class="message" href="#SCI_GETTEXTLENGTH">SCI_GETTEXTLENGTH &rarr; int</a><br />
     <a class="message" href="#SCI_GETLENGTH">SCI_GETLENGTH &rarr; int</a><br />
     <a class="message" href="#SCI_GETLINECOUNT">SCI_GETLINECOUNT &rarr; int</a><br />
     <a class="message" href="#SCI_LINESONSCREEN">SCI_LINESONSCREEN &rarr; int</a><br />
     <a class="message" href="#SCI_GETMODIFY">SCI_GETMODIFY &rarr; bool</a><br />
     <a class="message" href="#SCI_SETSEL">SCI_SETSEL(int anchor, int caret)</a><br />
     <a class="message" href="#SCI_GOTOPOS">SCI_GOTOPOS(int caret)</a><br />
     <a class="message" href="#SCI_GOTOLINE">SCI_GOTOLINE(int line)</a><br />
     <a class="message" href="#SCI_SETCURRENTPOS">SCI_SETCURRENTPOS(int caret)</a><br />
     <a class="message" href="#SCI_GETCURRENTPOS">SCI_GETCURRENTPOS &rarr; position</a><br />
     <a class="message" href="#SCI_SETANCHOR">SCI_SETANCHOR(int anchor)</a><br />
     <a class="message" href="#SCI_GETANCHOR">SCI_GETANCHOR &rarr; position</a><br />
     <a class="message" href="#SCI_SETSELECTIONSTART">SCI_SETSELECTIONSTART(int anchor)</a><br />
     <a class="message" href="#SCI_GETSELECTIONSTART">SCI_GETSELECTIONSTART &rarr; position</a><br />
     <a class="message" href="#SCI_SETSELECTIONEND">SCI_SETSELECTIONEND(int caret)</a><br />
     <a class="message" href="#SCI_GETSELECTIONEND">SCI_GETSELECTIONEND &rarr; position</a><br />
     <a class="message" href="#SCI_SETEMPTYSELECTION">SCI_SETEMPTYSELECTION(int caret)</a><br />
     <a class="message" href="#SCI_SELECTALL">SCI_SELECTALL</a><br />
     <a class="message" href="#SCI_LINEFROMPOSITION">SCI_LINEFROMPOSITION(int pos) &rarr; int</a><br />
     <a class="message" href="#SCI_POSITIONFROMLINE">SCI_POSITIONFROMLINE(int line) &rarr; position</a><br />
     <a class="message" href="#SCI_GETLINEENDPOSITION">SCI_GETLINEENDPOSITION(int line) &rarr; position</a><br />
     <a class="message" href="#SCI_LINELENGTH">SCI_LINELENGTH(int line) &rarr; int</a><br />
     <a class="message" href="#SCI_GETCOLUMN">SCI_GETCOLUMN(int pos) &rarr; int</a><br />
     <a class="message" href="#SCI_FINDCOLUMN">SCI_FINDCOLUMN(int line, int column) &rarr; int</a><br />
     <a class="message" href="#SCI_POSITIONFROMPOINT">SCI_POSITIONFROMPOINT(int x, int y) &rarr; position</a><br />
     <a class="message" href="#SCI_POSITIONFROMPOINTCLOSE">SCI_POSITIONFROMPOINTCLOSE(int x, int y) &rarr; position</a><br />
     <a class="message" href="#SCI_CHARPOSITIONFROMPOINT">SCI_CHARPOSITIONFROMPOINT(int x, int y) &rarr; position</a><br />
     <a class="message" href="#SCI_CHARPOSITIONFROMPOINTCLOSE">SCI_CHARPOSITIONFROMPOINTCLOSE(int x, int y) &rarr; position</a><br />
     <a class="message" href="#SCI_POINTXFROMPOSITION">SCI_POINTXFROMPOSITION(&lt;unused&gt;, int pos) &rarr; int</a><br />
     <a class="message" href="#SCI_POINTYFROMPOSITION">SCI_POINTYFROMPOSITION(&lt;unused&gt;, int pos) &rarr; int</a><br />
     <a class="message" href="#SCI_HIDESELECTION">SCI_HIDESELECTION(bool hide)</a><br />
     <a class="message" href="#SCI_GETSELTEXT">SCI_GETSELTEXT(&lt;unused&gt;, char *text) &rarr; int</a><br />
     <a class="message" href="#SCI_GETCURLINE">SCI_GETCURLINE(int length, char *text) &rarr; int</a><br />
     <a class="message" href="#SCI_SELECTIONISRECTANGLE">SCI_SELECTIONISRECTANGLE &rarr; bool</a><br />
     <a class="message" href="#SCI_SETSELECTIONMODE">SCI_SETSELECTIONMODE(int selectionMode)</a><br />
     <a class="message" href="#SCI_GETSELECTIONMODE">SCI_GETSELECTIONMODE &rarr; int</a><br />
     <a class="message" href="#SCI_GETMOVEEXTENDSSELECTION">SCI_GETMOVEEXTENDSSELECTION &rarr; bool</a><br />
     <a class="message" href="#SCI_GETLINESELSTARTPOSITION">SCI_GETLINESELSTARTPOSITION(int line) &rarr; position</a><br />
     <a class="message" href="#SCI_GETLINESELENDPOSITION">SCI_GETLINESELENDPOSITION(int line) &rarr; position</a><br />
     <a class="message" href="#SCI_MOVECARETINSIDEVIEW">SCI_MOVECARETINSIDEVIEW</a><br />
     <a class="message" href="#SCI_POSITIONBEFORE">SCI_POSITIONBEFORE(int pos) &rarr; position</a><br />
     <a class="message" href="#SCI_POSITIONAFTER">SCI_POSITIONAFTER(int pos) &rarr; position</a><br />
     <a class="message" href="#SCI_TEXTWIDTH">SCI_TEXTWIDTH(int style, const char *text) &rarr; int</a><br />
     <a class="message" href="#SCI_TEXTHEIGHT">SCI_TEXTHEIGHT(int line) &rarr; int</a><br />
     <a class="message" href="#SCI_CHOOSECARETX">SCI_CHOOSECARETX</a><br />
     <a class="message" href="#SCI_MOVESELECTEDLINESUP">SCI_MOVESELECTEDLINESUP</a><br />
     <a class="message" href="#SCI_MOVESELECTEDLINESDOWN">SCI_MOVESELECTEDLINESDOWN</a><br />
     <a class="message" href="#SCI_SETMOUSESELECTIONRECTANGULARSWITCH">SCI_SETMOUSESELECTIONRECTANGULARSWITCH(bool mouseSelectionRectangularSwitch)</a><br />
     <a class="message" href="#SCI_GETMOUSESELECTIONRECTANGULARSWITCH">SCI_GETMOUSESELECTIONRECTANGULARSWITCH &rarr; bool</a><br />
    </code>

    <p><b id="SCI_GETTEXTLENGTH">SCI_GETTEXTLENGTH &rarr; int</b><br />
     <b id="SCI_GETLENGTH">SCI_GETLENGTH &rarr; int</b><br />
     Both these messages return the length of the document in bytes.</p>

    <p><b id="SCI_GETLINECOUNT">SCI_GETLINECOUNT &rarr; int</b><br />
     This returns the number of lines in the document. An empty document contains 1 line. A
    document holding only an end of line sequence has 2 lines.</p>

    <p><b id="SCI_LINESONSCREEN">SCI_LINESONSCREEN &rarr; int</b><br />
     This returns the number of complete lines visible on the screen. With a constant line height,
    this is the vertical space available divided by the line separation. Unless you arrange to size
    your window to an integral number of lines, there may be a partial line visible at the bottom
    of the view.</p>

    <p><b id="SCI_GETMODIFY">SCI_GETMODIFY &rarr; bool</b><br />
     This returns non-zero if the document is modified and 0 if it is unmodified. The modified
    status of a document is determined by the undo position relative to the save point. The save
    point is set by <a class="message" href="#SCI_SETSAVEPOINT"><code>SCI_SETSAVEPOINT</code></a>,
    usually when you have saved data to a file.</p>

    <p>If you need to be notified when the document becomes modified, Scintilla notifies the
    container that it has entered or left the save point with the <a class="message"
    href="#SCN_SAVEPOINTREACHED"><code>SCN_SAVEPOINTREACHED</code></a> and <a class="message"
    href="#SCN_SAVEPOINTLEFT"><code>SCN_SAVEPOINTLEFT</code></a> <a class="jump"
    href="#Notifications">notification messages</a>.</p>

    <p><b id="SCI_SETSEL">SCI_SETSEL(int anchor, int caret)</b><br />
     This message sets both the anchor and the current position. If <code class="parameter">caret</code> is
    negative, it means the end of the document. If <code class="parameter">anchor</code> is negative, it means
    remove any selection (i.e. set the anchor to the same position as <code class="parameter">caret</code>). The
    caret is scrolled into view after this operation.</p>

    <p><b id="SCI_GOTOPOS">SCI_GOTOPOS(int caret)</b><br />
     This removes any selection, sets the caret at <code class="parameter">caret</code> and scrolls the view to make
    the caret visible, if necessary. It is equivalent to
    <code>SCI_SETSEL(caret, caret)</code>. The anchor position is set the same as the current
    position.</p>

    <p><b id="SCI_GOTOLINE">SCI_GOTOLINE(int line)</b><br />
     This removes any selection and sets the caret at the start of line number <code class="parameter">line</code>
    and scrolls the view (if needed) to make it visible. The anchor position is set the same as the
    current position. If <code class="parameter">line</code> is outside the lines in the document (first line is 0),
    the line set is the first or last.</p>

    <p><b id="SCI_SETCURRENTPOS">SCI_SETCURRENTPOS(int caret)</b><br />
     This sets the current position and creates a selection between the anchor and the current
    position. The caret is not scrolled into view.</p>

    <p>See also: <a class="message" href="#SCI_SCROLLCARET"><code>SCI_SCROLLCARET</code></a></p>

    <p><b id="SCI_GETCURRENTPOS">SCI_GETCURRENTPOS &rarr; position</b><br />
     This returns the current position.</p>

    <p><b id="SCI_SETANCHOR">SCI_SETANCHOR(int anchor)</b><br />
     This sets the anchor position and creates a selection between the anchor position and the
    current position. The caret is not scrolled into view.</p>

    <p>See also: <a class="message" href="#SCI_SCROLLCARET"><code>SCI_SCROLLCARET</code></a></p>

    <p><b id="SCI_GETANCHOR">SCI_GETANCHOR &rarr; position</b><br />
     This returns the current anchor position.</p>

    <p><b id="SCI_SETSELECTIONSTART">SCI_SETSELECTIONSTART(int anchor)</b><br />
     <b id="SCI_SETSELECTIONEND">SCI_SETSELECTIONEND(int caret)</b><br />
     These set the selection based on the assumption that the anchor position is less than the
    current position. They do not make the caret visible. The table shows the positions of the
    anchor and the current position after using these messages.</p>

    <table class="standard" summary="SetSelection caret positioning">
      <thead align="center">
        <tr>
          <th>
	  New value for
          </th>

          <th>anchor</th>

          <th>caret</th>
        </tr>
      </thead>

      <tbody align="center">
        <tr>
          <th><code>SCI_SETSELECTIONSTART</code></th>

          <td><code class="parameter">anchor</code></td>

          <td><code>Max(</code><code class="parameter">anchor</code><code>, current)</code></td>
        </tr>

        <tr>
          <th><code>SCI_SETSELECTIONEND</code></th>

          <td><code>Min(anchor, </code><code class="parameter">caret</code><code>)</code></td>

          <td><code class="parameter">caret</code></td>
        </tr>
      </tbody>
    </table>

    <p>See also: <a class="message" href="#SCI_SCROLLCARET"><code>SCI_SCROLLCARET</code></a></p>

    <p><b id="SCI_GETSELECTIONSTART">SCI_GETSELECTIONSTART &rarr; position</b><br />
     <b id="SCI_GETSELECTIONEND">SCI_GETSELECTIONEND &rarr; position</b><br />
     These return the start and end of the selection without regard to which end is the current
    position and which is the anchor. <code>SCI_GETSELECTIONSTART</code> returns the smaller of the
    current position or the anchor position. <code>SCI_GETSELECTIONEND</code> returns the larger of
    the two values.</p>

    <p><b id="SCI_SETEMPTYSELECTION">SCI_SETEMPTYSELECTION(int caret)</b><br />
     This removes any selection and sets the caret at <code class="parameter">caret</code>. The caret is not scrolled into view.</p>

    <p><b id="SCI_SELECTALL">SCI_SELECTALL</b><br />
     This selects all the text in the document. The current position is not scrolled into view.</p>

    <p><b id="SCI_LINEFROMPOSITION">SCI_LINEFROMPOSITION(int pos) &rarr; int</b><br />
     This message returns the line that contains the position <code class="parameter">pos</code> in the document. The
    return value is 0 if <code class="parameter">pos</code> &lt;= 0. The return value is the last line if
    <code class="parameter">pos</code> is beyond the end of the document.</p>

    <p><b id="SCI_POSITIONFROMLINE">SCI_POSITIONFROMLINE(int line) &rarr; position</b><br />
     This returns the document position that corresponds with the start of the line. If
    <code class="parameter">line</code> is negative, the position of the line holding the start of the selection is
    returned. If <code class="parameter">line</code> is greater than the lines in the document, the return value is
    -1. If <code class="parameter">line</code> is equal to the number of lines in the document (i.e. 1 line past the
    last line), the return value is the end of the document.</p>

    <p><b id="SCI_GETLINEENDPOSITION">SCI_GETLINEENDPOSITION(int line) &rarr; position</b><br />
     This returns the position at the end of the line, before any line end characters. If <code class="parameter">line</code>
    is the last line in the document (which does not have any end of line characters) or greater,
    the result is the size of the document.
    If <code class="parameter">line</code> is negative the result is undefined.</p>

    <p><b id="SCI_LINELENGTH">SCI_LINELENGTH(int line) &rarr; int</b><br />
     This returns the length of the line, including any line end characters. If <code class="parameter">line</code>
    is negative or beyond the last line in the document, the result is 0. If you want the length of
    the line not including any end of line characters, use <a class="message"
    href="#SCI_GETLINEENDPOSITION"><code>SCI_GETLINEENDPOSITION(line)</code></a> - <a class="message"
    href="#SCI_POSITIONFROMLINE"><code>SCI_POSITIONFROMLINE(line)</code></a>.</p>

    <p><b id="SCI_GETSELTEXT">SCI_GETSELTEXT(&lt;unused&gt;, char *text NUL-terminated) &rarr; int</b><br />
     This copies the currently selected text and a terminating 0 byte to the <code class="parameter">text</code>
    buffer. The buffer size should be determined by calling with a NULL pointer for the <code class="parameter">text</code> argument
    <code>SCI_GETSELTEXT(0,0)</code>.
    This allows for rectangular and discontiguous selections as well as simple selections.
    See <a class="toc" href="#MultipleSelectionAndVirtualSpace">Multiple Selection</a> for information on
    how multiple and rectangular selections and virtual space are copied.</p>

    <p>See also: <code><a class="seealso" href="#SCI_GETCURLINE">SCI_GETCURLINE</a>,
    <a class="seealso" href="#SCI_GETLINE">SCI_GETLINE</a>,
    <a class="seealso" href="#SCI_GETTEXT">SCI_GETTEXT</a>,
    <a class="seealso" href="#SCI_GETSTYLEDTEXT">SCI_GETSTYLEDTEXT</a>,
    <a class="seealso" href="#SCI_GETTEXTRANGE">SCI_GETTEXTRANGE</a>
    </code></p>

    <p><b id="SCI_GETCURLINE">SCI_GETCURLINE(int length, char *text NUL-terminated) &rarr; int</b><br />
     This retrieves the text of the line containing the caret and returns the position within the
    line of the caret. Pass in <code>char* text</code> pointing at a buffer large enough to hold
    the text you wish to retrieve and a terminating 0 character.
    Set <code class="parameter">length</code> to the
    length of the buffer which must be at least 1 to hold the terminating 0 character.
    If the text argument is 0 then the length that should be allocated
    to store the entire current line is returned.</p>

    <p>See also: <code><a class="seealso" href="#SCI_GETSELTEXT">SCI_GETSELTEXT</a>, <a
    class="seealso" href="#SCI_GETLINE">SCI_GETLINE</a>, <a class="seealso"
    href="#SCI_GETTEXT">SCI_GETTEXT</a>, <a class="seealso"
    href="#SCI_GETSTYLEDTEXT">SCI_GETSTYLEDTEXT</a>, <a class="seealso"
    href="#SCI_GETTEXTRANGE">SCI_GETTEXTRANGE</a></code></p>

    <p><b id="SCI_SELECTIONISRECTANGLE">SCI_SELECTIONISRECTANGLE &rarr; bool</b><br />
     This returns 1 if the current selection is in rectangle mode, 0 if not.</p>

    <p><b id="SCI_SETSELECTIONMODE">SCI_SETSELECTIONMODE(int selectionMode)</b><br />
    <b id="SCI_GETSELECTIONMODE">SCI_GETSELECTIONMODE &rarr; int</b><br />
    The two functions set and get the selection mode, which can be
     stream (<code>SC_SEL_STREAM</code>=0) or
     rectangular (<code>SC_SEL_RECTANGLE</code>=1) or
     by lines (<code>SC_SEL_LINES</code>=2)
     or thin rectangular (<code>SC_SEL_THIN</code>=3).
     When set in these modes, regular caret moves will extend or reduce the selection,
     until the mode is cancelled by a call with same value or with <code>SCI_CANCEL</code>.
     The get function returns the current mode even if the selection was made by mouse
     or with regular extended moves.
     <code>SC_SEL_THIN</code> is the mode after a rectangular selection has been typed into and ensures
     that no characters are selected.</p>

    <p><b id="SCI_GETMOVEEXTENDSSELECTION">SCI_GETMOVEEXTENDSSELECTION &rarr; bool</b><br />
     This returns 1 if regular caret moves will extend or reduce the selection, 0 if not.
     <code>SCI_SETSELECTIONMODE</code> toggles this setting between on and off.</p>

    <p><b id="SCI_GETLINESELSTARTPOSITION">SCI_GETLINESELSTARTPOSITION(int line) &rarr; position</b><br />
    <b id="SCI_GETLINESELENDPOSITION">SCI_GETLINESELENDPOSITION(int line) &rarr; position</b><br />
    Retrieve the position of the start and end of the selection at the given line with
    <code>INVALID_POSITION</code> returned if no selection on this line.</p>

    <p><b id="SCI_MOVECARETINSIDEVIEW">SCI_MOVECARETINSIDEVIEW</b><br />
     If the caret is off the top or bottom of the view, it is moved to the nearest line that is
    visible to its current position. Any selection is lost.</p>

    <p><b id="SCI_POSITIONBEFORE">SCI_POSITIONBEFORE(int pos) &rarr; position</b><br />
     <b id="SCI_POSITIONAFTER">SCI_POSITIONAFTER(int pos) &rarr; position</b><br />
     These messages return the position before and after another position
     in the document taking into account the current code page. The minimum
     position returned is 0 and the maximum is the last position in the document.
     If called with a position within a multi byte character will return the position
     of the start/end of that character.</p>

    <p><b id="SCI_TEXTWIDTH">SCI_TEXTWIDTH(int style, const char *text) &rarr; int</b><br />
     This returns the pixel width of a string drawn in the given <code class="parameter">style</code> which can
    be used, for example, to decide how wide to make the line number margin in order to display a
    given number of numerals.</p>

    <p><b id="SCI_TEXTHEIGHT">SCI_TEXTHEIGHT(int line) &rarr; int</b><br />
     This returns the height in pixels of a particular line. Currently all lines are the same
    height.</p>

    <p><b id="SCI_GETCOLUMN">SCI_GETCOLUMN(int pos) &rarr; int</b><br />
     This message returns the column number of a position <code class="parameter">pos</code> within the document
    taking the width of tabs into account. This returns the column number of the last tab on the
    line before <code class="parameter">pos</code>, plus the number of characters between the last tab and
    <code class="parameter">pos</code>. If there are no tab characters on the line, the return value is the number of
    characters up to the position on the line. In both cases, double byte characters count as a
    single character. This is probably only useful with monospaced fonts.</p>

    <p><b id="SCI_FINDCOLUMN">SCI_FINDCOLUMN(int line, int column) &rarr; int</b><br />
     This message returns the position of a <code class="parameter">column</code> on a <code class="parameter">line</code>
    taking the width of tabs into account. It treats a multi-byte character as a single column.
    Column numbers, like lines start at 0.</p>

    <p><b id="SCI_POSITIONFROMPOINT">SCI_POSITIONFROMPOINT(int x, int y) &rarr; position</b><br />
     <b id="SCI_POSITIONFROMPOINTCLOSE">SCI_POSITIONFROMPOINTCLOSE(int x, int y) &rarr; position</b><br />
     <code>SCI_POSITIONFROMPOINT</code> finds the closest character position to a point and
    <code>SCI_POSITIONFROMPOINTCLOSE</code> is similar but returns -1 if the point is outside the
    window or not close to any characters.</p>

    <p><b id="SCI_CHARPOSITIONFROMPOINT">SCI_CHARPOSITIONFROMPOINT(int x, int y) &rarr; position</b><br />
     <b id="SCI_CHARPOSITIONFROMPOINTCLOSE">SCI_CHARPOSITIONFROMPOINTCLOSE(int x, int y) &rarr; position</b><br />
     <code>SCI_CHARPOSITIONFROMPOINT</code> finds the closest character to a point and
    <code>SCI_CHARPOSITIONFROMPOINTCLOSE</code> is similar but returns -1 if the point is outside the
    window or not close to any characters. This is similar to the previous methods but finds characters rather than
    inter-character positions.</p>

    <p><b id="SCI_POINTXFROMPOSITION">SCI_POINTXFROMPOSITION(&lt;unused&gt;, int pos) &rarr; int</b><br />
     <b id="SCI_POINTYFROMPOSITION">SCI_POINTYFROMPOSITION(&lt;unused&gt;, int pos) &rarr; int</b><br />
     These messages return the x and y display pixel location of text at position <code class="parameter">pos</code>
    in the document.</p>

    <p><b id="SCI_HIDESELECTION">SCI_HIDESELECTION(bool hide)</b><br />
     The normal state is to make the selection visible by drawing it as set by <a class="message"
    href="#SCI_SETSELFORE"><code>SCI_SETSELFORE</code></a> and <a class="message"
    href="#SCI_SETSELBACK"><code>SCI_SETSELBACK</code></a>. However, if you hide the selection, it
    is drawn as normal text.</p>

    <p><b id="SCI_CHOOSECARETX">SCI_CHOOSECARETX</b><br />
     Scintilla remembers the x value of the last position horizontally moved to explicitly by the
    user and this value is then used when moving vertically such as by using the up and down keys.
    This message sets the current x position of the caret as the remembered value.</p>

    <p><b id="SCI_MOVESELECTEDLINESUP">SCI_MOVESELECTEDLINESUP</b><br />
     Move the selected lines up one line, shifting the line above after the selection.
     The selection will be automatically extended to the beginning of the selection's first line and the end of the selection's last line.
     If nothing was selected, the line the cursor is currently at will be selected.</p>

    <p><b id="SCI_MOVESELECTEDLINESDOWN">SCI_MOVESELECTEDLINESDOWN</b><br />
     Move the selected lines down one line, shifting the line below before the selection.
     The selection will be automatically extended to the beginning of the selection's first line and the end of the selection's last line.
     If nothing was selected, the line the cursor is currently at will be selected.</p>

    <p><b id="SCI_SETMOUSESELECTIONRECTANGULARSWITCH">SCI_SETMOUSESELECTIONRECTANGULARSWITCH(bool
     mouseSelectionRectangularSwitch)</b><br />
    <b id="SCI_GETMOUSESELECTIONRECTANGULARSWITCH">SCI_GETMOUSESELECTIONRECTANGULARSWITCH &rarr; bool</b><br />
     Enable or disable the ability to switch to rectangular selection mode while making a selection with the mouse.
     When this option is turned on, mouse selections in stream mode can be switched to rectangular mode by pressing
     the corresponding modifier key. They then stick to rectangular mode even when the modifier key is released again.
     When this option is turned off, mouse selections will always stick to the mode the selection was started in. It
     is off by default.</p>

    <h2 id="ByCharacterOrCodeUnit">By character or UTF-16 code unit</h2>

    <p>Most Scintilla APIs use byte positions but some applications want to use positions based on counting
    (UTF-32) characters or (UTF-16) code units
    or need to communicate with other code written in terms of characters or code units.
    With only byte positions, this may require examining many bytes to count characters or code units in the document
    but this may be sped up in some cases by indexing the line starts by character or code unit.</p>

    <code>
     <a class="message" href="#SCI_POSITIONRELATIVE">SCI_POSITIONRELATIVE(int pos, int relative) &rarr; position</a><br />
     <a class="message" href="#SCI_POSITIONRELATIVECODEUNITS">SCI_POSITIONRELATIVECODEUNITS(int pos, int relative) &rarr; position</a><br />
     <a class="message" href="#SCI_COUNTCHARACTERS">SCI_COUNTCHARACTERS(int start, int end) &rarr; int</a><br />
     <a class="message" href="#SCI_COUNTCODEUNITS">SCI_COUNTCODEUNITS(int start, int end) &rarr; int</a><br />
     <a class="message" href="#SCI_GETLINECHARACTERINDEX">SCI_GETLINECHARACTERINDEX &rarr; int</a><br />
     <a class="message" href="#SCI_ALLOCATELINECHARACTERINDEX">SCI_ALLOCATELINECHARACTERINDEX(int lineCharacterIndex)</a><br />
     <a class="message" href="#SCI_RELEASELINECHARACTERINDEX">SCI_RELEASELINECHARACTERINDEX(int lineCharacterIndex)</a><br />
     <a class="message" href="#SCI_LINEFROMINDEXPOSITION">SCI_LINEFROMINDEXPOSITION(int pos, int lineCharacterIndex) &rarr; int</a><br />
     <a class="message" href="#SCI_INDEXPOSITIONFROMLINE">SCI_INDEXPOSITIONFROMLINE(int line, int lineCharacterIndex) &rarr; position</a><br />
    </code>

    <p><b id="SCI_POSITIONRELATIVE">SCI_POSITIONRELATIVE(int pos, int relative) &rarr; position</b><br />
     Count a number of whole characters before or after the argument position and return that position.
     The minimum position returned is 0 and the maximum is the last position in the document.
     If the position goes past the document end then 0 is returned.
     </p>

    <p><b id="SCI_COUNTCHARACTERS">SCI_COUNTCHARACTERS(int start, int end) &rarr; int</b><br />
     Returns the number of whole characters between two positions.</p>

    <p><b id="SCI_POSITIONRELATIVECODEUNITS">SCI_POSITIONRELATIVECODEUNITS(int pos, int relative) &rarr; position</b><br />
    <b id="SCI_COUNTCODEUNITS">SCI_COUNTCODEUNITS(int start, int end) &rarr; int</b><br />
     These are the UTF-16 versions of <code>SCI_POSITIONRELATIVE</code> and <code>SCI_COUNTCHARACTERS</code>
     working in terms of UTF-16 code units.</p>

    <p><b id="SCI_GETLINECHARACTERINDEX">SCI_GETLINECHARACTERINDEX &rarr; int</b><br />
     Returns which if any indexes are active. It may be <code>SC_LINECHARACTERINDEX_NONE(0)</code> or one or more
     of <code>SC_LINECHARACTERINDEX_UTF32(1)</code> if whole characters are indexed or
     <code>SC_LINECHARACTERINDEX_UTF16(2)</code> if UTF-16 code units are indexed.
     Character indexes are currently only supported for UTF-8 documents.</p>

    <p><b id="SCI_ALLOCATELINECHARACTERINDEX">SCI_ALLOCATELINECHARACTERINDEX(int lineCharacterIndex)</b><br />
    <b id="SCI_RELEASELINECHARACTERINDEX">SCI_RELEASELINECHARACTERINDEX(int lineCharacterIndex)</b><br />
     Allocate or release one or more indexes using same enumeration as <code>SCI_GETLINECHARACTERINDEX</code>.
     Different aspects of an application may need indexes for different periods and should allocate for those periods.
     Indexes use additional memory so releasing them can help minimize memory but they also take time to recalculate.
     Scintilla may also allocate indexes to support features like accessibility or input method editors.
     Only one index of each type is created for a document at a time.</p>

    <p><b id="SCI_LINEFROMINDEXPOSITION">SCI_LINEFROMINDEXPOSITION(int pos, int lineCharacterIndex) &rarr; int</b><br />
    <b id="SCI_INDEXPOSITIONFROMLINE">SCI_INDEXPOSITIONFROMLINE(int line, int lineCharacterIndex) &rarr; position</b><br />
     The document line of a particular character or code unit may be found by calling <code>SCI_LINEFROMINDEXPOSITION</code> with one of
     <code>SC_LINECHARACTERINDEX_UTF32(1)</code> or <code>SC_LINECHARACTERINDEX_UTF16(2)</code>.
     The inverse action, finds the starting position of a document line either in characters or code units from the document start by calling
     <code>SCI_INDEXPOSITIONFROMLINE</code> with the same <code class="parameter">lineCharacterIndex</code> argument.</p>

    <h2 id="MultipleSelectionAndVirtualSpace">Multiple Selection and Virtual Space</h2>

    <code>
     <a class="message" href="#SCI_SETMULTIPLESELECTION">SCI_SETMULTIPLESELECTION(bool multipleSelection)</a><br />
     <a class="message" href="#SCI_GETMULTIPLESELECTION">SCI_GETMULTIPLESELECTION &rarr; bool</a><br />
     <a class="message" href="#SCI_SETADDITIONALSELECTIONTYPING">SCI_SETADDITIONALSELECTIONTYPING(bool additionalSelectionTyping)</a><br />
     <a class="message" href="#SCI_GETADDITIONALSELECTIONTYPING">SCI_GETADDITIONALSELECTIONTYPING &rarr; bool</a><br />
     <a class="message" href="#SCI_SETMULTIPASTE">SCI_SETMULTIPASTE(int multiPaste)</a><br />
     <a class="message" href="#SCI_GETMULTIPASTE">SCI_GETMULTIPASTE &rarr; int</a><br />
     <a class="message" href="#SCI_SETVIRTUALSPACEOPTIONS">SCI_SETVIRTUALSPACEOPTIONS(int virtualSpaceOptions)</a><br />
     <a class="message" href="#SCI_GETVIRTUALSPACEOPTIONS">SCI_GETVIRTUALSPACEOPTIONS &rarr; int</a><br />
     <a class="message" href="#SCI_SETRECTANGULARSELECTIONMODIFIER">SCI_SETRECTANGULARSELECTIONMODIFIER(int modifier)</a><br />
     <a class="message" href="#SCI_GETRECTANGULARSELECTIONMODIFIER">SCI_GETRECTANGULARSELECTIONMODIFIER &rarr; int</a><br />
     <br />

     <a class="message" href="#SCI_GETSELECTIONS">SCI_GETSELECTIONS &rarr; int</a><br />
     <a class="message" href="#SCI_GETSELECTIONEMPTY">SCI_GETSELECTIONEMPTY &rarr; bool</a><br />
     <a class="message" href="#SCI_CLEARSELECTIONS">SCI_CLEARSELECTIONS</a><br />
     <a class="message" href="#SCI_SETSELECTION">SCI_SETSELECTION(int caret, int anchor)</a><br />
     <a class="message" href="#SCI_ADDSELECTION">SCI_ADDSELECTION(int caret, int anchor)</a><br />
     <a class="message" href="#SCI_DROPSELECTIONN">SCI_DROPSELECTIONN(int selection)</a><br />
     <a class="message" href="#SCI_SETMAINSELECTION">SCI_SETMAINSELECTION(int selection)</a><br />
     <a class="message" href="#SCI_GETMAINSELECTION">SCI_GETMAINSELECTION &rarr; int</a><br />
     <br />

     <a class="message" href="#SCI_SETSELECTIONNCARET">SCI_SETSELECTIONNCARET(int selection, int caret)</a><br />
     <a class="message" href="#SCI_GETSELECTIONNCARET">SCI_GETSELECTIONNCARET(int selection) &rarr; position</a><br />
     <a class="message" href="#SCI_SETSELECTIONNCARETVIRTUALSPACE">SCI_SETSELECTIONNCARETVIRTUALSPACE(int selection, int space)</a><br />
     <a class="message" href="#SCI_GETSELECTIONNCARETVIRTUALSPACE">SCI_GETSELECTIONNCARETVIRTUALSPACE(int selection) &rarr; int</a><br />
     <a class="message" href="#SCI_SETSELECTIONNANCHOR">SCI_SETSELECTIONNANCHOR(int selection, int anchor)</a><br />
     <a class="message" href="#SCI_GETSELECTIONNANCHOR">SCI_GETSELECTIONNANCHOR(int selection) &rarr; position</a><br />
     <a class="message" href="#SCI_SETSELECTIONNANCHORVIRTUALSPACE">SCI_SETSELECTIONNANCHORVIRTUALSPACE(int selection, int space)</a><br />
     <a class="message" href="#SCI_GETSELECTIONNANCHORVIRTUALSPACE">SCI_GETSELECTIONNANCHORVIRTUALSPACE(int selection) &rarr; int</a><br />
     <a class="message" href="#SCI_SETSELECTIONNSTART">SCI_SETSELECTIONNSTART(int selection, int anchor)</a><br />
     <a class="message" href="#SCI_GETSELECTIONNSTART">SCI_GETSELECTIONNSTART(int selection) &rarr; position</a><br />
     <a class="message" href="#SCI_SETSELECTIONNEND">SCI_SETSELECTIONNEND(int selection, int caret)</a><br />
     <a class="message" href="#SCI_GETSELECTIONNEND">SCI_GETSELECTIONNEND(int selection) &rarr; position</a><br />
     <br />

     <a class="message" href="#SCI_SETRECTANGULARSELECTIONCARET">SCI_SETRECTANGULARSELECTIONCARET(int caret)</a><br />
     <a class="message" href="#SCI_GETRECTANGULARSELECTIONCARET">SCI_GETRECTANGULARSELECTIONCARET &rarr; position</a><br />
     <a class="message" href="#SCI_SETRECTANGULARSELECTIONCARETVIRTUALSPACE">SCI_SETRECTANGULARSELECTIONCARETVIRTUALSPACE(int space)</a><br />
     <a class="message" href="#SCI_GETRECTANGULARSELECTIONCARETVIRTUALSPACE">SCI_GETRECTANGULARSELECTIONCARETVIRTUALSPACE &rarr; int</a><br />
     <a class="message" href="#SCI_SETRECTANGULARSELECTIONANCHOR">SCI_SETRECTANGULARSELECTIONANCHOR(int anchor)</a><br />
     <a class="message" href="#SCI_GETRECTANGULARSELECTIONANCHOR">SCI_GETRECTANGULARSELECTIONANCHOR &rarr; position</a><br />
     <a class="message" href="#SCI_SETRECTANGULARSELECTIONANCHORVIRTUALSPACE">SCI_SETRECTANGULARSELECTIONANCHORVIRTUALSPACE(int space)</a><br />
     <a class="message" href="#SCI_GETRECTANGULARSELECTIONANCHORVIRTUALSPACE">SCI_GETRECTANGULARSELECTIONANCHORVIRTUALSPACE &rarr; int</a><br />
     <br />

     <a class="message" href="#SCI_SETADDITIONALSELALPHA">SCI_SETADDITIONALSELALPHA(alpha alpha)</a><br />
     <a class="message" href="#SCI_GETADDITIONALSELALPHA">SCI_GETADDITIONALSELALPHA &rarr; int</a><br />
     <a class="message" href="#SCI_SETADDITIONALSELFORE">SCI_SETADDITIONALSELFORE(colour fore)</a><br />
     <a class="message" href="#SCI_SETADDITIONALSELBACK">SCI_SETADDITIONALSELBACK(colour back)</a><br />
     <a class="message" href="#SCI_SETADDITIONALCARETFORE">SCI_SETADDITIONALCARETFORE(colour fore)</a><br />
     <a class="message" href="#SCI_GETADDITIONALCARETFORE">SCI_GETADDITIONALCARETFORE &rarr; colour</a><br />
     <a class="message" href="#SCI_SETADDITIONALCARETSBLINK">SCI_SETADDITIONALCARETSBLINK(bool additionalCaretsBlink)</a><br />
     <a class="message" href="#SCI_GETADDITIONALCARETSBLINK">SCI_GETADDITIONALCARETSBLINK &rarr; bool</a><br />
     <a class="message" href="#SCI_SETADDITIONALCARETSVISIBLE">SCI_SETADDITIONALCARETSVISIBLE(bool additionalCaretsVisible)</a><br />
     <a class="message" href="#SCI_GETADDITIONALCARETSVISIBLE">SCI_GETADDITIONALCARETSVISIBLE &rarr; bool</a><br />
     <br />

     <a class="message" href="#SCI_SWAPMAINANCHORCARET">SCI_SWAPMAINANCHORCARET</a><br />
     <a class="message" href="#SCI_ROTATESELECTION">SCI_ROTATESELECTION</a><br />
     <a class="message" href="#SCI_MULTIPLESELECTADDNEXT">SCI_MULTIPLESELECTADDNEXT</a><br />
     <a class="message" href="#SCI_MULTIPLESELECTADDEACH">SCI_MULTIPLESELECTADDEACH</a><br />
    </code>

    <p>
    There may be multiple selections active at one time.
    More selections are made by holding down the Ctrl key while dragging with the mouse.
    The most recent selection is the main selection and determines which part of the document is shown automatically.
    Any selection apart from the main selection is called an additional selection.
    The calls in the previous section operate on the main selection.
    There is always at least one selection.
    The selection can be simplified down to just the main selection by
    <a class="message" href="#SCI_CANCEL"><code>SCI_CANCEL</code></a>
    which is normally mapped to the Esc key.
    </p>

    <p>
    Rectangular selections are handled as multiple selections although the original rectangular range is remembered so that
    subsequent operations may be handled differently for rectangular selections. For example, pasting a rectangular selection
    places each piece in a vertical column.
    </p>

    <p>
    Virtual space is space beyond the end of each line. The caret may be moved into virtual space but no real space will be
    added to the document until there is some text typed or some other text insertion command is used.
    </p>

    <p>When discontiguous selections are copied to the clipboard, each selection is added to the clipboard text
    in order with no delimiting characters.
    For rectangular selections the document's line end is added after each line's text. Rectangular selections
    are always copied from top line to bottom, not in the in order of selection.Virtual space is not copied.</p>

    <p>
    <b id="SCI_SETMULTIPLESELECTION">SCI_SETMULTIPLESELECTION(bool multipleSelection)</b><br />
    <b id="SCI_GETMULTIPLESELECTION">SCI_GETMULTIPLESELECTION &rarr; bool</b><br />
     Enable or disable multiple selection. When multiple selection is disabled, it is not possible to select
     multiple ranges by holding down the Ctrl key while dragging with the mouse.</p>

    <p>
    <b id="SCI_SETADDITIONALSELECTIONTYPING">SCI_SETADDITIONALSELECTIONTYPING(bool additionalSelectionTyping)</b><br />
    <b id="SCI_GETADDITIONALSELECTIONTYPING">SCI_GETADDITIONALSELECTIONTYPING &rarr; bool</b><br />
     Whether typing, new line, cursor left/right/up/down, backspace, delete, home, and end work
     with multiple selections simultaneously.
     Also allows selection and word and line deletion commands.</p>

    <p>
    <b id="SCI_SETMULTIPASTE">SCI_SETMULTIPASTE(int multiPaste)</b><br />
    <b id="SCI_GETMULTIPASTE">SCI_GETMULTIPASTE &rarr; int</b><br />
     When pasting into multiple selections, the pasted text can go into just the main selection with <code>SC_MULTIPASTE_ONCE</code>=0
     or into each selection with <code>SC_MULTIPASTE_EACH</code>=1. <code>SC_MULTIPASTE_ONCE</code> is the default.</p>

    <p>
    <b id="SCI_SETVIRTUALSPACEOPTIONS">SCI_SETVIRTUALSPACEOPTIONS(int virtualSpaceOptions)</b><br />
    <b id="SCI_GETVIRTUALSPACEOPTIONS">SCI_GETVIRTUALSPACEOPTIONS &rarr; int</b><br />
     Virtual space can be enabled or disabled for rectangular selections or in other circumstances or in both.
     There are three bit flags <code>SCVS_RECTANGULARSELECTION</code>=1,
     <code>SCVS_USERACCESSIBLE</code>=2, and
     <code>SCVS_NOWRAPLINESTART</code>=4 which can be set independently.
     <code>SCVS_NONE</code>=0, the default, disables all use of virtual space.</p>
     <p><code>SCVS_NOWRAPLINESTART</code> prevents left arrow movement and selection
     from wrapping to the previous line.
     This is most commonly desired in conjunction with virtual space but is an independent
     setting so works without virtual space.</p>

    <p>
    <b id="SCI_SETRECTANGULARSELECTIONMODIFIER">SCI_SETRECTANGULARSELECTIONMODIFIER(int modifier)</b><br />
    <b id="SCI_GETRECTANGULARSELECTIONMODIFIER">SCI_GETRECTANGULARSELECTIONMODIFIER &rarr; int</b><br />
     On GTK+ and Qt, the key used to indicate that a rectangular selection should be created when combined with a mouse drag can be set.
     The three possible values are <code>SCMOD_CTRL</code>=2, <code>SCMOD_ALT</code>=4 (default) or <code>SCMOD_SUPER</code>=8.
     Since <code>SCMOD_ALT</code> may already be used by a window manager, the window manager may need configuring to allow this choice.
     <code>SCMOD_SUPER</code> is often a system dependent modifier key such as the Left Windows key on a Windows keyboard or the
     Command key on a Mac.</p>

    <p>
    <b id="SCI_GETSELECTIONS">SCI_GETSELECTIONS &rarr; int</b><br />
     Return the number of selections currently active. There is always at least one selection.</p>

    <p>
    <b id="SCI_GETSELECTIONEMPTY">SCI_GETSELECTIONEMPTY &rarr; bool</b><br />
     Return 1 if every selected range is empty else 0.</p>

    <p>
    <b id="SCI_CLEARSELECTIONS">SCI_CLEARSELECTIONS</b><br />
     Set a single empty selection at 0 as the only selection.</p>

    <p>
    <b id="SCI_SETSELECTION">SCI_SETSELECTION(int caret, int anchor)</b><br />
     Set a single selection from <code class="parameter">anchor</code> to <code class="parameter">caret</code> as the only selection.</p>

    <p>
    <b id="SCI_ADDSELECTION">SCI_ADDSELECTION(int caret, int anchor)</b><br />
     Add a new selection from <code class="parameter">anchor</code> to <code class="parameter">caret</code> as the main selection retaining all other
     selections as additional selections.
     Since there is always at least one selection, to set a list of selections, the first selection should be
     added with <code>SCI_SETSELECTION</code> and later selections added with <code>SCI_ADDSELECTION</code></p>

    <p>
    <b id="SCI_DROPSELECTIONN">SCI_DROPSELECTIONN(int selection)</b><br />
     If there are multiple selections, remove the indicated selection.
     If this was the main selection then make the previous selection the main and if it was the first then the last selection becomes main.
     If there is only one selection, or there is no selection <code class="parameter">selection</code>, then there is no effect.</p>

    <p>
    <b id="SCI_SETMAINSELECTION">SCI_SETMAINSELECTION(int selection)</b><br />
    <b id="SCI_GETMAINSELECTION">SCI_GETMAINSELECTION &rarr; int</b><br />
     One of the selections is the main selection which is used to determine what range of text is automatically visible.
     The main selection may be displayed in different colours or with a differently styled caret.
     Only an already existing selection can be made main.</p>

    <p>
     <b id="SCI_SETSELECTIONNCARET">SCI_SETSELECTIONNCARET(int selection, int caret)</b><br />
     <b id="SCI_GETSELECTIONNCARET">SCI_GETSELECTIONNCARET(int selection) &rarr; position</b><br />
     <b id="SCI_SETSELECTIONNCARETVIRTUALSPACE">SCI_SETSELECTIONNCARETVIRTUALSPACE(int selection, int space)</b><br />
     <b id="SCI_GETSELECTIONNCARETVIRTUALSPACE">SCI_GETSELECTIONNCARETVIRTUALSPACE(int selection) &rarr; int</b><br />
     <b id="SCI_SETSELECTIONNANCHOR">SCI_SETSELECTIONNANCHOR(int selection, int anchor)</b><br />
     <b id="SCI_GETSELECTIONNANCHOR">SCI_GETSELECTIONNANCHOR(int selection) &rarr; position</b><br />
     <b id="SCI_SETSELECTIONNANCHORVIRTUALSPACE">SCI_SETSELECTIONNANCHORVIRTUALSPACE(int selection, int space)</b><br />
     <b id="SCI_GETSELECTIONNANCHORVIRTUALSPACE">SCI_GETSELECTIONNANCHORVIRTUALSPACE(int selection) &rarr; int</b><br />
     Set or query the position and amount of virtual space for the caret and anchor of each already existing selection.</p>

    <p>
     <b id="SCI_SETSELECTIONNSTART">SCI_SETSELECTIONNSTART(int selection, int anchor)</b><br />
     <b id="SCI_GETSELECTIONNSTART">SCI_GETSELECTIONNSTART(int selection) &rarr; position</b><br />
     <b id="SCI_SETSELECTIONNEND">SCI_SETSELECTIONNEND(int selection, int caret)</b><br />
     <b id="SCI_GETSELECTIONNEND">SCI_GETSELECTIONNEND(int selection) &rarr; position</b><br />
     Set or query the start and end position of each already existing selection.
     Mostly of use to query each range for its text. The selection parameter is zero-based. </p>

    <p>
     <b id="SCI_SETRECTANGULARSELECTIONCARET">SCI_SETRECTANGULARSELECTIONCARET(int caret)</b><br />
     <b id="SCI_GETRECTANGULARSELECTIONCARET">SCI_GETRECTANGULARSELECTIONCARET &rarr; position</b><br />
     <b id="SCI_SETRECTANGULARSELECTIONCARETVIRTUALSPACE">SCI_SETRECTANGULARSELECTIONCARETVIRTUALSPACE(int space)</b><br />
     <b id="SCI_GETRECTANGULARSELECTIONCARETVIRTUALSPACE">SCI_GETRECTANGULARSELECTIONCARETVIRTUALSPACE &rarr; int</b><br />
     <b id="SCI_SETRECTANGULARSELECTIONANCHOR">SCI_SETRECTANGULARSELECTIONANCHOR(int anchor)</b><br />
     <b id="SCI_GETRECTANGULARSELECTIONANCHOR">SCI_GETRECTANGULARSELECTIONANCHOR &rarr; position</b><br />
     <b id="SCI_SETRECTANGULARSELECTIONANCHORVIRTUALSPACE">SCI_SETRECTANGULARSELECTIONANCHORVIRTUALSPACE(int space)</b><br />
     <b id="SCI_GETRECTANGULARSELECTIONANCHORVIRTUALSPACE">SCI_GETRECTANGULARSELECTIONANCHORVIRTUALSPACE &rarr; int</b><br />
     Set or query the position and amount of virtual space for the caret and anchor of the rectangular selection.
     After setting the rectangular selection, this is broken down into multiple selections, one for each line.</p>

    <p>
     <b id="SCI_SETADDITIONALSELALPHA">SCI_SETADDITIONALSELALPHA(<a class="jump" href="#alpha">alpha</a> alpha)</b><br />
     <b id="SCI_GETADDITIONALSELALPHA">SCI_GETADDITIONALSELALPHA &rarr; int</b><br />
     <b id="SCI_SETADDITIONALSELFORE">SCI_SETADDITIONALSELFORE(<a class="jump" href="#colour">colour</a> fore)</b><br />
     <b id="SCI_SETADDITIONALSELBACK">SCI_SETADDITIONALSELBACK(<a class="jump" href="#colour">colour</a> back)</b><br />
     Modify the appearance of additional selections so that they can be differentiated from the main selection which has its appearance set with
     <a class="message" href="#SCI_SETSELALPHA"><code>SCI_SETSELALPHA</code></a>,
     <a class="message" href="#SCI_GETSELALPHA"><code>SCI_GETSELALPHA</code></a>,
     <a class="message" href="#SCI_SETSELFORE"><code>SCI_SETSELFORE</code></a>, and
     <a class="message" href="#SCI_SETSELBACK"><code>SCI_SETSELBACK</code></a>.
     <code>SCI_SETADDITIONALSELFORE</code> and
     <code>SCI_SETADDITIONALSELBACK</code> calls have no
     effect until <a class="message" href="#SCI_SETSELFORE"><code>SCI_SETSELFORE</code></a>
     and <a class="message" href="#SCI_SETSELBACK"><code>SCI_SETSELBACK</code></a> are
     called with useSelection*Colour value set to true. Subsequent calls to
     <a class="message" href="#SCI_SETSELFORE"><code>SCI_SETSELFORE</code></a>,
     and <a class="message" href="#SCI_SETSELBACK"><code>SCI_SETSELBACK</code></a> will
     overwrite the values set by <code>SCI_SETADDITIONALSEL*</code> functions.</p>
    <p>
     <b id="SCI_SETADDITIONALCARETFORE">SCI_SETADDITIONALCARETFORE(<a class="jump" href="#colour">colour</a> fore)</b><br />
     <b id="SCI_GETADDITIONALCARETFORE">SCI_GETADDITIONALCARETFORE &rarr; colour</b><br />
     <b id="SCI_SETADDITIONALCARETSBLINK">SCI_SETADDITIONALCARETSBLINK(bool additionalCaretsBlink)</b><br />
     <b id="SCI_GETADDITIONALCARETSBLINK">SCI_GETADDITIONALCARETSBLINK &rarr; bool</b><br />
     Modify the appearance of additional carets so that they can be differentiated from the main caret which has its appearance set with
     <a class="message" href="#SCI_SETCARETFORE"><code>SCI_SETCARETFORE</code></a>,
     <a class="message" href="#SCI_GETCARETFORE"><code>SCI_GETCARETFORE</code></a>,
     <a class="message" href="#SCI_SETCARETPERIOD"><code>SCI_SETCARETPERIOD</code></a>, and
     <a class="message" href="#SCI_GETCARETPERIOD"><code>SCI_GETCARETPERIOD</code></a>.</p>

    <p>
     <b id="SCI_SETADDITIONALCARETSVISIBLE">SCI_SETADDITIONALCARETSVISIBLE(bool additionalCaretsVisible)</b><br />
     <b id="SCI_GETADDITIONALCARETSVISIBLE">SCI_GETADDITIONALCARETSVISIBLE &rarr; bool</b><br />
     Determine whether to show additional carets (defaults to <code>true</code>).</p>

    <p>
     <b id="SCI_SWAPMAINANCHORCARET">SCI_SWAPMAINANCHORCARET</b><br />
     <b id="SCI_ROTATESELECTION">SCI_ROTATESELECTION</b><br />
     <b id="SCI_MULTIPLESELECTADDNEXT">SCI_MULTIPLESELECTADDNEXT</b><br />
     <b id="SCI_MULTIPLESELECTADDEACH">SCI_MULTIPLESELECTADDEACH</b><br />
     These commands may be assigned to keys to make it possible to manipulate multiple selections.
     <code>SCI_SWAPMAINANCHORCARET</code> moves the caret to the opposite end of the main selection.
     <code>SCI_ROTATESELECTION</code> makes the next selection be the main selection.<br />
     <code>SCI_MULTIPLESELECTADDNEXT</code> adds the next occurrence of the main selection
     within the target to the set of selections as main. If the current selection is empty then select word around caret.
      The current <a class="jump" href="#searchFlags"><code>searchFlags</code></a>
      are used so the application may choose case sensitivity and word search options.<br />
     <code>SCI_MULTIPLESELECTADDEACH</code> is similar to
     <code>SCI_MULTIPLESELECTADDNEXT</code> but adds multiple occurrences instead of just one.
     </p>

    <h2 id="ScrollingAndAutomaticScrolling">Scrolling and automatic scrolling</h2>
    <code>
     <a class="message" href="#SCI_SETFIRSTVISIBLELINE">SCI_SETFIRSTVISIBLELINE(int displayLine)</a><br />
     <a class="message" href="#SCI_GETFIRSTVISIBLELINE">SCI_GETFIRSTVISIBLELINE &rarr; int</a><br />
     <a class="message" href="#SCI_SETXOFFSET">SCI_SETXOFFSET(int xOffset)</a><br />
     <a class="message" href="#SCI_GETXOFFSET">SCI_GETXOFFSET &rarr; int</a><br />
     <a class="message" href="#SCI_LINESCROLL">SCI_LINESCROLL(int columns, int lines)</a><br />
     <a class="message" href="#SCI_SCROLLCARET">SCI_SCROLLCARET</a><br />
     <a class="message" href="#SCI_SCROLLRANGE">SCI_SCROLLRANGE(int secondary, int primary)</a><br />
     <a class="message" href="#SCI_SETXCARETPOLICY">SCI_SETXCARETPOLICY(int caretPolicy, int
    caretSlop)</a><br />
     <a class="message" href="#SCI_SETYCARETPOLICY">SCI_SETYCARETPOLICY(int caretPolicy, int
    caretSlop)</a><br />
     <a class="message" href="#SCI_SETVISIBLEPOLICY">SCI_SETVISIBLEPOLICY(int visiblePolicy, int
    visibleSlop)</a><br />
     <a class="message" href="#SCI_SETHSCROLLBAR">SCI_SETHSCROLLBAR(bool visible)</a><br />
     <a class="message" href="#SCI_GETHSCROLLBAR">SCI_GETHSCROLLBAR &rarr; bool</a><br />
     <a class="message" href="#SCI_SETVSCROLLBAR">SCI_SETVSCROLLBAR(bool visible)</a><br />
     <a class="message" href="#SCI_GETVSCROLLBAR">SCI_GETVSCROLLBAR &rarr; bool</a><br />
     <a class="message" href="#SCI_SETSCROLLWIDTH">SCI_SETSCROLLWIDTH(int pixelWidth)</a><br />
     <a class="message" href="#SCI_GETSCROLLWIDTH">SCI_GETSCROLLWIDTH &rarr; int</a><br />
     <a class="message" href="#SCI_SETSCROLLWIDTHTRACKING">SCI_SETSCROLLWIDTHTRACKING(bool tracking)</a><br />
     <a class="message" href="#SCI_GETSCROLLWIDTHTRACKING">SCI_GETSCROLLWIDTHTRACKING &rarr; bool</a><br />
     <a class="message" href="#SCI_SETENDATLASTLINE">SCI_SETENDATLASTLINE(bool
    endAtLastLine)</a><br />
     <a class="message" href="#SCI_GETENDATLASTLINE">SCI_GETENDATLASTLINE &rarr; bool</a><br />
    </code>

    <p><b id="SCI_SETFIRSTVISIBLELINE">SCI_SETFIRSTVISIBLELINE(int displayLine)</b><br />
     <b id="SCI_GETFIRSTVISIBLELINE">SCI_GETFIRSTVISIBLELINE &rarr; int</b><br />
     These messages retrieve and set the line number of the first visible line in the Scintilla view. The first line
    in the document is numbered 0. The value is a visible line rather than a document line.</p>

    <p><b id="SCI_SETXOFFSET">SCI_SETXOFFSET(int xOffset)</b><br />
     <b id="SCI_GETXOFFSET">SCI_GETXOFFSET &rarr; int</b><br />
     The <code class="parameter">xOffset</code> is the horizontal scroll position in pixels of the start of the text
    view. A value of 0 is the normal position with the first text column visible at the left of the
    view.</p>

    <p><b id="SCI_LINESCROLL">SCI_LINESCROLL(int columns, int lines)</b><br />
     This will attempt to scroll the display by the number of columns and lines that you specify.
    Positive line values increase the line number at the top of the screen (i.e. they move the text
    upwards as far as the user is concerned), Negative line values do the reverse.</p>

    <p>The column measure is the width of a space in the default style. Positive values increase
    the column at the left edge of the view (i.e. they move the text leftwards as far as the user
    is concerned). Negative values do the reverse.</p>

    <p>See also: <a class="message" href="#SCI_SETXOFFSET"><code>SCI_SETXOFFSET</code></a></p>

    <p><b id="SCI_SCROLLCARET">SCI_SCROLLCARET</b><br />
     If the current position (this is the caret if there is no selection) is not visible, the view
    is scrolled to make it visible according to the current caret policy.</p>

    <p><b id="SCI_SCROLLRANGE">SCI_SCROLLRANGE(int secondary, int primary)</b><br />
     Scroll the argument positions and the range between them into view giving
     priority to the primary position then the secondary position.
     The behaviour is similar to <a class="message" href="#SCI_SCROLLCARET"><code>SCI_SCROLLCARET</code></a>
     with the primary position used instead of the caret. An effort is then made to ensure that the secondary
     position and range between are also visible.
     This may be used to make a search match visible.</p>

    <p><b id="SCI_SETXCARETPOLICY">SCI_SETXCARETPOLICY(int caretPolicy, int caretSlop)</b><br />
     <b id="SCI_SETYCARETPOLICY">SCI_SETYCARETPOLICY(int caretPolicy, int caretSlop)</b><br />
     These set the caret policy. The value of <code class="parameter">caretPolicy</code> is a combination of
    <code>CARET_SLOP</code>, <code>CARET_STRICT</code>, <code>CARET_JUMPS</code> and
    <code>CARET_EVEN</code>.</p>

    <table class="standard" summary="Caret policy">
      <tbody valign="top">
        <tr>
          <th align="left"><code>CARET_SLOP</code></th>

          <td>If set, we can define a slop value: <code class="parameter">caretSlop</code>. This value defines an
          unwanted zone (UZ) where the caret is... unwanted. This zone is defined as a number of
          pixels near the vertical margins, and as a number of lines near the horizontal margins.
          By keeping the caret away from the edges, it is seen within its context. This makes it
          likely that the identifier that the caret is on can be completely seen, and that the
          current line is seen with some of the lines following it, which are often dependent on
          that line.</td>
        </tr>

        <tr>
          <th align="left"><code>CARET_STRICT</code></th>

          <td>If set, the policy set by <code>CARET_SLOP</code> is enforced... strictly. The caret
          is centred on the display if <code class="parameter">caretSlop</code> is not set, and cannot go in the UZ
          if <code class="parameter">caretSlop</code> is set.</td>
        </tr>

        <tr>
          <th align="left"><code>CARET_JUMPS</code></th>

          <td>If set, the display is moved more energetically so the caret can move in the same
          direction longer before the policy is applied again. '3UZ' notation is used to indicate
          three time the size of the UZ as a distance to the margin.</td>
        </tr>

        <tr>
          <th align="left"><code>CARET_EVEN</code></th>

          <td>If not set, instead of having symmetrical UZs, the left and bottom UZs are extended
          up to right and top UZs respectively. This way, we favour the displaying of useful
          information: the beginning of lines, where most code reside, and the lines after the
          caret, for example, the body of a function.</td>
        </tr>
      </tbody>
    </table>
    <br />

    <table class="standard" summary="Caret positioning">
      <thead align="center">
        <tr>
          <th>slop</th>

          <th>strict</th>

          <th>jumps</th>

          <th>even</th>

          <th>Caret can go to the margin</th>

          <th>On reaching limit (going out of visibility<br />
           or going into the UZ) display is...</th>
        </tr>
      </thead>

      <tbody align="center">
        <tr>
          <td>0</td>

          <td>0</td>

          <td>0</td>

          <td>0</td>

          <td>Yes</td>

          <td>moved to put caret on top/on right</td>
        </tr>

        <tr>
          <td>0</td>

          <td>0</td>

          <td>0</td>

          <td>1</td>

          <td>Yes</td>

          <td>moved by one position</td>
        </tr>

        <tr>
          <td>0</td>

          <td>0</td>

          <td>1</td>

          <td>0</td>

          <td>Yes</td>

          <td>moved to put caret on top/on right</td>
        </tr>

        <tr>
          <td>0</td>

          <td>0</td>

          <td>1</td>

          <td>1</td>

          <td>Yes</td>

          <td>centred on the caret</td>
        </tr>

        <tr>
          <td>0</td>

          <td>1</td>

          <td>-</td>

          <td>0</td>

          <td>Caret is always on top/on right of display</td>

          <td>-</td>
        </tr>

        <tr>
          <td>0</td>

          <td>1</td>

          <td>-</td>

          <td>1</td>

          <td>No, caret is always centred</td>

          <td>-</td>
        </tr>

        <tr>
          <td>1</td>

          <td>0</td>

          <td>0</td>

          <td>0</td>

          <td>Yes</td>

          <td>moved to put caret out of the asymmetrical UZ</td>
        </tr>

        <tr>
          <td>1</td>

          <td>0</td>

          <td>0</td>

          <td>1</td>

          <td>Yes</td>

          <td>moved to put caret out of the UZ</td>
        </tr>

        <tr>
          <td>1</td>

          <td>0</td>

          <td>1</td>

          <td>0</td>

          <td>Yes</td>

          <td>moved to put caret at 3UZ of the top or right margin</td>
        </tr>

        <tr>
          <td>1</td>

          <td>0</td>

          <td>1</td>

          <td>1</td>

          <td>Yes</td>

          <td>moved to put caret at 3UZ of the margin</td>
        </tr>

        <tr>
          <td>1</td>

          <td>1</td>

          <td>-</td>

          <td>0</td>

          <td>Caret is always at UZ of top/right margin</td>

          <td>-</td>
        </tr>

        <tr>
          <td>1</td>

          <td>1</td>

          <td>0</td>

          <td>1</td>

          <td>No, kept out of UZ</td>

          <td>moved by one position</td>
        </tr>

        <tr>
          <td>1</td>

          <td>1</td>

          <td>1</td>

          <td>0</td>

          <td>No, kept out of UZ</td>

          <td>moved to put caret at 3UZ of the margin</td>
        </tr>
      </tbody>
    </table>

    <p><b id="SCI_SETVISIBLEPOLICY">SCI_SETVISIBLEPOLICY(int visiblePolicy, int visibleSlop)</b><br />
     This determines how the vertical positioning is determined when <a class="message"
    href="#SCI_ENSUREVISIBLEENFORCEPOLICY"><code>SCI_ENSUREVISIBLEENFORCEPOLICY</code></a> is
    called. It takes <code>VISIBLE_SLOP</code> and <code>VISIBLE_STRICT</code> flags for the policy
    parameter. It is similar in operation to <a class="message"
    href="#SCI_SETYCARETPOLICY"><code>SCI_SETYCARETPOLICY(int caretPolicy, int
    caretSlop)</code></a>.</p>

    <p><b id="SCI_SETHSCROLLBAR">SCI_SETHSCROLLBAR(bool visible)</b><br />
     <b id="SCI_GETHSCROLLBAR">SCI_GETHSCROLLBAR &rarr; bool</b><br />
     The horizontal scroll bar is only displayed if it is needed for the assumed width.
     If you never wish to see it, call
    <code>SCI_SETHSCROLLBAR(0)</code>. Use <code>SCI_SETHSCROLLBAR(1)</code> to enable it again.
    <code>SCI_GETHSCROLLBAR</code> returns the current state. The default state is to display it
    when needed.</p>
    <p>See also: <a class="seealso" href="#SCI_SETSCROLLWIDTH">SCI_SETSCROLLWIDTH</a>.</p>

    <p><b id="SCI_SETVSCROLLBAR">SCI_SETVSCROLLBAR(bool visible)</b><br />
     <b id="SCI_GETVSCROLLBAR">SCI_GETVSCROLLBAR &rarr; bool</b><br />
     By default, the vertical scroll bar is always displayed when required. You can choose to hide
    or show it with <code>SCI_SETVSCROLLBAR</code> and get the current state with
    <code>SCI_GETVSCROLLBAR</code>.</p>

    <p>See also: <a class="message" href="#SCI_LINESCROLL"><code>SCI_LINESCROLL</code></a></p>

    <p><b id="SCI_SETSCROLLWIDTH">SCI_SETSCROLLWIDTH(int pixelWidth)</b><br />
     <b id="SCI_GETSCROLLWIDTH">SCI_GETSCROLLWIDTH &rarr; int</b><br />
     For performance, Scintilla does not measure the display width of the document to determine
     the properties of the horizontal scroll bar. Instead, an assumed width is used.
     These messages set and get the document width in pixels assumed by Scintilla.
     The default value is 2000.
     To ensure the width of the currently visible lines can be scrolled use
     <a class="message" href="#SCI_SETSCROLLWIDTHTRACKING"><code>SCI_SETSCROLLWIDTHTRACKING</code></a></p>

    <p><b id="SCI_SETSCROLLWIDTHTRACKING">SCI_SETSCROLLWIDTHTRACKING(bool tracking)</b><br />
     <b id="SCI_GETSCROLLWIDTHTRACKING">SCI_GETSCROLLWIDTHTRACKING &rarr; bool</b><br />
     If scroll width tracking is enabled then the scroll width is adjusted to ensure that all of the lines currently
     displayed can be completely scrolled. This mode never adjusts the scroll width to be narrower.</p>

    <p><b id="SCI_SETENDATLASTLINE">SCI_SETENDATLASTLINE(bool endAtLastLine)</b><br />
     <b id="SCI_GETENDATLASTLINE">SCI_GETENDATLASTLINE &rarr; bool</b><br />
     <code>SCI_SETENDATLASTLINE</code> sets the scroll range so that maximum scroll position has
    the last line at the bottom of the view (default). Setting this to <code>false</code> allows
    scrolling one page below the last line.</p>

    <h2 id="WhiteSpace">White space</h2>
    <code><a class="message" href="#SCI_SETVIEWWS">SCI_SETVIEWWS(int viewWS)</a><br />
     <a class="message" href="#SCI_GETVIEWWS">SCI_GETVIEWWS &rarr; int</a><br />
     <a class="message" href="#SCI_SETWHITESPACEFORE">SCI_SETWHITESPACEFORE(bool
    useSetting, colour fore)</a><br />
     <a class="message" href="#SCI_SETWHITESPACEBACK">SCI_SETWHITESPACEBACK(bool
    useSetting, colour back)</a><br />
     <a class="message" href="#SCI_SETWHITESPACESIZE">SCI_SETWHITESPACESIZE(int
    size)</a><br />
     <a class="message" href="#SCI_GETWHITESPACESIZE">SCI_GETWHITESPACESIZE &rarr; int</a><br />
     <a class="message" href="#SCI_SETTABDRAWMODE">SCI_SETTABDRAWMODE(int tabDrawMode)</a><br />
     <a class="message" href="#SCI_GETTABDRAWMODE">SCI_GETTABDRAWMODE &rarr; int</a><br />
     <a class="message" href="#SCI_SETEXTRAASCENT">SCI_SETEXTRAASCENT(int extraAscent)</a><br />
     <a class="message" href="#SCI_GETEXTRAASCENT">SCI_GETEXTRAASCENT &rarr; int</a><br />
     <a class="message" href="#SCI_SETEXTRADESCENT">SCI_SETEXTRADESCENT(int extraDescent)</a><br />
     <a class="message" href="#SCI_GETEXTRADESCENT">SCI_GETEXTRADESCENT &rarr; int</a><br />
    </code>

    <p><b id="SCI_SETVIEWWS">SCI_SETVIEWWS(int viewWS)</b><br />
     <b id="SCI_GETVIEWWS">SCI_GETVIEWWS &rarr; int</b><br />
     White space can be made visible which may be useful for languages in which white space is
    significant, such as Python. Space characters appear as small centred dots and tab characters
    as light arrows pointing to the right. There are also ways to control the display of <a
    class="jump" href="#LineEndings">end of line characters</a>. The two messages set and get the
    white space display mode. The <code class="parameter">viewWS</code> argument can be one of:</p>

    <table class="standard" summary="White space policy">
      <tbody valign="top">
        <tr>
          <th align="left"><code>SCWS_INVISIBLE</code></th>

          <td>0</td>

          <td>The normal display mode with white space displayed as an empty background
          colour.</td>
        </tr>

        <tr>
          <th align="left"><code>SCWS_VISIBLEALWAYS</code></th>

          <td>1</td>

          <td>White space characters are drawn as dots and arrows,</td>
        </tr>

        <tr>
          <th align="left"><code>SCWS_VISIBLEAFTERINDENT</code></th>

          <td>2</td>

          <td>White space used for indentation is displayed normally but after the first visible
          character, it is shown as dots and arrows.</td>
        </tr>

        <tr>
          <th align="left"><code>SCWS_VISIBLEONLYININDENT</code></th>

          <td>3</td>

          <td>White space used for indentation is displayed as dots and arrows.</td>
        </tr>
      </tbody>
    </table>

    <p>The effect of using any other <code class="parameter">viewWS</code> value is undefined.</p>

    <p><b id="SCI_SETWHITESPACEFORE">SCI_SETWHITESPACEFORE(bool useSetting, <a class="jump" href="#colour">colour</a> fore)</b><br />
     <b id="SCI_SETWHITESPACEBACK">SCI_SETWHITESPACEBACK(bool useSetting, <a class="jump" href="#colour">colour</a> back)</b><br />
     By default, the colour of visible white space is determined by the lexer in use. The
    foreground and/or background colour of all visible white space can be set globally, overriding
    the lexer's colours with <code>SCI_SETWHITESPACEFORE</code> and
    <code>SCI_SETWHITESPACEBACK</code>.</p>

     <p><b id="SCI_SETWHITESPACESIZE">SCI_SETWHITESPACESIZE(int size)</b><br />
     <b id="SCI_GETWHITESPACESIZE">SCI_GETWHITESPACESIZE &rarr; int</b><br />
     <code>SCI_SETWHITESPACESIZE</code> sets the size of the dots used for mark space characters.
     The <code>SCI_GETWHITESPACESIZE</code> message retrieves the current size.
    </p>

    <p><b id="SCI_SETTABDRAWMODE">SCI_SETTABDRAWMODE(int tabDrawMode)</b><br />
     <b id="SCI_GETTABDRAWMODE">SCI_GETTABDRAWMODE &rarr; int</b><br />
    These two messages get and set how tab characters are drawn when white space is visible.
    The <code class="parameter">tabDrawMode</code> argument can be one of:</p>

    <table class="standard" summary="White space policy">
      <tbody valign="top">
        <tr>
          <th align="left"><code>SCTD_LONGARROW</code></th>

          <td>0</td>

          <td>The default mode of an arrow stretching until the tabstop.</td>
        </tr>

        <tr>
          <th align="left"><code>SCTD_STRIKEOUT</code></th>

          <td>1</td>

          <td>A horizontal line stretching until the tabstop.</td>
        </tr>
      </tbody>
    </table>

    <p>The effect of using any other <code class="parameter">tabDrawMode</code> value is undefined.</p>

    <p>
     <b id="SCI_SETEXTRAASCENT">SCI_SETEXTRAASCENT(int extraAscent)</b><br />
     <b id="SCI_GETEXTRAASCENT">SCI_GETEXTRAASCENT &rarr; int</b><br />
     <b id="SCI_SETEXTRADESCENT">SCI_SETEXTRADESCENT(int extraDescent)</b><br />
     <b id="SCI_GETEXTRADESCENT">SCI_GETEXTRADESCENT &rarr; int</b><br />
     Text is drawn with the base of each character on a 'baseline'. The height of a line is found from the maximum
     that any style extends above the baseline (its 'ascent'), added to the maximum that any style extends below the
     baseline (its 'descent').
     Space may be added to the maximum ascent (<code>SCI_SETEXTRAASCENT</code>) and the
     maximum descent (<code>SCI_SETEXTRADESCENT</code>) to allow for more space between lines.
     This may done to make the text easier to read or to accommodate underlines or highlights.
    </p>

    <h2 id="Cursor">Cursor</h2>
    <a class="message" href="#SCI_SETCURSOR">SCI_SETCURSOR(int cursorType)</a><br />
     <a class="message" href="#SCI_GETCURSOR">SCI_GETCURSOR &rarr; int</a><br />

    <p><b id="SCI_SETCURSOR">SCI_SETCURSOR(int cursorType)</b><br />
     <b id="SCI_GETCURSOR">SCI_GETCURSOR &rarr; int</b><br />
     The cursor is normally chosen in a context sensitive way, so it will be different over the
    margin than when over the text. When performing a slow action, you may wish to change to a wait
    cursor. You set the cursor type with <code>SCI_SETCURSOR</code>. The <code class="parameter">cursorType</code>
    argument can be:</p>

    <table class="standard" summary="Mouse cursors">
      <tbody valign="top">
        <tr>
          <th align="left"><code>SC_CURSORNORMAL</code></th>

          <td>-1</td>

          <td>The normal cursor is displayed.</td>
        </tr>

        <tr>
          <th align="left"><code>SC_CURSORWAIT</code></th>

          <td>&nbsp;4</td>

          <td>The wait cursor is displayed when the mouse is over or owned by the Scintilla
          window.</td>
        </tr>
      </tbody>
    </table>

    <p>Cursor values 1 through 7 have defined cursors, but only <code>SC_CURSORWAIT</code> is
    usefully controllable. Other values of <code class="parameter">cursorType</code> cause a pointer to be displayed.
    The <code>SCI_GETCURSOR</code> message returns the last cursor type you set, or
    <code>SC_CURSORNORMAL</code> (-1) if you have not set a cursor type.</p>

    <h2 id="MouseCapture">Mouse capture</h2>
    <a class="message" href="#SCI_SETMOUSEDOWNCAPTURES">SCI_SETMOUSEDOWNCAPTURES(bool captures)</a><br />
     <a class="message" href="#SCI_GETMOUSEDOWNCAPTURES">SCI_GETMOUSEDOWNCAPTURES &rarr; bool</a><br />
     <a class="message" href="#SCI_SETMOUSEWHEELCAPTURES">SCI_SETMOUSEWHEELCAPTURES(bool captures)</a><br />
     <a class="message" href="#SCI_GETMOUSEWHEELCAPTURES">SCI_GETMOUSEWHEELCAPTURES &rarr; bool</a><br />

    <p><b id="SCI_SETMOUSEDOWNCAPTURES">SCI_SETMOUSEDOWNCAPTURES(bool captures)</b><br />
     <b id="SCI_GETMOUSEDOWNCAPTURES">SCI_GETMOUSEDOWNCAPTURES &rarr; bool</b><br />
     When the mouse is pressed inside Scintilla, it is captured so future mouse movement events are
    sent to Scintilla. This behaviour may be turned off with
    <code>SCI_SETMOUSEDOWNCAPTURES(0)</code>.</p>

    <p><b id="SCI_SETMOUSEWHEELCAPTURES">SCI_SETMOUSEWHEELCAPTURES(bool captures)</b><br />
     <b id="SCI_GETMOUSEWHEELCAPTURES">SCI_GETMOUSEWHEELCAPTURES &rarr; bool</b><br />
     On Windows, Scintilla captures all <code>WM_MOUSEWHEEL</code> messages if it has the
     focus, even if the mouse pointer is nowhere near the Scintilla editor window. This
     behavior can be changed with <code>SCI_SETMOUSEWHEELCAPTURES(0)</code> so that
     Scintilla passes the <code>WM_MOUSEWHEEL</code> messages to its parent window.
     Scintilla will still react to the mouse wheel if the mouse pointer is over
     the editor window.</p>

    <h2 id="LineEndings">Line endings</h2>

    <p>Scintilla can handle the major line end conventions and, depending on settings and
    the current lexer also support additional Unicode line ends.</p>

    <p>Scintilla can interpret any of the Macintosh (\r), Unix (\n) and Windows (\r\n)
    line ends.
    When the user presses the Enter key, one of these line
    end strings is inserted into the buffer. The default is \r\n in Windows and \n in Unix, but
    this can be changed with the <code>SCI_SETEOLMODE</code> message. You can also convert the
    entire document to one of these line endings with <code>SCI_CONVERTEOLS</code>. Finally, you
    can choose to display the line endings with <code>SCI_SETVIEWEOL</code>.</p>

    <p>For the UTF-8 encoding, three additional Unicode line ends,
    Next Line (<code>NEL=U+0085</code>), Line Separator (<code>LS=U+2028</code>), and Paragraph Separator (<code>PS=U+2029</code>)
    may optionally be interpreted when Unicode line ends is turned on and the current lexer also supports
    Unicode line ends.</p>

    <a class="message" href="#SCI_SETEOLMODE">SCI_SETEOLMODE(int eolMode)</a><br />
     <a class="message" href="#SCI_GETEOLMODE">SCI_GETEOLMODE &rarr; int</a><br />
     <a class="message" href="#SCI_CONVERTEOLS">SCI_CONVERTEOLS(int eolMode)</a><br />
     <a class="message" href="#SCI_SETVIEWEOL">SCI_SETVIEWEOL(bool visible)</a><br />
     <a class="message" href="#SCI_GETVIEWEOL">SCI_GETVIEWEOL &rarr; bool</a><br />

     <a class="message" href="#SCI_GETLINEENDTYPESSUPPORTED">SCI_GETLINEENDTYPESSUPPORTED &rarr; int</a><br />
     <a class="message" href="#SCI_SETLINEENDTYPESALLOWED">SCI_SETLINEENDTYPESALLOWED(int lineEndBitSet)</a><br />
     <a class="message" href="#SCI_GETLINEENDTYPESALLOWED">SCI_GETLINEENDTYPESALLOWED &rarr; int</a><br />
     <a class="message" href="#SCI_GETLINEENDTYPESACTIVE">SCI_GETLINEENDTYPESACTIVE &rarr; int</a><br />

    <p><b id="SCI_SETEOLMODE">SCI_SETEOLMODE(int eolMode)</b><br />
     <b id="SCI_GETEOLMODE">SCI_GETEOLMODE &rarr; int</b><br />
     <code>SCI_SETEOLMODE</code> sets the characters that are added into the document when the user
    presses the Enter key. You can set <code class="parameter">eolMode</code> to one of <code>SC_EOL_CRLF</code> (0),
    <code>SC_EOL_CR</code> (1), or <code>SC_EOL_LF</code> (2). The <code>SCI_GETEOLMODE</code>
    message retrieves the current state.</p>

    <p><b id="SCI_CONVERTEOLS">SCI_CONVERTEOLS(int eolMode)</b><br />
     This message changes all the end of line characters in the document to match
    <code class="parameter">eolMode</code>. Valid values are: <code>SC_EOL_CRLF</code> (0), <code>SC_EOL_CR</code>
    (1), or <code>SC_EOL_LF</code> (2).</p>

    <p><b id="SCI_SETVIEWEOL">SCI_SETVIEWEOL(bool visible)</b><br />
     <b id="SCI_GETVIEWEOL">SCI_GETVIEWEOL &rarr; bool</b><br />
     Normally, the end of line characters are hidden, but <code>SCI_SETVIEWEOL</code> allows you to
    display (or hide) them by setting <code class="parameter">visible</code> <code>true</code> (or
    <code>false</code>). The visible rendering of the end of line characters is similar to
    <code>(CR)</code>, <code>(LF)</code>, or <code>(CR)(LF)</code>. <code>SCI_GETVIEWEOL</code>
    returns the current state.</p>

    <p><b id="SCI_GETLINEENDTYPESSUPPORTED">SCI_GETLINEENDTYPESSUPPORTED &rarr; int</b><br />
     <code>SCI_GETLINEENDTYPESSUPPORTED</code> reports the different types of line ends supported
     by the current lexer. This is a bit set although there is currently only a single choice
     with either <code>SC_LINE_END_TYPE_DEFAULT</code> (0) or <code>SC_LINE_END_TYPE_UNICODE</code> (1).
     These values are also used by the other messages concerned with Unicode line ends.</p>

    <p><b id="SCI_SETLINEENDTYPESALLOWED">SCI_SETLINEENDTYPESALLOWED(int lineEndBitSet)</b><br />
     <b id="SCI_GETLINEENDTYPESALLOWED">SCI_GETLINEENDTYPESALLOWED &rarr; int</b><br />
     By default, only the ASCII line ends are interpreted. Unicode line ends may be requested with
     <code>SCI_SETLINEENDTYPESALLOWED(SC_LINE_END_TYPE_UNICODE)</code>
     but this will be ineffective unless the lexer also allows you Unicode line ends.
    <code>SCI_GETLINEENDTYPESALLOWED</code> returns the current state.</p>

    <p><b id="SCI_GETLINEENDTYPESACTIVE">SCI_GETLINEENDTYPESACTIVE &rarr; int</b><br />
     <code>SCI_GETLINEENDTYPESACTIVE</code> reports the set of line ends currently interpreted
     by Scintilla. It is <code>SCI_GETLINEENDTYPESSUPPORTED &amp; SCI_GETLINEENDTYPESALLOWED</code>.</p>

    <h2 id="Words">Words</h2>

    <p>There is support for selecting, navigating by, and searching for words.</p>

    <p>
    Words are contiguous sequences of characters from a particular set of characters.
    4 categories define words: word, whitespace, punctuation, and line ends with each category
    having a role in word functions.
    Double clicking selects the word at that point, which may be a sequence of word, punctuation,
    or whitespace bytes.
    Line ends are not selected by double clicking but do act as word separators.
    </p>

    <p>Words are defined in terms of characters and the sets of characters in each category can be customized to an extent.
    The NUL character (0) is always a space as the APIs to set categories use NUL-terminated strings.
    For single-byte encodings a category may be assigned to any character (1 to 0xFF).
    For multi-byte encodings a category may be assigned to characters from 1 to 0x7F with static behaviour from 0x80.
    For UTF-8, characters from 0x80 will use a category based on their Unicode general category.
    For Asian encodings, code pages 932, 936, 949, 950, and 1361, characters from 0x80 are treated as word characters.
    </p>

    <p>Identifiers in programming languages are often sequences of words with capitalisation
     (aCamelCaseIdentifier) or underscores (an_under_bar_ident) used to mark word boundaries.
     The <code>SCI_WORDPART*</code> commands are used for moving between word parts:
     <a class="message" href="#SCI_WORDPARTLEFT"><code>SCI_WORDPARTLEFT</code></a>,
     <a class="message" href="#SCI_WORDPARTLEFTEXTEND"><code>SCI_WORDPARTLEFTEXTEND</code></a>,
     <a class="message" href="#SCI_WORDPARTRIGHT"><code>SCI_WORDPARTRIGHT</code></a>, and
     <a class="message" href="#SCI_WORDPARTRIGHTEXTEND"><code>SCI_WORDPARTRIGHTEXTEND</code></a>.
     </p>

     <a class="message" href="#SCI_WORDENDPOSITION">SCI_WORDENDPOSITION(int pos, bool onlyWordCharacters) &rarr; int</a><br />
     <a class="message" href="#SCI_WORDSTARTPOSITION">SCI_WORDSTARTPOSITION(int pos, bool onlyWordCharacters) &rarr; int</a><br />
     <a class="message" href="#SCI_ISRANGEWORD">SCI_ISRANGEWORD(int start, int end) &rarr; bool</a><br />

     <a class="message" href="#SCI_SETWORDCHARS">SCI_SETWORDCHARS(&lt;unused&gt;, const char *characters)</a><br />
     <a class="message" href="#SCI_GETWORDCHARS">SCI_GETWORDCHARS(&lt;unused&gt;, char *characters) &rarr; int</a><br />
     <a class="message" href="#SCI_SETWHITESPACECHARS">SCI_SETWHITESPACECHARS(&lt;unused&gt;, const char *characters)</a><br />
     <a class="message" href="#SCI_GETWHITESPACECHARS">SCI_GETWHITESPACECHARS(&lt;unused&gt;, char *characters) &rarr; int</a><br />
     <a class="message" href="#SCI_SETPUNCTUATIONCHARS">SCI_SETPUNCTUATIONCHARS(&lt;unused&gt;, const char *characters)</a><br />
     <a class="message" href="#SCI_GETPUNCTUATIONCHARS">SCI_GETPUNCTUATIONCHARS(&lt;unused&gt;, char *characters) &rarr; int</a><br />
     <a class="message" href="#SCI_SETCHARSDEFAULT">SCI_SETCHARSDEFAULT</a><br />

    <p><b id="SCI_WORDENDPOSITION">SCI_WORDENDPOSITION(int pos, bool onlyWordCharacters) &rarr; int</b><br />
     <b id="SCI_WORDSTARTPOSITION">SCI_WORDSTARTPOSITION(int pos, bool onlyWordCharacters) &rarr; int</b><br />
     These messages return the start and end of words using the same definition of words as used
    internally within Scintilla. You can set your own list of characters that count as words with
    <a class="message" href="#SCI_SETWORDCHARS"><code>SCI_SETWORDCHARS</code></a>. The position
    sets the start or the search, which is forwards when searching for the end and backwards when
    searching for the start.</p>

    <p><b id="SCI_ISRANGEWORD">SCI_ISRANGEWORD(int start, int end) &rarr; bool</b><br />
     Is the range start..end a word or set of words? This message checks that start is at a word start transition and that
     end is at a word end transition. It does not check whether there are any spaces inside the range.</p>

     <a class="message" href="#SCI_ISRANGEWORD">SCI_ISRANGEWORD(int start, int end) &rarr; bool</a><br />

    <p>Set <code class="parameter">onlyWordCharacters</code> to <code>true</code> (1) to stop searching at the first
    non-word character in the search direction. If <code class="parameter">onlyWordCharacters</code> is
    <code>false</code> (0), the first character in the search direction sets the type of the search
    as word or non-word and the search stops at the first non-matching character. Searches are also
    terminated by the start or end of the document.</p>

    <p>If "w" represents word characters and "." represents non-word characters and "|" represents
    the position and <code>true</code> or <code>false</code> is the state of
    <code class="parameter">onlyWordCharacters</code>:</p>

    <table class="standard" summary="Word start and end positions">
      <thead align="center">
        <tr>
          <th>Initial state</th>

          <th>end, true</th>

          <th>end, false</th>

          <th>start, true</th>

          <th>start, false</th>
        </tr>
      </thead>

      <tbody align="center">
        <tr>
          <td>..ww..|..ww..</td>

          <td>..ww..|..ww..</td>

          <td>..ww....|ww..</td>

          <td>..ww..|..ww..</td>

          <td>..ww|....ww..</td>
        </tr>

        <tr>
          <td>....ww|ww....</td>

          <td>....wwww|....</td>

          <td>....wwww|....</td>

          <td>....|wwww....</td>

          <td>....|wwww....</td>
        </tr>

        <tr>
          <td>..ww|....ww..</td>

          <td>..ww|....ww..</td>

          <td>..ww....|ww..</td>

          <td>..|ww....ww..</td>

          <td>..|ww....ww..</td>
        </tr>

        <tr>
          <td>..ww....|ww..</td>

          <td>..ww....ww|..</td>

          <td>..ww....ww|..</td>

          <td>..ww....|ww..</td>

          <td>..ww|....ww..</td>
        </tr>
      </tbody>
    </table>

    <p><b id="SCI_SETWORDCHARS">SCI_SETWORDCHARS(&lt;unused&gt;, const char *characters)</b><br />
     This message defines which characters are members of the word category.
     The character categories are set to default values before processing this function.
    For example, if you don't allow '_' in your set of characters
    use:<br />
     <code>SCI_SETWORDCHARS(0, "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")</code>;</p>

    <p><b id="SCI_GETWORDCHARS">SCI_GETWORDCHARS(&lt;unused&gt;, char *characters) &rarr; int</b><br />
     This fills the characters parameter with all the characters included in words.
     The characters parameter must be large enough to hold all of the characters.
     If the characters parameter is 0 then the length that should be allocated
     to store the entire set is returned.</p>

    <p>For multi-byte encodings, this API will not return meaningful values for 0x80 and above.</p>

    <p><b id="SCI_SETWHITESPACECHARS">SCI_SETWHITESPACECHARS(&lt;unused&gt;, const char *characters)</b><br />
    <b id="SCI_GETWHITESPACECHARS">SCI_GETWHITESPACECHARS(&lt;unused&gt;, char *characters) &rarr; int</b><br />
     Similar to <code>SCI_SETWORDCHARS</code>, this message allows the user to define which chars Scintilla considers
          as whitespace.  Setting the whitespace chars allows the user to fine-tune Scintilla's behaviour doing
          such things as moving the cursor to the start or end of a word; for example, by defining punctuation chars
          as whitespace, they will be skipped over when the user presses ctrl+left or ctrl+right.
          This function should be called after <code>SCI_SETWORDCHARS</code> as it will
          reset the whitespace characters to the default set.
	  <code>SCI_GETWHITESPACECHARS</code> behaves similarly to <code>SCI_GETWORDCHARS</code>.</p>

    <p><b id="SCI_SETPUNCTUATIONCHARS">SCI_SETPUNCTUATIONCHARS(&lt;unused&gt;, const char *characters)</b><br />
    <b id="SCI_GETPUNCTUATIONCHARS">SCI_GETPUNCTUATIONCHARS(&lt;unused&gt;, char *characters) &rarr; int</b><br />
     Similar to <code>SCI_SETWORDCHARS</code> and <code>SCI_SETWHITESPACECHARS</code>, this message
     allows the user to define which chars Scintilla considers as punctuation.
	  <code>SCI_GETPUNCTUATIONCHARS</code> behaves similarly to <code>SCI_GETWORDCHARS</code>.</p>

    <p><b id="SCI_SETCHARSDEFAULT">SCI_SETCHARSDEFAULT</b><br />
     Use the default sets of word and whitespace characters. This sets whitespace to space, tab and other
     characters with codes less than 0x20, with word characters set to alphanumeric and '_'.
    </p>

    <p>Word keyboard commands are:</p>
    <ul>
    <li class="message" id="SCI_WORDLEFT">SCI_WORDLEFT</li>
    <li class="message" id="SCI_WORDLEFTEXTEND">SCI_WORDLEFTEXTEND</li>
    <li class="message" id="SCI_WORDRIGHT">SCI_WORDRIGHT</li>
    <li class="message" id="SCI_WORDRIGHTEXTEND">SCI_WORDRIGHTEXTEND</li>
    <li class="message" id="SCI_WORDLEFTEND">SCI_WORDLEFTEND</li>
    <li class="message" id="SCI_WORDLEFTENDEXTEND">SCI_WORDLEFTENDEXTEND</li>
    <li class="message" id="SCI_WORDRIGHTEND">SCI_WORDRIGHTEND</li>
    <li class="message" id="SCI_WORDRIGHTENDEXTEND">SCI_WORDRIGHTENDEXTEND</li>
    <li class="message" id="SCI_WORDPARTLEFT">SCI_WORDPARTLEFT</li>
    <li class="message" id="SCI_WORDPARTLEFTEXTEND">SCI_WORDPARTLEFTEXTEND</li>
    <li class="message" id="SCI_WORDPARTRIGHT">SCI_WORDPARTRIGHT</li>
    <li class="message" id="SCI_WORDPARTRIGHTEXTEND">SCI_WORDPARTRIGHTEXTEND</li>
    <li class="message" id="SCI_DELWORDLEFT">SCI_DELWORDLEFT</li>
    <li class="message" id="SCI_DELWORDRIGHT">SCI_DELWORDRIGHT</li>
    <li class="message" id="SCI_DELWORDRIGHTEND">SCI_DELWORDRIGHTEND</li>
    </ul>

    <h2 id="Styling">Styling</h2>

    <p>The styling messages allow you to assign styles to text. If your styling needs can be met by
    one of the standard lexers, or if you can write your own, then a lexer is probably the easiest
    way to style your document. If you choose to use the container to do the styling you can use
    the <a class="message" href="#SCI_SETLEXER"><code>SCI_SETLEXER</code></a> command to select
    <code>SCLEX_CONTAINER</code>, in which case the container is sent a <a class="message"
    href="#SCN_STYLENEEDED"><code>SCN_STYLENEEDED</code></a> <a class="jump"
    href="#Notifications">notification</a> each time text needs styling for display. As another
    alternative, you might use idle time to style the document. Even if you use a lexer, you might
    use the styling commands to mark errors detected by a compiler. The following commands can be
    used.</p>
    <code><a class="message" href="#SCI_GETENDSTYLED">SCI_GETENDSTYLED &rarr; position</a><br />
     <a class="message" href="#SCI_STARTSTYLING">SCI_STARTSTYLING(int start, int unused)</a><br />
     <a class="message" href="#SCI_SETSTYLING">SCI_SETSTYLING(int length, int style)</a><br />
     <a class="message" href="#SCI_SETSTYLINGEX">SCI_SETSTYLINGEX(int length, const char
    *styles)</a><br />
     <a class="message" href="#SCI_SETIDLESTYLING">SCI_SETIDLESTYLING(int idleStyling)</a><br />
     <a class="message" href="#SCI_GETIDLESTYLING">SCI_GETIDLESTYLING &rarr; int</a><br />
     <a class="message" href="#SCI_SETLINESTATE">SCI_SETLINESTATE(int line, int state)</a><br />
     <a class="message" href="#SCI_GETLINESTATE">SCI_GETLINESTATE(int line) &rarr; int</a><br />
     <a class="message" href="#SCI_GETMAXLINESTATE">SCI_GETMAXLINESTATE &rarr; int</a><br />
    </code>

    <p><b id="SCI_GETENDSTYLED">SCI_GETENDSTYLED &rarr; position</b><br />
     Scintilla keeps a record of the last character that is likely to be styled correctly. This is
    moved forwards when characters after it are styled and moved backwards if changes are made to
    the text of the document before it. Before drawing text, this position is checked to see if any
    styling is needed and, if so, a <code><a class="message"
    href="#SCN_STYLENEEDED">SCN_STYLENEEDED</a></code> notification message is sent to the
    container. The container can send <code>SCI_GETENDSTYLED</code> to work out where it needs to
    start styling. Scintilla will always ask to style whole lines.</p>

    <p><b id="SCI_STARTSTYLING">SCI_STARTSTYLING(int start, int unused)</b><br />
     This prepares for styling by setting the styling position <code class="parameter">start</code> to start at.
     The unused argument was used in earlier versions but is now ignored.
     After
    <code>SCI_STARTSTYLING</code>, send multiple <code>SCI_SETSTYLING</code> messages for each
    lexical entity to style or send <code>SCI_SETSTYLINGEX</code> to style in blocks.</p>

    <p><b id="SCI_SETSTYLING">SCI_SETSTYLING(int length, int style)</b><br />
     This message sets the style of <code class="parameter">length</code> characters starting at the styling position
    and then increases the styling position by <code class="parameter">length</code>, ready for the next call.
    <code>SCI_STARTSTYLING</code> should be called before the first call to this.
    </p>

    <p><b id="SCI_SETSTYLINGEX">SCI_SETSTYLINGEX(int length, const char *styles)</b><br />
     As an alternative to <code>SCI_SETSTYLING</code>, which applies the same style to each byte,
    you can use this message which specifies the styles for each of <code class="parameter">length</code> bytes from
    the styling position and then increases the styling position by <code class="parameter">length</code>, ready for
    the next call.
    <code>SCI_STARTSTYLING</code> should be called before the first call to this.
    </p>

    <p><b id="SCI_SETIDLESTYLING">SCI_SETIDLESTYLING(int idleStyling)</b><br />
     <b id="SCI_GETIDLESTYLING">SCI_GETIDLESTYLING &rarr; int</b><br />
     By default, <code>SC_IDLESTYLING_NONE</code> (0),
     syntax styling is performed for all the currently visible text before displaying it.
     On very large files, this may make scrolling down slow.
     With <code>SC_IDLESTYLING_TOVISIBLE</code> (1),
     a small amount of styling is performed before display and then
     further styling is performed incrementally in the background as an idle-time task.
     This may result in the text initially appearing uncoloured and then, some time later, it is coloured.
     Text after the currently visible portion may be styled in the background with <code>SC_IDLESTYLING_AFTERVISIBLE</code> (2).
     To style both before and after the visible text in the background use <code>SC_IDLESTYLING_ALL</code> (3).
    </p>
    <p>
     Since wrapping also needs to perform styling and also uses idle time, this setting has no effect when
     the document is displayed wrapped.
    </p>

    <p><b id="SCI_SETLINESTATE">SCI_SETLINESTATE(int line, int state)</b><br />
     <b id="SCI_GETLINESTATE">SCI_GETLINESTATE(int line) &rarr; int</b><br />
     As well as the 8 bits of lexical state stored for each character there is also an integer
    stored for each line. This can be used for longer lived parse states such as what the current
    scripting language is in an ASP page. Use <code>SCI_SETLINESTATE</code> to set the integer
    value and <code>SCI_GETLINESTATE</code> to get the value.
    Changing the value produces a <a class="message" href="#SC_MOD_CHANGELINESTATE">SC_MOD_CHANGELINESTATE</a> notification.
    </p>

    <p><b id="SCI_GETMAXLINESTATE">SCI_GETMAXLINESTATE &rarr; int</b><br />
     This returns the last line that has any line state.</p>

    <h2 id="StyleDefinition">Style definition</h2>

    <p>While the style setting messages mentioned above change the style numbers associated with
    text, these messages define how those style numbers are interpreted visually. There are 256
    lexer styles that can be set, numbered 0 to <code>STYLE_MAX</code> (255).
    There are also some
    predefined numbered styles starting at 32, The following <code>STYLE_</code>* constants are
    defined.</p>

    <table class="standard" summary="Preset styles">
      <tbody valign="top">
        <tr>
          <th align="left"><code>STYLE_DEFAULT</code></th>

          <td>32</td>

          <td>This style defines the attributes that all styles receive when the
          <code>SCI_STYLECLEARALL</code> message is used.</td>
        </tr>

        <tr>
          <th align="left"><code>STYLE_LINENUMBER</code></th>

          <td>33</td>

          <td>This style sets the attributes of the text used to display line numbers in a line
          number margin. The background colour set for this style also sets the background colour
          for all margins that do not have any folding mask bits set. That is, any margin for which
          <code>mask &amp; SC_MASK_FOLDERS</code> is 0. See <a class="message"
          href="#SCI_SETMARGINMASKN"><code>SCI_SETMARGINMASKN</code></a> for more about masks.</td>
        </tr>

        <tr>
          <th align="left"><code>STYLE_BRACELIGHT</code></th>

          <td>34</td>

          <td>This style sets the attributes used when highlighting braces with the <a
          class="message" href="#BraceHighlighting"><code>SCI_BRACEHIGHLIGHT</code></a> message and
          when highlighting the corresponding indentation with <a class="message"
          href="#SCI_SETHIGHLIGHTGUIDE"><code>SCI_SETHIGHLIGHTGUIDE</code></a>.</td>
        </tr>

        <tr>
          <th align="left"><code>STYLE_BRACEBAD</code></th>

          <td>35</td>

          <td>This style sets the display attributes used when marking an unmatched brace with the
          <a class="message" href="#BraceHighlighting"><code>SCI_BRACEBADLIGHT</code></a>
          message.</td>
        </tr>

        <tr>
          <th align="left"><code>STYLE_CONTROLCHAR</code></th>

          <td>36</td>

          <td>This style sets the font used when drawing control characters.
            Only the font, size, bold, italics, and character set attributes are used and not
            the colour attributes. See
          also: <a class="message"
          href="#SCI_SETCONTROLCHARSYMBOL"><code>SCI_SETCONTROLCHARSYMBOL</code></a>.</td>
        </tr>

        <tr>
          <th align="left"><code>STYLE_INDENTGUIDE</code></th>

          <td>37</td>

          <td>This style sets the foreground and background colours used when drawing the
          indentation guides.</td>
        </tr>

        <tr>
          <th align="left"><code>STYLE_CALLTIP</code></th>

          <td>38</td>

          <td> Call tips normally use the font attributes defined by <code>STYLE_DEFAULT</code>.
          Use of <a class="seealso" href="#SCI_CALLTIPUSESTYLE"><code>SCI_CALLTIPUSESTYLE</code></a>
            causes call tips to use this style instead. Only the font face name, font size,
          foreground and background colours and character set attributes are used.</td>
        </tr>

        <tr>
          <th align="left"><code>STYLE_FOLDDISPLAYTEXT</code></th>

          <td>39</td>

          <td>This is the style used for drawing text tags attached to folded text.</td>
        </tr>

        <tr>
          <th align="left"><code>STYLE_LASTPREDEFINED</code></th>

          <td>39</td>

          <td>To make it easier for client code to discover the range of styles that are
          predefined, this is set to the style number of the last predefined style.</td>
        </tr>

        <tr>
          <th align="left"><code>STYLE_MAX</code></th>

          <td>255</td>

          <td>This is not a style but is the number of the maximum style that can be set. Styles
          between <code>STYLE_LASTPREDEFINED</code> and <code>STYLE_MAX</code> may be used.</td>
        </tr>
      </tbody>
    </table>

    <p>For each style you can set the font name, size and use of bold, italic and underline,
    foreground and background colour and the character set. You can also choose to hide text with a
    given style, display all characters as upper or lower case and fill from the last character on
    a line to the end of the line (for embedded languages). There is also an experimental attribute
    to make text read-only.</p>

    <p>It is entirely up to you how you use styles. If you want to use syntax colouring you might
    use style 0 for white space, style 1 for numbers, style 2 for keywords, style 3 for strings,
    style 4 for preprocessor, style 5 for operators, and so on.</p>
    <code><a class="message" href="#SCI_STYLERESETDEFAULT">SCI_STYLERESETDEFAULT</a><br />
    <a class="message" href="#SCI_STYLECLEARALL">SCI_STYLECLEARALL</a><br />
    <a class="message" href="#SCI_STYLESETFONT">SCI_STYLESETFONT(int style, const char
    *fontName)</a><br />
    <a class="message" href="#SCI_STYLEGETFONT">SCI_STYLEGETFONT(int style, char *fontName) &rarr; int</a><br />
    <a class="message" href="#SCI_STYLESETSIZE">SCI_STYLESETSIZE(int style, int
    sizePoints)</a><br />
    <a class="message" href="#SCI_STYLEGETSIZE">SCI_STYLEGETSIZE(int style) &rarr; int</a><br />
    <a class="message" href="#SCI_STYLESETSIZEFRACTIONAL">SCI_STYLESETSIZEFRACTIONAL(int style, int
    sizeHundredthPoints)</a><br />
    <a class="message" href="#SCI_STYLEGETSIZEFRACTIONAL">SCI_STYLEGETSIZEFRACTIONAL(int style) &rarr; int</a><br />
    <a class="message" href="#SCI_STYLESETBOLD">SCI_STYLESETBOLD(int style, bool
    bold)</a><br />
    <a class="message" href="#SCI_STYLEGETBOLD">SCI_STYLEGETBOLD(int style) &rarr; bool</a><br />
    <a class="message" href="#SCI_STYLESETWEIGHT">SCI_STYLESETWEIGHT(int style, int
    weight)</a><br />
    <a class="message" href="#SCI_STYLEGETWEIGHT">SCI_STYLEGETWEIGHT(int style) &rarr; int</a><br />
    <a class="message" href="#SCI_STYLESETITALIC">SCI_STYLESETITALIC(int style, bool
    italic)</a><br />
    <a class="message" href="#SCI_STYLEGETITALIC">SCI_STYLEGETITALIC(int style) &rarr; bool</a><br />
    <a class="message" href="#SCI_STYLESETUNDERLINE">SCI_STYLESETUNDERLINE(int style, bool
    underline)</a><br />
    <a class="message" href="#SCI_STYLEGETUNDERLINE">SCI_STYLEGETUNDERLINE(int style) &rarr; bool</a><br />
    <a class="message" href="#SCI_STYLESETFORE">SCI_STYLESETFORE(int style, colour fore)</a><br />
    <a class="message" href="#SCI_STYLEGETFORE">SCI_STYLEGETFORE(int style) &rarr; colour</a><br />
    <a class="message" href="#SCI_STYLESETBACK">SCI_STYLESETBACK(int style, colour back)</a><br />
    <a class="message" href="#SCI_STYLEGETBACK">SCI_STYLEGETBACK(int style) &rarr; colour</a><br />
    <a class="message" href="#SCI_STYLESETEOLFILLED">SCI_STYLESETEOLFILLED(int style, bool
    eolFilled)</a><br />
    <a class="message" href="#SCI_STYLEGETEOLFILLED">SCI_STYLEGETEOLFILLED(int style) &rarr; bool</a><br />
    <a class="message" href="#SCI_STYLESETCHARACTERSET">SCI_STYLESETCHARACTERSET(int style,
    int characterSet)</a><br />
    <a class="message" href="#SCI_STYLEGETCHARACTERSET">SCI_STYLEGETCHARACTERSET(int style) &rarr; int</a><br />
    <a class="message" href="#SCI_STYLESETCASE">SCI_STYLESETCASE(int style, int
    caseVisible)</a><br />
    <a class="message" href="#SCI_STYLEGETCASE">SCI_STYLEGETCASE(int style) &rarr; int</a><br />
    <a class="message" href="#SCI_STYLESETVISIBLE">SCI_STYLESETVISIBLE(int style, bool
    visible)</a><br />
    <a class="message" href="#SCI_STYLEGETVISIBLE">SCI_STYLEGETVISIBLE(int style) &rarr; bool</a><br />
    <a class="message" href="#SCI_STYLESETCHANGEABLE">SCI_STYLESETCHANGEABLE(int style, bool
    changeable)</a><br />
    <a class="message" href="#SCI_STYLEGETCHANGEABLE">SCI_STYLEGETCHANGEABLE(int style) &rarr; bool</a><br />
     <a class="message" href="#SCI_STYLESETHOTSPOT">SCI_STYLESETHOTSPOT(int style, bool
    hotspot)</a><br />
    <a class="message" href="#SCI_STYLEGETHOTSPOT">SCI_STYLEGETHOTSPOT(int style) &rarr; bool</a><br />
    </code>

    <p><b id="SCI_STYLERESETDEFAULT">SCI_STYLERESETDEFAULT</b><br />
     This message resets <code>STYLE_DEFAULT</code> to its state when Scintilla was
    initialised.</p>

    <p><b id="SCI_STYLECLEARALL">SCI_STYLECLEARALL</b><br />
     This message sets all styles to have the same attributes as <code>STYLE_DEFAULT</code>. If you
    are setting up Scintilla for syntax colouring, it is likely that the lexical styles you set
    will be very similar. One way to set the styles is to:<br />
     1. Set <code>STYLE_DEFAULT</code> to the common features of all styles.<br />
     2. Use <code>SCI_STYLECLEARALL</code> to copy this to all styles.<br />
     3. Set the style attributes that make your lexical styles different.</p>

    <p><b id="SCI_STYLESETFONT">SCI_STYLESETFONT(int style, const char *fontName)</b><br />
    <b id="SCI_STYLEGETFONT">SCI_STYLEGETFONT(int style, char *fontName NUL-terminated) &rarr; int</b><br />
    <b id="SCI_STYLESETSIZE">SCI_STYLESETSIZE(int style, int sizePoints)</b><br />
    <b id="SCI_STYLEGETSIZE">SCI_STYLEGETSIZE(int style) &rarr; int</b><br />
    <b id="SCI_STYLESETSIZEFRACTIONAL">SCI_STYLESETSIZEFRACTIONAL(int style, int sizeHundredthPoints)</b><br />
    <b id="SCI_STYLEGETSIZEFRACTIONAL">SCI_STYLEGETSIZEFRACTIONAL(int style) &rarr; int</b><br />
    <b id="SCI_STYLESETBOLD">SCI_STYLESETBOLD(int style, bool bold)</b><br />
    <b id="SCI_STYLEGETBOLD">SCI_STYLEGETBOLD(int style) &rarr; bool</b><br />
    <b id="SCI_STYLESETWEIGHT">SCI_STYLESETWEIGHT(int style, int weight)</b><br />
    <b id="SCI_STYLEGETWEIGHT">SCI_STYLEGETWEIGHT(int style) &rarr; int</b><br />
    <b id="SCI_STYLESETITALIC">SCI_STYLESETITALIC(int style, bool italic)</b><br />
    <b id="SCI_STYLEGETITALIC">SCI_STYLEGETITALIC(int style) &rarr; bool</b><br />
     These messages (plus <a class="message"
    href="#SCI_STYLESETCHARACTERSET"><code>SCI_STYLESETCHARACTERSET</code></a>) set the font
    attributes that are used to match the fonts you request to those available.</p>
    <p>The
    <code class="parameter">fontName</code> is a zero terminated string holding the name of a font. Under Windows,
    only the first 32 characters of the name are used, the name is decoded as UTF-8, and the name is not case sensitive. For
    internal caching, Scintilla tracks fonts by name and does care about the casing of font names,
    so please be consistent.
    On GTK+, Pango is used to display text and the name is sent directly to Pango without transformation.
    On Qt, the name is decoded as UTF-8.
    On Cocoa, the name is decoded as MacRoman.</p>
    <p>Sizes can be set to a whole number of points with <code>SCI_STYLESETSIZE</code>
    or to a fractional point size in hundredths of a point with <code>SCI_STYLESETSIZEFRACTIONAL</code>
    by multiplying the size by 100 (<code>SC_FONT_SIZE_MULTIPLIER</code>).
    For example, a text size of 9.4 points is set with <code>SCI_STYLESETSIZEFRACTIONAL(&lt;style&gt;, 940)</code>.
    </p>
    <p>The weight or boldness of a font can be set with <code>SCI_STYLESETBOLD</code>
    or <code>SCI_STYLESETWEIGHT</code>. The weight is a number between 1 and 999 with 1 being very light
    and 999 very heavy. While any value can be used, fonts often only support between 2 and 4 weights with three weights
    being common enough to have symbolic names:
    <code>SC_WEIGHT_NORMAL</code> (400),
    <code>SC_WEIGHT_SEMIBOLD</code> (600), and
    <code>SC_WEIGHT_BOLD</code> (700).
    The <code>SCI_STYLESETBOLD</code> message takes a boolean argument with 0 choosing <code>SC_WEIGHT_NORMAL</code>
    and 1 <code>SC_WEIGHT_BOLD</code>.
    </p>

    <p><b id="SCI_STYLESETUNDERLINE">SCI_STYLESETUNDERLINE(int style, bool
    underline)</b><br />
    <b id="SCI_STYLEGETUNDERLINE">SCI_STYLEGETUNDERLINE(int style) &rarr; bool</b><br />
    You can set a style to be underlined. The underline is drawn in the foreground colour. All
    characters with a style that includes the underline attribute are underlined, even if they are
    white space.</p>

    <p><b id="SCI_STYLESETFORE">SCI_STYLESETFORE(int style, <a class="jump" href="#colour">colour</a> fore)</b><br />
    <b id="SCI_STYLEGETFORE">SCI_STYLEGETFORE(int style) &rarr; colour</b><br />
    <b id="SCI_STYLESETBACK">SCI_STYLESETBACK(int style, <a class="jump" href="#colour">colour</a> back)</b><br />
    <b id="SCI_STYLEGETBACK">SCI_STYLEGETBACK(int style) &rarr; colour</b><br />
    Text is drawn in the foreground colour. The space in each character cell that is not occupied
    by the character is drawn in the background colour.</p>

    <p><b id="SCI_STYLESETEOLFILLED">SCI_STYLESETEOLFILLED(int style, bool
    eolFilled)</b><br />
    <b id="SCI_STYLEGETEOLFILLED">SCI_STYLEGETEOLFILLED(int style) &rarr; bool</b><br />
    If the last character in the line has a style with this attribute set, the remainder of the
    line up to the right edge of the window is filled with the background colour set for the last
    character. This is useful when a document contains embedded sections in another language such
    as HTML pages with embedded JavaScript. By setting <code class="parameter">eolFilled</code> to <code>true</code>
    and a consistent background colour (different from the background colour set for the HTML
    styles) to all JavaScript styles then JavaScript sections will be easily distinguished from
    HTML.</p>

    <p><b id="SCI_STYLESETCHARACTERSET">SCI_STYLESETCHARACTERSET(int style, int
    characterSet)</b><br />
    <b id="SCI_STYLEGETCHARACTERSET">SCI_STYLEGETCHARACTERSET(int style) &rarr; int</b><br />
    You can set a style to use a different character set than the default. The places where such
    characters sets are likely to be useful are comments and literal strings. For example,
    <code>SCI_STYLESETCHARACTERSET(SCE_C_STRING, SC_CHARSET_RUSSIAN)</code> would ensure that
    strings in Russian would display correctly in C and C++ (<code>SCE_C_STRING</code> is the style
    number used by the C and C++ lexer to display literal strings; it has the value 6). This
    feature works differently on Windows and GTK+.<br />
    The default character set is <code>SC_CHARSET_DEFAULT</code>.</p>
    <p><code>SC_CHARSET_ANSI</code> and <code>SC_CHARSET_DEFAULT</code> specify European Windows code page 1252 unless the code page is set.</p>

<table class="standard" summary="Character Sets supported"><tbody>
  <tr>
    <th>Character Set</th>
    <th>Windows</th>
    <th>GTK+</th>
    <th>Cocoa</th></tr></tbody>
  <tbody>
  <tr>
    <td><code>SC_CHARSET_ANSI</code></td>
    <td>&#10003;</td>
    <td>&#10003;</td>
    <td>&#10003; (8859-1)</td></tr>
   <tr>
    <td><code>SC_CHARSET_ARABIC</code></td>
    <td>&#10003;</td>
    <td></td>
    <td>&#10003;</td></tr>
   <tr>
    <td><code>SC_CHARSET_BALTIC</code></td>
    <td>&#10003;</td>
    <td></td>
    <td>&#10003;</td></tr>
   <tr>
    <td><code>SC_CHARSET_CHINESEBIG5</code></td>
    <td>&#10003;</td>
    <td></td>
    <td>&#10003;</td></tr>
   <tr>
    <td><code>SC_CHARSET_DEFAULT</code></td>
    <td>&#10003;</td>
    <td>&#10003; (8859-1)</td>
    <td>&#10003; (8859-1)</td></tr>
   <tr>
    <td><code>SC_CHARSET_EASTEUROPE</code></td>
    <td>&#10003;</td>
    <td>&#10003;</td>
    <td>&#10003;</td></tr>
   <tr>
    <td><code>SC_CHARSET_GB2312</code></td>
    <td>&#10003;</td>
    <td>&#10003;</td>
    <td>&#10003;</td></tr>
   <tr>
    <td><code>SC_CHARSET_GREEK</code></td>
    <td>&#10003;</td>
    <td></td>
    <td>&#10003;</td></tr>
   <tr>
    <td><code>SC_CHARSET_HANGUL</code></td>
    <td>&#10003;</td>
    <td>&#10003;</td>
    <td>&#10003;</td></tr>
   <tr>
    <td><code>SC_CHARSET_HEBREW</code></td>
    <td>&#10003;</td>
    <td></td>
    <td>&#10003;</td></tr>
   <tr>
    <td><code>SC_CHARSET_JOHAB</code></td>
    <td>&#10003;</td>
    <td></td>
    <td>&#10003;</td></tr>
   <tr>
    <td><code>SC_CHARSET_MAC</code></td>
    <td>&#10003;</td>
    <td></td>
    <td>&#10003;</td></tr>
   <tr>
    <td><code>SC_CHARSET_OEM</code></td>
    <td>&#10003;</td>
    <td></td>
    <td>&#10003;</td></tr>
   <tr>
    <td><code>SC_CHARSET_RUSSIAN</code></td>
    <td>&#10003; (cp1251)</td>
    <td>&#10003; (koi8-r)</td>
    <td>&#10003; (cp1251)</td></tr>
   <tr>
    <td><code>SC_CHARSET_SHIFTJIS</code></td>
    <td>&#10003;</td>
    <td>&#10003;</td>
    <td>&#10003;</td></tr>
   <tr>
    <td><code>SC_CHARSET_SYMBOL</code></td>
    <td>&#10003;</td>
    <td></td>
    <td>&#10003;</td></tr>
   <tr>
    <td><code>SC_CHARSET_THAI</code></td>
    <td>&#10003;</td>
    <td></td>
    <td>&#10003;</td></tr>
   <tr>
    <td><code>SC_CHARSET_TURKISH</code></td>
    <td>&#10003;</td>
    <td></td>
    <td>&#10003;</td></tr>
   <tr>
    <td><code>SC_CHARSET_VIETNAMESE</code></td>
    <td>&#10003;</td>
    <td></td>
    <td>&#10003;</td></tr>
   <tr>
    <td><code>SC_CHARSET_OEM866</code></td>
    <td></td>
    <td>&#10003; (cp866)</td>
    <td></td></tr>
   <tr>
    <td><code>SC_CHARSET_CYRILLIC</code></td>
    <td></td>
    <td>&#10003; (cp1251)</td>
    <td>&#10003; (cp1251)</td></tr>
   <tr>
    <td><code>SC_CHARSET_8859_15</code></td>
    <td></td>
    <td>&#10003;</td>
    <td>&#10003;</td></tr>
</tbody></table>

    <p><b id="SCI_STYLESETCASE">SCI_STYLESETCASE(int style, int caseVisible)</b><br />
    <b id="SCI_STYLEGETCASE">SCI_STYLEGETCASE(int style) &rarr; int</b><br />
    The value of <code>caseVisible</code> determines how text is displayed. You can set upper case
    (<code>SC_CASE_UPPER</code>, 1) or lower case (<code>SC_CASE_LOWER</code>, 2) or camel case (<code>SC_CASE_CAMEL</code>, 3)
    or display normally (<code>SC_CASE_MIXED</code>, 0). This does not change the stored text, only how it is
    displayed.</p>

    <p><b id="SCI_STYLESETVISIBLE">SCI_STYLESETVISIBLE(int style, bool visible)</b><br />
    <b id="SCI_STYLEGETVISIBLE">SCI_STYLEGETVISIBLE(int style) &rarr; bool</b><br />
    Text is normally visible. However, you can completely hide it by giving it a style with the
    <code class="parameter">visible</code> set to 0. This could be used to hide embedded formatting instructions or
    hypertext keywords in HTML or XML.
    Invisible text may not be deleted by user actions but the application may delete invisible text by calling
    <a class="seealso" href="#SCI_DELETERANGE">SCI_DELETERANGE</a>.</p>

    <p><b id="SCI_STYLESETCHANGEABLE">SCI_STYLESETCHANGEABLE(int style, bool
    changeable)</b><br />
    <b id="SCI_STYLEGETCHANGEABLE">SCI_STYLEGETCHANGEABLE(int style) &rarr; bool</b><br />
    This is an experimental and incompletely implemented style attribute. The default setting is
    <code class="parameter">changeable</code> set <code>true</code> but when set <code>false</code> it makes text
    read-only. The user can not move the caret within not-changeable text and not-changeable
    text may not be deleted by the user.
    The application may delete not-changeable text by calling
    <a class="seealso" href="#SCI_DELETERANGE">SCI_DELETERANGE</a>.</p>

    <p><b id="SCI_STYLESETHOTSPOT">SCI_STYLESETHOTSPOT(int style, bool
    hotspot)</b><br />
    <b id="SCI_STYLEGETHOTSPOT">SCI_STYLEGETHOTSPOT(int style) &rarr; bool</b><br />
    This style is used to mark ranges of text that can detect mouse clicks.
    The cursor changes to a hand over hotspots, and the foreground, and background colours
    may change and an underline appear to indicate that these areas are sensitive to clicking.
    This may be used to allow hyperlinks to other documents.</p>

    <h2 id="CaretAndSelectionStyles">Caret, selection, and hotspot styles</h2>

    <p>The selection is shown by changing the foreground and/or background colours. If one of these
    is not set then that attribute is not changed for the selection. The default is to show the
    selection by changing the background to light grey and leaving the foreground the same as when
    it was not selected. When there is no selection, the current insertion point is marked by the
    text caret. This is a vertical line that is normally blinking on and off to attract the users
    attention.</p>
    <code><a class="message" href="#SCI_SETSELFORE">SCI_SETSELFORE(bool useSetting, colour fore)</a><br />
    <a class="message" href="#SCI_SETSELBACK">SCI_SETSELBACK(bool useSetting, colour back)</a><br />
    <a class="message" href="#SCI_SETSELALPHA">SCI_SETSELALPHA(alpha alpha)</a><br />
    <a class="message" href="#SCI_GETSELALPHA">SCI_GETSELALPHA &rarr; int</a><br />
    <a class="message" href="#SCI_SETSELEOLFILLED">SCI_SETSELEOLFILLED(bool filled)</a><br />
    <a class="message" href="#SCI_GETSELEOLFILLED">SCI_GETSELEOLFILLED &rarr; bool</a><br />
    <a class="message" href="#SCI_SETCARETFORE">SCI_SETCARETFORE(colour fore)</a><br />
    <a class="message" href="#SCI_GETCARETFORE">SCI_GETCARETFORE &rarr; colour</a><br />
    <a class="message" href="#SCI_SETCARETLINEVISIBLE">SCI_SETCARETLINEVISIBLE(bool show)</a><br />
    <a class="message" href="#SCI_GETCARETLINEVISIBLE">SCI_GETCARETLINEVISIBLE &rarr; bool</a><br />
    <a class="message" href="#SCI_SETCARETLINEBACK">SCI_SETCARETLINEBACK(colour back)</a><br />
    <a class="message" href="#SCI_GETCARETLINEBACK">SCI_GETCARETLINEBACK &rarr; colour</a><br />
    <a class="message" href="#SCI_SETCARETLINEBACKALPHA">SCI_SETCARETLINEBACKALPHA(alpha alpha)</a><br />
    <a class="message" href="#SCI_GETCARETLINEBACKALPHA">SCI_GETCARETLINEBACKALPHA &rarr; int</a><br />
    <a class="message" href="#SCI_SETCARETLINEFRAME">SCI_SETCARETLINEFRAME(int width)</a><br />
    <a class="message" href="#SCI_GETCARETLINEFRAME">SCI_GETCARETLINEFRAME &rarr; int</a><br />
    <a class="message" href="#SCI_SETCARETLINEVISIBLEALWAYS">SCI_SETCARETLINEVISIBLEALWAYS(bool alwaysVisible)</a><br />
    <a class="message" href="#SCI_GETCARETLINEVISIBLEALWAYS">SCI_GETCARETLINEVISIBLEALWAYS &rarr; bool</a><br />
    <a class="message" href="#SCI_SETCARETPERIOD">SCI_SETCARETPERIOD(int periodMilliseconds)</a><br />
    <a class="message" href="#SCI_GETCARETPERIOD">SCI_GETCARETPERIOD &rarr; int</a><br />
    <a class="message" href="#SCI_SETCARETSTYLE">SCI_SETCARETSTYLE(int caretStyle)</a><br />
    <a class="message" href="#SCI_GETCARETSTYLE">SCI_GETCARETSTYLE &rarr; int</a><br />
    <a class="message" href="#SCI_SETCARETWIDTH">SCI_SETCARETWIDTH(int pixelWidth)</a><br />
    <a class="message" href="#SCI_GETCARETWIDTH">SCI_GETCARETWIDTH &rarr; int</a><br />
    <a class="message" href="#SCI_SETHOTSPOTACTIVEFORE">SCI_SETHOTSPOTACTIVEFORE(bool useSetting, colour fore)</a><br />
    <a class="message" href="#SCI_GETHOTSPOTACTIVEFORE">SCI_GETHOTSPOTACTIVEFORE &rarr; colour</a><br />
    <a class="message" href="#SCI_SETHOTSPOTACTIVEBACK">SCI_SETHOTSPOTACTIVEBACK(bool useSetting, colour back)</a><br />
    <a class="message" href="#SCI_GETHOTSPOTACTIVEBACK">SCI_GETHOTSPOTACTIVEBACK &rarr; colour</a><br />
    <a class="message" href="#SCI_SETHOTSPOTACTIVEUNDERLINE">SCI_SETHOTSPOTACTIVEUNDERLINE(bool underline)</a><br />
    <a class="message" href="#SCI_GETHOTSPOTACTIVEUNDERLINE">SCI_GETHOTSPOTACTIVEUNDERLINE &rarr; bool</a><br />
    <a class="message" href="#SCI_SETHOTSPOTSINGLELINE">SCI_SETHOTSPOTSINGLELINE(bool singleLine)</a><br />
    <a class="message" href="#SCI_GETHOTSPOTSINGLELINE">SCI_GETHOTSPOTSINGLELINE &rarr; bool</a><br />
     <a class="message" href="#SCI_SETCARETSTICKY">SCI_SETCARETSTICKY(int useCaretStickyBehaviour)</a><br />
     <a class="message" href="#SCI_GETCARETSTICKY">SCI_GETCARETSTICKY &rarr; int</a><br />
     <a class="message" href="#SCI_TOGGLECARETSTICKY">SCI_TOGGLECARETSTICKY</a><br />
    </code>

    <p><b id="SCI_SETSELFORE">SCI_SETSELFORE(bool useSetting, <a class="jump" href="#colour">colour</a> fore)</b><br />
     <b id="SCI_SETSELBACK">SCI_SETSELBACK(bool useSetting, <a class="jump" href="#colour">colour</a> back)</b><br />
     You can choose to override the default selection colouring with these two messages. The colour
    you provide is used if you set <code class="parameter">useSelection*Colour</code> to <code>true</code>. If it is
    set to <code>false</code>, the default styled colouring is used and the
    <code class="parameter">fore</code> or <code class="parameter">back</code>
    argument has no effect.</p>
     <p><b id="SCI_SETSELALPHA">SCI_SETSELALPHA(<a class="jump" href="#alpha">alpha</a> alpha)</b><br />
     <b id="SCI_GETSELALPHA">SCI_GETSELALPHA &rarr; int</b><br />
     The selection can be drawn translucently in the selection background colour by
     setting an alpha value.</p>

     <p><b id="SCI_SETSELEOLFILLED">SCI_SETSELEOLFILLED(bool filled)</b><br />
     <b id="SCI_GETSELEOLFILLED">SCI_GETSELEOLFILLED &rarr; bool</b><br />
     The selection can be drawn up to the right hand border by setting this property.</p>

    <p><b id="SCI_SETCARETFORE">SCI_SETCARETFORE(<a class="jump" href="#colour">colour</a> fore)</b><br />
     <b id="SCI_GETCARETFORE">SCI_GETCARETFORE &rarr; colour</b><br />
     The colour of the caret can be set with <code>SCI_SETCARETFORE</code> and retrieved with
    <code>SCI_GETCARETFORE</code>.</p>

    <p><b id="SCI_SETCARETLINEVISIBLE">SCI_SETCARETLINEVISIBLE(bool show)</b><br />
     <b id="SCI_GETCARETLINEVISIBLE">SCI_GETCARETLINEVISIBLE &rarr; bool</b><br />
     <b id="SCI_SETCARETLINEBACK">SCI_SETCARETLINEBACK(<a class="jump" href="#colour">colour</a> back)</b><br />
     <b id="SCI_GETCARETLINEBACK">SCI_GETCARETLINEBACK &rarr; colour</b><br />
     <b id="SCI_SETCARETLINEBACKALPHA">SCI_SETCARETLINEBACKALPHA(<a class="jump" href="#alpha">alpha</a> alpha)</b><br />
     <b id="SCI_GETCARETLINEBACKALPHA">SCI_GETCARETLINEBACKALPHA &rarr; int</b><br />
     <b id="SCI_SETCARETLINEFRAME">SCI_SETCARETLINEFRAME(int width)</b><br />
     <b id="SCI_GETCARETLINEFRAME">SCI_GETCARETLINEFRAME &rarr; int</b><br />
     You can choose to make the background colour of the line containing the caret different with
    these messages. To do this, set the desired background colour with
    <code>SCI_SETCARETLINEBACK</code>, then use <code>SCI_SETCARETLINEVISIBLE(true)</code> to
    enable the effect. You can cancel the effect with <code>SCI_SETCARETLINEVISIBLE(false)</code>.
    The two <code>SCI_GETCARET*</code> functions return the state and the colour. This form of
    background colouring has highest priority when a line has markers that would otherwise change
    the background colour.
           The caret line may also be drawn translucently which allows other background colours to show
           through. This is done by setting the alpha (translucency) value by calling
           SCI_SETCARETLINEBACKALPHA. When the alpha is not SC_ALPHA_NOALPHA,
           the caret line is drawn after all other features so will affect the colour of all other features.
           Alternatively <code>SCI_SETCARETLINEFRAME</code> can be used to display the caret line framed
           instead of filling the whole background. Set width != 0 to enable this option and width = 0 to disable it.
          </p>

    <p><b id="SCI_SETCARETLINEVISIBLEALWAYS">SCI_SETCARETLINEVISIBLEALWAYS(bool alwaysVisible)</b><br />
     <b id="SCI_GETCARETLINEVISIBLEALWAYS">SCI_GETCARETLINEVISIBLEALWAYS &rarr; bool</b><br />
     Choose to make the caret line always visible even when the window is not in focus.
     Default behaviour <code>SCI_SETCARETLINEVISIBLEALWAYS(false)</code> the caret line is only visible when the window is in focus.
          </p>

    <p><b id="SCI_SETCARETPERIOD">SCI_SETCARETPERIOD(int periodMilliseconds)</b><br />
     <b id="SCI_GETCARETPERIOD">SCI_GETCARETPERIOD &rarr; int</b><br />
     The rate at which the caret blinks can be set with <code>SCI_SETCARETPERIOD</code> which
    determines the time in milliseconds that the caret is visible or invisible before changing
    state. Setting the period to 0 stops the caret blinking. The default value is 500 milliseconds.
    <code>SCI_GETCARETPERIOD</code> returns the current setting.</p>

    <p><b id="SCI_SETCARETSTYLE">SCI_SETCARETSTYLE(int caretStyle)</b><br />
     <b id="SCI_GETCARETSTYLE">SCI_GETCARETSTYLE &rarr; int</b><br />
     The style of the caret can be set with <code>SCI_SETCARETSTYLE</code> to be a line caret
    (CARETSTYLE_LINE=1), a block caret (CARETSTYLE_BLOCK=2) or to not draw at all
    (CARETSTYLE_INVISIBLE=0). The default value is the line caret (CARETSTYLE_LINE=1).
    You can determine the current caret style setting using <code>SCI_GETCARETSTYLE</code>.</p>

    <p>The block character draws most combining and multibyte character sequences successfully,
    though some fonts like Thai Fonts (and possibly others) can sometimes appear strange when
    the cursor is positioned at these characters, which may result in only drawing a part of the
    cursor character sequence. This is most notable on Windows platforms.</p>

    <p><b id="SCI_SETCARETWIDTH">SCI_SETCARETWIDTH(int pixelWidth)</b><br />
     <b id="SCI_GETCARETWIDTH">SCI_GETCARETWIDTH &rarr; int</b><br />
     The width of the line caret can be set with <code>SCI_SETCARETWIDTH</code> to a value of
    0, 1, 2 or 3 pixels. The default width is 1 pixel. You can read back the current width with
    <code>SCI_GETCARETWIDTH</code>. A width of 0 makes the caret invisible (added at version
    1.50), similar to setting the caret style to CARETSTYLE_INVISIBLE (though not interchangeable).
    This setting only affects the width of the cursor when the cursor style is set to line caret
    mode, it does not affect the width for a block caret.</p>

    <p><b id="SCI_SETHOTSPOTACTIVEFORE">SCI_SETHOTSPOTACTIVEFORE(bool useSetting, <a class="jump" href="#colour">colour</a> fore)</b><br />
    <b id="SCI_GETHOTSPOTACTIVEFORE">SCI_GETHOTSPOTACTIVEFORE &rarr; colour</b><br />
    <b id="SCI_SETHOTSPOTACTIVEBACK">SCI_SETHOTSPOTACTIVEBACK(bool useSetting,
    <a class="jump" href="#colour">colour</a> back)</b><br />
    <b id="SCI_GETHOTSPOTACTIVEBACK">SCI_GETHOTSPOTACTIVEBACK &rarr; colour</b><br />
    <b id="SCI_SETHOTSPOTACTIVEUNDERLINE">SCI_SETHOTSPOTACTIVEUNDERLINE(bool underline)</b><br />
     <b id="SCI_GETHOTSPOTACTIVEUNDERLINE">SCI_GETHOTSPOTACTIVEUNDERLINE &rarr; bool</b><br />
    <b id="SCI_SETHOTSPOTSINGLELINE">SCI_SETHOTSPOTSINGLELINE(bool singleLine)</b><br />
     <b id="SCI_GETHOTSPOTSINGLELINE">SCI_GETHOTSPOTSINGLELINE &rarr; bool</b><br />
    While the cursor hovers over text in a style with the hotspot attribute set,
    the default colouring can be modified and an underline drawn with these settings.
    Single line mode stops a hotspot from wrapping onto next line.</p>

    <p><b id="SCI_SETCARETSTICKY">SCI_SETCARETSTICKY(int useCaretStickyBehaviour)</b><br />
    <b id="SCI_GETCARETSTICKY">SCI_GETCARETSTICKY &rarr; int</b><br />
    <b id="SCI_TOGGLECARETSTICKY">SCI_TOGGLECARETSTICKY</b><br />
    These messages set, get or toggle the caretSticky setting which controls when the last position
    of the caret on the line is saved.</p>

    <p>When set to <code>SC_CARETSTICKY_OFF</code> (0), the sticky flag is off; all text changes
    (and all caret position changes) will remember the
    caret's new horizontal position when moving to different lines. This is the default.</p>

    <p>When set to <code>SC_CARETSTICKY_ON</code> (1), the sticky flag is on, and the only thing which will cause the editor to remember the
    horizontal caret position is moving the caret with mouse or keyboard (left/right arrow keys, home/end keys, etc). </p>

    <p>When set to <code>SC_CARETSTICKY_WHITESPACE</code> (2), the caret acts like mode 0 (sticky off) except under one
    special case; when space or tab characters are inserted. (Including pasting <b>only space/tabs</b> -- undo, redo,
    etc. do not exhibit this behaviour..).</p>

    <p><code>SCI_TOGGLECARETSTICKY</code> switches from <code>SC_CARETSTICKY_ON</code> and <code>SC_CARETSTICKY_WHITESPACE</code>
    to <code>SC_CARETSTICKY_OFF</code> and from <code>SC_CARETSTICKY_OFF</code> to <code>SC_CARETSTICKY_ON</code>.</p>

    <h2 id="CharacterRepresentations">Character representations</h2>

    <p>Some characters, such as control characters and invalid bytes, do not have a visual glyph or use a glyph that is hard to distinguish.</p>

    <p>Control characters (characters with codes less than 32, or between 128 and 159 in some encodings)
    are displayed by Scintilla using their mnemonics inverted in a rounded rectangle. These mnemonics come from the
    early days of signalling, though some are still used (LF = Line Feed, BS = Back Space, CR =
    Carriage Return, for example).</p>

    <p>For the low 'C0' values: "NUL", "SOH", "STX", "ETX", "EOT", "ENQ", "ACK", "BEL",
    "BS", "HT", "LF", "VT", "FF", "CR", "SO", "SI", "DLE", "DC1", "DC2", "DC3", "DC4", "NAK",
    "SYN", "ETB", "CAN", "EM", "SUB", "ESC", "FS", "GS", "RS", "US".</p>

    <p>For the high 'C1' values:
       "PAD", "HOP", "BPH", "NBH", "IND", "NEL", "SSA", "ESA",
       "HTS", "HTJ", "VTS", "PLD", "PLU", "RI", "SS2", "SS3",
       "DCS", "PU1", "PU2", "STS", "CCH", "MW", "SPA", "EPA",
       "SOS", "SGCI", "SCI", "CSI", "ST", "OSC", "PM", "APC".</p>

    <p>Invalid bytes are shown in a similar way with an 'x' followed by their value in hexadecimal, like "xFE".</p>

    <code>
     <a class="message" href="#SCI_SETREPRESENTATION">SCI_SETREPRESENTATION(const char *encodedCharacter, const char *representation)</a><br />
     <a class="message" href="#SCI_GETREPRESENTATION">SCI_GETREPRESENTATION(const char *encodedCharacter, char *representation) &rarr; int</a><br />
     <a class="message" href="#SCI_CLEARREPRESENTATION">SCI_CLEARREPRESENTATION(const char *encodedCharacter)</a><br />
    <a class="message" href="#SCI_SETCONTROLCHARSYMBOL">SCI_SETCONTROLCHARSYMBOL(int symbol)</a><br />
     <a class="message" href="#SCI_GETCONTROLCHARSYMBOL">SCI_GETCONTROLCHARSYMBOL &rarr; int</a><br />
    </code>

    <p><b id="SCI_SETREPRESENTATION">SCI_SETREPRESENTATION(const char *encodedCharacter, const char *representation)</b><br />
     <b id="SCI_GETREPRESENTATION">SCI_GETREPRESENTATION(const char *encodedCharacter, char *representation NUL-terminated) &rarr; int</b><br />
     <b id="SCI_CLEARREPRESENTATION">SCI_CLEARREPRESENTATION(const char *encodedCharacter)</b><br />
    Any character, including those normally displayed  as mnemonics may be represented by a
    string inverted in a rounded rectangle.</p>

    <p>For example, the Ohm sign &#x2126; U+2126 looks very similar to the Greek Omega character &#x3a9; U+03C9 so,
    for the UTF-8 encoding, to distinguish the Ohm sign as "U+2126 &#x2126;" this call could be made:
    <code>SCI_SETREPRESENTATION("\xe2\x84\xa6", "U+2126 \xe2\x84\xa6")</code></p>

    <p>The encodedCharacter parameter is a NUL-terminated string of the bytes for one character in the
    current encoding. This can not be used to set a representation for multiple-character strings. </p>

    <p>The NUL (0) character is a special case since the encodedCharacter parameter is NUL terminated, the NUL
    character is specified as an empty string.</p>

    <p><b id="SCI_SETCONTROLCHARSYMBOL">SCI_SETCONTROLCHARSYMBOL(int symbol)</b><br />
     <b id="SCI_GETCONTROLCHARSYMBOL">SCI_GETCONTROLCHARSYMBOL &rarr; int</b><br />
    The mnemonics may be replaced by a nominated symbol with an ASCII code in the
    range 32 to 255. If you set a symbol value less than 32, all control characters are displayed
    as mnemonics. The symbol you set is rendered in the font of the style set for the character.
    You can read back the current symbol with the <code>SCI_GETCONTROLCHARSYMBOL</code> message.
    The default symbol value is 0.</p>

    <h2 id="Margins">Margins</h2>

    <p>There may be multiple margins to the left of the text display plus a gap either side of the text.
    5 margins are allocated initially numbered from 0 to <code>SC_MAX_MARGIN</code> (4)
    but this may be changed by calling
    <a class="message" href="#SCI_SETMARGINS"><code>SCI_SETMARGINS</code></a>.
    Each margin can be set to display only symbols, line numbers, or text with
    <a class="message" href="#SCI_SETMARGINTYPEN"><code>SCI_SETMARGINTYPEN</code></a>.
    Textual margins may also display symbols.
    The markers
    that can be displayed in each margin are set with <a class="message"
    href="#SCI_SETMARGINMASKN"><code>SCI_SETMARGINMASKN</code></a>. Any markers not associated with
    a visible margin will be displayed as changes in background colour in the text. A width in
    pixels can be set for each margin. Margins with a zero width are ignored completely. You can
    choose if a mouse click in a margin sends a <a class="message"
    href="#SCN_MARGINCLICK"><code>SCN_MARGINCLICK</code></a> or <a class="message"
    href="#SCN_MARGINRIGHTCLICK"><code>SCN_MARGINRIGHTCLICK</code></a> notification to the container or
    selects a line of text.</p>

    <p>Using a margin number outside the valid range has no
    effect. By default, margin 0 is set to display line numbers, but is given a width of 0, so it
    is hidden. Margin 1 is set to display non-folding symbols and is given a width of 16 pixels, so
    it is visible. Margin 2 is set to display the folding symbols, but is given a width of 0, so it
    is hidden. Of course, you can set the margins to be whatever you wish.</p>

    <p>Styled text margins used to show revision and blame information:</p>
    <p><img src="styledmargin.png" alt="Styled text margins used to show revision and blame information" /></p>

    <code>
     <a class="message" href="#SCI_SETMARGINS">SCI_SETMARGINS(int margins)</a><br />
     <a class="message" href="#SCI_GETMARGINS">SCI_GETMARGINS &rarr; int</a><br />
     <a class="message" href="#SCI_SETMARGINTYPEN">SCI_SETMARGINTYPEN(int margin, int marginType)</a><br />
     <a class="message" href="#SCI_GETMARGINTYPEN">SCI_GETMARGINTYPEN(int margin) &rarr; int</a><br />
     <a class="message" href="#SCI_SETMARGINWIDTHN">SCI_SETMARGINWIDTHN(int margin, int
    pixelWidth)</a><br />
     <a class="message" href="#SCI_GETMARGINWIDTHN">SCI_GETMARGINWIDTHN(int margin) &rarr; int</a><br />
     <a class="message" href="#SCI_SETMARGINMASKN">SCI_SETMARGINMASKN(int margin, int
    mask)</a><br />
     <a class="message" href="#SCI_GETMARGINMASKN">SCI_GETMARGINMASKN(int margin) &rarr; int</a><br />
     <a class="message" href="#SCI_SETMARGINSENSITIVEN">SCI_SETMARGINSENSITIVEN(int margin, bool
    sensitive)</a><br />
     <a class="message" href="#SCI_GETMARGINSENSITIVEN">SCI_GETMARGINSENSITIVEN(int margin) &rarr; bool</a><br />
     <a class="message" href="#SCI_SETMARGINCURSORN">SCI_SETMARGINCURSORN(int margin, int
    cursor)</a><br />
     <a class="message" href="#SCI_GETMARGINCURSORN">SCI_GETMARGINCURSORN(int margin) &rarr; int</a><br />
     <a class="message" href="#SCI_SETMARGINBACKN">SCI_SETMARGINBACKN(int margin, colour back)</a><br />
     <a class="message" href="#SCI_GETMARGINBACKN">SCI_GETMARGINBACKN(int margin) &rarr; colour</a><br />
     <a class="message" href="#SCI_SETMARGINLEFT">SCI_SETMARGINLEFT(&lt;unused&gt;, int
    pixelWidth)</a><br />
     <a class="message" href="#SCI_GETMARGINLEFT">SCI_GETMARGINLEFT &rarr; int</a><br />
     <a class="message" href="#SCI_SETMARGINRIGHT">SCI_SETMARGINRIGHT(&lt;unused&gt;, int
    pixelWidth)</a><br />
     <a class="message" href="#SCI_GETMARGINRIGHT">SCI_GETMARGINRIGHT &rarr; int</a><br />
     <a class="message" href="#SCI_SETFOLDMARGINCOLOUR">SCI_SETFOLDMARGINCOLOUR(bool useSetting, colour back)</a><br />
     <a class="message" href="#SCI_SETFOLDMARGINHICOLOUR">SCI_SETFOLDMARGINHICOLOUR(bool useSetting, colour fore)</a><br />
     <a class="message" href="#SCI_MARGINSETTEXT">SCI_MARGINSETTEXT(int line, const char *text)</a><br />
     <a class="message" href="#SCI_MARGINGETTEXT">SCI_MARGINGETTEXT(int line, char *text) &rarr; int</a><br />
     <a class="message" href="#SCI_MARGINSETSTYLE">SCI_MARGINSETSTYLE(int line, int style)</a><br />
     <a class="message" href="#SCI_MARGINGETSTYLE">SCI_MARGINGETSTYLE(int line) &rarr; int</a><br />
     <a class="message" href="#SCI_MARGINSETSTYLES">SCI_MARGINSETSTYLES(int line, const char *styles)</a><br />
     <a class="message" href="#SCI_MARGINGETSTYLES">SCI_MARGINGETSTYLES(int line, char *styles) &rarr; int</a><br />
     <a class="message" href="#SCI_MARGINTEXTCLEARALL">SCI_MARGINTEXTCLEARALL</a><br />
     <a class="message" href="#SCI_MARGINSETSTYLEOFFSET">SCI_MARGINSETSTYLEOFFSET(int style)</a><br />
     <a class="message" href="#SCI_MARGINGETSTYLEOFFSET">SCI_MARGINGETSTYLEOFFSET &rarr; int</a><br />
     <a class="message" href="#SCI_SETMARGINOPTIONS">SCI_SETMARGINOPTIONS(int marginOptions)</a><br />
     <a class="message" href="#SCI_GETMARGINOPTIONS">SCI_GETMARGINOPTIONS &rarr; int</a><br />
    </code>

<p><b id="SCI_SETMARGINS">SCI_SETMARGINS(int margins)</b><br />
     <b id="SCI_GETMARGINS">SCI_GETMARGINS &rarr; int</b><br />
     Allocate the number of margins or find the number of margins currently allocated.</p>

    <p><b id="SCI_SETMARGINTYPEN">SCI_SETMARGINTYPEN(int margin, int marginType)</b><br />
     <b id="SCI_GETMARGINTYPEN">SCI_GETMARGINTYPEN(int margin) &rarr; int</b><br />
     These two routines set and get the type of a margin. The margin argument should be 0, 1, 2, 3 or 4.
    You can use the predefined constants <code>SC_MARGIN_SYMBOL</code> (0) and
    <code>SC_MARGIN_NUMBER</code> (1) to set a margin as either a line number or a symbol margin.
    A margin with application defined text may use <code>SC_MARGIN_TEXT</code> (4) or
    <code>SC_MARGIN_RTEXT</code> (5) to right justify the text.
    By convention, margin 0 is used for line numbers and the next two are used for symbols. You can
    also use the constants <code>SC_MARGIN_BACK</code> (2), <code>SC_MARGIN_FORE</code> (3),
    and <code>SC_MARGIN_COLOUR</code> (6) for
    symbol margins that set their background colour to match the STYLE_DEFAULT background and
    foreground colours or a specified colour.</p>

    <p><b id="SCI_SETMARGINWIDTHN">SCI_SETMARGINWIDTHN(int margin, int pixelWidth)</b><br />
     <b id="SCI_GETMARGINWIDTHN">SCI_GETMARGINWIDTHN(int margin) &rarr; int</b><br />
     These routines set and get the width of a margin in pixels. A margin with zero width is
    invisible. By default, Scintilla sets margin 1 for symbols with a width of 16 pixels, so this
    is a reasonable guess if you are not sure what would be appropriate. Line number margins widths
    should take into account the number of lines in the document and the line number style. You
    could use something like <a class="message"
    href="#SCI_TEXTWIDTH"><code>SCI_TEXTWIDTH(STYLE_LINENUMBER, "_99999")</code></a> to get a
    suitable width.</p>

    <p><b id="SCI_SETMARGINMASKN">SCI_SETMARGINMASKN(int margin, int mask)</b><br />
     <b id="SCI_GETMARGINMASKN">SCI_GETMARGINMASKN(int margin) &rarr; int</b><br />
     The mask is a 32-bit value. Each bit corresponds to one of 32 logical symbols that can be
    displayed in a margin that is enabled for symbols. There is a useful constant,
    <code>SC_MASK_FOLDERS</code> (0xFE000000 or -********), that is a mask for the 7 logical
    symbols used to denote folding. You can assign a wide range of symbols and colours to each of
    the 32 logical symbols, see <a href="#Markers">Markers</a> for more information. If <code>(mask
    &amp; SC_MASK_FOLDERS)==0</code>, the margin background colour is controlled by style 33 (<a
    class="message" href="#StyleDefinition"><code>STYLE_LINENUMBER</code></a>).</p>

    <p>You add logical markers to a line with <a class="message"
    href="#SCI_MARKERADD"><code>SCI_MARKERADD</code></a>. If a line has an associated marker that
    does not appear in the mask of any margin with a non-zero width, the marker changes the
    background colour of the line. For example, suppose you decide to use logical marker 10 to mark
    lines with a syntax error and you want to show such lines by changing the background colour.
    The mask for this marker is 1 shifted left 10 times (1&lt;&lt;10) which is 0x400. If you make
    sure that no symbol margin includes 0x400 in its mask, any line with the marker gets the
    background colour changed.</p>

    <p>To set a non-folding margin 1 use <code>SCI_SETMARGINMASKN(1, ~SC_MASK_FOLDERS)</code>
    which is the default set by Scintilla.
    To set a folding margin 2 use <code>SCI_SETMARGINMASKN(2, SC_MASK_FOLDERS)</code>.
    <code>~SC_MASK_FOLDERS</code> is 0x1FFFFFF in hexadecimal or 33554431
    decimal. Of course, you may need to display all 32 symbols in a margin, in which case use
    <code>SCI_SETMARGINMASKN(margin, -1)</code>.</p>

    <p><b id="SCI_SETMARGINSENSITIVEN">SCI_SETMARGINSENSITIVEN(int margin, bool
    sensitive)</b><br />
     <b id="SCI_GETMARGINSENSITIVEN">SCI_GETMARGINSENSITIVEN(int margin) &rarr; bool</b><br />
     Each of the five margins can be set sensitive or insensitive to mouse clicks. A click in a
    sensitive margin sends a <a class="message"
    href="#SCN_MARGINCLICK"><code>SCN_MARGINCLICK</code></a> or <a class="message"
    href="#SCN_MARGINRIGHTCLICK"><code>SCN_MARGINRIGHTCLICK</code></a> <a class="jump"
    href="#Notifications">notification</a> to the container. Margins that are not sensitive act as
    selection margins which make it easy to select ranges of lines. By default, all margins are
    insensitive.</p>

    <p><b id="SCI_SETMARGINCURSORN">SCI_SETMARGINCURSORN(int margin, int
    cursor)</b><br />
     <b id="SCI_GETMARGINCURSORN">SCI_GETMARGINCURSORN(int margin) &rarr; int</b><br />
     A reversed arrow cursor is normally shown over all margins. This may be changed to a normal arrow with
     <code>SCI_SETMARGINCURSORN(margin, SC_CURSORARROW)</code> or restored to a
     reversed arrow with
     <code>SCI_SETMARGINCURSORN(margin, SC_CURSORREVERSEARROW)</code>.</p>
    <p><b id="SCI_SETMARGINBACKN">SCI_SETMARGINBACKN(int margin, <a class="jump" href="#colour">colour</a> back)</b><br />
     <b id="SCI_GETMARGINBACKN">SCI_GETMARGINBACKN(int margin) &rarr; colour</b><br />
     A margin of type <code>SC_MARGIN_COLOUR</code>
     may have its colour set with <code>SCI_SETMARGINBACKN</code>.</p>

    <p><b id="SCI_SETMARGINLEFT">SCI_SETMARGINLEFT(&lt;unused&gt;, int pixelWidth)</b><br />
     <b id="SCI_GETMARGINLEFT">SCI_GETMARGINLEFT &rarr; int</b><br />
     <b id="SCI_SETMARGINRIGHT">SCI_SETMARGINRIGHT(&lt;unused&gt;, int pixelWidth)</b><br />
     <b id="SCI_GETMARGINRIGHT">SCI_GETMARGINRIGHT &rarr; int</b><br />
     These messages set and get the width of the blank margin on both sides of the text in pixels.
    The default is to one pixel on each side.</p>

    <p><b id="SCI_SETFOLDMARGINCOLOUR">SCI_SETFOLDMARGINCOLOUR(bool useSetting, <a class="jump" href="#colour">colour</a> back)</b><br />
     <b id="SCI_SETFOLDMARGINHICOLOUR">SCI_SETFOLDMARGINHICOLOUR(bool useSetting, <a class="jump" href="#colour">colour</a> fore)</b><br />
     These messages allow changing the colour of the fold margin and fold margin highlight.
     On Windows the fold margin colour defaults to ::GetSysColor(COLOR_3DFACE) and the fold margin highlight
     colour to ::GetSysColor(COLOR_3DHIGHLIGHT).</p>

    <p>
     <b id="SCI_MARGINSETTEXT">SCI_MARGINSETTEXT(int line, const char *text)</b><br />
     <b id="SCI_MARGINGETTEXT">SCI_MARGINGETTEXT(int line, char *text) &rarr; int</b><br />
     <b id="SCI_MARGINSETSTYLE">SCI_MARGINSETSTYLE(int line, int style)</b><br />
     <b id="SCI_MARGINGETSTYLE">SCI_MARGINGETSTYLE(int line) &rarr; int</b><br />
     <b id="SCI_MARGINSETSTYLES">SCI_MARGINSETSTYLES(int line, const char *styles)</b><br />
     <b id="SCI_MARGINGETSTYLES">SCI_MARGINGETSTYLES(int line, char *styles) &rarr; int</b><br />
     <b id="SCI_MARGINTEXTCLEARALL">SCI_MARGINTEXTCLEARALL</b><br />
     Text margins are created with the type SC_MARGIN_TEXT or SC_MARGIN_RTEXT.
     A different string may be set for each line with <code>SCI_MARGINSETTEXT</code>.
     The whole of the text margin on a line may be displayed in a particular style with
     <code>SCI_MARGINSETSTYLE</code> or each character may be individually styled with
     <code>SCI_MARGINSETSTYLES</code> which uses an array of bytes with each byte setting the style
     of the corresponding text byte similar to <code>SCI_SETSTYLINGEX</code>.
     Setting a text margin will cause a
     <a class="message" href="#SC_MOD_CHANGEMARGIN"><code>SC_MOD_CHANGEMARGIN</code></a>
     notification to be sent.
    </p>
    <p>
    Only some style attributes are active in text margins: font, size/sizeFractional, bold/weight, italics, fore, back, and characterSet.
    </p>
    <p>
     <b id="SCI_MARGINSETSTYLEOFFSET">SCI_MARGINSETSTYLEOFFSET(int style)</b><br />
     <b id="SCI_MARGINGETSTYLEOFFSET">SCI_MARGINGETSTYLEOFFSET &rarr; int</b><br />
    Margin styles may be completely separated from standard text styles by setting a style offset. For example,
    <code>SCI_MARGINSETSTYLEOFFSET(256)</code> would allow the margin styles to be numbered from
    256 up to 511 so they do not overlap styles set by lexers. Each style number set with <code>SCI_MARGINSETSTYLE</code>
    or <code>SCI_MARGINSETSTYLES</code> has the offset added before looking up the style.
    </p>
    <p>
    Always call <a class="seealso" href="#SCI_ALLOCATEEXTENDEDSTYLES">SCI_ALLOCATEEXTENDEDSTYLES</a>
    before <code>SCI_MARGINSETSTYLEOFFSET</code> and use the result as the argument to <code>SCI_MARGINSETSTYLEOFFSET</code>.
    </p>
    <p>
     <b id="SCI_SETMARGINOPTIONS">SCI_SETMARGINOPTIONS(int marginOptions)</b><br />
     <b id="SCI_GETMARGINOPTIONS">SCI_GETMARGINOPTIONS &rarr; int</b><br />
    Define margin options by enabling appropriate bit flags. At the moment, only one flag is available
    <code>SC_MARGINOPTION_SUBLINESELECT</code>=1, which controls how wrapped lines are selected when clicking
    on margin in front of them. If <code>SC_MARGINOPTION_SUBLINESELECT</code> is set only sub line of wrapped
    line is selected, otherwise whole wrapped line is selected. Margin options are set to
    <code>SC_MARGINOPTION_NONE</code>=0 by default.
    </p>

    <h2 id="Annotations">Annotations</h2>

    <p>Annotations are read-only lines of text underneath each line of editable text.
    An annotation may consist of multiple lines separated by '\n'.
    Annotations can be used to display an assembler version of code for debugging or to show diagnostic messages inline or to
    line up different versions of text in a merge tool.</p>
    <p>Annotations count as display lines for the methods
    <a class="message" href="#SCI_VISIBLEFROMDOCLINE"><code>SCI_VISIBLEFROMDOCLINE</code></a> and
    <a class="message" href="#SCI_DOCLINEFROMVISIBLE"><code>SCI_DOCLINEFROMVISIBLE</code></a></p>
    <p>Annotations used for inline diagnostics:</p>
    <p><img src="annotations.png" alt="Annotations used for inline diagnostics" /></p>

    <code>
     <a class="message" href="#SCI_ANNOTATIONSETTEXT">SCI_ANNOTATIONSETTEXT(int line, const char *text)</a><br />
     <a class="message" href="#SCI_ANNOTATIONGETTEXT">SCI_ANNOTATIONGETTEXT(int line, char *text) &rarr; int</a><br />
     <a class="message" href="#SCI_ANNOTATIONSETSTYLE">SCI_ANNOTATIONSETSTYLE(int line, int style)</a><br />
     <a class="message" href="#SCI_ANNOTATIONGETSTYLE">SCI_ANNOTATIONGETSTYLE(int line) &rarr; int</a><br />
     <a class="message" href="#SCI_ANNOTATIONSETSTYLES">SCI_ANNOTATIONSETSTYLES(int line, const char *styles)</a><br />
     <a class="message" href="#SCI_ANNOTATIONGETSTYLES">SCI_ANNOTATIONGETSTYLES(int line, char *styles) &rarr; int</a><br />
     <a class="message" href="#SCI_ANNOTATIONGETLINES">SCI_ANNOTATIONGETLINES(int line) &rarr; int</a><br />
     <a class="message" href="#SCI_ANNOTATIONCLEARALL">SCI_ANNOTATIONCLEARALL</a><br />
     <a class="message" href="#SCI_ANNOTATIONSETVISIBLE">SCI_ANNOTATIONSETVISIBLE(int visible)</a><br />
     <a class="message" href="#SCI_ANNOTATIONGETVISIBLE">SCI_ANNOTATIONGETVISIBLE &rarr; int</a><br />
     <a class="message" href="#SCI_ANNOTATIONSETSTYLEOFFSET">SCI_ANNOTATIONSETSTYLEOFFSET(int style)</a><br />
     <a class="message" href="#SCI_ANNOTATIONGETSTYLEOFFSET">SCI_ANNOTATIONGETSTYLEOFFSET &rarr; int</a><br />
    </code>

    <p>
     <b id="SCI_ANNOTATIONSETTEXT">SCI_ANNOTATIONSETTEXT(int line, const char *text)</b><br />
     <b id="SCI_ANNOTATIONGETTEXT">SCI_ANNOTATIONGETTEXT(int line, char *text) &rarr; int</b><br />
     <b id="SCI_ANNOTATIONSETSTYLE">SCI_ANNOTATIONSETSTYLE(int line, int style)</b><br />
     <b id="SCI_ANNOTATIONGETSTYLE">SCI_ANNOTATIONGETSTYLE(int line) &rarr; int</b><br />
     <b id="SCI_ANNOTATIONSETSTYLES">SCI_ANNOTATIONSETSTYLES(int line, const char *styles)</b><br />
     <b id="SCI_ANNOTATIONGETSTYLES">SCI_ANNOTATIONGETSTYLES(int line, char *styles) &rarr; int</b><br />
     <b id="SCI_ANNOTATIONGETLINES">SCI_ANNOTATIONGETLINES(int line) &rarr; int</b><br />
     <b id="SCI_ANNOTATIONCLEARALL">SCI_ANNOTATIONCLEARALL</b><br />
     A different string may be set for each line with <code>SCI_ANNOTATIONSETTEXT</code>.
     To clear annotations call <code>SCI_ANNOTATIONSETTEXT</code> with a NULL pointer.
     The whole of the text ANNOTATION on a line may be displayed in a particular style with
     <code>SCI_ANNOTATIONSETSTYLE</code> or each character may be individually styled with
     <code>SCI_ANNOTATIONSETSTYLES</code> which uses an array of bytes with each byte setting the style
     of the corresponding text byte similar to <code>SCI_SETSTYLINGEX</code>. The text must be set first as it
     specifies how long the annotation is so how many bytes of styling to read.
     Setting an annotation will cause a
     <a class="message" href="#SC_MOD_CHANGEANNOTATION"><code>SC_MOD_CHANGEANNOTATION</code></a>
     notification to be sent.
    </p>
    <p>
    The number of lines annotating a line can be retrieved with <code>SCI_ANNOTATIONGETLINES</code>.
    All the lines can be cleared of annotations with <code>SCI_ANNOTATIONCLEARALL</code>
    which is equivalent to clearing each line (setting to 0) and then deleting other memory used for this feature.
    </p>
    <p>
    Only some style attributes are active in annotations: font, size/sizeFractional, bold/weight, italics, fore, back, and characterSet.
    </p>
    <p>
     <b id="SCI_ANNOTATIONSETVISIBLE">SCI_ANNOTATIONSETVISIBLE(int visible)</b><br />
     <b id="SCI_ANNOTATIONGETVISIBLE">SCI_ANNOTATIONGETVISIBLE &rarr; int</b><br />
     Annotations can be made visible in a view and there is a choice of display style when visible.
     The two messages set and get the annotation display mode. The <code class="parameter">visible</code>
     argument can be one of:</p>

    <table class="standard" summary="Annotation visibility">
      <tbody valign="top">
        <tr>
          <th align="left"><code>ANNOTATION_HIDDEN</code></th>

          <td>0</td>

          <td>Annotations are not displayed.</td>
        </tr>

        <tr>
          <th align="left"><code>ANNOTATION_STANDARD</code></th>

          <td>1</td>

          <td>Annotations are drawn left justified with no adornment.</td>
        </tr>

        <tr>
          <th align="left"><code>ANNOTATION_BOXED</code></th>

          <td>2</td>

          <td>Annotations are indented to match the text and are surrounded by a box.</td>
        </tr>

        <tr>
          <th align="left"><code>ANNOTATION_INDENTED</code></th>

          <td>3</td>

          <td>Annotations are indented to match the text.</td>
        </tr>
      </tbody>
    </table>

    <p>
     <b id="SCI_ANNOTATIONSETSTYLEOFFSET">SCI_ANNOTATIONSETSTYLEOFFSET(int style)</b><br />
     <b id="SCI_ANNOTATIONGETSTYLEOFFSET">SCI_ANNOTATIONGETSTYLEOFFSET &rarr; int</b><br />
    Annotation styles may be completely separated from standard text styles by setting a style offset. For example,
    <code>SCI_ANNOTATIONSETSTYLEOFFSET(512)</code> would allow the annotation styles to be numbered from
    512 up to 767 so they do not overlap styles set by lexers (or margins if margins offset is 256).
    Each style number set with <code>SCI_ANNOTATIONSETSTYLE</code>
    or <code>SCI_ANNOTATIONSETSTYLES</code> has the offset added before looking up the style.
    </p>
    <p>
    Always call <a class="seealso" href="#SCI_ALLOCATEEXTENDEDSTYLES">SCI_ALLOCATEEXTENDEDSTYLES</a>
    before <code>SCI_ANNOTATIONSETSTYLEOFFSET</code> and use the result as the argument to <code>SCI_ANNOTATIONSETSTYLEOFFSET</code>.
    </p>

    <h2 id="OtherSettings">Other settings</h2>
    <code>
     <a class="message" href="#SCI_SETBUFFEREDDRAW">SCI_SETBUFFEREDDRAW(bool buffered)</a><br />
     <a class="message" href="#SCI_GETBUFFEREDDRAW">SCI_GETBUFFEREDDRAW &rarr; bool</a><br />
     <a class="message" href="#SCI_SETPHASESDRAW">SCI_SETPHASESDRAW(int phases)</a><br />
     <a class="message" href="#SCI_GETPHASESDRAW">SCI_GETPHASESDRAW &rarr; int</a><br />
     <a class="message" href="#SCI_SETTWOPHASEDRAW">SCI_SETTWOPHASEDRAW(bool twoPhase)</a><br />
     <a class="message" href="#SCI_GETTWOPHASEDRAW">SCI_GETTWOPHASEDRAW &rarr; bool</a><br />
     <a class="message" href="#SCI_SETTECHNOLOGY">SCI_SETTECHNOLOGY(int technology)</a><br />
     <a class="message" href="#SCI_GETTECHNOLOGY">SCI_GETTECHNOLOGY &rarr; int</a><br />
     <a class="message" href="#SCI_SETFONTQUALITY">SCI_SETFONTQUALITY(int fontQuality)</a><br />
     <a class="message" href="#SCI_GETFONTQUALITY">SCI_GETFONTQUALITY &rarr; int</a><br />
     <a class="message" href="#SCI_SETCODEPAGE">SCI_SETCODEPAGE(int codePage)</a><br />
     <a class="message" href="#SCI_GETCODEPAGE">SCI_GETCODEPAGE &rarr; int</a><br />
     <a class="message" href="#SCI_SETIMEINTERACTION">SCI_SETIMEINTERACTION(int imeInteraction)</a><br />
     <a class="message" href="#SCI_GETIMEINTERACTION">SCI_GETIMEINTERACTION &rarr; int</a><br />
     <a class="message" href="#SCI_GRABFOCUS">SCI_GRABFOCUS</a><br />
     <a class="message" href="#SCI_SETFOCUS">SCI_SETFOCUS(bool focus)</a><br />
     <a class="message" href="#SCI_GETFOCUS">SCI_GETFOCUS &rarr; bool</a><br />
    </code>

    <p>To forward a message <code>(WM_XXXX, WPARAM, LPARAM)</code> to Scintilla, you can use
    <code>SendMessage(hScintilla, WM_XXXX, WPARAM, LPARAM)</code> where <code>hScintilla</code> is
    the handle to the Scintilla window you created as your editor.</p>

    <p>While we are on the subject of forwarding messages in Windows, the top level window should
    forward any <code>WM_SETTINGCHANGE</code> messages to Scintilla (this is currently used to
    collect changes to mouse settings, but could be used for other user interface items in the
    future).</p>

    <p><b id="SCI_SETBUFFEREDDRAW">SCI_SETBUFFEREDDRAW(bool buffered)</b><br />
     <b id="SCI_GETBUFFEREDDRAW">SCI_GETBUFFEREDDRAW &rarr; bool</b><br />
     These messages turn buffered drawing on or off and report the buffered drawing state. Buffered
    drawing draws each line into a bitmap rather than directly to the screen and then copies the
    bitmap to the screen. This avoids flickering although it does take longer. The default is for
    drawing to be buffered on Win32 and GTK+ and to not be buffered on Cocoa and Qt.
    Buffered drawing is not supported on Cocoa.
    </p>

    <p>Current platforms perform window buffering so it is almost always better for this option to be turned off.
    For Win32 and GTK+, client code should turn off buffering at initialisation.
    There are some older platforms and unusual modes where buffering may still be useful.
    </p>

    <p><b id="SCI_SETPHASESDRAW">SCI_SETPHASESDRAW(int phases)</b><br />
     <b id="SCI_GETPHASESDRAW">SCI_GETPHASESDRAW &rarr; int</b><br />
     There are several orders in which the text area may be drawn offering a trade-off between speed
     and allowing all pixels of text to be seen even when they overlap other elements.</p>
     <p>In single phase drawing (<code>SC_PHASES_ONE</code>) each
     run of characters in one style is drawn along with its background.
     If a character overhangs the end of a run, such as in "<i>V</i>_" where the
     "<i>V</i>" is in a different style from the "_", then this can cause the right hand
     side of the "<i>V</i>" to be overdrawn by the background of the "_" which
     cuts it off.</p>
     <p>Two phase drawing (<code>SC_PHASES_TWO</code>)
     fixes this by drawing all the backgrounds of a line first and then drawing the text
     in transparent mode. Lines are drawn separately and no line will overlap another
     so any pixels that overlap into another line such as extreme ascenders and
     descenders on characters will be cut off.
     Two phase drawing may flicker more than single phase
     unless buffered drawing is on or the platform is naturally buffered.
     The default is for drawing to be two phase.</p>
     <p>Multiple phase drawing (<code>SC_PHASES_MULTIPLE</code>)
     draws the whole area multiple times, once for each feature, building up the
     the appearance in layers or phases. The coloured backgrounds for all lines are
     drawn before any text and then all the text is drawn in transparent mode over this
     combined background without clipping text to the line boundaries. This allows
     extreme ascenders and descenders to overflow into the adjacent lines.
     This mode is incompatible with buffered drawing and will act as <code>SC_PHASES_TWO</code>
     if buffered drawing is turned on.
     Multiple phase drawing is slower than two phase drawing.
     Setting the layout cache with
     <a class="message" href="#SCI_SETLAYOUTCACHE"><code>
     SCI_SETLAYOUTCACHE(SC_CACHE_PAGE)</code></a>
     or higher can ensure that multiple phase drawing is not significantly slower.</p>

    <p><b id="SCI_SETTWOPHASEDRAW">SCI_SETTWOPHASEDRAW(bool twoPhase)</b><br />
     <b id="SCI_GETTWOPHASEDRAW">SCI_GETTWOPHASEDRAW &rarr; bool</b><br />
     This property has been replaced with the preceding PHASESDRAW property which is more general,
     allowing multiple phase drawing as well as one and two phase drawing.</p>

    <p><b id="SCI_SETTECHNOLOGY">SCI_SETTECHNOLOGY(int technology)</b><br />
     <b id="SCI_GETTECHNOLOGY">SCI_GETTECHNOLOGY &rarr; int</b><br />
    The technology property allows choosing between different drawing APIs and options.
    On most platforms, the only choice is <code>SC_TECHNOLOGY_DEFAULT</code> (0).
    On Windows Vista or later, <code>SC_TECHNOLOGY_DIRECTWRITE</code> (1),
    <code>SC_TECHNOLOGY_DIRECTWRITERETAIN</code> (2), or
    <code>SC_TECHNOLOGY_DIRECTWRITEDC</code> (3)
    can be chosen to use the Direct2D and DirectWrite APIs for higher quality antialiased drawing.
    <code>SC_TECHNOLOGY_DIRECTWRITERETAIN</code> differs from
    <code>SC_TECHNOLOGY_DIRECTWRITE</code> by requesting that the frame
    is retained after being presented which may prevent drawing failures on some cards and drivers.
    <code>SC_TECHNOLOGY_DIRECTWRITEDC</code> differs from
    <code>SC_TECHNOLOGY_DIRECTWRITE</code> by using DirectWrite to draw into a GDI DC.
    Since Direct2D buffers drawing, Scintilla's buffering can be turned off with
    <code>SCI_SETBUFFEREDDRAW(0)</code>.</p>

    <p><b id="SCI_SETFONTQUALITY">SCI_SETFONTQUALITY(int fontQuality)</b><br />
     <b id="SCI_GETFONTQUALITY">SCI_GETFONTQUALITY &rarr; int</b><br />
     Manage font quality (antialiasing method). Currently, the following values are available on Windows:
     <code>SC_EFF_QUALITY_DEFAULT</code> (backward compatible),
     <code>SC_EFF_QUALITY_NON_ANTIALIASED</code>,
     <code>SC_EFF_QUALITY_ANTIALIASED</code>,
     <code>SC_EFF_QUALITY_LCD_OPTIMIZED</code>.</p>
     <p>In case it is necessary to squeeze more options into this property, only a limited number of bits defined
     by SC_EFF_QUALITY_MASK (0xf) will be used for quality.</p>

    <p><b id="SCI_SETCODEPAGE">SCI_SETCODEPAGE(int codePage)</b><br />
     <b id="SCI_GETCODEPAGE">SCI_GETCODEPAGE &rarr; int</b><br />
     Scintilla supports UTF-8, Japanese, Chinese and Korean DBCS along with single byte encodings like Latin-1.
     UTF-8 (<code>SC_CP_UTF8</code>) is the default. Use this message with
    <code class="parameter">codePage</code> set to the code page number to set Scintilla to use code page information
    to ensure multiple byte characters are treated as one character rather than multiple. This also stops
    the caret from moving between the bytes in a multi-byte character.
    Do not use this message to choose between different single byte character sets - use
    <a class="seealso" href="#SCI_STYLESETCHARACTERSET">SCI_STYLESETCHARACTERSET</a>.
    Call with
    <code class="parameter">codePage</code> set to zero to disable multi-byte support.</p>

    <p>Code page <code>SC_CP_UTF8</code> (65001) sets Scintilla into Unicode mode with the document
    treated as a sequence of characters expressed in UTF-8. The text is converted to the platform's
    normal Unicode encoding before being drawn by the OS and thus can display Hebrew, Arabic,
    Cyrillic, and Han characters. Languages which can use two characters stacked vertically in one
    horizontal space, such as Thai, will mostly work but there are some issues where the characters
    are drawn separately leading to visual glitches. Bi-directional text is not supported. </p>

    <p>Code page can be set to 65001 (UTF-8), 932 (Japanese Shift-JIS), 936 (Simplified Chinese GBK),
    949 (Korean Unified Hangul Code), 950 (Traditional Chinese Big5), or 1361 (Korean Johab).</p>

    <p><b id="SCI_SETIMEINTERACTION">SCI_SETIMEINTERACTION(int imeInteraction)</b><br />
     <b id="SCI_GETIMEINTERACTION">SCI_GETIMEINTERACTION &rarr; int</b><br />
     When entering text in Chinese, Japanese, or Korean an Input Method Editor (IME) may be displayed.
     The IME may be an extra window appearing above Scintilla or may be displayed by Scintilla itself
     as text. On some platforms there is a choice between the two techniques.
     A windowed IME <code>SC_IME_WINDOWED</code> (0) may be more similar in appearance and
     behaviour to the IME in other applications.
     An inline IME <code>SC_IME_INLINE</code> (1) may work better with some Scintilla features such as
     rectangular and multiple selection.</p>
     <p>The windowed behaviour can be chosen with <code>SCI_SETIMEINTERACTION(SC_IME_WINDOWED)</code>
     and the inline behaviour with <code>SCI_SETIMEINTERACTION(SC_IME_INLINE)</code>.
     Scintilla may ignore this call in some cases. For example, the inline behaviour might only be supported for some languages.</p>
     <p>When the inline IME mode is active, characters are added tentatively before being finalised and an
     <a class="message" href="#SCN_CHARADDED">SCN_CHARADDED</a>
     notification is sent for each character.</p>

    <p><b id="SCI_GRABFOCUS">SCI_GRABFOCUS</b><br />
     <b id="SCI_SETFOCUS">SCI_SETFOCUS(bool focus)</b><br />
     <b id="SCI_GETFOCUS">SCI_GETFOCUS &rarr; bool</b><br />
     Scintilla can be told to grab the focus with this message. This is needed more on GTK+ where
           focus handling is more complicated than on Windows.</p>

    <p>The internal focus flag can be set with <code>SCI_SETFOCUS</code>. This is used by clients
    that have complex focus requirements such as having their own window that gets the real focus
    but with the need to indicate that Scintilla has the logical focus.</p>

    <h2 id="BraceHighlighting">Brace highlighting</h2>
    <code><a class="message" href="#SCI_BRACEHIGHLIGHT">SCI_BRACEHIGHLIGHT(int posA, int
    posB)</a><br />
     <a class="message" href="#SCI_BRACEBADLIGHT">SCI_BRACEBADLIGHT(int pos)</a><br />
     <a class="message" href="#SCI_BRACEHIGHLIGHTINDICATOR">SCI_BRACEHIGHLIGHTINDICATOR(bool useSetting, int indicator)</a><br />
     <a class="message" href="#SCI_BRACEBADLIGHTINDICATOR">SCI_BRACEBADLIGHTINDICATOR(bool useSetting, int indicator)</a><br />
     <a class="message" href="#SCI_BRACEMATCH">SCI_BRACEMATCH(int pos, int maxReStyle) &rarr; position</a><br />
    </code>

    <p><b id="SCI_BRACEHIGHLIGHT">SCI_BRACEHIGHLIGHT(int posA, int posB)</b><br />
     Up to two characters can be highlighted in a 'brace highlighting style', which is defined as
    style number <a class="message" href="#StyleDefinition"><code>STYLE_BRACELIGHT</code></a> (34).
    If you have enabled indent guides, you may also wish to highlight the indent that corresponds
    with the brace. You can locate the column with <a class="message"
    href="#SCI_GETCOLUMN"><code>SCI_GETCOLUMN</code></a> and highlight the indent with <a
    class="message" href="#SCI_SETHIGHLIGHTGUIDE"><code>SCI_SETHIGHLIGHTGUIDE</code></a>.</p>

    <p><b id="SCI_BRACEBADLIGHT">SCI_BRACEBADLIGHT(int pos)</b><br />
     If there is no matching brace then the <a class="jump" href="#StyleDefinition">brace
    badlighting style</a>, style <code>STYLE_BRACEBAD</code> (35), can be used to show the brace
    that is unmatched. Using a position of <code>INVALID_POSITION</code> (-1) removes the
    highlight.</p>

    <p><b id="SCI_BRACEHIGHLIGHTINDICATOR">SCI_BRACEHIGHLIGHTINDICATOR(bool useSetting, int indicator)</b><br />
     Use specified indicator to highlight matching braces instead of changing their style.</p>

    <p><b id="SCI_BRACEBADLIGHTINDICATOR">SCI_BRACEBADLIGHTINDICATOR(bool useSetting, int indicator)</b><br />
     Use specified indicator to highlight non matching brace instead of changing its style.</p>

    <p><b id="SCI_BRACEMATCH">SCI_BRACEMATCH(int pos, int maxReStyle) &rarr; position</b><br />
     The <code>SCI_BRACEMATCH</code> message finds a corresponding matching brace given
    <code class="parameter">pos</code>, the position of one brace. The brace characters handled are '(', ')', '[',
    ']', '{', '}', '&lt;', and '&gt;'. The search is forwards from an opening brace and backwards
    from a closing brace. If the character at position is not a brace character, or a matching
    brace cannot be found, the return value is -1. Otherwise, the return value is the position of
    the matching brace.</p>

    <p>A match only occurs if the style of the matching brace is the same as the starting brace or
    the matching brace is beyond the end of styling. Nested braces are handled correctly. The
    <code class="parameter">maxReStyle</code> parameter must currently be 0 - it may be used in the future to limit
    the length of brace searches.</p>

    <h2 id="TabsAndIndentationGuides">Tabs and Indentation Guides</h2>

    <p>Indentation (the white space at the start of a line) is often used by programmers to clarify
    program structure and in some languages, for example Python, it may be part of the language
    syntax. Tabs are normally used in editors to insert a tab character or to pad text with spaces
    up to the next tab.</p>

    <p>When Scintilla is laying out a section of text, text after a tab character will usually be
    displayed at the next multiple of TABWIDTH columns from the left. However, it is also possible
    to explicitly set tabstops in pixels for each line.</p>

    <p>Scintilla can be set to treat tab and backspace in the white space at the start of a line in
    a special way: inserting a tab indents the line to the next indent position rather than just
    inserting a tab at the current character position and backspace unindents the line rather than
    deleting a character. Scintilla can also display indentation guides (vertical lines) to help
    you to generate code.</p>
    <code><a class="message" href="#SCI_SETTABWIDTH">SCI_SETTABWIDTH(int tabWidth)</a><br />
     <a class="message" href="#SCI_GETTABWIDTH">SCI_GETTABWIDTH &rarr; int</a><br />
     <a class="message" href="#SCI_CLEARTABSTOPS">SCI_CLEARTABSTOPS(int line)</a><br />
     <a class="message" href="#SCI_ADDTABSTOP">SCI_ADDTABSTOP(int line, int x)</a><br />
     <a class="message" href="#SCI_GETNEXTTABSTOP">SCI_GETNEXTTABSTOP(int line, int x) &rarr; int</a><br />
     <a class="message" href="#SCI_SETUSETABS">SCI_SETUSETABS(bool useTabs)</a><br />
     <a class="message" href="#SCI_GETUSETABS">SCI_GETUSETABS &rarr; bool</a><br />
     <a class="message" href="#SCI_SETINDENT">SCI_SETINDENT(int indentSize)</a><br />
     <a class="message" href="#SCI_GETINDENT">SCI_GETINDENT &rarr; int</a><br />
     <a class="message" href="#SCI_SETTABINDENTS">SCI_SETTABINDENTS(bool tabIndents)</a><br />
     <a class="message" href="#SCI_GETTABINDENTS">SCI_GETTABINDENTS &rarr; bool</a><br />
     <a class="message" href="#SCI_SETBACKSPACEUNINDENTS">SCI_SETBACKSPACEUNINDENTS(bool
    bsUnIndents)</a><br />
     <a class="message" href="#SCI_GETBACKSPACEUNINDENTS">SCI_GETBACKSPACEUNINDENTS &rarr; bool</a><br />
     <a class="message" href="#SCI_SETLINEINDENTATION">SCI_SETLINEINDENTATION(int line, int
    indentation)</a><br />
     <a class="message" href="#SCI_GETLINEINDENTATION">SCI_GETLINEINDENTATION(int line) &rarr; int</a><br />
     <a class="message" href="#SCI_GETLINEINDENTPOSITION">SCI_GETLINEINDENTPOSITION(int line) &rarr; position</a><br />
     <a class="message" href="#SCI_SETINDENTATIONGUIDES">SCI_SETINDENTATIONGUIDES(int indentView)</a><br />
     <a class="message" href="#SCI_GETINDENTATIONGUIDES">SCI_GETINDENTATIONGUIDES &rarr; int</a><br />
     <a class="message" href="#SCI_SETHIGHLIGHTGUIDE">SCI_SETHIGHLIGHTGUIDE(int column)</a><br />
     <a class="message" href="#SCI_GETHIGHLIGHTGUIDE">SCI_GETHIGHLIGHTGUIDE &rarr; int</a><br />
    </code>

    <p><b id="SCI_SETTABWIDTH">SCI_SETTABWIDTH(int tabWidth)</b><br />
     <b id="SCI_GETTABWIDTH">SCI_GETTABWIDTH &rarr; int</b><br />
     <code>SCI_SETTABWIDTH</code> sets the size of a tab as a multiple of the size of a space
    character in <code>STYLE_DEFAULT</code>. The default tab width is 8 characters. There are no
    limits on tab sizes, but values less than 1 or large values may have undesirable effects.</p>

    <p><b id="SCI_CLEARTABSTOPS">SCI_CLEARTABSTOPS(int line)</b><br />
     <b id="SCI_ADDTABSTOP">SCI_ADDTABSTOP(int line, int x)</b><br />
     <b id="SCI_GETNEXTTABSTOP">SCI_GETNEXTTABSTOP(int line, int x) &rarr; int</b><br />
     <code>SCI_CLEARTABSTOPS</code> clears explicit tabstops on a line. <code>SCI_ADDTABSTOP</code>
    adds an explicit tabstop at the specified distance from the left (in pixels), and
    <code>SCI_GETNEXTTABSTOP</code> gets the next explicit tabstop position set after the given x position,
    or zero if there aren't any.
    Changing tab stops produces a <a class="message" href="#SC_MOD_CHANGETABSTOPS">SC_MOD_CHANGETABSTOPS</a> notification.
    </p>

    <p><b id="SCI_SETUSETABS">SCI_SETUSETABS(bool useTabs)</b><br />
     <b id="SCI_GETUSETABS">SCI_GETUSETABS &rarr; bool</b><br />
     <code>SCI_SETUSETABS</code> determines whether indentation should be created out of a mixture
    of tabs and spaces or be based purely on spaces. Set <code class="parameter">useTabs</code> to <code>false</code>
    (0) to create all tabs and indents out of spaces. The default is <code>true</code>. You can use
    <a class="message" href="#SCI_GETCOLUMN"><code>SCI_GETCOLUMN</code></a> to get the column of a
    position taking the width of a tab into account.</p>
    <p><b id="SCI_SETINDENT">SCI_SETINDENT(int indentSize)</b><br />
     <b id="SCI_GETINDENT">SCI_GETINDENT &rarr; int</b><br />
     <code>SCI_SETINDENT</code> sets the size of indentation in terms of the width of a space in <a
    class="message" href="#StyleDefinition"><code>STYLE_DEFAULT</code></a>. If you set a width of
    0, the indent size is the same as the tab size. There are no limits on indent sizes, but values
    less than 0 or large values may have undesirable effects.
    </p>

    <p><b id="SCI_SETTABINDENTS">SCI_SETTABINDENTS(bool tabIndents)</b><br />
     <b id="SCI_GETTABINDENTS">SCI_GETTABINDENTS &rarr; bool</b><br />
     <b id="SCI_SETBACKSPACEUNINDENTS">SCI_SETBACKSPACEUNINDENTS(bool bsUnIndents)</b><br />
     <b id="SCI_GETBACKSPACEUNINDENTS">SCI_GETBACKSPACEUNINDENTS &rarr; bool</b><br />
    </p>

    <p>Inside indentation white space, the tab and backspace keys can be made to indent and
    unindent rather than insert a tab character or delete a character with the
    <code>SCI_SETTABINDENTS</code> and <code>SCI_SETBACKSPACEUNINDENTS</code> functions.</p>

    <p><b id="SCI_SETLINEINDENTATION">SCI_SETLINEINDENTATION(int line, int indentation)</b><br />
     <b id="SCI_GETLINEINDENTATION">SCI_GETLINEINDENTATION(int line) &rarr; int</b><br />
     The amount of indentation on a line can be discovered and set with
    <code>SCI_GETLINEINDENTATION</code> and <code>SCI_SETLINEINDENTATION</code>. The indentation is
    measured in character columns, which correspond to the width of space characters.</p>

    <p><b id="SCI_GETLINEINDENTPOSITION">SCI_GETLINEINDENTPOSITION(int line) &rarr; position</b><br />
     This returns the position at the end of indentation of a line.</p>

    <p><b id="SCI_SETINDENTATIONGUIDES">SCI_SETINDENTATIONGUIDES(int indentView)</b><br />
     <b id="SCI_GETINDENTATIONGUIDES">SCI_GETINDENTATIONGUIDES &rarr; int</b><br />
     Indentation guides are dotted vertical lines that appear within indentation white space every
    indent size columns. They make it easy to see which constructs line up especially when they
    extend over multiple pages. Style <a class="message"
    href="#StyleDefinition"><code>STYLE_INDENTGUIDE</code></a> (37) is used to specify the
    foreground and background colour of the indentation guides.</p>

    <p>There are 4 indentation guide views.
    SC_IV_NONE turns the feature off but the other 3 states determine how far the guides appear on
    empty lines. </p>
    <table class="standard" summary="Search flags">
      <tbody>
        <tr>
          <td><code>SC_IV_NONE</code></td>
          <td>No indentation guides are shown.</td>
        </tr>

        <tr>
          <td><code>SC_IV_REAL</code></td>
          <td>Indentation guides are shown inside real indentation white space.</td>
        </tr>

        <tr>
          <td><code>SC_IV_LOOKFORWARD</code></td>
          <td>Indentation guides are shown beyond the actual indentation up to the level of the
          next non-empty line.
          If the previous non-empty line was a fold header then indentation guides are shown for
          one more level of indent than that line. This setting is good for Python.</td>
        </tr>

        <tr>
          <td><code>SC_IV_LOOKBOTH</code></td>
          <td>Indentation guides are shown beyond the actual indentation up to the level of the
          next non-empty line or previous non-empty line whichever is the greater.
          This setting is good for most languages.</td>
        </tr>

      </tbody>
    </table>

    <p><b id="SCI_SETHIGHLIGHTGUIDE">SCI_SETHIGHLIGHTGUIDE(int column)</b><br />
     <b id="SCI_GETHIGHLIGHTGUIDE">SCI_GETHIGHLIGHTGUIDE &rarr; int</b><br />
     When brace highlighting occurs, the indentation guide corresponding to the braces may be
    highlighted with the brace highlighting style, <a class="message"
    href="#StyleDefinition"><code>STYLE_BRACELIGHT</code></a> (34). Set <code class="parameter">column</code> to 0 to
    cancel this highlight.</p>

    <h2 id="Markers">Markers</h2>

    <p>There are 32 markers, numbered 0 to <code>MARKER_MAX</code> (31), and you can assign any combination of them to each
    line in the document. Markers appear in the <a class="jump" href="#Margins">selection
    margin</a> to the left of the text. If the selection margin is set to zero width, the
    background colour of the whole line is changed instead. Marker numbers 25 to 31 are used by
    Scintilla in folding margins, and have symbolic names of the form <code>SC_MARKNUM_</code>*,
    for example <code>SC_MARKNUM_FOLDEROPEN</code>.</p>

    <p>Marker numbers 0 to 24 have no pre-defined function; you can use them to mark syntax errors
    or the current point of execution, break points, or whatever you need marking. If you do not
    need folding, you can use all 32 for any purpose you wish.</p>

    <p>Each marker number has a symbol associated with it. You can also set the foreground and
    background colour for each marker number, so you can use the same symbol more than once with
    different colouring for different uses. Scintilla has a set of symbols you can assign
    (<code>SC_MARK_</code>*) or you can use characters. By default, all 32 markers are set to
    <code>SC_MARK_CIRCLE</code> with a black foreground and a white background.</p>

    <p>The markers are drawn in the order of their numbers, so higher numbered markers appear on
    top of lower numbered ones. Markers try to move with their text by tracking where the start of
    their line moves. When a line is deleted, its markers are combined, by an <code>OR</code>
    operation, with the markers of the next line.</p>
    <code><a class="message" href="#SCI_MARKERDEFINE">SCI_MARKERDEFINE(int markerNumber, int
    markerSymbol)</a><br />
     <a class="message" href="#SCI_MARKERDEFINEPIXMAP">SCI_MARKERDEFINEPIXMAP(int markerNumber,
    const char *pixmap)</a><br />
     <a class="message" href="#SCI_RGBAIMAGESETWIDTH">SCI_RGBAIMAGESETWIDTH(int width)</a><br />
     <a class="message" href="#SCI_RGBAIMAGESETHEIGHT">SCI_RGBAIMAGESETHEIGHT(int height)</a><br />
     <a class="message" href="#SCI_RGBAIMAGESETSCALE">SCI_RGBAIMAGESETSCALE(int scalePercent)</a><br />
     <a class="message" href="#SCI_MARKERDEFINERGBAIMAGE">SCI_MARKERDEFINERGBAIMAGE(int markerNumber,
    const char *pixels)</a><br />
     <a class="message" href="#SCI_MARKERSYMBOLDEFINED">SCI_MARKERSYMBOLDEFINED(int markerNumber) &rarr; int</a><br />
     <a class="message" href="#SCI_MARKERSETFORE">SCI_MARKERSETFORE(int markerNumber, colour
    fore)</a><br />
     <a class="message" href="#SCI_MARKERSETBACK">SCI_MARKERSETBACK(int markerNumber, colour
    back)</a><br />
    <a class="message" href="#SCI_MARKERSETBACKSELECTED">SCI_MARKERSETBACKSELECTED(int markerNumber, colour
    back)</a><br />
    <a class="message" href="#SCI_MARKERENABLEHIGHLIGHT">SCI_MARKERENABLEHIGHLIGHT(bool enabled)</a><br />
     <a class="message" href="#SCI_MARKERSETALPHA">SCI_MARKERSETALPHA(int markerNumber, alpha alpha)</a><br />
     <a class="message" href="#SCI_MARKERADD">SCI_MARKERADD(int line, int markerNumber) &rarr; int</a><br />
     <a class="message" href="#SCI_MARKERADDSET">SCI_MARKERADDSET(int line, int markerSet)</a><br />
     <a class="message" href="#SCI_MARKERDELETE">SCI_MARKERDELETE(int line, int
    markerNumber)</a><br />
     <a class="message" href="#SCI_MARKERDELETEALL">SCI_MARKERDELETEALL(int markerNumber)</a><br />
     <a class="message" href="#SCI_MARKERGET">SCI_MARKERGET(int line) &rarr; int</a><br />
     <a class="message" href="#SCI_MARKERNEXT">SCI_MARKERNEXT(int lineStart, int markerMask) &rarr; int</a><br />
     <a class="message" href="#SCI_MARKERPREVIOUS">SCI_MARKERPREVIOUS(int lineStart, int markerMask) &rarr; int</a><br />
     <a class="message" href="#SCI_MARKERLINEFROMHANDLE">SCI_MARKERLINEFROMHANDLE(int markerHandle) &rarr; int</a><br />
     <a class="message" href="#SCI_MARKERDELETEHANDLE">SCI_MARKERDELETEHANDLE(int markerHandle)</a><br />
    </code>

    <p><b id="SCI_MARKERDEFINE">SCI_MARKERDEFINE(int markerNumber, int markerSymbol)</b><br />
     This message associates a marker number in the range 0 to 31 with one of the marker symbols or
    an ASCII character. The general-purpose marker symbols currently available are:<br />
    <code>SC_MARK_CIRCLE</code>,
    <code>SC_MARK_ROUNDRECT</code>,
    <code>SC_MARK_ARROW</code>,
    <code>SC_MARK_SMALLRECT</code>,
    <code>SC_MARK_SHORTARROW</code>,
    <code>SC_MARK_EMPTY</code>,
    <code>SC_MARK_ARROWDOWN</code>,
    <code>SC_MARK_MINUS</code>,
    <code>SC_MARK_PLUS</code>,
    <code>SC_MARK_ARROWS</code>,
    <code>SC_MARK_DOTDOTDOT</code>,
    <code>SC_MARK_BACKGROUND</code>,
    <code>SC_MARK_LEFTRECT</code>,
    <code>SC_MARK_FULLRECT</code>,
    <code>SC_MARK_BOOKMARK</code>, and
    <code>SC_MARK_UNDERLINE</code>.
    </p>

    <p>The <code>SC_MARK_BACKGROUND</code> marker changes the background colour of the line only.
          The <code>SC_MARK_FULLRECT</code> symbol mirrors this, changing only the margin background colour.
          <code>SC_MARK_UNDERLINE</code> draws an underline across the text.
    The <code>SC_MARK_EMPTY</code> symbol is invisible, allowing client code to track the movement
    of lines. You would also use it if you changed the folding style and wanted one or more of the
    <code>SC_FOLDERNUM_</code>* markers to have no associated symbol.</p>

    <p>Applications may use the marker symbol <code>SC_MARK_AVAILABLE</code> to indicate that
    plugins may allocate that marker number.
    </p>

    <p>There are also marker symbols designed for use in the folding margin in a flattened tree
    style.<br />
    <code>SC_MARK_BOXMINUS</code>,
    <code>SC_MARK_BOXMINUSCONNECTED</code>,
    <code>SC_MARK_BOXPLUS</code>,
    <code>SC_MARK_BOXPLUSCONNECTED</code>,
    <code>SC_MARK_CIRCLEMINUS</code>,
    <code>SC_MARK_CIRCLEMINUSCONNECTED</code>,
    <code>SC_MARK_CIRCLEPLUS</code>,
    <code>SC_MARK_CIRCLEPLUSCONNECTED</code>,
    <code>SC_MARK_LCORNER</code>,
    <code>SC_MARK_LCORNERCURVE</code>,
    <code>SC_MARK_TCORNER</code>,
    <code>SC_MARK_TCORNERCURVE</code>, and
    <code>SC_MARK_VLINE</code>.</p>
    Characters can be used as markers by adding the ASCII value of the character to
    <code>SC_MARK_CHARACTER</code> (10000). For example, to use 'A' (ASCII code 65) as marker
    number 1 use:<br />
    <code>SCI_MARKERDEFINE(1, SC_MARK_CHARACTER+65)</code>. <br />

    <p>The marker numbers <code>SC_MARKNUM_FOLDER</code> and <code>SC_MARKNUM_FOLDEROPEN</code> are
    used for showing that a fold is present and open or closed. Any symbols may be assigned for
    this purpose although the (<code>SC_MARK_PLUS</code>, <code>SC_MARK_MINUS</code>) pair or the
    (<code>SC_MARK_ARROW</code>, <code>SC_MARK_ARROWDOWN</code>) pair are good choices. As well as
    these two, more assignments are needed for the flattened tree style:
    <code>SC_MARKNUM_FOLDEREND</code>, <code>SC_MARKNUM_FOLDERMIDTAIL</code>,
    <code>SC_MARKNUM_FOLDEROPENMID</code>, <code>SC_MARKNUM_FOLDERSUB</code>, and
    <code>SC_MARKNUM_FOLDERTAIL</code>. The bits used for folding are specified by
    <code>SC_MASK_FOLDERS</code>, which is commonly used as an argument to
    <code>SCI_SETMARGINMASKN</code> when defining a margin to be used for folding.</p>

    <p>This table shows which <code>SC_MARK_</code>* symbols should be assigned to which
    <code>SC_MARKNUM_</code>* marker numbers to obtain four folding styles: Arrow (mimics
    Macintosh), plus/minus shows folded lines as '+' and opened folds as '-', Circle tree, Box
    tree.</p>

    <table class="standard" summary="Markers used for folding">
      <thead align="left">
        <tr>
          <th><code>SC_MARKNUM_</code>*</th>

          <th>Arrow</th>

          <th>Plus/minus</th>

          <th>Circle tree</th>

          <th>Box tree</th>
        </tr>
      </thead>

      <tbody valign="top">
        <tr>
          <th align="left"><code>FOLDEROPEN</code></th>

          <td><code>ARROWDOWN</code></td>

          <td><code>MINUS</code></td>

          <td><code>CIRCLEMINUS</code></td>

          <td><code>BOXMINUS</code></td>
        </tr>

        <tr>
          <th align="left"><code>FOLDER</code></th>

          <td><code>ARROW</code></td>

          <td><code>PLUS</code></td>

          <td><code>CIRCLEPLUS</code></td>

          <td><code>BOXPLUS</code></td>
        </tr>

        <tr>
          <th align="left"><code>FOLDERSUB</code></th>

          <td><code>EMPTY</code></td>

          <td><code>EMPTY</code></td>

          <td><code>VLINE</code></td>

          <td><code>VLINE</code></td>
        </tr>

        <tr>
          <th align="left"><code>FOLDERTAIL</code></th>

          <td><code>EMPTY</code></td>

          <td><code>EMPTY</code></td>

          <td><code>LCORNERCURVE</code></td>

          <td><code>LCORNER</code></td>
        </tr>

        <tr>
          <th align="left"><code>FOLDEREND</code></th>

          <td><code>EMPTY</code></td>

          <td><code>EMPTY</code></td>

          <td><code>CIRCLEPLUSCONNECTED</code></td>

          <td><code>BOXPLUSCONNECTED</code></td>
        </tr>

        <tr>
          <th align="left"><code>FOLDEROPENMID</code></th>

          <td><code>EMPTY</code></td>

          <td><code>EMPTY</code></td>

          <td><code>CIRCLEMINUSCONNECTED</code></td>

          <td><code>BOXMINUSCONNECTED</code></td>
        </tr>

        <tr>
          <th align="left"><code>FOLDERMIDTAIL</code></th>

          <td><code>EMPTY</code></td>

          <td><code>EMPTY</code></td>

          <td><code>TCORNERCURVE</code></td>

          <td><code>TCORNER</code></td>
        </tr>
      </tbody>
    </table>
    <p><img src="Markers.png" alt="Marker samples" /></p>

    <p><b id="SCI_MARKERDEFINEPIXMAP">SCI_MARKERDEFINEPIXMAP(int markerNumber, const char
    *pixmap)</b><br />
     Markers can be set to pixmaps with this message. The
     <a class="jump" href="#XPM">XPM format</a> is used for the pixmap.
    Pixmaps use the <code>SC_MARK_PIXMAP</code> marker symbol. </p>

    <p>
    <b id="SCI_RGBAIMAGESETWIDTH">SCI_RGBAIMAGESETWIDTH(int width)</b><br />
    <b id="SCI_RGBAIMAGESETHEIGHT">SCI_RGBAIMAGESETHEIGHT(int height)</b><br />
    <b id="SCI_RGBAIMAGESETSCALE">SCI_RGBAIMAGESETSCALE(int scalePercent)</b><br />
    <b id="SCI_MARKERDEFINERGBAIMAGE">SCI_MARKERDEFINERGBAIMAGE(int markerNumber, const char *pixels)</b><br />
     Markers can be set to translucent pixmaps with this message. The
     <a class="jump" href="#RGBA">RGBA format</a> is used for the pixmap.
     The width and height must previously been set with the <code>SCI_RGBAIMAGESETWIDTH</code> and
     <code>SCI_RGBAIMAGESETHEIGHT</code> messages.</p>
     <p>A scale factor in percent may be set with <code>SCI_RGBAIMAGESETSCALE</code>. This is useful on OS X with
     a retina display where each display unit is 2 pixels: use a factor of 200 so that each image pixel is displayed using a screen pixel.
     The default scale, 100, will stretch each image pixel to cover 4 screen pixels on a retina display.</p>
    <p>Pixmaps use the <code>SC_MARK_RGBAIMAGE</code> marker symbol. </p>

    <p><b id="SCI_MARKERSYMBOLDEFINED">SCI_MARKERSYMBOLDEFINED(int markerNumber) &rarr; int</b><br />
     Returns the symbol defined for a markerNumber with <code>SCI_MARKERDEFINE</code>
     or <code>SC_MARK_PIXMAP</code> if defined with <code>SCI_MARKERDEFINEPIXMAP</code>
     or <code>SC_MARK_RGBAIMAGE</code> if defined with <code>SCI_MARKERDEFINERGBAIMAGE</code>.</p>

    <p><b id="SCI_MARKERSETFORE">SCI_MARKERSETFORE(int markerNumber, <a class="jump" href="#colour">colour</a> fore)</b><br />
     <b id="SCI_MARKERSETBACK">SCI_MARKERSETBACK(int markerNumber, <a class="jump" href="#colour">colour</a> back)</b><br />
     These two messages set the foreground and background colour of a marker number.<br />
     <b id="SCI_MARKERSETBACKSELECTED">SCI_MARKERSETBACKSELECTED(int markerNumber, <a class="jump" href="#colour">colour</a> back)</b><br />
     This message sets the highlight background colour of a marker number when its folding block is selected. The default colour is #FF0000.</p>
     <p><b id="SCI_MARKERENABLEHIGHLIGHT">SCI_MARKERENABLEHIGHLIGHT(bool enabled)</b><br />
     This message allows to enable/disable the highlight folding block when it is selected. (i.e. block that contains the caret)</p>
     <p><b id="SCI_MARKERSETALPHA">SCI_MARKERSETALPHA(int markerNumber,
     <a class="jump" href="#alpha">alpha</a> alpha)</b><br />
     When markers are drawn in the content area, either because there is no margin for them or
     they are of <code>SC_MARK_BACKGROUND</code> or <code>SC_MARK_UNDERLINE</code> types, they may be drawn translucently by
     setting an alpha value.</p>

    <p><b id="SCI_MARKERADD">SCI_MARKERADD(int line, int markerNumber) &rarr; int</b><br />
     This message adds marker number <code class="parameter">markerNumber</code> to a line. The message returns -1 if
    this fails (illegal line number, out of memory) or it returns a marker handle number that
    identifies the added marker. You can use this returned handle with <a class="seealso"
    href="#SCI_MARKERLINEFROMHANDLE"><code>SCI_MARKERLINEFROMHANDLE</code></a> to find where a
    marker is after moving or combining lines and with <a class="message"
    href="#SCI_MARKERDELETEHANDLE"><code>SCI_MARKERDELETEHANDLE</code></a> to delete the marker
    based on its handle. The message does not check the value of markerNumber, nor does it
    check if the line already contains the marker.</p>

    <p><b id="SCI_MARKERADDSET">SCI_MARKERADDSET(int line, int markerSet)</b><br />
     This message can add one or more markers to a line with a single call, specified in the same "one-bit-per-marker" 32-bit integer format returned by
    <a class="message" href="#SCI_MARKERGET"><code>SCI_MARKERGET</code></a>
    (and used by the mask-based marker search functions
    <a class="message" href="#SCI_MARKERNEXT"><code>SCI_MARKERNEXT</code></a> and
    <a class="message" href="#SCI_MARKERPREVIOUS"><code>SCI_MARKERPREVIOUS</code></a>).
    As with
    <a class="message" href="#SCI_MARKERADD"><code>SCI_MARKERADD</code></a>, no check is made
    to see if any of the markers are already present on the targeted line.</p>

    <p><b id="SCI_MARKERDELETE">SCI_MARKERDELETE(int line, int markerNumber)</b><br />
     This searches the given line number for the given marker number and deletes it if it is
    present. If you added the same marker more than once to the line, this will delete one copy
    each time it is used. If you pass in a marker number of -1, all markers are deleted from the
    line.</p>

    <p><b id="SCI_MARKERDELETEALL">SCI_MARKERDELETEALL(int markerNumber)</b><br />
     This removes markers of the given number from all lines. If markerNumber is -1, it deletes all
    markers from all lines.</p>

    <p><b id="SCI_MARKERGET">SCI_MARKERGET(int line) &rarr; int</b><br />
     This returns a 32-bit integer that indicates which markers were present on the line. Bit 0 is
    set if marker 0 is present, bit 1 for marker 1 and so on.</p>

    <p><b id="SCI_MARKERNEXT">SCI_MARKERNEXT(int lineStart, int markerMask) &rarr; int</b><br />
     <b id="SCI_MARKERPREVIOUS">SCI_MARKERPREVIOUS(int lineStart, int markerMask) &rarr; int</b><br />
     These messages search efficiently for lines that include a given set of markers. The search
    starts at line number <code class="parameter">lineStart</code> and continues forwards to the end of the file
    (<code>SCI_MARKERNEXT</code>) or backwards to the start of the file
    (<code>SCI_MARKERPREVIOUS</code>). The <code class="parameter">markerMask</code> argument should have one bit set
    for each marker you wish to find. Set bit 0 to find marker 0, bit 1 for marker 1 and so on. The
    message returns the line number of the first line that contains one of the markers in
    <code class="parameter">markerMask</code> or -1 if no marker is found.</p>

    <p><b id="SCI_MARKERLINEFROMHANDLE">SCI_MARKERLINEFROMHANDLE(int markerHandle) &rarr; int</b><br />
     The <code class="parameter">markerHandle</code> argument is an identifier for a marker returned by <a
    class="message" href="#SCI_MARKERADD"><code>SCI_MARKERADD</code></a>. This function searches
    the document for the marker with this handle and returns the line number that contains it or -1
    if it is not found.</p>

    <p><b id="SCI_MARKERDELETEHANDLE">SCI_MARKERDELETEHANDLE(int markerHandle)</b><br />
     The <code class="parameter">markerHandle</code> argument is an identifier for a marker returned by <a
    class="message" href="#SCI_MARKERADD"><code>SCI_MARKERADD</code></a>. This function searches
    the document for the marker with this handle and deletes the marker if it is found.</p>

    <h2 id="Indicators">Indicators</h2>

    <p>Indicators are used to display additional information over the top of styling.
    They can be used to show, for example, syntax errors, deprecated names and bad indentation
    by drawing underlines under text or boxes around text.</p>

    <p>Indicators may have a different "hover" colour and style when the mouse is over them or the caret is moved into them.
    This may be used, for example, to indicate that a URL can be clicked.</p>

    <p>Indicators may be displayed as simple underlines, squiggly underlines, a
    line of small 'T' shapes, a line of diagonal hatching, a strike-out or a rectangle around the text.
    They may also be invisible when used to track pieces of content for the application as <code>INDIC_HIDDEN</code>.</p>

    <p>The <code>SCI_INDIC*</code> messages allow you to get and set the visual appearance of the
    indicators. They all use an <code class="parameter">indicator</code> argument in the range 0 to INDIC_MAX(35)
    to set the indicator to style. To prevent interference the set of indicators is divided up into a range for use
    by lexers (0..7) a range for use by containers
    (8=<code>INDIC_CONTAINER</code> .. 31=<code>INDIC_IME-1</code>)
    and a range for IME indicators (32=<code>INDIC_IME</code> .. 35=<code>INDIC_IME_MAX</code>).</p>

    <p>Indicators are stored in a format similar to run length encoding which is efficient in both
    speed and storage for sparse information.</p>
    <p>An indicator may store different values for each range but normally all values are drawn the same.
    The <a class="seealso" href="#SCI_INDICSETFLAGS">SCI_INDICSETFLAGS</a>
    API may be used to display different colours for different values.</p>

    <p>Originally, Scintilla used a different technique for indicators but this
    has been <a href="#RemovedFeatures">removed</a>
    and the APIs perform <a href="#StyleByteIndicators">no action</a>.
    While both techniques were supported, the term "modern indicators" was used for the
    newer implementation.</p>

    <code><a class="message" href="#SCI_INDICSETSTYLE">SCI_INDICSETSTYLE(int indicator, int
    indicatorStyle)</a><br />
     <a class="message" href="#SCI_INDICGETSTYLE">SCI_INDICGETSTYLE(int indicator) &rarr; int</a><br />
     <a class="message" href="#SCI_INDICSETFORE">SCI_INDICSETFORE(int indicator, colour
    fore)</a><br />
     <a class="message" href="#SCI_INDICGETFORE">SCI_INDICGETFORE(int indicator) &rarr; colour</a><br />
     <a class="message" href="#SCI_INDICSETALPHA">SCI_INDICSETALPHA(int indicator, alpha alpha)</a><br />
     <a class="message" href="#SCI_INDICGETALPHA">SCI_INDICGETALPHA(int indicator) &rarr; int</a><br />
     <a class="message" href="#SCI_INDICSETOUTLINEALPHA">SCI_INDICSETOUTLINEALPHA(int indicator, alpha alpha)</a><br />
     <a class="message" href="#SCI_INDICGETOUTLINEALPHA">SCI_INDICGETOUTLINEALPHA(int indicator) &rarr; int</a><br />
     <a class="message" href="#SCI_INDICSETUNDER">SCI_INDICSETUNDER(int indicator, bool under)</a><br />
     <a class="message" href="#SCI_INDICGETUNDER">SCI_INDICGETUNDER(int indicator) &rarr; bool</a><br />
    <a class="message" href="#SCI_INDICSETHOVERSTYLE">SCI_INDICSETHOVERSTYLE(int indicator, int
    indicatorStyle)</a><br />
     <a class="message" href="#SCI_INDICGETHOVERSTYLE">SCI_INDICGETHOVERSTYLE(int indicator) &rarr; int</a><br />
     <a class="message" href="#SCI_INDICSETHOVERFORE">SCI_INDICSETHOVERFORE(int indicator, colour
    fore)</a><br />
     <a class="message" href="#SCI_INDICGETHOVERFORE">SCI_INDICGETHOVERFORE(int indicator) &rarr; colour</a><br />
     <a class="message" href="#SCI_INDICSETFLAGS">SCI_INDICSETFLAGS(int indicator, int flags)</a><br />
     <a class="message" href="#SCI_INDICGETFLAGS">SCI_INDICGETFLAGS(int indicator) &rarr; int</a><br />
     <br />

     <a class="message" href="#SCI_SETINDICATORCURRENT">SCI_SETINDICATORCURRENT(int indicator)</a><br />
     <a class="message" href="#SCI_GETINDICATORCURRENT">SCI_GETINDICATORCURRENT &rarr; int</a><br />
     <a class="message" href="#SCI_SETINDICATORVALUE">SCI_SETINDICATORVALUE(int value)</a><br />
     <a class="message" href="#SCI_GETINDICATORVALUE">SCI_GETINDICATORVALUE &rarr; int</a><br />
     <a class="message" href="#SCI_INDICATORFILLRANGE">SCI_INDICATORFILLRANGE(int start, int lengthFill)</a><br />
     <a class="message" href="#SCI_INDICATORCLEARRANGE">SCI_INDICATORCLEARRANGE(int start, int lengthClear)</a><br />
     <a class="message" href="#SCI_INDICATORALLONFOR">SCI_INDICATORALLONFOR(int pos) &rarr; int</a><br />
     <a class="message" href="#SCI_INDICATORVALUEAT">SCI_INDICATORVALUEAT(int indicator, int pos) &rarr; int</a><br />
     <a class="message" href="#SCI_INDICATORSTART">SCI_INDICATORSTART(int indicator, int pos) &rarr; int</a><br />
     <a class="message" href="#SCI_INDICATOREND">SCI_INDICATOREND(int indicator, int pos) &rarr; int</a><br />

     <a class="message" href="#SCI_FINDINDICATORSHOW">SCI_FINDINDICATORSHOW(int start, int end)</a><br />
     <a class="message" href="#SCI_FINDINDICATORFLASH">SCI_FINDINDICATORFLASH(int start, int end)</a><br />
     <a class="message" href="#SCI_FINDINDICATORHIDE">SCI_FINDINDICATORHIDE</a><br />
    </code>

    <p><b id="SCI_INDICSETSTYLE">SCI_INDICSETSTYLE(int indicator, int
    indicatorStyle)</b><br />
     <b id="SCI_INDICGETSTYLE">SCI_INDICGETSTYLE(int indicator) &rarr; int</b><br />
     These two messages set and get the style for a particular indicator. The indicator styles
    currently available are:<br />

    <img src="Indicators.png" alt="Indicator samples" /></p>

    <table class="standard" summary="Indicators">
      <tbody>
        <tr>
          <th align="left">Symbol</th>

          <th>Value</th>

          <th align="left">Visual effect</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>INDIC_PLAIN</code></td>

          <td align="center">0</td>

          <td>Underlined with a single, straight line.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_SQUIGGLE</code></td>

          <td align="center">1</td>

          <td>A squiggly underline. Requires 3 pixels of descender space.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_TT</code></td>

          <td align="center">2</td>

          <td>A line of small T shapes.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_DIAGONAL</code></td>

          <td align="center">3</td>

          <td>Diagonal hatching.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_STRIKE</code></td>

          <td align="center">4</td>

          <td>Strike out.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_HIDDEN</code></td>

          <td align="center">5</td>

          <td>An indicator with no visual effect.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_BOX</code></td>

          <td align="center">6</td>

          <td>A rectangle around the text.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_ROUNDBOX</code></td>

          <td align="center">7</td>

          <td>A rectangle with rounded corners around the text using translucent drawing with the
              interior usually more transparent than the border. You can use
              <a class="seealso" href="#SCI_INDICSETALPHA">SCI_INDICSETALPHA</a> and
			  <a class="seealso" href="#SCI_INDICSETOUTLINEALPHA">SCI_INDICSETOUTLINEALPHA</a>
              to control the alpha transparency values. The default alpha values are 30 for fill colour and 50 for outline colour.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_STRAIGHTBOX</code></td>

          <td align="center">8</td>

          <td>A rectangle around the text using translucent drawing with the
              interior usually more transparent than the border. You can use
              <a class="seealso" href="#SCI_INDICSETALPHA">SCI_INDICSETALPHA</a> and
			  <a class="seealso" href="#SCI_INDICSETOUTLINEALPHA">SCI_INDICSETOUTLINEALPHA</a>
              to control the alpha transparency values. The default alpha values are 30 for fill colour and 50 for outline colour.
              This indicator does not colour the top pixel of the line so that indicators on contiguous lines are visually distinct
              and disconnected.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_FULLBOX</code></td>

          <td align="center">16</td>

          <td>A rectangle around the text using translucent drawing similar to <code>INDIC_STRAIGHTBOX</code>
              but covering the entire character area.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_DASH</code></td>

          <td align="center">9</td>

          <td>A dashed underline.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_DOTS</code></td>

          <td align="center">10</td>

          <td>A dotted underline.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_SQUIGGLELOW</code></td>

          <td align="center">11</td>

          <td>Similar to <code>INDIC_SQUIGGLE</code> but only using 2 vertical pixels
	  so will fit under small fonts.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_DOTBOX</code></td>

          <td align="center">12</td>

          <td>A dotted rectangle around the text using translucent drawing.
          Translucency alternates between the alpha and outline alpha settings with the top-left pixel using the alpha setting.
              <a class="seealso" href="#SCI_INDICSETALPHA">SCI_INDICSETALPHA</a> and
              <a class="seealso" href="#SCI_INDICSETOUTLINEALPHA">SCI_INDICSETOUTLINEALPHA</a>
              control the alpha transparency values. The default values are 30 for alpha and 50 for outline alpha.
              To avoid excessive memory allocation the maximum width of a dotted box is 4000 pixels.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_GRADIENT</code></td>

          <td align="center">20</td>

          <td>A vertical gradient between a colour and alpha at top to fully transparent at bottom.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_GRADIENTCENTRE</code></td>

          <td align="center">21</td>

          <td>A vertical gradient with the specified colour and alpha in the middle
          fading to fully transparent at top and bottom.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_SQUIGGLEPIXMAP</code></td>

          <td align="center">13</td>

          <td>A version of <code>INDIC_SQUIGGLE</code> that draws using a pixmap instead of
	  as a series of line segments for performance.
	  Measured to be between 3 and 6 times faster than <code>INDIC_SQUIGGLE</code> on GTK+.
	  Appearance will not be as good as <code>INDIC_SQUIGGLE</code> on OS X in HiDPI mode.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_COMPOSITIONTHICK</code></td>

          <td align="center">14</td>

          <td>A 2-pixel thick underline located at the bottom of the line to try to avoid touching the character base.
	  Each side is inset 1 pixel so that different indicators in this style covering a range appear isolated.
	  This is similar to an appearance used for the target in Asian language input composition.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_COMPOSITIONTHIN</code></td>

          <td align="center">15</td>

          <td>A 1-pixel thick underline located just before the bottom of the line.
	  Each side is inset 1 pixel so that different indicators in this style covering a range appear isolated.
	  This is similar to an appearance used for non-target ranges in Asian language input composition.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_TEXTFORE</code></td>

          <td align="center">17</td>

          <td>Change the colour of the text to the indicator's fore colour.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_POINT</code></td>

          <td align="center">18</td>

          <td>Draw a triangle below the start of the indicator range.</td>
        </tr>

        <tr>
          <td align="left"><code>INDIC_POINTCHARACTER</code></td>

          <td align="center">19</td>

          <td>Draw a triangle below the centre of the first character of the indicator range.</td>
        </tr>

      </tbody>
    </table>

    <p>The default indicator styles are equivalent to:<br />
     <code>SCI_INDICSETSTYLE(0, INDIC_SQUIGGLE);</code><br />
     <code>SCI_INDICSETSTYLE(1, INDIC_TT);</code><br />
     <code>SCI_INDICSETSTYLE(2, INDIC_PLAIN);</code></p>

    <p><b id="SCI_INDICSETFORE">SCI_INDICSETFORE(int indicator, <a class="jump" href="#colour">colour</a> fore)</b><br />
     <b id="SCI_INDICGETFORE">SCI_INDICGETFORE(int indicator) &rarr; colour</b><br />
     These two messages set and get the colour used to draw an indicator. The default indicator
    colours are equivalent to:<br />
     <code>SCI_INDICSETFORE(0, 0x007f00);</code> (dark green)<br />
     <code>SCI_INDICSETFORE(1, 0xff0000);</code> (light blue)<br />
     <code>SCI_INDICSETFORE(2, 0x0000ff);</code> (light red)</p>

    <p><b id="SCI_INDICSETALPHA">SCI_INDICSETALPHA(int indicator, <a class="jump" href="#alpha">alpha</a> alpha)</b><br />
     <b id="SCI_INDICGETALPHA">SCI_INDICGETALPHA(int indicator) &rarr; int</b><br />
     These two messages set and get the alpha transparency used for drawing the
     fill colour of the INDIC_ROUNDBOX and INDIC_STRAIGHTBOX rectangle. The alpha value can range from
     0 (completely transparent) to 255 (no transparency).
     </p>

    <p><b id="SCI_INDICSETOUTLINEALPHA">SCI_INDICSETOUTLINEALPHA(int indicator, <a class="jump" href="#alpha">alpha</a> alpha)</b><br />
     <b id="SCI_INDICGETOUTLINEALPHA">SCI_INDICGETOUTLINEALPHA(int indicator) &rarr; int</b><br />
     These two messages set and get the alpha transparency used for drawing the
     outline colour of the INDIC_ROUNDBOX and INDIC_STRAIGHTBOX rectangle. The alpha value can range from
     0 (completely transparent) to 255 (no transparency).
     </p>

    <p><b id="SCI_INDICSETUNDER">SCI_INDICSETUNDER(int indicator, bool under)</b><br />
     <b id="SCI_INDICGETUNDER">SCI_INDICGETUNDER(int indicator) &rarr; bool</b><br />
     These two messages set and get whether an indicator is drawn under text or over(default).
     Drawing under text works only for indicators when <a class="message" href="#SCI_SETTWOPHASEDRAW">two phase drawing</a>
     is enabled.</p>

    <p><b id="SCI_INDICSETHOVERSTYLE">SCI_INDICSETHOVERSTYLE(int indicator, int
    indicatorStyle)</b><br />
     <b id="SCI_INDICGETHOVERSTYLE">SCI_INDICGETHOVERSTYLE(int indicator) &rarr; int</b><br />
     <b id="SCI_INDICSETHOVERFORE">SCI_INDICSETHOVERFORE(int indicator, <a class="jump" href="#colour">colour</a> fore)</b><br />
     <b id="SCI_INDICGETHOVERFORE">SCI_INDICGETHOVERFORE(int indicator) &rarr; colour</b><br />
     These messages set and get the colour and style used to draw indicators when the mouse is over them or the caret moved into them.
     The mouse cursor also changes when an indicator is drawn in hover style.
     The default is for the hover appearance to be the same as the normal appearance and calling
     <a class="seealso" href="#SCI_INDICSETFORE">SCI_INDICSETFORE</a> or
     <a class="seealso" href="#SCI_INDICSETSTYLE">SCI_INDICSETSTYLE</a> will
     also reset the hover attribute.</p>

    <p><b id="SCI_INDICSETFLAGS">SCI_INDICSETFLAGS(int indicator, int flags)</b><br />
     <b id="SCI_INDICGETFLAGS">SCI_INDICGETFLAGS(int indicator) &rarr; int</b><br />
     These messages set and get the flags associated with an indicator.
     There is currently one flag defined, <code>SC_INDICFLAG_VALUEFORE</code>: when this flag is set
     the colour used by the indicator is not from the indicator's fore setting but instead from the value of the indicator at
     that point in the file. This allows many colours to be displayed for a single indicator. The value is an <a class="jump"
    href="#colour">RGB integer colour</a> that has been ored with <code>SC_INDICVALUEBIT</code>(0x1000000)
    when calling <a class="seealso" href="#SCI_SETINDICATORVALUE">SCI_SETINDICATORVALUE</a>.
    To find the colour from the value, and the value with <code>SC_INDICVALUEMASK</code>(0xFFFFFF).
     </p>

    <p>
    <b id="SCI_SETINDICATORCURRENT">SCI_SETINDICATORCURRENT(int indicator)</b><br />
    <b id="SCI_GETINDICATORCURRENT">SCI_GETINDICATORCURRENT &rarr; int</b><br />
    These two messages set and get the indicator that will be affected by calls to
    <a class="message" href="#SCI_INDICATORFILLRANGE">SCI_INDICATORFILLRANGE(int start, int lengthFill)</a> and
    <a class="message" href="#SCI_INDICATORCLEARRANGE">SCI_INDICATORCLEARRANGE(int start, int lengthClear)</a>.
    </p>

    <p>
    <b id="SCI_SETINDICATORVALUE">SCI_SETINDICATORVALUE(int value)</b><br />
    <b id="SCI_GETINDICATORVALUE">SCI_GETINDICATORVALUE &rarr; int</b><br />
    These two messages set and get the value that will be set by calls to
    <a class="seealso" href="#SCI_INDICATORFILLRANGE">SCI_INDICATORFILLRANGE</a>.
    </p>

    <p>
    <b id="SCI_INDICATORFILLRANGE">SCI_INDICATORFILLRANGE(int start, int lengthFill)</b><br />
    <b id="SCI_INDICATORCLEARRANGE">SCI_INDICATORCLEARRANGE(int start, int lengthClear)</b><br />
    These two messages fill or clear a range for the current indicator.
    <code>SCI_INDICATORFILLRANGE</code> fills with the
    the current value.
    </p>

    <p>
    <b id="SCI_INDICATORALLONFOR">SCI_INDICATORALLONFOR(int pos) &rarr; int</b><br />
    Retrieve a bitmap value representing which indicators are non-zero at a position.
    Only the first 32 indicators are represented in the result so no IME indicators are included.
    </p>

    <p>
    <b id="SCI_INDICATORVALUEAT">SCI_INDICATORVALUEAT(int indicator, int pos) &rarr; int</b><br />
    Retrieve the value of a particular indicator at a position.
    </p>

    <p>
    <b id="SCI_INDICATORSTART">SCI_INDICATORSTART(int indicator, int pos) &rarr; int</b><br />
    <b id="SCI_INDICATOREND">SCI_INDICATOREND(int indicator, int pos) &rarr; int</b><br />
    Find the start or end of a range with one value from a position within the range.
    Can be used to iterate through the document to discover all the indicator positions.
    </p>

    <h3 id="FindIndicators">OS X Find Indicator</h3>

    <p>On OS X search matches are highlighted with an animated gold rounded rectangle.
    The indicator shows, then briefly grows 25% and shrinks to the original size to draw the user's attention.
    While this feature is currently only implemented on OS X, it may be implemented on other platforms
    in the future.</p>

    <p><b id="SCI_FINDINDICATORSHOW">SCI_FINDINDICATORSHOW(int start, int end)</b><br />
     <b id="SCI_FINDINDICATORFLASH">SCI_FINDINDICATORFLASH(int start, int end)</b><br />
     These two messages show and animate the find indicator. The indicator remains visible with
     <code>SCI_FINDINDICATORSHOW</code> and fades out after showing for half a second with
     <code>SCI_FINDINDICATORFLASH</code>.
     <code>SCI_FINDINDICATORSHOW</code> behaves similarly to the OS X TextEdit and Safari applications
     and is best suited to editing documentation where the search target is often a word.
     <code>SCI_FINDINDICATORFLASH</code> is similar to Xcode and is suited to editing source code
     where the match will often be located next to operators which would otherwise be hidden under the indicator's
     padding.
     </p>

    <p><b id="SCI_FINDINDICATORHIDE">SCI_FINDINDICATORHIDE</b><br />
     This message hides the find indicator.
     </p>

    <p>Earlier versions of Scintilla allowed <a href="#StyleByteIndicators">partitioning style bytes</a>
    between style numbers and indicators and provided APIs for setting and querying this.</p>


    <h2 id="Autocompletion">Autocompletion</h2>

    <p>Autocompletion displays a list box showing likely identifiers based upon the user's typing.
    The user chooses the currently selected item by pressing the tab character or another character
    that is a member of the fillup character set defined with <code>SCI_AUTOCSETFILLUPS</code>.
    Autocompletion is triggered by your application. For example, in C if you detect that the user
    has just typed <code>fred.</code> you could look up <code>fred</code>, and if it has a known
    list of members, you could offer them in an autocompletion list. Alternatively, you could
    monitor the user's typing and offer a list of likely items once their typing has narrowed down
    the choice to a reasonable list. As yet another alternative, you could define a key code to
    activate the list.</p>

    <p>When the user makes a selection from the list the container is sent a <code><a class="message"
    href="#SCN_AUTOCSELECTION">SCN_AUTOCSELECTION</a></code> <a class="jump"
    href="#Notifications">notification message</a>. On return from the notification Scintilla will insert
     the selected text and the container is sent a <code><a class="message"
    href="#SCN_AUTOCCOMPLETED">SCN_AUTOCCOMPLETED</a></code> <a class="jump"
    href="#Notifications">notification message</a> unless the autocompletion list has been cancelled, for example by the container sending
     <code><a class="message" href="#SCI_AUTOCCANCEL">SCI_AUTOCCANCEL</a></code>. </p>

    <p>To make use of autocompletion you must monitor each character added to the document. See
    <code>SciTEBase::CharAdded()</code> in SciTEBase.cxx for an example of autocompletion.</p>
    <code><a class="message" href="#SCI_AUTOCSHOW">SCI_AUTOCSHOW(int lengthEntered, const char
    *itemList)</a><br />
     <a class="message" href="#SCI_AUTOCCANCEL">SCI_AUTOCCANCEL</a><br />
     <a class="message" href="#SCI_AUTOCACTIVE">SCI_AUTOCACTIVE &rarr; bool</a><br />
     <a class="message" href="#SCI_AUTOCPOSSTART">SCI_AUTOCPOSSTART &rarr; position</a><br />
     <a class="message" href="#SCI_AUTOCCOMPLETE">SCI_AUTOCCOMPLETE</a><br />
     <a class="message" href="#SCI_AUTOCSTOPS">SCI_AUTOCSTOPS(&lt;unused&gt;, const char
    *characterSet)</a><br />
     <a class="message" href="#SCI_AUTOCSETSEPARATOR">SCI_AUTOCSETSEPARATOR(int
    separatorCharacter)</a><br />
     <a class="message" href="#SCI_AUTOCGETSEPARATOR">SCI_AUTOCGETSEPARATOR &rarr; int</a><br />
     <a class="message" href="#SCI_AUTOCSELECT">SCI_AUTOCSELECT(&lt;unused&gt;, const char
    *select)</a><br />
     <a class="message" href="#SCI_AUTOCGETCURRENT">SCI_AUTOCGETCURRENT &rarr; int</a><br />
     <a class="message" href="#SCI_AUTOCGETCURRENTTEXT">SCI_AUTOCGETCURRENTTEXT(&lt;unused&gt;, char *text) &rarr; int</a><br />
     <a class="message" href="#SCI_AUTOCSETCANCELATSTART">SCI_AUTOCSETCANCELATSTART(bool
    cancel)</a><br />
     <a class="message" href="#SCI_AUTOCGETCANCELATSTART">SCI_AUTOCGETCANCELATSTART &rarr; bool</a><br />
     <a class="message" href="#SCI_AUTOCSETFILLUPS">SCI_AUTOCSETFILLUPS(&lt;unused&gt;, const char
    *characterSet)</a><br />
     <a class="message" href="#SCI_AUTOCSETCHOOSESINGLE">SCI_AUTOCSETCHOOSESINGLE(bool
    chooseSingle)</a><br />
     <a class="message" href="#SCI_AUTOCGETCHOOSESINGLE">SCI_AUTOCGETCHOOSESINGLE &rarr; bool</a><br />
     <a class="message" href="#SCI_AUTOCSETIGNORECASE">SCI_AUTOCSETIGNORECASE(bool
    ignoreCase)</a><br />
     <a class="message" href="#SCI_AUTOCGETIGNORECASE">SCI_AUTOCGETIGNORECASE &rarr; bool</a><br />
     <a class="message" href="#SCI_AUTOCSETCASEINSENSITIVEBEHAVIOUR">SCI_AUTOCSETCASEINSENSITIVEBEHAVIOUR(int behaviour)</a><br />
     <a class="message" href="#SCI_AUTOCGETCASEINSENSITIVEBEHAVIOUR">SCI_AUTOCGETCASEINSENSITIVEBEHAVIOUR &rarr; int</a><br />
     <a class="message" href="#SCI_AUTOCSETMULTI">SCI_AUTOCSETMULTI(int multi)</a><br />
     <a class="message" href="#SCI_AUTOCGETMULTI">SCI_AUTOCGETMULTI &rarr; int</a><br />
     <a class="message" href="#SCI_AUTOCSETORDER">SCI_AUTOCSETORDER(int order)</a><br />
     <a class="message" href="#SCI_AUTOCGETORDER">SCI_AUTOCGETORDER &rarr; int</a><br />
     <a class="message" href="#SCI_AUTOCSETAUTOHIDE">SCI_AUTOCSETAUTOHIDE(bool autoHide)</a><br />
     <a class="message" href="#SCI_AUTOCGETAUTOHIDE">SCI_AUTOCGETAUTOHIDE &rarr; bool</a><br />
     <a class="message" href="#SCI_AUTOCSETDROPRESTOFWORD">SCI_AUTOCSETDROPRESTOFWORD(bool
    dropRestOfWord)</a><br />
     <a class="message" href="#SCI_AUTOCGETDROPRESTOFWORD">SCI_AUTOCGETDROPRESTOFWORD &rarr; bool</a><br />
     <a class="message" href="#SCI_REGISTERIMAGE">SCI_REGISTERIMAGE(int type, const char *xpmData)</a><br />
     <a class="message" href="#SCI_REGISTERRGBAIMAGE">SCI_REGISTERRGBAIMAGE(int type, const char *pixels)</a><br />
     <a class="message" href="#SCI_CLEARREGISTEREDIMAGES">SCI_CLEARREGISTEREDIMAGES</a><br />
     <a class="message" href="#SCI_AUTOCSETTYPESEPARATOR">SCI_AUTOCSETTYPESEPARATOR(int separatorCharacter)</a><br />
     <a class="message" href="#SCI_AUTOCGETTYPESEPARATOR">SCI_AUTOCGETTYPESEPARATOR &rarr; int</a><br />
     <a class="message" href="#SCI_AUTOCSETMAXHEIGHT">SCI_AUTOCSETMAXHEIGHT(int rowCount)</a><br />
     <a class="message" href="#SCI_AUTOCGETMAXHEIGHT">SCI_AUTOCGETMAXHEIGHT &rarr; int</a><br />
     <a class="message" href="#SCI_AUTOCSETMAXWIDTH">SCI_AUTOCSETMAXWIDTH(int characterCount)</a><br />
     <a class="message" href="#SCI_AUTOCGETMAXWIDTH">SCI_AUTOCGETMAXWIDTH &rarr; int</a><br />
    </code>

    <p><b id="SCI_AUTOCSHOW">SCI_AUTOCSHOW(int lengthEntered, const char *itemList)</b><br />
     This message causes a list to be displayed. <code class="parameter">lengthEntered</code> is the number of
    characters of the word already entered and <code class="parameter">itemList</code> is the list of words separated by
    separator characters. The initial separator character is a space but this can be set or got
    with <a class="message" href="#SCI_AUTOCSETSEPARATOR"><code>SCI_AUTOCSETSEPARATOR</code></a>
    and <a class="message"
    href="#SCI_AUTOCGETSEPARATOR"><code>SCI_AUTOCGETSEPARATOR</code></a>.</p>

    <p>With default settings, the list of words should be in sorted order.
    If set to ignore case mode with <a class="message" href="#SCI_AUTOCSETIGNORECASE"><code>SCI_AUTOCSETIGNORECASE</code></a>, then
    strings are matched after being converted to upper case. One result of this is that the list
    should be sorted with the punctuation characters '[', '\', ']', '^', '_', and '`' sorted after
    letters.
    Alternative handling of list order may be specified with  <a class="seealso" href="#SCI_AUTOCSETORDER">SCI_AUTOCSETORDER</a>
    </p>

    <p><b id="SCI_AUTOCCANCEL">SCI_AUTOCCANCEL</b><br />
     This message cancels any displayed autocompletion list. When in autocompletion mode, the list
    should disappear when the user types a character that can not be part of the autocompletion,
    such as '.', '(' or '[' when typing an identifier. A set of characters that will cancel
    autocompletion can be specified with <a class="message"
    href="#SCI_AUTOCSTOPS"><code>SCI_AUTOCSTOPS</code></a>.</p>

    <p><b id="SCI_AUTOCACTIVE">SCI_AUTOCACTIVE &rarr; bool</b><br />
     This message returns non-zero if there is an active autocompletion list and zero if there is
    not.</p>

    <p><b id="SCI_AUTOCPOSSTART">SCI_AUTOCPOSSTART &rarr; position</b><br />
     This returns the value of the current position when <code>SCI_AUTOCSHOW</code> started display
    of the list.</p>

    <p><b id="SCI_AUTOCCOMPLETE">SCI_AUTOCCOMPLETE</b><br />
     This message triggers autocompletion. This has the same effect as the tab key.</p>

    <p><b id="SCI_AUTOCSTOPS">SCI_AUTOCSTOPS(&lt;unused&gt;, const char *characterSet)</b><br />
     The <code class="parameter">characterSet</code> argument is a string containing a list of characters that will
    automatically cancel the autocompletion list. When you start the editor, this list is
    empty.</p>

    <p><b id="SCI_AUTOCSETSEPARATOR">SCI_AUTOCSETSEPARATOR(int separatorCharacter)</b><br />
     <b id="SCI_AUTOCGETSEPARATOR">SCI_AUTOCGETSEPARATOR &rarr; int</b><br />
     These two messages set and get the separator character used to separate words in the
    <code>SCI_AUTOCSHOW</code> list. The default is the space character.</p>

    <p><b id="SCI_AUTOCSELECT">SCI_AUTOCSELECT(&lt;unused&gt;, const char *select)</b><br />
     <b id="SCI_AUTOCGETCURRENT">SCI_AUTOCGETCURRENT &rarr; int</b><br />
     This message selects an item in the autocompletion list. It searches the list of words for the
    first that matches <code class="parameter">select</code>. By default, comparisons are case sensitive, but you can
    change this with <a class="message"
    href="#SCI_AUTOCSETIGNORECASE"><code>SCI_AUTOCSETIGNORECASE</code></a>. The match is character
    by character for the length of the <code class="parameter">select</code> string. That is, if select is "Fred" it
    will match "Frederick" if this is the first item in the list that begins with "Fred". If an
    item is found, it is selected. If the item is not found, the autocompletion list closes if
    auto-hide is true (see <a class="message"
    href="#SCI_AUTOCSETAUTOHIDE"><code>SCI_AUTOCSETAUTOHIDE</code></a>).<br />
    The current selection index can be retrieved with <code>SCI_AUTOCGETCURRENT</code>.</p>

    <p><b id="SCI_AUTOCGETCURRENTTEXT">SCI_AUTOCGETCURRENTTEXT(&lt;unused&gt;, char *text NUL-terminated) &rarr; int</b><br />
     This message retrieves the current selected text in the autocompletion list. Normally the
    <a class="message" href="#SCN_AUTOCSELECTION"><code>SCN_AUTOCSELECTION</code></a> notification
    is used instead.</p>

    <p>The value is copied to the <code class="parameter">text</code> buffer, returning the length (not including the
    terminating 0). If not found, an empty string is copied to the buffer and 0 is returned.</p>

    <p>If the value argument is 0 then the length that should be allocated to store the value is
    returned; again, the terminating 0 is not included.</p>

    <p><b id="SCI_AUTOCSETCANCELATSTART">SCI_AUTOCSETCANCELATSTART(bool cancel)</b><br />
     <b id="SCI_AUTOCGETCANCELATSTART">SCI_AUTOCGETCANCELATSTART &rarr; bool</b><br />
     The default behaviour is for the list to be cancelled if the caret moves to the location it
    was at when the list was displayed. By calling this message with a <code>false</code> argument,
    the list is not cancelled until the caret moves at least one character before the word being
    completed.</p>

    <p><b id="SCI_AUTOCSETFILLUPS">SCI_AUTOCSETFILLUPS(&lt;unused&gt;, const char *characterSet)</b><br />
     If a fillup character is typed with an autocompletion list active, the currently selected item
    in the list is added into the document, then the fillup character is added. Common fillup
    characters are '(', '[' and '.' but others are possible depending on the language. By default,
    no fillup characters are set.</p>

    <p><b id="SCI_AUTOCSETCHOOSESINGLE">SCI_AUTOCSETCHOOSESINGLE(bool chooseSingle)</b><br />
     <b id="SCI_AUTOCGETCHOOSESINGLE">SCI_AUTOCGETCHOOSESINGLE &rarr; bool</b><br />
     If you use <code>SCI_AUTOCSETCHOOSESINGLE(1)</code> and a list has only one item, it is
    automatically added and no list is displayed. The default is to display the list even if there
    is only a single item.</p>

    <p><b id="SCI_AUTOCSETIGNORECASE">SCI_AUTOCSETIGNORECASE(bool ignoreCase)</b><br />
     <b id="SCI_AUTOCGETIGNORECASE">SCI_AUTOCGETIGNORECASE &rarr; bool</b><br />
     By default, matching of characters to list members is case sensitive. These messages let you
    set and get case sensitivity.</p>

    <p><b id="SCI_AUTOCSETCASEINSENSITIVEBEHAVIOUR">SCI_AUTOCSETCASEINSENSITIVEBEHAVIOUR(int behaviour)</b><br />
    <b id="SCI_AUTOCGETCASEINSENSITIVEBEHAVIOUR">SCI_AUTOCGETCASEINSENSITIVEBEHAVIOUR &rarr; int</b><br />
    When autocompletion is set to ignore case (<code>SCI_AUTOCSETIGNORECASE</code>), by default it will
    nonetheless select the first list member that matches in a case sensitive way to entered characters.
    This corresponds to a behaviour property of <code>SC_CASEINSENSITIVEBEHAVIOUR_RESPECTCASE</code> (0).
    If you want autocompletion to ignore case at all, choose <code>SC_CASEINSENSITIVEBEHAVIOUR_IGNORECASE</code> (1).</p>

    <p><b id="SCI_AUTOCSETMULTI">SCI_AUTOCSETMULTI(int multi)</b><br />
    <b id="SCI_AUTOCGETMULTI">SCI_AUTOCGETMULTI &rarr; int</b><br />
    When autocompleting with multiple selections present, the autocompleted text can go into just the main selection with
    <code>SC_MULTIAUTOC_ONCE</code> (0) or into each selection with <code>SC_MULTIAUTOC_EACH</code> (1).
    The default is <code>SC_MULTIAUTOC_ONCE</code>.</p>

    <p><b id="SCI_AUTOCSETORDER">SCI_AUTOCSETORDER(int order)</b><br />
    <b id="SCI_AUTOCGETORDER">SCI_AUTOCGETORDER &rarr; int</b><br />
    The default setting <code>SC_ORDER_PRESORTED</code> (0) requires that the list be provided in alphabetical sorted order.
   </p>
    <p>Sorting the list can be done by Scintilla instead of the application with <code>SC_ORDER_PERFORMSORT</code> (1).
    This will take additional time.
   </p>
    <p>Applications that wish to prioritize some values and show the list in order of priority instead
    of alphabetical order can use <code>SC_ORDER_CUSTOM</code> (2).
    This requires extra processing in <a class="seealso" href="#SCI_AUTOCSHOW">SCI_AUTOCSHOW</a> to create a sorted index.
   </p>
    <p>Setting the order should be done before calling <a class="seealso" href="#SCI_AUTOCSHOW">SCI_AUTOCSHOW</a>.
   </p>

    <p><b id="SCI_AUTOCSETAUTOHIDE">SCI_AUTOCSETAUTOHIDE(bool autoHide)</b><br />
     <b id="SCI_AUTOCGETAUTOHIDE">SCI_AUTOCGETAUTOHIDE &rarr; bool</b><br />
     By default, the list is cancelled if there are no viable matches (the user has typed
    characters that no longer match a list entry). If you want to keep displaying the original
    list, set <code class="parameter">autoHide</code> to <code>false</code>. This also effects <a class="seealso"
    href="#SCI_AUTOCSELECT"><code>SCI_AUTOCSELECT</code></a>.</p>

    <p><b id="SCI_AUTOCSETDROPRESTOFWORD">SCI_AUTOCSETDROPRESTOFWORD(bool dropRestOfWord)</b><br />
     <b id="SCI_AUTOCGETDROPRESTOFWORD">SCI_AUTOCGETDROPRESTOFWORD &rarr; bool</b><br />
     When an item is selected, any word characters following the caret are first erased if
    <code class="parameter">dropRestOfWord</code> is set <code>true</code>. The default is <code>false</code>.</p>

    <p>
      <b id="SCI_REGISTERIMAGE">SCI_REGISTERIMAGE(int type, const char *xpmData)</b><br />
      <b id="SCI_REGISTERRGBAIMAGE">SCI_REGISTERRGBAIMAGE(int type, const char *pixels)</b><br />
      <b id="SCI_CLEARREGISTEREDIMAGES">SCI_CLEARREGISTEREDIMAGES</b><br />
      <b id="SCI_AUTOCSETTYPESEPARATOR">SCI_AUTOCSETTYPESEPARATOR(int separatorCharacter)</b><br />
      <b id="SCI_AUTOCGETTYPESEPARATOR">SCI_AUTOCGETTYPESEPARATOR &rarr; int</b><br />

      Autocompletion list items may display an image as well as text. Each image is first registered with an integer
      type. Then this integer is included in the text of the list separated by a '?' from the text. For example,
      "fclose?2 fopen" displays image 2 before the string "fclose" and no image before "fopen".
      The images are in either the <a class="jump" href="#XPM">XPM format</a> (<code>SCI_REGISTERIMAGE</code>) or
      <a class="jump" href="#RGBA">RGBA format</a> (<code>SCI_REGISTERRGBAIMAGE</code>).
      For <code>SCI_REGISTERRGBAIMAGE</code> the width and height must previously been set with
      the <a class="message" href="#SCI_RGBAIMAGESETWIDTH"><code>SCI_RGBAIMAGESETWIDTH</code></a> and
      <a class="message" href="#SCI_RGBAIMAGESETHEIGHT"><code>SCI_RGBAIMAGESETHEIGHT</code></a> messages.
      The set of registered images can be cleared with <code>SCI_CLEARREGISTEREDIMAGES</code> and the '?' separator changed
      with <code>SCI_AUTOCSETTYPESEPARATOR</code>.
    </p>

    <p>
      <b id="SCI_AUTOCSETMAXHEIGHT">SCI_AUTOCSETMAXHEIGHT(int rowCount)</b><br />
      <b id="SCI_AUTOCGETMAXHEIGHT">SCI_AUTOCGETMAXHEIGHT &rarr; int</b><br />

      Get or set the maximum number of rows that will be visible in an autocompletion list. If there are more rows in the list, then a vertical
      scrollbar is shown. The default is 5.
     </p>

    <p>
      <b id="SCI_AUTOCSETMAXWIDTH">SCI_AUTOCSETMAXWIDTH(int characterCount)</b><br />
      <b id="SCI_AUTOCGETMAXWIDTH">SCI_AUTOCGETMAXWIDTH &rarr; int</b><br />

      Get or set the maximum width of an autocompletion list expressed as the number of characters in the longest item that will be totally visible.
      If zero (the default) then the list's width is calculated to fit the item with the most characters. Any items that cannot be fully displayed within
      the available width are indicated by the presence of ellipsis.
     </p>

    <h2 id="UserLists">User lists</h2>

    <p>User lists use the same internal mechanisms as autocompletion lists, and all the calls
    listed for autocompletion work on them; you cannot display a user list at the same time as an
    autocompletion list is active. They differ in the following respects:</p>

    <code><a class="message" href="#SCI_USERLISTSHOW">SCI_USERLISTSHOW(int listType, const char *itemList)</a><br /></code>

    <p>o The <code><a class="message"
    href="#SCI_AUTOCSETCHOOSESINGLE">SCI_AUTOCSETCHOOSESINGLE</a></code> message has no
    effect.<br />
     o When the user makes a selection you are sent a <code><a class="jump"
    href="#SCN_USERLISTSELECTION">SCN_USERLISTSELECTION</a></code> <a class="jump"
    href="#Notifications">notification message</a> rather than <code><a class="jump"
    href="#SCN_AUTOCSELECTION">SCN_AUTOCSELECTION</a></code>.</p>

    <p>BEWARE: if you have set fillup characters or stop characters, these will still be active
    with the user list, and may result in items being selected or the user list cancelled due to
    the user typing into the editor.</p>

    <p><b id="SCI_USERLISTSHOW">SCI_USERLISTSHOW(int listType, const char *itemList)</b><br />
     The <code class="parameter">listType</code> parameter is returned to the container as the <code>wParam</code>
    field of the <a class="message" href="#SCNotification"><code>SCNotification</code></a>
    structure. It must be greater than 0 as this is how Scintilla tells the difference between an
    autocompletion list and a user list. If you have different types of list, for example a list of
    buffers and a list of macros, you can use <code class="parameter">listType</code> to tell which one has returned
    a selection. </p>

    <h2 id="CallTips">Call tips</h2>

    <p>Call tips are small windows displaying the arguments to a function and are displayed after
    the user has typed the name of the function. They normally display characters using the font
    facename, size and character set defined by
    <code><a class="message" href="#StyleDefinition">STYLE_DEFAULT</a></code>. You can choose to
    use <code><a class="message" href="#StyleDefinition">STYLE_CALLTIP</a></code> to define the
    facename, size, foreground and background colours and character set with
    <code><a class="seealso" href="#SCI_CALLTIPUSESTYLE">SCI_CALLTIPUSESTYLE</a></code>.
    This also enables support for Tab characters.

    There is some interaction between call tips and autocompletion lists in that showing a
    call tip cancels any active autocompletion list, and vice versa.</p>

    <p>Call tips are not implemented on Qt.</p>

    <p>Call tips can highlight part of the text within them. You could use this to highlight the
    current argument to a function by counting the number of commas (or whatever separator your
    language uses). See <code>SciTEBase::CharAdded()</code> in <code>SciTEBase.cxx</code> for an
    example of call tip use.</p>

    <p>The mouse may be clicked on call tips and this causes a
    <code><a class="message" href="#SCN_CALLTIPCLICK">SCN_CALLTIPCLICK</a></code>
    notification to be sent to the container. Small up and down arrows may be displayed within
    a call tip by, respectively, including the characters '\001', or '\002'. This is useful
    for showing that there are overloaded variants of one function name and that the user can
    click on the arrows to cycle through the overloads.</p>

    <p>Alternatively, call tips can be displayed when you leave the mouse pointer for a while over
    a word in response to the <code><a class="message"
    href="#SCN_DWELLSTART">SCN_DWELLSTART</a></code> <a class="jump"
    href="#Notifications">notification</a> and cancelled in response to <code><a class="message"
    href="#SCN_DWELLEND">SCN_DWELLEND</a></code>. This method could be used in a debugger to give
    the value of a variable, or during editing to give information about the word under the
    pointer.</p>
    <code><a class="message" href="#SCI_CALLTIPSHOW">SCI_CALLTIPSHOW(int pos, const char
    *definition)</a><br />
     <a class="message" href="#SCI_CALLTIPCANCEL">SCI_CALLTIPCANCEL</a><br />
     <a class="message" href="#SCI_CALLTIPACTIVE">SCI_CALLTIPACTIVE &rarr; bool</a><br />
     <a class="message" href="#SCI_CALLTIPPOSSTART">SCI_CALLTIPPOSSTART &rarr; position</a><br />
     <a class="message" href="#SCI_CALLTIPSETPOSSTART">SCI_CALLTIPSETPOSSTART(int posStart)</a><br />
     <a class="message" href="#SCI_CALLTIPSETHLT">SCI_CALLTIPSETHLT(int highlightStart, int
    highlightEnd)</a><br />
     <a class="message" href="#SCI_CALLTIPSETBACK">SCI_CALLTIPSETBACK(colour back)</a><br />
     <a class="message" href="#SCI_CALLTIPSETFORE">SCI_CALLTIPSETFORE(colour fore)</a><br />
     <a class="message" href="#SCI_CALLTIPSETFOREHLT">SCI_CALLTIPSETFOREHLT(colour fore)</a><br />
     <a class="message" href="#SCI_CALLTIPUSESTYLE">SCI_CALLTIPUSESTYLE(int tabSize)</a><br />
     <a class="message" href="#SCI_CALLTIPSETPOSITION">SCI_CALLTIPSETPOSITION(bool above)</a><br />
    </code>

    <p><b id="SCI_CALLTIPSHOW">SCI_CALLTIPSHOW(int pos, const char *definition)</b><br />
     This message starts the process by displaying the call tip window. If a call tip is already
    active, this has no effect.<br />
     <code class="parameter">pos</code> is the position in the document at which to align the call tip. The call
    tip text is aligned to start 1 line below this character unless you have included up and/or
    down arrows in the call tip text in which case the tip is aligned to the right-hand edge of
    the rightmost arrow. The assumption is that you will start the text with something like
    "\001 1 of 3 \002".<br />
     <code class="parameter">definition</code> is the call tip text. This can contain multiple lines separated by
    '\n' (Line Feed, ASCII code 10) characters. Do not include '\r' (Carriage Return, ASCII
     code 13), as this will most likely print as an empty box. '\t' (Tab, ASCII code 9) is
     supported if you set a tabsize with
    <code><a class="seealso" href="#SCI_CALLTIPUSESTYLE">SCI_CALLTIPUSESTYLE</a></code>.<br />
    The position of the caret is remembered here so that the call tip can be cancelled automatically if subsequent deletion
    moves the caret before this position.</p>

    <p><b id="SCI_CALLTIPCANCEL">SCI_CALLTIPCANCEL</b><br />
     This message cancels any displayed call tip. Scintilla will also cancel call tips for you if
    you use any keyboard commands that are not compatible with editing the argument list of a
    function.
    Call tips are cancelled if you delete back past the position where the caret was when the tip was triggered.</p>

    <p><b id="SCI_CALLTIPACTIVE">SCI_CALLTIPACTIVE &rarr; bool</b><br />
     This returns 1 if a call tip is active and 0 if it is not active.</p>

    <p><b id="SCI_CALLTIPPOSSTART">SCI_CALLTIPPOSSTART &rarr; position</b><br />
    <b id="SCI_CALLTIPSETPOSSTART">SCI_CALLTIPSETPOSSTART(int posStart)</b><br />
     This message returns or sets the value of the current position when <code>SCI_CALLTIPSHOW</code>
    started to display the tip.</p>

    <p><b id="SCI_CALLTIPSETHLT">SCI_CALLTIPSETHLT(int highlightStart, int highlightEnd)</b><br />
     This sets the region of the call tips text to display in a highlighted style.
    <code class="parameter">highlightStart</code> is the zero-based index into the string of the first character to
    highlight and <code class="parameter">highlightEnd</code> is the index of the first character after the highlight.
    <code class="parameter">highlightEnd</code> must be greater than <code class="parameter">highlightStart</code>;
    <code>highlightEnd-highlightStart</code> is the
    number of characters to highlight. Highlights can extend over line ends if this is
    required.</p>

    <p>Unhighlighted text is drawn in a mid grey. Selected text is drawn in a dark blue. The
    background is white. These can be changed with
    <code>SCI_CALLTIPSETBACK</code>,
    <code>SCI_CALLTIPSETFORE</code>, and
    <code>SCI_CALLTIPSETFOREHLT</code>.
    </p>

    <p><b id="SCI_CALLTIPSETBACK">SCI_CALLTIPSETBACK(<a class="jump" href="#colour">colour</a> back)</b><br />
     The background colour of call tips can be set with this message; the default colour is white.
    It is not a good idea to set a dark colour as the background as the default colour for normal
    calltip text is mid grey and the default colour for highlighted text is dark blue. This also
    sets the background colour of <code>STYLE_CALLTIP</code>.</p>

    <p><b id="SCI_CALLTIPSETFORE">SCI_CALLTIPSETFORE(<a class="jump" href="#colour">colour</a> fore)</b><br />
     The colour of call tip text can be set with this message; the default colour is mid grey.
    This also sets the foreground colour of <code>STYLE_CALLTIP</code>.</p>

    <p><b id="SCI_CALLTIPSETFOREHLT">SCI_CALLTIPSETFOREHLT(<a class="jump" href="#colour">colour</a> fore)</b><br />
     The colour of highlighted call tip text can be set with this message; the default colour
    is dark blue.</p>

    <p><b id="SCI_CALLTIPUSESTYLE">SCI_CALLTIPUSESTYLE(int tabSize)</b><br />
     This message changes the style used for call tips from <code>STYLE_DEFAULT</code> to
    <code>STYLE_CALLTIP</code> and sets a tab size in screen pixels. If <code class="parameter">tabsize</code> is
    less than 1, Tab characters are not treated specially. Once this call has been used, the
    call tip foreground and background colours are also taken from the style.</p>

    <p><b id="SCI_CALLTIPSETPOSITION">SCI_CALLTIPSETPOSITION(bool above)</b><br />
     By default the calltip is displayed below the text, setting above to <code>true</code>
    (1) will display it above the text.</p>


    <h2 id="KeyboardCommands">Keyboard commands</h2>

    <p>To allow the container application to perform any of the actions available to the user with
    keyboard, all the keyboard actions are messages. They do not take any parameters. These
    commands are also used when redefining the key bindings with the <a class="message"
    href="#SCI_ASSIGNCMDKEY"><code>SCI_ASSIGNCMDKEY</code></a> message.</p>

    <table border="0" summary="Keyboard commands">
      <tbody>
        <tr>
          <td><code>SCI_LINEDOWN</code></td>

          <td><code>SCI_LINEDOWNEXTEND</code></td>

          <td><code>SCI_LINEDOWNRECTEXTEND</code></td>

          <td><code>SCI_LINESCROLLDOWN</code></td>
        </tr>

        <tr>
          <td><code>SCI_LINEUP</code></td>

          <td><code>SCI_LINEUPEXTEND</code></td>

          <td><code>SCI_LINEUPRECTEXTEND</code></td>

          <td><code>SCI_LINESCROLLUP</code></td>
        </tr>

        <tr>
          <td><code>SCI_PARADOWN</code></td>

          <td><code>SCI_PARADOWNEXTEND</code></td>

          <td><code>SCI_PARAUP</code></td>

          <td><code>SCI_PARAUPEXTEND</code></td>
        </tr>

        <tr>
          <td><code>SCI_CHARLEFT</code></td>

          <td><code>SCI_CHARLEFTEXTEND</code></td>

          <td><code>SCI_CHARLEFTRECTEXTEND</code></td>
        </tr>

        <tr>
          <td><code>SCI_CHARRIGHT</code></td>

          <td><code>SCI_CHARRIGHTEXTEND</code></td>

          <td><code>SCI_CHARRIGHTRECTEXTEND</code></td>
        </tr>

        <tr>
          <td><code>SCI_WORDLEFT</code></td>

          <td><code>SCI_WORDLEFTEXTEND</code></td>

          <td><code>SCI_WORDRIGHT</code></td>

          <td><code>SCI_WORDRIGHTEXTEND</code></td>
        </tr>

        <tr>
          <td><code>SCI_WORDLEFTEND</code></td>

          <td><code>SCI_WORDLEFTENDEXTEND</code></td>

          <td><code>SCI_WORDRIGHTEND</code></td>

          <td><code>SCI_WORDRIGHTENDEXTEND</code></td>
        </tr>

        <tr>
          <td><code>SCI_WORDPARTLEFT</code></td>

          <td><code>SCI_WORDPARTLEFTEXTEND</code></td>

          <td><code>SCI_WORDPARTRIGHT</code></td>

          <td><code>SCI_WORDPARTRIGHTEXTEND</code></td>
        </tr>

        <tr>
          <td><code>SCI_HOME</code></td>

          <td><code>SCI_HOMEEXTEND</code></td>

          <td><code>SCI_HOMERECTEXTEND</code></td>
        </tr>

        <tr>
          <td><code>SCI_HOMEDISPLAY</code></td>

          <td><code>SCI_HOMEDISPLAYEXTEND</code></td>

          <td><code>SCI_HOMEWRAP</code></td>

          <td><code>SCI_HOMEWRAPEXTEND</code></td>
        </tr>

        <tr>
          <td><code>SCI_VCHOME</code></td>

          <td><code>SCI_VCHOMEEXTEND</code></td>

          <td><code>SCI_VCHOMERECTEXTEND</code></td>
        </tr>

        <tr>
          <td><code>SCI_VCHOMEWRAP</code></td>

          <td><code>SCI_VCHOMEWRAPEXTEND</code></td>

          <td><code>SCI_VCHOMEDISPLAY</code></td>

          <td><code>SCI_VCHOMEDISPLAYEXTEND</code></td>
        </tr>

        <tr>
          <td><code>SCI_LINEEND</code></td>

          <td><code>SCI_LINEENDEXTEND</code></td>

          <td><code>SCI_LINEENDRECTEXTEND</code></td>
        </tr>

        <tr>
          <td><code>SCI_LINEENDDISPLAY</code></td>

          <td><code>SCI_LINEENDDISPLAYEXTEND</code></td>

          <td><code>SCI_LINEENDWRAP</code></td>

          <td><code>SCI_LINEENDWRAPEXTEND</code></td>
        </tr>

        <tr>
          <td><code>SCI_DOCUMENTSTART</code></td>

          <td><code>SCI_DOCUMENTSTARTEXTEND</code></td>

          <td><code>SCI_DOCUMENTEND</code></td>

          <td><code>SCI_DOCUMENTENDEXTEND</code></td>
        </tr>

        <tr>
          <td><code>SCI_PAGEUP</code></td>

          <td><code>SCI_PAGEUPEXTEND</code></td>

          <td><code>SCI_PAGEUPRECTEXTEND</code></td>
        </tr>

        <tr>
          <td><code>SCI_PAGEDOWN</code></td>

          <td><code>SCI_PAGEDOWNEXTEND</code></td>

          <td><code>SCI_PAGEDOWNRECTEXTEND</code></td>
        </tr>


        <tr>
          <td><code>SCI_STUTTEREDPAGEUP</code></td>

          <td><code>SCI_STUTTEREDPAGEUPEXTEND</code></td>
        </tr>

        <tr>
          <td><code>SCI_STUTTEREDPAGEDOWN</code></td>

          <td><code>SCI_STUTTEREDPAGEDOWNEXTEND</code></td>
        </tr>

        <tr>
          <td><code>SCI_DELETEBACK</code></td>

          <td><code>SCI_DELETEBACKNOTLINE</code></td>
        </tr>

        <tr>
          <td><code>SCI_DELWORDLEFT</code></td>

          <td><code>SCI_DELWORDRIGHT</code></td>

          <td><code>SCI_DELWORDRIGHTEND</code></td>
        </tr>

        <tr>
          <td><code>SCI_DELLINELEFT</code></td>

          <td><code>SCI_DELLINERIGHT</code></td>

          <td><code>SCI_LINEDELETE</code></td>

          <td><code>SCI_LINECUT</code></td>
        </tr>

        <tr>

          <td><code>SCI_LINECOPY</code></td>

          <td><code>SCI_LINETRANSPOSE</code></td>

          <td><code>SCI_LINEREVERSE</code></td>

          <td><code>SCI_LINEDUPLICATE</code></td>
        </tr>

        <tr>
          <td><code>SCI_LOWERCASE</code></td>

          <td><code>SCI_UPPERCASE</code></td>

          <td><code>SCI_CANCEL</code></td>

          <td><code>SCI_EDITTOGGLEOVERTYPE</code></td>
        </tr>

        <tr>
          <td><code>SCI_NEWLINE</code></td>

          <td><code>SCI_FORMFEED</code></td>

          <td><code>SCI_TAB</code></td>

          <td><code>SCI_BACKTAB</code></td>
        </tr>

        <tr>
          <td><code>SCI_SELECTIONDUPLICATE</code></td>

          <td><code>SCI_VERTICALCENTRECARET</code></td>

        </tr>

        <tr>
          <td><code>SCI_MOVESELECTEDLINESUP</code></td>

          <td><code>SCI_MOVESELECTEDLINESDOWN</code></td>
        </tr>

        <tr>
          <td><code>SCI_SCROLLTOSTART</code></td>

          <td><code>SCI_SCROLLTOEND</code></td>
        </tr>
     </tbody>
    </table>

    <p>The <code>SCI_*EXTEND</code> messages extend the selection.</p>

    <p>The <code>SCI_*RECTEXTEND</code> messages extend the rectangular selection
    (and convert regular selection to rectangular one, if any).</p>

    <p>The <code>SCI_WORDPART*</code> commands are used to move between word segments marked by
    capitalisation (aCamelCaseIdentifier) or underscores (an_under_bar_ident).</p>

    <p>The <code>SCI_WORD[LEFT|RIGHT]END*</code> commands are
    similar to <code>SCI_WORD[LEFT|RIGHT]*</code> but move between word ends instead of word starts.</p>

    <p>The <code>SCI_HOME*</code> commands move the caret to the start of the line, while the
    <code>SCI_VCHOME*</code> commands move the caret to the first non-blank character of the line
    (ie. just after the indentation) unless it is already there; in this case, it acts as SCI_HOME*.</p>

    <p>The <code>SCI_[HOME|LINEEND]DISPLAY*</code> commands are used when in line wrap mode to
    allow movement to the start or end of display lines as opposed to the normal
    <code>SCI_[HOME|LINEEND]</code> commands which move to the start or end of document lines.</p>

    <p>The <code>SCI_[[VC]HOME|LINEEND]WRAP*</code> commands are like their namesakes
    <code>SCI_[[VC]HOME|LINEEND]*</code> except they behave differently when word-wrap is enabled:
     They go first to the start / end of the display line, like <code>SCI_[HOME|LINEEND]DISPLAY*</code>,
     but if the cursor is already at the point, it goes on to the start or end of the document line,
     as appropriate for <code>SCI_[[VC]HOME|LINEEND]*</code>.
     </p>

    <p>The <code>SCI_SCROLLTO[START|END]</code> commands scroll the document to the start
    or end without changing the selection. These commands match OS X platform conventions for the behaviour of the
    <code>home</code> and <code>end</code> keys. Scintilla can be made to match OS X applications
    by binding the <code>home</code> and <code>end</code> keys to these commands.
     </p>

    <p class="message" id="SCI_CANCEL">The <code>SCI_CANCEL</code> command cancels autocompletion and
    calltip display and drops any additional selections.
     </p>

    <h2 id="KeyBindings">Key bindings</h2>

    <p>There is a default binding of keys to commands that is defined in the Scintilla source in
    the file <code>KeyMap.cxx</code> by the constant <code>KeyMap::MapDefault[]</code>. This table
    maps key definitions to <code>SCI_*</code> messages with no parameters (mostly the <a
    class="jump" href="#KeyboardCommands">keyboard commands</a> discussed above, but any Scintilla
    command that has no arguments can be mapped). You can change the mapping to suit your own
    requirements.</p>
    <code><a class="message" href="#SCI_ASSIGNCMDKEY">SCI_ASSIGNCMDKEY(int keyDefinition, int
    sciCommand)</a><br />
     <a class="message" href="#SCI_CLEARCMDKEY">SCI_CLEARCMDKEY(int keyDefinition)</a><br />
     <a class="message" href="#SCI_CLEARALLCMDKEYS">SCI_CLEARALLCMDKEYS</a><br />
     <a class="message" href="#SCI_NULL">SCI_NULL</a><br />
    </code>

    <p><b id="keyDefinition">keyDefinition</b><br />
     A key definition contains the key code in the low 16-bits and the key modifiers in the high
    16-bits. To combine <code>keyCode</code> and <code>keyMod</code> set:<br />
    <br />
     <code>keyDefinition = keyCode + (keyMod &lt;&lt; 16)</code></p>

    <p>The key code is a visible or control character or a key from the <code>SCK_*</code>
    enumeration, which contains:<br />
     <code>SCK_ADD</code>, <code>SCK_BACK</code>, <code>SCK_DELETE</code>, <code>SCK_DIVIDE</code>,
    <code>SCK_DOWN</code>, <code>SCK_END</code>, <code>SCK_ESCAPE</code>, <code>SCK_HOME</code>,
    <code>SCK_INSERT</code>, <code>SCK_LEFT</code>, <code>SCK_MENU</code>, <code>SCK_NEXT</code> (Page Down),
    <code>SCK_PRIOR</code> (Page Up), <code>SCK_RETURN</code>, <code>SCK_RIGHT</code>,
    <code>SCK_RWIN</code>,
    <code>SCK_SUBTRACT</code>, <code>SCK_TAB</code>, <code>SCK_UP</code>, and
    <code>SCK_WIN</code>.</p>

    <p>The modifiers are a combination of zero or more of <code>SCMOD_ALT</code>,
    <code>SCMOD_CTRL</code>, <code>SCMOD_SHIFT</code>,
    <code>SCMOD_META</code>, and <code>SCMOD_SUPER</code>.
    On OS X, the Command key is mapped to <code>SCMOD_CTRL</code> and the Control key to
    <code>SCMOD_META</code>.
    <code>SCMOD_SUPER</code> is only available on GTK+ which is commonly the Windows key.
    If you are building a table, you might
    want to use <code>SCMOD_NORM</code>, which has the value 0, to mean no modifiers.</p>

    <p><b id="SCI_ASSIGNCMDKEY">SCI_ASSIGNCMDKEY(int <a class="jump"
    href="#keyDefinition">keyDefinition</a>, int sciCommand)</b><br />
     This assigns the given key definition to a Scintilla command identified by
    <code class="parameter">sciCommand</code>. <code class="parameter">sciCommand</code> can be any <code>SCI_*</code> command that has
    no arguments.</p>

    <p><b id="SCI_CLEARCMDKEY">SCI_CLEARCMDKEY(int <a class="jump"
    href="#keyDefinition">keyDefinition</a>)</b><br />
     This makes the given key definition do nothing by assigning the action <code>SCI_NULL</code>
    to it.</p>

    <p><b id="SCI_CLEARALLCMDKEYS">SCI_CLEARALLCMDKEYS</b><br />
     This command removes all keyboard command mapping by setting an empty mapping table.</p>

    <p><b id="SCI_NULL">SCI_NULL</b><br />
     The <code>SCI_NULL</code> does nothing and is the value assigned to keys that perform no
    action. SCI_NULL ensures that keys do not propagate to the parent window as that may
    cause focus to move. If you want the standard platform behaviour use the constant 0 instead.</p>

    <h2 id="PopupEditMenu">Popup edit menu</h2>

    <code><a class="message" href="#SCI_USEPOPUP">SCI_USEPOPUP(int popUpMode)</a><br />
    </code>

    <p><b id="SCI_USEPOPUP">SCI_USEPOPUP(int popUpMode)</b><br />
     Clicking the wrong button on the mouse pops up a short default editing menu. This may be
    turned off with <code>SCI_USEPOPUP(SC_POPUP_NEVER)</code>. If you turn it off, context menu commands (in
    Windows, <code>WM_CONTEXTMENU</code>) will not be handled by Scintilla, so the parent of the
    Scintilla window will have the opportunity to handle the message.</p>

        <table class="standard" summary="Display context menu mode">
          <tbody>
            <tr>
              <th align="left">Symbol</th>

              <th>Value</th>

              <th align="left">Meaning</th>

            </tr>
          </tbody>

          <tbody valign="top">
            <tr>
              <td align="left"><code>SC_POPUP_NEVER</code></td>

              <td align="center">0</td>

              <td>Never show default editing menu.</td>

            </tr>

            <tr>
              <td align="left"><code>SC_POPUP_ALL</code></td>

              <td align="center">1</td>

              <td>Show default editing menu if clicking on scintilla.</td>

            </tr>

            <tr>
              <td align="left"><code>SC_POPUP_TEXT</code></td>

              <td align="center">2</td>

              <td>Show default editing menu only if clicking on text area.</td>

            </tr>

          </tbody>
        </table>

    <h2 id="MacroRecording">Macro recording</h2>

    <p>Start and stop macro recording mode. In macro recording mode, actions are reported to the
    container through <code><a class="message" href="#SCN_MACRORECORD">SCN_MACRORECORD</a></code>
    <a class="jump" href="#Notifications">notifications</a>. It is then up to the container to
    record these actions for future replay.</p>

    <code><a class="message" href="#SCI_STARTRECORD">SCI_STARTRECORD</a><br />
     <a class="message" href="#SCI_STOPRECORD">SCI_STOPRECORD</a><br />
    </code>

    <p><b id="SCI_STARTRECORD">SCI_STARTRECORD</b><br />
     <b id="SCI_STOPRECORD">SCI_STOPRECORD</b><br />
     These two messages turn macro recording on and off.</p>

    <h2 id="Printing">Printing</h2>

    <p><code>SCI_FORMATRANGE</code> can be used to draw the text onto a display surface
    which can include a printer display surface. Printed output shows text styling as on the
    screen, but it hides all margins except a line number margin. All special marker effects are
    removed and the selection and caret are hidden.</p>

    <p>Different platforms use different display surface ID types to print on. On Windows, these are
    <code>HDC</code>s., on GTK+ 3.x <code>cairo_t *</code>,
    and on Cocoa <code>CGContextRef</code> is used.</p>

    <code><a class="message" href="#SCI_FORMATRANGE">SCI_FORMATRANGE(bool draw, Sci_RangeToFormat *fr) &rarr; position</a><br />
     <a class="message" href="#SCI_SETPRINTMAGNIFICATION">SCI_SETPRINTMAGNIFICATION(int
    magnification)</a><br />
     <a class="message" href="#SCI_GETPRINTMAGNIFICATION">SCI_GETPRINTMAGNIFICATION &rarr; int</a><br />
     <a class="message" href="#SCI_SETPRINTCOLOURMODE">SCI_SETPRINTCOLOURMODE(int mode)</a><br />
     <a class="message" href="#SCI_GETPRINTCOLOURMODE">SCI_GETPRINTCOLOURMODE &rarr; int</a><br />
     <a class="message" href="#SCI_SETPRINTWRAPMODE">SCI_SETPRINTWRAPMODE(int wrapMode)</a><br />
     <a class="message" href="#SCI_GETPRINTWRAPMODE">SCI_GETPRINTWRAPMODE &rarr; int</a><br />
    </code>

    <p><b id="SCI_FORMATRANGE">SCI_FORMATRANGE(bool draw, Sci_RangeToFormat *fr) &rarr; position</b><br />
     This call renders a range of text into a device context. If you use
    this for printing, you will probably want to arrange a page header and footer; Scintilla does
    not do this for you. See <code>SciTEWin::Print()</code> in <code>SciTEWinDlg.cxx</code> for an
    example. Each use of this message renders a range of text into a rectangular area and returns
    the position in the document of the next character to print.</p>

    <p><code class="parameter">draw</code> controls if any output is done. Set this to false if you are paginating
    (for example, if you use this with MFC you will need to paginate in
    <code>OnBeginPrinting()</code> before you output each page.</p>
<pre>
struct Sci_Rectangle { int left; int top; int right; int bottom; };

struct Sci_RangeToFormat {
    Sci_SurfaceID hdc;        // The Surface ID we print to
    Sci_SurfaceID hdcTarget;  // The Surface ID we use for measuring (may be same as hdc)
    Sci_Rectangle rc;         // Rectangle in which to print
    Sci_Rectangle rcPage;     // Physically printable page size
    Sci_CharacterRange chrg;  // Range of characters to print
};
</pre>

    <p>On Windows, <code>hdc</code> and <code>hdcTarget</code> should both be set to the device context handle
    of the output device (usually a printer). If you print to a metafile these will not be the same
    as Windows metafiles (unlike extended metafiles) do not implement the full API for returning
    information. In this case, set <code>hdcTarget</code> to the screen DC.<br />
     <code>rcPage</code> is the rectangle <code>{0, 0, maxX, maxY}</code> where <code>maxX+1</code>
    and <code>maxY+1</code> are the number of physically printable pixels in x and y.<br />
     <code>rc</code> is the rectangle to render the text in (which will, of course, fit within the
    rectangle defined by rcPage).<br />
     <code>chrg.cpMin</code> and <code>chrg.cpMax</code> define the start position and maximum
    position of characters to output. All of each line within this character range is drawn.</p>

    <p>On Cocoa, the surface IDs for printing (<code>draw=1</code>) should be the graphics port of the current context
    (<code>(CGContextRef) [[NSGraphicsContext currentContext] graphicsPort]</code>) when the view's drawRect method is called.
    The Surface IDs are not really used for measurement (<code>draw=0</code>) but can be set
    to a bitmap context (created with <code>CGBitmapContextCreate</code>) to avoid runtime warnings.</p>

    <p>On GTK+, the surface IDs to use can be found from the printing context with
    <code>gtk_print_context_get_cairo_context(context)</code>.</p>

     <p><code>chrg.cpMin</code> and <code>chrg.cpMax</code> define the start position and maximum
    position of characters to output. All of each line within this character range is drawn.</p>

    <p>When printing, the most tedious part is always working out what the margins should be to
    allow for the non-printable area of the paper and printing a header and footer. If you look at
    the printing code in SciTE, you will find that most of it is taken up with this. The loop that
    causes Scintilla to render text is quite simple if you strip out all the margin, non-printable
    area, header and footer code.</p>

    <p><b id="SCI_SETPRINTMAGNIFICATION">SCI_SETPRINTMAGNIFICATION(int magnification)</b><br />
     <b id="SCI_GETPRINTMAGNIFICATION">SCI_GETPRINTMAGNIFICATION &rarr; int</b><br />
     <code>SCI_GETPRINTMAGNIFICATION</code> lets you to print at a different size than the screen
    font. <code class="parameter">magnification</code> is the number of points to add to the size of each screen
    font. A value of -3 or -4 gives reasonably small print. You can get this value with
    <code>SCI_GETPRINTMAGNIFICATION</code>.</p>

    <p><b id="SCI_SETPRINTCOLOURMODE">SCI_SETPRINTCOLOURMODE(int mode)</b><br />
     <b id="SCI_GETPRINTCOLOURMODE">SCI_GETPRINTCOLOURMODE &rarr; int</b><br />
     These two messages set and get the method used to render coloured text on a printer that is
    probably using white paper. It is especially important to consider the treatment of colour if
    you use a dark or black screen background. Printing white on black uses up toner and ink very
    many times faster than the other way around. You can set the mode to one of:</p>

    <table class="standard" summary="Colour printing modes">
      <tbody>
        <tr>
          <th align="left">Symbol</th>

          <th>Value</th>

          <th align="left">Purpose</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>SC_PRINT_NORMAL</code></td>

          <td align="center">0</td>

          <td>Print using the current screen colours with the exception of line number margins which print on a white background.
		  This is the default.</td>
        </tr>

        <tr>
          <td align="left"><code>SC_PRINT_INVERTLIGHT</code></td>

          <td align="center">1</td>

          <td>If you use a dark screen background this saves ink by inverting the light value of
          all colours and printing on a white background.</td>
        </tr>

        <tr>
          <td align="left"><code>SC_PRINT_BLACKONWHITE</code></td>

          <td align="center">2</td>

          <td>Print all text as black on a white background.</td>
        </tr>

        <tr>
          <td align="left"><code>SC_PRINT_COLOURONWHITE</code></td>

          <td align="center">3</td>

          <td>Everything prints in its own colour on a white background.</td>
        </tr>

        <tr>
          <td align="left"><code>SC_PRINT_COLOURONWHITEDEFAULTBG</code></td>

          <td align="center">4</td>

          <td>Everything prints in its own foreground colour but all styles up to and including STYLE_LINENUMBER will print
		  on a white background.</td>
        </tr>

        <tr>
          <td align="left"><code>SC_PRINT_SCREENCOLOURS</code></td>

          <td align="center">5</td>

          <td>Print using the current screen colours for both foreground and background.
		  This is the only mode that does not set the background colour of the line number margin to white.</td>
        </tr>

      </tbody>
    </table>

    <p><b id="SCI_SETPRINTWRAPMODE">SCI_SETPRINTWRAPMODE(int wrapMode)</b><br />
     <b id="SCI_GETPRINTWRAPMODE">SCI_GETPRINTWRAPMODE &rarr; int</b><br />
     These two functions get and set the printer wrap mode. <code class="parameter">wrapMode</code> can be
     set to <code>SC_WRAP_NONE</code> (0), <code>SC_WRAP_WORD</code> (1) or
     <code>SC_WRAP_CHAR</code> (2). The default is
     <code>SC_WRAP_WORD</code>, which wraps printed output so that all characters fit
     into the print rectangle. If you set <code>SC_WRAP_NONE</code>, each line of text
     generates one line of output and the line is truncated if it is too long to fit
     into the print area.<br />
     <code>SC_WRAP_WORD</code> tries to wrap only between words as indicated by
     white space or style changes although if a word is longer than a line, it will be wrapped before
     the line end. <code>SC_WRAP_CHAR</code> is preferred to
     <code>SC_WRAP_WORD</code> for Asian languages where there is no white space
     between words.</p>

    <h2 id="DirectAccess">Direct access</h2>
    <code><a class="message" href="#SCI_GETDIRECTFUNCTION">SCI_GETDIRECTFUNCTION &rarr; int</a><br />
     <a class="message" href="#SCI_GETDIRECTPOINTER">SCI_GETDIRECTPOINTER &rarr; int</a><br />
     <a class="message" href="#SCI_GETCHARACTERPOINTER">SCI_GETCHARACTERPOINTER &rarr; int</a><br />
     <a class="message" href="#SCI_GETRANGEPOINTER">SCI_GETRANGEPOINTER(int start, int lengthRange) &rarr; int</a><br />
     <a class="message" href="#SCI_GETGAPPOSITION">SCI_GETGAPPOSITION &rarr; position</a><br />
    </code>

    <p>On Windows, the message-passing scheme used to communicate between the container and
    Scintilla is mediated by the operating system <code>SendMessage</code> function and can lead to
    bad performance when calling intensively. To avoid this overhead, Scintilla provides messages
    that allow you to call the Scintilla message function directly. The code to do this in C/C++ is
    of the form:</p>
<pre>
#include "Scintilla.h"
SciFnDirect pSciMsg = (SciFnDirect)SendMessage(hSciWnd, SCI_GETDIRECTFUNCTION, 0, 0);
sptr_t pSciWndData = (sptr_t)SendMessage(hSciWnd, SCI_GETDIRECTPOINTER, 0, 0);

// now a wrapper to call Scintilla directly
sptr_t CallScintilla(unsigned int iMessage, uptr_t wParam, sptr_t lParam){
    return pSciMsg(pSciWndData, iMessage, wParam, lParam);
}
</pre>

    <p><code>SciFnDirect</code>, <code>sptr_t</code> and <code>uptr_t</code> are declared in
    <code>Scintilla.h</code>. <code class="parameter">hSciWnd</code> is the window handle returned when you created
    the Scintilla window.</p>

    <p>While faster, this direct calling will cause problems if performed from a different thread
    to the native thread of the Scintilla window in which case <code>SendMessage(hSciWnd, SCI_*,
    wParam, lParam)</code> should be used to synchronize with the window's thread.</p>

    <p>This feature also works on GTK+ but has no significant impact on speed.</p>

    <p>From version 1.47 on Windows, Scintilla exports a function called
    <code>Scintilla_DirectFunction</code> that can be used the same as the function returned by
    <code>SCI_GETDIRECTFUNCTION</code>. This saves you the call to
    <code>SCI_GETDIRECTFUNCTION</code> and the need to call Scintilla indirectly via the function
    pointer.</p>

    <p><b id="SCI_GETDIRECTFUNCTION">SCI_GETDIRECTFUNCTION &rarr; int</b><br />
     This message returns the address of the function to call to handle Scintilla messages without
    the overhead of passing through the Windows messaging system. You need only call this once,
    regardless of the number of Scintilla windows you create.</p>

    <p><b id="SCI_GETDIRECTPOINTER">SCI_GETDIRECTPOINTER &rarr; int</b><br />
     This returns a pointer to data that identifies which Scintilla window is in use. You must call
    this once for each Scintilla window you create. When you call the direct function, you must
    pass in the direct pointer associated with the target window.</p>

    <p><b id="SCI_GETCHARACTERPOINTER">SCI_GETCHARACTERPOINTER &rarr; int</b><br />
    <b id="SCI_GETRANGEPOINTER">SCI_GETRANGEPOINTER(int start, int lengthRange) &rarr; int</b><br />
    <b id="SCI_GETGAPPOSITION">SCI_GETGAPPOSITION &rarr; position</b><br />
     Grant temporary direct read-only access to the memory used by Scintilla to store
     the document.</p>
     <p><code>SCI_GETCHARACTERPOINTER</code> moves the gap within Scintilla so that the
     text of the document is stored consecutively
     and ensure there is a NUL character after the text, then returns a pointer to the first character.
     Applications may then pass this to a function that accepts a character pointer such as a regular
     expression search or a parser. The pointer should <em>not</em> be written to as that may desynchronize
     the internal state of Scintilla.</p>
     <p>Since any action in Scintilla may change its internal state
     this pointer becomes invalid after any call or by allowing user interface activity. The application
     should reacquire the pointer after making any call to Scintilla or performing any user-interface calls such
     as modifying a progress indicator.</p>
     <p>This call takes similar time to inserting a character at the end of the document and this may
     include moving the document contents. Specifically, all the characters after the document gap
     are moved to before the gap. This compacted state should persist over calls and user interface
     actions that do not change the document contents so reacquiring the pointer afterwards is very
     quick. If this call is used to implement a global replace operation, then each replacement will
     move the gap so if <code>SCI_GETCHARACTERPOINTER</code> is called after
     each replacement then the operation will become O(n^2) rather than O(n). Instead, all
     matches should be found and remembered, then all the replacements performed.</p>

     <p><code>SCI_GETRANGEPOINTER</code> provides direct access to just the
     range requested. The gap is not moved unless it is within the requested range so this call
     can be faster than <code>SCI_GETCHARACTERPOINTER</code>.
     This can be used by application code that is able to act on blocks of text or ranges of lines.</p>

     <p><code>SCI_GETGAPPOSITION</code> returns the current gap position.
     This is a hint that applications can use to avoid calling <code>SCI_GETRANGEPOINTER</code>
     with a range that contains the gap and consequent costs of moving the gap.</p>

    <h2 id="MultipleViews">Multiple views</h2>

    <p>A Scintilla window and the document that it displays are separate entities. When you create
    a new window, you also create a new, empty document. Each document has a reference count that
    is initially set to 1. The document also has a list of the Scintilla windows that are linked to
    it so when any window changes the document, all other windows in which it appears are notified
    to cause them to update. The system is arranged in this way so that you can work with many
    documents in a single Scintilla window and so you can display a single document in multiple
    windows (for use with splitter windows).</p>

    <p>Although these messages use <code>document *doc</code>, to ensure compatibility with future
    releases of Scintilla you should treat <code class="parameter">doc</code> as an opaque <code>void*</code>. That
    is, you can use and store the pointer as described in this section but you should not
    dereference it.</p>
    <code><a class="message" href="#SCI_GETDOCPOINTER">SCI_GETDOCPOINTER &rarr; document *</a><br />
     <a class="message" href="#SCI_SETDOCPOINTER">SCI_SETDOCPOINTER(&lt;unused&gt;, document
    *doc)</a><br />
     <a class="message" href="#SCI_CREATEDOCUMENT">SCI_CREATEDOCUMENT(int bytes, int documentOptions) &rarr; document *</a><br />
     <a class="message" href="#SCI_ADDREFDOCUMENT">SCI_ADDREFDOCUMENT(&lt;unused&gt;, document
    *doc)</a><br />
     <a class="message" href="#SCI_RELEASEDOCUMENT">SCI_RELEASEDOCUMENT(&lt;unused&gt;, document
    *doc)</a><br />
     <a class="message" href="#SCI_GETDOCUMENTOPTIONS">SCI_GETDOCUMENTOPTIONS &rarr; int</a><br />
    </code>

    <p><b id="SCI_GETDOCPOINTER">SCI_GETDOCPOINTER &rarr; document *</b><br />
     This returns a pointer to the document currently in use by the window. It has no other
    effect.</p>

    <p><b id="SCI_SETDOCPOINTER">SCI_SETDOCPOINTER(&lt;unused&gt;, document *doc)</b><br />
     This message does the following:<br />
     1. It removes the current window from the list held by the current document.<br />
     2. It reduces the reference count of the current document by 1.<br />
     3. If the reference count reaches 0, the document is deleted.<br />
     4. <code class="parameter">doc</code> is set as the new document for the window.<br />
     5. If <code class="parameter">doc</code> was 0, a new, empty document is created and attached to the
    window.<br />
     6. If <code class="parameter">doc</code> was not 0, its reference count is increased by 1.</p>

    <p><b id="SCI_CREATEDOCUMENT">SCI_CREATEDOCUMENT(int bytes, int documentOptions) &rarr; document *</b><br />
     This message creates a new, empty document and returns a pointer to it. This document is not
     selected into the editor and starts with a reference count of 1. This means that you have
     ownership of it and must either reduce its reference count by 1 after using
    <code>SCI_SETDOCPOINTER</code> so that the Scintilla window owns it or you must make sure that
     you reduce the reference count by 1 with <code>SCI_RELEASEDOCUMENT</code> before you close the
     application to avoid memory leaks. The <code class="parameter">bytes</code> argument determines
     the initial memory allocation for the document as it is more efficient
     to allocate once rather than rely on the buffer growing as data is added.
     If <code>SCI_CREATEDOCUMENT</code> fails then 0 is returned.</p>

    <p id="documentOptions">The <code class="parameter">documentOptions</code> argument
    chooses between different document capabilities which affect memory allocation and performance with
    <code>SC_DOCUMENTOPTION_DEFAULT</code> (0) choosing standard options.
    <code>SC_DOCUMENTOPTION_STYLES_NONE</code> (0x1) stops allocation of memory to style characters
    which saves significant memory, often 40% with the whole document treated as being style 0.
    Lexers may still produce visual styling by using indicators.
    <span class="provisional"><code>SC_DOCUMENTOPTION_TEXT_LARGE</code> (0x100) accomodates documents larger than 2 GigaBytes
    in 64-bit executables.</span>
    </p>

    <p>With <code>SC_DOCUMENTOPTION_STYLES_NONE</code>, lexers are still active and may display
    indicators. Some may produce folding information althoough most require lexical styles to correctly determine folding.
    Its often more efficient to set the null lexer <code>SCLEX_NULL</code> so no lexer is run.
    </p>

    <p class="provisional">The <code>SC_DOCUMENTOPTION_TEXT_LARGE</code> option is experimental and has not been
    thoroughly tested. Lexers may fail or hang when lexing past 2GB or 4GB.
    Applications using this option should be tested to ensure the option works in their circumstances and each lexer
    included should also be tested with documents larger than 4GB.
    For many applications lexing documents larger than 4GB will be too sluggish so <code>SC_DOCUMENTOPTION_STYLES_NONE</code>
    and the null lexer <code>SCLEX_NULL</code> can be used. Another approach is to turn on idle styling with
    <a class="seealso" href="#SCI_SETIDLESTYLING"><code>SCI_SETIDLESTYLING</code></a>.
    </p>

    <table class="standard" summary="Document options">
      <tbody>
        <tr>
          <th align="left">Symbol</th>
          <th align="left">Value</th>
          <th align="left">Effect</th>
        </tr>
      </tbody>

      <tbody valign="top">

        <tr>
          <td align="left">SC_DOCUMENTOPTION_DEFAULT</td>
          <td align="left">0</td>
          <td align="left">Standard behaviour</td>
        </tr>

        <tr>
          <td align="left">SC_DOCUMENTOPTION_STYLES_NONE</td>
          <td align="left">0x1</td>
          <td align="left">Stop allocation of memory for styles and treat all text as style 0.</td>
        </tr>

        <tr>
          <td align="left" class="provisional">SC_DOCUMENTOPTION_TEXT_LARGE</td>
          <td align="left">0x100</td>
          <td align="left">Allow document to be larger than 2 GB.</td>
        </tr>

      </tbody>
    </table>

    <p><b id="SCI_ADDREFDOCUMENT">SCI_ADDREFDOCUMENT(&lt;unused&gt;, document *doc)</b><br />
     This increases the reference count of a document by 1. If you want to replace the current
    document in the Scintilla window and take ownership of the current document, for example if you
    are editing many documents in one window, do the following:<br />
     1. Use <code>SCI_GETDOCPOINTER</code> to get a pointer to the document,
    <code class="parameter">doc</code>.<br />
     2. Use <code>SCI_ADDREFDOCUMENT(0, doc)</code> to increment the reference count.<br />
     3. Use <code>SCI_SETDOCPOINTER(0, docNew)</code> to set a different document or
    <code>SCI_SETDOCPOINTER(0, 0)</code> to set a new, empty document.</p>

    <p><b id="SCI_RELEASEDOCUMENT">SCI_RELEASEDOCUMENT(&lt;unused&gt;, document *doc)</b><br />
     This message reduces the reference count of the document identified by <code class="parameter">doc</code>. doc
    must be the result of <code>SCI_GETDOCPOINTER</code> or <code>SCI_CREATEDOCUMENT</code> and
    must point at a document that still exists. If you call this on a document with a reference
    count of 1 that is still attached to a Scintilla window, bad things will happen. To keep the
    world spinning in its orbit you must balance each call to <code>SCI_CREATEDOCUMENT</code> or
    <code>SCI_ADDREFDOCUMENT</code> with a call to <code>SCI_RELEASEDOCUMENT</code>.</p>

    <p><b id="SCI_GETDOCUMENTOPTIONS">SCI_GETDOCUMENTOPTIONS &rarr; int</b><br />
     Returns the options that were used to create the document.</p>

    <h2 id="BackgroundLoadSave">Background loading and saving</h2>

    <p>To ensure a responsive user interface, applications may decide to load and save documents using a separate thread
    from the user interface.</p>

    <h3 id="BackgroundLoad">Loading in the background</h3>

    <code><a class="message" href="#SCI_CREATELOADER">SCI_CREATELOADER(int bytes, int documentOptions) &rarr; int</a><br />
    </code>

    <p>An application can load all of a file into a buffer it allocates on a background thread and then add the data in that buffer
    into a Scintilla document on the user interface thread. That technique uses extra memory to store a complete copy of the
    file and also means that the time that Scintilla takes to perform initial line end discovery blocks the user interface.</p>

    <p>To avoid these issues, a loader object may be created and used to load the file. The loader object supports the ILoader interface.</p>

    <p><b id="SCI_CREATELOADER">SCI_CREATELOADER(int bytes, int documentOptions) &rarr; int</b><br />
     Create an object that supports the <code>ILoader</code> interface which can be used to load data and then
     be turned into a Scintilla document object for attachment to a view object.
     The <code class="parameter">bytes</code> argument determines the initial memory allocation for the document as it is more efficient
     to allocate once rather than rely on the buffer growing as data is added.
     If <code>SCI_CREATELOADER</code> fails then 0 is returned.</p>

    <p>The <code class="parameter">documentOptions</code> argument
    is described in the <a class="seealso" href="#documentOptions"><code>SCI_CREATEDOCUMENT</code></a> section.</p>

<h4>ILoader</h4>

<div class="highlighted">
<span class="S5">class</span><span class="S0"> </span>ILoader<span class="S0"> </span><span class="S10">{</span><br />
<span class="S5">public</span><span class="S10">:</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>Release<span class="S10">()</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S2">// Returns a status code from SC_STATUS_*</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>AddData<span class="S10">(</span><span class="S5">const</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span><span class="S10">*</span>data<span class="S10">,</span><span class="S0"> </span>Sci_Position<span class="S0"> </span>length<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">void</span><span class="S0"> </span><span class="S10">*</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>ConvertToDocument<span class="S10">()</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S10">};</span><br />
</div>

    <p>The application should call the <code>AddData</code> method with each block of data read from the file.
    <code>AddData</code> will return SC_STATUS_OK unless a failure, such as memory exhaustion occurs.
    If a failure occurs in <code>AddData</code> or in a file reading call then loading can be abandoned and the loader released with
    the <code>Release</code> call.
    When the whole file has been read, <code>ConvertToDocument</code> should be called to produce a Scintilla
    document pointer. The newly created document will have a reference count of 1 in the same way as a document pointer
    returned from
    <a class="seealso" href="#SCI_CREATEDOCUMENT">SCI_CREATEDOCUMENT</a>.
    There is no need to call <code>Release</code> after <code>ConvertToDocument</code>.</p>

    <h3 id="BackgroundSave">Saving in the background</h3>

    <p>An application that wants to save in the background should lock the document with <code>SCI_SETREADONLY(1)</code>
    to prevent modifications and retrieve a pointer to the unified document contents with
    <a class="seealso" href="#SCI_GETCHARACTERPOINTER">SCI_GETCHARACTERPOINTER</a>.
    The buffer of a locked document will not move so the pointer is valid until the application calls <code>SCI_SETREADONLY(0)</code>.</p>

    <p>If the user tries to performs a modification while the document is locked then a <code><a class="message"
    href="#SCN_MODIFYATTEMPTRO">SCN_MODIFYATTEMPTRO</a></code> notification is sent to the application.
    The application may then decide to ignore the modification or to terminate the background saving thread and reenable
    modification before returning from the notification.</p>

    <h2 id="Folding">Folding</h2>

    <p>The fundamental operation in folding is making lines invisible or visible. Line visibility
    is a property of the view rather than the document so each view may be displaying a different
    set of lines. From the point of view of the user, lines are hidden and displayed using fold
    points. Generally, the fold points of a document are based on the hierarchical structure of the
    document contents. In Python, the hierarchy is determined by indentation and in C++ by brace
    characters. This hierarchy can be represented within a Scintilla document object by attaching a
    numeric "fold level" to each line. The fold level is most easily set by a lexer, but you can
    also set it with messages.</p>

    <p>It is up to your code to set the connection between user actions and folding and unfolding.
    The best way to see how this is done is to search the SciTE source code for the messages used
    in this section of the documentation and see how they are used. You will also need to use
    markers and a folding margin to complete your folding implementation.
    The <code>"fold"</code> property should be set to <code>"1"</code> with
    <code>SCI_SETPROPERTY("fold", "1")</code> to enable folding. </p>
    <code><a class="message" href="#SCI_VISIBLEFROMDOCLINE">SCI_VISIBLEFROMDOCLINE(int docLine) &rarr; int</a><br />
     <a class="message" href="#SCI_DOCLINEFROMVISIBLE">SCI_DOCLINEFROMVISIBLE(int displayLine) &rarr; int</a><br />
     <a class="message" href="#SCI_SHOWLINES">SCI_SHOWLINES(int lineStart, int lineEnd)</a><br />
     <a class="message" href="#SCI_HIDELINES">SCI_HIDELINES(int lineStart, int lineEnd)</a><br />
     <a class="message" href="#SCI_GETLINEVISIBLE">SCI_GETLINEVISIBLE(int line) &rarr; bool</a><br />
     <a class="message" href="#SCI_GETALLLINESVISIBLE">SCI_GETALLLINESVISIBLE &rarr; bool</a><br />
     <a class="message" href="#SCI_SETFOLDLEVEL">SCI_SETFOLDLEVEL(int line, int level)</a><br />
     <a class="message" href="#SCI_GETFOLDLEVEL">SCI_GETFOLDLEVEL(int line) &rarr; int</a><br />
     <a class="message" href="#SCI_SETAUTOMATICFOLD">SCI_SETAUTOMATICFOLD(int automaticFold)</a><br />
     <a class="message" href="#SCI_GETAUTOMATICFOLD">SCI_GETAUTOMATICFOLD &rarr; int</a><br />
     <a class="message" href="#SCI_SETFOLDFLAGS">SCI_SETFOLDFLAGS(int flags)</a><br />
     <a class="message" href="#SCI_GETLASTCHILD">SCI_GETLASTCHILD(int line, int level) &rarr; int</a><br />
     <a class="message" href="#SCI_GETFOLDPARENT">SCI_GETFOLDPARENT(int line) &rarr; int</a><br />
     <a class="message" href="#SCI_SETFOLDEXPANDED">SCI_SETFOLDEXPANDED(int line, bool
    expanded)</a><br />
     <a class="message" href="#SCI_GETFOLDEXPANDED">SCI_GETFOLDEXPANDED(int line) &rarr; bool</a><br />
     <a class="message" href="#SCI_CONTRACTEDFOLDNEXT">SCI_CONTRACTEDFOLDNEXT(int lineStart) &rarr; int</a><br />
     <a class="message" href="#SCI_TOGGLEFOLD">SCI_TOGGLEFOLD(int line)</a><br />
     <a class="message" href="#SCI_TOGGLEFOLDSHOWTEXT">SCI_TOGGLEFOLDSHOWTEXT(int line, const char *text)</a><br />
     <a class="message" href="#SCI_FOLDDISPLAYTEXTSETSTYLE">SCI_FOLDDISPLAYTEXTSETSTYLE(int style)</a><br />
     <a class="message" href="#SCI_FOLDLINE">SCI_FOLDLINE(int line, int action)</a><br />
     <a class="message" href="#SCI_FOLDCHILDREN">SCI_FOLDCHILDREN(int line, int action)</a><br />
     <a class="message" href="#SCI_FOLDALL">SCI_FOLDALL(int action)</a><br />
     <a class="message" href="#SCI_EXPANDCHILDREN">SCI_EXPANDCHILDREN(int line, int level)</a><br />
     <a class="message" href="#SCI_ENSUREVISIBLE">SCI_ENSUREVISIBLE(int line)</a><br />
     <a class="message" href="#SCI_ENSUREVISIBLEENFORCEPOLICY">SCI_ENSUREVISIBLEENFORCEPOLICY(int
    line)</a><br />
    </code>

    <p><b id="SCI_VISIBLEFROMDOCLINE">SCI_VISIBLEFROMDOCLINE(int docLine) &rarr; int</b><br />
     When some lines are hidden and/or annotations are displayed, then a particular line in the
     document may be displayed at a
    different position to its document position. If no lines are hidden and there are no annotations,
    this message returns
    <code class="parameter">docLine</code>. Otherwise, this returns the display line (counting the very first visible
    line as 0). The display line of an invisible line is the same as the previous visible line. The
    display line number of the first line in the document is 0. If lines are hidden and
    <code class="parameter">docLine</code> is outside the range of lines in the document, the return value is -1.
    Lines can occupy more than one display line if they wrap.</p>

    <p><b id="SCI_DOCLINEFROMVISIBLE">SCI_DOCLINEFROMVISIBLE(int displayLine) &rarr; int</b><br />
     When some lines are hidden and/or annotations are displayed, then a particular line in the
     document may be displayed at a
    different position to its document position. This message returns the document line number that
    corresponds to a display line (counting the display line of the first line in the document as
    0). If <code class="parameter">displayLine</code> is less than or equal to 0, the result is 0. If
    <code class="parameter">displayLine</code> is greater than or equal to the number of displayed lines, the result
    is the number of lines in the document.</p>

    <p><b id="SCI_SHOWLINES">SCI_SHOWLINES(int lineStart, int lineEnd)</b><br />
     <b id="SCI_HIDELINES">SCI_HIDELINES(int lineStart, int lineEnd)</b><br />
     <b id="SCI_GETLINEVISIBLE">SCI_GETLINEVISIBLE(int line) &rarr; bool</b><br />
     <b id="SCI_GETALLLINESVISIBLE">SCI_GETALLLINESVISIBLE &rarr; bool</b><br />
     The first two messages mark a range of lines as visible or invisible and then redraw the
    display.
    <code>SCI_GETLINEVISIBLE</code> reports on the visible state of a line and returns 1 if it is
    visible and 0 if it is not visible.
    <code>SCI_GETALLLINESVISIBLE</code> returns 1 if all lines are visible and 0
    if some lines are hidden.
    These messages have no effect on fold levels or fold
    flags. The first line can not be hidden.</p>

    <p><b id="SCI_SETFOLDLEVEL">SCI_SETFOLDLEVEL(int line, int level)</b><br />
     <b id="SCI_GETFOLDLEVEL">SCI_GETFOLDLEVEL(int line) &rarr; int</b><br />
     These two messages set and get a 32-bit value that contains the fold level of a line and some
    flags associated with folding. The fold level is a number in the range 0 to
    <code>SC_FOLDLEVELNUMBERMASK</code> (0x0FFF). However, the initial fold level is set to
    <code>SC_FOLDLEVELBASE</code> (0x400) to allow unsigned arithmetic on folding levels. There are
    two addition flag bits. <code>SC_FOLDLEVELWHITEFLAG</code> indicates that the line is blank and
    allows it to be treated slightly different then its level may indicate. For example, blank
    lines should generally not be fold points and will be considered part of the preceding section even though
    they may have a lesser fold level.
    <code>SC_FOLDLEVELHEADERFLAG</code> indicates that
    the line is a header (fold point).</p>

    <p>Use <code>SCI_GETFOLDLEVEL(line) &amp; SC_FOLDLEVELNUMBERMASK</code> to get the fold level
    of a line. Likewise, use <code>SCI_GETFOLDLEVEL(line) &amp; SC_FOLDLEVEL*FLAG</code> to get the
    state of the flags. To set the fold level you must or in the associated flags. For instance, to
    set the level to <code>thisLevel</code> and mark a line as being a fold point use:
    <code>SCI_SETFOLDLEVEL(line, thisLevel | SC_FOLDLEVELHEADERFLAG)</code>.</p>
    If you use a lexer, you should not need to use <code>SCI_SETFOLDLEVEL</code> as this is far
    better handled by the lexer. You will need to use <code>SCI_GETFOLDLEVEL</code> to decide how
    to handle user folding requests. If you do change the fold levels, the folding margin will
    update to match your changes.

    <p><b id="SCI_SETFOLDFLAGS">SCI_SETFOLDFLAGS(int flags)</b><br />
     In addition to showing markers in the folding margin, you can indicate folds to the user by
    drawing lines in the text area. The lines are drawn in the foreground colour set for <a
    class="message" href="#StyleDefinition"><code>STYLE_DEFAULT</code></a>. Bits set in
    <code class="parameter">flags</code> determine where folding lines are drawn:<br />
    </p>

    <table class="standard" summary="Fold flags">
      <tbody>
        <tr>
          <th align="left">Symbol</th>
          <th align="left">Value</th>
          <th align="left">Effect</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"></td>
          <td align="left">1</td>
          <td align="left">Experimental feature that has been removed.</td>
        </tr>

        <tr>
          <td align="left">SC_FOLDFLAG_LINEBEFORE_EXPANDED</td>
          <td align="left">2</td>

          <td align="left">Draw above if expanded</td>
        </tr>

        <tr>
          <td align="left">SC_FOLDFLAG_LINEBEFORE_CONTRACTED</td>
          <td align="left">4</td>

          <td align="left">Draw above if not expanded</td>
        </tr>

        <tr>
          <td align="left">SC_FOLDFLAG_LINEAFTER_EXPANDED</td>
          <td align="left">8</td>

          <td align="left">Draw below if expanded</td>
        </tr>

        <tr>
          <td align="left">SC_FOLDFLAG_LINEAFTER_CONTRACTED</td>
          <td align="left">16</td>

          <td align="left">Draw below if not expanded</td>
        </tr>

        <tr>
          <td align="left">SC_FOLDFLAG_LEVELNUMBERS</td>
          <td align="left">64</td>

          <td align="left">display hexadecimal fold levels in line margin to aid debugging of
          folding. The appearance of this feature may change in the future.</td>
        </tr>

        <tr>
          <td align="left">SC_FOLDFLAG_LINESTATE</td>
          <td align="left">128</td>

          <td align="left">display hexadecimal line state in line margin to aid debugging of lexing and folding.
	  May not be used at the same time as <code>SC_FOLDFLAG_LEVELNUMBERS</code>.</td>
        </tr>
      </tbody>
    </table>

    <p>This message causes the display to redraw.</p>

    <p><b id="SCI_GETLASTCHILD">SCI_GETLASTCHILD(int line, int level) &rarr; int</b><br />
     This message searches for the next line after <code class="parameter">line</code>, that has a folding level
    that is less than or equal to <code class="parameter">level</code> and then returns the previous line number. If
    you set <code class="parameter">level</code> to -1, <code class="parameter">level</code> is set to the folding level of line
    <code class="parameter">line</code>. If <code>from</code> is a fold point, <code>SCI_GETLASTCHILD(from,
    -1)</code> returns the last line that would be in made visible or hidden by toggling the fold
    state.</p>

    <p><b id="SCI_GETFOLDPARENT">SCI_GETFOLDPARENT(int line) &rarr; int</b><br />
     This message returns the line number of the first line before <code class="parameter">line</code> that is
    marked as a fold point with <code>SC_FOLDLEVELHEADERFLAG</code> and has a fold level less than
    the <code class="parameter">line</code>. If no line is found, or if the header flags and fold levels are
    inconsistent, the return value is -1.</p>

    <p><b id="SCI_TOGGLEFOLD">SCI_TOGGLEFOLD(int line)</b><br />
    <b id="SCI_TOGGLEFOLDSHOWTEXT">SCI_TOGGLEFOLDSHOWTEXT(int line, const char *text)</b><br />
     Each fold point may be either expanded, displaying all its child lines, or contracted, hiding
    all the child lines. These messages toggle the folding state of the given line as long as it has
    the <code>SC_FOLDLEVELHEADERFLAG</code> set. These messages take care of folding or expanding
    all the lines that depend on the line. The display updates after this message.</p>
    <p>An optional text tag may be shown to the right of the folded text with the
    <code class="parameter">text</code> argument to
    <code>SCI_TOGGLEFOLDSHOWTEXT</code>.
    The text is drawn with the
    <code><a class="message" href="#StyleDefinition">STYLE_FOLDDISPLAYTEXT</a></code> style.</p>

    <p><b id="SCI_FOLDDISPLAYTEXTSETSTYLE">SCI_FOLDDISPLAYTEXTSETSTYLE(int style)</b><br />
    This message changes the appearance of fold text tags.</p>
    <table class="standard" summary="Fold flags">
      <tbody>
        <tr>
          <th align="left">Symbol</th>
          <th align="left">Value</th>
          <th align="left">Effect</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left">SC_FOLDDISPLAYTEXT_HIDDEN</td>
          <td align="left">0</td>
          <td align="left">Do not display the text tags. This is the default.</td>
        </tr>

        <tr>
          <td align="left">SC_FOLDDISPLAYTEXT_STANDARD</td>
          <td align="left">1</td>
          <td align="left">Display the text tags.</td>
        </tr>

        <tr>
          <td align="left">SC_FOLDDISPLAYTEXT_BOXED</td>
          <td align="left">2</td>
          <td align="left">Display the text tags with a box drawn around them.</td>
        </tr>

      </tbody>
    </table>

    <p><b id="SCI_SETFOLDEXPANDED">SCI_SETFOLDEXPANDED(int line, bool expanded)</b><br />
     <b id="SCI_GETFOLDEXPANDED">SCI_GETFOLDEXPANDED(int line) &rarr; bool</b><br />
     These messages set and get the expanded state of a single line. The set message has no effect
    on the visible state of the line or any lines that depend on it. It does change the markers in
    the folding margin. If you ask for the expansion state of a line that is outside the document,
    the result is <code>false</code> (0).</p>

    <p>If you just want to toggle the fold state of one line and handle all the lines that are
    dependent on it, it is much easier to use <code>SCI_TOGGLEFOLD</code>. You would use the
    <code>SCI_SETFOLDEXPANDED</code> message to process many folds without updating the display
    until you had finished. See <code>SciTEBase::FoldAll()</code> and
    <code>SciTEBase::Expand()</code> for examples of the use of these messages.</p>

    <p><b id="SCI_FOLDLINE">SCI_FOLDLINE(int line, int action)</b><br />
    <b id="SCI_FOLDCHILDREN">SCI_FOLDCHILDREN(int line, int action)</b><br />
    <b id="SCI_FOLDALL">SCI_FOLDALL(int action)</b><br />
    These messages provide a higher-level approach to folding instead of setting expanded flags and showing
    or hiding individual lines.</p>
    <p>An individual fold can be contracted/expanded/toggled with <code>SCI_FOLDLINE</code>.
    To affect all child folds as well call <code>SCI_FOLDCHILDREN</code>.</p>
    <p>To affect the entire document call <code>SCI_FOLDALL</code>. With <code>SC_FOLDACTION_TOGGLE</code>
    the first fold header in the document is examined to decide whether to expand or contract.
    </p>
    <table class="standard" summary="Fold flags">
      <tbody>
        <tr>
          <th align="left">Symbol</th>
          <th align="left">Value</th>
          <th align="left">Effect</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left">SC_FOLDACTION_CONTRACT</td>
          <td align="left">0</td>
          <td align="left">Contract.</td>
        </tr>

        <tr>
          <td align="left">SC_FOLDACTION_EXPAND</td>
          <td align="left">1</td>
          <td align="left">Expand.</td>
        </tr>

        <tr>
          <td align="left">SC_FOLDACTION_TOGGLE</td>
          <td align="left">2</td>
          <td align="left">Toggle between contracted and expanded.</td>
        </tr>

      </tbody>
    </table>

    <p><b id="SCI_EXPANDCHILDREN">SCI_EXPANDCHILDREN(int line, int level)</b><br />
    This is used to respond to a change to a line causing its fold level or whether it is a header to change,
    perhaps when adding or removing a '{'.</p>
    <p>By the time the container has received the notification that the line has changed,
    the fold level has already been set, so the container has to use the previous level in this call
    so that any range hidden underneath this line can be shown.
    </p>

    <p><b id="SCI_SETAUTOMATICFOLD">SCI_SETAUTOMATICFOLD(int automaticFold)</b><br />
    <b id="SCI_GETAUTOMATICFOLD">SCI_GETAUTOMATICFOLD &rarr; int</b><br />
    Instead of implementing all the logic for handling folding in the container, Scintilla can provide behaviour
    that is adequate for many applications. The <code class="parameter">automaticFold</code> argument is a bit set defining
    which of the 3 pieces of folding implementation should be enabled. Most applications should be able to use the
    <code>SC_AUTOMATICFOLD_SHOW</code> and <code>SC_AUTOMATICFOLD_CHANGE</code>
    flags unless they wish to implement quite different behaviour such as defining their own fold structure.
    <code>SC_AUTOMATICFOLD_CLICK</code> is more likely to be set off when an application would
    like to add or change click behaviour such as showing method headers only when Shift+Alt is used in
    conjunction with a click.
    </p>
    <table class="standard" summary="Fold flags">
      <tbody>
        <tr>
          <th align="left">Symbol</th>
          <th align="left">Value</th>
          <th align="left">Effect</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left">SC_AUTOMATICFOLD_SHOW</td>
          <td align="left">1</td>
          <td align="left">Automatically show lines as needed.
          This avoids sending the <code>SCN_NEEDSHOWN</code> notification.</td>
        </tr>

        <tr>
          <td align="left">SC_AUTOMATICFOLD_CLICK</td>
          <td align="left">2</td>
          <td align="left">Handle clicks in fold margin automatically.
          This avoids sending the <code>SCN_MARGINCLICK</code> notification for folding margins.</td>
        </tr>

        <tr>
          <td align="left">SC_AUTOMATICFOLD_CHANGE</td>
          <td align="left">4</td>
          <td align="left">Show lines as needed when fold structure is changed.
          The <code>SCN_MODIFIED</code> notification is still sent unless it is disabled by the
          container.</td>
        </tr>

      </tbody>
    </table>

    <p><b id="SCI_CONTRACTEDFOLDNEXT">SCI_CONTRACTEDFOLDNEXT(int lineStart) &rarr; int</b><br />
     Search efficiently for lines that are contracted fold headers.
     This is useful when saving the user's folding when switching documents or saving folding with a file.
     The search starts at line number <code class="parameter">lineStart</code> and continues forwards to the end of the file.
     <code class="parameter">lineStart</code> is returned if it is a contracted fold header otherwise the next contracted
     fold header is returned. If there are no more contracted fold headers then -1 is returned.</p>

    <p><b id="SCI_ENSUREVISIBLE">SCI_ENSUREVISIBLE(int line)</b><br />
     <b id="SCI_ENSUREVISIBLEENFORCEPOLICY">SCI_ENSUREVISIBLEENFORCEPOLICY(int line)</b><br />
     A line may be hidden because more than one of its parent lines is contracted. Both these
    message travels up the fold hierarchy, expanding any contracted folds until they reach the top
    level. The line will then be visible. If you use <code>SCI_ENSUREVISIBLEENFORCEPOLICY</code>,
    the vertical caret policy set by <a class="message"
    href="#SCI_SETVISIBLEPOLICY"><code>SCI_SETVISIBLEPOLICY</code></a> is then applied.</p>

    <h2 id="LineWrapping">Line wrapping</h2>

    <code><a class="message" href="#SCI_SETWRAPMODE">SCI_SETWRAPMODE(int wrapMode)</a><br />
     <a class="message" href="#SCI_GETWRAPMODE">SCI_GETWRAPMODE &rarr; int</a><br />
     <a class="message" href="#SCI_SETWRAPVISUALFLAGS">SCI_SETWRAPVISUALFLAGS(int wrapVisualFlags)</a><br />
     <a class="message" href="#SCI_GETWRAPVISUALFLAGS">SCI_GETWRAPVISUALFLAGS &rarr; int</a><br />
     <a class="message" href="#SCI_SETWRAPVISUALFLAGSLOCATION">SCI_SETWRAPVISUALFLAGSLOCATION(int wrapVisualFlagsLocation)</a><br />
     <a class="message" href="#SCI_GETWRAPVISUALFLAGSLOCATION">SCI_GETWRAPVISUALFLAGSLOCATION &rarr; int</a><br />
     <a class="message" href="#SCI_SETWRAPINDENTMODE">SCI_SETWRAPINDENTMODE(int wrapIndentMode)</a><br />
     <a class="message" href="#SCI_GETWRAPINDENTMODE">SCI_GETWRAPINDENTMODE &rarr; int</a><br />
     <a class="message" href="#SCI_SETWRAPSTARTINDENT">SCI_SETWRAPSTARTINDENT(int indent)</a><br />
     <a class="message" href="#SCI_GETWRAPSTARTINDENT">SCI_GETWRAPSTARTINDENT &rarr; int</a><br />
     <a class="message" href="#SCI_SETLAYOUTCACHE">SCI_SETLAYOUTCACHE(int cacheMode)</a><br />
     <a class="message" href="#SCI_GETLAYOUTCACHE">SCI_GETLAYOUTCACHE &rarr; int</a><br />
     <a class="message" href="#SCI_SETPOSITIONCACHE">SCI_SETPOSITIONCACHE(int size)</a><br />
     <a class="message" href="#SCI_GETPOSITIONCACHE">SCI_GETPOSITIONCACHE &rarr; int</a><br />
     <a class="message" href="#SCI_LINESSPLIT">SCI_LINESSPLIT(int pixelWidth)</a><br />
     <a class="message" href="#SCI_LINESJOIN">SCI_LINESJOIN</a><br />
     <a class="message" href="#SCI_WRAPCOUNT">SCI_WRAPCOUNT(int docLine) &rarr; int</a><br />
    </code>

    <p>By default, Scintilla does not wrap lines of text. If you enable line wrapping, lines wider
    than the window width are continued on the following lines. Lines are broken after space or tab
    characters or between runs of different styles. If this is not possible because a word in one
    style is wider than the window then the break occurs after the last character that completely
    fits on the line. The horizontal scroll bar does not appear when wrap mode is on.</p>

        <p>For wrapped lines Scintilla can draw visual flags (little arrows) at end of a a subline of a
        wrapped line and at begin of the next subline. These can be enabled individually, but if Scintilla
        draws the visual flag at the beginning of the next subline this subline will be indented by one char.
        Independent from drawing a visual flag at the begin the subline can have an indention.</p>

    <p>Much of the time used by Scintilla is spent on laying out and drawing text. The same text
    layout calculations may be performed many times even when the data used in these calculations
    does not change. To avoid these unnecessary calculations in some circumstances, the line layout
    cache can store the results of the calculations. The cache is invalidated whenever the
    underlying data, such as the contents or styling of the document changes. Caching the layout of
    the whole document has the most effect, making dynamic line wrap as much as 20 times faster but
    this requires 7 times the memory required by the document contents plus around 80 bytes per
    line.</p>

    <p>Wrapping is not performed immediately there is a change but is delayed until the display
    is redrawn. This delay improves performance by allowing a set of changes to be performed
    and then wrapped and displayed once. Because of this, some operations may not occur as
    expected. If a file is read and the scroll position moved to a particular line in the text,
    such as occurs when a container tries to restore a previous editing session, then
    the scroll position will have been determined before wrapping so an unexpected range
    of text will be displayed. To scroll to the position correctly, delay the scroll until the
    wrapping has been performed by waiting for an initial
    <a class="message" href="#SCN_PAINTED">SCN_PAINTED</a> notification.</p>

    <p><b id="SCI_SETWRAPMODE">SCI_SETWRAPMODE(int wrapMode)</b><br />
     <b id="SCI_GETWRAPMODE">SCI_GETWRAPMODE &rarr; int</b><br />
     Set wrapMode to <code>SC_WRAP_WORD</code> (1) to enable wrapping
     on word or style boundaries, <code>SC_WRAP_CHAR</code> (2) to enable wrapping
     between any characters, <code>SC_WRAP_WHITESPACE</code> (3) to enable
     wrapping on whitespace, and <code>SC_WRAP_NONE</code> (0) to disable line
     wrapping. <code>SC_WRAP_CHAR</code> is preferred for Asian languages where
     there is no white space between words.
    </p>


    <p><b id="SCI_SETWRAPVISUALFLAGS">SCI_SETWRAPVISUALFLAGS(int wrapVisualFlags)</b><br />
     <b id="SCI_GETWRAPVISUALFLAGS">SCI_GETWRAPVISUALFLAGS &rarr; int</b><br />
                You can enable the drawing of visual flags to indicate a line is wrapped. Bits set in
                wrapVisualFlags determine which visual flags are drawn.
    </p>
    <table class="standard" summary="Wrap visual flags">
      <tbody>
        <tr>
          <th align="left">Symbol</th>
          <th>Value</th>
          <th align="left">Effect</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>SC_WRAPVISUALFLAG_NONE</code></td>
          <td align="center">0</td>
          <td>No visual flags</td>
        </tr>

        <tr>
          <td align="left"><code>SC_WRAPVISUALFLAG_END</code></td>
          <td align="center">1</td>
          <td>Visual flag at end of subline of a wrapped line.</td>
        </tr>

        <tr>
          <td align="left"><code>SC_WRAPVISUALFLAG_START</code></td>
          <td align="center">2</td>
          <td>Visual flag at begin of subline of a wrapped line.<br />
                                             Subline is indented by at least 1 to make room for the flag.<br />
                                </td>
        </tr>

        <tr>
          <td align="left"><code>SC_WRAPVISUALFLAG_MARGIN</code></td>
          <td align="center">4</td>
          <td>Visual flag in line number margin.</td>
        </tr>
      </tbody>
    </table>

    <p><b id="SCI_SETWRAPVISUALFLAGSLOCATION">SCI_SETWRAPVISUALFLAGSLOCATION(int wrapVisualFlagsLocation)</b><br />
     <b id="SCI_GETWRAPVISUALFLAGSLOCATION">SCI_GETWRAPVISUALFLAGSLOCATION &rarr; int</b><br />
                You can set whether the visual flags to indicate a line is wrapped are drawn near the border or near the text.
                Bits set in wrapVisualFlagsLocation set the location to near the text for the corresponding visual flag.
    </p>

    <table class="standard" summary="Wrap visual flags locations">
      <tbody>
        <tr>
          <th align="left">Symbol</th>
          <th>Value</th>
          <th align="left">Effect</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>SC_WRAPVISUALFLAGLOC_DEFAULT</code></td>
          <td align="center">0</td>
          <td>Visual flags drawn near border</td>
        </tr>

        <tr>
          <td align="left"><code>SC_WRAPVISUALFLAGLOC_END_BY_TEXT</code></td>
          <td align="center">1</td>
          <td>Visual flag at end of subline drawn near text</td>
        </tr>

        <tr>
          <td align="left"><code>SC_WRAPVISUALFLAGLOC_START_BY_TEXT</code></td>
          <td align="center">2</td>
          <td>Visual flag at beginning of subline drawn near text</td>
        </tr>
      </tbody>
    </table>

    <p><b id="SCI_SETWRAPINDENTMODE">SCI_SETWRAPINDENTMODE(int wrapIndentMode)</b><br />
     <b id="SCI_GETWRAPINDENTMODE">SCI_GETWRAPINDENTMODE &rarr; int</b><br />
                Wrapped sublines can be indented to the position of their first subline or one more indent level.
	  The default is <code>SC_WRAPINDENT_FIXED</code>.
                The modes are:
    </p>

    <table class="standard" summary="Wrap visual flags locations">
      <tbody>
        <tr>
          <th align="left">Symbol</th>
          <th>Value</th>
          <th align="left">Effect</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>SC_WRAPINDENT_FIXED</code></td>
          <td align="center">0</td>
          <td>
              Wrapped sublines aligned to left of window plus amount set by
              <a class="seealso" href="#SCI_SETWRAPSTARTINDENT">SCI_SETWRAPSTARTINDENT</a>
          </td>
        </tr>

        <tr>
          <td align="left"><code>SC_WRAPINDENT_SAME</code></td>
          <td align="center">1</td>
          <td>Wrapped sublines are aligned to first subline indent</td>
        </tr>

        <tr>
          <td align="left"><code>SC_WRAPINDENT_INDENT</code></td>
          <td align="center">2</td>
          <td>Wrapped sublines are aligned to first subline indent plus one more level of indentation</td>
        </tr>

        <tr>
          <td align="left"><code>SC_WRAPINDENT_DEEPINDENT</code></td>
          <td align="center">3</td>
          <td>Wrapped sublines are aligned to first subline indent plus two more levels of indentation</td>
        </tr>
      </tbody>
    </table>

    <p><b id="SCI_SETWRAPSTARTINDENT">SCI_SETWRAPSTARTINDENT(int indent)</b><br />
     <b id="SCI_GETWRAPSTARTINDENT">SCI_GETWRAPSTARTINDENT &rarr; int</b><br />
     <code>SCI_SETWRAPSTARTINDENT</code> sets the size of indentation of sublines for
                 wrapped lines in terms of the average character width in
                <a class="message" href="#StyleDefinition"><code>STYLE_DEFAULT</code></a>.
                There are no limits on indent sizes, but values        less than 0 or large values may have
                undesirable effects.<br />
                The indention of sublines is independent of visual flags, but if
                <code>SC_WRAPVISUALFLAG_START</code> is set an indent of at least 1 is used.
     </p>

    <p><b id="SCI_SETLAYOUTCACHE">SCI_SETLAYOUTCACHE(int cacheMode)</b><br />
     <b id="SCI_GETLAYOUTCACHE">SCI_GETLAYOUTCACHE &rarr; int</b><br />
     You can set <code class="parameter">cacheMode</code> to one of the symbols in the table:</p>

    <table class="standard" summary="Line caching styles">
      <tbody>
        <tr>
          <th align="left">Symbol</th>

          <th>Value</th>

          <th align="left">Layout cached for these lines</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>SC_CACHE_NONE</code></td>

          <td align="center">0</td>

          <td>No lines are cached.</td>
        </tr>

        <tr>
          <td align="left"><code>SC_CACHE_CARET</code></td>

          <td align="center">1</td>

          <td>The line containing the text caret. This is the default.</td>
        </tr>

        <tr>
          <td align="left"><code>SC_CACHE_PAGE</code></td>

          <td align="center">2</td>

          <td>Visible lines plus the line containing the caret.</td>
        </tr>

        <tr>
          <td align="left"><code>SC_CACHE_DOCUMENT</code></td>

          <td align="center">3</td>

          <td>All lines in the document.</td>
        </tr>
      </tbody>
    </table>

    <p><b id="SCI_SETPOSITIONCACHE">SCI_SETPOSITIONCACHE(int size)</b><br />
     <b id="SCI_GETPOSITIONCACHE">SCI_GETPOSITIONCACHE &rarr; int</b><br />
     The position cache stores position information for short runs of text
     so that their layout can be determined more quickly if the run recurs.
     The size in entries of this cache can be set with <code>SCI_SETPOSITIONCACHE</code>.</p>

    <p><b id="SCI_LINESSPLIT">SCI_LINESSPLIT(int pixelWidth)</b><br />
     Split a range of lines indicated by the target into lines that are at most pixelWidth wide.
     Splitting occurs on word boundaries wherever possible in a similar manner to line wrapping.
     When <code class="parameter">pixelWidth</code> is 0 then the width of the window is used.
     </p>

    <p><b id="SCI_LINESJOIN">SCI_LINESJOIN</b><br />
     Join a range of lines indicated by the target into one line by
     removing line end characters.
     Where this would lead to no space between words, an extra space is inserted.
     </p>

    <p><b id="SCI_WRAPCOUNT">SCI_WRAPCOUNT(int docLine) &rarr; int</b><br />
    Document lines can occupy more than one display line if they wrap and this
    returns the number of display lines needed to wrap a document line.</p>

    <h2 id="Zooming">Zooming</h2>

    <p>Scintilla incorporates a "zoom factor" that lets you make all the text in the document
    larger or smaller in steps of one point. The displayed point size never goes below 2, whatever
    zoom factor you set. You can set zoom factors in the range -10 to +20 points.</p>
    <code><a class="message" href="#SCI_ZOOMIN">SCI_ZOOMIN</a><br />
     <a class="message" href="#SCI_ZOOMOUT">SCI_ZOOMOUT</a><br />
     <a class="message" href="#SCI_SETZOOM">SCI_SETZOOM(int zoomInPoints)</a><br />
     <a class="message" href="#SCI_GETZOOM">SCI_GETZOOM &rarr; int</a><br />
    </code>

    <p><b id="SCI_ZOOMIN">SCI_ZOOMIN</b><br />
     <b id="SCI_ZOOMOUT">SCI_ZOOMOUT</b><br />
     <code>SCI_ZOOMIN</code> increases the zoom factor by one point if the current zoom factor is
    less than 20 points. <code>SCI_ZOOMOUT</code> decreases the zoom factor by one point if the
    current zoom factor is greater than -10 points.</p>

    <p><b id="SCI_SETZOOM">SCI_SETZOOM(int zoomInPoints)</b><br />
     <b id="SCI_GETZOOM">SCI_GETZOOM &rarr; int</b><br />
     These messages let you set and get the zoom factor directly. There is no limit set on the
    factors you can set, so limiting yourself to -10 to +20 to match the incremental zoom functions
    is a good idea.</p>

    <h2 id="LongLines">Long lines</h2>

    <p>You can choose to mark lines that exceed a given length by drawing a vertical line or by
    colouring the background of characters that exceed the set length.</p>
    <code><a class="message" href="#SCI_SETEDGEMODE">SCI_SETEDGEMODE(int edgeMode)</a><br />
     <a class="message" href="#SCI_GETEDGEMODE">SCI_GETEDGEMODE &rarr; int</a><br />
     <a class="message" href="#SCI_SETEDGECOLUMN">SCI_SETEDGECOLUMN(int column)</a><br />
     <a class="message" href="#SCI_GETEDGECOLUMN">SCI_GETEDGECOLUMN &rarr; int</a><br />
     <a class="message" href="#SCI_SETEDGECOLOUR">SCI_SETEDGECOLOUR(colour edgeColour)</a><br />
     <a class="message" href="#SCI_GETEDGECOLOUR">SCI_GETEDGECOLOUR &rarr; colour</a><br />
     <br />
     <a class="message" href="#SCI_MULTIEDGEADDLINE">SCI_MULTIEDGEADDLINE(int column, colour edgeColour)</a><br />
     <a class="message" href="#SCI_MULTIEDGECLEARALL">SCI_MULTIEDGECLEARALL</a><br />
    </code>

    <p><b id="SCI_SETEDGEMODE">SCI_SETEDGEMODE(int edgeMode)</b><br />
     <b id="SCI_GETEDGEMODE">SCI_GETEDGEMODE &rarr; int</b><br />
     These two messages set and get the mode used to display long lines. You can set one of the
    values in the table:</p>

    <table class="standard" summary="Long line styles">
      <tbody>
        <tr>
          <th align="left">Symbol</th>

          <th>Value</th>

          <th align="left">Long line display mode</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>EDGE_NONE</code></td>

          <td align="center">0</td>

          <td>Long lines are not marked. This is the default state.</td>
        </tr>

        <tr>
          <td align="left"><code>EDGE_LINE</code></td>

          <td align="center">1</td>

          <td>A vertical line is drawn at the column number set by <code>SCI_SETEDGECOLUMN</code>.
          This works well for monospaced fonts. The line is drawn at a position based on the width
          of a space character in <a class="message"
          href="#StyleDefinition"><code>STYLE_DEFAULT</code></a>, so it may not work very well if
          your styles use proportional fonts or if your style have varied font sizes or you use a
          mixture of bold, italic and normal text.</td>
        </tr>

        <tr>
          <td align="left"><code>EDGE_BACKGROUND</code></td>

          <td align="center">2</td>

          <td>The background colour of characters after the column limit is changed to the colour
          set by <code>SCI_SETEDGECOLOUR</code>. This is recommended for proportional fonts.</td>
        </tr>

        <tr>
          <td align="left"><code>EDGE_MULTILINE</code></td>

          <td align="center">3</td>

          <td>This is similar to <code>EDGE_LINE</code> but in contrary to showing only one single
          line a configurable set of vertical lines can be shown simultaneously. This <code>edgeMode
          </code> uses a completely independent dataset that can only be configured by using the
          <code>SCI_MULTIEDGE*</code> messages.</td>
        </tr>
      </tbody>
    </table>

    <p><b id="SCI_SETEDGECOLUMN">SCI_SETEDGECOLUMN(int column)</b><br />
     <b id="SCI_GETEDGECOLUMN">SCI_GETEDGECOLUMN &rarr; int</b><br />
     These messages set and get the column number at which to display the long line marker. When
    drawing lines, the column sets a position in units of the width of a space character in
    <code>STYLE_DEFAULT</code>. When setting the background colour, the column is a character count
    (allowing for tabs) into the line.</p>

    <p><b id="SCI_SETEDGECOLOUR">SCI_SETEDGECOLOUR(<a class="jump" href="#colour">colour</a> edgeColour)</b><br />
     <b id="SCI_GETEDGECOLOUR">SCI_GETEDGECOLOUR &rarr; colour</b><br />
     These messages set and get the colour of the marker used to show that a line has exceeded the
    length set by <code>SCI_SETEDGECOLUMN</code>.</p>

    <p><b id="SCI_MULTIEDGEADDLINE">SCI_MULTIEDGEADDLINE(int column,
    <a class="jump" href="#colour">colour</a> edgeColour)</b><br />
     <b id="SCI_MULTIEDGECLEARALL">SCI_MULTIEDGECLEARALL</b><br />
     <code>SCI_MULTIEDGEADDLINE</code> adds a new vertical edge to the view. The edge will be
    displayed at the given column number. The resulting edge position depends on the metric
    of a space character in <code>STYLE_DEFAULT</code>. All the edges can be cleared with
    <code>SCI_MULTIEDGECLEARALL</code>.</p>

    <h2 id="Accessibility">Accessibility</h2>

    <p>Scintilla supports some platform accessibility features.
    This support differs between platforms.
    On GTK+ and Cocoa the platform accessibility APIs are implemented sufficiently to
    make screen readers work.
    On Win32, the system caret is manipulated to help screen readers.
    </p>

    <code><a class="message" href="#SCI_SETACCESSIBILITY">SCI_SETACCESSIBILITY(int accessibility)</a><br />
     <a class="message" href="#SCI_GETACCESSIBILITY">SCI_GETACCESSIBILITY &rarr; int</a><br />
    </code>

    <p><b id="SCI_SETACCESSIBILITY">SCI_SETACCESSIBILITY(int accessibility)</b><br />
     <b id="SCI_GETACCESSIBILITY">SCI_GETACCESSIBILITY &rarr; int</b><br />
     These messages may enable or disable accessibility and report its current status.</p>

    <p>On most platforms, accessibility is either implemented or not implemented and this can be
    discovered with <code>SCI_GETACCESSIBILITY</code> with
    <code>SCI_SETACCESSIBILITY</code> performing no action.
    On GTK+, there are storage and performance costs to accessibility, so it can be disabled
    by calling <code>SCI_SETACCESSIBILITY</code>.
    </p>

    <table class="standard" summary="Accessibility status">
      <tbody>
        <tr>
          <th align="left">Symbol</th>

          <th>Value</th>

          <th align="left">Accessibility status</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>SC_ACCESSIBILITY_DISABLED</code></td>

          <td align="center">0</td>

          <td>Accessibility is disabled.</td>
        </tr>

        <tr>
          <td align="left"><code>SC_ACCESSIBILITY_ENABLED</code></td>

          <td align="center">1</td>

          <td>Accessibility is enabled.</td>
        </tr>
      </tbody>
    </table>

    <h2 id="Lexer">Lexer</h2>

    <p>If you define the symbol <code>SCI_LEXER</code> when building Scintilla, (this is sometimes
    called the SciLexer version of Scintilla), lexing support for a wide range of programming
    languages is included and the messages in this section are supported. If you want to set
    styling and fold points for an unsupported language you can either do this in the container or
    better still, write your own lexer following the pattern of one of the existing ones.</p>

    <p>Scintilla also supports external lexers. These are DLLs (on Windows) or .so modules (on GTK+/Linux) that export three
    functions: <code>GetLexerCount</code>, <code>GetLexerName</code>, and
    <code>GetLexerFactory</code>. See <code>externalLexer.cxx</code> for more.</p>
     <a class="message" href="#SCI_SETLEXER">SCI_SETLEXER(int lexer)</a><br />
     <a class="message" href="#SCI_GETLEXER">SCI_GETLEXER &rarr; int</a><br />
     <a class="message" href="#SCI_SETLEXERLANGUAGE">SCI_SETLEXERLANGUAGE(&lt;unused&gt;, const char
    *language)</a><br />
     <a class="message" href="#SCI_GETLEXERLANGUAGE">SCI_GETLEXERLANGUAGE(&lt;unused&gt;, char *language) &rarr; int</a><br />
     <a class="message" href="#SCI_LOADLEXERLIBRARY">SCI_LOADLEXERLIBRARY(&lt;unused&gt;, const char
    *path)</a><br />
     <a class="message" href="#SCI_COLOURISE">SCI_COLOURISE(int start, int end)</a><br />
     <a class="message" href="#SCI_CHANGELEXERSTATE">SCI_CHANGELEXERSTATE(int start, int end) &rarr; int</a><br />
     <a class="message" href="#SCI_PROPERTYNAMES">SCI_PROPERTYNAMES(&lt;unused&gt;, char *names) &rarr; int</a><br />
     <a class="message" href="#SCI_PROPERTYTYPE">SCI_PROPERTYTYPE(const char *name) &rarr; int</a><br />
     <a class="message" href="#SCI_DESCRIBEPROPERTY">SCI_DESCRIBEPROPERTY(const char *name, char *description) &rarr; int</a><br />
     <a class="message" href="#SCI_SETPROPERTY">SCI_SETPROPERTY(const char *key, const char *value)</a><br />
     <a class="message" href="#SCI_GETPROPERTY">SCI_GETPROPERTY(const char *key, char *value) &rarr; int</a><br />
     <a class="message" href="#SCI_GETPROPERTYEXPANDED">SCI_GETPROPERTYEXPANDED(const char *key, char *value) &rarr; int</a><br />
     <a class="message" href="#SCI_GETPROPERTYINT">SCI_GETPROPERTYINT(const char *key, int defaultValue) &rarr; int</a><br />
     <a class="message" href="#SCI_DESCRIBEKEYWORDSETS">SCI_DESCRIBEKEYWORDSETS(&lt;unused&gt;, char *descriptions) &rarr; int</a><br />
     <a class="message" href="#SCI_SETKEYWORDS">SCI_SETKEYWORDS(int keyWordSet, const char
    *keyWords)</a><br />

     <a class="message" href="#SCI_GETSUBSTYLEBASES">SCI_GETSUBSTYLEBASES(&lt;unused&gt;, char *styles) &rarr; int</a><br />
     <a class="message" href="#SCI_DISTANCETOSECONDARYSTYLES">SCI_DISTANCETOSECONDARYSTYLES &rarr; int</a><br />
     <a class="message" href="#SCI_ALLOCATESUBSTYLES">SCI_ALLOCATESUBSTYLES(int styleBase, int numberStyles) &rarr; int</a><br />
     <a class="message" href="#SCI_FREESUBSTYLES">SCI_FREESUBSTYLES</a><br />
     <a class="message" href="#SCI_GETSUBSTYLESSTART">SCI_GETSUBSTYLESSTART(int styleBase) &rarr; int</a><br />
     <a class="message" href="#SCI_GETSUBSTYLESLENGTH">SCI_GETSUBSTYLESLENGTH(int styleBase) &rarr; int</a><br />
     <a class="message" href="#SCI_GETSTYLEFROMSUBSTYLE">SCI_GETSTYLEFROMSUBSTYLE(int subStyle) &rarr; int</a><br />
     <a class="message" href="#SCI_GETPRIMARYSTYLEFROMSTYLE">SCI_GETPRIMARYSTYLEFROMSTYLE(int style) &rarr; int</a><br />
     <a class="message" href="#SCI_SETIDENTIFIERS">SCI_SETIDENTIFIERS(int style, const char *identifiers)</a><br />
     <a class="message" href="#SCI_PRIVATELEXERCALL">SCI_PRIVATELEXERCALL(int operation, int pointer) &rarr; int</a><br />
     <a class="message" href="#SCI_GETNAMEDSTYLES">SCI_GETNAMEDSTYLES &rarr; int</a><br />
     <a class="message" href="#SCI_NAMEOFSTYLE">SCI_NAMEOFSTYLE(int style, char *name) &rarr; int</a><br />
     <a class="message" href="#SCI_TAGSOFSTYLE">SCI_TAGSOFSTYLE(int style, char *tags) &rarr; int</a><br />
     <a class="message" href="#SCI_DESCRIPTIONOFSTYLE">SCI_DESCRIPTIONOFSTYLE(int style, char *description) &rarr; int</a><br />

    <p><b id="SCI_SETLEXER">SCI_SETLEXER(int lexer)</b><br />
     <b id="SCI_GETLEXER">SCI_GETLEXER &rarr; int</b><br />
     You can select the lexer to use with an integer code from the <code>SCLEX_*</code> enumeration
    in <code>Scintilla.h</code>. There are two codes in this sequence that do not use lexers:
    <code>SCLEX_NULL</code> to select no lexing action and <code>SCLEX_CONTAINER</code> which sends
    the <code><a class="message" href="#SCN_STYLENEEDED">SCN_STYLENEEDED</a></code> notification to
    the container whenever a range of text needs to be styled. You cannot use the
    <code>SCLEX_AUTOMATIC</code> value; this identifies additional external lexers that Scintilla
    assigns unused lexer numbers to.</p>

    <p><b id="SCI_SETLEXERLANGUAGE">SCI_SETLEXERLANGUAGE(&lt;unused&gt;, const char *language)</b><br />
    <b id="SCI_GETLEXERLANGUAGE">SCI_GETLEXERLANGUAGE(&lt;unused&gt;, char *language NUL-terminated) &rarr; int</b><br />
     <code>SCI_SETLEXERLANGUAGE</code> lets you select a lexer by name, and is the only method if you are using an
    external lexer or if you have written a lexer module for a language of your own and do not wish
    to assign it an explicit lexer number. To select an existing lexer, set <code class="parameter">language</code> to
    match the (case sensitive) name given to the module, for example "ada" or "python", not "Ada"
    or "Python". To locate the name for the built-in lexers, open the relevant
    <code>Lex*.cxx</code> file and search for <code>LexerModule</code>. The third argument in the
    <code>LexerModule</code> constructor is the name to use.</p>

    <p>To test if your lexer assignment worked, use <a class="message"
    href="#SCI_GETLEXER"><code>SCI_GETLEXER</code></a> before and after setting the new lexer to
    see if the lexer number changed.</p>

    <p><code>SCI_GETLEXERLANGUAGE</code> retrieves the name of the lexer.</p>

    <p><b id="SCI_LOADLEXERLIBRARY">SCI_LOADLEXERLIBRARY(&lt;unused&gt;, const char *path)</b><br />
     Load a lexer implemented in a shared library. This is a .so file on GTK+/Linux or a .DLL file on Windows.
     </p>

    <p><b id="SCI_COLOURISE">SCI_COLOURISE(int start, int end)</b><br />
     This requests the current lexer or the container (if the lexer is set to
    <code>SCLEX_CONTAINER</code>) to style the document between <code class="parameter">start</code> and
    <code class="parameter">end</code>. If <code class="parameter">end</code> is -1, the document is styled from
    <code class="parameter">start</code> to the end. If the <code>"fold"</code> property is set to
    <code>"1"</code> and your lexer or container supports folding, fold levels are also set. This
    message causes a redraw.</p>

    <p><b id="SCI_CHANGELEXERSTATE">SCI_CHANGELEXERSTATE(int start, int end) &rarr; int</b><br />
    Indicate that the internal state of a lexer has changed over a range and therefore
    there may be a need to redraw.</p>

    <p><b id="SCI_PROPERTYNAMES">SCI_PROPERTYNAMES(&lt;unused&gt;, char *names NUL-terminated) &rarr; int</b><br />
    <b id="SCI_PROPERTYTYPE">SCI_PROPERTYTYPE(const char *name) &rarr; int</b><br />
    <b id="SCI_DESCRIBEPROPERTY">SCI_DESCRIBEPROPERTY(const char *name, char *description NUL-terminated) &rarr; int</b><br />
    Information may be retrieved about the properties that can be set for the current lexer.
    This information is only available for newer lexers.
    <code>SCI_PROPERTYNAMES</code> returns a string with all of the valid properties separated by "\n".
    If the lexer does not support this call then an empty string is returned.
    Properties may be boolean (<code>SC_TYPE_BOOLEAN</code>), integer (<code>SC_TYPE_INTEGER</code>),
    or string (<code>SC_TYPE_STRING</code>) and this is found with <code>SCI_PROPERTYTYPE</code>.
    A description of a property in English is returned by <code>SCI_DESCRIBEPROPERTY</code>.</p>

    <p><b id="SCI_SETPROPERTY">SCI_SETPROPERTY(const char *key, const char *value)</b><br />
     You can communicate settings to lexers with keyword:value string pairs. There is no limit to
    the number of keyword pairs you can set, other than available memory. <code class="parameter">key</code> is a
    case sensitive keyword, <code class="parameter">value</code> is a string that is associated with the keyword. If
    there is already a value string associated with the keyword, it is replaced. If you pass a zero
    length string, the message does nothing. Both <code class="parameter">key</code> and <code class="parameter">value</code> are used
    without modification; extra spaces at the beginning or end of <code class="parameter">key</code> are
    significant.</p>

    <p>The <code class="parameter">value</code> string can refer to other keywords. For example,
    <code>SCI_SETPROPERTY("foldTimes10", "$(fold)0")</code> stores the string
    <code>"$(fold)0"</code>, but when this is accessed, the <code>$(fold)</code> is replaced by the
    value of the <code>"fold"</code> keyword (or by nothing if this keyword does not exist).</p>

    <p>Currently the "fold" property is defined for most of the lexers to set the fold structure if
    set to "1". <code>SCLEX_PYTHON</code> understands <code>"tab.timmy.whinge.level"</code> as a
    setting that determines how to indicate bad indentation. Most keywords have values that are
    interpreted as integers. Search the lexer sources for <code>GetPropertyInt</code> to see how
    properties are used.</p>

    <p>There is a convention for naming properties used by lexers so that the set of properties can be found by scripts.
    Property names should start with "lexer.&lt;lexer&gt;." or "fold.&lt;lexer&gt;." when they apply to one
    lexer or start with "lexer." or "fold." if they apply to multiple lexers.</p>

    <p>Applications may discover the set of properties used by searching the source code of lexers for lines that contain
    <code>GetProperty</code> and a double quoted string and extract the value of the double quoted string as the property name.
    The <code>scintilla/scripts/LexGen.py</code> script does this and can be used as an example.
    Documentation for the property may be located above the call as a multi-line comment starting with
    <br/><code>// property &lt;property-name&gt;</code></p>

    <p><b id="SCI_GETPROPERTY">SCI_GETPROPERTY(const char *key, char *value NUL-terminated) &rarr; int</b><br />
    Lookup a keyword:value pair using the specified key; if found, copy the value to the user-supplied
    buffer and return the length (not including the terminating 0).  If not found, copy an empty string
    to the buffer and return 0.</p>

    <p>Note that  "keyword replacement" as described in <a class="message" href="#SCI_SETPROPERTY">
    <code>SCI_SETPROPERTY</code></a> will not be performed.</p>

    <p>If the value argument is 0 then the length that should be allocated to store the value is returned;
    again, the terminating 0 is not included.</p>

    <p><b id="SCI_GETPROPERTYEXPANDED">SCI_GETPROPERTYEXPANDED(const char *key, char *value) &rarr; int</b><br />
    Lookup a keyword:value pair using the specified key; if found, copy the value to the user-supplied
    buffer and return the length (not including the terminating 0).  If not found, copy an empty string
    to the buffer and return 0.</p>

    <p>Note that  "keyword replacement" as described in <a class="message" href="#SCI_SETPROPERTY">
    <code>SCI_SETPROPERTY</code></a> will be performed.</p>

    <p>If the value argument is 0 then the length that should be allocated to store the value (including any indicated keyword replacement)
    is returned; again, the terminating 0 is not included.</p>

    <p><b id="SCI_GETPROPERTYINT">SCI_GETPROPERTYINT(const char *key, int defaultValue) &rarr; int</b><br />
    Lookup a keyword:value pair using the specified key; if found, interpret the value as an integer and return it.
    If not found (or the value is an empty string) then return the supplied default.  If the keyword:value pair is found but is not
    a number, then return 0.</p>

    <p>Note that  "keyword replacement" as described in <a class="message" href="#SCI_SETPROPERTY">
    <code>SCI_SETPROPERTY</code></a> will be performed before any numeric interpretation.</p>

    <p><b id="SCI_SETKEYWORDS">SCI_SETKEYWORDS(int keyWordSet, const char *keyWords)</b><br />
     You can set up to 9 lists of keywords for use by the current lexer.
     <code class="parameter">keyWordSet</code> can be 0 to 8 (actually 0 to <code>KEYWORDSET_MAX</code>)
    and selects which keyword list to replace. <code class="parameter">keyWords</code> is a list of keywords
    separated by spaces, tabs, <code>"\n"</code> or <code>"\r"</code> or any combination of these.
    It is expected that the keywords will be composed of standard ASCII printing characters,
    but there is nothing to stop you using any non-separator character codes from 1 to 255
    (except common sense).</p>

    <p>How these keywords are used is entirely up to the lexer. Some languages, such as HTML may
    contain embedded languages, VBScript and JavaScript are common for HTML. For HTML, key word set
    0 is for HTML, 1 is for JavaScript and 2 is for VBScript, 3 is for Python, 4 is for PHP and 5
    is for SGML and DTD keywords. Review the lexer code to see examples of keyword list. A fully
    conforming lexer sets the fourth argument of the <code>LexerModule</code> constructor to be a
    list of strings that describe the uses of the keyword lists.</p>

    <p>Alternatively, you might use set 0 for general keywords, set 1 for keywords that cause
    indentation and set 2 for keywords that cause unindentation. Yet again, you might have a simple
    lexer that colours keywords and you could change languages by changing the keywords in set 0.
    There is nothing to stop you building your own keyword lists into the lexer, but this means
    that the lexer must be rebuilt if more keywords are added.</p>

    <p><b id="SCI_DESCRIBEKEYWORDSETS">SCI_DESCRIBEKEYWORDSETS(&lt;unused&gt;, char *descriptions NUL-terminated) &rarr; int</b><br />
    A description of all of the keyword sets separated by "\n" is returned by <code>SCI_DESCRIBEKEYWORDSETS</code>.</p>

    <h3 id="Substyles">Substyles</h3>
    <p>Lexers may support several different sublanguages and each sublanguage may want to style some number of
    sets of identifiers (or similar lexemes such as documentation keywords) uniquely. Preallocating a large number for each
    purpose would exhaust the number of allowed styles quickly.
    This is alleviated by substyles which allow the application to determine how many sets of identifiers to allocate for
    each purpose.
    Lexers have to explicitly support this feature by implementing the methods in <code>ILexerWithSubStyles</code>.</p>

    <p><b id="SCI_GETSUBSTYLEBASES">SCI_GETSUBSTYLEBASES(&lt;unused&gt;, char *styles NUL-terminated) &rarr; int</b><br />
     Fill <code class="parameter">styles</code> with a byte for each style that can be split into substyles.</p>

    <p><b id="SCI_DISTANCETOSECONDARYSTYLES">SCI_DISTANCETOSECONDARYSTYLES &rarr; int</b><br />
     Returns the distance between a primary style and its corresponding secondary style.</p>

    <p><b id="SCI_ALLOCATESUBSTYLES">SCI_ALLOCATESUBSTYLES(int styleBase, int numberStyles) &rarr; int</b><br />
     Allocate some number of substyles for a particular base style returning the first substyle number allocated.
     Substyles are allocated contiguously.</p>

    <p><b id="SCI_FREESUBSTYLES">SCI_FREESUBSTYLES</b><br />
     Free all allocated substyles.</p>

    <p><b id="SCI_GETSUBSTYLESSTART">SCI_GETSUBSTYLESSTART(int styleBase) &rarr; int</b><br />
    <b id="SCI_GETSUBSTYLESLENGTH">SCI_GETSUBSTYLESLENGTH(int styleBase) &rarr; int</b><br />
     Return the start and length of the substyles allocated for a base style.</p>

    <p><b id="SCI_GETSTYLEFROMSUBSTYLE">SCI_GETSTYLEFROMSUBSTYLE(int subStyle) &rarr; int</b><br />
     For a sub style, return the base style, else return the argument.</p>

    <p><b id="SCI_GETPRIMARYSTYLEFROMSTYLE">SCI_GETPRIMARYSTYLEFROMSTYLE(int style) &rarr; int</b><br />
     For a secondary style, return the primary style, else return the argument.</p>

    <p><b id="SCI_SETIDENTIFIERS">SCI_SETIDENTIFIERS(int style, const char *identifiers)</b><br />
     Similar to <code>SCI_SETKEYWORDS</code> but for substyles.
     The prefix feature available with <code>SCI_SETKEYWORDS</code> is not implemented for <code>SCI_SETIDENTIFIERS</code>.</p>

    <p><b id="SCI_PRIVATELEXERCALL">SCI_PRIVATELEXERCALL(int operation, int pointer) &rarr; int</b><br />
     Call into a lexer in a way not understood by Scintilla.</p>

    <h3 id="StyleMetadata">Style Metadata</h3>
    <p>Lexers may provide information on the styles they use.
    Lexers have to explicitly support this feature by implementing particular methods.</p>

    <p><b id="SCI_GETNAMEDSTYLES">SCI_GETNAMEDSTYLES &rarr; int</b><br />
     Retrieve the number of named styles for the lexer.</p>

    <p><b id="SCI_NAMEOFSTYLE">SCI_NAMEOFSTYLE(int style, char *name) &rarr; int</b><br />
     Retrieve the name of a style. This is a C preprocessor symbol like "SCE_C_COMMENTDOC".</p>

    <p><b id="SCI_TAGSOFSTYLE">SCI_TAGSOFSTYLE(int style, char *tags) &rarr; int</b><br />
     Retrieve the tags of a style. This is a space-separated set of words like "comment documentation".</p>

    <p><b id="SCI_DESCRIPTIONOFSTYLE">SCI_DESCRIPTIONOFSTYLE(int style, char *description) &rarr; int</b><br />
     Retrieve an English-language description of a style which may be suitable for display in a user interface.
     This looks like "Doc comment: block comments beginning with /** or /*!".</p>

    <h2 id="LexerObjects">Lexer Objects</h2>

    <p>Lexers are programmed as objects that implement the ILexer interface and that interact
    with the document they are lexing through the IDocument interface.
    Previously lexers were defined by providing lexing and folding functions but creating an object
    to handle the interaction of a lexer with a document allows the lexer to store state information that
    can be used during lexing. For example a C++ lexer may store a set of preprocessor definitions
    or variable declarations and style these depending on their role.</p>

    <p>A set of helper classes allows older lexers defined by functions to be used in Scintilla.</p>
<h4>ILexer</h4>

<div class="highlighted">
<span class="S5">class</span><span class="S0"> </span>ILexer<span class="S0"> </span><span class="S10">{</span><br />
<span class="S5">public</span><span class="S10">:</span><br />
<span class="S0">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="S5">virtual</span>
<span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD
<span class="S0"> </span>Version<span class="S10">()</span><span class="S0"> </span>
<span class="S5">const</span><span class="S0"> </span><span class="S10">=</span>
<span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="S5">virtual</span>
<span class="S0"> </span><span class="S5">void</span><span class="S0"> </span>SCI_METHOD
<span class="S0"> </span>Release<span class="S10">()</span><span class="S0"> </span>
<span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="S5">virtual</span><span class="S0"> </span>
<span class="S5">const</span><span class="S0"> </span>
<span class="S5">char</span><span class="S0"> </span>
<span class="S10">*</span><span class="S0"> </span>
SCI_METHOD<span class="S0"> </span>PropertyNames<span class="S10">()</span>
<span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="S5">virtual</span>
<span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>PropertyType<span class="S10">(</span><span class="S5">const</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span><span class="S10">*</span>name<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="S5">virtual</span>
<span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span><span class="S10">*</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>DescribeProperty<span class="S10">(</span><span class="S5">const</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span><span class="S10">*</span>name<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="S5">virtual</span>
<span class="S0"> </span>Sci_Position<span class="S0"> </span>SCI_METHOD
<span class="S0"> </span>PropertySet<span class="S10">(</span><span class="S5">const</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span><span class="S10">*</span>key<span class="S10">,</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span><span class="S10">*</span>val<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="S5">virtual</span>
<span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span><span class="S10">*</span><span class="S0"> </span>SCI_METHOD
<span class="S0"> </span>DescribeWordListSets<span class="S10">()</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="S5">virtual</span>
<span class="S0"> </span>Sci_Position<span class="S0"> </span>SCI_METHOD
<span class="S0"> </span>WordListSet<span class="S10">(</span><span class="S5">int</span><span class="S0"> </span>n<span class="S10">,</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span><span class="S10">*</span>wl<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="S5">virtual</span>
<span class="S0"> </span><span class="S5">void</span><span class="S0"> </span>SCI_METHOD
<span class="S0"> </span>Lex<span class="S10">(</span>Sci_PositionU<span class="S0"> </span>startPos<span class="S10">,</span><span class="S0"> </span>Sci_Position<span class="S0"> </span>lengthDoc<span class="S10">,</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>initStyle<span class="S10">,</span><span class="S0"> </span>IDocument<span class="S0"> </span><span class="S10">*</span>pAccess<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="S5">virtual</span>
<span class="S0"> </span><span class="S5">void</span><span class="S0"> </span>SCI_METHOD
<span class="S0"> </span>Fold<span class="S10">(</span>Sci_PositionU<span class="S0"> </span>startPos<span class="S10">,</span><span class="S0"> </span>Sci_Position<span class="S0"> </span>lengthDoc<span class="S10">,</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>initStyle<span class="S10">,</span><span class="S0"> </span>IDocument<span class="S0"> </span><span class="S10">*</span>pAccess<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="S5">virtual</span>
<span class="S0"> </span><span class="S5">void</span><span class="S0"> </span><span class="S10">*</span><span class="S0"> </span>SCI_METHOD
<span class="S0"> </span>PrivateCall<span class="S10">(</span><span class="S5">int</span><span class="S0"> </span>operation<span class="S10">,</span><span class="S0"> </span><span class="S5">void</span><span class="S0"> </span><span class="S10">*</span>pointer<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S10">};</span><br />
</div>

<p>
The types <code>Sci_Position</code> and <code>Sci_PositionU</code> are used for positions and line numbers in the document.
Before release 3.6.0 the types <code>int</code> and <code>unsigned int</code> were used instead and, for 3.6.0,
<code>Sci_Position</code> is defined as <code>int</code> and <code>Sci_PositionU</code> is defined as <code>unsigned int</code>.
In a future release, 64-bit builds will define these as 64-bit types to allow documents larger than 2 GB.
</p>

<p>
Methods that return strings as <code>const char *</code> are not required to maintain separate allocations indefinitely:
lexer implementations may own a single buffer that is reused for each call.
Callers should make an immediate copy of returned strings.
</p>

<p>
The return values from PropertySet and WordListSet are used to indicate whether the change requires
performing lexing or folding over any of the document. It is the position at which to restart lexing and folding or -1
if the change does not require any extra work on the document.
A simple approach is to return 0 if there is any possibility that a change requires lexing the document again while an
optimisation could be to remember where a setting first affects the document and return that position.
</p>

<p><code>Version</code> returns an enumerated value specifying which version of the interface is implemented:
<code>lvOriginal</code> for <code>ILexer</code>, <code>lvSubStyles</code> for <code>ILexerWithSubStyles</code>,
and <code>lvMetaData</code> for <code>ILexerWithMetaData</code>.</p>

<p><code>Release</code> is called to destroy the lexer object.</p>

<p><code>PrivateCall</code> allows for direct communication between the
application and a lexer. An example would be where an application
maintains a single large data structure containing symbolic information
about system headers (like Windows.h) and provides this to the lexer
where it can be applied to each document. This avoids the costs of
constructing the system header information for each document. This is
invoked with the <code>SCI_PRIVATELEXERCALL</code> API.</p>

<p><code>Fold</code> is called with the exact range that needs folding.
Previously, lexers were called with a range that started one line before the range that
needs to be folded as this allowed fixing up the last line from the previous folding.
The new approach allows the lexer to decide whether to backtrack or to handle this
more efficiently.</p>

<h4>ILexerWithSubStyles</h4>

<p>
To allow lexers to report which line ends they support, and to support substyles,
<code>Ilexer</code> is extended to <code>ILexerWithSubStyles</code>.
</p>

<div class="highlighted">
<span class="S5">class</span><span class="S0"> </span>ILexerWithSubStyles<span class="S0"> </span><span class="S10">:</span><span class="S0"> </span><span class="S5">public</span><span class="S0"> </span>ILexer<span class="S0"> </span><span class="S10">{</span><br />
<span class="S5">public</span><span class="S10">:</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>LineEndTypesSupported<span class="S10">()</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>AllocateSubStyles<span class="S10">(</span><span class="S5">int</span><span class="S0"> </span>styleBase<span class="S10">,</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>numberStyles<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>SubStylesStart<span class="S10">(</span><span class="S5">int</span><span class="S0"> </span>styleBase<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>SubStylesLength<span class="S10">(</span><span class="S5">int</span><span class="S0"> </span>styleBase<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>StyleFromSubStyle<span class="S10">(</span><span class="S5">int</span><span class="S0"> </span>subStyle<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>PrimaryStyleFromStyle<span class="S10">(</span><span class="S5">int</span><span class="S0"> </span>style<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">void</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>FreeSubStyles<span class="S10">()</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">void</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>SetIdentifiers<span class="S10">(</span><span class="S5">int</span><span class="S0"> </span>style<span class="S10">,</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span><span class="S10">*</span>identifiers<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>DistanceToSecondaryStyles<span class="S10">()</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span><span class="S10">*</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>GetSubStyleBases<span class="S10">()</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S10">};</span><br />
</div>

<h4>ILexerWithMetaData</h4>

<p><code>NamedStyles</code>, <code>NameOfStyle</code>,
<code>TagsOfStyle</code>, and <code>DescriptionOfStyle</code>
are used to provide information on the set of styles used by this lexer.
<code>NameOfStyle</code> is the C-language identifier like "<code>SCE_LUA_COMMENT</code>".
<code>TagsOfStyle</code> is a set of tags describing the style in a standardized way like "<code>literal string multiline raw</code>".
A set of common tags and conventions for combining them is <a  class="jump" href="StyleMetadata.html">described here</a>.
<code>DescriptionOfStyle</code> is an English description of the style like "<code>Function or method name definition</code>".
</p>

<div class="highlighted">
<span class="S5">class</span><span class="S0"> </span>ILexerWithMetaData<span class="S0"> </span><span class="S10">:</span><span class="S0"> </span><span class="S5">public</span><span class="S0"> </span>ILexerWithSubStyles<span class="S0"> </span><span class="S10">{</span><br />
<span class="S5">public</span><span class="S10">:</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>NamedStyles<span class="S10">()</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span><span class="S10">*</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>NameOfStyle<span class="S10">(</span><span class="S5">int</span><span class="S0"> </span>style<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span><span class="S10">*</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>TagsOfStyle<span class="S10">(</span><span class="S5">int</span><span class="S0"> </span>style<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span><span class="S10">*</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>DescriptionOfStyle<span class="S10">(</span><span class="S5">int</span><span class="S0"> </span>style<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S10">};</span><br />
</div>

<h4>IDocument</h4>

<div class="highlighted">
<span class="S5">class</span><span class="S0"> </span>IDocument<span class="S0"> </span><span class="S10">{</span><br />
<span class="S5">public</span><span class="S10">:</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>Version<span class="S10">()</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">void</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>SetErrorStatus<span class="S10">(</span><span class="S5">int</span><span class="S0"> </span>status<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span>Sci_Position<span class="S0"> </span>SCI_METHOD<span class="S0"> </span>Length<span class="S10">()</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">void</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>GetCharRange<span class="S10">(</span><span class="S5">char</span><span class="S0"> </span><span class="S10">*</span>buffer<span class="S10">,</span><span class="S0"> </span>Sci_Position<span class="S0"> </span>position<span class="S10">,</span><span class="S0"> </span>Sci_Position<span class="S0"> </span>lengthRetrieve<span class="S10">)</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>StyleAt<span class="S10">(</span>Sci_Position<span class="S0"> </span>position<span class="S10">)</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span>Sci_Position<span class="S0"> </span>SCI_METHOD<span class="S0"> </span>LineFromPosition<span class="S10">(</span>Sci_Position<span class="S0"> </span>position<span class="S10">)</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span>Sci_Position<span class="S0"> </span>SCI_METHOD<span class="S0"> </span>LineStart<span class="S10">(</span>Sci_Position<span class="S0"> </span>line<span class="S10">)</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>GetLevel<span class="S10">(</span>Sci_Position<span class="S0"> </span>line<span class="S10">)</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>SetLevel<span class="S10">(</span>Sci_Position<span class="S0"> </span>line<span class="S10">,</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>level<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>GetLineState<span class="S10">(</span>Sci_Position<span class="S0"> </span>line<span class="S10">)</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>SetLineState<span class="S10">(</span>Sci_Position<span class="S0"> </span>line<span class="S10">,</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>state<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">void</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>StartStyling<span class="S10">(</span>Sci_Position<span class="S0"> </span>position<span class="S10">,</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span>mask<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">bool</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>SetStyleFor<span class="S10">(</span>Sci_Position<span class="S0"> </span>length<span class="S10">,</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span>style<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">bool</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>SetStyles<span class="S10">(</span>Sci_Position<span class="S0"> </span>length<span class="S10">,</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span><span class="S10">*</span>styles<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">void</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>DecorationSetCurrentIndicator<span class="S10">(</span><span class="S5">int</span><span class="S0"> </span>indicator<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">void</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>DecorationFillRange<span class="S10">(</span>Sci_Position<span class="S0"> </span>position<span class="S10">,</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>value<span class="S10">,</span><span class="S0"> </span>Sci_Position<span class="S0"> </span>fillLength<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">void</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>ChangeLexerState<span class="S10">(</span>Sci_Position<span class="S0"> </span>start<span class="S10">,</span><span class="S0"> </span>Sci_Position<span class="S0"> </span>end<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>CodePage<span class="S10">()</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">bool</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>IsDBCSLeadByte<span class="S10">(</span><span class="S5">char</span><span class="S0"> </span>ch<span class="S10">)</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S5">char</span><span class="S0"> </span><span class="S10">*</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>BufferPointer<span class="S10">()</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>GetLineIndentation<span class="S10">(</span>Sci_Position<span class="S0"> </span>line<span class="S10">)</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S10">};</span><br />
</div>

<p>Scintilla tries to minimize the consequences of modifying text to
only relex and redraw the line of the change where possible. Lexer
objects contain their own private extra state which can affect later
lines. For example, if the C++ lexer is greying out inactive code
segments then changing the statement <code>#define BEOS 0</code> to <code>#define
 BEOS 1</code> may require restyling and redisplaying later parts of the
 document. The lexer can call <code>ChangeLexerState</code> to signal to
 the document that it should relex and display more.</p>

<p>For <code>StartStyling</code> the mask argument has no effect. It was used in version 3.4.2 and earlier.</p>

<p><code>SetErrorStatus</code> is used to notify the document of
exceptions. Exceptions should not be thrown over build boundaries as the
 two sides may be built with different compilers or incompatible
exception options.</p>

<h4>IDocumentWithLineEnd</h4>

<p>
To allow lexers to determine the end position of a line and thus more easily support Unicode line ends
<code>IDocument</code> is extended to <code>IDocumentWithLineEnd</code>.</p>
<p><code>GetRelativePosition</code> navigates the document by whole characters,
returning <code>INVALID_POSITION</code> for movement beyond the start and end of the document.</p>
<p><code>GetCharacterAndWidth</code> provides a standard
conversion from UTF-8 bytes to a UTF-32 character or from DBCS to a 16 bit value.
Bytes in invalid UTF-8 are reported individually with values 0xDC80+byteValue, which are
not valid Unicode code points.
The <code class="parameter">pWidth</code> argument can be NULL if the caller does not need to know the number of
bytes in the character.
</p>

<div class="highlighted">
<span class="S5">class</span><span class="S0"> </span>IDocumentWithLineEnd<span class="S0"> </span><span class="S10">:</span><span class="S0"> </span><span class="S5">public</span><span class="S0"> </span>IDocument<span class="S0"> </span><span class="S10">{</span><br />
<span class="S5">public</span><span class="S10">:</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span>Sci_Position<span class="S0"> </span>SCI_METHOD<span class="S0"> </span>LineEnd<span class="S10">(</span>Sci_Position<span class="S0"> </span>line<span class="S10">)</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span>Sci_Position<span class="S0"> </span>SCI_METHOD<span class="S0"> </span>GetRelativePosition<span class="S10">(</span>Sci_Position<span class="S0"> </span>positionStart<span class="S10">,</span><span class="S0"> </span>Sci_Position<span class="S0"> </span>characterOffset<span class="S10">)</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S0">&nbsp; &nbsp; &nbsp; &nbsp; </span><span class="S5">virtual</span><span class="S0"> </span><span class="S5">int</span><span class="S0"> </span>SCI_METHOD<span class="S0"> </span>GetCharacterAndWidth<span class="S10">(</span>Sci_Position<span class="S0"> </span>position<span class="S10">,</span><span class="S0"> </span>Sci_Position<span class="S0"> </span><span class="S10">*</span>pWidth<span class="S10">)</span><span class="S0"> </span><span class="S5">const</span><span class="S0"> </span><span class="S10">=</span><span class="S0"> </span><span class="S4">0</span><span class="S10">;</span><br />
<span class="S10">};</span><br />
</div>

<p>The <code>ILexer</code>, <code>ILexerWithSubStyles</code>, <code>IDocument</code>, and
<code>IDocumentWithLineEnd</code>  interfaces may be
expanded in the future with extended versions (<code>ILexer2</code>...).
 The <code>Version</code> method indicates which interface is
implemented and thus which methods may be called.</p>

    <h2 id="Notifications">Notifications</h2>

    <p>Notifications are sent (fired) from the Scintilla control to its container when an event has
    occurred that may interest the container.</p>
    <p>Notifications are sent using the
    <code>WM_NOTIFY</code> message on Windows.</p>
    <p>On GTK+, the "sci-notify" signal is sent and the signal handler should have the signature
    <code>handler(GtkWidget *, gint, SCNotification *notification, gpointer  userData)</code>.</p>
    <p>On Cocoa, a delegate implementing the <code>ScintillaNotificationProtocol</code>
    may be set to receive notifications or the <code>ScintillaView</code> class may be subclassed and the
    <code>notification:</code> method overridden. Overriding <code>notification:</code> allows the
    subclass to control whether default handling is performed.</p>
    <p>The container is
    passed a <code>SCNotification</code> structure containing information about the event.</p>
<pre id="SCNotification">
struct Sci_NotifyHeader {   // This matches the Win32 NMHDR structure
    void *hwndFrom;     // environment specific window handle/pointer
    uptr_t idFrom;        // CtrlID of the window issuing the notification
    unsigned int code;  // The SCN_* notification code
};

struct SCNotification {
	struct Sci_NotifyHeader nmhdr;
	Sci_Position position;
	/* SCN_STYLENEEDED, SCN_DOUBLECLICK, SCN_MODIFIED, SCN_MARGINCLICK, */
	/* SCN_MARGINRIGHTCLICK, SCN_NEEDSHOWN, SCN_DWELLSTART, SCN_DWELLEND, */
	/* SCN_CALLTIPCLICK, SCN_HOTSPOTCLICK, SCN_HOTSPOTDOUBLECLICK, */
	/* SCN_HOTSPOTRELEASECLICK, SCN_INDICATORCLICK, SCN_INDICATORRELEASE, */
	/* SCN_USERLISTSELECTION, SCN_AUTOCSELECTION, SCN_AUTOCSELECTIONCHANGE */

	int ch;
	/* SCN_CHARADDED, SCN_KEY, SCN_AUTOCCOMPLETE, SCN_AUTOCSELECTION, */
	/* SCN_USERLISTSELECTION */
	int modifiers;
	/* SCN_KEY, SCN_DOUBLECLICK, SCN_HOTSPOTCLICK, SCN_HOTSPOTDOUBLECLICK, */
	/* SCN_HOTSPOTRELEASECLICK, SCN_INDICATORCLICK, SCN_INDICATORRELEASE, */

	int modificationType;	/* SCN_MODIFIED */
	const char *text;
	/* SCN_MODIFIED, SCN_USERLISTSELECTION, SCN_AUTOCSELECTION, SCN_URIDROPPED, */
	/* SCN_AUTOCSELECTIONCHANGE */

	Sci_Position length;		/* SCN_MODIFIED */
	Sci_Position linesAdded;	/* SCN_MODIFIED */
	int message;	/* SCN_MACRORECORD */
	uptr_t wParam;	/* SCN_MACRORECORD */
	sptr_t lParam;	/* SCN_MACRORECORD */
	Sci_Position line;		/* SCN_MODIFIED */
	int foldLevelNow;	/* SCN_MODIFIED */
	int foldLevelPrev;	/* SCN_MODIFIED */
	int margin;		/* SCN_MARGINCLICK, SCN_MARGINRIGHTCLICK */
	int listType;	/* SCN_USERLISTSELECTION, SCN_AUTOCSELECTIONCHANGE */
	int x;			/* SCN_DWELLSTART, SCN_DWELLEND */
	int y;		/* SCN_DWELLSTART, SCN_DWELLEND */
	int token;		/* SCN_MODIFIED with SC_MOD_CONTAINER */
	int annotationLinesAdded;	/* SCN_MODIFIED with SC_MOD_CHANGEANNOTATION */
	int updated;	/* SCN_UPDATEUI */
	int listCompletionMethod;
	/* SCN_AUTOCSELECTION, SCN_AUTOCCOMPLETED, SCN_USERLISTSELECTION */

};
</pre>

    <p>The notification messages that your container can choose to handle and the messages
    associated with them are:</p>
    <code><a class="message" href="#SCN_STYLENEEDED">SCN_STYLENEEDED</a><br />
     <a class="message" href="#SCN_CHARADDED">SCN_CHARADDED</a><br />
     <a class="message" href="#SCN_SAVEPOINTREACHED">SCN_SAVEPOINTREACHED</a><br />
     <a class="message" href="#SCN_SAVEPOINTLEFT">SCN_SAVEPOINTLEFT</a><br />
     <a class="message" href="#SCN_MODIFYATTEMPTRO">SCN_MODIFYATTEMPTRO</a><br />
     <a class="message" href="#SCN_KEY">SCN_KEY</a><br />
     <a class="message" href="#SCN_DOUBLECLICK">SCN_DOUBLECLICK</a><br />
     <a class="message" href="#SCN_UPDATEUI">SCN_UPDATEUI</a><br />
     <a class="message" href="#SCN_MODIFIED">SCN_MODIFIED</a><br />
     <a class="message" href="#SCN_MACRORECORD">SCN_MACRORECORD</a><br />
     <a class="message" href="#SCN_MARGINCLICK">SCN_MARGINCLICK</a><br />
     <a class="message" href="#SCN_NEEDSHOWN">SCN_NEEDSHOWN</a><br />
     <a class="message" href="#SCN_PAINTED">SCN_PAINTED</a><br />
     <a class="message" href="#SCN_USERLISTSELECTION">SCN_USERLISTSELECTION</a><br />
     <a class="message" href="#SCN_URIDROPPED">SCN_URIDROPPED</a><br />
     <a class="message" href="#SCN_DWELLSTART">SCN_DWELLSTART</a><br />
     <a class="message" href="#SCN_DWELLEND">SCN_DWELLEND</a><br />
     <a class="message" href="#SCN_ZOOM">SCN_ZOOM</a><br />
     <a class="message" href="#SCN_HOTSPOTCLICK">SCN_HOTSPOTCLICK</a><br />
     <a class="message" href="#SCN_HOTSPOTDOUBLECLICK">SCN_HOTSPOTDOUBLECLICK</a><br />
     <a class="message" href="#SCN_HOTSPOTRELEASECLICK">SCN_HOTSPOTRELEASECLICK</a><br />
     <a class="message" href="#SCN_INDICATORCLICK">SCN_INDICATORCLICK</a><br />
     <a class="message" href="#SCN_INDICATORRELEASE">SCN_INDICATORRELEASE</a><br />
     <a class="message" href="#SCN_CALLTIPCLICK">SCN_CALLTIPCLICK</a><br />
     <a class="message" href="#SCN_AUTOCSELECTION">SCN_AUTOCSELECTION</a><br />
     <a class="message" href="#SCN_AUTOCCANCELLED">SCN_AUTOCCANCELLED</a><br />
     <a class="message" href="#SCN_AUTOCCHARDELETED">SCN_AUTOCCHARDELETED</a><br />
     <a class="message" href="#SCN_FOCUSIN">SCN_FOCUSIN</a><br />
     <a class="message" href="#SCN_FOCUSOUT">SCN_FOCUSOUT</a><br />
     <a class="message" href="#SCN_AUTOCCOMPLETED">SCN_AUTOCCOMPLETED</a><br />
     <a class="message" href="#SCN_MARGINRIGHTCLICK">SCN_MARGINRIGHTCLICK</a><br />
     <a class="message" href="#SCN_AUTOCSELECTIONCHANGE">SCN_AUTOCSELECTIONCHANGE</a><br />
    </code>

    <p>The following <code>SCI_*</code> messages are associated with these notifications:</p>
    <code><a class="message" href="#SCI_SETMODEVENTMASK">SCI_SETMODEVENTMASK(int eventMask)</a><br />
     <a class="message" href="#SCI_GETMODEVENTMASK">SCI_GETMODEVENTMASK &rarr; int</a><br />
     <a class="message" href="#SCI_SETCOMMANDEVENTS">SCI_SETCOMMANDEVENTS(bool commandEvents)</a><br />
     <a class="message" href="#SCI_GETCOMMANDEVENTS">SCI_GETCOMMANDEVENTS &rarr; bool</a><br />
     <a class="message" href="#SCI_SETMOUSEDWELLTIME">SCI_SETMOUSEDWELLTIME(int periodMilliseconds)</a><br />
     <a class="message" href="#SCI_GETMOUSEDWELLTIME">SCI_GETMOUSEDWELLTIME &rarr; int</a><br />
     <a class="message" href="#SCI_SETIDENTIFIER">SCI_SETIDENTIFIER(int identifier)</a><br />
     <a class="message" href="#SCI_GETIDENTIFIER">SCI_GETIDENTIFIER &rarr; int</a><br />
    </code>

    <p>The following additional notifications are sent using a secondary "command" method and should
    be avoided in new code as the primary "notification" method provides all the same events with richer
    information.
    The <code>WM_COMMAND</code> message is used on Windows.
    This emulates the Windows Edit control. Only the lower
    16 bits of the control's ID is passed in these notifications.</p>
    <p>On GTK+, the "command" signal is sent and the signal handler should have the signature
    <code>handler(GtkWidget *, gint wParam, gpointer lParam, gpointer userData)</code>.</p>
    <code><a class="message" href="#SCEN_CHANGE">SCEN_CHANGE</a><br />
     <a class="message" href="#SCEN_SETFOCUS">SCEN_SETFOCUS</a><br />
     <a class="message" href="#SCEN_KILLFOCUS">SCEN_KILLFOCUS</a><br />
    </code>

    <p><b id="SCI_SETIDENTIFIER">SCI_SETIDENTIFIER(int identifier)</b><br />
     <b id="SCI_GETIDENTIFIER">SCI_GETIDENTIFIER &rarr; int</b><br />
     These two messages set and get the identifier of the Scintilla instance which is included in notifications as the
     <code>idFrom</code> field.
     When an application creates multiple Scintilla widgets, this allows the source of each notification to be found.
     On Windows, this value is initialised in the <code>CreateWindow</code> call and stored as the
     <code>GWLP_ID</code> attribute of the window.
     The value should be small, preferably less than 16 bits,
     rather than a pointer as some of the functions will only transmit 16 or 32 bits.
    </p>

    <p><b id="SCN_STYLENEEDED">SCN_STYLENEEDED</b><br />
     If you used <code><a class="message"
    href="#SCI_SETLEXER">SCI_SETLEXER</a>(SCLEX_CONTAINER)</code> to make the container act as the
    lexer, you will receive this notification when Scintilla is about to display or print text that
    requires styling. You are required to style the text from the line that contains the position
    returned by <a class="seealso" href="#SCI_GETENDSTYLED"><code>SCI_GETENDSTYLED</code></a> up to
    the position passed in <code>SCNotification.position</code>. Symbolically, you need code of the
    form:</p>
<pre>
    startPos = <a class="seealso" href="#SCI_GETENDSTYLED">SCI_GETENDSTYLED</a>()
    lineNumber = <a class="seealso"
href="#SCI_LINEFROMPOSITION">SCI_LINEFROMPOSITION</a>(startPos);
    startPos = <a class="seealso"
href="#SCI_POSITIONFROMLINE">SCI_POSITIONFROMLINE</a>(lineNumber);
    MyStyleRoutine(startPos, SCNotification.position);
</pre>

    <p><b id="SCN_CHARADDED">SCN_CHARADDED</b><br />
     This is sent when the user types an ordinary text character (as opposed to a command
    character) that is entered into the text. The container can use this to decide to display a <a
    class="jump" href="#CallTips">call tip</a> or an <a class="jump" href="#Autocompletion">auto
    completion list</a>. The character is in <code>SCNotification.ch</code>.
        This notification is sent before the character has been styled so processing that depends on
        styling should instead be performed in the SCN_UPDATEUI notification.</p>

    <p><b id="SCN_SAVEPOINTREACHED">SCN_SAVEPOINTREACHED</b><br />
     <b id="SCN_SAVEPOINTLEFT">SCN_SAVEPOINTLEFT</b><br />
     Sent to the container when the save point is entered or left, allowing the container to
    display a "document dirty" indicator and change its menus.<br />
     See also: <a class="message" href="#SCI_SETSAVEPOINT"><code>SCI_SETSAVEPOINT</code></a>, <a
    class="message" href="#SCI_GETMODIFY"><code>SCI_GETMODIFY</code></a></p>

    <p><b id="SCN_MODIFYATTEMPTRO">SCN_MODIFYATTEMPTRO</b><br />
     When in read-only mode, this notification is sent to the container if the user tries to change
    the text. This can be used to check the document out of a version control system. You can set
    the read-only state of a document with <code><a class="message"
    href="#SCI_SETREADONLY">SCI_SETREADONLY</a></code>.</p>

    <p><b id="SCN_KEY">SCN_KEY</b><br />
     Reports all keys pressed but not consumed by Scintilla. Used on GTK+ because of
     some problems with keyboard focus and is not sent by the Windows version. <code>SCNotification.ch</code> holds the key code and
    <code>SCNotification.modifiers</code> holds the modifiers. This notification is sent if the
    modifiers include <code>SCMOD_ALT</code> or <code>SCMOD_CTRL</code> and the key code is less
    than 256.</p>

    <p><b id="SCN_DOUBLECLICK">SCN_DOUBLECLICK</b><br />
     The mouse button was double clicked in editor. The <code>position</code> field is set to the text position of the
    double click, the <code>line</code> field is set to the line of the double click, and
    the <code>modifiers</code> field is set to the key modifiers
    held down in a similar manner to <a class="message" href="#SCN_KEY">SCN_KEY</a>.</p>

    <p><b id="SCN_UPDATEUI">SCN_UPDATEUI</b><br />
     Either the text or styling of the document has changed or the selection range or scroll position may have changed.
     Now would be a good time to update any container UI elements that depend on document or view state.
     As it is sometimes difficult to determine whether a change has occurred, these events may also fire when there
     has been no actual change.
     The <code>updated</code> field is set to the bit set of things changed since the previous notification.</p>
    <table class="standard" summary="Modify notification type flags">
      <tbody>
        <tr>
          <th align="left">Symbol</th>

          <th>Value</th>

          <th align="left">Meaning</th>

        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>SC_UPDATE_CONTENT</code></td>

          <td align="center">0x01</td>

          <td>Contents, styling or markers may have been changed.</td>

        </tr>

        <tr>
          <td align="left"><code>SC_UPDATE_SELECTION</code></td>

          <td align="center">0x02</td>

          <td>Selection may have been changed.</td>

        </tr>

        <tr>
          <td align="left"><code>SC_UPDATE_V_SCROLL</code></td>

          <td align="center">0x04</td>

          <td>May have scrolled vertically.</td>

        </tr>

        <tr>
          <td align="left"><code>SC_UPDATE_H_SCROLL</code></td>

          <td align="center">0x08</td>

          <td>May have scrolled horizontally.</td>

        </tr>
      </tbody>
    </table>

    <p><b id="SCN_MODIFIED">SCN_MODIFIED</b><br />
     This notification is sent when the text or styling of the document changes or is about to
    change. You can set a mask for the notifications that are sent to the container with <a
    class="message" href="#SCI_SETMODEVENTMASK"><code>SCI_SETMODEVENTMASK</code></a>. The
    notification structure contains information about what changed, how the change occurred and
    whether this changed the number of lines in the document. No modifications may be performed
    while in a <code>SCN_MODIFIED</code> event. The <code>SCNotification</code> fields used
    are:</p>

    <table class="standard" summary="Modify notification types">
      <tbody>
        <tr>
          <th align="left">Field</th>

          <th align="left">Usage</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>modificationType</code></td>

          <td align="left">A set of flags that identify the change(s) made. See the next
          table.</td>
        </tr>

        <tr>
          <td align="left"><code>position</code></td>

          <td align="left">Start position of a text or styling change. Set to 0 if not used.</td>
        </tr>

        <tr>
          <td align="left"><code>length</code></td>

          <td align="left">Length of the change in bytes when the text or styling
          changes. Set to 0 if not used.</td>
        </tr>

        <tr>
          <td align="left"><code>linesAdded</code></td>

          <td align="left">Number of added lines. If negative, the number of deleted lines. Set to
          0 if not used or no lines added or deleted.</td>
        </tr>

        <tr>
          <td align="left"><code>text</code></td>

          <td align="left">Valid for text changes, not for style changes. If we are collecting undo
          information this holds a pointer to the text that is handed to the Undo system, otherwise
          it is zero. For user performed SC_MOD_BEFOREDELETE the text field is 0.</td>
        </tr>

        <tr>
          <td align="left"><code>line</code></td>

          <td align="left">The line number at which a fold level or marker change occurred. This is
          0 if unused and may be -1 if more than one line changed.</td>
        </tr>

        <tr>
          <td align="left"><code>foldLevelNow</code></td>

          <td align="left">The new fold level applied to the line or 0 if this field is
          unused.</td>
        </tr>

        <tr>
          <td align="left"><code>foldLevelPrev</code></td>

          <td align="left">The previous folding level of the line or 0 if this field is
          unused.</td>
        </tr>
      </tbody>
    </table>

    <p>The <code>SCNotification.modificationType</code> field has bits set to tell you what has
    been done. The <code>SC_MOD_*</code> bits correspond to actions. The
    <code>SC_PERFORMED_*</code> bits tell you if the action was done by the user, or the result of
    Undo or Redo of a previous action.</p>

    <table class="standard" summary="Modify notification type flags">
      <tbody>
        <tr>
          <th align="left">Symbol</th>

          <th>Value</th>

          <th align="left">Meaning</th>

          <th align="left">SCNotification fields</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>SC_MOD_INSERTTEXT</code></td>

          <td align="right">0x01</td>

          <td>Text has been inserted into the document.</td>

          <td><code>position, length, text, linesAdded</code></td>
        </tr>

        <tr>
          <td align="left"><code>SC_MOD_DELETETEXT</code></td>

          <td align="right">0x02</td>

          <td>Text has been removed from the document.</td>

          <td><code>position, length, text, linesAdded</code></td>
        </tr>

        <tr>
          <td align="left"><code>SC_MOD_CHANGESTYLE</code></td>

          <td align="right">0x04</td>

          <td>A style change has occurred.</td>

          <td><code>position, length</code></td>
        </tr>

        <tr>
          <td align="left"><code>SC_MOD_CHANGEFOLD</code></td>

          <td align="right">0x08</td>

          <td>A folding change has occurred.</td>

          <td><code>line, foldLevelNow, foldLevelPrev</code></td>
        </tr>

        <tr>
          <td align="left"><code>SC_PERFORMED_USER</code></td>

          <td align="right">0x10</td>

          <td>Information: the operation was done by the user.</td>

          <td>None</td>
        </tr>

        <tr>
          <td align="left"><code>SC_PERFORMED_UNDO</code></td>

          <td align="right">0x20</td>

          <td>Information: this was the result of an Undo.</td>

          <td>None</td>
        </tr>

        <tr>
          <td align="left"><code>SC_PERFORMED_REDO</code></td>

          <td align="right">0x40</td>

          <td>Information: this was the result of a Redo.</td>

          <td>None</td>
        </tr>

        <tr>
          <td align="left"><code>SC_MULTISTEPUNDOREDO</code></td>

          <td align="right">0x80</td>

          <td>This is part of a multi-step Undo or Redo transaction.</td>

          <td>None</td>
        </tr>

        <tr>
          <td align="left"><code>SC_LASTSTEPINUNDOREDO</code></td>

          <td align="right">0x100</td>

          <td>This is the final step in an Undo or Redo transaction.</td>

          <td>None</td>
        </tr>

        <tr>
          <td align="left"><code>SC_MOD_CHANGEMARKER</code></td>

          <td align="right">0x200</td>

          <td>One or more markers has changed in a line.</td>

          <td><code>line</code></td>
        </tr>

        <tr>
          <td align="left"><code>SC_MOD_BEFOREINSERT</code></td>

          <td align="right">0x400</td>

          <td>Text is about to be inserted into the document.</td>

          <td><code>position, if performed by user then text in bytes, length in bytes</code></td>
        </tr>

        <tr>
          <td align="left"><code>SC_MOD_BEFOREDELETE</code></td>

          <td align="right">0x800</td>

          <td>Text is about to be deleted from the document.</td>

          <td><code>position, length</code></td>
        </tr>

        <tr>
          <td align="left"><code>SC_MOD_CHANGEINDICATOR</code></td>

          <td align="right">0x4000</td>

          <td>An indicator has been added or removed from a range of text.</td>

          <td><code>position, length</code></td>
        </tr>

        <tr>
          <td align="left"><code id="SC_MOD_CHANGELINESTATE">SC_MOD_CHANGELINESTATE</code></td>

          <td align="right">0x8000</td>

          <td>A line state has changed because <a class="seealso" href="#SCI_SETLINESTATE">SCI_SETLINESTATE</a>
          was called.</td>

          <td><code>line</code></td>
        </tr>

        <tr>
          <td align="left"><code id="SC_MOD_CHANGETABSTOPS">SC_MOD_CHANGETABSTOPS</code></td>

          <td align="right">0x200000</td>

          <td>The explicit tab stops on a line have changed because <a class="seealso" href="#SCI_CLEARTABSTOPS">SCI_CLEARTABSTOPS</a> or
          <a class="seealso" href="#SCI_ADDTABSTOP">SCI_ADDTABSTOP</a> was called.</td>

          <td><code>line</code></td>
        </tr>

        <tr>
          <td align="left"><code id="SC_MOD_LEXERSTATE">SC_MOD_LEXERSTATE</code></td>

          <td align="right">0x80000</td>

          <td>The internal state of a lexer has changed over a range.</td>

          <td><code>position, length</code></td>
        </tr>

        <tr>
          <td align="left"><code id="SC_MOD_CHANGEMARGIN">SC_MOD_CHANGEMARGIN</code></td>

          <td align="right">0x10000</td>

          <td>A text margin has changed.</td>

          <td><code>line</code></td>
        </tr>

        <tr>
          <td align="left"><code id="SC_MOD_CHANGEANNOTATION">SC_MOD_CHANGEANNOTATION</code></td>

          <td align="right">0x20000</td>

          <td>An annotation has changed.</td>

          <td><code>line</code></td>
        </tr>

        <tr>
          <td align="left"><code id="SC_MOD_INSERTCHECK">SC_MOD_INSERTCHECK</code></td>

          <td align="right">0x100000</td>

          <td>Text is about to be inserted. The handler may change the text being inserted by calling
	  <a class="seealso" href="#SCI_CHANGEINSERTION">SCI_CHANGEINSERTION</a>.
	  No other modifications may be made in this handler.</td>

          <td><code>position, length, text</code></td>
        </tr>

        <tr>
          <td align="left"><code>SC_MULTILINEUNDOREDO</code></td>

          <td align="right">0x1000</td>

          <td>This is part of an Undo or Redo with multi-line changes.</td>

          <td>None</td>
        </tr>

        <tr>
          <td align="left"><code>SC_STARTACTION</code></td>

          <td align="right">0x2000</td>

          <td>This is set on a SC_PERFORMED_USER action when it is the
          first or only step in an undo transaction. This can be used to integrate the Scintilla
          undo stack with an undo stack in the container application by adding a Scintilla
          action to the container's stack for the currently opened container transaction or
          to open a new container transaction if there is no open container transaction.
          </td>

          <td>None</td>
        </tr>

        <tr>
          <td align="left"><code id="SC_MOD_CONTAINER">SC_MOD_CONTAINER</code></td>

          <td align="right">0x40000</td>

          <td>This is set on for actions that the container stored into the undo stack with
	  <a class="message" href="#SCI_ADDUNDOACTION"><code>SCI_ADDUNDOACTION</code></a>.
          </td>

          <td>token</td>
        </tr>

        <tr>
          <td align="left"><code>SC_MODEVENTMASKALL</code></td>

          <td align="right">0x1FFFFF</td>

          <td>This is a mask for all valid flags. This is the default mask state set by <a
          class="message" href="#SCI_SETMODEVENTMASK"><code>SCI_SETMODEVENTMASK</code></a>.</td>

          <td>None</td>
        </tr>
      </tbody>
    </table>

    <p><b id="SCEN_CHANGE">SCEN_CHANGE</b><br />
     <code>SCEN_CHANGE</code> (768) is fired when the text (not the style) of the document changes.
    This notification is sent using the <code>WM_COMMAND</code> message on Windows and the
    "command" signal on GTK+ as this is the behaviour of the standard Edit control
    (<code>SCEN_CHANGE</code> has the same value as the Windows Edit control
    <code>EN_CHANGE</code>). No other information is sent. If you need more detailed information
    use <a class="message" href="#SCN_MODIFIED"><code>SCN_MODIFIED</code></a>. You can filter the
    types of changes you are notified about with <a class="message"
    href="#SCI_SETMODEVENTMASK"><code>SCI_SETMODEVENTMASK</code></a> and
    <a class="message"
    href="#SCI_SETCOMMANDEVENTS"><code>SCI_SETCOMMANDEVENTS</code></a>.</p>

    <p><b id="SCI_SETMODEVENTMASK">SCI_SETMODEVENTMASK(int eventMask)</b><br />
     <b id="SCI_GETMODEVENTMASK">SCI_GETMODEVENTMASK &rarr; int</b><br />
     These messages set and get an event mask that determines which document change events are
    notified to the container with <a class="message"
    href="#SCN_MODIFIED"><code>SCN_MODIFIED</code></a> and <a class="message"
    href="#SCEN_CHANGE"><code>SCEN_CHANGE</code></a>. For example, a container may decide to see
    only notifications about changes to text and not styling changes by calling
    <code>SCI_SETMODEVENTMASK(SC_MOD_INSERTTEXT|SC_MOD_DELETETEXT)</code>.</p>

    <p>The possible notification types are the same as the <code>modificationType</code> bit flags
    used by <code>SCN_MODIFIED</code>: <code>SC_MOD_INSERTTEXT</code>,
    <code>SC_MOD_DELETETEXT</code>, <code>SC_MOD_CHANGESTYLE</code>,
    <code>SC_MOD_CHANGEFOLD</code>, <code>SC_PERFORMED_USER</code>, <code>SC_PERFORMED_UNDO</code>,
    <code>SC_PERFORMED_REDO</code>, <code>SC_MULTISTEPUNDOREDO</code>,
    <code>SC_LASTSTEPINUNDOREDO</code>, <code>SC_MOD_CHANGEMARKER</code>,
    <code>SC_MOD_BEFOREINSERT</code>, <code>SC_MOD_BEFOREDELETE</code>,
    <code>SC_MULTILINEUNDOREDO</code>, and <code>SC_MODEVENTMASKALL</code>.</p>

    <p><b id="SCI_SETCOMMANDEVENTS">SCI_SETCOMMANDEVENTS(bool commandEvents)</b><br />
     <b id="SCI_GETCOMMANDEVENTS">SCI_GETCOMMANDEVENTS &rarr; bool</b><br />
     These messages set and get whether <code>SCEN_*</code> command events are
    sent to the container. For <code>SCEN_CHANGE</code> this acts as an additional filter over
    <a class="message" href="#SCI_SETMODEVENTMASK"><code>SCI_SETMODEVENTMASK</code></a>.
    Most applications should set this off to avoid overhead and only use
    <a class="message" href="#SCN_MODIFIED"><code>SCN_MODIFIED</code></a>.</p>

    <p><b id="SCEN_SETFOCUS">SCEN_SETFOCUS</b><br />
     <b id="SCEN_KILLFOCUS">SCEN_KILLFOCUS</b><br />
     <code>SCEN_SETFOCUS</code> (512) is fired when Scintilla receives focus and
    <code>SCEN_KILLFOCUS</code> (256) when it loses focus. These notifications are sent using the
    <code>WM_COMMAND</code> message on Windows and the "command" signal on GTK+ as this is the
    behaviour of the standard Edit control. Unfortunately, these codes do not match the Windows Edit
    notification codes <code>EN_SETFOCUS</code> (256) and <code>EN_KILLFOCUS</code> (512). It is
    now too late to change the Scintilla codes as clients depend on the current values.</p>

    <p><b id="SCN_MACRORECORD">SCN_MACRORECORD</b><br />
     The <code><a class="message" href="#SCI_STARTRECORD">SCI_STARTRECORD</a></code> and <a
    class="message" href="#SCI_STOPRECORD"><code>SCI_STOPRECORD</code></a> messages enable and
    disable macro recording. When enabled, each time a recordable change occurs, the
    <code>SCN_MACRORECORD</code> notification is sent to the container. It is up to the container
    to record the action. To see the complete list of <code>SCI_*</code> messages that are
    recordable, search the Scintilla source <code>Editor.cxx</code> for
    <code>Editor::NotifyMacroRecord</code>. The fields of <code>SCNotification</code> set in this
    notification are:</p>

    <table class="standard" summary="Macro record notification data">
      <tbody>
        <tr>
          <th align="left">Field</th>

          <th align="left">Usage</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>message</code></td>

          <td align="left">The <code>SCI_*</code> message that caused the notification.</td>
        </tr>

        <tr>
          <td align="left"><code>wParam</code></td>

          <td align="left">The value of <code>wParam</code> in the <code>SCI_*</code> message.</td>
        </tr>

        <tr>
          <td align="left"><code>lParam</code></td>

          <td align="left">The value of <code>lParam</code> in the <code>SCI_*</code> message.</td>
        </tr>
      </tbody>
    </table>

    <p><b id="SCN_MARGINCLICK">SCN_MARGINCLICK</b><br />
     <b id="SCN_MARGINRIGHTCLICK">SCN_MARGINRIGHTCLICK</b><br />
     These notifications tell the container that the mouse was clicked or right clicked inside a <a class="jump"
    href="#Margins">margin</a> that was marked as sensitive (see <a class="message"
    href="#SCI_SETMARGINSENSITIVEN"><code>SCI_SETMARGINSENSITIVEN</code></a>). This can be used to
    perform folding or to place breakpoints. The following <code>SCNotification</code> fields are
    used:</p>

    <table class="standard" summary="Margin click notification">
      <tbody>
        <tr>
          <th align="left">Field</th>

          <th align="left">Usage</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>modifiers</code></td>

          <td align="left">The appropriate combination of <code>SCI_SHIFT</code>,
          <code>SCI_CTRL</code> and <code>SCI_ALT</code> to indicate the keys that were held down
          at the time of the margin click.</td>
        </tr>

        <tr>
          <td align="left"><code>position</code></td>

          <td align="left">The position of the start of the line in the document that corresponds
          to the margin click.</td>
        </tr>

        <tr>
          <td align="left"><code>margin</code></td>

          <td align="left">The margin number that was clicked.</td>
        </tr>
      </tbody>
    </table>

    <p><b id="SCN_NEEDSHOWN">SCN_NEEDSHOWN</b><br />
     Scintilla has determined that a range of lines that is currently invisible should be made
    visible. An example of where this may be needed is if the end of line of a contracted fold
    point is deleted. This message is sent to the container in case it wants to make the line
    visible in some unusual way such as making the whole document visible. Most containers will
    just ensure each line in the range is visible by calling <a class="message"
    href="#SCI_ENSUREVISIBLE"><code>SCI_ENSUREVISIBLE</code></a>. The <code>position</code> and
    <code>length</code> fields of <code>SCNotification</code> indicate the range of the document
    that should be made visible. The container code will be similar to the following code
    skeleton:</p>
<pre>
firstLine = SCI_LINEFROMPOSITION(scn.position)
lastLine = SCI_LINEFROMPOSITION(scn.position+scn.length-1)
for line = lineStart to lineEnd do SCI_ENSUREVISIBLE(line) next
</pre>

    <p><b id="SCN_PAINTED">SCN_PAINTED</b><br />
     Painting has just been done. Useful when you want to update some other widgets based on a
    change in Scintilla, but want to have the paint occur first to appear more responsive. There is
    no other information in <code>SCNotification</code>.</p>

    <p><b id="SCN_USERLISTSELECTION">SCN_USERLISTSELECTION</b><br />
     The user has selected an item in a <a class="jump" href="#UserLists">user list</a>. The
    <code>SCNotification</code> fields used are:</p>

    <table class="standard" summary="User list notification">
      <tbody>
        <tr>
          <th align="left">Field</th>

          <th align="left">Usage</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>listType</code></td>

          <td align="left">This is set to the <code>listType</code> parameter from the <a
          class="message" href="#SCI_USERLISTSHOW"><code>SCI_USERLISTSHOW</code></a> message that
          initiated the list.</td>
        </tr>

        <tr>
          <td align="left"><code>text</code></td>

          <td align="left">The text of the selection.</td>
        </tr>

        <tr>
          <td align="left"><code>position</code></td>

          <td align="left">The position the list was displayed at.</td>
        </tr>
        <tr>
          <td align="left"><code>ch</code></td>

          <td align="left">If a fillup character was the method of selection, the used
            character, otherwise 0.</td>
        </tr>
        <tr>
          <td align="left"><code>listCompletionMethod</code></td>

          <td align="left">A value indicating the way in which the completion
            occurred. See the table below.</td>
        </tr>
      </tbody>
    </table>
    <br />

     See the <code><a class="jump" href="#SCN_AUTOCCOMPLETED">SCN_AUTOCCOMPLETED</a></code> notification
    for the possible values for <code>listCompletionMethod.</code>
    <p><b id="SCN_URIDROPPED">SCN_URIDROPPED</b><br />
     Only on the GTK+ version. Indicates that the user has dragged a URI such as a file name or Web
    address onto Scintilla. The container could interpret this as a request to open the file. The
    <code>text</code> field of <code>SCNotification</code> points at the URI text.</p>

    <p><b id="SCN_DWELLSTART">SCN_DWELLSTART</b><br />
     <b id="SCN_DWELLEND">SCN_DWELLEND</b><br />
     <code>SCN_DWELLSTART</code> is generated when the user keeps the mouse in one position for the
    dwell period (see <code><a class="message"
    href="#SCI_SETMOUSEDWELLTIME">SCI_SETMOUSEDWELLTIME</a></code>). <code>SCN_DWELLEND</code> is
    generated after a <code>SCN_DWELLSTART</code> and the mouse is moved or other activity such as
    key press indicates the dwell is over. Both notifications set the same fields in
    <code>SCNotification</code>:</p>

    <table class="standard" summary="Mouse dwell notification">
      <tbody>
        <tr>
          <th align="left">Field</th>

          <th align="left">Usage</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>position</code></td>

          <td align="left">This is the nearest position in the document to the position where the
          mouse pointer was lingering.</td>
        </tr>

        <tr>
          <td align="left"><code>x, y</code></td>

          <td align="left">Where the pointer lingered. The <code>position</code> field is set to
          <code><a class="message"
          href="#SCI_POSITIONFROMPOINTCLOSE">SCI_POSITIONFROMPOINTCLOSE</a>(x, y)</code>.</td>
        </tr>
      </tbody>
    </table>
    <br />

    <p><b id="SCI_SETMOUSEDWELLTIME">SCI_SETMOUSEDWELLTIME(int periodMilliseconds)</b><br />
     <b id="SCI_GETMOUSEDWELLTIME">SCI_GETMOUSEDWELLTIME &rarr; int</b><br />
     These two messages set and get the time the mouse must sit still, in milliseconds, to generate
    a <code><a class="message" href="#SCN_DWELLSTART">SCN_DWELLSTART</a></code> notification. If
    set to <code>SC_TIME_FOREVER</code>, the default, no dwell events are generated.</p>

    <p><b id="SCN_ZOOM">SCN_ZOOM</b><br />
     This notification is generated when the user zooms the display using the keyboard or the
    <code><a class="seealso" href="#SCI_SETZOOM">SCI_SETZOOM</a></code> method is called. This
    notification can be used to recalculate positions, such as the width of the line number margin
    to maintain sizes in terms of characters rather than pixels. <code>SCNotification</code> has no
    additional information.</p>

    <p>
    <b id="SCN_HOTSPOTCLICK">SCN_HOTSPOTCLICK</b><br />
    <b id="SCN_HOTSPOTDOUBLECLICK">SCN_HOTSPOTDOUBLECLICK</b><br />
    <b id="SCN_HOTSPOTRELEASECLICK">SCN_HOTSPOTRELEASECLICK</b><br />
     These notifications are generated when the user clicks or double clicks on
     text that is in a style with the hotspot attribute set.
    This notification can be used to link to variable definitions or web pages.
    In the notification handler, you should avoid calling any function that modifies the current selection or caret position.
    The <code>position</code> field is set the text position of the click or
    double click and the <code>modifiers</code> field set to the key modifiers
    held down in a similar manner to <a class="message" href="#SCN_KEY">SCN_KEY</a>.
    Only the state of the Ctrl key is reported for <code>SCN_HOTSPOTRELEASECLICK</code>.</p>

    <p>
    <b id="SCN_INDICATORCLICK">SCN_INDICATORCLICK</b><br />
    <b id="SCN_INDICATORRELEASE">SCN_INDICATORRELEASE</b><br />
     These notifications are generated when the user clicks or releases the mouse on
     text that has an indicator.
    The <code>position</code> field is set the text position of the click or
    double click and the <code>modifiers</code> field set to the key modifiers
    held down in a similar manner to <a class="message" href="#SCN_KEY">SCN_KEY</a>.</p>

    <p><b id="SCN_CALLTIPCLICK">SCN_CALLTIPCLICK</b><br />
     This notification is generated when the user clicks on a calltip.
    This notification can be used to display the next function prototype when a
    function name is overloaded with different arguments.
    The <code>position</code> field is set to 1 if the click is in an up arrow,
    2 if in a down arrow, and 0 if elsewhere.</p>

    <p><b id="SCN_AUTOCSELECTION">SCN_AUTOCSELECTION</b><br />
     The user has selected an item in an <a class="jump" href="#Autocompletion">autocompletion list</a>. The
     notification is sent before the selection is inserted. Automatic insertion can be cancelled by sending a
     <code><a class="message" href="#SCI_AUTOCCANCEL">SCI_AUTOCCANCEL</a></code> message
     before returning from the notification. The <code>SCNotification</code> fields used are:</p>

    <table class="standard" summary="Autocompletion list notification">
      <tbody>
        <tr>
          <th align="left">Field</th>

          <th align="left">Usage</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>position</code></td>

          <td align="left">The start position of the word being completed.</td>
        </tr>
        <tr>
          <td align="left"><code>text</code></td>

          <td align="left">The text of the selection.</td>
        </tr>
        <tr>
          <td align="left"><code>ch</code></td>

          <td align="left">If a fillup character was the method of selection, the used
            character, otherwise 0.</td>
        </tr>
        <tr>
          <td align="left"><code>listCompletionMethod</code></td>

          <td align="left">A value indicating the way in which the completion
            occurred. See the table below.</td>
        </tr>
      </tbody>
    </table>
    <br />
    <table class="standard" summary="Modify notification type flags">
      <tbody>
        <tr>
          <th align="left">Symbol</th>

          <th>Value</th>

          <th align="left">Meaning</th>

        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>SC_AC_FILLUP</code></td>

          <td align="center">1</td>

          <td>A fillup character triggered the completion. The character used is
            in ch. </td>

        </tr>

        <tr>
          <td align="left"><code>SC_AC_DOUBLECLICK</code></td>

          <td align="center">2</td>

          <td>A double-click triggered the completion. ch is 0.</td>

        </tr>

        <tr>
          <td align="left"><code>SC_AC_TAB</code></td>

          <td align="center">3</td>

          <td>The tab key or SCI_TAB triggered the completion. ch is 0.</td>

        </tr>

        <tr>
          <td align="left"><code>SC_AC_NEWLINE</code></td>

          <td align="center">4</td>

          <td>A new line or SCI_NEWLINE triggered the completion. ch is 0.</td>

        </tr>

        <tr>
          <td align="left"><code>SC_AC_COMMAND</code></td>

          <td align="center">5</td>

          <td>The
    <code>
     <a class="seealso" href="#SCI_AUTOCSELECT">SCI_AUTOCSELECT</a></code> message
          triggered the completion. ch is 0.</td>

        </tr>
      </tbody>
    </table>

    <p><b id="SCN_AUTOCCANCELLED">SCN_AUTOCCANCELLED</b><br />
     The user has cancelled an <a class="jump" href="#Autocompletion">autocompletion list</a>.
     There is no other information in SCNotification.</p>

    <p><b id="SCN_AUTOCCHARDELETED">SCN_AUTOCCHARDELETED</b><br />
     The user deleted a character while autocompletion list was active.
     There is no other information in SCNotification.</p>


    <p><b id="SCN_AUTOCCOMPLETED">SCN_AUTOCCOMPLETED<br />
    </b>This notification is generated after an autocompletion has inserted its
    text. The fields are identical to the
    <code>
     <a class="jump" href="#SCN_AUTOCSELECTION">SCN_AUTOCSELECTION</a></code>
     notification.</p>

    <p><b id="SCN_AUTOCSELECTIONCHANGE">SCN_AUTOCSELECTIONCHANGE<br />
    </b>This notification is sent when items are highlighted in an autocompletion or user list.
     The
    <code>SCNotification</code> fields used are:</p>

    <table class="standard" summary="User list notification">
      <tbody>
        <tr>
          <th align="left">Field</th>

          <th align="left">Usage</th>
        </tr>
      </tbody>

      <tbody valign="top">
        <tr>
          <td align="left"><code>listType</code></td>

          <td align="left">This is set to the <code>listType</code> parameter from the <a
          class="message" href="#SCI_USERLISTSHOW"><code>SCI_USERLISTSHOW</code></a> message
	  or 0 for an autocompletion.</td>
        </tr>

        <tr>
          <td align="left"><code>text</code></td>

          <td align="left">The text of the selection.</td>
        </tr>

        <tr>
          <td align="left"><code>position</code></td>

          <td align="left">The position the list was displayed at.</td>
        </tr>
      </tbody>
    </table>

    <p><b id="SCN_FOCUSIN">SCN_FOCUSIN</b><br />
    <b id="SCN_FOCUSOUT">SCN_FOCUSOUT</b><br />
    <code>SCN_FOCUSIN</code> (2028) is fired when Scintilla receives focus and
    <code>SCN_FOCUSOUT</code> (2029) when it loses focus.</p>

    <h2 id="Images">Images</h2>

    <p>Two formats are supported for images used in margin markers and autocompletion lists, RGBA and XPM.</p>

    <h3 id="RGBA">RGBA</h3>

    <p>The RGBA format allows translucency with an <a class="jump" href="#alpha">alpha</a>
    value for each pixel. It is simpler than
    <code>XPM</code> and more capable.</p>

    <p>The data is a sequence of 4 byte pixel values starting with the pixels for the top line, with the
    leftmost pixel first, then continuing with the pixels for subsequent lines. There is no gap between
    lines for alignment reasons.</p>

    <p>Each pixel consists of, in order, a red byte, a green byte, a blue byte and an alpha byte.
    The colour bytes are not premultiplied by the alpha value. That is, a fully red pixel that is
    25% opaque will be [FF, 00, 00, 3F]</p>

    <p>Since the RGBA pixel data does not include any size information the
    width and height must previously been set with the
    <a class="message" href="#SCI_RGBAIMAGESETWIDTH"><code>SCI_RGBAIMAGESETWIDTH</code></a> and
    <a class="message" href="#SCI_RGBAIMAGESETHEIGHT"><code>SCI_RGBAIMAGESETHEIGHT</code></a> messages.</p>

    <p>GUI platforms often include functions for reading image file formats like PNG into memory
    in the RGBA form or a similar form.
    If there is no suitable platform support, the <a href="http://lodev.org/lodepng/">LodePNG and picoPNG</a> libraries are small libraries
    for loading and decoding PNG files available under a BSD-style license.</p>

    <p>RGBA format is supported on Windows, GTK+ and OS X Cocoa.</p>

    <h3 id="XPM">XPM</h3>

    <p>The XPM format is
    <a class="jump" href="http://en.wikipedia.org/wiki/X_PixMap">described here</a>.
    Scintilla is only able to handle XPM pixmaps that use one character per pixel with no named colours.
    There may be a completely transparent colour named "None".</p>
    <p>There are two forms of data structure used for XPM images, the first "lines form" format is well suited
    to embedding an image inside C source code and the "text form" is suited to reading from a file.
    In the lines form, an array of strings is used with the first string indicating the dimensions and number of colours
    used. This is followed by a string for each colour and that section is followed by the image with one string per line.
    The text form contains the same data as one null terminated block formatted as C source code starting
    with a "/* XPM */" comment to mark the format.</p>
    <p>Either format may be used with Scintilla APIs with the bytes at the location pointed to examined
    to determine which format: if the bytes start with "/* XPM */" then it is treated as text form,
    otherwise it is treated as lines form.</p>

    <p>XPM format is supported on on all platforms.</p>

    <h2 id="GTK">GTK+</h2>
    <p>On GTK+, the following functions create a Scintilla widget, communicate with it and allow
    resources to be released after all Scintilla widgets have been destroyed.</p>
    <code><a class="message" href="#scintilla_new">GtkWidget *scintilla_new()</a><br />
     <a class="message" href="#scintilla_set_id">void scintilla_set_id(ScintillaObject *sci, uptr_t id)</a><br />
     <a class="message" href="#scintilla_send_message">sptr_t scintilla_send_message(ScintillaObject *sci,unsigned int iMessage, uptr_t wParam, sptr_t lParam)</a><br />
     <a class="message" href="#scintilla_release_resources">void scintilla_release_resources()</a><br />
     </code>

    <p><b id="scintilla_new">GtkWidget *scintilla_new()</b><br />
    Create a new Scintilla widget. The returned pointer can be added to a container and displayed in the same way as other
    widgets.</p>

    <p><b id="scintilla_set_id">void scintilla_set_id(ScintillaObject *sci, uptr_t id)</b><br />
    Set the control ID which will be used in the idFrom field of the Sci_NotifyHeader structure of all
    notifications for this instance.
    This is equivalent to <a class="seealso" href="#SCI_SETIDENTIFIER">SCI_SETIDENTIFIER</a>.</p>

    <p><b id="scintilla_send_message">sptr_t scintilla_send_message(ScintillaObject *sci,unsigned int iMessage, uptr_t wParam, sptr_t lParam)</b><br />
    The main entry point allows sending any of the messages described in this document.</p>

    <p><b id="scintilla_release_resources">void scintilla_release_resources()</b><br />
    Call this to free any remaining resources after all the Scintilla widgets have been destroyed.</p>

    <h2 id="ProvisionalMessages">Provisional messages</h2>

    <p>Complex new features may be added as 'provisional' to allow further changes to the API.
    Provisional features may even be removed if experience shows they are a mistake.</p>

    <p>Provisional features are displayed in this document with <span class="provisional">a distinctive background colour</span>.</p>

    <p>There are currently no provisional messages or values.</p>

    <p>Some developers may want to only use features that are stable and have graduated from
    provisional status. To avoid using provisional messages compile with the symbol
    <code>SCI_DISABLE_PROVISIONAL</code> defined.</p>

    <h2 id="DeprecatedMessages">Deprecated messages and notifications</h2>

    <p>The following messages are currently supported to emulate existing Windows controls, but
    they will be removed in future versions of Scintilla. If you use these messages you should
    replace them with the Scintilla equivalent.</p>
<pre>
WM_GETTEXT(int length, char *text)
WM_SETTEXT(&lt;unused&gt;, const char *text)
EM_GETLINE(int line, char *text)
EM_REPLACESEL(&lt;unused&gt;, const char *text)
EM_SETREADONLY
EM_GETTEXTRANGE(&lt;unused&gt;, TEXTRANGE *tr)
WM_CUT
WM_COPY
WM_PASTE
WM_CLEAR
WM_UNDO
EM_CANUNDO
EM_EMPTYUNDOBUFFER
WM_GETTEXTLENGTH
EM_GETFIRSTVISIBLELINE
EM_GETLINECOUNT
EM_GETMODIFY
EM_SETMODIFY(bool isModified)
EM_GETRECT(RECT *rect)
EM_GETSEL(int *start, int *end)
EM_EXGETSEL(&lt;unused&gt;, CHARRANGE *cr)
EM_SETSEL(int start, int end)
EM_EXSETSEL(&lt;unused&gt;, CHARRANGE *cr)
EM_GETSELTEXT(&lt;unused&gt;, char *text)
EM_LINEFROMCHAR(int position)
EM_EXLINEFROMCHAR(int position)
EM_LINEINDEX(int line)
EM_LINELENGTH(int position)
EM_SCROLL(int line)
EM_LINESCROLL(int column, int line)
EM_SCROLLCARET()
EM_CANPASTE
EM_CHARFROMPOS(&lt;unused&gt;, POINT *location)
EM_POSFROMCHAR(int position, POINT *location)
EM_SELECTIONTYPE
EM_HIDESELECTION(bool hide)
EM_FINDTEXT(int flags, FINDTEXTEX *ft)
EM_FINDTEXTEX(int flags, FINDTEXTEX *ft)
EM_GETMARGINS
EM_SETMARGINS(EC_LEFTMARGIN or EC_RIGHTMARGIN or EC_USEFONTINFO, int val)
EM_FORMATRANGE
</pre>

    <p>The following are features that are only included if you define
    <code>INCLUDE_DEPRECATED_FEATURES</code> in <code>Scintilla.h</code>. To ensure future
    compatibility you should change them as indicated.</p>

    <code>
     <a class="message" href="#SCI_SETKEYSUNICODE">SCI_SETKEYSUNICODE(bool keysUnicode)</a><br />
     <a class="message" href="#SCI_GETKEYSUNICODE">SCI_GETKEYSUNICODE &rarr; bool</a><br />
     <a class="message" href="#SCI_SETSTYLEBITS">SCI_SETSTYLEBITS(int bits)</a><br />
     <a class="message" href="#SCI_GETSTYLEBITS">SCI_GETSTYLEBITS &rarr; int</a><br />
     <a class="message" href="#SCI_GETSTYLEBITSNEEDED">SCI_GETSTYLEBITSNEEDED &rarr; int</a><br />
    </code>

    <p><b id="SCI_SETKEYSUNICODE">SCI_SETKEYSUNICODE(bool keysUnicode)</b> Deprecated<br />
     <b id="SCI_GETKEYSUNICODE">SCI_GETKEYSUNICODE &rarr; bool</b> Deprecated<br />
     On Windows, Scintilla no longer supports narrow character windows so input is always treated as Unicode.</p>

    <p>The following are features that should be removed from calling code but are still
    defined to avoid breaking callers.</p>

    <p id="StyleByteIndicators"><b id="SCI_SETSTYLEBITS">SCI_SETSTYLEBITS(int bits)</b> Deprecated<br />
    <b id="SCI_GETSTYLEBITS">SCI_GETSTYLEBITS &rarr; int</b> Deprecated<br />
    <b id="SCI_GETSTYLEBITSNEEDED">SCI_GETSTYLEBITSNEEDED &rarr; int</b> Deprecated<br />
    <code>INDIC0_MASK</code>, <code>INDIC1_MASK</code>, <code>INDIC2_MASK</code>, <code>INDICS_MASK</code> Deprecated<br />
     Scintilla no longer supports style byte indicators. The last version to support style byte indicators was 3.4.2.
     Any use of these symbols should be removed and replaced with <a href="#Indicators">standard indicators</a>.
    <code>SCI_GETSTYLEBITS</code> and <code>SCI_GETSTYLEBITSNEEDED</code> always return 8,
    indicating that 8 bits are used for styling and there are 256 styles.</p>

    <h2 id="EditMessagesNeverSupportedByScintilla">Edit messages never supported by Scintilla</h2>
<pre>
EM_GETWORDBREAKPROC EM_GETWORDBREAKPROCEX
EM_SETWORDBREAKPROC EM_SETWORDBREAKPROCEX
EM_GETWORDWRAPMODE EM_SETWORDWRAPMODE
EM_LIMITTEXT EM_EXLIMITTEXT
EM_SETRECT EM_SETRECTNP
EM_FMTLINES
EM_GETHANDLE EM_SETHANDLE
EM_GETPASSWORDCHAR EM_SETPASSWORDCHAR
EM_SETTABSTOPS
EM_FINDWORDBREAK
EM_GETCHARFORMAT EM_SETCHARFORMAT
EM_GETOLEINTERFACE EM_SETOLEINTERFACE
EM_SETOLECALLBACK
EM_GETPARAFORMAT EM_SETPARAFORMAT
EM_PASTESPECIAL
EM_REQUESTRESIZE
EM_GETBKGNDCOLOR EM_SETBKGNDCOLOR
EM_STREAMIN EM_STREAMOUT
EM_GETIMECOLOR EM_SETIMECOLOR
EM_GETIMEOPTIONS EM_SETIMEOPTIONS
EM_GETOPTIONS EM_SETOPTIONS
EM_GETPUNCTUATION EM_SETPUNCTUATION
EM_GETTHUMB
EM_GETEVENTMASK
EM_SETEVENTMASK
EM_DISPLAYBAND
EM_SETTARGETDEVICE
</pre>

    <p>Scintilla tries to be a superset of the standard windows Edit and RichEdit controls wherever
    that makes sense. As it is not intended for use in a word processor, some edit messages can not
    be sensibly handled. Unsupported messages have no effect.</p>

    <h2 id="RemovedFeatures">Removed features</h2>

    <p>These features have now been removed completely.</p>

    <p><b id="SC_CP_DBCS">SC_CP_DBCS</b> Removed in 2016 with release 3.7.1<br />
     This was used to set a DBCS (Double Byte Character Set) mode on GTK+.
     An explicit DBCS code page should be used when calling <a class="seealso" href="#SCI_SETCODEPAGE">SCI_SETCODEPAGE</a></p>

    <p><b id="SCI_SETUSEPALETTE">SCI_SETUSEPALETTE(bool usePalette)</b> Removed in 2016 with release 3.7.1<br />
    <b id="SCI_GETUSEPALETTE">SCI_GETUSEPALETTE &rarr; bool</b> Removed in 2016 with release 3.7.1<br />
     Scintilla no longer supports palette mode. The last version to support palettes was 2.29.
     Any calls to these methods must be removed.</p>

    <p>Previous versions of Scintilla allowed indicators to be stored in bits of each style byte.
    This was deprecated in 2007 and removed in 2014 with release 3.4.3.
    All uses of style byte indicators should be replaced with <a href="#Indicators">standard indicators</a>.</p>

    <h2 id="BuildingScintilla">Building Scintilla</h2>

    <p>To build Scintilla or SciTE, see the README file present in both the Scintilla and SciTE
    directories.
    The compiler must support C++11.
    For Windows, GCC 4.8 or Microsoft Visual C++ 2015 can be used
    for building. For GTK+, GCC 4.8 or newer should be used. GTK+ 2.24+ and 3.x are
    supported with glib 2.22+. The version of GTK+ installed should be detected automatically.
    When both GTK+ 2 and GTK+ 3 are present, building for GTK+ 3.x requires defining GTK3
    on the command line.</p>

    <h3>Static linking</h3>

    <p>On Windows, Scintilla is normally used as a dynamic library as a .DLL file. If you want to
    link Scintilla directly into your application .EXE or .DLL file, then you can link to the static library
    bin/libscintilla.lib (or .a if using GCC) and call <code>Scintilla_RegisterClasses</code>.
    <code>Scintilla_RegisterClasses</code> takes the <code>HINSTANCE</code> of your
    application and ensures that the "Scintilla" window class is registered.</p>
    <p>When producing a stand-alone Scintilla DLL, the ScintillaDLL.cxx file should be compiled and
    linked in to provide <code>DllMain</code> and <code>Scintilla_RegisterClasses</code>.</p>

    <h3>Ensuring lexers are linked into Scintilla</h3>

    <p>Depending on the compiler and linker used, the lexers may be stripped out. This is most
    often caused when building a static library. To ensure the lexers are linked in, the
    <code>Scintilla_LinkLexers()</code> function may be called.</p>

    <h3>Changing set of lexers</h3>

    <p>To change the set of lexers in Scintilla, add and remove lexer source files
    (<code>Lex*.cxx</code>) from the <code>scintilla/lexers directory</code> and run the
    <code>scripts/LexGen.py</code> script from the <code>scripts</code> directory to update the make files
    and <code>Catalogue.cxx</code>. <code>LexGen.py</code> requires Python 2.5 or later. If you do
    not have access to Python, you can hand edit <code>Catalogue.cxx</code> in a simple-minded way,
    following the patterns of other lexers. The important thing is to include
    <code>LINK_LEXER(lmMyLexer);</code> to correspond with the <code>LexerModule
    lmMyLexer(...);</code> in your lexer source code.</p>

    <h3>Building with an alternative Regular Expression implementation</h3>

 <p id="AlternativeRegEx">A simple interface provides support for switching the Regular Expressions engine at
 compile time. You must implement <code>RegexSearchBase</code> for your chosen engine,
 look at the built-in implementation <code>BuiltinRegex</code> to see how this is done.
 You then need to implement the factory method <code>CreateRegexSearch</code>
 to create an instance of your class. You must disable the built-in implementation by defining
 <code>SCI_OWNREGEX</code>.</p>

  </body>
</html>

