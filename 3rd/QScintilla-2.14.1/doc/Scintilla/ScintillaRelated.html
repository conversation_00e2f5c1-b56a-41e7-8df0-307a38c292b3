<?xml version="1.0"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta name="generator" content="HTML Tidy, see www.w3.org" />
    <meta name="generator" content="SciTE" />
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>
      Scintilla and SciTE Related Sites
    </title>
  </head>
  <body bgcolor="#FFFFFF" text="#000000">
    <table bgcolor="#000000" width="100%" cellspacing="0" cellpadding="0" border="0">
      <tr>
        <td>
          <img src="SciTEIco.png" border="3" height="64" width="64" alt="Scintilla icon" />
        </td>
        <td>
          <a href="index.html" style="color:white;text-decoration:none"><font size="5">Scintilla
          and SciTE</font></a>
        </td>
      </tr>
    </table>
    <h2>
       Related Sites
    </h2>
    <h3>
       Ports and Bindings of Scintilla
    </h3>
    <p>
	<a href="http://www.morphos-team.net/releasenotes/3.0">Scintilla.mcc</a>
	is a port to MorphOS.
    </p>
    <p>
	<a href="https://metacpan.org/pod/Wx::Scintilla">Wx::Scintilla</a>
	is a Perl Binding for Scintilla on wxWidgets.
    </p>
    <p>
	<a href="http://codebrainz.github.com/GtkScintilla/">GtkScintilla</a>
	is a GTK+ widget which enables easily adding a powerful
	source code editor to your applications. Harnessing the abilities
	of the Scintilla editing component, GtkScintilla adds a familiar
	GTK+/GObject API, making the widget comfortable to use in
	these programs, using all the typical GObject conventions.
    </p>
    <p>
	<a href="http://www.mewsoft.com/forums/source-code-editor-activex-control-released-scintilla-activex-wrapper-control&amp;action=ViewTopic&amp;Topic=1494&amp;Forum=1&amp;Page=1&amp;Period=0a&amp;Lang=English">Editawy</a>
	is an ActiveX Control wrapper that support all Scintilla functions and additional high level functions.
    </p>
    <p>
	<a href="http://sourceforge.net/projects/jintilla/">Jintilla</a>
	is a JNI wrapper that allows Scintilla to be used in Java with
	both SWT and AWT.
    </p>
    <p>
	<a href="http://delphisci.sourceforge.net/">Delphi Scintilla Interface Components</a>
	is a FREE collection of components that makes it easy to use the
         Scintilla source code editing control from within Delphi and C++ Builder.
    </p>
    <p>
	<a href="http://wxcode.sourceforge.net/showcomp.php?name=wxStEdit">wxStEdit</a>
	is a library and sample program that provides extra features over wxStyledTextControl.
    </p>
    <p>
	<a href="http://www.naughter.com/scintilla.html">CScintillaCtrl, CScintillaView &amp; CScintillaDoc</a>
	are freeware MFC classes to encapsulate Scintilla.
    </p>
    <p>
	<a href="http://sourceforge.net/projects/scide/">ScintillaNet
	</a> is an encapsulation of Scintilla for use within the .NET framework.
    </p>
    <p>
	<a href="https://riverbankcomputing.com/software/qscintilla/intro">QScintilla
	</a> is a port of Scintilla to the Qt platform. It has a similar license to Qt: GPL for use in
	free software and commercial for use in close-source applications.
    </p>
    <p>
	<a href="http://www.adapower.com/gwindows/">
	GWindows</a> is a Win32 RAD GUI Framework for Ada 95 that
	includes a binding of Scintilla.
    </p>
    <p>
	<a href="http://scintilla.cvs.sourceforge.net/viewvc/scintilla/ScintillaVB/">ScintillaVB</a>
	is an ActiveX control written in VB that encapsulates Scintilla.
    </p>
    <p>
	<a href="http://savannah.nongnu.org/projects/fxscintilla/">FXScintilla
	</a> is a port of Scintilla to the FOX platform. FXRuby includes Ruby
	bindings for FXScintilla.
    </p>
    <p>
	<a href="http://www.pnotepad.org/scintilla/">Delphi wrapper</a> for
	Scintilla which is also usable from Borland C++ Builder.
    </p>
    <p>
       The wxStyledTextCtrl editor component  in the
       <a href="http://www.wxwidgets.org/">wxWidgets</a> cross platform toolkit is based on Scintilla.<br />
       A Python binding for wxStyledTextCtrl is part of <a href="http://wxpython.org/">wxPython</a>.
    </p>
    <p>
	<a href="http://sourceforge.net/projects/moleskine/">gtkscintilla</a>
	is an alternative GTK class implementation for scintilla.
	This implementation acts more like a Gtk+ object, with many methods rather
	than just scintilla_send_message() and is available as a shared library.
	This implementation works with GTK 1.x.
    </p>
    <p>
	<a href="http://sourceforge.net/projects/moleskine/">gtkscintilla2</a>
	is an alternative GTK class implementation for scintilla
	similar to the above, but for GTK 2.x.
    </p>
    <p>
	<a href="http://sourceforge.net/projects/moleskine/">pygtkscintilla</a>
	is a Python binding for gtk1.x scintilla that uses
	gtkscintilla instead of the default GTK class.
    </p>
    <p>
	<a href="http://scintilla.cvs.sourceforge.net/viewvc/scintilla/scintillactrl/">ScintillaCtrl</a>
	is an unmaintained ActiveX control wrapper for Scintilla.
    </p>
    <h3>
       Projects using Scintilla
    </h3>
    <p>
	<a href="https://github.com/martinrotter/textilosaurus">Textilosaurus</a>
	is simple cross-platform UTF-8 text editor based on Qt and Scintilla.
    </p>
    <p>
	<a href="http://stefanstools.sourceforge.net/BowPad.html">BowPad</a>
	is a small and fast text editor with a modern ribbon user interface (Windows7 or later).
    </p>
    <p>
	<a href="http://studio.zerobrane.com">ZeroBrane Studio Lua IDE</a>
	is a lightweight Lua IDE with code completion, syntax highlighting, live
	coding, remote debugger, and code analyzer (Windows, OSX, and Linux).
    </p>
    <p>
	<a href="http://www.xml-buddy.com/">XML Validator Buddy</a>
	is an XML/JSON editor and XML validator for Windows.
    </p>
    <p>
	<a href="http://sciteco.sf.net/">SciTECO</a>
	is an advanced TECO dialect and interactive screen editor based on Scintilla.
    </p>
    <p>
	<a href="http://www.qgis.org/">Quantum GIS</a>
	is a user friendly Open Source Geographic Information System (GIS).
    </p>
    <p>
	<a href="https://gitorious.org/qgrinui">QGrinUI</a>
	searches for a regex within all relevant files in a directory and shows matches using
	SciTE through the director interface.
    </p>
    <p>
	<a href="http://foicica.com/textadept/">Textadept</a>
	is a ridiculously extensible cross-platform text editor for programmers written (mostly) in
	Lua using LPeg to handle the lexers.
    </p>
    <p>
	<a href="http://www.morphos-team.net/releasenotes/3.0">Scribble</a>
	is a text editor included in MorphOS.
    </p>
    <p>
	<a href="http://mysqlworkbench.org/">MySQL Workbench</a>
	is a cross-platform, visual database design, sql coding and administration tool.
    </p>
    <p>
	<a href="http://liveditor.com/index.php">LIVEditor</a>
	is for web front end coders editing html/css/js code.
    </p>
    <p>
	<a href="http://padre.perlide.org/">Padre</a>
	is a wxWidgets-based Perl IDE.
    </p>
    <p>
	<a href="http://www.manoscoder.gr/wintools/viewtopic.php?f=20&t=84">CoderStudio</a>
	is an IDE for plain C and Assembly programming similar to Visual Studio.
    </p>
    <p>
	<a href="http://www.sparxsystems.com/products/ea/index.html">Enterprise Architect</a>
	is a UML 2.1 analysis and design tool.
    </p>
    <p>
	<a href="https://launchpad.net/codeassistor">The CodeAssistor Editor</a>
	is a small and simple source code editor for MacOSX, Windows, and GTK/Linux.
    </p>
    <p>
	<a href="http://www.topwizprogramming.com/freecode_pbeditor.html">PBEditor</a>
	is a text editor for PowerBuilder.
    </p>
    <p>
	<a href="https://www.cryptool.org/en/">CrypTool</a>
	is an application for applying and analyzing cryptographic algorithms.
    </p>
    <p>
	<a href="http://code.google.com/p/fxite/">FXiTe</a>
	is an advanced cross-platform text editor built with the Fox GUI toolkit
	and the FXScintilla text widget.
    </p>
    <p>
	<a href="http://www.jabaco.org/">Jabaco</a>
	is a simple programming language with a Visual Basic like syntax.
    </p>
    <p>
	<a href="http://www.daansystems.com/lispide/">LispIDE</a>
	is a basic Lisp editor for Windows 2000, XP and Vista.
    </p>
    <p>
	<a href="https://www.assembla.com/wiki/show/FileWorkbench">File Workbench:</a>
	a file manager / text editor environment with Squirrel scripting.
    </p>
    <p>
	<a href="http://kephra.sf.net">Kephra</a>
	is a free, easy and comfortable cross-platform editor written in Perl.
    </p>
    <p>
	<a href="http://universalindent.sourceforge.net/">UniversalIndentGUI</a>
	is a cross platform GUI for several code formatters, beautifiers and indenters
	like GreatCode, AStyle (Artistic Styler), GNU Indent, BCPP and so on.
    </p>
    <p>
	<a href="http://elementaryreports.com/">Elementary Reports</a>
	is designed to reduce the time to compose detailed and professional primary school reports.
    </p>
    <p>
	<a href="http://stepaheadsoftware.com/products/vcw/vcw.htm">Visual Classworks</a>
	Visual class modeling and coding in C++ via 'live'
	UML style class diagrams.
    </p>
    <p>
	<a href="http://stepaheadsoftware.com/products/javelin/javelin.htm">Javelin</a>
	Visual Class modeling and coding in Java via 'live' UML style
	class diagrams.
    </p>
    <p>
	The <a href="http://www.adobe.com/devnet/bridge.html">ExtendScript Toolkit</a>
	is a development and debugging tool for JavaScript
	scripts included with Adobe CS3 Suites.
    </p>
    <p>
	<a href="https://tortoisesvn.net/">TortoiseSVN</a>
	is a Windows GUI client for the Subversion source control software.
    </p>
    <p>
	<a href="http://www.geany.org/">Geany</a>
	is a small and fast GTK2 based IDE, which has only a few dependencies from other packages.
    </p>
    <p>
	<a href="http://www.elliecomputing.com/products/merge_overview.asp">ECMerge</a>
	is a commercial graphical and batch diff / merge tool for Windows, Linux and Solaris
	(aiming to target all major platforms).
    </p>
    <p>
	<a href="http://pype.sourceforge.net/">PyPE</a>
	is an editor written in Python with the wxPython GUI toolkit.
    </p>
    <p>
	<a href="http://home.mweb.co.za/sd/sdonovan/sciboo.html">Sciboo</a>
	is an editor based on ScintillaNET.
    </p>
    <p>
	<a href="https://sourceforge.net/projects/tsct/">The Scite Config Tool</a>
	is a graphical user interface for changing SciTE properties files.
    </p>
    <p>
	<a href="http://totalcmd.net/plugring/SciLister.html">Scintilla Lister</a>
	is a plugin for Total Commander allowing viewing all documents with syntax highlighting
	inside Total Commander.
    </p>
    <p>
	<a href="http://chscite.sourceforge.net">ChSciTE</a>
	is a free IDE for C/C++ interpreter Ch. It runs cross platform.
	Ch is for cross-platform scripting, shell
	programming, 2D/3D plotting, numerical computing, and embedded
	scripting.
    </p>
    <p>
       <a href="http://codeblocks.org/">
       Code::Blocks</a> is an open source, cross platform free C++ IDE.
    </p>
    <p>
       <a href="https://notepad-plus-plus.org/">
       Notepad++</a> is a free source code editor under Windows.
    </p>
    <p>
       <a href="http://gubed.mccabe.nu/">
       Gubed</a> is a cross platform program to debug PHP scripts.
    </p>
    <p>
       <a href="http://www.lesser-software.com/lswdnl.htm">
       LSW DotNet-Lab</a> is a development environment for the .NET platform.
    </p>
    <p>
       <a href="https://github.com/dtrebilco/glintercept">
       GLIntercept</a> is an OpenGL function call interceptor that uses SciTE as a
       run-time shader editor.
    </p>
    <p>
       <a href="http://wxguide.sourceforge.net/indexedit.html">
       wyoEditor</a> is "A nice editor with a well designed and consistent look and feel".
    </p>
    <p>
       <a href="http://www.flos-freeware.ch/notepad2.html">
       Notepad2</a> is "Yet another Notepad replacement".
    </p>
    <p>
       <a href="http://pycrash.sourceforge.net/index.php?type=3">
       PyCrash Viewer</a> can examine crash dumps of Python programs.
    </p>
    <p>
       <a href="http://www.cabletest.com/en/featured_products_MPT2.aspx">
       MPT series Wire Analyzers</a> use Scintilla and SciTE.
    </p>
    <p>
       <a href="http://www.mygenerationsoftware.com">MyGeneration</a>
	is a .NET based code generator.
    </p>
    <p>
       <a href="http://cssed.sourceforge.net">CSSED</a>
	is a tiny GTK2 CSS editor.
    </p>
    <p>
       <a href="http://wxghostscript.sourceforge.net/">
        IdePS</a>
	is a free Integrated Development Environment for PostScript
    </p>
    <p>
       <a href="http://cute.sourceforge.net/">
        CUTE</a>
	is a user-friendly source code editor easily extended using Python.
    </p>
    <p>
       <a href="http://www.spaceblue.com/products/venis/index.html">
        Venis IX</a>,
	the Visual Environment for NSIS (Nullsoft Scriptable Install System).
    </p>
    <p>
       <a href="http://eric-ide.python-projects.org/">Eric3</a>
       is a Python IDE written using PyQt and QScintilla.
    </p>
    <p>
       <a href="http://www.computersciencelab.com/CppIde.htm">CPPIDE</a>
       is part of some commercial high-school oriented programming course software.
    </p>
    <p>
       <a href="http://www.blazingtools.com/is.html">Instant Source</a>
       is a commercial tool for looking at the HTML on web sites.
    </p>
    <p>
       <a href="http://www.codejoin.com/radon/">RAD.On++</a>
       is a free C++ Rapid Application Developer for Win32.
    </p>
    <p>
       <a href="http://wxbasic.sourceforge.net/">wxBasic</a> is an open source
       Basic interpreter that uses the wxWidgets toolkit. A small IDE is under construction.
    </p>
    <p>
       <a href="http://visual-mingw.sourceforge.net/">Visual MinGW</a> is an
       IDE for the MinGW compiler system.This runs on Windows with gcc.
    </p>
    <p>
       The <a href="http://archaeopteryx.com/">Wing IDE</a> is a
       complete integrated development environment for the Python programming
       language.
       Available on Intel based Linux and Windows and on MacOS X through XDarwin.
    </p>
    <p>
	<a href="http://www.spheredev.org/">Sphere</a>
	is 2D RPG engine with a development environment.
    </p>
    <p>
	<a href="http://gaiacrtn.free.fr/practical-ruby/index.html">Practical Ruby</a>
	is an IDE for Ruby on Windows.
    </p>
    <p>
	<a href="http://www.gnuenterprise.org/">GNUe</a>
	is a suite of tools and applications for solving the needs of the enterprise.
    </p>
    <p>
	<a href="http://silvercity.sourceforge.net/">SilverCity</a>
	is a lexing package that can provide lexical analysis for over 20 programming
	and markup languages.
    </p>
    <p>
	<a href="http://hapdebugger.sourceforge.net/">HAP Python Remote Debugger</a>
	is a Python debugger that can run on one Windows machine debugging a Python program running
	on either the same or another machine.
    </p>
    <p>
	<a href="http://sourceforge.net/projects/pycrust/">PyCrust</a> is an interactive
	Python shell based on wxPython.
    </p>
    <p>
	<a href="http://www.activestate.com/Products/Komodo/">Komodo</a>
	is a cross-platform multi-language development environment built
	as an application of Mozilla.
    </p>
    <p>
	<a href="http://llt.chez-alice.fr/">Filerx</a>
	is a project manager for SciTE on Windows.
	Open source and includes an implementation of SciTE's Director interface so
	will be of interest to others wanting to control SciTE.
    </p>
    <p>
	<a href="http://anjuta.org/">Anjuta</a>
	is an open source C/C++ IDE for Linux/GNOME.
    </p>
    <p>
       A <a href="https://www.burgaud.com">version of SciTE for Win32</a> enhanced
       with a tab control to allow easy movement between buffers.
       Go to the "Goodies" area on this site.
    </p>
    <p>
       <a href="http://suneido.com">
       Suneido</a> is an integrated application platform currently available for Win32 that includes an
       object-oriented language, client-server database, and user interface and reporting frameworks.
    </p>
    <p>
       <a href="http://www.allitis.com/agast/home.html">
       Agast</a> is an authoring system for adventure games which includes
       a customised version of SciTE.
    </p>
    <p>
       <a href="http://boa-constructor.sourceforge.net/">Boa Constructor</a> is a RAD GUI
       Building IDE for the wxWidgets cross platform platform. Written using wxPython with the
       wxStyledTextCtrl used as its editor.
    </p>
    <p>
       <a href="https://www.python.org/download/windows/">PythonWin</a>, a Win32 IDE for Python, uses
      Scintilla for both its editing and interactive windows.
    </p>
    <h3>
       Editing Components
    </h3>
    <p>
       <a href="https://codemirror.net/">CodeMirror</a>
       is a versatile text editor implemented in JavaScript for the browser.
    </p>
    <p>
       <a href="http://www.soft-gems.net/index.php/controls/unicodeeditor-formerly-unicode-syntax-editor">UniCodeEditor</a>
       is a Unicode aware syntax editor control for Delphi and C++ Builder.
    </p>
    <p>
       <a href="https://wiki.gnome.org/Projects/GtkSourceView">GtkSourceView</a>
	is a text widget that extends the standard GTK+ 2.x text widget and improves it
	by implementing syntax highlighting and other features typical of a source editor.
    </p>
    <p>
       <a href="http://aeditor.rubyforge.org/">AEditor</a>
       is a free source code editing component implemented in Ruby.
    </p>
    <p>
       <a href="http://www.actiprosoftware.com/products/controls/wpf/syntaxeditor">SyntaxEditor</a>
       is a commercial native .Net source code editing component.
    </p>
    <p>
       <a href="http://jedit.sourceforge.net/">jEdit</a> is a good Open Source syntax colouring
      editor written in and for Java.
    </p>
    <p>
       <a href="http://www.gtk.org/">GTK+</a>, the GIMP Toolkit, contains a rich text editing
      widget.<br />
       <a href="https://wiki.gnome.org/Apps/Gedit">Gedit</a> is an editor for GTK+/GNOME.<br />
    <!--
       <a href="http://www.daimi.au.dk/~mailund/gtk.html">GtkEditor</a> is a source code editing
      widget based on the GTK+ text widget.<br />
       <a href="http://gide.gdev.net/">gIDE</a> is an IDE based on GTK+.<br />
       <a href="http://www.bahnhof.se/~mikeh/linux_software.html">GtkExText</a> is a source code
      oriented text widget for GTK+.
    -->
    </p>
    <p>
       <a href="http://www.codeguru.com/">CodeGuru</a> has source code for several Win32 MFC based
      editors.
    </p>
    <a href="http://sourceforge.net/projects/synedit/">SynEdit</a> is a Win32 edit control written
    in Delphi.
    <p>
       <a href="http://www.tetradyne.com/srcvwax.htm">SourceView</a> is a commercial editing
      component for Win32.
    </p>
    <h3>
       Documents
    </h3>
    <p>
       <a href="http://www.finseth.com/craft/">The Craft of Text Editing</a>
       describes how EMACS works, <i>Craig A. Finseth</i>
    </p>
    <p>
       <a href="http://www.cs.cmu.edu/~wjh/papers/byte.html">Data Structures in a Bit-Mapped Text
      Editor</a>, <i>Wilfred J. Hanson</i>, Byte January 1987
    </p>
    <p>
       Text Editors: Algorithms and Architectures, <i>Ray Vald&eacute;s</i>, Dr. Dobbs Journal
      April 1993
    </p>
    <p>
       Macintosh User Interface Guidelines and TextEdit chapters of Inside Macintosh
    </p>
    <h3>
       Development Tools
    </h3>
    <p>
       Scintilla and SciTE were developed using the
       <a href="http://www.mingw.org/">Mingw version of GCC</a>.
    </p>
    <p>
       <a href="http://astyle.sourceforge.net/">AStyle</a> is a source code formatter for C++ and
      Java code. SciTE has an Indent command defined for .cxx files that uses AStyle.
    </p>
    <p>
       <a href="http://winmerge.org/">WinMerge</a> is an interactive diff / merge
       for Windows. I prefer code submissions in the form of source files rather than diffs and then run
       WinMerge over the files to work out how to merge.
    </p>
    <p>
       <a href="https://www.python.org">Python</a> is my favourite programming language. Scintilla
      was started after I tried to improve the editor built into <a
      href="https://www.python.org/download/windows/">PythonWin</a>, but was frustrated by the limitations of
      the Windows Richedit control which PythonWin used.
    </p>
    <p>
       <a href="http://www.cse.yorku.ca/~oz/">regex</a> is a public domain
       implementation of regular expression pattern matching used in Scintilla.
    </p>
    <p>
       Inspirational coding soundscapes by <a href="http://www.davidbridie.com/">David Bridie</a>.
    </p>
  </body>
</html>

