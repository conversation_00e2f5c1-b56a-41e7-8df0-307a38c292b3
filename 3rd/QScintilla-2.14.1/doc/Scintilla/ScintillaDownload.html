<?xml version="1.0"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta name="generator" content="HTML Tidy, see www.w3.org" />
    <meta name="generator" content="SciTE" />
    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>
      Download Scintilla
    </title>
  </head>
  <body bgcolor="#FFFFFF" text="#000000">
    <table bgcolor="#000000" width="100%" cellspacing="0" cellpadding="0" border="0">
      <tr>
        <td>
          <img src="SciTEIco.png" border="3" height="64" width="64" alt="Scintilla icon" />
        </td>
        <td>
          <a href="index.html" style="color:white;text-decoration:none"><font size="5">Download
          Scintilla</font></a>
        </td>
      </tr>
    </table>
    <table bgcolor="#CCCCCC" width="100%" cellspacing="0" cellpadding="8" border="0">
      <tr>
        <td>
          <font size="4"> <a href="https://www.scintilla.org/scintilla3101.zip">
	Windows</a>&nbsp;&nbsp;
	<a href="https://www.scintilla.org/scintilla3101.tgz">
          GTK+/Linux</a>&nbsp;&nbsp;
	</font>
        </td>
      </tr>
    </table>
    <h2>
       Download.
    </h2>
    <p>
       The <a href="License.txt">license</a> for using Scintilla or SciTE is similar to that of Python
      containing very few restrictions.
    </p>
    <h3>
       Release 3.10.1
    </h3>
    <h4>
       Source Code
    </h4>
       The source code package contains all of the source code for Scintilla but no binary
	executable code and is available in
       <ul>
       <li><a href="https://www.scintilla.org/scintilla3101.zip">zip format</a> (1600K) commonly used on Windows</li>
       <li><a href="https://www.scintilla.org/scintilla3101.tgz">tgz format</a> (1400K) commonly used on Linux and compatible operating systems</li>
       </ul>
       Instructions for building on both Windows and Linux are included in the readme file.
    <h4>
       Windows Executable Code
    </h4>
       There is no download available containing only the Scintilla DLL.
       However, it is included in the <a href="SciTEDownload.html">SciTE
       executable full download</a> as SciLexer.DLL.
    <p>
       <a href="SciTEDownload.html">SciTE</a> is a good demonstration of Scintilla.
    </p>
    <p>
       Previous versions can be downloaded from the <a href="ScintillaHistory.html">history
      page</a>.
    </p>
  </body>
</html>
