<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta name="generator" content="HTML Tidy, see www.w3.org" />
    <meta name="generator" content="SciTE" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>
      Scintilla and SciTE
    </title>
    <style type="text/css">
        table {
            border-collapse: collapse;
            font-size: 80%;
        }
        td {
            xborder: 1px solid #1F1F1F;
            padding: 0px 4px;
        }
    </style>
  </head>
  <body bgcolor="#FFFFFF" text="#000000">
    <table bgcolor="#000000" width="100%" cellspacing="0" cellpadding="0" border="0">
      <tr>
        <td>
          <img src="SciTEIco.png" border="3" height="64" width="64" alt="Scintilla icon" />
        </td>
        <td>
          <a href="index.html" style="color:white;text-decoration:none"><font size="5">Scintilla
          and SciTE</font></a>
        </td>
      </tr>
    </table>
    <h2>
       History of Scintilla and SciTE
    </h2>
    <h3>
       Contributors
    </h3>
    <p>
       Thanks to all the people that have contributed patches, bug reports and suggestions.
    </p>
    <p>
       Source code and documentation have been contributed by
    </p>
    <table>
      <tr>
	<td>Atsuo Ishimoto</td>
	<td>Mark Hammond</td>
	<td>Francois Le Coguiec</td>
	<td>Dale Nagata</td>
      </tr><tr>
	<td>Ralf Reinhardt</td>
	<td>Philippe Lhoste</td>
	<td>Andrew McKinlay</td>
	<td>Stephan R. A. Deibel</td>
      </tr><tr>
	<td>Hans Eckardt</td>
	<td>Vassili Bourdo</td>
	<td>Maksim Lin</td>
	<td>Robin Dunn</td>
      </tr><tr>
	<td>John Ehresman</td>
	<td>Steffen Goeldner</td>
	<td>Deepak S.</td>
	<td><a href="http://www.develop.com">DevelopMentor</a></td>
      </tr><tr>
	<td>Yann Gaillard</td>
	<td>Aubin Paul</td>
	<td>Jason Diamond</td>
	<td>Ahmad Baitalmal</td>
      </tr><tr>
	<td>Paul Winwood</td>
	<td>Maxim Baranov</td>
	<td>Ragnar Højland</td>
	<td>Christian Obrecht</td>
      </tr><tr>
	<td>Andreas Neukoetter</td>
	<td>Adam Gates</td>
	<td>Steve Lhomme</td>
	<td>Ferdinand Prantl</td>
      </tr><tr>
	<td>Jan Dries</td>
	<td>Markus Gritsch</td>
	<td>Tahir Karaca</td>
	<td>Ahmad Zawawi</td>
      </tr><tr>
	<td>Laurent le Tynevez</td>
	<td>Walter Braeu</td>
	<td>Ashley Cambrell</td>
	<td>Garrett Serack</td>
      </tr><tr>
	<td>Holger Schmidt</td>
	<td><a href="http://www.activestate.com">ActiveState</a></td>
	<td>James Larcombe</td>
	<td>Alexey Yutkin</td>
      </tr><tr>
	<td>Jan Hercek</td>
	<td>Richard Pecl</td>
	<td>Edward K. Ream</td>
	<td>Valery Kondakoff</td>
      </tr><tr>
	<td>Smári McCarthy</td>
	<td>Clemens Wyss</td>
	<td>Simon Steele</td>
	<td>Serge A. Baranov</td>
      </tr><tr>
	<td>Xavier Nodet</td>
	<td>Willy Devaux</td>
	<td>David Clain</td>
	<td>Brendon Yenson</td>
      </tr><tr>
	<td><a href="http://www.baanboard.com">Vamsi Potluru</a></td>
	<td>Praveen Ambekar</td>
	<td>Alan Knowles</td>
	<td>Kengo Jinno</td>
      </tr><tr>
	<td>Valentin Valchev</td>
	<td>Marcos E. Wurzius</td>
	<td>Martin Alderson</td>
	<td>Robert Gustavsson</td>
      </tr><tr>
	<td>José Fonseca</td>
	<td>Holger Kiemes</td>
	<td>Francis Irving</td>
	<td>Scott Kirkwood</td>
      </tr><tr>
	<td>Brian Quinlan</td>
	<td>Ubi</td>
	<td>Michael R. Duerig</td>
	<td>Deepak T</td>
      </tr><tr>
	<td>Don Paul Beletsky</td>
	<td>Gerhard Kalab</td>
	<td>Olivier Dagenais</td>
	<td>Josh Wingstrom</td>
      </tr><tr>
	<td>Bruce Dodson</td>
	<td>Sergey Koshcheyev</td>
	<td>Chuan-jian Shen</td>
	<td>Shane Caraveo</td>
      </tr><tr>
	<td>Alexander Scripnik</td>
	<td>Ryan Christianson</td>
	<td>Martin Steffensen</td>
	<td>Jakub Vrána</td>
      </tr><tr>
	<td>The Black Horus</td>
	<td>Bernd Kreuss</td>
	<td>Thomas Lauer</td>
	<td>Mike Lansdaal</td>
      </tr><tr>
	<td>Yukihiro Nakai</td>
	<td>Jochen Tucht</td>
	<td>Greg Smith</td>
	<td>Steve Schoettler</td>
      </tr><tr>
	<td>Mauritius Thinnes</td>
	<td>Darren Schroeder</td>
	<td>Pedro Guerreiro</td>
	<td>Steven te Brinke</td>
      </tr><tr>
	<td>Dan Petitt</td>
	<td>Biswapesh Chattopadhyay</td>
	<td>Kein-Hong Man</td>
	<td>Patrizio Bekerle</td>
      </tr><tr>
	<td>Nigel Hathaway</td>
	<td>Hrishikesh Desai</td>
	<td>Sergey Puljajev</td>
	<td>Mathias Rauen</td>
      </tr><tr>
	<td><a href="http://www.spaceblue.com">Angelo Mandato</a></td>
	<td>Denis Sureau</td>
	<td>Kaspar Schiess</td>
	<td>Christoph Hösler</td>
      </tr><tr>
	<td>João Paulo F Farias</td>
	<td>Ron Schofield</td>
	<td>Stefan Wosnik</td>
	<td>Marius Gheorghe</td>
      </tr><tr>
	<td>Naba Kumar</td>
	<td>Sean O'Dell</td>
	<td>Stefanos Togoulidis</td>
	<td>Hans Hagen</td>
      </tr><tr>
	<td>Jim Cape</td>
	<td>Roland Walter</td>
	<td>Brian Mosher</td>
	<td>Nicholas Nemtsev</td>
      </tr><tr>
	<td>Roy Wood</td>
	<td>Peter-Henry Mander</td>
	<td>Robert Boucher</td>
	<td>Christoph Dalitz</td>
      </tr><tr>
	<td>April White</td>
	<td>S. Umar</td>
	<td>Trent Mick</td>
	<td>Filip Yaghob</td>
      </tr><tr>
	<td>Avi Yegudin</td>
	<td>Vivi Orunitia</td>
	<td>Manfred Becker</td>
	<td>Dimitris Keletsekis</td>
      </tr><tr>
	<td>Yuiga</td>
	<td>Davide Scola</td>
	<td>Jason Boggs</td>
	<td>Reinhold Niesner</td>
      </tr><tr>
	<td>Jos van der Zande</td>
	<td>Pescuma</td>
	<td>Pavol Bosik</td>
	<td>Johannes Schmid</td>
      </tr><tr>
	<td>Blair McGlashan</td>
	<td>Mikael Hultgren</td>
	<td>Florian Balmer</td>
	<td>Hadar Raz</td>
      </tr><tr>
	<td>Herr Pfarrer</td>
	<td>Ben Key</td>
	<td>Gene Barry</td>
	<td>Niki Spahiev</td>
      </tr><tr>
	<td>Carsten Sperber</td>
	<td>Phil Reid</td>
	<td>Iago Rubio</td>
	<td>Régis Vaquette</td>
      </tr><tr>
	<td>Massimo Corà</td>
	<td>Elias Pschernig</td>
	<td>Chris Jones</td>
	<td>Josiah Reynolds</td>
      </tr><tr>
	<td>Robert Roessler <a href="http://www.rftp.com">rftp.com</a></td>
	<td>Steve Donovan</td>
	<td>Jan Martin Pettersen</td>
	<td>Sergey Philippov</td>
      </tr><tr>
	<td>Borujoa</td>
	<td>Michael Owens</td>
	<td>Franck Marcia</td>
	<td>Massimo Maria Ghisalberti</td>
      </tr><tr>
	<td>Frank Wunderlich</td>
	<td>Josepmaria Roca</td>
	<td>Tobias Engvall</td>
	<td>Suzumizaki Kimitaka</td>
      </tr><tr>
	<td>Michael Cartmell</td>
	<td>Pascal Hurni</td>
	<td>Andre</td>
	<td>Randy Butler</td>
      </tr><tr>
	<td>Georg Ritter</td>
	<td>Michael Goffioul</td>
	<td>Ben Harper</td>
	<td>Adam Strzelecki</td>
      </tr><tr>
	<td>Kamen Stanev</td>
	<td>Steve Menard</td>
	<td>Oliver Yeoh</td>
	<td>Eric Promislow</td>
      </tr><tr>
	<td>Joseph Galbraith</td>
	<td>Jeffrey Ren</td>
	<td>Armel Asselin</td>
	<td>Jim Pattee</td>
      </tr><tr>
	<td>Friedrich Vedder</td>
	<td>Sebastian Pipping</td>
	<td>Andre Arpin</td>
	<td>Stanislav Maslovski</td>
      </tr><tr>
	<td>Martin Stone</td>
	<td>Fabien Proriol</td>
	<td>mimir</td>
	<td>Nicola Civran</td>
      </tr><tr>
	<td>Snow</td>
	<td>Mitchell Foral</td>
	<td>Pieter Holtzhausen</td>
	<td>Waldemar Augustyn</td>
      </tr><tr>
	<td>Jason Haslam</td>
	<td>Sebastian Steinlechner</td>
	<td>Chris Rickard</td>
	<td>Rob McMullen</td>
      </tr><tr>
	<td>Stefan Schwendeler</td>
	<td>Cristian Adam</td>
	<td>Nicolas Chachereau</td>
	<td>Istvan Szollosi</td>
      </tr><tr>
	<td>Xie Renhui</td>
	<td>Enrico Tröger</td>
	<td>Todd Whiteman</td>
	<td>Yuval Papish</td>
      </tr><tr>
	<td>instanton</td>
	<td>Sergio Lucato</td>
	<td>VladVRO</td>
	<td>Dmitry Maslov</td>
      </tr><tr>
	<td>chupakabra</td>
	<td>Juan Carlos Arevalo Baeza</td>
	<td>Nick Treleaven</td>
	<td>Stephen Stagg</td>
      </tr><tr>
	<td>Jean-Paul Iribarren</td>
	<td>Tim Gerundt</td>
	<td>Sam Harwell</td>
	<td>Boris</td>
      </tr><tr>
	<td>Jason Oster</td>
	<td>Gertjan Kloosterman</td>
	<td>alexbodn</td>
	<td>Sergiu Dotenco</td>
      </tr><tr>
	<td>Anders Karlsson</td>
	<td>ozlooper</td>
	<td>Marko Njezic</td>
	<td>Eugen Bitter</td>
      </tr><tr>
	<td>Christoph Baumann</td>
	<td>Christopher Bean</td>
	<td>Sergey Kishchenko</td>
	<td>Kai Liu</td>
      </tr><tr>
	<td>Andreas Rumpf</td>
	<td>James Moffatt</td>
	<td>Yuzhou Xin</td>
	<td>Nic Jansma</td>
      </tr><tr>
	<td>Evan Jones</td>
	<td>Mike Lischke</td>
	<td>Eric Kidd</td>
	<td>maXmo</td>
      </tr><tr>
	<td>David Severwright</td>
	<td>Jon Strait</td>
	<td>Oliver Kiddle</td>
	<td>Etienne Girondel</td>
      </tr><tr>
	<td>Haimag Ren</td>
	<td>Andrey Moskalyov</td>
	<td>Xavi</td>
	<td>Toby Inkster</td>
      </tr><tr>
	<td>Eric Forgeot</td>
	<td>Colomban Wendling</td>
	<td>Neo</td>
	<td>Jordan Russell</td>
      </tr><tr>
	<td>Farshid Lashkari</td>
	<td>Sam Rawlins</td>
	<td>Michael Mullin</td>
	<td>Carlos SS</td>
      </tr><tr>
	<td>vim</td>
	<td>Martial Demolins</td>
	<td>Tino Weinkauf</td>
	<td>Jérôme Laforge</td>
      </tr><tr>
	<td>Udo Lechner</td>
	<td>Marco Falda</td>
	<td>Dariusz Knociński</td>
	<td>Ben Fisher</td>
      </tr><tr>
	<td>Don Gobin</td>
	<td>John Yeung</td>
	<td>Adobe</td>
	<td>Elizabeth A. Irizarry</td>
      </tr><tr>
	<td>Mike Schroeder</td>
	<td>Morten MacFly</td>
	<td>Jaime Gimeno</td>
	<td>Thomas Linder Puls</td>
      </tr><tr>
	<td>Artyom Zuikov</td>
	<td>Gerrit</td>
	<td>Occam's Razor</td>
	<td>Ben Bluemel</td>
      </tr><tr>
	<td>David Wolfendale</td>
	<td>Chris Angelico</td>
	<td>Marat Dukhan</td>
	<td>Stefan Weil</td>
      </tr><tr>
	<td>Rex Conn</td>
	<td>Ross McKay</td>
	<td>Bruno Barbieri</td>
	<td>Gordon Smith</td>
      </tr><tr>
	<td>dimitar</td>
	<td>Sébastien Granjoux</td>
	<td>zeniko</td>
	<td>James Ribe</td>
      </tr><tr>
	<td>Markus Nißl</td>
	<td>Martin Panter</td>
	<td>Mark Yen</td>
	<td>Philippe Elsass</td>
      </tr><tr>
	<td>Dimitar Zhekov</td>
	<td>Fan Yang</td>
	<td>Denis Shelomovskij</td>
	<td>darmar</td>
      </tr><tr>
	<td>John Vella</td>
	<td>Chinh Nguyen</td>
	<td>Sakshi Verma</td>
	<td>Joel B. Mohler</td>
      </tr><tr>
	<td>Isiledhel</td>
	<td>Vidya Wasi</td>
	<td>G. Hu</td>
	<td>Byron Hawkins</td>
      </tr><tr>
	<td>Alpha</td>
	<td>John Donoghue</td>
	<td>kudah</td>
	<td>Igor Shaula</td>
      </tr><tr>
	<td>Pavel Bulochkin</td>
	<td>Yosef Or Boczko</td>
	<td>Brian Griffin</td>
	<td>Özgür Emir</td>
      </tr><tr>
	<td>Neomi</td>
	<td>OmegaPhil</td>
	<td>SiegeLord</td>
	<td>Erik</td>
      </tr><tr>
	<td>TJF</td>
	<td>Mark Robinson</td>
	<td>Thomas Martitz</td>
	<td>felix</td>
      </tr><tr>
	<td>Christian Walther</td>
	<td>Ebben</td>
	<td>Robert Gieseke</td>
	<td>Mike M</td>
      </tr><tr>
	<td>nkmathew</td>
	<td>Andreas Tscharner</td>
	<td>Lee Wilmott</td>
	<td>johnsonj</td>
      </tr><tr>
	<td>Vicente</td>
	<td>Nick Gravgaard</td>
	<td>Ian Goldby</td>
	<td>Holger Stenger</td>
      </tr><tr>
	<td>danselmi</td>
	<td>Mat Berchtold</td>
	<td>Michael Staszewski</td>
	<td>Baurzhan Muftakhidinov</td>
      </tr><tr>
	<td>Erik Angelin</td>
	<td>Yusuf Ramazan Karagöz</td>
	<td>Markus Heidelberg</td>
	<td>Joe Mueller</td>
      </tr><tr>
	<td>Mika Attila</td>
	<td>JoMazM</td>
	<td>Markus Moser</td>
	<td>Stefan Küng</td>
      </tr><tr>
	<td>Jiří Techet</td>
	<td>Jonathan Hunt</td>
	<td>Serg Stetsuk</td>
	<td>Jordan Jueckstock</td>
      </tr><tr>
	<td>Yury Dubinsky</td>
	<td>Sam Hocevar</td>
	<td>Luyomi</td>
	<td>Matt Gilarde</td>
      </tr><tr>
	<td>Mark C</td>
	<td>Johannes Sasongko</td>
	<td>fstirlitz</td>
	<td>Robin Haberkorn</td>
      </tr><tr>
	<td>Pavel Sountsov</td>
	<td>Dirk Lorenzen</td>
	<td>Kasper B. Graversen</td>
	<td>Chris Mayo</td>
      </tr><tr>
	<td>Van de Bugger</td>
	<td>Tse Kit Yam</td>
	<td><a href="https://www.smartsharesystems.com/">SmartShare Systems</a></td>
	<td>Morten Brørup</td>
      </tr><tr>
	<td>Alexey Denisov</td>
	<td>Justin Dailey</td>
	<td>oirfeodent</td>
	<td>A-R-C-A</td>
      </tr><tr>
	<td>Roberto Rossi</td>
	<td>Kenny Liu</td>
	<td>Iain Clarke</td>
	<td>desto</td>
      </tr><tr>
	<td>John Flatness</td>
	<td>Thorsten Kani</td>
	<td>Bernhard M. Wiedemann</td>
	<td>Baldur Karlsson</td>
      </tr><tr>
	<td>Martin Kleusberg</td>
	<td>Jannick</td>
	<td>Zufu Liu</td>
	<td>Simon Sobisch</td>
      </tr><tr>
	<td>Georger Araújo</td>
	<td>Dimitar Radev</td>
	<td>Gunter Königsmann</td>
	<td>Nicholai Benalal</td>
      </tr><tr>
	<td>Andreas Rönnquist</td>
	<td>Henrik Hank</td>
	<td>Luke Rasmussen</td>
      </tr><tr>
	<td>maboroshin</td>
	<td>Gokul Krishnan</td>
    </tr>
    </table>
    <p>
       Images used in GTK+ version
    </p>
    <ul>
      <li>
        <a href="http://sourceforge.net/projects/icon-collection/">
        Icons</a> Copyright(C) 1998 by Dean S. Jones<br />
      </li>
    </ul>
	<h3>
       <a href="https://sourceforge.net/projects/scintilla/files/scintilla/3.10.1/scintilla3101.zip/download">Release 3.10.1</a>
    </h3>
    <ul>
	<li>
	Released 31 October 2018.
	</li>
	<li>
	Add SCI_SETCOMMANDEVENTS API to allow turning off command events as they
	can be a significant performance cost.
	</li>
	<li>
	Optional indexing of line starts in UTF-8 documents by UTF-32 code points and UTF-16 code units added.
	This can improve performance for clients that provide UTF-32 or UTF-16 interfaces or that need to interoperate
	with UTF-32 or UTF-16 components.
	</li>
	<li>
	Lexers added for SAS and Stata.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1185/">Feature #1185.</a>
	</li>
	<li>
	Improve efficiency of idle wrapping by wrapping in blocks as large as possible while
	still remaining responsive.
	</li>
	<li>
	Updated case conversion and character categories to Unicode 11.
	</li>
	<li>
	C++ lexer fixes evaluation of "#elif".
	<a href="https://sourceforge.net/p/scintilla/bugs/2045/">Bug #2045</a>.
	</li>
	<li>
	Markdown lexer fixes highlighting of non-ASCII characters in links.
	</li>
	<li>
	SCI_MARKERADD returns -1 for invalid lines as documented instead of 0.
	<a href="https://sourceforge.net/p/scintilla/bugs/2051/">Bug #2051</a>.
	</li>
	<li>
	Shell folder folds "if", "do", and "case".
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1144/">Feature #1144.</a>
	</li>
	<li>
	Fix margin cursor on Cocoa to point more accurately.
	</li>
	<li>
	Updated ConTeXt Lua LPeg lexer.
	</li>
	<li>
	Improved folding of traditionally single elements in HTML Lua LPeg lexer.
	</li>
	<li>
	Improved accuracy of Markdown Lua LPeg lexer.
	</li>
    </ul>
    <h3>
		<a href="https://sourceforge.net/projects/scintilla/files/scintilla/3.10.0/scintilla3100.zip/download">Release 3.10.0</a>
	</h3>
	<ul>
	<li>
	Released 30 June 2018.
	</li>
	<li>
	Add experimental SC_DOCUMENTOPTION_TEXT_LARGE option to accomodate documents larger than
	2 GigaBytes.
	</li>
	<li>
	The platform layer interface has changed with the removal of the ElapsedTime
	class in favor of C++11's chrono.
	</li>
	<li>
	Additional print option SC_PRINT_SCREENCOLOURS prints with the same colours used on screen
	including line numbers.
	</li>
	<li>
	INDIC_GRADIENT and INDIC_GRADIENTCENTRE indicator types added.
	INDIC_GRADIENT starts with a specified colour and alpha at top of line and fades
	to fully transparent at bottom.
	INDIC_GRADIENTCENTRE starts with a specified colour and alpha at centre of line and fades
	to fully transparent at top and bottom.
	</li>
	<li>
	Wrap indent mode SC_WRAPINDENT_DEEPINDENT added which indents two tabs from previous line.
	</li>
	<li>
	Indicators are drawn for line end characters when displayed.
	</li>
	<li>
	Most invalid bytes in DBCS encodings are displayed as blobs to make problems clear
	and ensure something is shown.
	</li>
	<li>
	Curses platform fixed potential crash with newer versions of ncurses.
	</li>
	<li>
	Diff lexer adds styles for diffs containing patches.
	</li>
	<li>
	Regular expression crash fixed on macOS when linking to libstdc++.
	</li>
	<li>
	EDIFACT lexer adds property lexer.edifact.highlight.un.all to highlight all UN* segments.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1166/">Feature #1166.</a>
	</li>
	<li>
	Fortran folder understands "change team" and "endteam".
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1216/">Feature #1216.</a>
	</li>
	<li>
	Markdown Lua LPeg lexer fixes incorrect highlighting of markdown lists.
	</li>
	<li>
	Fixed child Lua LPeg lexers that embed themselves into parents. Also fixed proxy lexers.
	</li>
	<li>
	Fixed legacy Lua LPeg lexers with custom fold functions.
	</li>
	<li>
	C# Lua LPeg lexer highlights "var" keyword.
	</li>
	<li>
	Set the last X chosen when SCI_REPLACESEL called to ensure macros work
	when text insertion followed by caret up or down.
	</li>
	<li>
	Bugs fixed in regular expression searches in Scintilla where some matches did not occur in an
	effort to avoid infinite loops when replacing on empty matches like "^" and "$".
	Applications should always handle empty matches in a way that avoids infinite loops, commonly
	by incrementing the search position after replacing an empty match.
	SciTE fixes a bug where replacing "^" always matched on the first line even when it was an
	"in selection" replace and the selection started after the line start.
	</li>
	<li>
	Crashes fixed on macOS for invalid DBCS characters when dragging text,
	changing case of text, case-insensitive searching, and retrieving text as UTF-8.
	</li>
	<li>
	Regular expression crash fixed on macOS when linking to libstdc++.
	</li>
	<li>
	On Win32, a new file, ScintillaDLL.cxx, provides
	the DllMain function required for a stand-alone Scintilla DLL. Build and project files should include this
	file when producing a DLL and omit it when producing a static library or linking Scintilla statically.
	The STATIC_BUILD preprocessor symbol is no longer used.
	</li>
	<li>
	On Win32, Direct2D support is no longer automatically detected during build.
	DISABLE_D2D may still be defined to remove Direct2D features.
	</li>
	<li>
	In some cases, invalid UTF-8 is handled in a way that is a little friendlier.
	For example, when copying to the clipboard on Windows, an invalid lead byte will be copied as the
	equivalent ISO 8859-1 character and will not hide the following byte.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1211/">Feature #1211.</a>
	</li>
    </ul>
    <h3>
       <a href="https://sourceforge.net/projects/scintilla/files/scintilla/3.8.0/scintilla380.zip/download">Release 3.8.0</a>
    </h3>
    <ul>
	<li>
	Released 29 Mar 2018.
	</li>
	<li>
	Lua can be used to write lexers with the new, optional, <a href="LPegLexer.html">LPeg lexer</a>.
	</li>
	<li>
	New curses platform support.
	</li>
	<li>
	Support dropped for GTK+ versions before 2.24.
	</li>
	<li>
	A new lexer interface ILexerWithMetaData with additional style metadata methods was added.
	</li>
	<li>
	The platform layer interface has changed with unused methods removed, a new mechanism for
	reporting events, removal of methods that take individual keyboard modifiers, and removal of old timer methods.
	</li>
	<li>
	<a href="StyleMetadata.html">Style metadata</a> may be retrieved from lexers that support this through the SCI_GETNAMEDSTYLES, SCI_NAMEOFSTYLE,
	SCI_TAGSOFSTYLE, and SCI_DESCRIPTIONOFSTYLE APIs.
	</li>
	<li>
	The default encoding in Scintilla is UTF-8.
	</li>
	<li>
	An SCN_AUTOCSELECTIONCHANGE notification is sent when items are highlighted in an autocompletion or user list.
	</li>
	<li>
	The data parameter to ILoader::AddData made const.
	<a href="https://sourceforge.net/p/scintilla/bugs/1955/">Bug #1955</a>.
	</li>
	<li>
	The ILoader interface is defined in its own header ILoader.h as it is not
	related to lexing so doesn't belong in ILexer.h.
	</li>
	<li>
	The Scintilla namespace is always active for internal symbols and for the lexer and document interfaces.
	</li>
	<li>
	SCI_CREATEDOCUMENT adds a bytes argument to allocate memory for an initial size.
	SCI_CREATELOADER and SCI_CREATEDOCUMENT add a documentOption argument to
	allow choosing different document capabilities.
	</li>
	<li>
	Add SC_DOCUMENTOPTION_STYLES_NONE option to stop allocating memory for styles.
	</li>
	<li>
	Add SCI_GETMOVEEXTENDSSELECTION to allow applications to add more
	complex selection commands.
	</li>
	<li>
	For rectangular selections, pressing Home or End now moves the caret to the Home or End
	position instead of the limit of the rectangular selection.
	</li>
	<li>
	On Win32, the standard makefiles build a libscintilla static library as well as the existing dynamic libraries.
	</li>
	<li>
	Fix move-extends-selection mode for rectangular and line selections.
	</li>
	<li>
	On GTK+, change lifetime of selection widget to avoid runtime warnings.
	</li>
	<li>
	Fix building on Mingw/MSYS to perform file copies and deletions.
	<a href="https://sourceforge.net/p/scintilla/bugs/1993/">Bug #1993</a>.
	</li>
	<li>
	Lexer added for the Maxima computer algebra language.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1210/">Feature #1210.</a>
	</li>
	<li>
	Fix hang in Lua lexer when lexing a label upto the terminating "::".
	<a href="https://sourceforge.net/p/scintilla/bugs/1999/">Bug #1999</a>.
	</li>
	<li>
	Lua lexer matches identifier chains with dots and colons.
	<a href="https://sourceforge.net/p/scintilla/bugs/1952/">Bug #1952</a>.
	</li>
	<li>
	Fix HTML lexer handling of Django so that nesting a  &#123;&#123; &#125;&#125; or &#123;% %&#125;
	Django tag inside of a &#123;# #&#125; Django comment does not break highlighting of rest of file
	</li>
	<li>
	The Matlab folder now treats "while" as a fold start.
	<a href="https://sourceforge.net/p/scintilla/bugs/1985/">Bug #1985</a>.
	</li>
	<li>
	Improve VHDL lexer's handling of character literals and escape characters in strings.
	</li>
	<li>
	The Baan lexer checks that matches to 3rd set of keywords are function calls and leaves as identifiers if not.
	Baan lexer and folder support #context_on / #context_off preprocessor feature.
	</li>
	<li>
	The C++ lexer improved preprocessor conformance.<br />
	Default value of 0 for undefined preprocessor symbols.<br />
	#define A is treated as #define A 1.<br />
	"defined A" removes "A" before replacing "defined" with value.
	<a href="https://sourceforge.net/p/scintilla/bugs/1966/">Bug #1966</a>.
	</li>
	<li>
	The Python folder treats triple-quoted f-strings like triple-quoted strings.
	<a href="https://sourceforge.net/p/scintilla/bugs/1977/">Bug #1977</a>.
	</li>
	<li>
	The SQL lexer uses sql.backslash.escapes for double quoted strings.
	<a href="https://sourceforge.net/p/scintilla/bugs/1968/">Bug #1968</a>.
	</li>
	<li>
	The Matlab lexer treats 'end' as a number rather than a keyword when used as a index.
	This also stops incorrect folding.
	<a href="https://sourceforge.net/p/scintilla/bugs/1951/">Bug #1951</a>.
	</li>
	<li>
	The Matlab folder implements "fold", "fold.comment", and "fold.compact" properties.
	<a href="https://sourceforge.net/p/scintilla/bugs/1965/">Bug #1965</a>.
	</li>
	<li>
	The Rust lexer recognizes 'usize' numeric literal suffixes.
	<a href="https://sourceforge.net/p/scintilla/bugs/1919/">Bug #1919</a>.
	</li>
	<li>
	Minor undefined behaviour fixed.
	<a href="https://sourceforge.net/p/scintilla/bugs/1978">Bug #1978</a>.
	</li>
	<li>
	Fix double tap word selection on Windows 10 1709 Fall Creators Update.
	<a href="https://sourceforge.net/p/scintilla/bugs/1983/">Bug #1983</a>.
	</li>
	<li>
	Fix closing autocompletion lists on Cocoa for macOS 10.13 where the window
	was emptying but staying visible.
	<a href="https://sourceforge.net/p/scintilla/bugs/1981/">Bug #1981</a>.
	</li>
	<li>
	Fix Cocoa hang when Scintilla loaded from SMB share on macOS 10.13.
	<a href="https://sourceforge.net/p/scintilla/bugs/1979/">Bug #1979</a>.
	</li>
	<li>
	On Cocoa, improve scrolling on macOS 10.12.
	<a href="https://sourceforge.net/p/scintilla/bugs/1885">Bug #1885</a>.
	</li>
	<li>
	On Cocoa, fix line selection by clicking in the margin when scrolled.
	<a href="https://sourceforge.net/p/scintilla/bugs/1971">Bug #1971</a>.
	</li>
	<li>
	Ensure redraw when application changes overtype mode so caret change visible even when not blinking.
	Notify application with SC_UPDATE_SELECTION when overtype changed - previously
	sent SC_UPDATE_CONTENT.
	</li>
	<li>
	Fix drawing failure when in wrap mode for delete to start/end of line which
	affects later lines but did not redraw them.
	Also fixed drawing for wrap mode on GTK+ 2.x.
	<a href="https://sourceforge.net/p/scintilla/bugs/1949/">Bug #1949</a>.
	</li>
	<li>
	On GTK+ fix drawing problems including incorrect scrollbar redrawing and flickering of text.
	<a href="https://sourceforge.net/p/scintilla/bugs/1876">Bug #1876</a>.
	</li>
	<li>
	On Linux, both for GTK+ and Qt, the default modifier key for rectangular selection is now Alt.
	This is the same as Windows and macOS.
	This was changed from Ctrl as window managers are less likely to intercept Alt+Drag for
	moving windows than in the past.
	</li>
	<li>
	On Cocoa, fix doCommandBySelector but avoid double effect of 'delete'
	key.
	<a href="https://sourceforge.net/p/scintilla/bugs/1958">Bug #1958</a>.
	</li>
	<li>
	On Qt, the updateUi signal includes the 'updated' flags.
	No updateUi signal is sent for focus in events.
	These changes make Qt behave more like the other platforms.
	</li>
	<li>
	On Qt, dropping files on Scintilla now fires the SCN_URIDROPPED notification
	instead of inserting text.
	</li>
	<li>
	On Qt, focus changes send the focusChanged signal.
	<a href="https://sourceforge.net/p/scintilla/bugs/1957/">Bug #1957</a>.
	</li>
	<li>
	On Qt, mouse tracking is reenabled when the window is reshown.
	<a href="https://sourceforge.net/p/scintilla/bugs/1948/">Bug #1948</a>.
	</li>
	<li>
	On Windows, the DirectWrite modes SC_TECHNOLOGY_DIRECTWRITEDC and
	SC_TECHNOLOGY_DIRECTWRITERETAIN are no longer provisional.
	</li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/scintilla376.zip">Release 3.7.6</a>
    </h3>
    <ul>
	<li>
	Released 8 August 2017.
	</li>
	<li>
	This is the first release of the
	<a href="https://www.scintilla.org/LongTermDownload.html">long term branch</a>
	which avoids using features from C++14 or later in order to support older systems.
	</li>
	<li>
	The Baan lexer correctly highlights numbers when followed by an operator.
	</li>
	<li>
	On Cocoa, fix a bug with retrieving encoded bytes.
	</li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/scite375.zip">Release 3.7.5</a>
    </h3>
    <ul>
	<li>
	Released 26 May 2017.
	</li>
	<li>
	Support dropped for Microsoft Visual C++ 2013 due to increased use of C++11 features.
	</li>
	<li>
	Added a caret line frame as an alternative visual for highlighting the caret line.
	</li>
	<li>
	Added "Reverse Selected Lines" feature.
	</li>
	<li>
	SciTE adds "Select All Bookmarks" command.
	</li>
	<li>
	SciTE adds a save.path.suggestion setting to suggest a file name when saving an
	unnamed buffer.
	</li>
	<li>
	Updated case conversion and character categories to Unicode 9.
	</li>
	<li>
	The Baan lexer recognizes numeric literals in a more compliant manner including
	hexadecimal numbers and exponentials.
	</li>
	<li>
	The Bash lexer recognizes strings in lists in more cases.
	<a href="https://sourceforge.net/p/scintilla/bugs/1944/">Bug #1944</a>.
	</li>
	<li>
	The Fortran lexer recognizes a preprocessor line after a line continuation &amp;.
	<a href="https://sourceforge.net/p/scintilla/bugs/1935/">Bug #1935</a>.
	</li>
	<li>
	The Fortran folder can fold comments.
	<a href="https://sourceforge.net/p/scintilla/bugs/1936/">Bug #1936</a>.
	</li>
	<li>
	The PowerShell lexer recognizes escaped quotes in strings.
	<a href="https://sourceforge.net/p/scintilla/bugs/1929/">Bug #1929</a>.
	</li>
	<li>
	The Python lexer recognizes identifiers more accurately when they include non-ASCII characters.
	</li>
	<li>
	The Python folder treats comments at the end of the file as separate from the preceding structure.
	</li>
	<li>
	The YAML lexer recognizes comments in more situations and styles a
	"..." line like a "---" line.
	<a href="https://sourceforge.net/p/scintilla/bugs/1931/">Bug #1931</a>.
	</li>
	<li>
	Update scroll bar when annotations added, removed, or visibility changed.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1187/">Feature #1187.</a>
	</li>
	<li>
	Canceling modes with the Esc key preserves a rectangular selection.
	<a href="https://sourceforge.net/p/scintilla/bugs/1940/">Bug #1940</a>.
	</li>
	<li>
	Builds are made with a sorted list of lexers to be more reproducible.
	<a href="https://sourceforge.net/p/scintilla/bugs/1946/">Bug #1946</a>.
	</li>
	<li>
	On Cocoa, a leak of mouse tracking areas was fixed.
	</li>
	<li>
	On Cocoa, the autocompletion is 4 pixels wider to avoid text truncation.
	</li>
	<li>
	On Windows, stop drawing a focus rectangle on the autocompletion list and
	raise the default list length to 9 items.
	</li>
	<li>
	SciTE examines at most 1 MB of a file to automatically determine indentation
	for indent.auto to avoid a lengthy pause when loading very large files.
	</li>
	<li>
	SciTE user interface uses lighter colours and fewer 3D elements to match current desktop environments.
	</li>
	<li>
	SciTE sets buffer dirty and shows message when file deleted if load.on.activate on.
	</li>
	<li>
	SciTE on Windows Find strip Find button works in incremental no-close mode.
	<a href="https://sourceforge.net/p/scintilla/bugs/1926/">Bug #1926</a>.
	</li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/scite374.zip">Release 3.7.4</a>
    </h3>
    <ul>
	<li>
	Released 21 March 2017.
	</li>
	<li>
	Requires a C++11 compiler. GCC 4.8 and MSVC 2015 are supported.
	</li>
	<li>
	Support dropped for Windows NT 4.
	</li>
	<li>
	Accessibility support may be queried with SCI_GETACCESSIBILITY.
	On GTK+, accessibility may be disabled by calling SCI_SETACCESSIBILITY.
	</li>
	<li>
	Lexer added for "indent" language which is styled as plain text but folded by indentation level.
	</li>
	<li>
	The Progress ABL lexer handles nested comments where comment starts or ends
	are adjacent like "/*/*" or "*/*/".
	</li>
	<li>
	In the Python lexer, improve f-string support.
	Add support for multiline expressions in triple quoted f-strings.
	Handle nested "()", "[]", and "{}" in f-string expressions and terminate expression colouring at ":" or "!".
	End f-string if ending quote is seen in a "{}" expression.
	Fix terminating single quoted f-string at EOL.
	<a href="https://sourceforge.net/p/scintilla/bugs/1918/">Bug #1918</a>.
	</li>
	<li>
	The VHDL folder folds an "entity" on the first line of the file.
	</li>
	<li>
	For IMEs, do not clear selected text when there is no composition text to show.
	</li>
	<li>
	Fix to crash with fold tags where line inserted at start.
	</li>
	<li>
	Fix to stream selection mode when moving caret up or down.
	<a href="https://sourceforge.net/p/scintilla/bugs/1905/">Bug #1905</a>.
	</li>
	<li>
	Drawing fixes for fold tags include fully drawing lines and not overlapping some
	drawing and ensuring edges and mark underlines are visible.
	</li>
	<li>
	Fix Cocoa failure to display accented character chooser for European
	languages by partially reverting a change made to prevent a crash with
	Chinese input by special-casing the Cangjie input source.
	<a href="https://sourceforge.net/p/scintilla/bugs/1881/">Bug #1881</a>.
	</li>
	<li>
	Fix potential problems with IME on Cocoa when document contains invalid
	UTF-8.
	</li>
	<li>
	Fix crash on Cocoa with OS X 10.9 due to accessibility API not available.
	<a href="https://sourceforge.net/p/scintilla/bugs/1915/">Bug #1915</a>.
	</li>
	<li>
	Improved speed of accessibility code on GTK+ by using additional memory
	as a cache.
	<a href="https://sourceforge.net/p/scintilla/bugs/1910/">Bug #1910</a>.
	</li>
	<li>
	Fix crash in accessibility code on GTK+ &lt; 3.3.6 caused by previous bug fix.
	<a href="https://sourceforge.net/p/scintilla/bugs/1907/">Bug #1907</a>.
	</li>
	<li>
	Fix to prevent double scrolling on GTK+ with X11.
	<a href="https://sourceforge.net/p/scintilla/bugs/1901/">Bug #1901</a>.
	</li>
	<li>
	SciTE on GTK+ adds an "accessibility" property to allow disabling accessibility
	on GTK+ as an optimization.
	</li>
	<li>
	SciTE on GTK+ has changed file chooser behaviour for some actions:
	overwriting an existing file shows a warning;
	the default session file name "SciTE.session" is shown and a "*.session" filter is applied;
	appropriate filters are applied when exporting;
	the current file name is displayed in "Save As" even when that file no longer exists.
	</li>
	<li>
	SciTE fixed a bug where, on GTK+, when the output pane had focus, menu commands
	performed by mouse were sent instead to the edit pane.
	</li>
	<li>
	SciTE on Windows 8+ further restricts the paths searched for DLLs to the application
	and system directories which may prevent some binary planting attacks.
	</li>
	<li>
	Fix failure to load Direct2D on Windows when used on old versions of Windows.
	<a href="https://sourceforge.net/p/scintilla/bugs/1653/">Bug #1653</a>.
	</li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/scite373.zip">Release 3.7.3</a>
    </h3>
    <ul>
	<li>
	Released 19 February 2017.
	</li>
	<li>
	Display block caret over the character at the end of a selection to be similar
	to other editors.
	</li>
	<li>
	In SciTE can choose colours for fold markers.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1172/">Feature #1172.</a>
	</li>
	<li>
	In SciTE can hide buffer numbers in tabs.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1173/">Feature #1173.</a>
	</li>
	<li>
	The Diff lexer recognizes deleted lines that start with "--- ".
	</li>
	<li>
	The Lua lexer requires the first line to start with "#!" to be treated as a shebang comment,
	not just "#".
	<a href="https://sourceforge.net/p/scintilla/bugs/1900/">Bug #1900</a>.
	</li>
	<li>
	The Matlab lexer requires block comment start and end to be alone on a line.
	<a href="https://sourceforge.net/p/scintilla/bugs/1902/">Bug #1902</a>.
	</li>
	<li>
	The Python lexer supports f-strings with new styles, allows Unicode identifiers,
	and no longer allows @1 to be a decorator.
	<a href="https://sourceforge.net/p/scintilla/bugs/1848/">Bug #1848</a>.
	</li>
	<li>
	Fix folding inconsistency when fold header added above a folded part.
	Avoid unnecessary unfolding when a deletion does not include a line end.
	<a href="https://sourceforge.net/p/scintilla/bugs/1896/">Bug #1896</a>.
	</li>
	<li>
	Fix finalization crash on Cocoa.
	<a href="https://sourceforge.net/p/scintilla/bugs/1909/">Bug #1909</a>.
	</li>
	<li>
	SciTE on GTK+ can have a wide divider between the panes with the
	split.wide property.
	</li>
	<li>
	Fix display of autocompletion lists and calltips on GTK+ 3.22 on Wayland.
	Newer APIs used on GTK+ 3.22 as older APIs were deprecated.
	</li>
	<li>
	Fix crash in accessibility code on GTK+ due to signal receipt after destruction.
	<a href="https://sourceforge.net/p/scintilla/bugs/1907/">Bug #1907</a>.
	</li>
	<li>
	Make trackpad scrolling work on Wayland.
	<a href="https://sourceforge.net/p/scintilla/bugs/1901/">Bug #1901</a>.
	</li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/scite372.zip">Release 3.7.2</a>
    </h3>
    <ul>
	<li>
	Released 30 December 2016.
	</li>
	<li>
	Minimize redrawing for SCI_SETSELECTIONN* APIs.
	<a href="https://sourceforge.net/p/scintilla/bugs/1888/">Bug #1888</a>.
	</li>
	<li>
	Use more precision to allow selecting individual lines in files with
	more than 16.7 million lines.
	</li>
	<li>
	For Qt 5, define QT_WS_MAC or QT_WS_X11 on those platforms.
	<a href="https://sourceforge.net/p/scintilla/bugs/1887/">Bug #1887</a>.
	</li>
	<li>
	For Cocoa, fix crash on view destruction with macOS 10.12.2.
	<a href="https://sourceforge.net/p/scintilla/bugs/1891/">Bug #1891</a>.
	</li>
	<li>
	Fix crash on GTK+ &lt;3.8 due to incorrect lifetime of accessibility object.
	More accurate reporting of attribute ranges and deletion lengths for accessibility.
	</li>
	<li>
	In SciTE, if a Lua script causes a Scintilla failure exception, display error
	message in output pane instead of exiting.
	<a href="https://sourceforge.net/p/scintilla/bugs/1773/">Bug #1773</a>.
	</li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/scite371.zip">Release 3.7.1</a>
    </h3>
    <ul>
	<li>
	Released 4 December 2016.
	</li>
	<li>
	The Scintilla namespace is no longer applied to struct definitions in Scintilla.h even
	when SCI_NAMESPACE defined.
	Client code should not define SCI_NAMESPACE.
	</li>
	<li>
	Structure names in Scintilla.h without prefixes are deprecated and will now only
	be usable with INCLUDE_DEPRECATED_FEATURES defined.<br />
	Use the newer names with the "Sci_" prefix:<br />
	CharacterRange &rarr; Sci_CharacterRange<br />
	TextRange &rarr; Sci_TextRange<br />
	TextToFind &rarr; Sci_TextToFind<br />
	RangeToFormat &rarr; Sci_RangeToFormat<br />
	NotifyHeader &rarr; Sci_NotifyHeader
	</li>
	<li>
	Previously deprecated features SC_CP_DBCS, SCI_SETUSEPALETTE. and SCI_GETUSEPALETTE
	have been removed and can no longer be used in client code.
	</li>
	<li>
	Accessibility support allowing screen readers to work added on GTK+ and Cocoa.
	</li>
	<li>
	Textual tags may be displayed to the right on folded lines with SCI_TOGGLEFOLDSHOWTEXT.
	This is commonly something like "{ ... }" or "&lt;tr&gt;...&lt;/tr&gt;".
	It is displayed with the STYLE_FOLDDISPLAYTEXT style and may have a box drawn around it
	with SCI_FOLDDISPLAYTEXTSETSTYLE.
	</li>
	<li>
	A mouse right-click over the margin may send an SCN_MARGINRIGHTCLICK event.
	This only occurs when popup menus are turned off.
	SCI_USEPOPUP now has three states: SC_POPUP_NEVER, SC_POPUP_ALL, or SC_POPUP_TEXT.
	</li>
	<li>
	INDIC_POINT and INDIC_POINTCHARACTER indicators added to display small arrows
	underneath positions or characters.
	</li>
	<li>
	Added alternate appearance for visible tabs which looks like a horizontal line.
	Controlled with SCI_SETTABDRAWMODE.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1165/">Feature #1165.</a>
	</li>
	<li>
	On Cocoa, a modulemap file is included to allow Scintilla to be treated as a module.
	This makes it easier to use Scintilla from the Swift language.
	</li>
	<li>
	Baan folder accommodates sections and lexer fixes definition of SCE_BAAN_FUNCDEF.
	</li>
	<li>
	EDIFACT lexer and folder added.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1166/">Feature #1166.</a>
	</li>
	<li>
	JSON folder fixed where it didn't resume folding with the correct fold level.
	</li>
	<li>
	Matlab folder based on syntax instead of indentation so more accurate.
	<a href="https://sourceforge.net/p/scintilla/bugs/1692/">Bug #1692</a>.
	</li>
	<li>
	YAML lexer fixed style of references and keywords when followed by a comment.
	<a href="https://sourceforge.net/p/scintilla/bugs/1872/">Bug #1872</a>.
	</li>
	<li>
	Margin click to select line now clears rectangular and additional selections.
	</li>
	<li>
	Fixed a NULL access bug on GTK+ where the scrollbars could be used during destruction.
	<a href="https://sourceforge.net/p/scintilla/bugs/1873/">Bug #1873</a>.
	</li>
	<li>
	A potential bug on GTK+ fixed where asynchronous clipboard could be delivered after its
	target Scintilla instance was destroyed.
	</li>
	<li>
	Cocoa IME made more compliant with documented behaviour to avoid bugs that caused
	huge allocations.
	<a href="https://sourceforge.net/p/scintilla/bugs/1881/">Bug #1881</a>.
	</li>
	<li>
	On Win32 fix EM_SETSEL to match Microsoft documentation..
	<a href="https://sourceforge.net/p/scintilla/bugs/1886/">Bug #1886</a>.
	</li>
	<li>
	SciTE on GTK+ allows localizing tool bar tool tips.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1167/">Feature #1167.</a>
	</li>
	<li>
	SciTE on Windows restores focus to edit pane after closing user strip.
	</li>
	<li>
	SciTE measures files larger that 2 GB which allows it to refuse to open huge files more consistently
	and to show better warning messages.
	</li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/scite370.zip">Release 3.7.0</a>
    </h3>
    <ul>
	<li>
	Released 16 October 2016.
	</li>
	<li>
	Word selection, navigation, and manipulation is now performed on characters instead of bytes
	leading to more natural behaviour for multi-byte encodings like UTF-8.
	For UTF-8 characters 0x80 and above, classification into word; punctuation; space; or line-end
	is based on the Unicode general category of the character and is not customizable.
	<a href="https://sourceforge.net/p/scintilla/bugs/1832/">Bug #1832</a>.
	</li>
	<li>
	Two enums changed in Scintilla.iface which may lead to changed bindings.
	There were 2 FontQuality enums and the first is now PhasesDraw.
	The prefix for FoldAction was SC_FOLDACTION and is now SC_FOLDACTION_
	which is similar to other enums.
	These changes do not affect the standard C/C++ binding.
	</li>
	<li>
	EDGE_MULTILINE and SCI_MULTIEDGEADDLINE added to allow displaying multiple
	vertical edges simultaneously.
	</li>
	<li>
	The number of margins can be changed with SCI_SETMARGINS.
	</li>
	<li>
	Margin type SC_MARGIN_COLOUR added so that the application may
	choose any colour for a margin with SCI_SETMARGINBACKN.
	</li>
	<li>
	On Win32, mouse wheel scrolling can be restricted to only occur when the mouse is
	within the window.
	</li>
	<li>
	The WordList class in lexlib used by lexers adds an InListAbridged method for
	matching keywords that have particular prefixes and/or suffixes.
	</li>
	<li>
	The Baan lexer was changed significantly with more lexical states, keyword sets,
	and support for abridged keywords.
	</li>
	<li>
	The CoffeeScript lexer styles interpolated code in strings.
	<a href="https://sourceforge.net/p/scintilla/bugs/1865/">Bug #1865</a>.
	</li>
	<li>
	The Progress lexer "progress" has been replaced with a new lexer "abl"
	(Advanced Business Language)
	with a different set of lexical states and more functionality.
	The lexical state prefix has changed from SCE_4GL_ to SCE_ABL_.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1143/">Feature #1143.</a>
	</li>
	<li>
	The PowerShell lexer understands the grave accent escape character.
	<a href="https://sourceforge.net/p/scintilla/bugs/1868/">Bug #1868</a>.
	</li>
	<li>
	The YAML lexer recognizes inline comments.
	<a href="https://sourceforge.net/p/scintilla/bugs/1660/">Bug #1660</a>.
	</li>
	<li>
	SciTE on Windows can retain coloured selection when inactive with
	selection.always.visible property.
	</li>
	<li>
	SciTE on Windows adds a state to close.on.find to close the find strip when
	a match is found.
	</li>
	<li>
	Fix caret position after left or right movement with rectangular selection.
	<a href="https://sourceforge.net/p/scintilla/bugs/1861/">Bug #1861</a>.
	</li>
	<li>
	In SciTE, optional prefix argument added to scite.ConstantName method.
	<a href="https://sourceforge.net/p/scintilla/bugs/1860/">Bug #1860</a>.
	</li>
	<li>
	On Cocoa, include ILexer.h in the public headers of the framework.
	<a href="https://sourceforge.net/p/scintilla/bugs/1855/">Bug #1855</a>.
	</li>
	<li>
	On Cocoa, allow subclass of SCIContentView to set cursor.
	<a href="https://sourceforge.net/p/scintilla/bugs/1863/">Bug #1863</a>.
	</li>
	<li>
	On Cocoa, recognize the numeric keypad '+', '-', and '/' keys as
	SCK_ADD, SCK_SUBTRACT, and SCK_DIVIDE.
	<a href="https://sourceforge.net/p/scintilla/bugs/1867/">Bug #1867</a>.
	</li>
	<li>
	On GTK+ 3.21+ fix incorrect font size in auto-completion list.
	<a href="https://sourceforge.net/p/scintilla/bugs/1859/">Bug #1859</a>.
	</li>
	<li>
	Fix SciTE crash when command.mode ends with comma.
	<a href="https://sourceforge.net/p/scintilla/bugs/1857/">Bug #1857</a>.
	</li>
	<li>
	SciTE on Windows has a full size toolbar icon for "Close".
	</li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/scite367.zip">Release 3.6.7</a>
    </h3>
    <ul>
	<li>
	Released 4 September 2016.
	</li>
	<li>
	C++11 range-based for loops used in SciTE so GCC 4.6 is now the minimum supported version.
	</li>
	<li>
	SC_CHARSET_DEFAULT now means code page 1252 on Windows unless a code page is set.
	This prevents unexpected behaviour and crashes on East Asian systems where default locales are commonly DBCS.
	Projects which want to default to DBCS code pages in East Asian locales should set the code page and
	character set explicitly.
	</li>
	<li>
	SCVS_NOWRAPLINESTART option stops left arrow from wrapping to the previous line.
	Most commonly wanted when virtual space is used.
	<a href="https://sourceforge.net/p/scintilla/bugs/1648/">Bug #1648</a>.
	</li>
	<li>
	The C++ lexer can fold on #else and #elif with the fold.cpp.preprocessor.at.else property.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/210/">Bug #210</a>.
	</li>
	<li>
	The errorlist lexer detects warnings from Visual C++ which do not contain line numbers.
	</li>
	<li>
	The HTML lexer no longer treats "&lt;?" inside a string in a script as potentially starting an XML document.
	<a href="https://sourceforge.net/p/scintilla/bugs/767/">Bug #767</a>.
	</li>
	<li>
	The HTML lexer fixes a problem resuming at a script start where the starting state continued
	past where it should.
	<a href="https://sourceforge.net/p/scintilla/bugs/1849/">Bug #1849</a>.
	</li>
	<li>
	When inserting spaces for virtual space and the position is in indentation and tabs are enabled
	for indentation then use tabs.
	<a href="https://sourceforge.net/p/scintilla/bugs/1850/">Bug #1850</a>.
	</li>
	<li>
	Fix fold expand when some child text not styled.
	Caused by fixes for Bug #1799.
	<a href="https://sourceforge.net/p/scintilla/bugs/1842/">Bug #1842</a>.
	</li>
	<li>
	Fix key binding bug on Cocoa for control+.
	<a href="https://sourceforge.net/p/scintilla/bugs/1854/">Bug #1854</a>.
	</li>
	<li>
	Fix scroll bar size warnings on GTK+ caused by #1831.
	<a href="https://sourceforge.net/p/scintilla/bugs/1851/">Bug #1851</a>.
	</li>
	<li>
	Small fixes for GTK+ makefile.
	<a href="https://sourceforge.net/p/scintilla/bugs/1844/">Bug #1844</a>.
	<a href="https://sourceforge.net/p/scintilla/bugs/1845/">Bug #1845</a>.
	<a href="https://sourceforge.net/p/scintilla/bugs/1846/">Bug #1846</a>.
	</li>
	<li>
	Fix SciTE indentation after code like "void function () {}".
	</li>
	<li>
	Fix SciTE global regex replace of "^" with something which missed the line after empty
	lines with LF line ends.
	<a href="https://sourceforge.net/p/scintilla/bugs/1839/">Bug #1839</a>.
	</li>
	<li>
	Fix SciTE on GTK+ 3.20 bug where toggle buttons on find and replace strips
	did not show active state.
	<a href="https://sourceforge.net/p/scintilla/bugs/1853/">Bug #1853</a>.
	</li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/scite366.zip">Release 3.6.6</a>
    </h3>
    <ul>
	<li>
	Released 24 May 2016.
	</li>
	<li>
	C++ 11 &lt;regex&gt; support built by default. Can be disabled by defining NO_CXX11_REGEX.
	</li>
	<li>
	SciTE_USERHOME environment variable allows separate location for writeable properties files.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/965/">Feature #965.</a>
	</li>
	<li>
	GObject introspection supports notify and command events.
	</li>
	<li>
	The Progress lexer now allows comments preceded by a tab.
	</li>
	<li>
	Scripts reading Scintilla.iface file include comments for enu and lex definitions.
	<a href="https://sourceforge.net/p/scintilla/bugs/1829/">Bug #1829</a>.
	</li>
	<li>
	Fix crashes on GTK+ if idle work active when destroyed.
	<a href="https://sourceforge.net/p/scintilla/bugs/1827/">Bug #1827</a>.
	</li>
	<li>
	Fixed bugs when used on GTK+ 3.20.
	<a href="https://sourceforge.net/p/scintilla/bugs/1825/">Bug #1825</a>.
	<a href="https://sourceforge.net/p/scintilla/bugs/1831/">Bug #1831</a>.
	</li>
	<li>
	Fix SciTE search field background with dark theme on GTK+ 2.x.
	<a href="https://sourceforge.net/p/scintilla/bugs/1826/">Bug #1826</a>.
	</li>
	<li>
	Fixed bug on Win32 that allowed resizing autocompletion from bottom when it was
	located above the caret.
	</li>
	<li>
	On Win32, when using a screen reader and selecting text using Shift+Arrow,
	fix bug when scrolling made the caret stay at the same screen location
	so the screen reader did not speak the added or removed selection.
	</li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/scite365.zip">Release 3.6.5</a>
    </h3>
    <ul>
	<li>
	Released 26 April 2016.
	</li>
	<li>
	JSON lexer added.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1140/">Feature #1140.</a>
	</li>
	<li>
	The C++ lexer fixes a bug with multi-line strings with line continuation where the string style
	overflowed after an edit.
	<a href="https://sourceforge.net/p/scintilla/bugs/1824/">Bug #1824</a>.
	</li>
	<li>
	The Python lexer treats '@' as an operator except when it is the first visible character on a line.
	This is for Python 3.5.
	</li>
	<li>
	The Rust lexer allows '?' as an operator.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1146/">Feature #1146.</a>
	</li>
	<li>
	Doubled size of compiled regex buffer.
	<a href="https://sourceforge.net/p/scintilla/bugs/1822/">Bug #1822</a>.
	</li>
	<li>
	For GTK+, the Super modifier key can be used in key bindings.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1142/">Feature #1142.</a>
	</li>
	<li>
	For GTK+, fix some crashes when using multiple threads.
	</li>
	<li>
	Platform layer font cache removed on GTK+ as platform-independent caches are used.
	This avoids the use of thread locking and initialization of threads so any GTK+
	applications that rely on Scintilla initializing threads will have to do that themselves.
	</li>
	<li>
	SciTE bug fixed with exported HTML where extra line shown.
	<a href="https://sourceforge.net/p/scintilla/bugs/1816/">Bug #1816</a>.
	</li>
	<li>
	SciTE on Windows fixes bugs with pop-up menus in the find and replace strips.
	For the replace strip, menu choices change the state.
	For the find strip, menu choices are reflected in the appearance of their corresponding buttons.
	</li>
	<li>
	SciTE on Windows on high DPI displays fixes the height of edit boxes in user strips.
	</li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/scite364.zip">Release 3.6.4</a>
    </h3>
    <ul>
	<li>
	Released 13 March 2016.
	</li>
	<li>
	SciTE allows setting the autocompletion type separator character.
	</li>
	<li>
	The C++ folder folds code on '(' and ')' to allow multi-line calls to be folded.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1138/">Feature #1138.</a>
	</li>
	<li>
	For the HTML lexer, limit the extent of Mako line comments to finish before
	the line end characters.
	</li>
	<li>
	Folds unfolded when two fold regions are merged by either deleting an intervening line
	or changing its fold level by adding characters.
	This was fixed both in Scintilla and in SciTE's equivalent code.
	<a href="https://sourceforge.net/p/scintilla/bugs/1799/">Bug #1799</a>.<br />
	</li>
	<li>
	The Progress lexer supports hexadecimal numeric literals,
	single-line comments, abbreviated keywords and
	extends nested comments to unlimited levels.
	</li>
	<li>
	Ruby lexer treats alternate hash key syntax "key:" as a symbol.
	<a href="https://sourceforge.net/p/scintilla/bugs/1810/">Bug #1810</a>.
	</li>
	<li>
	Rust lexer handles bracketed Unicode string escapes like "\u{123abc}".
	<a href="https://sourceforge.net/p/scintilla/bugs/1809/">Bug #1809</a>.
	</li>
	<li>
	For GTK+ on Windows fix 64-bit build which was broken in 3.6.3.
	</li>
	<li>
	For Qt, release builds have assertions turned off.
	</li>
	<li>
	For Qt on Windows, fix compilation failure for Qt 4.x.
	</li>
	<li>
	IME target range displayed on Qt for OS X.
	</li>
	<li>
	On Windows, make clipboard operations more robust by retrying OpenClipboard if it fails
	as this may occur when another application has opened the clipboard.
	</li>
	<li>
	On Windows back out change that removed use of def file to ensure
	Scintilla_DirectFunction exported without name mangling.
	<a href="https://sourceforge.net/p/scintilla/bugs/1813/">Bug #1813</a>.
	</li>
	<li>
	On GTK+ and Qt over Win32 in Korean fix bug caused by last release's word input change.
	</li>
	<li>
	For SciTE, more descriptive error messages are displayed when there are problems loading the
	Lua startup script.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1139/">Feature #1139.</a>
	</li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/scite363.zip">Release 3.6.3</a>
    </h3>
    <ul>
	<li>
	Released 18 January 2016.
	</li>
	<li>
	Allow painting without first styling all visible text then styling in the background
	using idle-time. This helps performance when scrolling down in very large documents.
	Can also incrementally style after the visible area to the end of the document so that
	the document is already styled when the user scrolls to it.
	</li>
	<li>
	Support GObject introspection on GTK+.
	</li>
	<li>
	SciTE supports pasting to each selection with the selection.multipaste setting.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1123/">Feature #1123.</a>
	</li>
	<li>
	SciTE can optionally display a read-only indicator on tabs and in the Buffers menu.
	</li>
	<li>
	Bash lexer flags incomplete here doc delimiters as syntax errors.
	<a href="https://sourceforge.net/p/scintilla/bugs/1789/">Bug #1789</a>.<br />
	Support added for using '#' in non-comment ways as is possible with zsh.
	<a href="https://sourceforge.net/p/scintilla/bugs/1794/">Bug #1794</a>.<br />
	Recognize more characters as here-doc delimiters.
	<a href="https://sourceforge.net/p/scintilla/bugs/1778/">Bug #1778</a>.
	</li>
	<li>
	Errorlist lexer highlights warning messages from the Microsoft linker.
	</li>
	<li>
	Errorlist lexer fixes bug with final line in escape sequence recognition mode.
	</li>
	<li>
	Lua lexer includes '&amp;' and '|' bitwise operators for Lua 5.3.
	<a href="https://sourceforge.net/p/scintilla/bugs/1790/">Bug #1790</a>.
	</li>
	<li>
	Perl lexer updated for Perl 5.20 and 5.22.<br />
	Allow '_' for subroutine prototypes.
	<a href="https://sourceforge.net/p/scintilla/bugs/1791/">Bug #1791</a>.<br />
	Double-diamond operator &lt;&lt;&gt;&gt;.<br />
	Hexadecimal floating point literals.<br />
	Repetition in list assignment.
	<a href="https://sourceforge.net/p/scintilla/bugs/1793/">Bug #1793</a>.<br />
	Highlight changed subroutine prototype syntax for Perl 5.20.
	<a href="https://sourceforge.net/p/scintilla/bugs/1797/">Bug #1797</a>.<br />
	Fix module ::-syntax when special characters such as 'x' are used.<br />
	Added ' and " detection as prefix chars for x repetition operator.
	<a href="https://sourceforge.net/p/scintilla/bugs/1800/">Bug #1800</a>.
	</li>
	<li>
	Visual Prolog lexer recognizes numbers more accurately and allows non-ASCII verbatim
	quoting characters.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1130/">Feature #1130.</a>
	</li>
	<li>
	Send SCN_UPDATEUI with SC_UPDATE_SELECTION when the application changes multiple
	selection.
	</li>
	<li>
	Expand folded areas before deleting fold header line.
	<a href="https://sourceforge.net/p/scintilla/bugs/1796/">Bug #1796</a>.
	</li>
	<li>
	Treat Unicode line ends like common line ends when maintaining fold state.
	</li>
	<li>
	Highlight whole run for hover indicator when wrapped.
	<a href="https://sourceforge.net/p/scintilla/bugs/1784/">Bug #1784</a>.
	</li>
	<li>
	On Cocoa, fix crash when autocompletion list closed during scroll bounce-back.
	<a href="https://sourceforge.net/p/scintilla/bugs/1788/">Bug #1788</a>.
	</li>
	<li>
	On Windows, fix non-BMP input through WM_CHAR and allow WM_UNICHAR to work
	with non-BMP characters and on non-Unicode documents.
	<a href="https://sourceforge.net/p/scintilla/bugs/1779/">Bug #1779</a>.
	</li>
	<li>
	On Windows using DirectWrite, for ligatures and other character clusters,
	display caret and selections part-way through clusters so that the caret doesn't stick
	to the end of the cluster making it easier to understand editing actions.
	</li>
	<li>
	On Windows, Scintilla no longer uses a .DEF file during linking as it duplicates
	source code directives.
	</li>
	<li>
	On GTK+ and Qt, Korean input by word fixed.
	</li>
	<li>
	On GTK+, Qt, and Win32 block IME input when document is read-only or any selected text
	is protected.
	</li>
	<li>
	On GTK+ on OS X, fix warning during destruction.
	<a href="https://sourceforge.net/p/scintilla/bugs/1777/">Bug #1777</a>.
	</li>
	<li>
	Fix SciTE crashes when using LPEG lexers.
	</li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/scite362.zip">Release 3.6.2</a>
    </h3>
    <ul>
	<li>
	Released 6 November 2015.
	</li>
	<li>
	Whitespace may be made visible just in indentation.
	</li>
	<li>
	Whitespace dots are centred when larger than 1 pixel.
	</li>
	<li>
	The Scintilla framework on Cocoa now contains version numbers.
	</li>
	<li>
	SciTE's standard properties collect values from all active .properties file to produce the Language menu
	and the file types pull-down in the File open dialog.
	</li>
	<li>
	The single executable version of SciTE, Sc1, uses 'module' statements within its embedded
	properties. This makes it act more like the full distribution allowing languages to be turned on
	and off by setting imports.include and imports.exclude.
	The default imports.exclude property adds eiffel, erlang, ps, and pov so these languages are
	turned off by default.
	</li>
	<li>
	SciTE adds an output.blank.margin.left property to allow setting the output pane
	margin to a different width than the edit pane.
	</li>
	<li>
	CoffeeScript lexer highlights ranges correctly.
	<a href="https://sourceforge.net/p/scintilla/bugs/1765/">Bug #1765</a>.
	</li>
	<li>
	Markdown lexer treats line starts consistently to always highlight *foo* or similar at line start.
	<a href="https://sourceforge.net/p/scintilla/bugs/1766/">Bug #1766</a>.
	</li>
	<li>
	Optimize marker redrawing by only drawing affected lines when markers shown in the text.
	</li>
	<li>
	On Cocoa, timers and idling now work in modal dialogs. This also stops some crashes.
	</li>
	<li>
	On Cocoa, fix crashes when deleting a ScintillaView. These crashes could occur when scrolling
	at the time the ScintillaView was deleted although there may have been other cases.
	</li>
	<li>
	On GTK+ 2.x, fix height of lines in autocompletion lists.
	<a href="https://sourceforge.net/p/scintilla/bugs/1774/">Bug #1774</a>.
	</li>
	<li>
	Fix bug with SCI_LINEENDDISPLAY where the caret moved to the next document line instead of the
	end of the display line.
	<a href="https://sourceforge.net/p/scintilla/bugs/1772/">Bug #1772</a>.
	</li>
	<li>
	Report error (SC_STATUS_FAILURE) when negative length passed to SCI_SETSTYLING.
	<a href="https://sourceforge.net/p/scintilla/bugs/1768/">Bug #1768</a>.
	</li>
	<li>
	When SC_MARK_UNDERLINE is not assigned to a margin, stop drawing the whole line.
	</li>
	<li>
	When reverting an untitled document in SciTE, just clear it with no message about a file.
	<a href="https://sourceforge.net/p/scintilla/bugs/1764/">Bug #1764</a>.
	</li>
	<li>
	SciTE on GTK+ allows use of Ctrl+A (Select All) inside find and replace strips.
	<a href="https://sourceforge.net/p/scintilla/bugs/1769/">Bug #1769</a>.
	</li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/scite361.zip">Release 3.6.1</a>
    </h3>
    <ul>
	<li>
	Released 15 September 2015.
	</li>
	<li>
	The oldest version of GTK+ supported now is 2.18 and for glib it is 2.22.
	</li>
	<li>
	On GTK+, SC_CHARSET_OEM866 added to allow editing Russian files encoded in code page 866.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1019/">Feature #1019.</a>
	</li>
	<li>
	On Windows, reconversion is performed when requested by the IME.
	</li>
	<li>
	CoffeeScript lexer adds lexical class for instance properties and fixes some cases of regex highlighting.
	<a href="https://sourceforge.net/p/scintilla/bugs/1749/">Bug #1749</a>.
	</li>
	<li>
	The errorlist lexer understands some ANSI escape sequences to change foreground colour and intensity.
	This is sufficient to colour diagnostic output from gcc and clang when -fdiagnostics-color set.
	</li>
	<li>
	The errorlist lexer allows the line number to be 0 in GCC errors as some tools report whole file
	errors as line 0.
	</li>
	<li>
	MySql lexer fixes empty comments /**/ so the comment state does not continue.
	</li>
	<li>
	VHDL folder supports "protected" keyword.
	</li>
	<li>
	Treat CRLF line end as two characters in SCI_COUNTCHARACTERS.
	<a href="https://sourceforge.net/p/scintilla/bugs/1757/">Bug #1757</a>.
	</li>
	<li>
	On GTK+ 3.x, fix height of lines in autocompletion lists to match the font.
	Switch from deprecated style calls to CSS styling.
	Removed setting list colours on GTK+ 3.16+ as no longer appears needed.
	</li>
	<li>
	On GTK+, avoid "Invalid rectangle passed" warning messages by never reporting the client
	rectangle with a negative width or height.
	<a href="https://sourceforge.net/p/scintilla/bugs/1743/">Bug #1743</a>.
	</li>
	<li>
	On Cocoa, copy Sci_Position.h into the framework so clients can build.
	</li>
	<li>
	On Cocoa fix bug with drag and drop that could lead to crashes.
	<a href="https://sourceforge.net/p/scintilla/bugs/1751/">Bug #1751</a>.
	</li>
	<li>
	Fix SciTE disk exhaustion bug by reporting failures when writing files.
	<a href="https://sourceforge.net/p/scintilla/bugs/1760/">Bug #1760</a>.
	</li>
	<li>
	Fix find strip in SciTE on Windows XP to be visible.
	</li>
	<li>
	SciTE on Windows changes the way it detects that a tool has finished executing to ensure all output data
	from the process is read.
	</li>
	<li>
	SciTE on Windows improves the time taken to read output from tools that produce a large amount
	of output by a factor of around 10.
	</li>
	<li>
	On GTK+ the keyboard command for View | End of Line was changed to Ctrl+Shift+N
	to avoid clash with Search | Selection Add Next.
	<a href="https://sourceforge.net/p/scintilla/bugs/1750/">Bug #1750</a>.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite360.zip?download">Release 3.6.0</a>
    </h3>
    <ul>
	<li>
	Released 3 August 2015.
	</li>
	<li>
	External interfaces use the Sci_Position and Sci_PositionU typedefs instead of int and unsigned int
	to allow for changes to a 64-bit interface on 64-bit platforms in the future.
	Applications and external lexers should start using the new type names so that
	they will be compatible when the 64-bit change occurs.
	There is also Sci_PositionCR (long) for use in the Sci_CharacterRange struct which will
	also eventually become 64-bit.
	</li>
	<li>
	Multiple selection now works over more key commands.
	The new multiple-selection handling commands include horizontal movement and selection commands,
	line up and down movement and selection commands, word and line deletion commands, and
	line end insertion.
	This change in behaviours is conditional on setting the SCI_SETADDITIONALSELECTIONTYPING property.
	</li>
	<li>
	Autocompletion lists send an SCN_AUTOCCOMPLETED notification after the text has been inserted.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1109/">Feature #1109.</a>
	</li>
	<li>
	The case mode style attribute can now be SC_CASE_CAMEL.
	</li>
	<li>
	The Python lexer supports substyles for identifiers.
	</li>
	<li>
	SciTE adds support for substyles.
	</li>
	<li>
	SciTE's Export as RTF and Copy as RTF commands support UTF-8.
	</li>
	<li>
	SciTE can display autocompletion on all IME input with ime.autocomplete property.
	</li>
	<li>
	SciTE properties files now discard trailing white space on variable names.
	</li>
	<li>
	Calling SCI_SETIDENTIFIERS resets styling to ensure any added identifier are highlighted.
	</li>
	<li>
	Avoid candidate box randomly popping up away from edit pane with (especially
	Japanese) IME input.
	</li>
	<li>
	On Cocoa fix problems with positioning of autocompletion lists near screen edge
	or under dock. Cancel autocompletion when window moved.
	<a href="https://sourceforge.net/p/scintilla/bugs/1740/">Bug #1740</a>.
	</li>
	<li>
	Fix drawing problem when control characters are in a hidden style as they then
	have a zero width rectangle to draw but modify that rectangle in a way that
	clears some pixels.
	</li>
	<li>
	Report error when attempt to resize buffer to more than 2GB with SC_STATUS_FAILURE.
	</li>
	<li>
	Fix bug on GTK+ with scroll bars leaking.
	<a href="https://sourceforge.net/p/scintilla/bugs/1742/">Bug #1742</a>.
	</li>
	<li>
	LexOthers.cxx file split into one file per lexer: LexBatch, LexDiff,
	LexErrorList, LexMake, LexNull, and LexProps.
	</li>
	<li>
	SciTE exporters handle styles &gt; 127 correctly now.
	</li>
	<li>
	SciTE on Windows can scale window element sizes based on the system DPI setting.
	</li>
	<li>
	SciTE implements find.in.files.close.on.find on all platforms, not just Windows.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite357.zip?download">Release 3.5.7</a>
    </h3>
    <ul>
	<li>
	Released 20 June 2015.
	</li>
	<li>
	Added SCI_MULTIPLESELECTADDNEXT to add the next occurrence of the main selection within the
	target to the set of selections as main. If the current selection is empty then select word around caret.
	SCI_MULTIPLESELECTADDEACH adds each occurrence of the main selection within the
	target to the set of selections.
	</li>
	<li>
	SciTE adds "Selection Add Next" and "Selection Add Each" commands to the Search menu.
	</li>
	<li>
	Added SCI_ISRANGEWORD to determine if the parameters are at the start and end of a word.
	</li>
	<li>
	Added SCI_TARGETWHOLEDOCUMENT to set the target to the whole document.
	</li>
	<li>
	Verilog lexer recognizes protected regions and the folder folds protected regions.
	</li>
	<li>
	A performance problem with markers when deleting many lines was fixed.
	<a href="https://sourceforge.net/p/scintilla/bugs/1733/">Bug #1733</a>.
	</li>
	<li>
	On Cocoa fix crash when ScintillaView destroyed if no autocompletion ever displayed.
	<a href="https://sourceforge.net/p/scintilla/bugs/1728/">Bug #1728</a>.
	</li>
	<li>
	On Cocoa fix crash in drag and drop.
	</li>
	<li>
	On GTK+ 3.4+, when there are both horizontal and vertical scrollbars, draw the lower-right corner
	so that it does not appear black when text selected.
	<a href="https://sourceforge.net/p/scintilla/bugs/1611/">Bug #1611</a>.
	</li>
	<li>
	Fixed most calls deprecated in GTK+ 3.16. Does not fix style override calls
	as they are more complex.
	</li>
	<li>
	SciTE on GTK+ 3.x uses a different technique for highlighting the search strip when there is
	no match which is more compatible with future and past versions and different themes.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite356.zip?download">Release 3.5.6</a>
    </h3>
    <ul>
	<li>
	Released 26 May 2015.
	</li>
	<li>
	On Qt, use fractional positioning calls and avoid rounding to ensure consistency.
	</li>
	<li>
	SCI_TARGETASUTF8 and SCI_ENCODEDFROMUTF8 implemented on
	Win32 as well as GTK+ and Cocoa.
	</li>
	<li>
	C++ lexer fixes empty backquoted string.
	<a href="https://sourceforge.net/p/scintilla/bugs/1711/">Bug #1711</a>.
	</li>
	<li>
	C++ lexer fixes #undef directive.
	<a href="https://sourceforge.net/p/scintilla/bugs/1719/">Bug #1719</a>.
	</li>
	<li>
	Fortran folder fixes handling of "selecttype" and "selectcase".
	<a href="https://sourceforge.net/p/scintilla/bugs/1724/">Bug #1724</a>.
	</li>
	<li>
	Verilog folder folds interface definitions.
	</li>
	<li>
	VHDL folder folds units declarations and fixes a case insensitivity bug with not treating "IS" the same as "is".
	</li>
	<li>
	Fix bug when drawing text margins in buffered mode which would use default
	encoding instead of chosen encoding.
	<a href="https://sourceforge.net/p/scintilla/bugs/1703/">Bug #1703</a>.
	</li>
	<li>
	Fix bug with Korean Hanja conversions in DBCS encoding on Windows.
	</li>
	<li>
	Fix for reading a UTF-16 file in SciTE where a non-BMP character is split over a read buffer boundary.
	<a href="https://sourceforge.net/p/scintilla/bugs/1710/">Bug #1710</a>.
	</li>
	<li>
	Fix bug on GTK+ 2.x for Windows where there was an ABI difference between
	compiler version.
	<a href="https://sourceforge.net/p/scintilla/bugs/1726/">Bug #1726</a>.
	</li>
	<li>
	Fix undo bug on Cocoa that could lose data..
	</li>
	<li>
	Fix link error on Windows when SCI_NAMESPACE used.
	</li>
	<li>
	Fix exporting from SciTE when using Scintillua for lexing.
	</li>
	<li>
	SciTE does not report twice that a search string can not be found when "Replace" pressed.
	<a href="https://sourceforge.net/p/scintilla/bugs/1716/">Bug #1716</a>.
	</li>
	<li>
	SciTE on GTK+ 3.x disables arrow in search combo when no entries.
	<a href="https://sourceforge.net/p/scintilla/bugs/1717/">Bug #1717</a>.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite355.zip?download">Release 3.5.5</a>
    </h3>
    <ul>
	<li>
	Released 17 April 2015.
	</li>
	<li>
	Scintilla on Windows is now always a wide character window so SCI_SETKEYSUNICODE has no effect
	and SCI_GETKEYSUNICODE always returns true. These APIs are deprecated and should not be called.
	</li>
	<li>
	The wxWidgets-specific ascent member of Font has been removed which breaks
	compatibility with current wxStyledTextCtrl.
	<a href="https://sourceforge.net/p/scintilla/bugs/1682/">Bug #1682</a>.
	</li>
	<li>
	IME on Qt supports multiple carets and behaves more like other platforms.
	</li>
	<li>
	Always use inline IME on GTK+ for Korean.
	</li>
	<li>
	SQL lexer fixes handling of '+' and '-' in numbers so the '-' in '1-1' is seen as an operator and for
	'1--comment' the comment is recognized.
	</li>
	<li>
	TCL lexer reverts change to string handling.
	<a href="https://sourceforge.net/p/scintilla/bugs/1642/">Bug #1642</a>.
	</li>
	<li>
	Verilog lexer fixes bugs with macro styling.
	Verilog folder fixes bugs with `end completing an `if* instead of `endif and fold.at.else, and implements
	folding at preprocessor `else.
	</li>
	<li>
	VHDL lexer supports extended identifiers.
	</li>
	<li>
	Fix bug on Cocoa where the calltip would display incorrectly when
	switching calltips and the new calltip required a taller window.
	</li>
	<li>
	Fix leak on Cocoa with autocompletion lists.
	<a href="https://sourceforge.net/p/scintilla/bugs/1706/">Bug #1706</a>.
	</li>
	<li>
	Fix potential crash on Cocoa with drag and drop.
	<a href="https://sourceforge.net/p/scintilla/bugs/1709/">Bug #1709</a>.
	</li>
	<li>
	Fix bug on Windows when compiling with MinGW-w64 which caused text to not be drawn
	when in wrap mode.
	<a href="https://sourceforge.net/p/scintilla/bugs/1705/">Bug #1705</a>.
	</li>
	<li>
	Fix SciTE bug with missing file open filters and add hex to excluded set of properties files so that its
	settings don't appear.
	<a href="https://sourceforge.net/p/scintilla/bugs/1707/">Bug #1707</a>.
	</li>
	<li>
	Fix SciTE bug where files without extensions like "makefile" were not highlighted correctly.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite354.zip?download">Release 3.5.4</a>
    </h3>
    <ul>
	<li>
	Released 8 March 2015.
	</li>
	<li>
	Indicators may have a different colour and style when the mouse is over them or the caret is moved into them.
	</li>
	<li>
	An indicator may display in a large variety of colours with the SC_INDICFLAG_VALUEFORE
	flag taking the colour from the indicator's value, which may differ for every character, instead of its
	foreground colour attribute.
	</li>
	<li>
	On Cocoa, additional IME methods implemented so that more commands are enabled.
	For Japanese: Reverse Conversion, Convert to Related Character, and Search Similar Kanji
	can now be performed.
	The global definition hotkey Command+Control+D and the equivalent three finger tap gesture
	can be used.
	</li>
	<li>
	Minimum version of Qt supported is now 4.8 due to the use of QElapsedTimer::nsecsElapsed.
	</li>
	<li>
	On Windows, for Korean, the VK_HANJA key is implemented to choose Hanja for Hangul and
	to convert from Hanja to Hangul.
	</li>
	<li>
	C++ lexer adds lexer.cpp.verbatim.strings.allow.escapes option that allows verbatim (@") strings
	to contain escape sequences. This should remain off (0) for C# and be turned on (1) for Objective C.
	</li>
	<li>
	Rust lexer accepts new 'is'/'us' integer suffixes instead of 'i'/'u'.
	<a href="https://sourceforge.net/p/scintilla/bugs/1098/">Bug #1098</a>.
	</li>
	<li>
	Ruby folder can fold multiline comments.
	<a href="https://sourceforge.net/p/scintilla/bugs/1697/">Bug #1697</a>.
	</li>
	<li>
	SQL lexer fixes a bug with the q-quote operator.
	</li>
	<li>
	TCL lexer fixes a bug with some strings.
	<a href="https://sourceforge.net/p/scintilla/bugs/1642/">Bug #1642</a>.
	</li>
	<li>
	Verilog lexer handles escaped identifiers that begin with \ and end with space like \reset* .
	Verilog folder fixes one bug with inconsistent folding when fold.comment is on and another
	with typedef class statements creating a fold point, expecting an endclass statement.
	</li>
	<li>
	VHDL folder fixes hang in folding when document starts with "entity".
	</li>
	<li>
	Add new indicators INDIC_COMPOSITIONTHIN, INDIC_FULLBOX, and INDIC_TEXTFORE.
	INDIC_COMPOSITIONTHIN is a thin underline that mimics the appearance of non-target segments in OS X IME.
	INDIC_FULLBOX is similar to INDIC_STRAIGHTBOX but covers the entire character area which means that
	indicators with this style on contiguous lines may touch. INDIC_TEXTFORE changes the text foreground colour.
	</li>
	<li>
	Fix adaptive scrolling speed for GTK+ on OS X with GTK Quartz backend (as opposed to X11 backend).
	<a href="https://sourceforge.net/p/scintilla/bugs/1696/">Bug #1696</a>.
	</li>
	<li>
	Fix position of autocompletion and calltips on Cocoa when there were two screens stacked vertically.
	</li>
	<li>
	Fix crash in SciTE when saving large files in background when closing application.
	<a href="https://sourceforge.net/p/scintilla/bugs/1691/">Bug #1691</a>.
	</li>
	<li>
	Fix decoding of MSVC warnings in SciTE so that files in the C:\Program Files (x86)\ directory can be opened.
	This is a common location of system include files.
	</li>
	<li>
	Fix compilation failure of C++11 &lt;regex&gt; on Windows using gcc.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite353.zip?download">Release 3.5.3</a>
    </h3>
    <ul>
	<li>
	Released 20 January 2015.
	</li>
	<li>
	Support removed for Windows 95, 98, and ME.
	</li>
	<li>
	Lexers added for Motorola S-Record files, Intel hex files, and Tektronix extended hex files with folding for Intel hex files.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1091/">Feature #1091.</a>
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1093/">Feature #1093.</a>
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1095/">Feature #1095.</a>
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1096/">Feature #1096.</a>
	</li>
	<li>
	C++ folder allows folding on square brackets '['.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1087/">Feature #1087.</a>
	</li>
	<li>
	Shell lexer fixes three issues with here-documents.
	<a href="https://sourceforge.net/p/scintilla/bugs/1672/">Bug #1672</a>.
	</li>
	<li>
	Verilog lexer highlights doc comment keywords; has separate styles for input, output, and inout ports
	(lexer.verilog.portstyling); fixes a bug in highlighting numbers; can treat upper-case identifiers as
	keywords (lexer.verilog.allupperkeywords); and can use different styles for code that is inactive due
	to preprocessor commands (lexer.verilog.track.preprocessor, lexer.verilog.update.preprocessor).
	</li>
	<li>
	When the calltip window is taller than the Scintilla window, leave it in a
	position that avoids overlapping the Scintilla text.
	</li>
	<li>
	When a text margin is displayed, for annotation lines, use the background colour of the base line.
	</li>
	<li>
	On Windows GDI, assume font names are encoded in UTF-8. This matches the Direct2D code path.
	</li>
	<li>
	Fix paste for GTK+ on OS X.
	<a href="https://sourceforge.net/p/scintilla/bugs/1677/">Bug #1677</a>.
	</li>
	<li>
	Reverted a fix on Qt where Qt 5.3 has returned to the behaviour of 4.x.
	<a href="https://sourceforge.net/p/scintilla/bugs/1575/">Bug #1575</a>.
	</li>
	<li>
	When the mouse is on the line between margin and text changed to treat as within text.
	This makes the PLAT_CURSES character cell platform work better.
	</li>
	<li>
	Fix a crash in SciTE when the command line is just "-close:".
	<a href="https://sourceforge.net/p/scintilla/bugs/1675/">Bug #1675</a>.
	</li>
	<li>
	Fix unexpected dialog in SciTE on Windows when the command line has a quoted filename then ends with a space.
	<a href="https://sourceforge.net/p/scintilla/bugs/1673/">Bug #1673</a>.
	</li>
	<li>
	On Windows and GTK+, use indicators for inline IME.
	</li>
	<li>
	SciTE shuts down quicker when there is no user-written OnClose function and no directors are attached.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite352.zip?download">Release 3.5.2</a>
    </h3>
    <ul>
	<li>
	Released 2 December 2014.
	</li>
	<li>
	For OS X Cocoa switch C++ runtime to libc++ to enable use of features that will never
	be added to libstdc++ including those part of C++11.
	Scintilla will now run only on OS X 10.7 or later and only in 64-bit mode.
	</li>
	<li>
	Include support for using C++11 &lt;regex&gt; for regular expression searches.
	Enabling this requires rebuilding Scintilla with a non-default option.
	This is a provisional feature and may change API before being made permanent.
	</li>
	<li>
	Allocate indicators used for Input Method Editors after 31 which was the previous limit of indicators to
	ensure no clash between the use of indicators for IME and for the application.
	</li>
	<li>
	ANNOTATION_INDENTED added which is similar to ANNOTATION_BOXED in terms of positioning
	but does not show a border.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1086/">Feature #1086.</a>
	</li>
	<li>
	Allow platform overrides for drawing tab arrows, wrap markers, and line markers.
	Size of double click detection area is a variable.
	These enable better visuals and behaviour for PLAT_CURSES as it is character cell based.
	</li>
	<li>
	CoffeeScript lexer fixes "/*" to not be a comment.
	<a href="https://sourceforge.net/p/scintilla/bugs/1420/">Bug #1420</a>.
	</li>
	<li>
	VHDL folder fixes "block" keyword.
	<a href="https://sourceforge.net/p/scintilla/bugs/1664/">Bug #1664</a>.
	</li>
	<li>
	Prevent caret blinking when holding down Delete key.
	<a href="https://sourceforge.net/p/scintilla/bugs/1657/">Bug #1657</a>.
	</li>
	<li>
	On Windows, allow right click selection in popup menu.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1080/">Feature #1080.</a>
	</li>
	<li>
	On Windows, only call ShowCaret in GDI mode as it interferes with caret drawing when using Direct2D.
	<a href="https://sourceforge.net/p/scintilla/bugs/1643/">Bug #1643</a>.
	</li>
	<li>
	On Windows, another DirectWrite mode SC_TECHNOLOGY_DIRECTWRITEDC added
	which may avoid drawing failures in some circumstances by drawing into a GDI DC.
	This feature is provisional and may be changed or removed if a better solution is found.
	</li>
	<li>
	On Windows, avoid processing mouse move events where the mouse has not moved as these can
	cause unexpected dwell start notifications.
	<a href="https://sourceforge.net/p/scintilla/bugs/1670/">Bug #1670</a>.
	</li>
	<li>
	For GTK+ on Windows, avoid extra space when pasting from external application.
	</li>
	<li>
	On GTK+ 2.x allow Scintilla to be used inside tool tips by changing when preedit window created.
	<a href="https://sourceforge.net/p/scintilla/bugs/1662/">Bug #1662</a>.
	</li>
	<li>
	Support MinGW compilation under Linux.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1077/">Feature #1077.</a>
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite351.zip?download">Release 3.5.1</a>
    </h3>
    <ul>
	<li>
	Released 30 September 2014.
	</li>
	<li>
	BibTeX lexer added.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1071/">Feature #1071.</a>
	</li>
	<li>
	SQL lexer supports the q-quote operator as SCE_SQL_QOPERATOR(24).
	</li>
	<li>
	VHDL lexer supports block comments.
	<a href="https://sourceforge.net/p/scintilla/bugs/1527/">Bug #1527</a>.
	</li>
	<li>
	VHDL folder fixes case where "component" used before name.
	<a href="https://sourceforge.net/p/scintilla/bugs/613/">Bug #613</a>.
	</li>
	<li>
	Restore fractional pixel tab positioning which was truncated to whole pixels in 3.5.0.
	<a href="https://sourceforge.net/p/scintilla/bugs/1652/">Bug #1652</a>.
	</li>
	<li>
	Allow choice between windowed and inline IME on some platforms.
	</li>
	<li>
	On GTK+ cache autocomplete window to avoid platform bug where windows
	were sometimes lost.
	<a href="https://sourceforge.net/p/scintilla/bugs/1649/">Bug #1649</a>.
	</li>
	<li>
	On GTK+ size autocomplete window more accurately.
	</li>
	<li>
	On Windows only unregister windows classes registered.
	<a href="https://sourceforge.net/p/scintilla/bugs/1639/">Bug #1639</a>.
	</li>
	<li>
	On Windows another DirectWrite mode SC_TECHNOLOGY_DIRECTWRITERETAIN added
	which may avoid drawing failures on some cards and drivers.
	This feature is provisional and may be changed or removed if a better solution is found.
	</li>
	<li>
	On Windows support the Visual Studio 2010+ clipboard format that indicates a line copy.
	<a href="https://sourceforge.net/p/scintilla/bugs/1636/">Bug #1636</a>.
	</li>
	<li>
	SciTE session files remember the scroll position.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite350.zip?download">Release 3.5.0</a>
    </h3>
    <ul>
	<li>
	Released 13 August 2014.
	</li>
	<li>
	Text may share space vertically so that extreme ascenders and descenders are
	not cut off by calling SCI_SETPHASESDRAW(SC_PHASES_MULTIPLE).
	</li>
	<li>
	Separate timers are used for each type of periodic activity and they are turned on and off
	as required. This saves power as there are fewer wake ups.
	On recent releases of OS X Cocoa and Windows, coalescing timers are used to further
	save power.
	<a href="https://sourceforge.net/p/scintilla/bugs/1086/">Bug #1086</a>.
	<a href="https://sourceforge.net/p/scintilla/bugs/1532/">Bug #1532</a>.
	</li>
	<li>
	Explicit tab stops may be set for each line.
	</li>
	<li>
	On Windows and GTK+, when using Korean input methods, IME composition is moved from a
	separate window into the Scintilla window.
	</li>
	<li>
	SciTE adds a "Clean" command to the "Tools" menu which is meant to be bound to a command like
	"make clean".
	</li>
	<li>
	Lexer added for Windows registry files.
	</li>
	<li>
	HTML lexer fixes a crash with SGML after a Mako comment.
	<a href="https://sourceforge.net/p/scintilla/bugs/1622/">Bug #1622</a>.
	</li>
	<li>
	KiXtart lexer adds a block comment state.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1053/">Feature #1053.</a>
	</li>
	<li>
	Matlab lexer fixes transpose operations like "X{1}'".
	<a href="https://sourceforge.net/p/scintilla/bugs/1629/">Bug #1629</a>.
	</li>
	<li>
	Ruby lexer fixes bugs with the syntax of symbols including allowing a symbol to end with '?'.
	<a href="https://sourceforge.net/p/scintilla/bugs/1627/">Bug #1627</a>.
	</li>
	<li>
	Rust lexer supports byte string literals, naked CR can be escaped in strings, and files starting with
	"#![" are not treated as starting with a hashbang comment.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1063/">Feature #1063.</a>
	</li>
	<li>
	Bug fixed where style data was stale when deleting a rectangular selection.
	</li>
	<li>
	Bug fixed where annotations disappeared when SCI_CLEARDOCUMENTSTYLE called.
	</li>
	<li>
	Bug fixed where selection not redrawn after SCI_DELWORDRIGHT.
	<a href="https://sourceforge.net/p/scintilla/bugs/1633/">Bug #1633</a>.
	</li>
	<li>
	Change the function prototypes to be complete for functions exported as "C".
	<a href="https://sourceforge.net/p/scintilla/bugs/1618/">Bug #1618</a>.
	</li>
	<li>
	Fix a memory leak on GTK+ with autocompletion lists.
	<a href="https://sourceforge.net/p/scintilla/bugs/1638/">Bug #1638</a>.
	</li>
	<li>
	On GTK+, use the full character width for the overstrike caret for multibyte characters.
	</li>
	<li>
	On Qt, set list icon size to largest icon. Add padding on OS X.
	<a href="https://sourceforge.net/p/scintilla/bugs/1634/">Bug #1634</a>.
	</li>
	<li>
	On Qt, fix building on FreeBSD 9.2.
	<a href="https://sourceforge.net/p/scintilla/bugs/1635/">Bug #1635</a>.
	</li>
	<li>
	On Qt, add a get_character method on the document.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1064/">Feature #1064.</a>
	</li>
	<li>
	On Qt, add SCI_* for methods to ScintillaConstants.py.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1065/">Feature #1065.</a>
	</li>
	<li>
	SciTE on GTK+ crash fixed with Insert Abbreviation command.
	</li>
	<li>
	For SciTE with read-only files and are.you.sure=0 reenable choice to save to another
	location when using Save or Close commands.
	</li>
	<li>
	Fix SciTE bug where toggle bookmark did not work after multiple lines with bookmarks merged.
	<a href="https://sourceforge.net/p/scintilla/bugs/1617/">Bug #1617</a>.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite344.zip?download">Release 3.4.4</a>
    </h3>
    <ul>
	<li>
	Released 3 July 2014.
	</li>
	<li>
	Style byte indicators removed. They were deprecated in 2007. Standard indicators should be used instead.
	Some elements used by lexers no longer take number of bits or mask arguments so lexers may need to be
	updated for LexAccessor::StartAt,  LexAccessor::SetFlags (removed),  LexerModule::LexerModule.
	</li>
	<li>
	When multiple selections are active, autocompletion text may be inserted at each selection with new
	SCI_AUTOCSETMULTI method.
	</li>
	<li>
	C++ lexer fixes crash for "#define x(".
	<a href="https://sourceforge.net/p/scintilla/bugs/1614/">Bug #1614</a>.
	</li>
	<li>
	C++ lexer fixes raw string recognition so that R"xxx(blah)xxx" is styled as SCE_C_STRINGRAW.
	</li>
	<li>
	The Postscript lexer no longer marks token edges with indicators as this used style byte indicators.
	</li>
	<li>
	The Scriptol lexer no longer displays indicators for poor indentation as this used style byte indicators.
	</li>
	<li>
	TCL lexer fixes names of keyword sets.
	<a href="https://sourceforge.net/p/scintilla/bugs/1615/">Bug #1615</a>.
	</li>
	<li>
	Shell lexer fixes fold matching problem caused by "&lt;&lt;&lt;".
	<a href="https://sourceforge.net/p/scintilla/bugs/1605/">Bug #1605</a>.
	</li>
	<li>
	Fix bug where indicators were not removed when fold highlighting on.
	<a href="https://sourceforge.net/p/scintilla/bugs/1604/">Bug #1604</a>.
	</li>
	<li>
	Fix bug on Cocoa where emoji were treated as being zero width.
	</li>
	<li>
	Fix crash on GTK+ with Ubuntu 12.04 and overlay scroll bars.
	</li>
	<li>
	Avoid creating a Cairo context when measuring text on GTK+ as future versions of GTK+
	may prohibit calling gdk_cairo_create except inside drawing handlers. This prohibition may
	be required on Wayland.
	</li>
	<li>
	On Cocoa, the registerNotifyCallback method is now marked as deprecated so client code that
	uses it will display an error message.
	Client code should use the delegate mechanism or subclassing instead.
	The method will be removed in the next version.
	</li>
	<li>
	On Cocoa, package Scintilla more in compliance with platform conventions.
	Only publish public headers in the framework headers directory.
	Only define the Scintilla namespace in Scintilla.h when compiling as C++.
	Use the Cocoa NS_ENUM and NS_OPTIONS macros for exposed enumerations.
	Hide internal methods from public headers.
	These changes are aimed towards publishing Scintilla as a module which will allow it to
	be used from the Swift programming language, although more changes will be needed here.
	</li>
	<li>
	Fix crash in SciTE when stream comment performed at line end.
	<a href="https://sourceforge.net/p/scintilla/bugs/1610/">Bug #1610</a>.
	</li>
	<li>
	For SciTE on Windows, display error message when common dialogs fail.
	<a href="https://sourceforge.net/p/scintilla/bugs/156/">Bug #156</a>.
	</li>
	<li>
	For SciTE on GTK+ fix bug with initialization of toggle buttons in find and replace strips.
	<a href="https://sourceforge.net/p/scintilla/bugs/1612/">Bug #1612</a>.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite343.zip?download">Release 3.4.3</a>
    </h3>
    <ul>
	<li>
	Released 27 May 2014.
	</li>
	<li>
	Fix hangs and crashes in DLL at shutdown on Windows when using Direct2D.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite342.zip?download">Release 3.4.2</a>
    </h3>
    <ul>
	<li>
	Released 22 May 2014.
	</li>
	<li>
	Insertions can be filtered or modified by calling SCI_CHANGEINSERTION inside a handler for
	SC_MOD_INSERTCHECK.
	</li>
	<li>
	DMIS lexer added. DMIS is a language for coordinate measuring machines.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1049/">Feature #1049.</a>
	</li>
	<li>
	Line state may be displayed in the line number margin to aid in debugging lexing and folding with
	SC_FOLDFLAG_LINESTATE (128).
	</li>
	<li>
	C++ lexer understands more preprocessor statements. #if defined SYMBOL is understood.
	Some macros with arguments can be understood and these may be predefined in keyword set 4
	(keywords5 for SciTE)
	with syntax similar to CHECKVERSION(x)=(x&lt;3).
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1051/">Feature #1051.</a>
	</li>
	<li>
	C++ lexer can highlight task marker keywords in comments as SCE_C_TASKMARKER.
	</li>
	<li>
	C++ lexer can optionally highlight escape sequences in strings as SCE_C_ESCAPESEQUENCE.
	</li>
	<li>
	C++ lexer supports Go back quoted raw string literals with lexer.cpp.backquoted.strings option.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1047/">Feature #1047.</a>
	</li>
	<li>
	SciTE performs word and search match highlighting as an idle task to improve interactivity
	and allow use of these features on large files.
	</li>
	<li>
	Bug fixed on Cocoa where previous caret lines were visible.
	<a href="https://sourceforge.net/p/scintilla/bugs/1593/">Bug #1593</a>.
	</li>
	<li>
	Bug fixed where caret remained invisible when period set to 0.
	<a href="https://sourceforge.net/p/scintilla/bugs/1592/">Bug #1592</a>.
	</li>
	<li>
	Fixed display flashing when scrolling with GTK+ 3.10.
	<a href="https://sourceforge.net/p/scintilla/bugs/1567/">Bug #1567</a>.
	</li>
	<li>
	Fixed calls and constants deprecated in GTK+ 3.10.
	</li>
	<li>
	Fixed bug on Windows where WM_GETTEXT did not provide data in UTF-16 for Unicode window.
	<a href="https://sourceforge.net/p/scintilla/bugs/685/">Bug #685</a>.
	</li>
	<li>
	For SciTE, protect access to variables used by threads with a mutex to prevent data races.
	</li>
	<li>
	For SciTE on GTK+ fix thread object leaks.
	Display the version of GTK+ compiled against in the about box.
	</li>
	<li>
	For SciTE on GTK+ 3.10, fix the size of the tab bar's content and use
	freedesktop.org standard icon names where possible.
	</li>
	<li>
	For SciTE on Windows, fix bug where invoking help resubmitted the
	running program.
	<a href="https://sourceforge.net/p/scintilla/bugs/272/">Bug #272</a>.
	</li>
	<li>
	SciTE's highlight current word feature no longer matches the selection when it contains space.
	</li>
	<li>
	For building SciTE in Visual C++, the win\SciTE.vcxproj project file should be used.
	The boundscheck directory and its project and solution files have been removed.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite341.zip?download">Release 3.4.1</a>
    </h3>
    <ul>
	<li>
	Released 1 April 2014.
	</li>
	<li>
	Display Unicode line ends as [LS], [PS], and [NEL] blobs.
	</li>
	<li>
	Bug fixed where cursor down failed on wrapped lines.
	<a href="https://sourceforge.net/p/scintilla/bugs/1585/">Bug #1585</a>.
	</li>
	<li>
	Caret positioning changed a little to appear inside characters less often by
	rounding the caret position to the pixel grid instead of truncating.
	<a href="https://sourceforge.net/p/scintilla/bugs/1588/">Bug #1588</a>.
	</li>
	<li>
	Bug fixed where automatic indentation wrong when caret in virtual space.
	<a href="https://sourceforge.net/p/scintilla/bugs/1586/">Bug #1586</a>.
	</li>
	<li>
	Bug fixed on Windows where WM_LBUTTONDBLCLK was no longer sent to window.
	<a href="https://sourceforge.net/p/scintilla/bugs/1587/">Bug #1587</a>.
	</li>
	<li>
	Bug fixed with SciTE on Windows XP where black stripes appeared inside the find and
	replace strips.
	</li>
	<li>
	Crash fixed in SciTE with recursive properties files.
	<a href="https://sourceforge.net/p/scintilla/bugs/1507/">Bug #1507</a>.
	</li>
	<li>
	Bug fixed with SciTE where Ctrl+E before an unmatched end brace jumps to file start.
	<a href="https://sourceforge.net/p/scintilla/bugs/315/">Bug #315</a>.
	</li>
	<li>
	Fixed scrolling on Cocoa to avoid display glitches and be smoother.
	</li>
	<li>
	Fixed crash on Cocoa when character composition used when autocompletion list active.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite340.zip?download">Release 3.4.0</a>
    </h3>
    <ul>
	<li>
	Released 22 March 2014.
	</li>
	<li>
	The Unicode line ends and substyles features added as provisional in 3.2.5 are now finalized.
	There are now no provisional features.
	</li>
	<li>
	Added wrap mode SC_WRAP_WHITESPACE which only wraps on whitespace, not on style changes.
	</li>
	<li>
	SciTE find and replace strips can perform incremental searching and temporary highlighting of all
	matches with the find.strip.incremental, replace.strip.incremental, and find.indicator.incremental settings.
	</li>
	<li>
	SciTE default settings changed to use strips for find and replace and to draw with Direct2D and
	DirectWrite on Windows.
	</li>
	<li>
	SciTE on Windows scales image buttons on the find and replace strips to match the current system scale factor.
	</li>
	<li>
	Additional assembler lexer variant As(SCLEX_AS) for Unix assembly code which uses '#' for comments and
	';' to separate statements.
	</li>
	<li>
	Fix Coffeescript lexer for keyword style extending past end of word.
	Also fixes styling 0...myArray.length all as a number.
	<a href="https://sourceforge.net/p/scintilla/bugs/1583/">Bug #1583</a>.
	</li>
	<li>
	Fix crashes and other bugs in Fortran folder by removing folding of do-label constructs.
	</li>
	<li>
	Deleting a whole line deletes the annotations on that line instead of the annotations on the next line.
	<a href="https://sourceforge.net/p/scintilla/bugs/1577/">Bug #1577</a>.
	</li>
	<li>
	Changed position of tall calltips to prefer lower half of screen to cut off end instead of start.
	</li>
	<li>
	Fix Qt bug where double click treated as triple click.
	<a href="https://sourceforge.net/p/scintilla/bugs/1575/">Bug #1575</a>.
	</li>
	<li>
	On Qt, selecting an item in an autocompletion list that is not currently visible positions it at the top.
	</li>
	<li>
	Fix bug on Windows when resizing autocompletion list with only short strings caused the list to move.
	</li>
	<li>
	On Cocoa reduce scrollable height by one line to fix bugs with moving caret
	up or down.
	</li>
	<li>
	On Cocoa fix calltips which did not appear when they were created in an off-screen position.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite339.zip?download">Release 3.3.9</a>
    </h3>
    <ul>
	<li>
	Released 31 January 2014.
	</li>
	<li>
	Fix 3.3.8 bug where external lexers became inaccessible.
	<a href="https://sourceforge.net/p/scintilla/bugs/1574/">Bug #1574</a>.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite338.zip?download">Release 3.3.8</a>
    </h3>
    <ul>
	<li>
	Released 28 January 2014.
	</li>
	<li>
	DropSelectionN API added to drop a selection from a multiple selection.
	</li>
	<li>
	CallTipSetPosStart API added to change the position at which backspacing removes the calltip.
	</li>
	<li>
	SC_MARK_BOOKMARK marker symbol added which looks like bookmark ribbons used in
	book reading applications.
	</li>
	<li>
	Basic lexer highlights hex, octal, and binary numbers in FreeBASIC which use the prefixes
	&amp;h, &amp;o and &amp;b respectively.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1041/">Feature #1041.</a>
	</li>
	<li>
	C++ lexer fixes bug where keyword followed immediately by quoted string continued
	keyword style.
	<a href="https://sourceforge.net/p/scintilla/bugs/1564/">Bug #1564</a>.
	</li>
	<li>
	Matlab lexer treats '!' differently for Matlab and Octave languages.
	<a href="https://sourceforge.net/p/scintilla/bugs/1571/">Bug #1571</a>.
	</li>
	<li>
	Rust lexer improved with nested comments, more compliant doc-comment detection,
	octal literals, NUL characters treated as valid, and highlighting of raw string literals and float literals fixed.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1038/">Feature #1038.</a>
	<a href="https://sourceforge.net/p/scintilla/bugs/1570/">Bug #1570</a>.
	</li>
	<li>
	On Qt expose the EOLMode on the document object.
	</li>
	<li>
	Fix hotspot clicking where area was off by half a character width.
	<a href="https://sourceforge.net/p/scintilla/bugs/1562/">Bug #1562</a>.
	</li>
	<li>
	Tweaked scroll positioning by either 2 pixels or 1 pixel when caret is at left or right of view
	to ensure caret is inside visible area.
	</li>
	<li>
	Send SCN_UPDATEUI with SC_UPDATE_SELECTION for Shift+Tab inside text.
	</li>
	<li>
	On Windows update the system caret position when scrolling to help screen readers
	see the scroll quickly.
	</li>
	<li>
	On Cocoa, GTK+, and Windows/Direct2D draw circles more accurately so that
	circular folding margin markers appear circular, of consistent size, and centred.
	Make SC_MARK_ARROWS drawing more even.
	Fix corners of SC_MARK_ROUNDRECT with Direct2D to be similar to other platforms.
	</li>
	<li>
	SciTE uses a bookmark ribbon symbol for bookmarks as it scales better to higher resolutions
	than the previous blue gem bitmap.
	</li>
	<li>
	SciTE will change the width of margins while running when the margin.width and fold.margin.width
	properties are changed.
	</li>
	<li>
	SciTE on Windows can display a larger tool bar with the toolbar.large property.
	</li>
	<li>
	SciTE displays a warning message when asked to open a directory.
	<a href="https://sourceforge.net/p/scintilla/bugs/1568/">Bug #1568</a>.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite337.zip?download">Release 3.3.7</a>
    </h3>
    <ul>
	<li>
	Released 12 December 2013.
	</li>
	<li>
	Lexer added for DMAP language.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1026/">Feature #1026.</a>
	</li>
	<li>
	Basic lexer supports multiline comments in FreeBASIC.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1023/">Feature #1023.</a>
	</li>
	<li>
	Bash lexer allows '#' inside words..
	<a href="https://sourceforge.net/p/scintilla/bugs/1553/">Bug #1553</a>.
	</li>
	<li>
	C++ lexer recognizes C++11 user-defined literals and applies lexical class SCE_C_USERLITERAL.
	</li>
	<li>
	C++ lexer allows single quote characters as digit separators in numeric literals like 123'456 as this is
	included in C++14.
	</li>
	<li>
	C++ lexer fixes bug with #include statements without " or &gt; terminating filename.
	<a href="https://sourceforge.net/p/scintilla/bugs/1538/">Bug #1538</a>.
	</li>
	<li>
	C++ lexer fixes split of Doxygen keywords @code{.fileExtension} and @param[in,out].
	<a href="https://sourceforge.net/p/scintilla/bugs/1551/">Bug #1551</a>.
	</li>
	<li>
	C++ lexer styles Doxygen keywords at end of document.
	</li>
	<li>
	Cmake lexer fixes bug with empty comments.
	<a href="https://sourceforge.net/p/scintilla/bugs/1550/">Bug #1550</a>.
	</li>
	<li>
	Fortran folder improved. Treats "else" as fold header.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/962/">Feature #962.</a>
	</li>
	<li>
	Fix bug with adjacent instances of the same indicator with different values where only the first was drawn.
	<a href="https://sourceforge.net/p/scintilla/bugs/1560/">Bug #1560</a>.
	</li>
	<li>
	For DirectWrite, use the GDI ClearType gamma value for SC_EFF_QUALITY_LCD_OPTIMIZED as
	this results in text that is similar in colour intensity to GDI.
	For the duller default DirectWrite ClearType text appearance, use SC_EFF_QUALITY_DEFAULT.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/887/">Feature #887.</a>
	</li>
	<li>
	Fix another problem with drawing on Windows with Direct2D when returning from lock screen.
	The whole window is redrawn as just redrawing the initially required area left other areas black.
	</li>
	<li>
	When scroll width is tracked, take width of annotation lines into account.
	</li>
	<li>
	For Cocoa on OS X 10.9, responsive scrolling is supported.
	</li>
	<li>
	On Cocoa, apply font quality setting to line numbers.
	<a href="https://sourceforge.net/p/scintilla/bugs/1544/">Bug #1544</a>.
	</li>
	<li>
	On Cocoa, clicking in margin now sets focus.
	<a href="https://sourceforge.net/p/scintilla/bugs/1542/">Bug #1542</a>.
	</li>
	<li>
	On Cocoa, correct cursor displayed in margin after showing dialog.
	</li>
	<li>
	On Cocoa, multipaste mode now works.
	<a href="https://sourceforge.net/p/scintilla/bugs/1541/">Bug #1541</a>.
	</li>
	<li>
	On GTK+, chain up to superclass finalize so that all finalization is performed.
	<a href="https://sourceforge.net/p/scintilla/bugs/1549/">Bug #1549</a>.
	</li>
	<li>
	On GTK+, fix horizontal scroll bar range to not be double the needed width.
	<a href="https://sourceforge.net/p/scintilla/bugs/1546/">Bug #1546</a>.
	</li>
	<li>
	On OS X GTK+, report control key as SCI_META for mouse down events.
	</li>
	<li>
	On Qt, bug fixed with drawing of scrollbars, where previous contents were not drawn over with some
	themes.
	</li>
	<li>
	On Qt, bug fixed with finding monitor rectangle which could lead to autocomplete showing at wrong location.
	</li>
	<li>
	SciTE fix for multiple message boxes when failing to save a file with save.on.deactivate.
	<a href="https://sourceforge.net/p/scintilla/bugs/1540/">Bug #1540</a>.
	</li>
	<li>
	SciTE on GTK+ fixes SIGCHLD handling so that Lua scripts can determine the exit status of processes
	they start.
	<a href="https://sourceforge.net/p/scintilla/bugs/1557/">Bug #1557</a>.
	</li>
	<li>
	SciTE on Windows XP fixes bad display of find and replace values when using strips.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite336.zip?download">Release 3.3.6</a>
    </h3>
    <ul>
	<li>
	Released 15 October 2013.
	</li>
	<li>
	Added functions to help convert between substyles and base styles and between secondary and primary styles.
	SCI_GETSTYLEFROMSUBSTYLE finds the base style of substyles.
	Can be used to treat all substyles of a style equivalent to that style.
	SCI_GETPRIMARYSTYLEFROMSTYLE finds the primary style of secondary styles.
	StyleFromSubStyle and PrimaryStyleFromStyle methods were added to ILexerWithSubStyles so each lexer can implement these.
	</li>
	<li>
	Lexer added for Rust language.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1024/">Feature #1024.</a>
	</li>
	<li>
	Avoid false matches in errorlist lexer which is used for the SciTE output pane
	by stricter checking of ctags lines.
	</li>
	<li>
	Perl lexer fixes bugs with multi-byte characters, including in HEREDOCs and PODs.
	<a href="https://sourceforge.net/p/scintilla/bugs/1528/">Bug #1528</a>.
	</li>
	<li>
	SQL folder folds 'create view' statements.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1020/">Feature #1020.</a>
	</li>
	<li>
	Visual Prolog lexer updated with better support for string literals and Unicode.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1025/">Feature #1025.</a>
	</li>
	<li>
	For SCI_SETIDENTIFIERS, \t, \r, and \n are allowed as well as space between identifiers.
	<a href="https://sourceforge.net/p/scintilla/bugs/1521/">Bug #1521</a>.
	</li>
	<li>
	Gaining and losing focus is now reported as a notification with the code set to SCN_FOCUSIN
	or SCN_FOCUSOUT.
	This allows clients to uniformly use notifications instead of commands.
	Since there is no longer a need for commands they will be deprecated in a future version.
	Clients should switch any code that currently uses SCEN_SETFOCUS or SCEN_KILLFOCUS.
	</li>
	<li>
	On Cocoa, clients should use the delegate mechanism or subclass ScintillaView in preference
	to registerNotifyCallback: which will be deprecated in the future.
	</li>
	<li>
	On Cocoa, the ScintillaView.h header hides internal implementation details from Platform.h and ScintillaCocoa.h.
	InnerView was renamed to SCIContentView and MarginView was renamed to SCIMarginView.
	dealloc removed from @interface.
	</li>
	<li>
	On Cocoa, clients may customize SCIContentView by subclassing both SCIContentView and ScintillaView
	and implementing the contentViewClass class method on the ScintillaView subclass to return the class of
	the SCIContentView subclass.
	</li>
	<li>
	On Cocoa, fixed appearance of alpha rectangles to use specified alpha and colour for outline as well as corner size.
	This makes INDIC_STRAIGHTBOX and INDIC_ROUNDBOX look correct.
	</li>
	<li>
	On Cocoa, memory leak fixed for MarginView.
	</li>
	<li>
	On Cocoa, make drag and drop work when destination view is empty.
	<a href="https://sourceforge.net/p/scintilla/bugs/1534/">Bug #1534</a>.
	</li>
	<li>
	On Cocoa, drag image fixed when view scrolled.
	</li>
	<li>
	On Cocoa, SCI_POSITIONFROMPOINTCLOSE fixed when view scrolled.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1021/">Feature #1021.</a>
	</li>
	<li>
	On Cocoa, don't send selection change notification when scrolling.
	<a href="https://sourceforge.net/p/scintilla/bugs/1522/">Bug #1522</a>.
	</li>
	<li>
	On Qt, turn off idle events on destruction to prevent repeatedly calling idle.
	</li>
	<li>
	Qt bindings in ScintillaEdit changed to use signed first parameter.
	</li>
	<li>
	Compilation errors fixed on Windows and GTK+ with SCI_NAMESPACE.
	</li>
	<li>
	On Windows, building with gcc will check if Direct2D headers are available and enable Direct2D if they are.
	</li>
	<li>
	Avoid attempts to redraw empty areas when lexing beyond the currently visible lines.
	</li>
	<li>
	Control more attributes of indicators in SciTE with find.mark.indicator and highlight.current.word.indicator
	properties.
	</li>
	<li>
	Fix SciTE bug with buffers becoming read-only.
	<a href="https://sourceforge.net/p/scintilla/bugs/1525/">Bug #1525</a>.
	</li>
	<li>
	Fix linking SciTE on non-Linux Unix systems with GNU toolchain by linking to libdl.
	<a href="https://sourceforge.net/p/scintilla/bugs/1523/">Bug #1523</a>.
	</li>
	<li>
	On Windows, SciTE's Incremental Search displays match failures by changing the background colour
	instead of not adding the character that caused failure.
	</li>
	<li>
	Fix SciTE on GTK+ 3.x incremental search to change foreground colour when no match as
	changing background colour is difficult.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite335.zip?download">Release 3.3.5</a>
    </h3>
    <ul>
	<li>
	Released 31 August 2013.
	</li>
	<li>
	Characters may be represented by strings.
	In Unicode mode C1 control characters are represented by their mnemonics.
	</li>
	<li>
	Added SCI_POSITIONRELATIVE to optimize navigation by character.
	</li>
	<li>
	Option to allow mouse selection to switch to rectangular by pressing Alt after start of gesture.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1007/">Feature #1007.</a>
	</li>
	<li>
	Lexer added for KVIrc script.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1008/">Feature #1008.</a>
	</li>
	<li>
	Bash lexer fixed quoted HereDoc delimiters.
	<a href="https://sourceforge.net/p/scintilla/bugs/1500/">Bug #1500</a>.
	</li>
	<li>
	MS SQL lexer fixed ';' to appear as an operator.
	<a href="https://sourceforge.net/p/scintilla/bugs/1509/">Bug #1509</a>.
	</li>
	<li>
	Structured Text lexer fixed styling of enumeration members.
	<a href="https://sourceforge.net/p/scintilla/bugs/1508/">Bug #1508</a>.
	</li>
	<li>
	Fixed bug with horizontal caret position when margin changed.
	<a href="https://sourceforge.net/p/scintilla/bugs/1512/">Bug #1512</a>.
	</li>
	<li>
	Fixed bug on Cocoa where coordinates were relative to text subview instead of whole view.
	</li>
	<li>
	Ensure selection redrawn correctly in two cases.
	When switching from stream to rectangular selection with Alt+Shift+Up.
	When reducing the range of an additional selection by moving mouse up.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1007/">Feature #1007.</a>
	</li>
	<li>
	Copy and paste of rectangular selections compatible with Borland Delphi IDE on Windows.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/1002/">Feature #1002.</a>
	<a href="https://sourceforge.net/p/scintilla/bugs/1513/">Bug #1513</a>.
	</li>
	<li>
	Initialize extended styles to the default style.
	</li>
	<li>
	On Windows, fix painting on an explicit HDC when first paint attempt abandoned.
	</li>
	<li>
	Qt bindings in ScintillaEdit made to work on 64-bit Unix systems.
	</li>
	<li>
	Easier access to printing on Qt with formatRange method.
	</li>
	<li>
	Fixed SciTE failure to save initial buffer in single buffer mode.
	<a href="https://sourceforge.net/p/scintilla/bugs/1339/">Bug #1339</a>.
	</li>
	<li>
	Fixed compilation problem with Visual C++ in non-English locales.
	<a href="https://sourceforge.net/p/scintilla/bugs/1506/">Bug #1506</a>.
	</li>
	<li>
	Disable Direct2D when compiling with MinGW gcc on Windows because of changes in the recent MinGW release.
	</li>
	<li>
	SciTE crash fixed for negative line.margin.width.
	<a href="https://sourceforge.net/p/scintilla/bugs/1504/">Bug #1504</a>.
	</li>
	<li>
	SciTE fix for infinite dialog boxes when failing to automatically save a file.
	<a href="https://sourceforge.net/p/scintilla/bugs/1503/">Bug #1503</a>.
	</li>
	<li>
	SciTE settings buffered.draw, two.phase.draw, and technology are applied to the
	output pane as well as the edit pane.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite334.zip?download">Release 3.3.4</a>
    </h3>
    <ul>
	<li>
	Released 19 July 2013.
	</li>
	<li>
	Handling of UTF-8 and DBCS text in lexers improved with methods ForwardBytes and
	GetRelativeCharacter added to StyleContext.
	<a href="https://sourceforge.net/p/scintilla/bugs/1483/">Bug #1483</a>.
	</li>
	<li>
	For Unicode text, case-insensitive searching and making text upper or lower case is now
	compliant with Unicode standards on all platforms and is much faster for non-ASCII characters.
	</li>
	<li>
	A CategoriseCharacter function was added to return the Unicode general category of a character
	which can be useful in lexers.
	</li>
	<li>
	On Cocoa, the LCD Optimized font quality level turns font smoothing on.
	</li>
	<li>
	SciTE 'immediate' subsystem added to allow scripts that work while tools are executed.
	</li>
	<li>
	Font quality exposed in SciTE as font.quality setting.
	</li>
	<li>
	On Cocoa, message:... methods simplify direct access to Scintilla and avoid call layers..
	</li>
	<li>
	A68K lexer updated.
	</li>
	<li>
	CoffeeScript lexer fixes a bug with comment blocks.
	<a href="https://sourceforge.net/p/scintilla/bugs/1495/">Bug #1495</a>
	</li>
	<li>
	ECL lexer regular expression code fixed.
	<a href="https://sourceforge.net/p/scintilla/bugs/1491/">Bug #1491</a>.
	</li>
	<li>
	errorlist lexer only recognizes Perl diagnostics when there is a filename between
	"at" and "line". Had been triggering for MSVC errors containing "at line".
	</li>
	<li>
	Haskell lexer fixed to avoid unnecessary full redraws.
	Don't highlight CPP inside comments when styling.within.preprocessor is on.
	<a href="https://sourceforge.net/p/scintilla/bugs/1459/">Bug #1459</a>.
	</li>
	<li>
	Lua lexer fixes bug in labels with UTF-8 text.
	<a href="https://sourceforge.net/p/scintilla/bugs/1483/">Bug #1483</a>.
	</li>
	<li>
	Perl lexer fixes bug in string interpolation with UTF-8 text.
	<a href="https://sourceforge.net/p/scintilla/bugs/1483/">Bug #1483</a>.
	</li>
	<li>
	Fixed bugs with case conversion when the result was longer or shorter than the original text.
	Could access past end of string potentially crashing.
	Selection now updated to result length.
	</li>
	<li>
	Fixed bug where data being inserted and removed was not being reported in
	notification messages. Bug was introduced in 3.3.2.
	</li>
	<li>
	Word wrap bug fixed where the last line could be shown twice.
	</li>
	<li>
	Word wrap bug fixed for lines wrapping too short on Windows and GTK+.
	</li>
	<li>
	Word wrap performance improved.
	</li>
	<li>
	Minor memory leak fixed.
	<a href="https://sourceforge.net/p/scintilla/bugs/1487/">Bug #1487</a>.
	</li>
	<li>
	On Cocoa, fixed insertText: method which was broken when implementing a newer protocol.
	</li>
	<li>
	On Cocoa, fixed a crash when performing string folding for bytes that do not represent a character
	in the current encoding.
	</li>
	<li>
	On Qt, fixed layout problem when QApplication construction delayed.
	</li>
	<li>
	On Qt, find_text reports failure with -1 as first element of return value.
	</li>
	<li>
	Fixed SciTE on GTK+ bug where a tool command could be performed using the keyboard while one was
	already running leading to confusion and crashes.
	<a href="https://sourceforge.net/p/scintilla/bugs/1486/">Bug #1486</a>.
	</li>
	<li>
	Fixed SciTE bug in Copy as RTF which was limited to first 32 styles.
	<a href="https://sourceforge.net/p/scintilla/bugs/1011/">Bug #1011</a>.
	</li>
	<li>
	Fixed SciTE on Windows user strip height when the system text scaling factor is 125% or 150%.
	</li>
	<li>
	Compile time checks for Digital Mars C++ removed.
	</li>
	<li>
	Visual C++ 2013 supported.
	<a href="https://sourceforge.net/p/scintilla/bugs/1492/">Bug #1492</a>.
	</li>
	<li>
	Python scripts used for building and maintenance improved and moved into scripts directory.
	</li>
	<li>
	Testing scripts now work on Linux using Qt and PySide.
	</li>
	<li>
	Tk platform defined.
	Implementation for Tk will be available separately from main Scintilla distribution.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite333.zip?download">Release 3.3.3</a>
    </h3>
    <ul>
	<li>
	Released 2 June 2013.
	</li>
	<li>
	Lexer and folder added for Structured Text language.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/959/">Feature #959.</a>
	</li>
	<li>
	Out of bounds access fixed for GTK+.
	<a href="https://sourceforge.net/p/scintilla/bugs/1480/">Bug #1480</a>.
	</li>
	<li>
	Crash fixed for GTK+ on Windows paste.
	</li>
	<li>
	Bug fixed with incorrect event copying on GTK+ 3.x.
	<a href="https://sourceforge.net/p/scintilla/bugs/1481/">Bug #1481</a>.
	</li>
	<li>
	Bug fixed with right to left locales, like Hebrew, on GTK+.
	<a href="https://sourceforge.net/p/scintilla/bugs/1477/">Bug #1477</a>.
	</li>
	<li>
	Bug fixed with undo grouping of tab and backtab commands.
	<a href="https://sourceforge.net/p/scintilla/bugs/1478/">Bug #1478</a>.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite332.zip?download">Release 3.3.2</a>
    </h3>
    <ul>
	<li>
	Released 22 May 2013.
	</li>
	<li>
	Basic implementations of common folding methods added to Scintilla to make it
	easier for containers to implement folding.
	</li>
	<li>
	Add indicator INDIC_COMPOSITIONTHICK, a thick low underline, to mimic an
	appearance used for Asian language input composition.
	</li>
	<li>
	On Cocoa, implement font quality setting.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/988/">Feature #988.</a>
	</li>
	<li>
	On Cocoa, implement automatic enabling of commands and added clear command.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/987/">Feature #987.</a>
	</li>
	<li>
	C++ lexer adds style for preprocessor doc comment.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/990/">Feature #990.</a>
	</li>
	<li>
	Haskell lexer and folder improved. Separate mode for literate Haskell "literatehaskell" SCLEX_LITERATEHASKELL.
	<a href="https://sourceforge.net/p/scintilla/bugs/1459/">Bug #1459 </a>.
	</li>
	<li>
	LaTeX lexer bug fixed for Unicode character following '\'.
	<a href="https://sourceforge.net/p/scintilla/bugs/1468/">Bug #1468 </a>.
	</li>
	<li>
	PowerShell lexer recognizes here strings and doccomment keywords.
	#region folding added.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/985/">Feature #985.</a>
	</li>
	<li>
	Fix multi-typing when two carets are located in virtual space on one line so that spaces
	are preserved.
	</li>
	<li>
	Fixes to input composition on Cocoa and implementation of accented character input through
	press and hold. Set selection correctly so that changes to pieces of composition text are easier to perform.
	Restore undo collection after a sequence of composition actions.
	Composition popups appear near input.
	</li>
	<li>
	Fix lexer problem where no line end was seen at end of document.
	</li>
	<li>
	Fix crash on Cocoa when view deallocated.
	<a href="https://sourceforge.net/p/scintilla/bugs/1466/">Bug #1466</a>.
	</li>
	<li>
	Fix Qt window positioning to not assume the top right of a monitor is at 0, 0.
	</li>
	<li>
	Fix Qt to not track mouse when widget is hidden.
	</li>
	<li>
	Qt now supports Qt 5.0.
	<a href="https://sourceforge.net/p/scintilla/bugs/1448/">Bug #1448</a>.
	</li>
	<li>
	Fix drawing on Windows with Direct2D when returning from lock screen.
	The render target had to be recreated and an area would be black since the drawing was not retried.
	</li>
	<li>
	Fix display of DBCS documents on Windows Direct2D/DirectWrite with default character set.
	</li>
	<li>
	For SciTE on Windows, fixed most-recently-used menu when files opened through check.if.already.opened.
	</li>
	<li>
	In SciTE, do not call OnSave twice when files saved asynchronously.
	</li>
	<li>
	Scintilla no longer builds with Visual C++ 6.0.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite331.zip?download">Release 3.3.1</a>
    </h3>
    <ul>
	<li>
	Released 11 April 2013.
	</li>
	<li>
	Autocompletion lists can now appear in priority order or be sorted by Scintilla.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/981/">Feature #981.</a>
	</li>
	<li>
	Most lexers now lex an extra NUL byte at the end of the
	document which makes it more likely they will classify keywords at document end correctly.
	<a href="https://sourceforge.net/p/scintilla/bugs/574/">Bug #574</a>,
	<a href="https://sourceforge.net/p/scintilla/bugs/588/">Bug #588.</a>
	</li>
	<li>
	Haskell lexer improved in several ways.
	<a href="https://sourceforge.net/p/scintilla/bugs/1459/">Bug #1459.</a>
	</li>
	<li>
	Matlab/Octave lexer recognizes block comments and ... comments.
	<a href="https://sourceforge.net/p/scintilla/bugs/1414/">Bug #1414.</a>
	</li>
	<li>
	Ruby lexer crash fixed with keyword at start of document.
	</li>
	<li>
	The PLAT_NCURSES platform now called PLAT_CURSES as may work on other implementations.
	</li>
	<li>
	Bug on Cocoa fixed where input composition with multiple selection or virtual space selection
	could make undo stop working.
	</li>
	<li>
	Direct2D/DirectWrite mode on Windows now displays documents in non-Latin1 8-bit encodings correctly.
	</li>
	<li>
	Character positioning corrected in Direct2D/DirectWrite mode on Windows to avoid text moving and cutting off
	lower parts of characters.
	</li>
	<li>
	Position of calltip and autocompletion lists fixed on Cocoa.
	</li>
	<li>
	While regular expression search in DBCS text is still not working, matching partial characters is now avoided
	by moving end of match to end of character.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite330.zip?download">Release 3.3.0</a>
    </h3>
    <ul>
	<li>
	Released 30 March 2013.
	</li>
	<li>
	Overlay scrollers and kinetic scrolling implemented on Cocoa.
	</li>
	<li>
	To improve display smoothness, styling and UI Update notifications will, when possible, be performed in
	a high-priority idle task on Cocoa instead of during painting.
	Performing these jobs inside painting can cause paints to be abandoned and a new paint scheduled.
	On GTK+, the high-priority idle task is used in more cases.
	</li>
	<li>
	SCI_SCROLLRANGE added to scroll the view to display a range of text.
	If the whole range can not be displayed, priority is given to one end.
	</li>
	<li>
	C++ lexer no longer recognizes raw (R"") strings when the first character after "
	is invalid.
	<a href="https://sourceforge.net/p/scintilla/bugs/1454/">Bug #1454.</a>
	</li>
	<li>
	HTML lexer recognizes JavaScript RegEx literals in more contexts.
	<a href="https://sourceforge.net/p/scintilla/bugs/1412/">Bug #1412.</a>
	</li>
	<li>
	Fixed automatic display of folded text when return pressed at end of fold header and
	first folded line was blank.
	<a href="https://sourceforge.net/p/scintilla/bugs/1455/">Bug #1455.</a>
	</li>
	<li>
	SCI_VISIBLEFROMDOCLINE fixed to never return a line beyond the document end.
	</li>
	<li>
	SCI_LINESCROLL fixed for a negative column offset.
	<a href="https://sourceforge.net/p/scintilla/bugs/1450/">Bug #1450.</a>
	</li>
	<li>
	On GTK+, fix tab markers so visible if indent markers are visible.
	<a href="https://sourceforge.net/p/scintilla/bugs/1453/">Bug #1453.</a>
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite325.zip?download">Release 3.2.5</a>
    </h3>
    <ul>
	<li>
	Released 26 February 2013.
	</li>
	<li>
	To allow cooperation between different uses of extended (beyond 255) styles they should be allocated
	using SCI_ALLOCATEEXTENDEDSTYLES.
	</li>
	<li>
	For Unicode documents, lexers that use StyleContext will retrieve whole characters
	instead of bytes.
	LexAccessor provides a LineEnd method which can be a more efficient way to
	handle line ends and can enable Unicode line ends.
	</li>
	<li>
	The C++ lexer understands the #undef directive when determining preprocessor definitions.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/978/">Feature #978.</a>
	</li>
	<li>
	The errorlist lexer recognizes gcc include path diagnostics that appear before an error.
	</li>
	<li>
	Folding implemented for GetText (PO)  translation language.
	<a href="https://sourceforge.net/p/scintilla/bugs/1437/">Bug #1437.</a>
	</li>
	<li>
	HTML lexer does not interrupt comment style for processing instructions.
	<a href="https://sourceforge.net/p/scintilla/bugs/1447/">Bug #1447.</a>
	</li>
	<li>
	Fix SciTE forgetting caret x-position when switching documents.
	<a href="https://sourceforge.net/p/scintilla/bugs/1442/">Bug #1442.</a>
	</li>
	<li>
	Fixed bug where vertical scrollbar thumb appeared at beginning of document when
	scrollbar shown.
	<a href="https://sourceforge.net/p/scintilla/bugs/1446/">Bug #1446.</a>
	</li>
	<li>
	Fixed brace-highlighting bug on OS X 10.8 where matching brace is on a different line.
	</li>
	<li>
	<a href="ScintillaDoc.html#ProvisionalMessages">Provisional features</a>
	are new features that may change or be removed if they cause problems but should become
	permanent if they work well.
	For this release <a href="ScintillaDoc.html#SCI_GETLINEENDTYPESSUPPORTED">Unicode line ends</a> and
	<a href="ScintillaDoc.html#Substyles">substyles</a>
	are provisional features.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite324.zip?download">Release 3.2.4</a>
    </h3>
    <ul>
	<li>
	Released 17 January 2013.
	</li>
	<li>
	Caret line highlight can optionally remain visible when window does not have focus.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/964/">Feature #964.</a>
	</li>
	<li>
	Delegate mechanism for notifications added on Cocoa.
	</li>
	<li>
	NUL characters in selection are copied to clipboard as spaces to avoid truncating
	at the NUL.
	<a href="https://sourceforge.net/p/scintilla/bugs/1289/">Bug #1289.</a>
	</li>
	<li>
	C++ lexer fixes problem with showing inactive sections when preprocessor lines contain trailing comment.
	<a href="https://sourceforge.net/p/scintilla/bugs/1413/">Bug #1413.</a>
	</li>
	<li>
	C++ lexer fixes problem with JavaScript regular expressions with '/' in character ranges.
	<a href="https://sourceforge.net/p/scintilla/bugs/1415/">Bug #1415.</a>
	</li>
	<li>
	LaTeX folder added.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/970/">Feature #970.</a>
	</li>
	<li>
	LaTeX lexer improves styling of math environments.
	<a href="https://sourceforge.net/p/scintilla/feature-requests/970/">Feature #970.</a>
	</li>
	<li>
	MySQL lexer implements hidden commands.
	</li>
	<li>
	Only produce a single undo step when autocompleting a single word.
	<a href="https://sourceforge.net/p/scintilla/bugs/1421/">Bug #1421.</a>
	</li>
	<li>
	Fixed crash when printing lines longer than 8000 characters.
	<a href="https://sourceforge.net/p/scintilla/bugs/1430/">Bug #1430.</a>
	</li>
	<li>
	Fixed problem in character movement extends selection mode where reversing
	direction collapsed the selection.
	</li>
	<li>
	Memory issues fixed on Cocoa, involving object ownership,
	lifetime of timers, and images held by the info bar.
	<a href="https://sourceforge.net/p/scintilla/bugs/1436/">Bug #1436.</a>
	</li>
	<li>
	Cocoa key binding for Alt+Delete changed to delete previous word to be more compatible with
	platform standards.
	</li>
	<li>
	Fixed crash on Cocoa with scrollbar when there is no scrolling possible.
	<a href="https://sourceforge.net/p/scintilla/bugs/1416/">Bug #1416.</a>
	</li>
	<li>
	On Cocoa with retina display fixed positioning of autocompletion lists.
	</li>
	<li>
	Fixed SciTE on Windows failure to run a batch file with a name containing a space by
	quoting the path in the properties file.
	<a href="https://sourceforge.net/p/scintilla/bugs/1423/">Bug #1423.</a>
	</li>
	<li>
	Fixed scaling bug when printing on GTK+.
	<a href="https://sourceforge.net/p/scintilla/bugs/1427/">Bug #1427.</a>
	</li>
	<li>
	SciTE on GTK toolbar.detachable feature removed.
	</li>
	<li>
	Fixed some background saving bugs in SciTE.
	<a href="https://sourceforge.net/p/scintilla/bugs/1366/">Bug #1366.</a>
	<a href="https://sourceforge.net/p/scintilla/bugs/1339/">Bug #1339.</a>
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite323.zip?download">Release 3.2.3</a>
    </h3>
    <ul>
	<li>
	Released 21 October 2012.
	</li>
	<li>
	Improve speed when performing multiple searches.
	</li>
	<li>
	SciTE adds definition of PLAT_UNIX for both PLAT_GTK and PLAT_MAC to allow consolidation of
	settings valid on all Unix variants.
	</li>
	<li>
	Signal autoCompleteCancelled added on Qt.
	</li>
	<li>
	Bash lexer supports nested delimiter pairs.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3569352&group_id=2439">Feature #3569352.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=1515556&group_id=2439">Bug #1515556.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3008483&group_id=2439">Bug #3008483.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3512208&group_id=2439">Bug #3512208.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3515392&group_id=2439">Bug #3515392.</a>
	</li>
	<li>
	For C/C++, recognize exponent in floating point hexadecimal literals.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3576454&group_id=2439">Bug #3576454.</a>
	</li>
	<li>
	For C #include statements, do not treat // in the path as a comment.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3519260&group_id=2439">Bug #3519260.</a>
	</li>
	<li>
	Lexer for GetText translations (PO) improved with additional styles and single instance limitation fixed.
	</li>
	<li>
	Ruby for loop folding fixed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3240902&group_id=2439">Bug #3240902.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3567391&group_id=2439">Bug #3567391.</a>
	</li>
	<li>
	Ruby recognition of here-doc after class or instance variable fixed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3567809&group_id=2439">Bug #3567809.</a>
	</li>
	<li>
	SQL folding of loop and case fixed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3567905&group_id=2439">Bug #3567905.</a>
	</li>
	<li>
	SQL folding of case with assignment fixed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3571820&group_id=2439">Bug #3571820.</a>
	</li>
	<li>
	Fix hang when removing all characters from indicator at end of document.
	</li>
	<li>
	Fix failure of \xhh in regular expression search for values greater than 0x79.
	</li>
	<li>
	On Cocoa on OS X 10.8, fix inverted drawing of find indicator.
	</li>
	<li>
	On Cocoa, fix double drawing when horizontal scroll range small and user swipes horizontally.
	</li>
	<li>
	On Cocoa, remove incorrect setting of save point when reading information through 'string' and 'selectedString'.
	</li>
	<li>
	On Cocoa, fix incorrect memory management of infoBar.
	</li>
	<li>
	On GTK+ 3 Ubuntu, fix crash when drawing margin.
	</li>
	<li>
	On ncurses, fix excessive spacing with italics line end.
	</li>
	<li>
	On Windows, search for D2D1.DLL and DWRITE.DLL in system directory to avoid loading from earlier
	in path where could be planted by malware.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite322.zip?download">Release 3.2.2</a>
    </h3>
    <ul>
	<li>
	Released 31 August 2012.
	</li>
	<li>
	Retina display support for Cocoa. Text size fixed.
	Scale factor for images implemented so they can be displayed in high definition.
	</li>
	<li>
	Implement INDIC_SQUIGGLEPIXMAP as a faster version of INDIC_SQUIGGLE.
	Avoid poor drawing at right of INDIC_SQUIGGLE.
	Align INDIC_DOTBOX to pixel grid for full intensity.
	</li>
	<li>
	Implement SCI_GETSELECTIONEMPTY API.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3543121&group_id=2439">Bug #3543121.</a>
	</li>
	<li>
	Added SCI_VCHOMEDISPLAY and SCI_VCHOMEDISPLAYEXTEND key commands.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3561433&group_id=2439">Feature #3561433.</a>
	</li>
	<li>
	Allow specifying SciTE Find in Files directory with find.in.directory property.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3558594&group_id=2439">Feature #3558594.</a>
	</li>
	<li>
	Override SciTE global strip.trailing.spaces with strip.trailing.spaces by pattern files.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3556320&group_id=2439">Feature #3556320.</a>
	</li>
	<li>
	Fix long XML script tag handling in XML lexer.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3534190&group_id=2439">Bug #3534190.</a>
	</li>
	<li>
	Fix rectangular selection range after backspace.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3543097&group_id=2439">Bug #3543097.</a>
	</li>
	<li>
	Send SCN_UPDATEUI with SC_UPDATE_SELECTION for backspace in virtual space.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3543121&group_id=2439">Bug #3543121.</a>
	</li>
	<li>
	Avoid problems when calltip highlight range is negative.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3545938&group_id=2439">Bug #3545938.</a>
	</li>
	<li>
	On Cocoa, fix image drawing code so that image is not accessed after being freed
	and is drawn in the correct location.
	</li>
	<li>
	On Cocoa, limit horizontal touch scrolling to existing established width.
	</li>
	<li>
	On Cocoa, decrease sensitivity of pinch-zoom.
	</li>
	<li>
	Fix Cocoa drawing where style changes were not immediately visible.
	</li>
	<li>
	Fix Cocoa memory leak due to reference cycle.
	</li>
	<li>
	Fix Cocoa bug where notifications were sent after Scintilla was freed.
	</li>
	<li>
	SciTE on OS X user shortcuts treats "Ctrl+D" as equivalent to "Ctrl+d".
	</li>
	<li>
	On Windows, saving SciTE's Lua startup script causes it to run.
	</li>
	<li>
	Limit time allowed to highlight current word in SciTE to 0.25 seconds to remain responsive.
	</li>
	<li>
	Fixed SciTE read-only mode to stick with buffer.
	</li>
	<li>
	For SciTE on Windows, enable Ctrl+Z, Ctrl+X, and Ctrl+C (Undo, Cut, and Copy) in the
	editable fields of find and replace strips
	</li>
	<li>
	Remove limit on logical line length in SciTE .properties files.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3544312&group_id=2439">Bug #3544312.</a>
	</li>
	<li>
	Improve performance of SciTE Save As command.
	</li>
	<li>
	Fix SciTE crash with empty .properties files. Bug #3545938.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3555308&group_id=2439">Bug #3555308.</a>
	</li>
	<li>
	Fix repeated letter in SciTE calltips.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3545938&group_id=2439">Bug #3545938.</a>
	</li>
	<li>
	Refine build time checking for Direct2D and DirectWrite.
	</li>
	<li>
	Avoid potential build problems on Windows with MultiMon.h by explicitly checking for multi-monitor APIs.
	</li>
	<li>
	Automatically disable themed drawing in SciTE when building on Windows 2000.
	Reenable building for Windows NT 4 on NT 4 .
	</li>
	<li>
	Added ncurses platform definitions. Implementation is maintained separately as
	<a href="https://foicica.com/scinterm/">Scinterm</a>.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite321.zip?download">Release 3.2.1</a>
    </h3>
    <ul>
	<li>
	Released 14 July 2012.
	</li>
	<li>
	In Scintilla.iface, specify features as properties instead of functions where possible and fix some enumerations.
	</li>
	<li>
	In SciTE Lua scripts, string properties in Scintilla API can be retrieved as well as set using property notation.
	</li>
	<li>
	Added character class APIs: SCI_SETPUNCTUATIONCHARS, SCI_GETWORDCHARS, SCI_GETWHITESPACECHARS,
	and SCI_GETPUNCTUATIONCHARS.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3529805&group_id=2439">Feature #3529805.</a>
	</li>
	<li>
	Less/Hss support added to CSS lexer.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3532413&group_id=2439">Feature #3532413.</a>
	</li>
	<li>
	C++ lexer style SCE_C_PREPROCESSORCOMMENT added for stream comments in preprocessor.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3487406&group_id=2439">Bug #3487406.</a>
	</li>
	<li>
	Fix incorrect styling of inactive code in C++ lexer.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3533036&group_id=2439">Bug #3533036.</a>
	</li>
	<li>
	Fix incorrect styling by C++ lexer after empty lines in preprocessor style.
	</li>
	<li>
	C++ lexer option "lexer.cpp.allow.dollars" fixed so can be turned off after being on.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3541461&group_id=2439">Bug #3541461.</a>
	</li>
	<li>
	Fortran fixed format lexer fixed to style comments from column 73.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3540486&group_id=2439">Bug #3540486.</a>
	</li>
	<li>
	Fortran folder folds CRITICAL .. END CRITICAL.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3540486&group_id=2439">Bug #3540486.</a>
	</li>
	<li>
	Fortran lexer fixes styling after comment line ending with '&amp;'.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3087226&group_id=2439">Bug #3087226.</a>
	</li>
	<li>
	Fortran lexer styles preprocessor lines so they do not trigger incorrect folding.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2906275&group_id=2439">Bug #2906275.</a>
	</li>
	<li>
	Fortran folder fixes folding of nested ifs.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2809176&group_id=2439">Bug #2809176.</a>
	</li>
	<li>
	HTML folder fixes folding of CDATA when fold.html.preprocessor=0.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3540491&group_id=2439">Bug #3540491.</a>
	</li>
	<li>
	On Cocoa, fix autocompletion font lifetime issue and row height computation.
	</li>
	<li>
	In 'choose single' mode, autocompletion will close an existing list if asked to display a single entry list.
	</li>
	<li>
	Fixed SCI_MARKERDELETE to only delete one marker per call.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3535806&group_id=2439">Bug #3535806.</a>
	</li>
	<li>
	Properly position caret after undoing coalesced delete operations.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3523326&group_id=2439">Bug #3523326.</a>
	</li>
	<li>
	Ensure margin is redrawn when SCI_MARGINSETSTYLE called.
	</li>
	<li>
	Fix clicks in first pixel of margins to send SCN_MARGINCLICK.
	</li>
	<li>
	Fix infinite loop when drawing block caret for a zero width space character at document start.
	</li>
	<li>
	Crash fixed for deleting negative range.
	</li>
	<li>
	For characters that overlap the beginning of their space such as italics descenders and bold serifs, allow start
	of text to draw 1 pixel into margin.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=699587&group_id=2439">Bug #699587.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3537799&group_id=2439">Bug #3537799.</a>
	</li>
	<li>
	Fixed problems compiling Scintilla for Qt with GCC 4.7.1 x64.
	</li>
	<li>
	Fixed problem with determining GTK+ sub-platform caused when adding Qt support in 3.2.0.
	</li>
	<li>
	Fix incorrect measurement of untitled file in SciTE on Linux leading to message "File ...' is 2147483647 bytes long".
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3537764&group_id=2439">Bug #3537764.</a>
	</li>
	<li>
	In SciTE, fix open of selected filename with line number to go to that line.
	</li>
	<li>
	Fix problem with last visible buffer closing in SciTE causing invisible buffers to be active.
	</li>
	<li>
	Avoid blinking of SciTE's current word highlight when output pane changes.
	</li>
	<li>
	SciTE properties files can be longer than 60K.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite320.zip?download">Release 3.2.0</a>
    </h3>
    <ul>
	<li>
	Released 1 June 2012.
	</li>
	<li>
	Platform layer added for the Qt open-source cross-platform application and user interface framework
	for development in C++ or in Python with the PySide bindings for Qt.
	</li>
	<li>
	Direct access provided to the document bytes for ranges within Scintilla.
	This is similar to the existing SCI_GETCHARACTERPOINTER API but allows for better performance.
	</li>
	<li>
	Ctrl+Double Click and Ctrl+Triple Click add the word or line to the set of selections.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3520037&group_id=2439">Feature #3520037.</a>
	</li>
	<li>
	A SCI_DELETERANGE API was added for deleting a range of text.
	</li>
	<li>
	Line wrap markers may now be drawn in the line number margin.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3518198&group_id=2439">Feature #3518198.</a>
	</li>
	<li>
	SciTE on OS X adds option to hide hidden files in the open dialog box.
	</li>
	<li>
	Lexer added for OScript language.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3523197&group_id=2439">Feature #3523197.</a>
	</li>
	<li>
	Lexer added for Visual Prolog language.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3523018&group_id=2439">Feature #3523018.</a>
	</li>
	<li>
	UTF-8 validity is checked more stringently and consistently. All 66 non-characters are now treated as invalid.
	</li>
	<li>
	HTML lexer bug fixed with inconsistent highlighting for PHP when attribute on separate line from tag.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3520027&group_id=2439">Bug #3520027.</a>
	</li>
	<li>
	HTML lexer bug fixed for JavaScript block comments.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3520032&group_id=2439">Bug #3520032.</a>
	</li>
	<li>
	Annotation drawing bug fixed when box displayed with different colours on different lines.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3519872&group_id=2439">Bug #3519872.</a>
	</li>
	<li>
	On Windows with Direct2D, fix drawing with 125% and 150% DPI system settings.
	</li>
	<li>
	Virtual space selection bug fixed for rectangular selections.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3519246&group_id=2439">Bug #3519246.</a>
	</li>
	<li>
	Replacing multiple selection with newline changed to only affect main selection.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3522251&group_id=2439">Bug #3522251.</a>
	</li>
	<li>
	Replacing selection with newline changed to group deletion and insertion as a single undo action.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3522250&group_id=2439">Bug #3522250.</a>
	</li>
	<li>
	Auto-completion lists on GTK+ 3 set height correctly instead of showing too few lines.
	</li>
	<li>
	Mouse wheel scrolling changed to avoid GTK+ bug in recent distributions.
	</li>
	<li>
	IME bug on Windows fixed for horizontal jump.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3529728&group_id=2439">Bug #3529728.</a>
	</li>
	<li>
	SciTE case-insensitive autocompletion filters equal identifiers better.
	Calltip arrows work with bare word identifiers.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3517810&group_id=2439">Bug #3517810.</a>
	</li>
	<li>
	SciTE bug fixed where shbang lines not setting file type when switching
	to file loaded in background.
	</li>
	<li>
	SciTE on GTK+ shows open and save dialogs with the directory of the current file displayed.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite310.zip?download">Release 3.1.0</a>
    </h3>
    <ul>
	<li>
	Released 20 April 2012.
	</li>
	<li>
	Animated find indicator added on Cocoa.
	</li>
	<li>
	Buttons can be made default in SciTE user strips.
	</li>
	<li>
	SciTE allows find and replace histories to be saved in session.
	</li>
	<li>
	Option added to allow case-insensitive selection in auto-completion lists.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3516538&group_id=2439">Bug #3516538.</a>
	</li>
	<li>
	Replace \0 by complete found text in regular expressions.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3510979&group_id=2439">Feature #3510979.</a>
	</li>
	<li>
	Fixed single quoted strings in bash lexer.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3512208&group_id=2439">Bug #3512208.</a>
	</li>
	<li>
	Incorrect highlighting fixed in C++ lexer for continued lines.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3509317&group_id=2439">Bug #3509317.</a>
	</li>
	<li>
	Hang fixed in diff lexer.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3508602&group_id=2439">Bug #3508602.</a>
	</li>
	<li>
	Folding improved for SQL CASE/MERGE statement.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3503277&group_id=2439">Bug #3503277.</a>
	</li>
	<li>
	Fix extra drawing of selection inside word wrap indentation.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3515555&group_id=2439">Bug #3515555.</a>
	</li>
	<li>
	Fix problem with determining the last line that needs styling when drawing.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3514882&group_id=2439">Bug #3514882.</a>
	</li>
	<li>
	Fix problems with drawing in margins.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3514882&group_id=2439">Bug #3514882.</a>
	</li>
	<li>
	Fix printing crash when using Direct2D to display on-screen.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3513946&group_id=2439">Bug #3513946.</a>
	</li>
	<li>
	Fix SciTE bug where background.*.size disabled restoration of bookmarks and positions from session.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3514885&group_id=2439">Bug #3514885.</a>
	</li>
	<li>
	Fixed the Move Selected Lines command when last line does not end with a line end character.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3511023&group_id=2439">Bug #3511023.</a>
	</li>
	<li>
	Fix word wrap indentation printing to use printer settings instead of screen settings.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3512961&group_id=2439">Bug #3512961.</a>
	</li>
	<li>
	Fix SciTE bug where executing an empty command prevented executing further commands
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3512976&group_id=2439">Bug #3512976.</a>
	</li>
	<li>
	Fix SciTE bugs with focus in user strips and made strips more robust with invalid definitions.
	</li>
	<li>
	Suppress SciTE regular expression option when searching with find next selection.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3510985&group_id=2439">Bug #3510985.</a>
	</li>
	<li>
	SciTE Find in Files command matches empty pattern to all files.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3495918&group_id=2439">Feature #3495918.</a>
	</li>
	<li>
	Fix scroll with mouse wheel on GTK+.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3501321&group_id=2439">Bug #3501321.</a>
	</li>
	<li>
	Fix column finding method so that tab is counted correctly.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3483713&group_id=2439">Bug #3483713.</a>
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite304.zip?download">Release 3.0.4</a>
    </h3>
    <ul>
	<li>
	Released 8 March 2012.
	</li>
	<li>
	SciTE scripts can create user interfaces as strips.
	</li>
	<li>
	SciTE can save files automatically in the background.
	</li>
	<li>
	Pinch zoom implemented on Cocoa.
	</li>
	<li>
	ECL lexer added.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3488209&group_id=2439">Feature #3488209.</a>
	</li>
	<li>
	CPP lexer fixes styling after document comment keywords.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3495445&group_id=2439">Bug #3495445.</a>
	</li>
	<li>
	Pascal folder improves handling of some constructs.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3486385&group_id=2439">Feature #3486385.</a>
	</li>
	<li>
	XML lexer avoids entering a bad mode due to complex preprocessor instructions.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3488060&group_id=2439">Bug #3488060.</a>
	</li>
	<li>
	Duplicate command is always remembered as a distinct command for undo.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3495836&group_id=2439">Bug #3495836.</a>
	</li>
	<li>
	SciTE xml.auto.close.tags no longer closes with PHP code similar to &lt;a $this-&gt;
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3488067&group_id=2439">Bug #3488067.</a>
	</li>
	<li>
	Fix bug where setting an indicator for the whole document would fail.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3487440&group_id=2439">Bug #3487440.</a>
	</li>
	<li>
	Crash fixed for SCI_MOVESELECTEDLINESDOWN with empty vertical selection.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3496403&group_id=2439">Bug #3496403.</a>
	</li>
	<li>
	Differences between buffered and unbuffered mode on Direct2D eliminated.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3495791&group_id=2439">Bug #3495791.</a>
	</li>
	<li>
	Font leading implemented for Direct2D to improve display of character blobs.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3494744&group_id=2439">Bug #3494744.</a>
	</li>
	<li>
	Fractional widths used for line numbers, character markers and other situations.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3494492&group_id=2439">Bug #3494492.</a>
	</li>
	<li>
	Translucent rectangles drawn using Direct2D with sharper corners.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3494492&group_id=2439">Bug #3494492.</a>
	</li>
	<li>
	RGBA markers drawn sharper when centred using Direct2D.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3494202&group_id=2439">Bug #3494202.</a>
	</li>
	<li>
	RGBA markers are drawn centred when taller than line.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3494184&group_id=2439">Bug #3494184.</a>
	</li>
	<li>
	Image marker drawing problem fixed for markers taller than line.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3493503&group_id=2439">Bug #3493503.</a>
	</li>
	<li>
	Markers are drawn horizontally off-centre based on margin type instead of dimensions.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3488696&group_id=2439">Bug #3488696.</a>
	</li>
	<li>
	Fold tail markers drawn vertically centred.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3488289&group_id=2439">Feature #3488289.</a>
	</li>
	<li>
	On Windows, Scintilla is more responsive in wrap mode.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3487397&group_id=2439">Bug #3487397.</a>
	</li>
	<li>
	Unimportant "Gdk-CRITICAL" messages are no longer displayed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3488481&group_id=2439">Bug #3488481.</a>
	</li>
	<li>
	SciTE on Windows Find in Files sets focus to dialog when already created; allows opening dialog when a job is running.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3480635&group_id=2439">Bug #3480635.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3486657&group_id=2439">Bug #3486657.</a>
	</li>
	<li>
	Fixed problems with multiple clicks in margin and with mouse actions combined with virtual space.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3484370&group_id=2439">Bug #3484370.</a>
	</li>
	<li>
	Fixed bug with using page up and down and not returning to original line.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3485669&group_id=2439">Bug #3485669.</a>
	</li>
	<li>
	Down arrow with wrapped text no longer skips lines.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=1776560&group_id=2439">Bug #1776560.</a>
	</li>
	<li>
	Fix problem with dwell ending immediately due to word wrap.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3484416&group_id=2439">Bug #3484416.</a>
	</li>
	<li>
	Wrapped lines are rewrapped more consistently while resizing window.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3484179&group_id=2439">Bug #3484179.</a>
	</li>
	<li>
	Selected line ends are highlighted more consistently.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3484330&group_id=2439">Bug #3484330.</a>
	</li>
	<li>
	Fix grey background on files that use shbang to choose language.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3482777&group_id=2439">Bug #3482777.</a>
	</li>
	<li>
	Fix failure messages from empty commands in SciTE.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3480645&group_id=2439">Bug #3480645.</a>
	</li>
	<li>
	Redrawing reduced for some marker calls.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3493530&group_id=2439">Feature #3493530.</a>
	</li>
	<li>
	Match brace and select brace commands work in SciTE output pane.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3486598&group_id=2439">Feature #3486598.</a>
	</li>
	<li>
	Performing SciTE "Show Calltip" command when a calltip is already visible shows the next calltip.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3487017&group_id=2439">Feature #3487017.</a>
	</li>
	<li>
	SciTE allows saving file even when file unchanged.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3486654&group_id=2439">Feature #3486654.</a>
	</li>
	<li>
	SciTE allows optional use of character escapes in calltips.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3495239&group_id=2439">Feature #3495239.</a>
	</li>
	<li>
	SciTE can open file:// URLs with Ctrl+Shift+O.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3495389&group_id=2439">Feature #3495389.</a>
	</li>
	<li>
	Key modifiers updated for GTK+ on OS X to match upstream changes.
	</li>
	<li>
	SciTE hang when marking all occurrences of regular expressions fixed.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite303.zip?download">Release 3.0.3</a>
    </h3>
    <ul>
	<li>
	Released 28 January 2012.
	</li>
	<li>
	Printing works on GTK+ version 2.x as well as 3.x.
	</li>
	<li>
	Lexer added for the AviSynth language.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3475611&group_id=2439">Feature #3475611.</a>
	</li>
	<li>
	Lexer added for the Take Command / TCC scripting language.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3462462&group_id=2439">Feature #3462462.</a>
	</li>
	<li>
	CSS lexer gains support for SCSS.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3268017&group_id=2439">Feature #3268017.</a>
	</li>
	<li>
	CPP lexer fixes problems in the preprocessor structure caused by continuation lines.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3458508&group_id=2439">Bug #3458508.</a>
	</li>
	<li>
	Errorlist lexer handles column numbers for GCC format diagnostics.
	In SciTE, Next Message goes to column where this can be decoded from GCC format diagnostics.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3453075&group_id=2439">Feature #3453075.</a>
	</li>
	<li>
	HTML folder fixes spurious folds on some tags.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3459262&group_id=2439">Bug #3459262.</a>
	</li>
	<li>
	Ruby lexer fixes bug where '=' at start of file caused whole file to appear as a comment.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3452488&group_id=2439">Bug #3452488.</a>
	</li>
	<li>
	SQL folder folds blocks of single line comments.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3467425&group_id=2439">Feature #3467425.</a>
	</li>
	<li>
	On Windows using Direct2D, defer invalidation of render target until completion of painting to avoid failures.
	</li>
	<li>
	Further support of fractional positioning. Spaces, tabs, and single character tokens can take fractional space
	and wrapped lines are positioned taking fractional positions into account.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3471998&group_id=2439">Bug #3471998.</a>
	</li>
	<li>
	On Windows using Direct2D, fix extra carets appearing.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3471998&group_id=2439">Bug #3471998.</a>
	</li>
	<li>
	For autocompletion lists Page Up and Down move by the list height instead of by 5 lines.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3455493&group_id=2439">Bug #3455493.</a>
	</li>
	<li>
	For SCI_LINESCROLLDOWN/UP don't select into virtual space.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3451681&group_id=2439">Bug #3451681.</a>
	</li>
	<li>
	Fix fold highlight not being fully drawn.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3469936&group_id=2439">Bug #3469936.</a>
	</li>
	<li>
	Fix selection margin appearing black when starting in wrap mode.
	</li>
	<li>
	Fix crash when changing end of document after adding an annotation.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3476637&group_id=2439">Bug #3476637.</a>
	</li>
	<li>
	Fix problems with building to make RPMs.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3476149&group_id=2439">Bug #3476149.</a>
	</li>
	<li>
	Fix problem with building on GTK+ where recent distributions could not find gmodule.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3469056&group_id=2439">Bug #3469056.</a>
	</li>
	<li>
	Fix problem with installing SciTE on GTK+ due to icon definition in .desktop file including an extension.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3476117&group_id=2439">Bug #3476117.</a>
	</li>
	<li>
	Fix SciTE bug where new buffers inherited some properties from previously opened file.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3457060&group_id=2439">Bug #3457060.</a>
	</li>
	<li>
	Fix focus when closing tab in SciTE with middle click. Focus moves to edit pane instead of staying on tab bar.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3440142&group_id=2439">Bug #3440142.</a>
	</li>
	<li>
	For SciTE on Windows fix bug where Open Selected Filename for URL would append a file extension.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3459185&group_id=2439">Feature #3459185.</a>
	</li>
	<li>
	For SciTE on Windows fix key handling of control characters in Parameters dialog so normal editing (Ctrl+C, ...) works.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3459345&group_id=2439">Bug #3459345.</a>
	</li>
	<li>
	Fix SciTE bug where files became read-only after saving. Drop the "*" dirty marker after save completes.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3467432&group_id=2439">Bug #3467432.</a>
	</li>
	<li>
	For SciTE handling of diffs with "+++" and "---" lines, also handle case where not followed by tab.
	Go to correct line for diff "+++" message.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3467143&group_id=2439">Bug #3467143.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3467178&group_id=2439">Bug #3467178.</a>
	</li>
	<li>
	SciTE on GTK+ now performs threaded actions even on GTK+ versions before 2.12.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite302.zip?download">Release 3.0.2</a>
    </h3>
    <ul>
	<li>
	Released 9 December 2011.
	</li>
	<li>
	SciTE saves files in the background without blocking the user interface.
	</li>
	<li>
	Printing implemented in SciTE on GTK+ 3.x.
	</li>
	<li>
	ILoader interface for background loading finalized and documented.
	</li>
	<li>
	CoffeeScript lexer added.
	</li>
	<li>
	C++ lexer fixes crash with "#if defined( XXX 1".
	</li>
	<li>
	Crash with Direct2D on Windows fixed.
	</li>
	<li>
	Backspace removing protected range fixed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3445911&group_id=2439">Bug #3445911.</a>
	</li>
	<li>
	Cursor setting failure on Windows when screen saver on fixed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3438780&group_id=2439">Bug #3438780.</a>
	</li>
	<li>
	SciTE on GTK+ hang fixed with -open:file option.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3441980&group_id=2439">Bug #3441980.</a>
	</li>
	<li>
	Failure to evaluate shbang fixed in SciTE.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3441801&group_id=2439">Bug #3441801.</a>
	</li>
	<li>
	SciTE failure to treat files starting with "&lt;?xml" as XML fixed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3440718&group_id=2439">Bug #3440718.</a>
	</li>
	<li>
	Made untitled tab saveable when created by closing all files.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3440244&group_id=2439">Bug #3440244.</a>
	</li>
	<li>
	SciTE crash fixed when using Scintillua.
	</li>
	<li>
	SciTE revert command fixed so that undo works on individual actions instead of undoing to revert point.
	</li>
	<li>
	Focus loss in SciTE when opening a recent file fixed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3440142&group_id=2439">Bug #3440142.</a>
	</li>
	<li>
	Fixed SciTE SelLength property to measure characters instead of bytes.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3283519&group_id=2439">Bug #3283519.</a>
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite301.zip?download">Release 3.0.1</a>
    </h3>
    <ul>
	<li>
	Released 15 November 2011.
	</li>
	<li>
	SciTE on Windows now runs Lua scripts directly on the main thread instead of starting them on a
	secondary thread and then moving back to the main thread.
	</li>
	<li>
	Highlight "else" as a keyword for TCL in the same way as other languages.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=1836954&group_id=2439">Bug #1836954.</a>
	</li>
	<li>
	Fix problems with setting fonts for autocompletion lists on Windows where
	font handles were copied and later deleted causing a system default font to be used.
	</li>
	<li>
	Fix font size used on Windows for Asian language input methods which sometimes led to IME not being visible.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3436753&group_id=2439">Bug #3436753.</a>
	</li>
	<li>
	Fixed polygon drawing on Windows so fold symbols are visible again.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3433558&group_id=2439">Bug #3433558.</a>
	</li>
	<li>
	Changed background drawing on GTK+ to allow for fractional character positioning as occurs on OS X
	as this avoids faint lines at lexeme boundaries.
	</li>
	<li>
	Ensure pixmaps allocated before painting as there was a crash when Scintilla drew without common initialization calls.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3432354&group_id=2439">Bug #3432354.</a>
	</li>
	<li>
	Fixed SciTE on Windows bug causing wrong caret position after indenting a selection.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3433433&group_id=2439">Bug #3433433.</a>
	</li>
	<li>
	Fixed SciTE session saving to store buffer position matching buffer.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3434372&group_id=2439">Bug #3434372.</a>
	</li>
	<li>
	Fixed leak of document objects in SciTE.
	</li>
	<li>
	Recognize URL characters '?' and '%' for Open Selected command in SciTE.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3429409&group_id=2439">Bug #3429409.</a>
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite300.zip?download">Release 3.0.0</a>
    </h3>
    <ul>
	<li>
	Released 1 November 2011.
	</li>
	<li>
	Carbon platform support removed. OS X applications should switch to Cocoa.
	</li>
	<li>
	On Windows Vista or newer, drawing may be performed with Direct2D and DirectWrite instead of GDI.
	</li>
	<li>
	Cairo is now used for all drawing on GTK+. GDK drawing was removed.
	</li>
	<li>
	Paletted display support removed.
	</li>
	<li>
	Fractional font sizes can be specified.
	</li>
	<li>
	Different weights of text supported on some platforms instead of just normal and bold.
	</li>
	<li>
	Sub-pixel character positioning supported.
	</li>
	<li>
	SciTE loads files in the background without blocking the user interface.
	</li>
	<li>
	SciTE can display diagnostic messages interleaved with the text of files immediately after the
	line referred to by the diagnostic.
	</li>
	<li>
	New API to see if all lines are visible which can be used to optimize processing fold structure notifications.
	</li>
	<li>
	Scrolling optimized by avoiding invalidation of fold margin when redrawing whole window.
	</li>
	<li>
	Optimized SCI_MARKERNEXT.
	</li>
	<li>
	C++ lexer supports Pike hash quoted strings when turned on with lexer.cpp.hashquoted.strings.
	</li>
	<li>
	Fixed incorrect line height with annotations in wrapped mode when there are multiple views.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3388159&group_id=2439">Bug #3388159.</a>
	</li>
	<li>
	Calltips may be displayed above the text as well as below.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3410830&group_id=2439">Bug #3410830.</a>
	</li>
	<li>
	For huge files SciTE only examines the first megabyte for newline discovery.
	</li>
	<li>
	SciTE on GTK+ removes the fileselector.show.hidden property and check box as this was buggy and GTK+ now
	supports an equivalent feature.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3413630&group_id=2439">Bug #3413630.</a>
	</li>
	<li>
	SciTE on GTK+ supports mnemonics in dynamic menus.
	</li>
	<li>
	SciTE on GTK+ displays the user's home directory as '~' in menus to make them shorter.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite229.zip?download">Release 2.29</a>
    </h3>
    <ul>
	<li>
	Released 16 September 2011.
	</li>
	<li>
	To automatically discover the encoding of a file when opening it, SciTE can run a program set with command.discover.properties.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3324341&group_id=2439">Feature #3324341.</a>
	</li>
	<li>
	Cairo always used for drawing on GTK+.
	</li>
	<li>
	The set of properties files imported by SciTE can be controlled with the properties imports.include and imports.exclude.
	The import statement has been extended to allow "import *".
	The properties files for some languages are no longer automatically loaded by default. The properties files affected are
	avenue, baan, escript, lot, metapost, and mmixal.
	</li>
	<li>
	C++ lexer fixed a bug with raw strings being recognized too easily.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3388122&group_id=2439">Bug #3388122.</a>
	</li>
	<li>
	LaTeX lexer improved with more states and fixes to most outstanding bugs.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=1493111&group_id=2439">Bug #1493111.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=1856356&group_id=2439">Bug #1856356.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3081692&group_id=2439">Bug #3081692.</a>
	</li>
	<li>
	Lua lexer updates for Lua 5.2 beta with goto labels and "\z" string escape.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3386330&group_id=2439">Feature #3386330.</a>
	</li>
	<li>
	Perl string styling highlights interpolated variables.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3394258&group_id=2439">Feature #3394258.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3076629&group_id=2439">Bug #3076629.</a>
	</li>
	<li>
	Perl lexer updated for Perl 5.14.0 with 0X and 0B numeric literal prefixes, break keyword and "+" supported in subroutine prototypes.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3388802&group_id=2439">Feature #3388802.</a>
	</li>
	<li>
	Perl bug fixed with CRLF line endings.
	</li>
	<li>
	Markdown lexer fixed to not change state with "_" in middle of word.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3398184&group_id=2439">Bug #3398184.</a>
	</li>
	<li>
	Cocoa restores compatibility with OS X 10.5.
	</li>
	<li>
	Mouse pointer changes over selection to an arrow near start when scrolled horizontally.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3389055&group_id=2439">Bug #3389055.</a>
	</li>
	<li>
	Indicators that finish at the end of the document no longer expand when text is appended.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3378718&group_id=2439">Bug #3378718.</a>
	</li>
	<li>
	SparseState merge fixed to check if other range is empty.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3387053&group_id=2439">Bug #3387053.</a>
	</li>
	<li>
	On Windows, autocompletion lists will scroll instead of document when mouse wheel spun.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3403600&group_id=2439">Feature #3403600.</a>
	</li>
	<li>
	SciTE performs more rapid polling for command completion so will return faster and report more accurate times.
	</li>
	<li>
	SciTE resizes panes proportionally when switched between horizontal and vertical layout.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3376784&group_id=2439">Feature #3376784.</a>
	</li>
	<li>
	SciTE on GTK+ opens multiple files into a single instance more reliably.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3363754&group_id=2439">Bug #3363754.</a>
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite228.zip?download">Release 2.28</a>
    </h3>
    <ul>
	<li>
	Released 1 August 2011.
	</li>
	<li>
	GTK+ Cairo support works back to GTK+ version 2.8. Requires changing Scintilla source code to enable before GTK+ 2.22.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3322351&group_id=2439">Bug #3322351.</a>
	</li>
	<li>
	Translucent images in RGBA format can be used for margin markers and in autocompletion lists.
	</li>
	<li>
	INDIC_DOTBOX added as a translucent dotted rectangular indicator.
	</li>
	<li>
	Asian text input using IME works for GTK+ 3.x and GTK+ 2.x with Cairo.
	</li>
	<li>
	On GTK+, IME works for Ctrl+Shift+U Unicode input in Scintilla. For SciTE, Ctrl+Shift+U is still Make Selection Uppercase.
	</li>
	<li>
	Key bindings for GTK+ on OS X made compatible with Cocoa port and platform conventions.
	</li>
	<li>
	Cocoa port supports different character encodings, improves scrolling performance and drag image appearance.
	The control ID is included in WM_COMMAND notifications. Text may be deleted by dragging to the trash.
	ScrollToStart and ScrollToEnd key commands added to simplify implementation of standard OS X Home and End
	behaviour.
	</li>
	<li>
	SciTE on GTK+ uses a paned widget to contain the edit and output panes instead of custom code.
	This allows the divider to be moved easily on GTK+ 3 and its appearance follows GTK+ conventions more closely.
	</li>
	<li>
	SciTE builds and installs on BSD.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3324644&group_id=2439">Bug #3324644.</a>
	</li>
	<li>
	Cobol supports fixed format comments.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3014850&group_id=2439">Bug #3014850.</a>
	</li>
	<li>
	Mako template language block syntax extended and ## comments recognized.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3325178&group_id=2439">Feature #3325178.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3318818&group_id=2439">Bug #3318818.</a>
	</li>
	<li>
	Folding of Mako template language within HTML fixed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3324563&group_id=2439">Bug #3324563.</a>
	</li>
	<li>
	Python lexer has lexer.python.keywords2.no.sub.identifiers option to avoid highlighting second set of
	keywords following '.'.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3325333&group_id=2439">Bug #3325333.</a>
	</li>
	<li>
	Python folder fixes bug where fold would not extend to final line.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3349157&group_id=2439">Bug #3349157.</a>
	</li>
	<li>
	SciTE treats LPEG lexers the same as script lexers by setting all 8 style bits.
	</li>
	<li>
	For Cocoa, crashes with unsupported font variants and memory leaks for colour objects fixed.
	</li>
	<li>
	Shift-JIS lead byte ranges modified to match Windows.
	</li>
	<li>
	Mouse pointer changes over selection to an arrow more consistently.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3315756&group_id=2439">Bug #3315756.</a>
	</li>
	<li>
	Bug fixed with annotations beyond end of document.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3347268&group_id=2439">Bug #3347268.</a>
	</li>
	<li>
	Incorrect drawing fixed for combination of background colour change and translucent selection.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3377116&group_id=2439">Bug #3377116.</a>
	</li>
	<li>
	Lexers initialized correctly when started at position other than start of line.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3377148&group_id=2439">Bug #3377148.</a>
	</li>
	<li>
	Fold highlight drawing fixed for some situations.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3323015&group_id=2439">Bug #3323015.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3323805&group_id=2439">Bug #3323805.</a>
	</li>
	<li>
	Case insensitive search fixed for cases where folded character uses fewer bytes than base character.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3362038&group_id=2439">Bug #3362038.</a>
	</li>
	<li>
	SciTE bookmark.alpha setting fixed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3373907&group_id=2439">Bug #3373907.</a>
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite227.zip?download">Release 2.27</a>
    </h3>
    <ul>
	<li>
	Released 20 June 2011.
	</li>
	<li>
	On recent GTK+ 2.x versions when using Cairo, bug fixed where wrong colours were drawn.
	</li>
	<li>
	SciTE on GTK+ slow performance in menu maintenance fixed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3315233&group_id=2439">Bug #3315233.</a>
	</li>
	<li>
	Cocoa platform supports 64-bit builds and uses only non-deprecated APIs.
	Asian Input Method Editors are supported.
	Autocompletion lists and calltips implemented.
	Control identifier used in notifications.
	</li>
	<li>
	On Cocoa, rectangular selection now uses Option/Alt key to be compatible with Apple Human
	Interface Guidelines and other applications.
	The Control key is reported with an SCMOD_META modifier bit.
	</li>
	<li>
	API added for setting and retrieving the identifier number used in notifications.
	</li>
	<li>
	SCI_SETEMPTYSELECTION added to set selection without scrolling or redrawing more than needed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3314877&group_id=2439">Feature #3314877.</a>
	</li>
	<li>
	Added new indicators. INDIC_DASH and INDIC_DOTS are variants of underlines.
	INDIC_SQUIGGLELOW indicator added as shorter alternative to INDIC_SQUIGGLE for small fonts.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3314591&group_id=2439">Bug #3314591</a>
	</li>
	<li>
	Margin line selection can be changed to select display lines instead of document lines.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3312763&group_id=2439">Bug #3312763.</a>
	</li>
	<li>
	On Windows, SciTE can perform reverse searches by pressing Shift+Enter
	in the Find or Replace strips or dialogs.
	</li>
	<li>
	Matlab lexer does not special case '\' in single quoted strings.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=948757&group_id=2439">Bug #948757</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=1755950&group_id=2439">Bug #1755950</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=1888738&group_id=2439">Bug #1888738</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3316852&group_id=2439">Bug #3316852.</a>
	</li>
	<li>
	Verilog lexer supports SystemVerilog folding and keywords.
	</li>
	<li>
	Font leak fixed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3306156&group_id=2439">Bug #3306156.</a>
	</li>
	<li>
	Automatic scrolling works for long wrapped lines.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3312763&group_id=2439">Bug #3312763.</a>
	</li>
	<li>
	Multiple typing works for cases where selections collapse together.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3309906&group_id=2439">Bug #3309906.</a>
	</li>
	<li>
	Fold expanded when needed in word wrap mode.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3291579&group_id=2439">Bug #3291579.</a>
	</li>
	<li>
	Bug fixed with edge drawn in wrong place on wrapped lines.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3314807&group_id=2439">Bug #3314807.</a>
	</li>
	<li>
	Bug fixed with unnecessary scrolling for SCI_GOTOLINE.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3303406&group_id=2439">Bug #3303406.</a>
	</li>
	<li>
	Bug fixed where extra step needed to undo SCI_CLEAR in virtual space.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3159691&group_id=2439">Bug #3159691.</a>
	</li>
	<li>
	Regular expression search fixed for \$ on last line of search range.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3313746&group_id=2439">Bug #3313746.</a>
	</li>
	<li>
	SciTE performance improved when switching to a tab with a very large file.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3311421&group_id=2439">Bug #3311421.</a>
	</li>
	<li>
	On Windows, SciTE advanced search remembers the "Search only in this style" setting.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3313344&group_id=2439">Bug #3313344.</a>
	</li>
	<li>
	On GTK+, SciTE opens help using "xdg-open" instead of "netscape" as "netscape" no longer commonly installed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3314377&group_id=2439">Bug #3314377.</a>
	</li>
	<li>
	SciTE script lexers can use 256 styles.
	</li>
	<li>
	SciTE word highlight works for words containing DBCS characters.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3315173&group_id=2439">Bug #3315173.</a>
	</li>
	<li>
	Compilation fixed for wxWidgets.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3306156&group_id=2439">Bug #3306156.</a>
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite226.zip?download">Release 2.26</a>
    </h3>
    <ul>
	<li>
	Released 25 May 2011.
	</li>
	<li>
	Folding margin symbols can be highlighted for the current folding block.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3147069&group_id=2439">Feature #3147069.</a>
	</li>
	<li>
	Selected lines can be moved up or down together.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3304850&group_id=2439">Feature #3304850.</a>
	</li>
	<li>
	SciTE can highlight all occurrences of the current word or selected text.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3291636&group_id=2439">Feature #3291636.</a>
	</li>
	<li>
	Experimental GTK+ 3.0 support: build with "make GTK3=1".
	</li>
	<li>
	INDIC_STRAIGHTBOX added. Is similar to INDIC_ROUNDBOX but without rounded corners.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3290435&group_id=2439">Bug #3290435.</a>
	</li>
	<li>
	Can show brace matching and mismatching with indicators instead of text style.
	Translucency of outline can be altered for INDIC_ROUNDBOX and INDIC_STRAIGHTBOX.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3290434&group_id=2439">Feature #3290434.</a>
	</li>
	<li>
	SciTE can automatically indent python by examining previous line for scope-starting ':' with indent.python.colon.
	</li>
	<li>
	Batch file lexer allows braces '(' or ')' inside variable names.
	</li>
	<li>
	The cpp lexer only recognizes Vala triple quoted strings when lexer.cpp.triplequoted.strings property is set.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3239234&group_id=2439">Bug #3239234.</a>
	</li>
	<li>
	Make file lexer treats a variable with a nested variable like $(f$(qx)b) as one variable.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3298223&group_id=2439">Bug #3298223.</a>
	</li>
	<li>
	Folding bug fixed for JavaScript with nested PHP.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3193530&group_id=2439">Bug #3193530.</a>
	</li>
	<li>
	HTML lexer styles Django's {# #} comments.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3013798&group_id=2439">Bug #3013798.</a>
	</li>
	<li>
	HTML lexer styles JavaScript regular expression correctly for /abc/i.test('abc');.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3209108&group_id=2439">Bug #3209108.</a>
	</li>
	<li>
	Inno Setup Script lexer now works properly when it restarts from middle of [CODE] section.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3283880&group_id=2439">Bug #3283880.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3129044&group_id=2439">Bug #3129044.</a>
	</li>
	<li>
	Lua lexer updated for Lua 5.2 with hexadecimal floating-point numbers and '\*' whitespace escaping in strings.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3243811&group_id=2439">Feature #3243811.</a>
	</li>
	<li>
	Perl folding folds "here doc"s and adds options fold.perl.at.else and fold.perl.comment.explicit. Fold structure for Perl fixed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3112671&group_id=2439">Feature #3112671.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3265401&group_id=2439">Bug #3265401.</a>
	</li>
	<li>
	Python lexer supports cpdef keyword for Cython.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3279728&group_id=2439">Bug #3279728.</a>
	</li>
	<li>
	SQL folding option lexer.sql.fold.at.else renamed to fold.sql.at.else.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3271474&group_id=2439">Bug #3271474.</a>
	</li>
	<li>
	SQL lexer no longer treats ';' as terminating a comment.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3196071&group_id=2439">Bug #3196071.</a>
	</li>
	<li>
	Text drawing and measurement segmented into smaller runs to avoid platform bugs.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3277449&group_id=2439">Bug #3277449.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3165743&group_id=2439">Bug #3165743.</a>
	</li>
	<li>
	SciTE on Windows adds temp.files.sync.load property to open dropped temporary files synchronously as they may
	be removed before they can be opened asynchronously.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3072009&group_id=2439">Bug #3072009.</a>
	</li>
	<li>
	Bug fixed with indentation guides ignoring first line in SC_IV_LOOKBOTH mode.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3291317&group_id=2439">Bug #3291317.</a>
	</li>
	<li>
	Bugs fixed in backward regex search.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3292659&group_id=2439">Bug #3292659.</a>
	</li>
	<li>
	Bugs with display of folding structure fixed for wrapped lines and where there is a fold header but no body.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3291579&group_id=2439">Bug #3291579.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3265401&group_id=2439">Bug #3265401.</a>
	</li>
	<li>
	SciTE on Windows cursor changes to an arrow now when over horizontal splitter near top of window.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3286620&group_id=2439">Bug #3286620.</a>
	</li>
	<li>
	Fixed default widget size problem on GTK+.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3267892&group_id=2439">Bug #3267892.</a>
	</li>
	<li>
	Fixed font size when using Cairo on GTK+.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3272662&group_id=2439">Bug #3272662.</a>
	</li>
	<li>
	Fixed primary selection and cursor issues on GTK+ when unrealized then realized.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3256153&group_id=2439">Bug #3256153.</a>
	</li>
	<li>
	Right click now cancels selection on GTK+ like on Windows.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3235190&group_id=2439">Bug #3235190.</a>
	</li>
	<li>
	SciTE on GTK+ implements z-order buffer switching like on Windows.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3228384&group_id=2439">Bug #3228384.</a>
	</li>
	<li>
	Improve selection position after SciTE Insert Abbreviation command when abbreviation expansion includes '|'.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite225.zip?download">Release 2.25</a>
    </h3>
    <ul>
	<li>
	Released 21 March 2011.
	</li>
	<li>
	SparseState class makes it easier to write lexers which have to remember complex state between lines.
	</li>
	<li>
	Visual Studio project (.dsp) files removed. The make files should be used instead as described in the README.
	</li>
	<li>
	Modula 3 lexer added along with SciTE support.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3173374&group_id=2439">Feature #3173374.</a>
	</li>
	<li>
	Asm, Basic, and D lexers add extra folding properties.
	</li>
	<li>
	Raw string literals for C++0x supported in C++ lexer.
	</li>
	<li>
	Triple-quoted strings used in Vala language supported in C++ lexer.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3177601&group_id=2439">Feature #3177601.</a>
	</li>
	<li>
 	The errorlist lexer used in SciTE's output pane colours lines that start with '&lt;' as diff deletions.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3172878&group_id=2439">Feature #3172878.</a>
	</li>
	<li>
 	The Fortran lexer correctly folds type-bound procedures from Fortran 2003.
	</li>
	<li>
	LPeg lexer support‎ improved in SciTE.
	</li>
	<li>
	SciTE on Windows-64 fixes for menu localization and Lua scripts.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3204502&group_id=2439">Bug #3204502.</a>
	</li>
	<li>
	SciTE on Windows avoids locking folders when using the open or save dialogs.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=1795484&group_id=2439">Bug #1795484.</a>
	</li>
	<li>
	Diff lexer fixes problem where diffs of diffs producing lines that start with "----".
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3197952&group_id=2439">Bug #3197952.</a>
	</li>
	<li>
	Bug fixed when searching upwards in Chinese code page 936.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3176271&group_id=2439">Bug #3176271.</a>
	</li>
	<li>
	On Cocoa, translucent drawing performed as on other platforms instead of 2.5 times less translucent.
	</li>
	<li>
	Performance issue and potential bug fixed on GTK+ with caret line for long lines.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite224.zip?download">Release 2.24</a>
    </h3>
    <ul>
	<li>
	Released 3 February 2011.
	</li>
	<li>
	Fixed memory leak in GTK+ Cairo code.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3157655&group_id=2439">Feature #3157655.</a>
	</li>
	<li>
	Insert Abbreviation dialog added to SciTE on GTK+.
	</li>
	<li>
	SCN_UPDATEUI notifications received when window scrolled. An 'updated' bit mask indicates which
	types of update have occurred from SC_UPDATE_SELECTION, SC_UPDATE_CONTENT, SC_UPDATE_H_SCROLL
	or SC_UPDATE_V_SCROLL.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3125977&group_id=2439">Feature #3125977.</a>
	</li>
	<li>
	On Windows, to ensure reverse arrow cursor matches platform default, it is now generated by
	reflecting the platform arrow cursor.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3143968&group_id=2439">Feature #3143968.</a>
	</li>
	<li>
	Can choose mouse cursor used in margins.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3161326&group_id=2439">Feature #3161326.</a>
	</li>
	<li>
	On GTK+, SciTE sets a mime type of text/plain in its .desktop file so that it will appear in the shell context menu.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3137126&group_id=2439">Feature #3137126.</a>
	</li>
	<li>
	Bash folder handles here docs.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3118223&group_id=2439">Feature #3118223.</a>
	</li>
	<li>
	C++ folder adds fold.cpp.syntax.based, fold.cpp.comment.multiline, fold.cpp.explicit.start, fold.cpp.explicit.end,
	and fold.cpp.explicit.anywhere properties to allow more control over folding and choice of explicit fold markers.
	</li>
	<li>
	C++ lexer fixed to always handle single quote strings continued past a line end.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3150522&group_id=2439">Bug #3150522.</a>
	</li>
	<li>
	Ruby folder handles here docs.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3118224&group_id=2439">Feature #3118224.</a>
	</li>
	<li>
	SQL lexer allows '.' to be part of words.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3103129&group_id=2439">Feature #3103129.</a>
	</li>
	<li>
	SQL folder handles case statements in more situations.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3135027&group_id=2439">Feature #3135027.</a>
	</li>
	<li>
	SQL folder adds fold points inside expressions based on bracket structure.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3165488&group_id=2439">Feature #3165488.</a>
	</li>
	<li>
	SQL folder drops fold.sql.exists property as 'exists' is handled automatically.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3164194&group_id=2439">Bug #3164194.</a>
	</li>
	<li>
	SciTE only forwards properties to lexers when they have been explicitly set so the defaults set by lexers are used
	rather than 0.
	</li>
	<li>
	Mouse double click word selection chooses the word around the character under the mouse rather than
	the inter-character position under the mouse. This makes double clicking select what the user is pointing
	at and avoids selecting adjacent non-word characters.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3111174&group_id=2439">Bug #3111174.</a>
	</li>
	<li>
	Fixed mouse double click to always perform word select, not line select.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3143635&group_id=2439">Bug #3143635.</a>
	</li>
	<li>
	Right click cancels autocompletion.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3144531&group_id=2439">Bug #3144531.</a>
	</li>
	<li>
	Fixed multiPaste to work when additionalSelectionTyping off.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3126221&group_id=2439">Bug #3126221.</a>
	</li>
	<li>
	Fixed virtual space problems when text modified at caret.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3154986&group_id=2439">Bug #3154986.</a>
	</li>
	<li>
	Fixed memory leak in lexer object code.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3133672&group_id=2439">Bug #3133672.</a>
	</li>
	<li>
	Fixed SciTE on GTK+ search failure when using regular expression.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3156217&group_id=2439">Bug #3156217.</a>
	</li>
	<li>
	Avoid unnecessary full window redraw for SCI_GOTOPOS.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3146650&group_id=2439">Feature #3146650.</a>
	</li>
	<li>
	Avoid unnecessary redraw when indicator fill range makes no real change.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite223.zip?download">Release 2.23</a>
    </h3>
    <ul>
	<li>
	Released 7 December 2010.
	</li>
	<li>
	On GTK+ version 2.22 and later, drawing is performed with Cairo rather than GDK.
	This is in preparation for GTK+ 3.0 which will no longer support GDK drawing.
	The appearance of some elements will be different with Cairo as it is anti-aliased and uses sub-pixel positioning.
	Cairo may be turned on for GTK+ versions before 2.22 by defining USE_CAIRO although this has not
	been extensively tested.
	</li>
	<li>
	New lexer a68k for Motorola 68000 assembler.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3101598&group_id=2439">Feature #3101598.</a>
	</li>
	<li>
	Borland C++ is no longer supported for building Scintilla or SciTE on Windows.
	</li>
	<li>
	Performance improved when creating large rectangular selections.
	</li>
	<li>
	PHP folder recognizes #region and #endregion comments.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3101624&group_id=2439">Feature #3101624.</a>
	</li>
	<li>
	SQL lexer has a lexer.sql.numbersign.comment option to turn off use of '#' comments
	as these are a non-standard feature only available in some implementations.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3098071&group_id=2439">Feature #3098071.</a>
	</li>
	<li>
	SQL folder recognizes case statements and understands the fold.at.else property.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3104091&group_id=2439">Bug #3104091.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3107362&group_id=2439">Bug #3107362.</a>
	</li>
	<li>
	SQL folder fixes bugs with end statements when fold.sql.only.begin=1.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3104091&group_id=2439">Bug #3104091.</a>
	</li>
	<li>
	SciTE on Windows bug fixed with multi-line tab bar not adjusting correctly when maximizing and demaximizing.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3097517&group_id=2439">Bug #3097517.</a>
	</li>
	<li>
	Crash fixed on GTK+ when Scintilla widget destroyed while it still has an outstanding style idle pending.
	</li>
	<li>
	Bug fixed where searching backwards in DBCS text (code page 936 or similar) failed to find occurrences at the start of the line.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3103936&group_id=2439">Bug #3103936.</a>
	</li>
	<li>
	SciTE on Windows supports Unicode file names when executing help applications with winhelp and htmlhelp subsystems.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite222.zip?download">Release 2.22</a>
    </h3>
    <ul>
	<li>
	Released 27 October 2010.
	</li>
	<li>
	SciTE includes support for integrating with Scintillua which allows lexers to be implemented in Lua as a
	Parsing Expression Grammar (PEG).
	</li>
	<li>
	Regular expressions allow use of '?' for non-greedy matches or to match 0 or 1 instances of an item.
	</li>
	<li>
	SCI_CONTRACTEDFOLDNEXT added to allow rapid retrieval of folding state.
	</li>
	<li>
	SCN_HOTSPOTRELEASECLICK notification added which is similar to SCN_HOTSPOTCLICK but occurs
	when the mouse is released.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3082409&group_id=2439">Feature #3082409.</a>
	</li>
	<li>
	Command added for centring current line in window.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3064696&group_id=2439">Feature #3064696.</a>
	</li>
	<li>
	SciTE performance improved by not examining document for line ends when switching buffers and not
	storing folds when folding turned off.
	</li>
	<li>
	Bug fixed where scrolling to ensure the caret is visible did not take into account all pixels of the line.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3081721&group_id=2439">Bug #3081721.</a>
	</li>
	<li>
	Bug fixed for autocompletion list overlapping text when WS_EX_CLIENTEDGE used.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3079778&group_id=2439">Bug #3079778.</a>
	</li>
	<li>
	After autocompletion, the caret's X is updated.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3079114&group_id=2439">Bug #3079114.</a>
	</li>
	<li>
	On Windows, default to the system caret blink time.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3079784&group_id=2439">Feature #3079784.</a>
	</li>
	<li>
	PgUp/PgDn fixed to allow virtual space.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3077452&group_id=2439">Bug #3077452.</a>
	</li>
	<li>
	Crash fixed when AddMark and AddMarkSet called with negative argument.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3075074&group_id=2439">Bug #3075074.</a>
	</li>
	<li>
	Dwell notifications fixed so that they do not occur when the mouse is outside Scintilla.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3073481&group_id=2439">Bug #3073481.</a>
	</li>
	<li>
	Bash lexer bug fixed for here docs starting with &lt;&lt;-.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3063822&group_id=2439">Bug #3063822.</a>
	</li>
	<li>
	C++ lexer bug fixed for // comments that are continued onto a second line by a \.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3066031&group_id=2439">Bug #3066031.</a>
	</li>
	<li>
	C++ lexer fixes wrong highlighting for float literals containing +/-.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3058924&group_id=2439">Bug #3058924.</a>
	</li>
	<li>
	JavaScript lexer recognize regexes following return keyword.‎
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3062287&group_id=2439">Bug #3062287.</a>
	</li>
	<li>
	Ruby lexer handles % quoting better and treats range dots as operators in 1..2 and 1...2.
	Ruby folder handles "if" keyword used as a modifier even when it is separated from the modified statement by an escaped new line.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2093767&group_id=2439">Bug #2093767.</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3058496&group_id=2439">Bug #3058496.</a>
	</li>
	<li>
	Bug fixed where upwards search failed with DBCS code pages.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3065912&group_id=2439">Bug #3065912.</a>
	</li>
	<li>
	SciTE has a default Lua startup script name distributed in SciTEGlobal.properties.
	No error message is displayed if this file does not exist.
	</li>
	<li>
	SciTE on Windows tab control height is calculated better.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2635702&group_id=2439">Bug #2635702.</a>
	</li>
	<li>
	SciTE on Windows uses better themed check buttons in find and replace strips.
	</li>
	<li>
	SciTE on Windows fixes bug with Find strip appearing along with Incremental Find strip.
	</li>
	<li>
	SciTE setting find.close.on.find added to allow preventing the Find dialog from closing.
	</li>
	<li>
	SciTE on Windows attempts to rerun commands that fail by prepending them with "cmd.exe /c".
	This allows commands built in to the command processor like "dir" to run.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite221.zip?download">Release 2.21</a>
    </h3>
    <ul>
	<li>
	Released 1 September 2010.
	</li>
	<li>
	Asian Double Byte Character Set (DBCS) support improved.
	Case insensitive search works and other operations are much faster.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2999125&group_id=2439">Bug #2999125,</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2774616&group_id=2439">Bug #2774616,</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2991942&group_id=2439">Bug #2991942,</a>
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3005688&group_id=2439">Bug #3005688.</a>
	</li>
	<li>
	Scintilla on GTK+ uses only non-deprecated APIs (for GTK+ 2.20) except for GdkFont and GdkFont use can be disabled
	with the preprocessor symbol DISABLE_GDK_FONT.
	</li>
	<li>
	IDocument interface used by lexers adds BufferPointer and GetLineIndentation methods.
	</li>
	<li>
	On Windows, clicking sets focus before processing the click or sending notifications.
	</li>
	<li>
	Bug on OS X (macosx platform) fixed where drag/drop overwrote clipboard.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3039732&group_id=2439">Bug #3039732.</a>
	</li>
	<li>
	GTK+ drawing bug when the view was horizontally scrolled more than 32000 pixels fixed.
	</li>
	<li>
	SciTE bug fixed with invoking Complete Symbol from output pane.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3050957&group_id=2439">Bug #3050957.</a>
	</li>
	<li>
	Bug fixed where it was not possible to disable folding.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3040649&group_id=2439">Bug #3040649.</a>
	</li>
	<li>
	Bug fixed with pressing Enter on a folded fold header line not opening the fold.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3043419&group_id=2439">Bug #3043419.</a>
	</li>
	<li>
	SciTE 'Match case' option in find and replace user interfaces changed to 'Case sensitive' to allow use of 'v'
	rather than 'c' as the mnemonic.
	</li>
	<li>
	SciTE displays stack trace for Lua when error occurs..
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3051397&group_id=2439">Bug #3051397.</a>
	</li>
	<li>
	SciTE on Windows fixes bug where double clicking on error message left focus in output pane.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=1264835&group_id=2439">Bug #1264835.</a>
	</li>
	<li>
	SciTE on Windows uses SetDllDirectory to avoid a security problem.
	</li>
	<li>
	C++ lexer crash fixed with preprocessor expression that looked like division by 0.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3056825&group_id=2439">Bug #3056825.</a>
	</li>
	<li>
	Haskell lexer improved.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3039490&group_id=2439">Feature #3039490.</a>
	</li>
	<li>
	HTML lexing fixed around Django {% %} tags.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3034853&group_id=2439">Bug #3034853.</a>
	</li>
	<li>
	HTML JavaScript lexing fixed when line end escaped.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3038381&group_id=2439">Bug #3038381.</a>
	</li>
	<li>
	HTML lexer stores line state produced by a line on that line rather than on the next line.
	</li>
	<li>
	Markdown lexer fixes infinite loop.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3045386&group_id=2439">Bug #3045386.</a>
	</li>
	<li>
	MySQL folding bugs with END statements fixed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3031742&group_id=2439">Bug #3031742.</a>
	</li>
	<li>
	PowerShell lexer allows '_' as a word character.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3042228&group_id=2439">Feature #3042228.</a>
	</li>
	<li>
	SciTE on GTK+ abandons processing of subsequent commands if a command.go.needs command fails.
	</li>
	<li>
	When SciTE is closed, all buffers now receive an OnClose call.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3033857&group_id=2439">Bug #3033857.</a>
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite220.zip?download">Release 2.20</a>
    </h3>
    <ul>
	<li>
	Released 30 July 2010.
	</li>
	<li>
	Lexers are implemented as objects so that they may retain extra state.
	The interfaces defined for this are tentative and may change before the next release.
	Compatibility classes allow current lexers compiled into Scintilla to run with few changes.
	The interface to external lexers has changed and existing external lexers will need to have changes
	made and be recompiled.
	A single lexer object is attached to a document whereas previously lexers were attached to views
	which could lead to different lexers being used for split views with confusing results.
	</li>
	<li>
	C++ lexer understands the preprocessor enough to grey-out inactive code due to conditional compilation.
	</li>
	<li>
	SciTE can use strips within the main window for find and replace rather than dialogs.
	On Windows SciTE always uses a strip for incremental search.
	</li>
	<li>
	Lexer added for Txt2Tags language.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3018736&group_id=2439">Feature #3018736.</a>
	</li>
	<li>
	Sticky caret feature enhanced with additional SC_CARETSTICKY_WHITESPACE mode .
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3027559&group_id=2439">Feature #3027559.</a>
	</li>
	<li>
	Bash lexer implements basic parsing of compound commands and constructs.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3033135&group_id=2439">Feature #3033135.</a>
	</li>
	<li>
	C++ folder allows disabling explicit fold comments.
	</li>
	<li>
	Perl folder works for array blocks, adjacent package statements, nested PODs, and terminates package folding at __DATA__, ^D and ^Z.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3030887&group_id=2439">Feature #3030887.</a>
	</li>
	<li>
	PowerShell lexer supports multiline &lt;# .. #&gt; comments and adds 2 keyword classes.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3015176&group_id=2439">Feature #3015176.</a>
	</li>
	<li>
	Lexing performed incrementally when needed by wrapping to make user interface more responsive.
	</li>
	<li>
	SciTE setting replaceselection:yes works on GTK+.
	</li>
	<li>
	SciTE Lua scripts calling io.open or io.popen on Windows have arguments treated as UTF-8 and converted to Unicode
	so that non-ASCII file paths will work. Lua files with non-ASCII paths run.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3016951&group_id=2439">Bug #3016951.</a>
	</li>
	<li>
	Crash fixed when searching for empty string.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3017572&group_id=2439">Bug #3017572.</a>
	</li>
	<li>
	Bugs fixed with folding and lexing when Enter pressed at start of line.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3032652&group_id=2439">Bug #3032652.</a>
	</li>
	<li>
	Bug fixed with line selection mode not affecting selection range.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3021480&group_id=2439">Bug #3021480.</a>
	</li>
	<li>
	Bug fixed where indicator alpha was limited to 100 rather than 255.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3021473&group_id=2439">Bug #3021473.</a>
	</li>
	<li>
	Bug fixed where changing annotation did not cause automatic redraw.
	</li>
	<li>
	Regular expression bug fixed when a character range included non-ASCII characters.
	</li>
	<li>
	Compilation failure with recent compilers fixed on GTK+.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3022027&group_id=2439">Bug #3022027.</a>
	</li>
	<li>
	Bug fixed on Windows with multiple monitors where autocomplete pop up would appear off-screen
	or straddling monitors.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3017512&group_id=2439">Bug #3017512.</a>
	</li>
	<li>
	SciTE on Windows bug fixed where changing directory to a Unicode path failed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3011987&group_id=2439">Bug #3011987.</a>
	</li>
	<li>
	SciTE on Windows bug fixed where combo boxes were not allowing Unicode characters.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3012986&group_id=2439">Bug #3012986.</a>
	</li>
	<li>
	SciTE on GTK+ bug fixed when dragging files into SciTE on KDE.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3026555&group_id=2439">Bug #3026555.</a>
	</li>
	<li>
	SciTE bug fixed where closing untitled file could lose data if attempt to name file same as another buffer.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3011680&group_id=2439">Bug #3011680.</a>
	</li>
	<li>
	COBOL number masks now correctly highlighted.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3012164&group_id=2439">Bug #3012164.</a>
	</li>
	<li>
	PHP comments can include &lt;?PHP without triggering state change.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2854183&group_id=2439">Bug #2854183.</a>
	</li>
	<li>
	VHDL lexer styles unclosed string correctly.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3029627&group_id=2439">Bug #3029627.</a>
	</li>
	<li>
	Memory leak fixed in list boxes on GTK+.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3007669&group_id=2439">Bug #3007669.</a>
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite212.zip?download">Release 2.12</a>
    </h3>
    <ul>
	<li>
	Released 1 June 2010.
	</li>
	<li>
	Drawing optimizations improve speed and fix some visible flashing when scrolling.
	</li>
	<li>
	Copy Path command added to File menu in SciTE.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2986745&group_id=2439">Feature #2986745.</a>
	</li>
	<li>
	Optional warning displayed by SciTE when saving a file which has been modified by another process.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2975041&group_id=2439">Feature #2975041.</a>
	</li>
	<li>
	Flagship lexer for xBase languages updated to follow the language much more closely.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2992689&group_id=2439">Feature #2992689.</a>
	</li>
	<li>
	HTML lexer highlights Django templates in more regions.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=3002874&group_id=2439">Feature #3002874.</a>
	</li>
	<li>
	Dropping files on SciTE on Windows, releases the drag object earlier and opens the files asynchronously,
	leading to smoother user experience.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2986724&group_id=2439">Feature #2986724.</a>
	</li>
	<li>
	SciTE HTML exports take the Use Monospaced Font setting into account.
	</li>
	<li>
	SciTE window title "[n of m]" localized.
	</li>
	<li>
	When new line inserted at start of line, markers are moved down.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2986727&group_id=2439">Bug #2986727.</a>
	</li>
	<li>
	On Windows, dropped text has its line ends converted, similar to pasting.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3005328&group_id=2439">Bug #3005328.</a>
	</li>
	<li>
	Fixed bug with middle-click paste in block select mode where text was pasted next to selection rather than at cursor.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2984460&group_id=2439">Bug #2984460.</a>
	</li>
	<li>
	Fixed SciTE crash where a style had a size parameter without a value.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3003834&group_id=2439">Bug #3003834.</a>
	</li>
	<li>
	Debug assertions in multiple lexers fixed.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3000566&group_id=2439">Bug #3000566.</a>
	</li>
	<li>
	CSS lexer fixed bug where @font-face displayed incorrectly
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2994224&group_id=2439">Bug #2994224.</a>
	</li>
	<li>
	CSS lexer fixed bug where open comment caused highlighting error.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=1683672&group_id=2439">Bug #1683672.</a>
	</li>
	<li>
	Shell file lexer fixed highlight glitch with here docs where the first line is a comment.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2830239&group_id=2439">Bug #2830239.</a>
	</li>
	<li>
	Bug fixed in SciTE openpath property that caused Open Selected File to fail to open the selected file.
	</li>
	<li>
	Bug fixed in SciTE FileExt property when file name with no extension evaluated to whole path.
	</li>
	<li>
	Fixed SciTE on Windows printing bug where the $(CurrentTime), $(CurrentPage) variables were not expanded.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2994612&group_id=2439">Bug #2994612.</a>
	</li>
	<li>
	SciTE compiles for 64-bit Windows and runs without crashing.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2986312&group_id=2439">Bug #2986312.</a>
	</li>
	<li>
	Full Screen mode in Windows Vista/7 improved to hide Start button and size borders a little better.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=3002813&group_id=2439">Bug #3002813.</a>
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite211.zip?download">Release 2.11</a>
    </h3>
    <ul>
	<li>
	Released 9 April 2010.
	</li>
	<li>
	Fixes compatibility of Scintilla.h with the C language.
	</li>
	<li>
	With a rectangular selection SCI_GETSELECTIONSTART and SCI_GETSELECTIONEND return limits of the
	rectangular selection rather than the limits of the main selection.
	</li>
	<li>
	When SciTE on Windows is minimized to tray, only takes a single click to restore rather than a double click.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=981917&group_id=2439">Feature #981917.</a>
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite210.zip?download">Release 2.10</a>
    </h3>
    <ul>
	<li>
	Released 4 April 2010.
	</li>
	<li>
	Version 1.x of GTK+ is no longer supported.
	</li>
	<li>
	SciTE is no longer supported on Windows 95, 98 or ME.
	</li>
	<li>
	Case-insensitive search works for non-ASCII characters in UTF-8 and 8-bit encodings.
	Non-regex search in DBCS encodings is always case-sensitive.
	</li>
	<li>
	Non-ASCII characters may be changed to upper and lower case.
	</li>
	<li>
	SciTE on Windows can access all files including those with names outside the user's preferred character encoding.
	</li>
	<li>
	SciTE may be extended with lexers written in Lua.
	</li>
	<li>
	When there are multiple selections, the paste command can go either to the main selection or to each
	selection. This is controlled with SCI_SETMULTIPASTE.
	</li>
	<li>
	More forms of bad UTF-8 are detected including overlong sequences, surrogates, and characters outside
	the valid range. Bad UTF-8 bytes are now displayed as 2 hex digits preceded by 'x'.
	</li>
	<li>
	SCI_GETTAG retrieves the value of captured expressions within regular expression searches.
	</li>
	<li>
	Django template highlighting added to the HTML lexer.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2974889&group_id=2439">Feature #2974889.</a>
	</li>
	<li>
	Verilog line comments can be folded.
	</li>
	<li>
	SciTE on Windows allows specifying a filter for the Save As dialog.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2943445&group_id=2439">Feature #2943445.</a>
	</li>
	<li>
	Bug fixed when multiple selection disabled where rectangular selections could be expanded into multiple selections.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2948260&group_id=2439">Bug #2948260.</a>
	</li>
	<li>
	Bug fixed when document horizontally scrolled and up/down-arrow did not return to the same
	column after horizontal scroll occurred.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2950799&group_id=2439">Bug #2950799.</a>
	</li>
	<li>
	Bug fixed to remove hotspot highlight when mouse is moved out of the document. Windows only fix.
	<a href="https://sourceforge.net/tracker/?func=detail&aid=2951353&group_id=2439&atid=102439">Bug #2951353.</a>
	</li>
	<li>
	R lexer now performs case-sensitive check for keywords.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2956543&group_id=2439">Bug #2956543.</a>
	</li>
	<li>
	Bug fixed on GTK+ where text disappeared when a wrap occurred.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2958043&group_id=2439">Bug #2958043.</a>
	</li>
	<li>
	Bug fixed where regular expression replace cannot escape the '\' character by using '\\'.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2959876&group_id=2439">Bug #2959876.</a>
	</li>
	<li>
	Bug fixed on GTK+ when virtual space disabled, middle-click could still paste text beyond end of line.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2971618&group_id=2439">Bug #2971618.</a>
	</li>
	<li>
	SciTE crash fixed when double clicking on a malformed error message in the output pane.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2976551&group_id=2439">Bug #2976551.</a>
	</li>
	<li>
	Improved performance on GTK+ when changing parameters associated with scroll bars to the same value.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2964357&group_id=2439">Bug #2964357.</a>
	</li>
	<li>
	Fixed bug with pressing Shift+Tab with a rectangular selection so that it performs an un-indent
	similar to how Tab performs an indent.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite203.zip?download">Release 2.03</a>
    </h3>
    <ul>
	<li>
	Released 14 February 2010.
	</li>
	<li>
	Added SCI_SETFIRSTVISIBLELINE to match SCI_GETFIRSTVISIBLELINE.
	</li>
	<li>
	Erlang lexer extended set of numeric bases recognized; separate style for module:function_name; detects
	built-in functions, known module attributes, and known preprocessor instructions; recognizes EDoc and EDoc macros;
	separates types of comments.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2942448&group_id=2439">Bug #2942448.</a>
	</li>
	<li>
	Python lexer extended with lexer.python.strings.over.newline option that allows non-triple-quoted strings to extend
	past line ends. This allows use of the Ren'Py language.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2945550&group_id=2439">Feature #2945550.</a>
	</li>
	<li>
	Fixed bugs with cursor movement after deleting a rectangular selection.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2942131&group_id=2439">Bug #2942131.</a>
	</li>
	<li>
	Fixed bug where calling SCI_SETSEL when there is a rectangular selection left
	the additional selections selected.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2947064&group_id=2439">Bug #2947064.</a>
	</li>
	<li>
	Fixed macro recording bug where not all bytes in multi-byte character insertions were reported through
	SCI_REPLACESEL.
	</li>
	<li>
	Fixed SciTE bug where using Ctrl+Enter followed by Ctrl+Space produced an autocompletion list
	with only a single line containing all the identifiers.
	</li>
	<li>
	Fixed SciTE on GTK+ bug where running a tool made the user interface completely unresponsive.
	</li>
	<li>
	Fixed SciTE on Windows Copy to RTF bug.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2108574&group_id=2439">Bug #2108574.</a>
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite202.zip?download">Release 2.02</a>
    </h3>
    <ul>
	<li>
	Released on 25 January 2010.
	</li>
	<li>
	Markdown lexer added.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2844081&group_id=2439">Feature #2844081.</a>
	</li>
	<li>
	On GTK+, include code that understands the ranges of lead bytes for code pages 932, 936, and 950
	so that most Chinese and Japanese text can be used on systems that are not set to the corresponding locale.
	</li>
	<li>
	Allow changing the size of dots in visible whitespace using SCI_SETWHITESPACESIZE.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2839427&group_id=2439">Feature #2839427.</a>
	</li>
	<li>
	Additional carets can be hidden with SCI_SETADDITIONALCARETSVISIBLE.
	</li>
	<li>
	Can choose anti-aliased, non-anti-aliased or lcd-optimized text using SCI_SETFONTQUALITY.
	</li>
	<li>
	Retrieve the current selected text in the autocompletion list with SCI_AUTOCGETCURRENTTEXT.
	</li>
	<li>
	Retrieve the name of the current lexer with SCI_GETLEXERLANGUAGE.
	</li>
	<li>
	Progress 4GL lexer improves handling of comments in preprocessor declaration.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2902206&group_id=2439">Feature #2902206.</a>
	</li>
	<li>
	HTML lexer extended to handle Mako template language.
	</li>
	<li>
	SQL folder extended for SQL Anywhere "EXISTS" and "ENDIF" keywords.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2887524&group_id=2439">Feature #2887524.</a>
	</li>
	<li>
	SciTE adds APIPath and AbbrevPath variables.
	</li>
	<li>
	SciTE on GTK+ uses pipes instead of temporary files for running tools. This should be more secure.
	</li>
	<li>
	Fixed crash when calling SCI_STYLEGETFONT for a style which does not have a font set.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2857425&group_id=2439">Bug #2857425.</a>
	</li>
	<li>
	Fixed crash caused by not having sufficient styles allocated after choosing a lexer.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2881279&group_id=2439">Bug #2881279.</a>
	</li>
	<li>
	Fixed crash in SciTE using autocomplete word when word characters includes space.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2840141&group_id=2439">Bug #2840141.</a>
	</li>
	<li>
	Fixed bug with handling upper-case file extensions SciTE on GTK+.
	</li>
	<li>
	Fixed SciTE loading files from sessions with folded folds where it would not
	be scrolled to the correct location.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2882775&group_id=2439">Bug #2882775.</a>
	</li>
	<li>
	Fixed SciTE loading files from sessions when file no longer exists.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2883437&group_id=2439">Bug #2883437.</a>
	</li>
	<li>
	Fixed SciTE export to HTML using the wrong background colour.
	</li>
	<li>
	Fixed crash when adding an annotation and then adding a new line after the annotation.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2929708&group_id=2439">Bug #2929708.</a>
	</li>
	<li>
	Fixed crash in SciTE setting a property to nil from Lua.
	</li>
	<li>
	SCI_GETSELTEXT fixed to return correct length.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2929441&group_id=2439">Bug #2929441.</a>
	</li>
	<li>
	Fixed text positioning problems with selection in some circumstances.
	</li>
	<li>
	Fixed text positioning problems with ligatures on GTK+.
	</li>
	<li>
	Fixed problem pasting into rectangular selection with caret at bottom caused text to go from the caret down
	rather than replacing the selection.
	</li>
	<li>
	Fixed problem replacing in a rectangular selection where only the final line was changed.
	</li>
	<li>
	Fixed inability to select a rectangular area using Alt+Shift+Click at both corners.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2899746&group_id=2439">Bug #2899746.</a>
	</li>
	<li>
	Fixed problem moving to start/end of a rectangular selection with left/right key.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2871358&group_id=2439">Bug #2871358.</a>
	</li>
	<li>
	Fixed problem with Select All when there's a rectangular selection.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2930488&group_id=2439">Bug #2930488.</a>
	</li>
	<li>
	Fixed SCI_LINEDUPLICATE on a rectangular selection to not produce multiple discontinuous selections.
	</li>
	<li>
	Virtual space removed when performing delete word left or delete line left.
	Virtual space converted to real space for delete word right.
	Preserve virtual space when pressing Delete key.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2882566&group_id=2439">Bug #2882566.</a>
	</li>
	<li>
	Fixed problem where Shift+Alt+Down did not move through wrapped lines.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2871749&group_id=2439">Bug #2871749.</a>
	</li>
	<li>
	Fixed incorrect background colour when using coloured lines with virtual space.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2914691&group_id=2439">Bug #2914691.</a>
	</li>
	<li>
	Fixed failure to display wrap symbol for SC_WRAPVISUALFLAGLOC_END_BY_TEXT.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2936108&group_id=2439">Bug #2936108.</a>
	</li>
	<li>
	Fixed blank background colour with EOLFilled style on last line.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2890105&group_id=2439">Bug #2890105.</a>
	</li>
	<li>
	Fixed problem in VB lexer with keyword at end of file.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2901239&group_id=2439">Bug #2901239.</a>
	</li>
	<li>
	Fixed SciTE bug where double clicking on a tab closed the file.
	</li>
	<li>
	Fixed SciTE brace matching commands to only work when the caret is next to the brace, not when
	it is in virtual space.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2885560&group_id=2439">Bug #2885560.</a>
	</li>
	<li>
	Fixed SciTE on Windows Vista to access files in the Program Files directory rather than allow Windows
	to virtualize access.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2916685&group_id=2439">Bug #2916685.</a>
	</li>
	<li>
	Fixed NSIS folder to handle keywords that start with '!'.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2872157&group_id=2439">Bug #2872157.</a>
	</li>
	<li>
	Changed linkage of Scintilla_LinkLexers to "C" so that it can be used by clients written in C.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2844718&group_id=2439">Bug #2844718.</a>
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite201.zip?download">Release 2.01</a>
    </h3>
    <ul>
	<li>
	Released on 19 August 2009.
	</li>
	<li>
	Fix to positioning rectangular paste when viewing line ends.
	</li>
	<li>
	Don't insert new lines and indentation for line ends at end of rectangular paste.
	</li>
	<li>
	When not in additional selection typing mode, cutting a rectangular selection removes all of the selected text.
	</li>
	<li>
	Rectangular selections are copied to the clipboard in document order, not in the order of selection.
	</li>
	<li>
	SCI_SETCURRENTPOS and SCI_SETANCHOR work in rectangular mode.
	</li>
	<li>
	On GTK+, drag and drop to a later position in the document now drops at the position.
	</li>
	<li>
	Fix bug where missing property did not use default value.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite200.zip?download">Release 2.0</a>
    </h3>
    <ul>
	<li>
	Released on 11 August 2009.
	</li>
	<li>
	Multiple pieces of text can be selected simultaneously by holding control while dragging the mouse.
	Typing, backspace and delete may affect all selections together.
	</li>
	<li>
	Virtual space allows selecting beyond the last character on a line.
	</li>
	<li>
	SciTE on GTK+ path bar is now optional and defaults to off.
	</li>
	<li>
	MagikSF lexer recognizes numbers correctly.
	</li>
	<li>
	Folding of Python comments and blank lines improved. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=210240&group_id=2439">Bug #210240.</a>
	</li>
	<li>
	Bug fixed where background colour of last character in document leaked past that character.
	</li>
	<li>
	Crash fixed when adding marker beyond last line in document. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2830307&group_id=2439">Bug #2830307.</a>
	</li>
	<li>
	Resource leak fixed in SciTE for Windows when printing fails. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2816524&group_id=2439">Bug #2816524.</a>
	</li>
	<li>
	Bug fixed on Windows where the system caret was destroyed during destruction when another window
	was using the system caret. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2830223&group_id=2439">Bug #2830223.</a>
	</li>
	<li>
	Bug fixed where indentation guides were drawn over text when the indentation used a style with a different
	space width to the default style.
	</li>
	<li>
	SciTE bug fixed where box comment added a bare line feed rather than the chosen line end. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2818104&group_id=2439">Bug #2818104.</a>
	</li>
	<li>
	Reverted fix that led to wrapping whole document when displaying the first line of the document.
	</li>
	<li>
	Export to LaTeX in SciTE fixed to work in more cases and not use as much space. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=1286548&group_id=2439">Bug #1286548.</a>
	</li>
	<li>
	Bug fixed where EN_CHANGE notification was sent when performing a paste operation in a
	read-only document. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2825485&group_id=2439">Bug #2825485.</a>
	</li>
	<li>
	Refactored code so that Scintilla exposes less of its internal implementation and uses the C++ standard
	library for some basic collections. Projects that linked to Scintilla's SString or PropSet classes
	should copy this code from a previous version of Scintilla or from SciTE.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite179.zip?download">Release 1.79</a>
    </h3>
    <ul>
	<li>
	Released on 1 July 2009.
	</li>
	<li>
	Memory exhaustion and other exceptions handled by placing an error value into the
	status property rather than crashing.
	Scintilla now builds with exception handling enabled and requires exception handling to be enabled. <br />
	This is a major change and application developers should consider how they will deal with Scintilla exhausting
	memory since Scintilla may not be in a stable state.
	</li>
	<li>
	Deprecated APIs removed. The symbols removed are:
	<ul>
 <li>SCI_SETCARETPOLICY</li>
<li> CARET_CENTER</li>
<li> CARET_XEVEN</li>
<li> CARET_XJUMPS</li>
<li> SC_FOLDFLAG_BOX</li>
<li> SC_FOLDLEVELBOXHEADERFLAG</li>
<li> SC_FOLDLEVELBOXFOOTERFLAG</li>
<li> SC_FOLDLEVELCONTRACTED</li>
<li> SC_FOLDLEVELUNINDENT</li>
<li> SCN_POSCHANGED</li>
<li> SCN_CHECKBRACE</li>
<li> SCLEX_ASP</li>
<li> SCLEX_PHP</li>
</ul>
	</li>
	<li>
	Cocoa platform added.
	</li>
	<li>
	Names of struct types in Scintilla.h now start with "Sci_" to avoid possible clashes with platform
	definitions. Currently, the old names still work but these will be phased out.
	</li>
	<li>
	When lines are wrapped, subsequent lines may be indented to match the indent of the initial line,
	or one more indentation level. <a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2796119&group_id=2439">Feature #2796119.</a>
	</li>
	<li>
	APIs added for finding the character at a point rather than an inter-character position. <a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2646738&group_id=2439">Feature #2646738.</a>
	</li>
	<li>
	A new marker SC_MARK_BACKGROUND_UNDERLINE is drawn in the text area as an underline
	the full width of the window.
	</li>
	<li>
	Batch file lexer understands variables surrounded by '!'.
	</li>
	<li>
	CAML lexer also supports SML.
	</li>
	<li>
	D lexer handles string and numeric literals more accurately. <a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2793782&group_id=2439">Feature #2793782.</a>
	</li>
	<li>
	Forth lexer is now case-insensitive and better supports numbers like $hex and %binary. <a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2804894&group_id=2439">Feature #2804894.</a>
	</li>
	<li>
	Lisp lexer treats '[', ']', '{', and '}' as balanced delimiters which is common usage. <a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2794989&group_id=2439">Feature #2794989.</a>
	<br />
	It treats keyword argument names as being equivalent to symbols. <a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2794901&group_id=2439">Feature #2794901.</a>
	</li>
	<li>
	Pascal lexer bug fixed to prevent hang when 'interface' near beginning of file. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2802863&group_id=2439">Bug #2802863.</a>
	</li>
	<li>
	Perl lexer bug fixed where previous lexical states persisted causing "/" special case styling and
	subroutine prototype styling to not be correct. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2809168&group_id=2439">Bug #2809168.</a>
	</li>
	<li>
	XML lexer fixes bug where Unicode entities like '&amp;—' were broken into fragments. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2804760&group_id=2439">Bug #2804760.</a>
	</li>
	<li>
	SciTE on GTK+ enables scrolling the tab bar on recent versions of GTK+. <a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2061821&group_id=2439">Feature #2061821.</a>
	</li>
	<li>
	SciTE on Windows allows tab bar tabs to be reordered by drag and drop.
	</li>
	<li>
	Unit test script for Scintilla on Windows included with source code.
	</li>
	<li>
	User defined menu items are now localized when there is a matching translation.
	</li>
	<li>
	Width of icon column of autocompletion lists on GTK+ made more consistent.
	</li>
	<li>
	Bug with slicing UTF-8 text into character fragments when there is a sequence of 100 or more 3 byte characters. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2780566&group_id=2439">Bug #2780566.</a>
	</li>
	<li>
	Folding bugs introduced in 1.78 fixed. Some of the fix was generic and there was also a specific fix for C++.
	</li>
	<li>
	Bug fixed where a rectangular paste was not padding the line with sufficient spaces to align the pasted text.
	</li>
	<li>
	Bug fixed with showing all text on each line of multi-line annotations when styling the whole annotation using SCI_ANNOTATIONSETSTYLE. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2789430&group_id=2439">Bug #2789430.</a>
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite178.zip?download">Release 1.78</a>
    </h3>
    <ul>
	<li>
	Released on 28 April 2009.
	</li>
	<li>
	Annotation lines may be added to each line.
	</li>
	<li>
	A text margin may be defined with different text on each line.
	</li>
	<li>
	Application actions may be added to the undo history.
	</li>
	<li>
	Can query the symbol defined for a marker.
	An available symbol added for applications to indicate that plugins may allocate a marker.
	</li>
	<li>
	Can increase the amount of font ascent and descent.
	</li>
	<li>
	COBOL lexer added. <a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2127406&group_id=2439">Feature #2127406.</a>
	</li>
	<li>
	Nimrod lexer added. <a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2642620&group_id=2439">Feature #2642620.</a>
	</li>
	<li>
	PowerPro lexer added. <a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2195308&group_id=2439">Feature #2195308.</a>
	</li>
	<li>
	SML lexer added. <a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2710950&group_id=2439">Feature #2710950.</a>
	</li>
	<li>
	SORCUS Installation file lexer added. <a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2343375&group_id=2439">Feature #2343375.</a>
	</li>
	<li>
	TACL lexer added. <a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2127406&group_id=2439">Feature #2127406.</a>
	</li>
	<li>
	TAL lexer added. <a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2127406&group_id=2439">Feature #2127406.</a>
	</li>
	<li>
	Rewritten Pascal lexer with improved folding and other fixes. <a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2190650&group_id=2439">Feature #2190650.</a>
	</li>
	<li>
	INDIC_ROUNDBOX translucency level can be modified. <a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2586290&group_id=2439">Feature #2586290.</a>
	</li>
	<li>
	C++ lexer treats angle brackets in #include directives as quotes when styling.within.preprocessor. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2551033&group_id=2439">Bug #2551033.</a>
	</li>
	<li>
	Inno Setup lexer is sensitive to whether within the [Code] section and handles comments better. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2552973&group_id=2439">Bug #2552973.</a>
	</li>
	<li>
	HTML lexer does not go into script mode when script tag is self-closing.
	</li>
	<li>
	HTML folder fixed where confused by comments when fold.html.preprocessor off. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2532774&group_id=2439">Bug #2532774.</a>
	</li>
	<li>
	Perl lexer fixes problem with string matching caused by line endings. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2648342&group_id=2439">Bug #2648342.</a>
	</li>
	<li>
	Progress lexer fixes problem with "last-event:function" phrase. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2483619&group_id=2439">Bug #2483619.</a>
	</li>
	<li>
	Properties file lexer extended to handle RFC2822 text when lexer.props.allow.initial.spaces on.
	</li>
	<li>
	Python lexer adds options for Python 3 and Cython.
	</li>
	<li>
	Shell lexer fixes heredoc problem caused by line endings. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2635257&group_id=2439">Bug #2635257.</a>
	</li>
	<li>
	TeX lexer handles comment at end of line correctly. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2698766&group_id=2439">Bug #2698766.</a>
	</li>
	<li>
	SciTE retains selection range when performing a replace selection command. <a href="https://sourceforge.net/tracker/?func=detail&atid=352439&aid=2339160&group_id=2439">Feature #2339160.</a>
	</li>
	<li>
	SciTE definition of word characters fixed to match documentation. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2464531&group_id=2439">Bug #2464531.</a>
	</li>
	<li>
	SciTE on GTK+ performing Search or Replace when dialog already shown now brings dialog to foreground.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2634224&group_id=2439">Bug #2634224.</a>
	</li>
	<li>
	Fixed encoding bug with calltips on GTK+.
	</li>
	<li>
	Block caret drawn in correct place on wrapped lines. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2126144&group_id=2439">Bug #2126144.</a>
	</li>
	<li>
	Compilation for 64 bit Windows works using MinGW. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2515578&group_id=2439">Bug #2515578.</a>
	</li>
	<li>
	Incorrect memory freeing fixed on OS X.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2354098&group_id=2439">Bug #2354098</a>,
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2671749&group_id=2439">Bug #2671749.</a>
	</li>
	<li>
	SciTE on GTK+ crash fixed on startup when child process exits before initialization complete.
	<a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2716987&group_id=2439">Bug #2716987.</a>
	</li>
	<li>
	Crash fixed when AutoCompleteGetCurrent called with no active autocompletion.
	</li>
	<li>
	Flickering diminished when pressing Tab. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2723006&group_id=2439">Bug #2723006.</a>
	</li>
	<li>
	Namespace compilation issues with GTK+ on OS X fixed.
	</li>
	<li>
	Increased maximum length of SciTE's Language menu on GTK+ to 100 items. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2528241&group_id=2439">Bug #2528241.</a>
	</li>
	<li>
	Fixed incorrect Python lexing for multi-line continued strings. <a href="https://sourceforge.net/tracker/?func=detail&atid=102439&aid=2450963&group_id=2439">Bug #2450963.</a>
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite177.zip?download">Release 1.77</a>
    </h3>
    <ul>
	<li>
	Released on 18 October 2008.
	</li>
	<li>
	Direct temporary access to Scintilla's text buffer to allow simple efficient interfacing
	to libraries like regular expression libraries.
	</li>
	<li>
	Scintilla on Windows can interpret keys as Unicode even when a narrow character
	window with SCI_SETKEYSUNICODE.
	</li>
	<li>
	Notification sent when autocompletion cancelled.
	</li>
	<li>
	MySQL lexer added.
	</li>
	<li>
	Lexer for gettext .po files added.
	</li>
	<li>
	Abaqus lexer handles program structure more correctly.
	</li>
	<li>
	Assembler lexer works with non-ASCII text.
	</li>
	<li>
	C++ lexer allows mixed case doc comment tags.
	</li>
	<li>
	CSS lexer updated and works with non-ASCII.
	</li>
	<li>
	Diff lexer adds style for changed lines, handles subversion diffs better and
	fixes styling and folding for lines containing chunk dividers ("---").
	</li>
	<li>
	FORTRAN lexer accepts more styles of compiler directive.
	</li>
	<li>
	Haskell lexer allows hexadecimal literals.
	</li>
	<li>
	HTML lexer improves PHP and JavaScript folding.
	PHP heredocs, nowdocs, strings and comments processed more accurately.
	Internet Explorer's non-standard &gt;comment&lt; tag supported.
	Script recognition in XML can be controlled with lexer.xml.allow.scripts property.
	</li>
	<li>
	Lua lexer styles last character correctly.
	</li>
	<li>
	Perl lexer update.
	</li>
	<li>
	Comment folding implemented for Ruby.
	</li>
	<li>
	Better TeX folding.
	</li>
	<li>
	Verilog lexer updated.
	</li>
	<li>
	Windows Batch file lexer handles %~ and %*.
	</li>
	<li>
	YAML lexer allows non-ASCII text.
	</li>
	<li>
	SciTE on GTK+ implements "Replace in Buffers" in advanced mode.
	</li>
	<li>
	The extender OnBeforeSave method can override the default file saving behaviour by retuning true.
	</li>
	<li>
	Window position and recent files list may be saved into the session file.
	</li>
	<li>
	Right button press outside the selection moves the caret.
	</li>
	<li>
	SciTE load.on.activate works when closing a document reveals a changed document.
	</li>
	<li>
	SciTE bug fixed where eol.mode not used for initial buffer.
	</li>
	<li>
	SciTE bug fixed where a file could be saved as the same name as another
	buffer leading to confusing behaviour.
	</li>
	<li>
	Fixed display bug for long lines in same style on Windows.
	</li>
	<li>
	Fixed SciTE crash when finding matching preprocessor command used on some files.
	</li>
	<li>
	Drawing performance improved for files with many blank lines.
	</li>
	<li>
	Folding bugs fixed where changing program text produced a decrease in fold level on a fold header line.
	</li>
	<li>
	Clearing document style now clears all indicators.
	</li>
	<li>
	SciTE's embedded Lua updated to 5.1.4.
	</li>
	<li>
	SciTE will compile with versions of GTK+ before 2.8 again.
	</li>
	<li>
	SciTE on GTK+ bug fixed where multiple files not opened.
	</li>
	<li>
	Bug fixed with SCI_VCHOMEWRAP and SCI_VCHOMEWRAPEXTEND on white last line.
	</li>
	<li>
	Regular expression bug fixed where "^[^(]+$" matched empty lines.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite176.zip?download">Release 1.76</a>
    </h3>
    <ul>
	<li>
	Released on 16 March 2008.
	</li>
	<li>
	Support for PowerShell.
	</li>
	<li>
	Lexer added for Magik.
	</li>
	<li>
	Director extension working on GTK+.
	</li>
	<li>
	Director extension may set focus to SciTE through "focus:" message on GTK+.
	</li>
	<li>
	C++ folder handles final line better in some cases.
	</li>
	<li>
	SCI_COPYALLOWLINE added which is similar to SCI_COPY except that if the selection is empty then
	the line holding the caret is copied. On Windows an extra clipboard format allows pasting this as a whole
	line before the current selection. This behaviour is compatible with Visual Studio.
	</li>
	<li>
	On Windows, the horizontal scroll bar can handle wider files.
	</li>
	<li>
	On Windows, a system palette leak was fixed. Should not affect many as palette mode is rarely used.
	</li>
	<li>
	Install command on GTK+ no longer tries to set explicit owner.
	</li>
	<li>
	Perl lexer handles defined-or operator "//".
	</li>
	<li>
	Octave lexer fixes "!=" operator.
	</li>
	<li>
	Optimized selection change drawing to not redraw as much when not needed.
	</li>
	<li>
	SciTE on GTK+ no longer echoes Lua commands so is same as on Windows.
	</li>
	<li>
	Automatic vertical scrolling limited to one line at a time so is not too fast.
	</li>
	<li>
	Crash fixed when line states set beyond end of line states. This occurred when lexers did not
	set a line state for each line.
	</li>
	<li>
	Crash in SciTE on Windows fixed when search for 513 character string fails.
	</li>
	<li>
	SciTE disables translucent features on Windows 9x due to crashes reported when using translucency.
	</li>
	<li>
	Bug fixed where whitespace background was not seen on wrapped lines.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite175.zip?download">Release 1.75</a>
    </h3>
    <ul>
	<li>
	Released on 22 November 2007.
	</li>
	<li>
	Some WordList and PropSet functionality moved from Scintilla to SciTE.
	Projects that link to Scintilla's code for these classes may need to copy
	code from SciTE.
	</li>
	<li>
	Borland C++ can no longer build Scintilla.
	</li>
	<li>
	Invalid bytes in UTF-8 mode are displayed as hex blobs. This also prevents crashes due to
	passing invalid UTF-8 to platform calls.
	</li>
	<li>
	Indentation guides enhanced to be visible on completely empty lines when possible.
	</li>
	<li>
	The horizontal scroll bar may grow to match the widest line displayed.
	</li>
	<li>
	Allow autocomplete pop ups to appear outside client rectangle in some cases.
	</li>
	<li>
	When line state changed, SC_MOD_CHANGELINESTATE modification notification sent and
	margin redrawn.
	</li>
	<li>
	SciTE scripts can access the menu command values IDM_*.
	</li>
	<li>
	SciTE's statement.end property has been implemented again.
	</li>
	<li>
	SciTE shows paths and matches in different styles for Find In Files.
	</li>
	<li>
	Incremental search in SciTE for Windows is modeless to make it easier to exit.
	</li>
	<li>
	Folding performance improved.
	</li>
	<li>
	SciTE for GTK+ now includes a Browse button in the Find In Files dialog.
	</li>
	<li>
	On Windows versions that support Unicode well, Scintilla is a wide character window
	which allows input for some less common languages like Armenian, Devanagari,
	Tamil, and Georgian. To fully benefit, applications should use wide character calls.
	</li>
	<li>
	Lua function names are exported from SciTE to allow some extension libraries to work.
	</li>
	<li>
	Lexers added for Abaqus, Ansys APDL, Asymptote, and R.
	</li>
	<li>
	SCI_DELWORDRIGHTEND added for closer compatibility with GTK+ entry widget.
	</li>
	<li>
	The styling buffer may now use all 8 bits in each byte for lexical states with 0 bits for indicators.
	</li>
	<li>
	Multiple characters may be set for SciTE's calltip.&lt;lexer&gt;.parameters.start property.
	</li>
	<li>
	Bash lexer handles octal literals.
	</li>
	<li>
	C++/JavaScript lexer recognizes regex literals in more situations.
	</li>
	<li>
	Haskell lexer fixed for quoted strings.
	</li>
	<li>
	HTML/XML lexer does not notice XML indicator if there is
	non-whitespace between the "&lt;?" and "XML".
	ASP problem fixed where &lt;/ is used inside a comment.
	</li>
	<li>
	Error messages from Lua 5.1 are recognized.
	</li>
	<li>
	Folding implemented for Metapost.
	</li>
	<li>
	Perl lexer enhanced for handling minus-prefixed barewords,
	underscores in numeric literals and vector/version strings,
	^D and ^Z similar to __END__,
	subroutine prototypes as a new lexical class,
	formats and format blocks as new lexical classes, and
	'/' suffixed keywords and barewords.
	</li>
	<li>
	Python lexer styles all of a decorator in the decorator style rather than just the name.
	</li>
	<li>
	YAML lexer styles colons as operators.
	</li>
	<li>
	Fixed SciTE bug where undo would group together multiple separate modifications.
	</li>
	<li>
	Bug fixed where setting background colour of calltip failed.
	</li>
	<li>
	SciTE allows wildcard suffixes for file pattern based properties.
	</li>
	<li>
	SciTE on GTK+ bug fixed where user not prompted to save untitled buffer.
	</li>
	<li>
	SciTE bug fixed where property values from one file were not seen by lower priority files.
	</li>
	<li>
	Bug fixed when showing selection with a foreground colour change which highlighted
	an incorrect range in some positions.
	</li>
	<li>
	Cut now invokes SCN_MODIFYATTEMPTRO notification.
	</li>
	<li>
	Bug fixed where caret not shown at beginning of wrapped lines.
	Caret made visible in some cases after wrapping and scroll bar updated after wrapping.
	</li>
	<li>
	Modern indicators now work on wrapped lines.
	</li>
	<li>
	Some crashes fixed for 64-bit GTK+.
	</li>
	<li>
	On GTK+ clipboard features improved for VMWare tools copy and paste.
	SciTE exports the clipboard more consistently on shut down.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite174.zip?download">Release 1.74</a>
    </h3>
    <ul>
	<li>
	Released on 18 June 2007.
	</li>
	<li>
	OS X support.
	</li>
	<li>
	Indicators changed to be a separate data structure allowing more indicators. Storing indicators in high bits
	of styling bytes is deprecated and will be removed in the next version.
	</li>
	<li>
	Unicode support extended to all Unicode characters not just the Basic Multilingual Plane.
	</li>
	<li>
	Performance improved on wide lines by breaking long runs in a single style into shorter segments.
	</li>
	<li>
	Performance improved by caching layout of short text segments.
	</li>
	<li>
	SciTE includes Lua 5.1.
	</li>
	<li>
	Caret may be displayed as a block.
	</li>
	<li>
	Lexer added for GAP.
	</li>
	<li>
	Lexer added for PL/M.
	</li>
	<li>
	Lexer added for Progress.
	</li>
	<li>
	SciTE session files have changed format to be like other SciTE .properties files
	and now use the extension .session.
	Bookmarks and folds may optionally be saved in session files.
	Session files created with previous versions of SciTE will not load into this version.
	</li>
	<li>
	SciTE's extension and scripting interfaces add OnKey, OnDwellStart, and OnClose methods.
	</li>
	<li>
	On GTK+, copying to the clipboard does not include the text/urilist type since this caused problems when
	pasting into Open Office.
	</li>
	<li>
	On GTK+, Scintilla defaults caret blink rate to platform preference.
	</li>
	<li>
	Dragging does not start until the mouse has been dragged a certain amount.
	This stops spurious drags when just clicking inside the selection.
	</li>
	<li>
	Bug fixed where brace highlight not shown when caret line background set.
	</li>
	<li>
	Bug fixed in Ruby lexer where out of bounds access could occur.
	</li>
	<li>
	Bug fixed in XML folding where tags were not being folded because they are singletons in HTML.
	</li>
	<li>
	Bug fixed when many font names used.
	</li>
	<li>
	Layout bug fixed on GTK+ where fonts have ligatures available.
	</li>
	<li>
	Bug fixed with SCI_LINETRANSPOSE on a blank line.
	</li>
	<li>
	SciTE hang fixed when using UNC path with directory properties feature.
	</li>
	<li>
	Bug on Windows fixed by examining dropped text for Unicode even in non-Unicode mode so it
	can work when source only provides Unicode or when using an encoding different from the
	system default.
	</li>
	<li>
	SciTE bug on GTK+ fixed where Stop Executing did not work when more than a single process started.
	</li>
	<li>
	SciTE bug on GTK+ fixed where mouse wheel was not switching between buffers.
	</li>
	<li>
	Minor line end fix to PostScript lexer.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite173.zip?download">Release 1.73</a>
    </h3>
    <ul>
	<li>
	Released on 31 March 2007.
	</li>
	<li>
	SciTE adds a Directory properties file to configure behaviour for files in a directory and its subdirectories.
	</li>
	<li>
	Style changes may be made during text modification events.
	</li>
	<li>
	Regular expressions recognize \d, \D, \s, \S, \w, \W, and \xHH.
	</li>
	<li>
	Support for cmake language added.
	</li>
	<li>
	More Scintilla properties can be queried.
	</li>
	<li>
	Edge line drawn under text.
	</li>
	<li>
	A savesession command added to SciTE director interface.
	</li>
	<li>
	SciTE File | Encoding menu item names changed to be less confusing.
	</li>
	<li>
	SciTE on GTK+ dialog buttons reordered to follow guidelines.
	</li>
	<li>
	SciTE on GTK+ removed GTK+ 1.x compatible file dialog code.
	</li>
	<li>
	SciTE on GTK+ recognizes key names KeypadMultiply and KeypadDivide.
	</li>
	<li>
	Background colour of line wrapping visual flag changed to STYLE_DEFAULT.
	</li>
	<li>
	Makefile lexing enhanced for ':=' operator and when lines start with tab.
	</li>
	<li>
	TADS3 lexer and folder improved.
	</li>
	<li>
	SCN_DOUBLECLICK notification may set SCI_SHIFT, SCI_CTRL, and SCI_ALT flags on modifiers field.
	</li>
	<li>
	Slow folding of large constructs in Python fixed.
	</li>
	<li>
	MSSQL folding fixed to be case-insensitive and fold at more keywords.
	</li>
	<li>
	SciTE's brace matching works better for HTML.
	</li>
	<li>
	Determining API list items checks for specified parameters start character before default '('.
	</li>
	<li>
	Hang fixed in HTML lexer.
	</li>
	<li>
	Bug fixed in with LineTranspose command where markers could move to different line.
	</li>
	<li>
	Memory released when buffer completely emptied.
	</li>
	<li>
	If translucency not available on Windows, draw rectangular outline instead.
	</li>
	<li>
	Bash lexer handles "-x" in "--x-includes..." better.
	</li>
	<li>
	AutoIt3 lexer fixes string followed by '+'.
	</li>
	<li>
	LinesJoin fixed where it stopped early due to not adjusting for inserted spaces..
	</li>
	<li>
	StutteredPageDown fixed when lines wrapped.
	</li>
	<li>
	FormatRange fixed to not double count line number width which could lead to a large space.
	</li>
	<li>
	SciTE Export As PDF and Latex commands fixed to format floating point numbers with '.' even in locales
	that use ','.
	</li>
	<li>
	SciTE bug fixed where File | New could produce buffer with contents of previous file when using read-only mode.
	</li>
	<li>
	SciTE retains current scroll position when switching buffers and fold.on.open set.
	</li>
	<li>
	SciTE crash fixed where '*' used to invoke parameters dialog.
	</li>
	<li>
	SciTE bugs when writing large UCS-2 files fixed.
	</li>
	<li>
	Bug fixed when scrolling inside a SCN_PAINTED event by invalidating window
	rather than trying to perform synchronous painting.
	</li>
	<li>
	SciTE for GTK+ View | Full Screen works on recent versions of GTK+.
	</li>
	<li>
	SciTE for Windows enables and disables toolbar commands correctly.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite172.zip?download">Release 1.72</a>
    </h3>
    <ul>
	<li>
	Released on 15 January 2007.
	</li>
	<li>
	Performance of per-line data improved.
	</li>
	<li>
	SC_STARTACTION flag set on the first modification notification in an undo
	transaction to help synchronize the container's undo stack with Scintilla's.
	</li>
	<li>
	On GTK+ drag and drop defaults to move rather than copy.
	</li>
	<li>
	Scintilla supports extending appearance of selection to right hand margin.
	</li>
	<li>
	Incremental search available on GTK+.
	</li>
	<li>
	SciTE Indentation Settings dialog available on GTK+ and adds a "Convert" button.
	</li>
	<li>
	Find in Files can optionally ignore binary files or directories that start with ".".
	</li>
	<li>
	Lexer added for "D" language.
	</li>
	<li>
	Export as HTML shows folding with underline lines and +/- symbols.
	</li>
	<li>
	Ruby lexer interprets interpolated strings as expressions.
	</li>
	<li>
	Lua lexer fixes some cases of numeric literals.
	</li>
	<li>
	C++ folder fixes bug with "@" in doc comments.
	</li>
	<li>
	NSIS folder handles !if and related commands.
	</li>
	<li>
	Inno setup lexer adds styling for single and double quoted strings.
	</li>
	<li>
	Matlab lexer handles backslashes in string literals correctly.
	</li>
	<li>
	HTML lexer fixed to allow "?&gt;" in comments in Basic script.
	</li>
	<li>
	Added key codes for Windows key and Menu key.
	</li>
	<li>
	Lua script method scite.MenuCommand(x) performs a menu command.
	</li>
	<li>
	SciTE bug fixed with box comment command near start of file setting selection to end of file.
	</li>
	<li>
	SciTE on GTK+, fixed loop that occurred with automatic loading for an unreadable file.
	</li>
	<li>
	SciTE asks whether to save files when Windows shuts down.
	</li>
	<li>
	Save Session on Windows now defaults the extension to "ses".
	</li>
	<li>
	Bug fixed with single character keywords.
	</li>
	<li>
	Fixed infinite loop for SCI_GETCOLUMN for position beyond end of document.
	</li>
	<li>
	Fixed failure to accept typing on Solaris/GTK+ when using default ISO-8859-1 encoding.
	</li>
	<li>
	Fixed warning from Lua in SciTE when creating a new buffer when already have
	maximum number of buffers open.
	</li>
	<li>
	Crash fixed with "%%" at end of batch file.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite171.zip?download">Release 1.71</a>
    </h3>
    <ul>
	<li>
	Released on 21 August 2006.
	</li>
	<!--li>
	On GTK+ drag and drop defaults to move rather than copy.
	</li-->
	<li>
	Double click notification includes line and position.
	</li>
	<li>
	VB lexer bugs fixed for preprocessor directive below a comment or some other states and
	to use string not closed style back to the starting quote when there are internal doubled quotes.
	</li>
	<li>
	C++ lexer allows identifiers to contain '$' and non-ASCII characters such as UTF-8.
	The '$' character can be disallowed with lexer.cpp.allow.dollars=0.
	</li>
	<li>
	Perl lexer allows UTF-8 identifiers and has some other small improvements.
	</li>
	<li>
	SciTE's $(CurrentWord) uses word.characters.&lt;filepattern&gt; to define the word
	rather than a hardcoded list of word characters.
	</li>
	<li>
	SciTE Export as HTML adds encoding information for UTF-8 file and fixes DOCTYPE.
	</li>
	<li>
	SciTE session and .recent files default to the user properties directory rather than global
	properties directory.
	</li>
	<li>
	Left and right scroll events handled correctly on GTK+ and horizontal scroll bar has more sensible
	distances for page and arrow clicks.
	</li>
	<li>
	SciTE on GTK+ tab bar fixed to work on recent versions of GTK+.
	</li>
	<li>
	On GTK+, if the approximate character set conversion is unavailable, a second attempt is made
	without approximations. This may allow keyboard input and paste to work on older systems.
	</li>
	<li>
	SciTE on GTK+ can redefine the Insert key.
	</li>
	<li>
	SciTE scripting interface bug fixed where some string properties could not be changed.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite170.zip?download">Release 1.70</a>
    </h3>
    <ul>
	<li>
	Released on 20 June 2006.
	</li>
	<li>
	On GTK+, character set conversion is performed using an option that allows approximate conversions rather
	than failures when a character can not be converted. This may lead to similar characters being inserted or
	when no similar character is available a '?' may be inserted.
	</li>
	<li>
	On GTK+, the internationalized IM (Input Method) feature is used for all typed input for all character sets.
	</li>
	<li>
	Scintilla has new margin types SC_MARGIN_BACK and SC_MARGIN_FORE that use the default
	style's background and foreground colours (normally white and black) as the background to the margin.
	</li>
	<li>
	Scintilla/GTK+ allows file drops on Windows when drop is of type DROPFILES_DND
	as well as text/uri-list.
	</li>
	<li>
	Code page can only be set to one of the listed valid values.
	</li>
	<li>
	Text wrapping fixed for cases where insertion was not wide enough to trigger
	wrapping before being styled but was after styling.
	</li>
	<li>
	SciTE find marks are removed before printing or exporting to avoid producing incorrect styles.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite169.zip?download">Release 1.69</a>
    </h3>
    <ul>
	<li>
	Released on 29 May 2006.
	</li>
	<li>
	SciTE supports z-order based buffer switching on Ctrl+Tab.
	</li>
	<li>
	Translucent support for selection and whole line markers.
	</li>
	<li>
	SciTE may have per-language abbreviations files.
	</li>
	<li>
	Support for Spice language.
	</li>
	<li>
	On GTK+ autocompletion lists are optimized and use correct selection colours.
	</li>
	<li>
	On GTK+ the URI data type is preferred in drag and drop so that applications
	will see files dragged from the shell rather than dragging the text of the file name
	into the document.
	</li>
	<li>
	Increased number of margins to 5.
	</li>
	<li>
	Basic lexer allows include directive $include: "file name".
	</li>
	<li>
	SQL lexer no longer bases folding on indentation.
	</li>
	<li>
	Line ends are transformed when copied to clipboard on
	Windows/GTK+2 as well as Windows/GTK+ 1.
	</li>
	<li>
	Lexing code masks off the indicator bits on the start style before calling the lexer
	to avoid confusing the lexer when an application has used an indicator.
	</li>
	<li>
	SciTE savebefore:yes only saves the file when it has been changed.
	</li>
	<li>
	SciTE adds output.initial.hide setting to allow setting the size of the output pane
	without it showing initially.
	</li>
	<li>
	SciTE on Windows Go To dialog allows line number with more digits.
	</li>
	<li>
	Bug in HTML lexer fixed where a segment of PHP could switch scripting language
	based on earlier text on that line.
	</li>
	<li>
	Memory bug fixed when freeing regions on GTK+.
	Other minor bugs fixed on GTK+.
	</li>
	<li>
	Deprecated GTK+ calls in Scintilla replaced with current calls.
	</li>
	<li>
	Fixed a SciTE bug where closing the final buffer, if read-only, left the text present in an
	untitled buffer.
	</li>
	<li>
	Bug fixed in bash lexer that prevented folding.
	</li>
	<li>
	Crash fixed in bash lexer when backslash at end of file.
	</li>
	<li>
	Crash on recent releases of GTK+ 2.x avoided by changing default font from X
	core font to Pango font "!Sans".
	</li>
	<li>
	Fix for SciTE properties files where multiline properties continued over completely blank lines.
	</li>
	<li>
	Bug fixed in SciTE/GTK+ director interface where more data available than
	buffer size.
	</li>
	<li>
	Minor visual fixes to SciTE splitter on GTK+.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite168.zip?download">Release 1.68</a>
    </h3>
    <ul>
	<li>
	Released on 9 March 2006.
	</li>
	<li>
	Translucent drawing implemented for caret line and box indicators.
	</li>
	<li>
	Lexer specifically for TCL is much more accurate than reusing C++ lexer.
	</li>
	<li>
	Support for Inno Setup scripts.
	</li>
	<li>
	Support for Opal language.
	</li>
	<li>
	Calltips may use a new style, STYLE_CALLTIP which allows choosing a
	different font for calltips.
	</li>
	<li>
	Python lexer styles comments on decorators.
	</li>
	<li>
	HTML lexer refined handling of "?>" and "%>" within server
	side scripts.
	</li>
	<li>
	Batch file lexer improved.
	</li>
	<li>
	Eiffel lexer doesn't treat '.' as a name character.
	</li>
	<li>
	Lua lexer handles length operator, #, and hex literals.
	</li>
	<li>
	Properties file lexer has separate style for keys.
	</li>
	<li>
	PL/SQL folding improved.
	</li>
	<li>
	SciTE Replace dialog always searches in forwards direction.
	</li>
	<li>
	SciTE can detect language of file from initial #! line.
	</li>
	<li>
	SciTE on GTK+ supports output.scroll=2 setting.
	</li>
	<li>
	SciTE can perform an import a properties file from the command line.
	</li>
	<li>
	Set of word characters used for regular expression \&lt; and \&gt;.
	</li>
	<li>
	Bug fixed with SCI_COPYTEXT stopping too early.
	</li>
	<li>
	Bug fixed with splitting lines so that all lines are split.
	</li>
	<li>
	SciTE calls OnSwitchFile when closing one buffer causes a switch to another.
	</li>
	<li>
	SciTE bug fixed where properties were being reevaluated without good reason
	after running a macro.
	</li>
	<li>
	Crash fixed when clearing document with some lines contracted in word wrap mode.
	</li>
	<li>
	Palette expands as more entries are needed.
	</li>
	<li>
	SCI_POSITIONFROMPOINT returns more reasonable value when close to
	last text on a line.
	</li>
	<li>
	On Windows, long pieces of text may be drawn in segments if they fail to draw
	as a whole.
	</li>
	<li>
	Bug fixed with bad drawing when some visual changes made inside SCN_UPDATEUI
	notification.
	</li>
	<li>
	SciTE bug fixed with groupundo setting.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite167.zip?download">Release 1.67</a>
    </h3>
    <ul>
	<li>
	Released on 17 December 2005.
	</li>
	<li>
	Scintilla checks the paint region more accurately when seeing if an area is being
	repainted. Platform layer implementations may need to change for this to take
	effect. This fixes some drawing and styling bugs. Also optimized some parts of
	marker code to only redraw the line of the marker rather than whole of the margin.
	</li>
	<li>
	Quoted identifier style for SQL. SQL folding performed more simply.
	</li>
	<li>
	Ruby lexer improved to better handle here documents and non-ASCII
	characters.
	</li>
	<li>
	Lua lexer supports long string and block comment syntax from Lua 5.1.
	</li>
	<li>
	Bash lexer handles here documents better.
	</li>
	<li>
	JavaScript lexing recognizes regular expressions more accurately and includes flag
	characters in the regular expression style. This is both in JavaScript files and when
	JavaScript is embedded in HTML.
	</li>
	<li>
	Scintilla API provided to reveal how many style bits are needed for the
	current lexer.
	</li>
	<li>
	Selection duplicate added.
	</li>
	<li>
	Scintilla API for adding a set of markers to a line.
	</li>
	<li>
	DBCS encodings work on Windows 9x.
	</li>
	<li>
	Convention defined for property names to be used by lexers and folders
	so they can be automatically discovered and forwarded from containers.
	</li>
	<li>
	Default bookmark in SciTE changed to a blue sphere image.
	</li>
	<li>
	SciTE stores the time of last asking for a save separately for each buffer
	which fixes bugs with automatic reloading.
	</li>
	<li>
	On Windows, pasted text has line ends converted to current preference.
	GTK+ already did this.
	</li>
	<li>
	Kid template language better handled by HTML lexer by finishing ASP Python
	mode when a ?> is found.
	</li>
	<li>
	SciTE counts number of characters in a rectangular selection correctly.
	</li>
	<li>
	64-bit compatibility improved. One change that may affect user code is that
	the notification message header changed to include a pointer-sized id field
	to match the current Windows definition.
	</li>
	<li>
	Empty ranges can no longer be dragged.
	</li>
	<li>
	Crash fixed when calls made that use layout inside the painted notification.
	</li>
	<li>
	Bug fixed where Scintilla created pixmap buffers that were too large leading
	to failures when many instances used.
	</li>
	<li>
	SciTE sets the directory of a new file to the directory of the currently
	active file.
	</li>
	<li>
	SciTE allows choosing a code page for the output pane.
	</li>
	<li>
	SciTE HTML exporter no longer honours monospaced font setting.
	</li>
	<li>
	Line layout cache in page mode caches the line of the caret. An assertion is
	now used to ensure that the layout reentrancy problem that caused this
	is easier to find.
	</li>
	<li>
	Speed optimized for long lines and lines containing many control characters.
	</li>
	<li>
	Bug fixed in brace matching in DBCS files where byte inside character
	is same as brace.
	</li>
	<li>
	Indent command does not indent empty lines.
	</li>
	<li>
	SciTE bug fixed for commands that operate on files with empty extensions.
	</li>
	<li>
	SciTE bug fixed where monospaced option was copied for subsequently opened files.
	</li>
	<li>
	SciTE on Windows bug fixed in the display of a non-ASCII search string
	which can not be found.
	</li>
	<li>
	Bugs fixed with nested calls displaying a new calltip while one is already
	displayed.
	</li>
	<li>
	Bug fixed when styling PHP strings.
	</li>
	<li>
	Bug fixed when styling C++ continued preprocessor lines.
	</li>
	<li>
	SciTE bug fixed where opening file from recently used list reset choice of
	language.
	</li>
	<li>
	SciTE bug fixed when compiled with NO_EXTENSIONS and
	closing one file closes the application.
	</li>
	<li>
	SciTE crash fixed for error messages that look like Lua messages but aren't
	in the same order.
	</li>
	<li>
	Remaining fold box support deprecated. The symbols SC_FOLDLEVELBOXHEADERFLAG,
   SC_FOLDLEVELBOXFOOTERFLAG, SC_FOLDLEVELCONTRACTED,
   SC_FOLDLEVELUNINDENT, and SC_FOLDFLAG_BOX are deprecated.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite166.zip?download">Release 1.66</a>
    </h3>
    <ul>
	<li>
	Released on 26 August 2005.
	</li>
	<li>
	New, more ambitious Ruby lexer.
	</li>
	<li>
	SciTE Find in Files dialog has options for matching case and whole words which are
	enabled when the internal find command is used.
	</li>
	<li>
	SciTE output pane can display automatic completion after "$(" typed.
	An initial ">" on a line is ignored when Enter pressed.
	</li>
	<li>
	C++ lexer recognizes keywords within line doc comments. It continues styles over line
	end characters more consistently so that eolfilled style can be used for preprocessor lines
	and line comments.
	</li>
	<li>
	VB lexer improves handling of file numbers and date literals.
	</li>
	<li>
	Lua folder handles repeat until, nested comments and nested strings.
	</li>
	<li>
	POV lexer improves handling of comment lines.
	</li>
	<li>
	AU3 lexer and folder updated. COMOBJ style added.
	</li>
	<li>
	Bug fixed with text display on GTK+ with Pango 1.8.
	</li>
	<li>
	Caret painting avoided when not focused.
	</li>
	<li>
	SciTE on GTK+ handles file names used to reference properties as case-sensitive.
	</li>
	<li>
	SciTE on GTK+ Save As and Export commands set the file name field.
	On GTK+ the Export commands modify the file name in the same way as on Windows.
	</li>
	<li>
	Fixed SciTE problem where confirmation was not displaying when closing a file where all
	contents had been deleted.
	</li>
	<li>
	Middle click on SciTE tab now closes correct buffer on Windows when tool bar is visible.
	</li>
	<li>
	SciTE bugs fixed where files contained in directory that includes '.' character.
	</li>
	<li>
	SciTE bug fixed where import in user options was reading file from directory of
	global options.
	</li>
	<li>
	SciTE calltip bug fixed where single line calltips had arrow displayed incorrectly.
	</li>
	<li>
	SciTE folding bug fixed where empty lines were shown for no reason.
	</li>
	<li>
	Bug fixed where 2 byte per pixel XPM images caused crash although they are still not
	displayed.
	</li>
	<li>
	Autocompletion list size tweaked.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite165.zip?download">Release 1.65</a>
    </h3>
    <ul>
	<li>
	Released on 1 August 2005.
	</li>
	<li>
	FreeBasic support.
	</li>
	<li>
	SciTE on Windows handles command line arguments
	"-" (read standard input into buffer),
	"--" (read standard input into output pane) and
	"-@" (read file names from standard input and open each).
	</li>
	<li>
	SciTE includes a simple implementation of Find in Files which is used if no find.command is set.
	</li>
	<li>
	SciTE can close tabs with a mouse middle click.
	</li>
	<li>
	SciTE includes a save.all.for.build setting.
	</li>
	<li>
	Folder for MSSQL.
	</li>
	<li>
	Batch file lexer understands more of the syntax and the behaviour of built in commands.
	</li>
	<li>
	Perl lexer handles here docs better; disambiguates barewords, quote-like delimiters, and repetition operators;
	handles Pods after __END__; recognizes numbers better; and handles some typeglob special variables.
	</li>
	<li>
	Lisp adds more lexical states.
	</li>
	<li>
	PHP allows spaces after &lt;&lt;&lt;.
	</li>
	<li>
	TADS3 has a simpler set of states and recognizes identifiers.
	</li>
	<li>
	Avenue elseif folds better.
	</li>
	<li>
	Errorlist lexer treats lines starting with '+++' and '---' as separate
	styles from '+' and '-' as they indicate file names in diffs.
	</li>
	<li>
	SciTE error recognizer handles file paths in extra explanatory lines from MSVC
	and in '+++' and '---' lines from diff.
	</li>
	<li>
	Bugs fixed in SciTE and Scintilla folding behaviour when text pasted before
	folded text caused unnecessary
	unfolding and cutting text could lead to text being irretrievably hidden.
	</li>
	<li>
	SciTE on Windows uses correct font for dialogs and better font for tab bar
	allowing better localization
	</li>
	<li>
	When Windows is used with a secondary monitor before the primary
	monitor, autocompletion lists are not forced onto the primary monitor.
	</li>
	<li>
	Scintilla calltip bug fixed where down arrow setting wrong value in notification
	if not in first line. SciTE bug fixed where second arrow only shown on multiple line
	calltip and was therefore misinterpreting the notification value.
	</li>
	<li>
	Lexers will no longer be re-entered recursively during, for example, fold level setting.
	</li>
	<li>
	Undo of typing in overwrite mode undoes one character at a time rather than requiring a removal
	and addition step for each character.
	</li>
	<li>
	EM_EXSETSEL(0,-1) fixed.
	</li>
	<li>
	Bug fixed where part of a rectangular selection was not shown as selected.
	</li>
	<li>
	Autocomplete window size fixed.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite164.zip?download">Release 1.64</a>
    </h3>
    <ul>
	<li>
	Released on 6 June 2005.
	</li>
	<li>
	TADS3 support
	</li>
	<li>
	Smalltalk support.
	</li>
	<li>
	Rebol support.
	</li>
	<li>
	Flagship (Clipper / XBase) support.
	</li>
	<li>
	CSound support.
	</li>
	<li>
	SQL enhanced to support SQL*Plus.
	</li>
	<li>
	SC_MARK_FULLRECT margin marker fills the whole marker margin for marked
	lines with a colour.
	</li>
	<li>
	Performance improved for some large undo and redo operations and modification flags
	added in notifications.
	</li>
	<li>
	SciTE adds command equivalents for fold margin mouse actions.
	</li>
	<li>
	SciTE adds OnUpdateUI to set of events that can be handled by a Lua script.
	</li>
	<li>
	Properties set in Scintilla can be read.
	</li>
	<li>
	GTK+ SciTE exit confirmation adds Cancel button.
	</li>
	<li>
	More accurate lexing of numbers in PHP and Caml.
	</li>
	<li>
	Perl can fold POD and package sections. POD verbatim section style.
	Globbing syntax recognized better.
	</li>
	<li>
	Context menu moved slightly on GTK+ so that it will be under the mouse and will
	stay open if just clicked rather than held.
	</li>
	<li>
	Rectangular selection paste works the same whichever direction the selection was dragged in.
	</li>
	<li>
	EncodedFromUTF8 handles -1 length argument as documented.
	</li>
	<li>
	Undo and redo can cause SCN_MODIFYATTEMPTRO notifications.
	</li>
	<li>
	Indicators display correctly when they start at the second character on a line.
	</li>
	<li>
	SciTE Export As HTML uses standards compliant CSS.
	</li>
	<li>
	SciTE automatic indentation handles keywords for indentation better.
	</li>
	<li>
	SciTE fold.comment.python property removed as does not work.
	</li>
	<li>
	Fixed problem with character set conversion when pasting on GTK+.
	</li>
	<li>
	SciTE default character set changed from ANSI_CHARSET to DEFAULT_CHARSET.
	</li>
	<li>
	Fixed crash when creating empty autocompletion list.
	</li>
	<li>
	Autocomplete window size made larger under some conditions to make truncation less common.
	</li>
	<li>
	Bug fixed where changing case of a selection did not affect initial character of lines
	in multi-byte encodings.
	</li>
	<li>
	Bug fixed where rectangular selection not displayed after Alt+Shift+Click.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite163.zip?download">Release 1.63</a>
    </h3>
    <ul>
	<li>
	Released on 4 April 2005.
	</li>
	<li>
	Autocompletion on Windows changed to use pop up window, be faster,
	allow choice of maximum width and height, and to highlight only the text of the
	selected item rather than both the text and icon if any.
	</li>
	<li>
	Extra items can be added to the context menu in SciTE.
	</li>
	<li>
	Character wrap mode in Scintilla helps East Asian languages.
	</li>
	<li>
	Lexer added for Haskell.
	</li>
	<li>
	Objective Caml support.
	</li>
	<li>
	BlitzBasic and PureBasic support.
	</li>
	<li>
	CSS support updated to handle CSS2.
	</li>
	<li>
	C++ lexer is more selective about document comment keywords.
	</li>
	<li>
	AutoIt 3 lexer improved.
	</li>
	<li>
	Lua lexer styles end of line characters on comment and preprocessor
	lines so that the eolfilled style can be applied to them.
	</li>
	<li>
	NSIS support updated for line continuations, box comments, SectionGroup and
	PageEx, and with more up-to-date properties.
	</li>
	<li>
	Clarion lexer updated to perform folding and have more styles.
	</li>
	<li>
	SQL lexer gains second set of keywords.
	</li>
	<li>
	Errorlist lexer recognizes Borland Delphi error messages.
	</li>
	<li>
	Method added for determining number of visual lines occupied by a document
	line due to wrapping.
	</li>
	<li>
	Sticky caret mode does not modify the preferred caret x position when typing
	and may be useful for typing columns of text.
	</li>
	<li>
	Dwell end notification sent when scroll occurs.
	</li>
	<li>
	On GTK+, Scintilla requisition height is screen height rather than large fixed value.
	</li>
	<li>
	Case insensitive autocompletion prefers exact case match.
	</li>
	<li>
	SCI_PARADOWN and SCI_PARAUP treat lines containing only white
	space as empty and handle text hidden by folding.
	</li>
	<li>
	Scintilla on Windows supports WM_PRINTCLIENT although there are some
	limitations.
	</li>
	<li>
	SCN_AUTOCSELECTION notification sent when user selects from autoselection list.
	</li>
	<li>
	SciTE's standard properties file sets buffers to 10, uses Pango fonts on GTK+ and
	has dropped several languages to make the menu fit on screen.
	</li>
	<li>
	SciTE's encoding cookie detection loosened so that common XML files will load
	in UTF-8 if that is their declared encoding.
	</li>
	<li>
	SciTE on GTK+ changes menus and toolbars to not be detachable unless turned
	on with a property. Menus no longer tear off. The toolbar may be set to use the
	default theme icons rather than SciTE's set. Changed key for View | End of Line
	because of a conflict. Language menu can contain more items.
	</li>
	<li>
	SciTE on GTK+ 2.x allows the height and width of the file open file chooser to
	be set, for the show hidden files check box to be set from an option and for it
	to be opened in the directory of the current file explicitly. Enter key works in
	save chooser.
	</li>
	<li>
	Scintilla lexers should no longer see bits in style bytes that are outside the set
	they modify so should be able to correctly lex documents where the container
	has used indicators.
	</li>
	<li>
	SciTE no longer asks to save before performing a revert.
	</li>
	<li>
	SciTE director interface adds a reloadproperties command to reload properties
	from files.
	</li>
	<li>
	Allow build on CYGWIN platform.
	</li>
	<li>
	Allow use from LccWin compiler.
	</li>
	<li>
	SCI_COLOURISE for SCLEX_CONTAINER causes a
	SCN_STYLENEEDED notification.
	</li>
	<li>
	Bugs fixed in lexing of HTML/ASP/JScript.
	</li>
	<li>
	Fix for folding becoming confused.
	</li>
	<li>
	On Windows, fixes for Japanese Input Method Editor and for 8 bit Katakana
	characters.
	</li>
	<li>
	Fixed buffer size bug avoided when typing long words by making buffer bigger.
	</li>
	<li>
	Undo after automatic indentation more sensible.
	</li>
	<li>
	SciTE menus on GTK+ uses Shift and Ctrl rather than old style abbreviations.
	</li>
	<li>
	SciTE full screen mode on Windows calculates size more correctly.
	</li>
	<li>
	SciTE on Windows menus work better with skinning applications.
	</li>
	<li>
	Searching bugs fixed.
	</li>
	<li>
	Colours reallocated when changing image using SCI_REGISTERIMAGE.
	</li>
	<li>
	Caret stays visible when Enter held down.
	</li>
	<li>
	Undo of automatic indentation more reasonable.
	</li>
	<li>
	High processor usage fixed in background wrapping under some
	circumstances.
	</li>
	<li>
	Crashing bug fixed on AMD64.
	</li>
	<li>
	SciTE crashing bug fixed when position.height or position.width not set.
	</li>
	<li>
	Crashing bug on GTK+ fixed when setting cursor and window is NULL.
	</li>
	<li>
	Crashing bug on GTK+ preedit window fixed.
	</li>
	<li>
	SciTE crashing bug fixed in incremental search on Windows ME.
	</li>
	<li>
	SciTE on Windows has a optional find and replace dialogs that can search through
	all buffers and search within a particular style number.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite162.zip?download">Release 1.62</a>
    </h3>
    <ul>
	<li>
	Released on 31 October 2004.
	</li>
	<li>
	Lexer added for ASN.1.
	</li>
	<li>
	Lexer added for VHDL.
	</li>
	<li>
	On Windows, an invisible system caret is used to allow screen readers to determine
	where the caret is. The visible caret is still drawn by the painting code.
	</li>
	<li>
	On GTK+, Scintilla has methods to read the target as UTF-8 and to convert
	a string from UTF-8 to the document encoding. This eases integration with
	containers that use the UTF-8 encoding which is the API encoding for GTK+ 2.
	</li>
	<li>
	SciTE on GTK+2 and Windows NT/2000/XP allows search and replace of Unicode text.
	</li>
	<li>
	SciTE calltips allow setting the characters used to start and end parameter lists and
	to separate parameters.
	</li>
	<li>
	FindColumn method converts a line and column into a position, taking into account
	tabs and multi-byte characters.
	</li>
	<li>
	On Windows, when Scintilla copies text to the clipboard as Unicode, it avoids
	adding an ANSI copy as the system will automatically convert as required in
	a context-sensitive manner.
	</li>
	<li>
	SciTE indent.auto setting automatically determines indent.size and use.tabs from
	document contents.
	</li>
	<li>
	SciTE defines a CurrentMessage property that holds the most recently selected
	output pane message.
	</li>
	<li>
	SciTE Lua scripting enhanced with
	<ul>
	<li>A Lua table called 'buffer' is associated with each buffer and can be used to
	maintain buffer-specific state.</li>
	<li>A 'scite' object allows interaction with the application such as opening
	files from script.</li>
	<li>Dynamic properties can be reset by assigning nil to a given key in
	the props table.</li>
	<li>An 'OnClear' event fires whenever properties and extension scripts are
	about to be reloaded.</li>
	<li>On Windows, loadlib is enabled and can be used to access Lua
	binary modules / DLLs.</li></ul>
	</li>
	<li>
	SciTE Find in Files on Windows can be used in a modeless way and gains a '..'
	button to move up to the parent directory. It is also wider so that longer paths
	can be seen.
	</li>
	<li>
	Close buttons added to dialogs in SciTE on Windows.
	</li>
	<li>
	SciTE on GTK+ 2 has a "hidden files" check box in file open dialog.
	</li>
	<li>
	SciTE use.monospaced setting removed. More information in the
	<a href="SciTEFAQ.html">FAQ</a>.
	</li>
	<li>
	APDL lexer updated with more lexical classes
	</li>
	<li>
	AutoIt3 lexer updated.
	</li>
	<li>
	Ada lexer fixed to support non-ASCII text.
	</li>
	<li>
	Cpp lexer now only matches exactly three slashes as starting a doc-comment so that
	lines of slashes are seen as a normal comment.
	Line ending characters are appear in default style on preprocessor and single line
	comment lines.
	</li>
	<li>
	CSS lexer updated to support CSS2 including second set of keywords.
	</li>
	<li>
	Errorlist lexer now understands Java stack trace lines.
	</li>
	<li>
	SciTE's handling of HTML Tidy messages jumps to column as well as line indicated.
	</li>
	<li>
	Lisp lexer allows multiline strings.
	</li>
	<li>
	Lua lexer treats .. as an operator when between identifiers.
	</li>
	<li>
	PHP lexer handles 'e' in numerical literals.
	</li>
	<li>
	PowerBasic lexer updated for macros and optimized.
	</li>
	<li>
	Properties file folder changed to leave lines before a header at the base level
	and thus avoid a vertical line when using connected folding symbols.
	</li>
	<li>
	GTK+ on Windows version uses Alt for rectangular selection to be compatible with
	platform convention.
	</li>
	<li>
	SciTE abbreviations file moved from system directory to user directory
	so each user can have separate abbreviations.
	</li>
	<li>
	SciTE on GTK+ has improved .desktop file and make install support that may
	lead to better integration with system shell.
	</li>
	<li>
	Disabling of themed background drawing on GTK+ extended to all cases.
	</li>
	<li>
	SciTE date formatting on Windows performed with the user setting rather than the
	system setting.
	</li>
	<li>
	GTK+ 2 redraw while scrolling fixed.
	</li>
	<li>
	Recursive property definitions are safer, avoiding expansion when detected.
	</li>
	<li>
	SciTE thread synchronization for scripts no longer uses HWND_MESSAGE
	so is compatible with older versions of Windows.
	Other Lua scripting bugs fixed.
	</li>
	<li>
	SciTE on Windows localization of menu accelerators changed to be compatible
	with alternative UI themes.
	</li>
	<li>
	SciTE on Windows full screen mode now fits better when menu different height
	to title bar height.
	</li>
	<li>
	SC_MARK_EMPTY marker is now invisible and does not change the background
	colour.
	</li>
	<li>
	Bug fixed in HTML lexer to allow use of &lt;?xml in strings in scripts without
	triggering xml mode.
	</li>
	<li>
	Bug fixed in SciTE abbreviation expansion that could break indentation or crash.
	</li>
	<li>
	Bug fixed when searching for a whole word string that ends one character before
	end of document.
	</li>
	<li>
	Drawing bug fixed when indicators drawn on wrapped lines.
	</li>
	<li>
	Bug fixed when double clicking a hotspot.
	</li>
	<li>
	Bug fixed where autocompletion would remove typed text if no match found.
	</li>
	<li>
	Bug fixed where display does not scroll when inserting in long wrapped line.
	</li>
	<li>
	Bug fixed where SCI_MARKERDELETEALL would only remove one of the markers
	on a line that contained multiple markers with the same number.
	</li>
	<li>
	Bug fixed where markers would move when converting line endings.
	</li>
	<li>
	Bug fixed where SCI_LINEENDWRAP would move too far when line ends are visible.
	</li>
	<li>
	Bugs fixed where calltips with unicode or other non-ASCII text would display
	incorrectly.
	</li>
	<li>
	Bug fixed in determining if at save point after undoing from save point and then
	performing changes.
	</li>
	<li>
	Bug fixed on GTK+ using unsupported code pages where extraneous text could
	be drawn.
	</li>
	<li>
	Bug fixed in drag and drop code on Windows where dragging from SciTE to
	Firefox could hang both applications.
	</li>
	<li>
	Crashing bug fixed on GTK+ when no font allocation succeeds.
	</li>
	<li>
	Crashing bug fixed when autocompleting word longer than 1000 characters.
	</li>
	<li>
	SciTE crashing bug fixed when both Find and Replace dialogs shown by disallowing
	this situation.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite161.zip?download">Release 1.61</a>
    </h3>
    <ul>
	<li>
	Released on 29 May 2004.
	</li>
	<li>
	Improvements to selection handling on GTK+.
	</li>
	<li>
	SciTE on GTK+ 2.4 uses the improved file chooser which allows
	file extension filters, multiple selection, and remembers favourite
	directories.
	</li>
	<li>
	SciTE Load Session and Save Session commands available on GTK+.
	</li>
	<li>
	SciTE lists Lua Startup Script in Options menu when loaded.
	</li>
	<li>
	In SciTE, OnUserListSelection can be implemented in Lua.
	</li>
	<li>
	SciTE on Windows has a context menu on the file tabs.
	</li>
	<li>
	SQL lexer allows '#' comments and optionally '\' quoting inside strings.
	</li>
	<li>
	Mssql lexer improved.
	</li>
	<li>
	AutoIt3 lexer updated.
	</li>
	<li>
	Perl lexer recognizes regular expression use better.
	</li>
	<li>
	Errorlist lexer understands Lua tracebacks and copes with findstr
	output for file names that end with digits.
	</li>
	<li>
	Drawing of lines on GTK+ improved and made more like Windows
	without final point.
	</li>
	<li>
	SciTE on GTK+ uses a high resolution window icon.
	</li>
	<li>
	SciTE can be set to warn before loading files larger than a particular size.
	</li>
	<li>
	SciTE Lua scripting bugs fixed included a crashing bug when using
	an undefined function name that would go before first actual name.
	</li>
	<li>
	SciTE bug fixed where a modified buffer was not saved if it was
	the last buffer and was not current when the New command used.
	</li>
	<li>
	SciTE monofont mode no longer affects line numbers.
	</li>
	<li>
	Crashing bug in SciTE avoided by not allowing both the Find and Replace
	dialogs to be visible at one time.
	</li>
	<li>
	Crashing bug in SciTE fixed when Lua scripts were being run
	concurrently.
	</li>
	<li>
	Bug fixed that caused incorrect line number width in SciTE.
	</li>
	<li>
	PHP folding bug fixed.
	</li>
	<li>
	Regression fixed when setting word characters to not include
	some of the standard word characters.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite160.zip?download">Release 1.60</a>
    </h3>
    <ul>
	<li>
	Released on 1 May 2004.
	</li>
	<li>
	SciTE can be scripted using the Lua programming language.
	</li>
	<li>
	command.mode is a better way to specify tool command options in SciTE.
	</li>
	<li>
	Continuation markers can be displayed so that you can see which lines are wrapped.
	</li>
	<li>
	Lexer for Gui4Cli language.
	</li>
	<li>
	Lexer for Kix language.
	</li>
	<li>
	Lexer for Specman E language.
	</li>
	<li>
	Lexer for AutoIt3 language.
	</li>
	<li>
	Lexer for APDL language.
	</li>
	<li>
	Lexer for Bash language. Also reasonable for other Unix shells.
	</li>
	<li>
	SciTE can load lexers implemented in external shared libraries.
	</li>
	<li>
	Perl treats "." not as part of an identifier and interprets '/' and '->'
	correctly in more circumstances.
	</li>
	<li>
	PHP recognizes variables within strings.
	</li>
	<li>
	NSIS has properties "nsis.uservars" and "nsis.ignorecase".
	</li>
	<li>
	MSSQL lexer adds keyword list for operators and stored procedures,
	defines '(', ')', and ',' as operators and changes some other details.
	</li>
	<li>
	Input method preedit window on GTK+ 2 may support some Asian languages.
	</li>
	<li>
	Platform interface adds an extra platform-specific flag to Font::Create.
	Used on wxWidgets to choose antialiased text display but may be used for
	any task that a platform needs.
	</li>
	<li>
	OnBeforeSave method added to Extension interface.
	</li>
	<li>
	Scintilla methods that return strings can be called with a NULL pointer
	to find out how long the string should be.
	</li>
	<li>
	Visual Studio .NET project file now in VS .NET 2003 format so can not be used
	directly in VS .NET 2002.
	</li>
	<li>
	Scintilla can be built with GTK+ 2 on Windows.
	</li>
	<li>
	Updated RPM spec for SciTE on GTK+.
	</li>
	<li>
	GTK+ makefile for SciTE allows selection of destination directory, creates destination
	directories and sets file modes and owners better.
	</li>
	<li>
	Tab indents now go to next tab multiple rather than add tab size.
	</li>
	<li>
	SciTE abbreviations now use the longest possible match rather than the shortest.
	</li>
	<li>
	Autocompletion does not remove prefix when actioned with no choice selected.
	</li>
	<li>
	Autocompletion cancels when moving beyond the start position, not at the start position.
	</li>
	<li>
	SciTE now shows only calltips for functions that match exactly, not
	those that match as a prefix.
	</li>
	<li>
	SciTE can repair box comment sections where some lines were added without
	the box comment middle line prefix.
	</li>
	<li>
	Alt+ works in user.shortcuts on Windows.
	</li>
	<li>
	SciTE on GTK+ enables replace in selection for rectangular selections.
	</li>
	<li>
	Key bindings for command.shortcut implemented in a way that doesn't break
	when the menus are localized.
	</li>
	<li>
	Drawing of background on GTK+ faster as theme drawing disabled.
	</li>
	<li>
	On GTK+, calltips are moved back onto the screen if they extend beyond the screen bounds.
	</li>
	<li>
	On Windows, the Scintilla object is destroyed on WM_NCDESTROY rather than
	WM_DESTROY which arrives earlier. This fixes some problems when Scintilla was subclassed.
	</li>
	<li>
	The zorder switching feature removed due to number of crashing bugs.
	</li>
	<li>
	Code for XPM images made more robust.
	</li>
	<li>
	Bug fixed with primary selection on GTK+.
	</li>
	<li>
	On GTK+ 2, copied or cut text can still be pasted after the Scintilla widget is destroyed.
	</li>
	<li>
	Styling change not visible problem fixed when line was cached.
	</li>
	<li>
	Bug in SciTE on Windows fixed where clipboard commands stopped working.
	</li>
	<li>
	Crashing bugs in display fixed in line layout cache.
	</li>
	<li>
	Crashing bug may be fixed on AMD64 processor on GTK+.
	</li>
	<li>
	Rare hanging crash fixed in Python lexer.
	</li>
	<li>
	Display bugs fixed with DBCS characters on GTK+.
	</li>
	<li>
	Autocompletion lists on GTK+ 2 are not sorted by the ListModel as the
	contents are sorted correctly by Scintilla.
	</li>
	<li>
	SciTE fixed to not open extra untitled buffers with check.if.already.open.
	</li>
	<li>
	Sizing bug fixed on GTK+ when window resized while unmapped.
	</li>
	<li>
	Text drawing crashing bug fixed on GTK+ with non-Pango fonts and long strings.
	</li>
	<li>
	Fixed some issues if characters are unsigned.
	</li>
	<li>
	Fixes in NSIS support.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite159.zip?download">Release 1.59</a>
    </h3>
    <ul>
	<li>
	Released on 19 February 2004.
	</li>
	<li>
	SciTE Options and Language menus reduced in length by commenting
	out some languages. Languages can be enabled by editing the global
	properties file.
	</li>
	<li>
	Verilog language supported.
	</li>
	<li>
	Lexer for Microsoft dialect of SQL. SciTE properties file available from extras page.
	</li>
	<li>
	Perl lexer disambiguates '/' better.
	</li>
	<li>
	NSIS lexer improved with a lexical class for numbers, option for ignoring case
	of keywords, and folds only occurring when folding keyword first on line.
	</li>
	<li>
	PowerBasic lexer improved with styles for constants and assembler and
	folding improvements.
	</li>
	<li>
	On GTK+, input method support only invoked for Asian languages and not
	European languages as the old European keyboard code works better.
	</li>
	<li>
	Scintilla can be requested to allocate a certain amount and so avoid repeated
	reallocations and memory inefficiencies. SciTE uses this and so should require
	less memory.
	</li>
	<li>
	SciTE's "toggle current fold" works when invoked on child line as well as
	fold header.
	</li>
	<li>
	SciTE output pane scrolling can be set to not scroll back to start after
	completion of command.
	</li>
	<li>
	SciTE has a $(SessionPath) property.
	</li>
	<li>
	SciTE on Windows can use VK_* codes for keys in user.shortcuts.
	</li>
	<li>
	Stack overwrite bug fixed in SciTE's command to move to the end of a
	preprocessor conditional.
	</li>
	<li>
	Bug fixed where vertical selection appeared to select a different set of characters
	then would be used by, for example, a copy.
	</li>
	<li>
	SciTE memory leak fixed in fold state remembering.
	</li>
	<li>
	Bug fixed where changing the style of some text outside the
	standard StyleNeeded notification would not be visible.
	</li>
	<li>
	On GTK+ 2 g_iconv is used in preference to iconv, as it is provided by GTK+
	so should avoid problems finding the iconv library.
	</li>
	<li>
	On GTK+ fixed a style reference count bug.
	</li>
	<li>
	Memory corruption bug fixed with GetSelText.
	</li>
	<li>
	On Windows Scintilla deletes memory on WM_NCDESTROY rather than
	the earlier WM_DESTROY to avoid problems when the window is subclassed.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite158.zip?download">Release 1.58</a>
    </h3>
    <ul>
	<li>
	Released on 11 January 2004.
	</li>
	<li>
	Method to discover the currently highlighted element in an autocompletion list.
	</li>
	<li>
	On GTK+, the lexers are now included in the scintilla.a library file. This
	will require changes to the make files of dependent projects.
	</li>
	<li>
	Octave support added alongside related Matlab language and Matlab support improved.
	</li>
	<li>
	VB lexer gains an unterminated string state and 4 sets of keywords.
	</li>
	<li>
	Ruby lexer handles $' correctly.
	</li>
	<li>
	Error line handling improved for FORTRAN compilers from Absoft and Intel.
	</li>
	<li>
	International input enabled on GTK+ 2 although there is no way to choose an
	input method.
	</li>
	<li>
	MultiplexExtension in SciTE allows multiple extensions to be used at once.
	</li>
	<li>
	Regular expression replace interprets backslash expressions \a, \b, \f, \n, \r, \t,
	and \v in the replacement value.
	</li>
	<li>
	SciTE Replace dialog displays number of replacements made when Replace All or
	Replace in Selection performed.
	</li>
	<li>
	Localization files may contain a translation.encoding setting which is used
	on GTK+ 2 to automatically reencode the translation to UTF-8 so it will be
	the localized text will be displayed correctly.
	</li>
	<li>
	SciTE on GTK+ implements check.if.already.open.
	</li>
	<li>
	Make files for Mac OS X made more robust.
	</li>
	<li>
	Performance improved in SciTE when switching buffers when there
	is a rectangular selection.
	</li>
	<li>
	Fixed failure to display some text when wrapped.
	</li>
	<li>
	SciTE crashes from Ctrl+Tab buffer cycling fixed.
	May still be some rare bugs here.
	</li>
	<li>
	Crash fixed when decoding an error message that appears similar to a
	Borland error message.
	</li>
	<li>
	Fix to auto-scrolling allows containers to implement enhanced double click selection.
	</li>
	<li>
	Hang fixed in idle word wrap.
	</li>
	<li>
	Crash fixed in hotspot display code..
	</li>
	<li>
	SciTE on Windows Incremental Search no longer moves caret back.
	</li>
	<li>
	SciTE hang fixed when performing a replace with a find string that
	matched zero length strings such as ".*".
	</li>
	<li>
	SciTE no longer styles the whole file when saving buffer fold state
	as that was slow.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite157.zip?download">Release 1.57</a>
    </h3>
    <ul>
	<li>
	Released on 27 November 2003.
	</li>
	<li>
	SciTE remembers folding of each buffer.
	</li>
	<li>
	Lexer for Erlang language.
	</li>
	<li>
	Scintilla allows setting the set of white space characters.
	</li>
	<li>
	Scintilla has 'stuttered' page movement commands to first move
	to top or bottom within current visible lines before scrolling.
	</li>
	<li>
	Scintilla commands for moving to end of words.
	</li>
	<li>
	Incremental line wrap enabled on Windows.
	</li>
	<li>
	SciTE PDF exporter produces output that is more compliant with reader
	applications, is smaller and allows more configuration.
	HTML exporter optimizes size of output files.
	</li>
	<li>
	SciTE defines properties PLAT_WINNT and PLAT_WIN95 on the
	corresponding platforms.
	</li>
	<li>
	SciTE can adjust the line margin width to fit the largest line number.
	The line.numbers property is split between line.margin.visible and
	line.margin.width.
	</li>
	<li>
	SciTE on GTK+ allows user defined menu accelerators.
	Alt can be included in user.shortcuts.
	</li>
	<li>
	SciTE Language menu can have items commented out.
	</li>
	<li>
	SciTE on Windows Go to dialog allows choosing a column number as
	well as a line number.
	</li>
	<li>
	SciTE on GTK+ make file uses prefix setting more consistently.
	</li>
	<li>
	Bug fixed that caused word wrapping to fail to display all text.
	</li>
	<li>
	Crashing bug fixed in GTK+ version of Scintilla when using GDK fonts
	and opening autocompletion.
	</li>
	<li>
	Bug fixed in Scintilla SCI_GETSELTEXT where an extra NUL
	was included at end of returned string
	</li>
	<li>
	Crashing bug fixed in SciTE z-order switching implementation.
	</li>
	<li>
	Hanging bug fixed in Perl lexer.
	</li>
	<li>
	SciTE crashing bug fixed for using 'case' without argument in style definition.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite156.zip?download">Release 1.56</a>
    </h3>
    <ul>
	<li>
	Released on 25 October 2003.
	</li>
	<li>
	Rectangular selection can be performed using the keyboard.
	Greater programmatic control over rectangular selection.
	This has caused several changes to key bindings.
	</li>
	<li>
	SciTE Replace In Selection works on rectangular selections.
	</li>
	<li>
	Improved lexer for TeX, new lexer for Metapost and other support for these
	languages.
	</li>
	<li>
	Lexer for PowerBasic.
	</li>
	<li>
	Lexer for Forth.
	</li>
	<li>
	YAML lexer improved to include error styling.
	</li>
	<li>
	Perl lexer improved to correctly handle more cases.
	</li>
	<li>
	Assembler lexer updated to support single-quote strings and fix some
	problems.
	</li>
	<li>
	SciTE on Windows can switch between buffers in order of use (z-order) rather
	than static order.
	</li>
	<li>
	SciTE supports adding an extension for "Open Selected Filename".
	The openpath setting works on GTK+.
	</li>
	<li>
	SciTE can Export as XML.
	</li>
	<li>
	SciTE $(SelHeight) variable gives a more natural result for empty and whole line
	selections.
	</li>
	<li>
	Fixes to wrapping problems, such as only first display line being visible in some
	cases.
	</li>
	<li>
	Fixes to hotspot to only highlight when over the hotspot, only use background
	colour when set and option to limit hotspots to a single line.
	</li>
	<li>
	Small fixes to FORTRAN lexing and folding.
	</li>
	<li>
	SQL lexer treats single quote strings as a separate class to double quote strings..
	</li>
	<li>
	Scintilla made compatible with expectations of container widget in GTK+ 2.3.
	</li>
	<li>
	Fix to strip out pixmap ID when automatically choosing from an autocompletion
	list with only one element.
	</li>
	<li>
	SciTE bug fixed where UTF-8 files longer than 128K were gaining more than one
	BOM.
	</li>
	<li>
	Crashing bug fixed in SciTE on GTK+ where using "Stop Executing" twice leads
	to all applications exiting.
	</li>
	<li>
	Bug fixed in autocompletion scrolling on GTK+ 2 with a case sensitive list.
	The ListBox::Sort method is no longer needed or available so platform
	maintainers should remove it.
	</li>
	<li>
	SciTE check.if.already.open setting removed from GTK+ version as unmaintained.
	</li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite155.zip?download">Release 1.55</a>
    </h3>
    <ul>
      <li>
	Released on 25 September 2003.
      </li>
      <li>
	Fix a crashing bug in indicator display in Scintilla.
      </li>
      <li>
	GTK+ version now defaults to building for GTK+ 2 rather than 1.
      </li>
      <li>
	Mingw make file detects compiler version and avoids options
	that are cause problems for some versions.
      </li>
      <li>
	Large performance improvement on GTK+ 2 for long lines.
      </li>
      <li>
	Incremental line wrap on GTK+.
      </li>
      <li>
	International text entry works much better on GTK+ with particular
	improvements for Baltic languages and languages that use 'dead' accents.
	NUL key events such as those generated by some function keys, ignored.
      </li>
      <li>
	Unicode clipboard support on GTK+.
      </li>
      <li>
	Indicator type INDIC_BOX draws a rectangle around the text.
      </li>
      <li>
	Clarion language support.
      </li>
      <li>
	YAML language support.
      </li>
      <li>
	MPT LOG language support.
      </li>
      <li>
	On Windows, SciTE can switch buffers based on activation order rather
	than buffer number.
      </li>
      <li>
	SciTE save.on.deactivate saves all buffers rather than just the current buffer.
      </li>
      <li>
	Lua lexer handles non-ASCII characters correctly.
      </li>
      <li>
	Error lexer understands Borland errors with pathnames that contain space.
      </li>
      <li>
	On GTK+ 2, autocompletion uses TreeView rather than deprecated CList.
      </li>
      <li>
	SciTE autocompletion removed when expand abbreviation command used.
      </li>
      <li>
	SciTE calltips support overloaded functions.
      </li>
      <li>
	When Save fails in SciTE, choice offered to Save As.
      </li>
      <li>
	SciTE message boxes on Windows may be moved to front when needed.
      </li>
      <li>
	Indicators drawn correctly on wrapped lines.
      </li>
      <li>
	Regular expression search no longer matches characters with high bit
	set to characters without high bit set.
      </li>
      <li>
	Hang fixed in backwards search in multi byte character documents.
      </li>
      <li>
	Hang fixed in SciTE Mark All command when wrap around turned off.
      </li>
      <li>
	SciTE Incremental Search no longer uses hot keys on Windows.
      </li>
      <li>
	Calltips draw non-ASCII characters correctly rather than as arrows.
      </li>
      <li>
	SciTE crash fixed when going to an error message with empty file name.
      </li>
      <li>
	Bugs fixed in XPM image handling code.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite154.zip?download">Release 1.54</a>
    </h3>
    <ul>
      <li>
	Released on 12 August 2003.
      </li>
      <li>
	SciTE on GTK+ 2.x can display a tab bar.
      </li>
      <li>
	SciTE on Windows provides incremental search.
      </li>
      <li>
	Lexer for PostScript.
      </li>
      <li>
	Lexer for the NSIS scripting language.
      </li>
      <li>
	New lexer for POV-Ray Scene Description Language
	replaces previous implementation.
      </li>
      <li>
	Lexer for the MMIX Assembler language.
      </li>
      <li>
	Lexer for the Scriptol language.
      </li>
      <li>
	Incompatibility: SQL keywords are specified in lower case rather than upper case.
	SQL lexer allows double quoted strings.
      </li>
      <li>
	Pascal lexer: character constants that start with '#' understood,
	'@' only allowed within assembler blocks,
	'$' can be the start of a number,
	initial '.' in 0..constant not treated as part of a number,
	and assembler blocks made more distinctive.
      </li>
      <li>
	Lua lexer allows '.' in keywords.
	Multi-line strings and comments can be folded.
      </li>
      <li>
	CSS lexer handles multiple psuedoclasses.
      </li>
      <li>
	Properties file folder works for INI file format.
      </li>
      <li>
	Hidden indicator style allows the container to mark text within Scintilla
	without there being any visual effect.
      </li>
      <li>
	SciTE does not prompt to save changes when the buffer is empty and untitled.
      </li>
      <li>
	Modification notifications caused by SCI_INSERTSTYLEDSTRING
	now include the contents of the insertion.
      </li>
      <li>
	SCI_MARKERDELETEALL deletes all the markers on a line
	rather than just the first match.
      </li>
      <li>
	Better handling of 'dead' accents on GTK+ 2 for languages
	that use accented characters.
      </li>
      <li>
	SciTE now uses value of output.vertical.size property.
      </li>
      <li>
	Crash fixed in SciTE autocompletion on long lines.
      </li>
      <li>
	Crash fixed in SciTE comment command on long lines.
      </li>
      <li>
	Bug fixed with backwards regular expression search skipping
	every second match.
      </li>
      <li>
	Hang fixed with regular expression replace where both target and replacement were empty.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite153.zip?download">Release 1.53</a>
    </h3>
    <ul>
      <li>
	Released on 16 May 2003.
      </li>
      <li>
	On GTK+ 2, encodings other than ASCII, Latin1, and Unicode are
	supported for both display and input using iconv.
      </li>
      <li>
	External lexers supported on GTK+/Linux.
	External lexers must now be explicitly loaded with SCI_LOADLEXERLIBRARY
	rather than relying upon a naming convention and automatic loading.
      </li>
      <li>
	Support of Lout typesetting language.
      </li>
      <li>
	Support of E-Scripts language used in the POL Ultima Online Emulator.
      </li>
      <li>
	Scrolling and drawing performance on GTK+ enhanced, particularly for GTK+ 2.x
	with an extra window for the text area avoiding conflicts with the scroll bars.
      </li>
      <li>
	CopyText and CopyRange methods in Scintilla allow container to
	easily copy to the system clipboard.
      </li>
      <li>
	Line Copy command implemented and bound to Ctrl+Shift+T.
      </li>
      <li>
	Scintilla APIs PositionBefore and PositionAfter can be used to iterate through
	a document taking into account the encoding and multi-byte characters.
      </li>
      <li>
	C++ folder can fold on the "} else {" line of an if statement by setting
	fold.at.else property to 1.
      </li>
      <li>
	C++ lexer allows an extra set of keywords.
      </li>
      <li>
	Property names and thus abbreviations may be non-ASCII.
      </li>
      <li>
	Removed attempt to load a file when setting properties that was
	part of an old scripting experiment.
      </li>
      <li>
	SciTE no longer warns about a file not existing when opening
	properties files from the Options menu as there is a good chance
	the user wants to create one.
      </li>
      <li>
	Bug fixed with brace recognition in multi-byte encoded files where a partial
	character matched a brace byte.
      </li>
      <li>
	More protection against infinite loops or recursion with recursive property definitions.
      </li>
      <li>
	On Windows, cursor will no longer disappear over margins in custom builds when
	cursor resource not present. The Windows default cursor is displayed instead.
      </li>
      <li>
	load.on.activate fixed in SciTE as was broken in 1.52.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite152.zip?download">Release 1.52</a>
    </h3>
    <ul>
      <li>
	Released on 17 April 2003.
      </li>
      <li>
	Pango font support on GTK+ 2.
	Unicode input improved on GTK+ 2.
      </li>
      <li>
	Hotspot style implemented in Scintilla.
      </li>
      <li>
	Small up and down arrows can be displayed in calltips and the container
	is notified when the mouse is clicked on a calltip.
	Normal and selected calltip text colours can be set.
      </li>
      <li>
	POSIX compatibility flag in Scintilla regular expression search
	interprets bare ( and ) as tagged sections.
      </li>
      <li>
	Error message lexer tightened to yield fewer false matches.
	Recognition of Lahey and Intel FORTRAN error formats.
      </li>
      <li>
	Scintilla keyboard commands for moving to start and end of
	screen lines rather than document lines, unless already there
	where these keys move to the start or end of the document line.
      </li>
      <li>
	Line joining command.
      </li>
      <li>
	Lexer for POV-Ray.
      </li>
      <li>
	Calltips on Windows are no longer clipped by the parent window.
      </li>
      <li>
	Autocompletion lists are cancelled when focus leaves their parent window.
      </li>
      <li>
	Move to next/previous empty line delimited paragraph key commands.
      </li>
      <li>
	SciTE hang fixed with recursive property definitions by placing limit
	on number of substitutions performed.
      </li>
      <li>
	SciTE Export as PDF reenabled and works.
      </li>
      <li>
	Added loadsession: command line command to SciTE.
      </li>
      <li>
	SciTE option to quit application when last document closed.
      </li>
      <li>
	SciTE option to ask user if it is OK to reload a file that has been
	modified outside SciTE.
      </li>
      <li>
	SciTE option to automatically save before running particular command tools
	or to ask user or to not save.
      </li>
      <li>
	SciTE on Windows 9x will write a Ctrl+Z to the process input pipe before
	closing the pipe when running tool commands that take input.
      </li>
      <li>
	Added a manifest resource to SciTE on Windows to enable Windows XP
	themed UI.
      </li>
      <li>
	SciTE calltips handle nested calls and other situations better.
      </li>
      <li>
	CSS lexer improved.
      </li>
      <li>
	Interface to platform layer changed - Surface initialization now requires
	a WindowID parameter.
      </li>
      <li>
	Bug fixed with drawing or measuring long pieces of text on Windows 9x
	by truncating the pieces.
      </li>
      <li>
	Bug fixed with SciTE on GTK+ where a user shortcut for a visible character
	inserted the character as well as executing the command.
      </li>
      <li>
	Bug fixed where primary selection on GTK+ was reset by
	Scintilla during creation.
      </li>
      <li>
	Bug fixed where SciTE would close immediately on startup
	when using save.session.
      </li>
      <li>
	Crash fixed when entering '\' in LaTeX file.
      </li>
      <li>
	Hang fixed when '#' last character in VB file.
      </li>
      <li>
	Crash fixed in error message lexer.
      </li>
      <li>
	Crash fixed when searching for long regular expressions.
      </li>
      <li>
	Pressing return when nothing selected in user list sends notification with
	empty text rather than random text.
      </li>
      <li>
	Mouse debouncing disabled on Windows as it interfered with some
	mouse utilities.
      </li>
      <li>
	Bug fixed where overstrike mode inserted before rather than replaced last
	character in document.
      </li>
      <li>
	Bug fixed with syntax highlighting of Japanese text.
      </li>
      <li>
	Bug fixed in split lines function.
      </li>
      <li>
	Cosmetic fix to SciTE tab bar on Windows when window resized.
	Focus sticks to either pane more consistently.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite151.zip?download">Release 1.51</a>
    </h3>
    <ul>
      <li>
	Released on 16 February 2003.
      </li>
      <li>
	Two phase drawing avoids cutting off text that overlaps runs by drawing
	all the backgrounds of a line then drawing all the text transparently.
	Single phase drawing is an option.
      </li>
      <li>
	Scintilla method to split lines at a particular width by adding new line
	characters.
      </li>
      <li>
	The character used in autocompletion lists to separate the text from the image
	number can be changed.
      </li>
      <li>
	The scrollbar range will automatically expand when the caret is moved
	beyond the current range.
	The scroll bar is updated when SCI_SETXOFFSET is called.
      </li>
      <li>
	Mouse cursors on GTK+ improved to be consistent with other applications
	and the Windows version.
      </li>
      <li>
	Horizontal scrollbar on GTK+ now disappears in wrapped mode.
      </li>
      <li>
	Scintilla on GTK+ 2: mouse wheel scrolling, cursor over scrollbars, focus,
	and syntax highlighting now work.
	gtk_selection_notify avoided for compatibility with GTK+ 2.2.
      </li>
      <li>
	Fold margin colours can now be set.
      </li>
      <li>
	SciTE can be built for GTK+ 2.
      </li>
      <li>
	SciTE can optionally preserve the undo history over an automatic file reload.
      </li>
      <li>
	Tags can optionally be case insensitive in XML and HTML.
      </li>
      <li>
	SciTE on Windows handles input to tool commands in a way that should avoid
	deadlock. Output from tools can be used to replace the selection.
      </li>
      <li>
	SciTE on GTK+ automatically substitutes '|' for '/' in menu items as '/'
	is used to define the menu hierarchy.
      </li>
      <li>
	Optional buffer number in SciTE title bar.
      </li>
      <li>
	Crash fixed in SciTE brace matching.
      </li>
      <li>
	Bug fixed where automatic scrolling past end of document
	flipped back to the beginning.
      </li>
      <li>
	Bug fixed where wrapping caused text to disappear.
      </li>
      <li>
	Bug fixed on Windows where images in autocompletion lists were
	shown on the wrong item.
      </li>
      <li>
	Crash fixed due to memory bug in autocompletion lists on Windows.
      </li>
      <li>
	Crash fixed when double clicking some error messages.
      </li>
      <li>
	Bug fixed in word part movement where sometimes no movement would occur.
      </li>
      <li>
	Bug fixed on Windows NT where long text runs were truncated by
	treating NT differently to 9x where there is a limitation.
      </li>
      <li>
	Text in not-changeable style works better but there remain some cases where
	it is still possible to delete text protected this way.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite150.zip?download">Release 1.50</a>
    </h3>
    <ul>
      <li>
	Released on 24 January 2003.
      </li>
      <li>
	Autocompletion lists may have a per-item pixmap.
      </li>
      <li>
	Autocompletion lists allow Unicode text on Windows.
      </li>
      <li>
	Scintilla documentation rewritten.
      </li>
      <li>
	Additional DBCS encoding support in Scintilla on GTK+ primarily aimed at
	Japanese EUC encoding.
      </li>
      <li>
	CSS (Cascading Style Sheets) lexer added.
      </li>
      <li>
	diff lexer understands some more formats.
      </li>
      <li>
	Fold box feature is an alternative way to show the structure of code.
      </li>
      <li>
	Avenue lexer supports multiple keyword lists.
      </li>
      <li>
	The caret may now be made invisible by setting the caret width to 0.
      </li>
      <li>
	Python folder attaches comments before blocks to the next block rather
	than the previous block.
      </li>
      <li>
	SciTE openpath property on Windows searches a path for files that are
	the subject of the Open Selected Filename command.
      </li>
      <li>
        The localization file name can be changed with the locale.properties property.
      </li>
      <li>
	On Windows, SciTE can pipe the result of a string expression into a command line tool.
      </li>
      <li>
	On Windows, SciTE's Find dialog has a Mark All button.
      </li>
      <li>
	On Windows, there is an Insert Abbreviation command that allows a choice from
	the defined abbreviations and inserts the selection into the abbreviation at the
	position of a '|'.
      </li>
      <li>
	Minor fixes to Fortran lexer.
      </li>
      <li>
	fold.html.preprocessor decides whether to fold &lt;? and ?&gt;.
	Minor improvements to PHP folding.
      </li>
      <li>
	Maximum number of keyword lists allowed increased from 6 to 9.
      </li>
      <li>
	Duplicate line command added with default assignment to Ctrl+D.
      </li>
      <li>
	SciTE sets $(Replacements) to the number of replacements made by the
	Replace All command. $(CurrentWord) is set to the word before the caret if the caret
	is at the end of a word.
      </li>
      <li>
	Opening a SciTE session now loads files in remembered order, sets the current file
	as remembered, and moves the caret to the remembered line.
      </li>
      <li>
	Bugs fixed with printing on Windows where line wrapping was causing some text
	to not print.
      </li>
      <li>
	Bug fixed with Korean Input Method Editor on Windows.
      </li>
      <li>
	Bugs fixed with line wrap which would sometimes choose different break positions
	after switching focus away and back.
      </li>
      <li>
	Bug fixed where wheel scrolling had no effect on GTK+ after opening a fold.
      </li>
      <li>
	Bug fixed with file paths containing non-ASCII characters on Windows.
      </li>
      <li>
	Crash fixed with printing on Windows after defining pixmap marker.
      </li>
      <li>
	Crash fixed in makefile lexer when first character on line was '='.
      </li>
      <li>
	Bug fixed where local properties were not always being applied.
      </li>
      <li>
	Ctrl+Keypad* fold command works on GTK+.
      </li>
      <li>
	Hangs fixed in SciTE's Replace All command when replacing regular expressions '^'
	or '$'.
      </li>
      <li>
	SciTE monospace setting behaves more sensibly.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite149.zip?download">Release 1.49</a>
    </h3>
    <ul>
      <li>
	Released on 1 November 2002.
      </li>
      <li>
	Unicode supported on GTK+. To perform well, this added a font cache to GTK+
	and to make that safe, a mutex is used. The mutex requires the application to link in
	the threading library by evaluating `glib-config --libs gthread`. A Unicode locale
	should also be set up by a call like setlocale(LC_CTYPE, "en_US.UTF-8").
	scintilla_release_resources function added to release mutex.
      </li>
      <li>
	FORTRAN and assembler lexers added along with other support for these
	languages in SciTE.
      </li>
      <li>
	Ada lexer improved handling of based numbers, identifier validity and attributes
	distinguished from character literals.
      </li>
      <li>
	Lua lexer handles block comments and a deep level of nesting for literal strings
	and block comments.
      </li>
      <li>
	Errorlist lexer recognizes PHP error messages.
      </li>
      <li>
	Variant of the C++ lexer with case insensitive keywords
	called cppnocase. Whitespace in preprocessor text handled more correctly.
      </li>
      <li>
	Folder added for Perl.
      </li>
      <li>
	Compilation with GCC 3.2 supported.
      </li>
      <li>
	Markers can be pixmaps.
      </li>
      <li>
	Lines are wrapped when printing.
	Bug fixed which printed line numbers in different styles.
      </li>
      <li>
	Text can be appended to end with AppendText method.
      </li>
      <li>
	ChooseCaretX method added.
      </li>
      <li>
	Vertical scroll bar can be turned off with SetVScrollBar method.
      </li>
      <li>
	SciTE Save All command saves all buffers.
      </li>
      <li>
	SciTE localization compares keys case insensitively to make translations more flexible.
      </li>
      <li>
	SciTE detects a utf-8 coding cookie "coding: utf-8" in first two
	lines and goes into Unicode mode.
      </li>
      <li>
	SciTE key bindings are definable.
      </li>
      <li>
	SciTE Find in Files dialog can display directory browser to
	choose directory to search.
      </li>
      <li>
	SciTE enabling of undo and redo toolbar buttons improved.
      </li>
      <li>
	SciTE on Windows file type filters in open dialog sorted.
      </li>
      <li>
	Fixed crashing bug when using automatic tag closing in XML or HTML.
      </li>
      <li>
	Fixed bug on Windows causing very long (&gt;64K) lines to not display.
      </li>
      <li>
	Fixed bug in backwards regular expression searching.
      </li>
      <li>
	Fixed bug in calltips where wrong argument was highlighted.
      </li>
      <li>
	Fixed bug in tab timmy feature when file has line feed line endings.
      </li>
      <li>
	Fixed bug in compiling without INCLUDE_DEPRECATED_FEATURES
	defined.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite148.zip?download">Release 1.48</a>
    </h3>
    <ul>
      <li>
	Released on 9 September 2002.
      </li>
      <li>
	Improved Pascal lexer with context sensitive keywords
	and separate folder which handles  //{ and //} folding comments and
	{$region} and {$end} folding directives.
	The "case" statement now folds correctly.
      </li>
      <li>
	C++ lexer correctly handles comments on preprocessor lines.
      </li>
      <li>
	New commands for moving to beginning and end of display lines when in line
	wrap mode. Key bindings added for these commands.
      </li>
      <li>
	New marker symbols that look like ">>>" and "..." which can be used for
	interactive shell prompts for Python.
      </li>
      <li>
	The foreground and background colours of visible whitespace can be chosen
	independent of the colours chosen for the lexical class of that whitespace.
      </li>
      <li>
	Per line data optimized by using an exponential allocation scheme.
      </li>
      <li>
	SciTE API file loading optimized.
      </li>
      <li>
	SciTE for GTK+ subsystem 2 documented. The exit status of commands
	is decoded into more understandable fields.
      </li>
      <li>
	SciTE find dialog remembers previous find string when there is no selection.
	Find in Selection button disabled when selection is rectangular as command
	did not work.
      </li>
      <li>
	Shift+Enter made equivalent to Enter to avoid users having to let go of
	the shift key when typing. Avoids the possibility of entering single carriage
	returns in a file that contains CR+LF line ends.
      </li>
      <li>
	Autocompletion does not immediately disappear when the length parameter
	to SCI_AUTOCSHOW is 0.
      </li>
      <li>
	SciTE focuses on the editor pane when File | New executed and when the
	output pane is closed with F8. Double clicking on a non-highlighted output
	pane line selects the word under the cursor rather than seeking the next
	highlighted line.
      </li>
      <li>
	SciTE director interface implements an "askproperty" command.
      </li>
      <li>
	SciTE's Export as LaTeX output improved.
      </li>
      <li>
	Better choice of autocompletion displaying above the caret rather then
	below when that is more sensible.
      </li>
      <li>
	Bug fixed where context menu would not be completely visible if invoked
	when cursor near bottom or left of screen.
      </li>
      <li>
	Crashing bug fixed when displaying long strings on GTK+ caused failure of X server
	by displaying long text in segments.
      </li>
      <li>
	Crashing bug fixed on GTK+ when a Scintilla window was removed from its parent
	but was still the selection owner.
      </li>
      <li>
	Bug fixed on Windows in Unicode mode where not all characters on a line
	were displayed when that line contained some characters not in ASCII.
      </li>
      <li>
	Crashing bug fixed in SciTE on Windows with clearing output while running command.
      </li>
      <li>
	Bug fixed in SciTE for GTK+ with command completion not detected when
	no output was produced by the command.
      </li>
      <li>
	Bug fixed in SciTE for Windows where menus were not shown translated.
      </li>
      <li>
	Bug fixed where words failed to display in line wrapping mode with visible
	line ends.
      </li>
      <li>
	Bug fixed in SciTE where files opened from a session file were not closed.
      </li>
      <li>
	Cosmetic flicker fixed when using Ctrl+Up and Ctrl+Down with some caret policies.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite147.zip?download">Release 1.47</a>
    </h3>
    <ul>
      <li>
	Released on 1 August 2002.
      </li>
      <li>
	Support for GTK+ 2 in Scintilla. International input methods not supported
	on GTK+2.
      </li>
      <li>
	Line wrapping performance improved greatly.
      </li>
      <li>
	New caret policy implementation that treats horizontal and vertical
	positioning equivalently and independently. Old caret policy methods
	deprecated and not all options work correctly with old methods.
      </li>
      <li>
	Extra fold points for C, C++, Java, ... for fold comments //{ .. //} and
	#if / #ifdef .. #endif and the #region .. #endregion feature of C#.
      </li>
      <li>
	Scintilla method to find the height in pixels of a line. Currently returns the
	same result for every line as all lines are same height.
      </li>
      <li>
	Separate make file, scintilla_vc6.mak, for Scintilla to use Visual C++
	version 6 since main makefile now assumes VS .NET.
	VS .NET project files available for combined Scintilla and
	SciTE in scite/boundscheck.
      </li>
      <li>
	SciTE automatically recognizes Unicode files based
	on their Byte Order Marks and switches to Unicode mode.
	On Windows, where SciTE supports Unicode display, this
	allows display of non European characters.
	The file is saved back into the same character encoding unless
	the user decides to switch using the File | Encoding menu.
      </li>
      <li>
	Handling of character input changed so that a fillup character, typically '('
	displays a calltip when an autocompletion list was being displayed.
      </li>
      <li>
	Multiline strings lexed better for C++ and Lua.
      </li>
      <li>
	Regular expressions in JavaScript within hypertext files are lexed better.
      </li>
      <li>
	On Windows, Scintilla exports a function called Scintilla_DirectFunction
	that can be used the same as the function returned by GetDirectFunction.
      </li>
      <li>
	Scintilla converts line endings of text obtained from the clipboard to
	the current default line endings.
      </li>
      <li>
	New SciTE property ensure.final.line.end can ensure that saved files
	always end with a new line as this is required by some tools.
	The ensure.consistent.line.ends property ensures all line ends are the
	current default when saving files.
	The strip.trailing.spaces property now works on the buffer so the
	buffer in memory and the file on disk are the same after a save is performed.
      </li>
      <li>
	The SciTE expand abbreviation command again allows '|' characters
	in expansions to be quoted by using '||'.
      </li>
      <li>
	SciTE on Windows can send data to the find tool through standard
	input rather than using a command line argument to avoid problems
	with quoting command line arguments.
      </li>
      <li>
	The Stop Executing command in SciTE on Windows improved to send
	a Ctrl+Z character to the tool. Better messages when stopping a tool.
      </li>
      <li>
	Autocompletion can automatically "fill up" when one of a set of characters is
	type with the autocomplete.&lt;lexer&gt;.fillups property.
      </li>
      <li>
	New predefined properties in SciTE, SelectionStartColumn, SelectionStartLine,
	SelectionEndColumn, SelectionEndLine can be used to integrate with other
	applications.
      </li>
      <li>
	Environment variables are available as properties in SciTE.
      </li>
      <li>
	SciTE on Windows keeps status line more current.
      </li>
      <li>
	Abbreviations work in SciTE on Linux when first opened.
      </li>
      <li>
	File saving fixed in SciTE to ensure files are not closed when they can not be
	saved because of file permissions. Also fixed a problem with buffers that
	caused files to not be saved.
      </li>
      <li>
	SciTE bug fixed where monospace mode not remembered when saving files.
	Some searching options now remembered when switching files.
      </li>
      <li>
	SciTE on Linux now waits on child termination when it shuts a child down
	to avoid zombies.
      </li>
      <li>
	SciTE on Linux has a Print menu command that defaults to invoking a2ps.
      </li>
      <li>
	Fixed incorrect highlighting of indentation guides in SciTE for Python.
      </li>
      <li>
	Crash fixed in Scintilla when calling GetText for 0 characters.
      </li>
      <li>
	Exporting as LaTeX improved when processing backslashes and tabs
	and setting up font.
      </li>
      <li>
	Crash fixed in SciTE when exporting or copying as RTF.
      </li>
      <li>
	SciTE session loading fixed to handle more than 10 files in session.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite146.zip?download">Release 1.46</a>
    </h3>
    <ul>
      <li>
	Released on 10 May 2002.
      </li>
      <li>
	Set of lexers compiled into Scintilla can now be changed by adding and
	removing lexer source files from scintilla/src and running LexGen.py.
      </li>
      <li>
	SCN_ZOOM notification provided by Scintilla when user changes zoom level.
	Method to determine width of strings in pixels so that elements can be sized
	relative to text size.
	SciTE changed to keep line number column displaying a given
	number of characters.
      </li>
      <li>
	The logical width of the document used to determine scroll bar range can be set.
      </li>
      <li>
	Setting to allow vertical scrolling to display last line at top rather than
	bottom of window.
      </li>
      <li>
	Read-only mode improved to avoid changing the selection in most cases
	when a modification is attempted. Drag and drop cursors display correctly
	for read-only in some cases.
      </li>
      <li>
	Visual C++ options in make files changed to suit Visual Studio .NET.
      </li>
      <li>
	Scintilla.iface includes feature types for enumerations and lexers.
      </li>
      <li>
	Lua lexer improves handling of literal strings and copes with nested literal strings.
      </li>
      <li>
	Diff lexer changed to treat lines starting with "***" similarly to "---".
	Symbolic names defined for lexical classes.
      </li>
      <li>
	nncrontab lexer improved.
      </li>
      <li>
	Turkish fonts (iso8859-9) supported on GTK+.
      </li>
      <li>
	Automatic close tag feature for XML and HTML in SciTE.
      </li>
      <li>
	Automatic indentation in SciTE improved.
      </li>
      <li>
	Maximum number of buffers available in SciTE increased. May be up to 100
	although other restrictions on menu length limit the real maximum.
      </li>
      <li>
	Save a Copy command added to SciTE.
      </li>
      <li>
	Export as TeX command added to SciTE.
      </li>
      <li>
	Export as HTML command in SciTE respects Use Monospaced Font and
	background colour settings.
      </li>
      <li>
	Compilation problem on Solaris fixed.
      </li>
      <li>
	Order of files displayed for SciTE's previous and next menu and key commands
	are now consistent.
      </li>
      <li>
	Saving of MRU in recent file changed so files open when SciTE quit
	are remembered.
      </li>
      <li>
	More variants of ctags tags handled by Open Selected Filename in SciTE.
      </li>
      <li>
	JavaScript embedded in XML highlighted again.
      </li>
      <li>
	SciTE status bar updated after changing parameters in case they are being
	displayed in status bar.
      </li>
      <li>
	Crash fixed when handling some multi-byte languages.
      </li>
      <li>
	Crash fixed when replacing end of line characters.
      </li>
      <li>
	Bug in SciTE fixed in multiple buffer mode where automatic loading
	turned on could lead to losing file contents.
      </li>
      <li>
	Bug in SciTE on GTK+ fixed where dismissing dialogs with close box led to
	those dialogs never being shown again.
      </li>
      <li>
	Bug in SciTE on Windows fixed where position.tile with default positions
	led to SciTE being positioned off-screen.
      </li>
      <li>
	Bug fixed in read-only mode, clearing all deletes contraction state data
	leading to it not being synchronized with text.
      </li>
      <li>
	Crash fixed in SciTE on Windows when tab bar displayed.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite145.zip?download">Release 1.45</a>
    </h3>
    <ul>
      <li>
	Released on 15 March 2002.
      </li>
      <li>
	Line layout cache implemented to improve performance by maintaining
	the positioning of characters on lines. Can be set to cache nothing,
	the line with the caret, the visible page or the whole document.
      </li>
      <li>
	Support, including a new lexer, added for Matlab programs.
      </li>
      <li>
	Lua folder supports folding {} ranges and compact mode.
	Lua lexer styles floating point numbers in number style instead of
	setting the '.' in operator style.
	Up to 6 sets of keywords.
	Better support for [[ although only works well
	when all on one line.
      </li>
      <li>
	Python lexer improved to handle floating point numbers that contain negative
	exponents and that start with '.'.
      </li>
      <li>
	When performing a rectangular paste, the caret now remains at the
	insertion point.
      </li>
      <li>
	On Windows with a wheel mouse, page-at-a-time mode is recognized.
      </li>
      <li>
	Read-only mode added to SciTE with a property to initialize it and another property,
	$(ReadOnly) available to show this mode in the status bar.
      </li>
      <li>
	SciTE status bar can show the number of lines in the selection
	with the $(SelHeight) property.
      </li>
      <li>
	SciTE's "Export as HTML" command uses the current character set to produce
	correct output for non-Western-European character sets, such as Russian.
      </li>
      <li>
	SciTE's "Export as RTF" fixed to produce correct output when file contains '\'.
      </li>
      <li>
	SciTE goto command accepts a column as well as a line.
	If given a column, it selects the word at that column.
      </li>
      <li>
	SciTE's Build, Compile and Go commands are now disabled if no
	action has been assigned to them.
      </li>
      <li>
	The Refresh button in the status bar has been removed from SciTE on Windows.
      </li>
      <li>
	Bug fixed in line wrap mode where cursor up or down command did not work.
      </li>
      <li>
	Some styling bugs fixed that were due to a compilation problem with
	gcc and inline functions with same name but different code.
      </li>
      <li>
	The way that lexers loop over text was changed to avoid accessing beyond the
	end or setting beyond the end. May fix some bugs and make the code safer but
	may also cause new bugs.
      </li>
      <li>
	Bug fixed in HTML lexer's handling of SGML.
      </li>
      <li>
	Bug fixed on GTK+/X where lines wider than 32767 pixels did not display.
      </li>
      <li>
	SciTE bug fixed with file name generation for standard property files.
      </li>
      <li>
	SciTE bug fixed with Open Selected Filename command when used with
	file name and line number combination.
      </li>
      <li>
	In SciTE, indentation and tab settings stored with buffers so maintained correctly
	as buffers selected.
	The properties used to initialize these settings can now be set separately for different
	file patterns.
      </li>
      <li>
	Thread safety improved on Windows with a critical section protecting the font
	cache and initialization of globals performed within Scintilla_RegisterClasses.
	New Scintilla_ReleaseResources call provided to allow explicit freeing of resources
	when statically bound into another application. Resources automatically freed
	in DLL version. The window classes are now unregistered as part of resource
	freeing which fixes bugs that occurred in some containers such as Internet Explorer.
      </li>
      <li>
	'make install' fixed on Solaris.
      </li>
      <li>
	Bug fixed that could lead to a file being opened twice in SciTE.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite144.zip?download">Release 1.44</a>
    </h3>
    <ul>
      <li>
	Released on 4 February 2002.
      </li>
      <li>
	Crashing bug fixed in Editor::Paint.
      </li>
      <li>
	Lua lexer no longer treats '.' as a word character and
	handles 6 keyword sets.
      </li>
      <li>
	WordStartPosition and WordEndPosition take an onlyWordCharacters
	argument.
      </li>
      <li>
	SciTE option for simplified automatic indentation which repeats
	the indentation of the previous line.
      </li>
      <li>
	Compilation fix on Alpha because of 64 bit.
      </li>
      <li>
	Compilation fix for static linking.
      </li>
      <li>
	Limited maximum line length handled to 8000 characters as previous
	value of 16000 was causing stack exhaustion crashes for some.
      </li>
      <li>
	When whole document line selected, only the last display line gets
	the extra selected rectangle at the right hand side rather than
	every display line.
      </li>
      <li>
	Caret disappearing bug fixed for the case that the caret was not on the
	first display line of a document line.
      </li>
      <li>
	SciTE bug fixed where untitled buffer containing text was sometimes
	deleted without chance to save.
      </li>
      <li>
	SciTE bug fixed where use.monospaced not working with
	multiple buffers.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite143.zip?download">Release 1.43</a>
    </h3>
    <ul>
      <li>
	Released on 19 January 2002.
      </li>
      <li>
	Line wrapping robustness and performance improved in Scintilla.
      </li>
      <li>
	Line wrapping option added to SciTE for both edit and output panes.
      </li>
      <li>
	Static linking on Windows handles cursor resource better.
	Documentation of static linking improved.
      </li>
      <li>
	Autocompletion has an option to delete any word characters after the caret
	upon selecting an item.
      </li>
      <li>
	FOX version identified by PLAT_FOX in Platform.h.
      </li>
      <li>
	Calltips in SciTE use the calltip.&lt;lexer&gt;.word.characters setting to
	correctly find calltips for functions that include characters like '$' which
	is not normally considered a word character.
      </li>
      <li>
	SciTE has a command to show help on itself which gets hooked up to displaying
	SciTEDoc.html.
      </li>
      <li>
	SciTE option calltip.&lt;lexer&gt;.end.definition to display help text on a
	second line of calltip.
      </li>
      <li>
	Fixed the handling of the Buffers menu on GTK+ to ensure current buffer
	indicated and no warnings occur.
	Changed some menu items on GTK+ version to be same as Windows version.
      </li>
      <li>
	use.monospaced property for SciTE determines initial state of Use Monospaced Font
	setting.
      </li>
      <li>
	The SciTE Complete Symbol command now works when there are no word
	characters before the caret, even though it is slow to display the whole set of
	symbols.
      </li>
      <li>
	Function names removed from SciTE's list of PHP keywords. The full list of
	predefined functions is available from another web site mentioned on the
	Extras page.
      </li>
      <li>
	Crashing bug at startup on GTK+ for some configurations fixed.
      </li>
      <li>
	Crashing bug on GTK+ on 64 bit platforms fixed.
      </li>
      <li>
	Compilation problem with some compilers fixed in GTK+.
      </li>
      <li>
	Japanese text entry improved on Windows 9x.
      </li>
      <li>
        SciTE recent files directory problem on Windows when HOME and SciTE_HOME
	environment variables not set is now the directory of the executable.
      </li>
      <li>
	Session files no longer include untitled buffers.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite142.zip?download">Release 1.42</a>
    </h3>
    <ul>
      <li>
	Released on 24 December 2001.
      </li>
      <li>
	Better localization support including context menus and most messages.
	Translations of the SciTE user interface available for Bulgarian,
	French, German, Italian, Russian, and Turkish.
      </li>
      <li>
	Can specify a character to use to indicate control characters
	rather than having them displayed as mnemonics.
      </li>
      <li>
	Scintilla key command for backspace that will not delete line
	end characters.
      </li>
      <li>
	Scintilla method to find start and end of words.
      </li>
      <li>
	SciTE on GTK+ now supports the load.on.activate and save.on.deactivate
	properties in an equivalent way to the Windows version.
      </li>
      <li>
	The output pane of SciTE on Windows is now interactive so command line
	utilities that prompt for input or confirmation can be used.
      </li>
      <li>
	SciTE on Windows can choose directory for a "Find in Files"
	command like the GTK+ version could.
      </li>
      <li>
	SciTE can now load a set of API files rather than just one file.
      </li>
      <li>
	ElapsedTime class added to Platform for accurate measurement of durations.
	Used for debugging and for showing the user how long commands take in SciTE.
      </li>
      <li>
	Baan lexer added.
      </li>
      <li>
	In C++ lexer, document comment keywords no longer have to be at the start
	of the line.
      </li>
      <li>
	PHP lexer changed to match keywords case insensitively.
      </li>
      <li>
	More shell keywords added.
      </li>
      <li>
	SciTE support for VoiceXML added to xml.properties.
      </li>
       <li>
	In SciTE the selection is not copied to the find field of the Search and Replace
	dialogs if it contains end of line characters.
      </li>
      <li>
	SciTE on Windows has a menu item to decide whether to respond to other
	instances which are performing their check.if.already.open check.
      </li>
      <li>
	SciTE accelerator key for Box Comment command changed to avoid problems
	in non-English locales.
      </li>
      <li>
	SciTE context menu includes Close command for the editor pane and
	Hide command for the output pane.
      </li>
      <li>
	output: command added to SciTE director interface to add text to the
	output pane. The director interface can execute commands (such as tool
	commands with subsystem set to 3) by sending a macro:run message.
      </li>
      <li>
	SciTE on GTK+ will defer to the Window Manager for position if position.left or
	position.top not set and for size if position.width or position.height not set.
      </li>
      <li>
	SciTE on Windows has a position.tile property to place a second instance
	to the right of the first.
      </li>
      <li>
	 Scintilla on Windows again supports EM_GETSEL and EM_SETSEL.
      </li>
      <li>
	Problem fixed in Scintilla on Windows where control ID is no longer cached
	as it could be changed by external code.
      </li>
      <li>
	Problems fixed in SciTE on Windows when finding any other open instances at
	start up when check.if.already.open is true.
      </li>
      <li>
	Bugs fixed in SciTE where command strings were not always having
	variables evaluated.
      </li>
      <li>
	Bugs fixed with displaying partial double-byte and Unicode characters
	in rectangular selections and at the edge when edge mode is EDGE_BACKGROUND.
	Column numbers reported by GetColumn treat multiple byte characters as one column
	rather than counting bytes.
      </li>
      <li>
	Bug fixed with caret movement over folded lines.
      </li>
      <li>
        Another bug fixed with tracking selection in secondary views when performing
	modifications.
      </li>
      <li>
	Horizontal scrolling and display of long lines optimized.
      </li>
      <li>
	Cursor setting in Scintilla on GTK+ optimized.
      </li>
      <li>
	Experimental changeable style attribute.
	Set to false to make text read-only.
	Currently only stops caret from being within not-changeable
	text and does not yet stop deleting a range that contains
	not-changeable text.
	Can be used from SciTE by adding notchangeable to style entries.
      </li>
      <li>
	Experimental line wrapping.
	Currently has performance and appearance problems.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite141.zip?download">Release 1.41</a>
    </h3>
    <ul>
      <li>
	Released on 6 November 2001.
      </li>
      <li>
        Changed Platform.h to not include platform	headers. This lessens likelihood and impact of
	name clashes from system headers and also speeds up compilation.
	Renamed DrawText to DrawTextNoClip to avoid name clash.
      </li>
      <li>
        Changed way word functions work to treat a sequence of punctuation as
	a word. This is more sensible and also more compatible with other editors.
      </li>
      <li>
        Cursor changes over the margins and selection on GTK+ platform.
      </li>
      <li>
        SC_MARK_BACKGROUND is a marker that only changes the line's background colour.
      </li>
      <li>
	Enhanced Visual Basic lexer handles character date and octal literals,
	and bracketed keywords for VB.NET. There are two VB lexers, vb and vbscript
	with type indication characters like ! and $ allowed at the end of identifiers
	in vb but not vbscript. Lexer states now separate from those used for C++ and
	names start with SCE_B.
      </li>
      <li>
         Lexer added for Bullant language.
      </li>
      <li>
         The horizontal scroll position, xOffset, is now exposed through the API.
      </li>
      <li>
         The SCN_POSCHANGED notification is deprecated as it was causing confusion.
	 Use SCN_UPDATEUI  instead.
      </li>
      <li>
         Compilation problems fixed for some versions of gcc.
      </li>
      <li>
        Support for WM_GETTEXT restored on Windows.
      </li>
      <li>
        Double clicking on an autocompletion list entry works on GTK+.
      </li>
      <li>
        Bug fixed with case insensitive sorts for autocompletion lists.
      </li>
      <li>
        Bug fixed with tracking selection in secondary views when performing modifications.
      </li>
      <li>
        SciTE's abbreviation expansion feature will now indent expansions to the current
	indentation level if indent.automatic is on.
      </li>
      <li>
        SciTE allows setting up of parameters to commands from a dialog and can also
       show this dialog automatically to prompt for arguments when running a command.
      </li>
      <li>
        SciTE's Language menu (formerly Options | Use Lexer) is now defined by the
	menu.language property rather than being hardcoded.
      </li>
      <li>
        The user interface of SciTE can be localized to a particular language by editing
	a locale.properties file.
      </li>
      <li>
        On Windows, SciTE will try to move to the front when opening a new file from
	the shell and using check.if.already.open.
      </li>
      <li>
        SciTE can display the file name and directory in the title bar in the form
	"file @ directory" when title.full.path=2.
      </li>
      <li>
        The SciTE time.commands property reports the time taken by a command as well
	as its status when completed.
      </li>
      <li>
        The SciTE find.files property is now a list separated by '|' characters and this list is
	added into the Files pull down of the Find in Files dialog.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite140.zip?download">Release 1.40</a>
    </h3>
    <ul>
      <li>
	Released on 23 September 2001.
      </li>
      <li>
	Removal of emulation of Win32 RichEdit control in core of Scintilla.
	<em>This change may be incompatible with existing client code.</em>
	Some emulation still done in Windows platform layer.
      </li>
      <li>
	SGML support in the HTML/XML lexer.
      </li>
      <li>
	SciTE's "Stop Executing" command will terminate GUI programs on
	Windows NT and Windows 2000.
      </li>
      <li>
	StyleContext class helps construct lexers that are simple and accurate.
	Used in the C++, Eiffel, and Python lexers.
      </li>
      <li>
	Clipboard operations in GTK+ version convert between platform '\n' line endings and
	currently chosen line endings.
      </li>
      <li>
	Any character in range 0..255 can be used as a marker.
	This can be used to support numbered bookmarks, for example.
      </li>
      <li>
	The default scripting language for ASP can be set.
      </li>
      <li>
	New lexer and other support for crontab files used with the nncron scheduler.
      </li>
      <li>
	Folding of Python improved.
      </li>
      <li>
	The ` character is treated as a Python operator.
      </li>
      <li>
	Line continuations ("\" at end of line) handled inside Python strings.
      </li>
      <li>
	More consistent handling of line continuation ('\' at end of line) in
	C++ lexer.
	This fixes macro definitions that span more than one line.
      </li>
      <li>
	C++ lexer can understand Doxygen keywords in doc comments.
      </li>
      <li>
	SciTE on Windows allows choosing to open the "open" dialog on the directory
	of the current file rather than in the default directory.
      </li>
      <li>
	SciTE on Windows handles command line arguments in "check.if.already.open"
	correctly when the current directory of the new instance is different to the
	already open instance of SciTE.
      </li>
      <li>
	"cwd" command (change working directory) defined for SciTE director interface.
      </li>
      <li>
	SciTE "Export As HTML" produces better, more compliant, and shorter files.
      </li>
      <li>
	SciTE on Windows allows several options for determining default file name
	for exported files.
      </li>
      <li>
	Automatic indentation of Python in SciTE fixed.
      </li>
      <li>
	Exported HTML can support folding.
      </li>
      <li>
	Bug fixed in SCI_GETTEXT macro command of director interface.
      </li>
      <li>
	Cursor leak fixed on GTK+.
      </li>
      <li>
	During SciTE shutdown, "identity" messages are no longer sent over the director interface.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite139.zip?download">Release 1.39</a>
    </h3>
    <ul>
      <li>
	Released on 22 August 2001.
      </li>
      <li>
	Windows version requires msvcrt.dll to be available so will not work
	on original Windows 95 version 1. The msvcrt.dll file is installed
	by almost everything including Internet Explorer so should be available.
      </li>
      <li>
	Flattened tree control style folding margin. The SciTE fold.plus option is
	now fold.symbols and has more values for the new styles.
      </li>
      <li>
	Mouse dwell events are generated when the user holds the mouse steady
	over Scintilla.
      </li>
      <li>
      PositionFromPointClose is like PositionFromPoint but returns
      INVALID_POSITION when point outside window or after end of line.
      </li>
      <li>
      Input of Hungarian and Russian characters in GTK+ version works by
      truncating input to 8 bits if in the range of normal characters.
      </li>
      <li>
      Better choices for font descriptors on GTK+ for most character sets.
      </li>
      <li>
      GTK+ Scintilla is destroyed upon receiving destroy signal rather than
      destroy_event signal.
      </li>
      <li>
      Style setting that force upper or lower case text.
      </li>
      <li>
      Case-insensitive autocompletion lists work correctly.
      </li>
      <li>
      Keywords can be prefix based so ^GTK_ will treat all words that start
      with GTK_ as keywords.
      </li>
      <li>
      Horizontal scrolling can be jumpy rather than gradual.
      </li>
      <li>
      GetSelText places a '\0' in the buffer if the selection is empty..
      </li>
      <li>
      EnsureVisible split into two methods EnsureVisible which will not scroll to show
      the line and EnsureVisibleEnforcePolicy which may scroll.
      </li>
      <li>
      Python folder has options to fold multi-line comments and triple quoted strings.
      </li>
      <li>
      C++ lexer handles keywords before '.' like "this.x" in Java as keywords.
      Compact folding mode option chooses whether blank lines after a structure are
      folded with that structure. Second set of keywords with separate style supported.
      </li>
      <li>
      Ruby lexer handles multi-line comments.
      </li>
      <li>
      VB has folder.
      </li>
      <li>
      PHP lexer has an operator style, handles "&lt;?" and "?&gt;" inside strings
      and some comments.
      </li>
      <li>
      TCL lexer which is just an alias for the C++ lexer so does not really
      understand TCL syntax.
      </li>
      <li>
      Error lines lexer has styles for Lua error messages and .NET stack traces.
      </li>
      <li>
      Makefile lexer has a target style.
      </li>
      <li>
      Lua lexer handles some [[]] string literals.
      </li>
      <li>
      HTML and XML lexer have a SCE_H_SGML state for tags that
      start with "&lt;!".
      </li>
      <li>
      Fixed Scintilla bugs with folding. When modifications were performed near
      folded regions sometimes no unfolding occurred when it should have. Deleting a
      fold causing character sometimes failed to update fold information correctly.
      </li>
      <li>
      Better support for Scintilla on GTK+ for Win32 including separate
      PLAT_GTK_WIN32 definition and correct handling of rectangular selection
      with clipboard operations.
      </li>
      <li>
      SciTE has a Tools | Switch Pane (Ctrl+F6) command to switch focus between
      edit and output panes.
      </li>
      <li>
      SciTE option output.scroll allows automatic scrolling of output pane to
      be turned off.
      </li>
      <li>
      Commands can be typed into the SciTE output pane similar to a shell window.
      </li>
      <li>
      SciTE properties magnification and output magnification set initial zoom levels.
      </li>
      <li>
      Option for SciTE comment block command to place comments at start of line.
      </li>
      <li>
       SciTE for Win32 has an option to minimize to the tray rather than the task bar.
      </li>
      <li>
      Close button on SciTE tool bar for Win32.
      </li>
      <li>
      SciTE compiles with GCC 3.0.
      </li>
      <li>
      SciTE's automatic indentation of C++ handles braces without preceding keyword
      correctly.
      </li>
      <li>
      Bug fixed with GetLine method writing past the end of where it should.
      </li>
      <li>
      Bug fixed with mouse drag automatic scrolling when some lines were folded.
      </li>
      <li>
      Bug fixed because caret XEven setting was inverted.
      </li>
      <li>
      Bug fixed where caret was initially visible even though window was not focussed.
      </li>
      <li>
      Bug fixed where some file names could end with "\\" which caused slow
      downs on Windows 9x.
      </li>
      <li>
      On Win32, SciTE Replace dialog starts with focus on replacement text.
      </li>
      <li>
      SciTE Go to dialog displays correct current line.
      </li>
      <li>
      Fixed bug with SciTE opening multiple files at once.
      </li>
      <li>
      Fixed bug with Unicode key values reported to container truncated.
      </li>
      <li>
      Fixed bug with unnecessary save point notifications.
      </li>
      <li>
      Fixed bugs with indenting and unindenting at start of line.
      </li>
      <li>
      Monospace Font setting behaves more consistently.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite138.zip?download">Release 1.38</a>
    </h3>
    <ul>
      <li>
	Released on 23 May 2001.
      </li>
      <li>
	Loadable lexer plugins on Windows.
      </li>
      <li>
	Ruby lexer and support.
      </li>
      <li>
	Lisp lexer and support.
      </li>
      <li>
	Eiffel lexer and support.
      </li>
      <li>
	Modes for better handling of Tab and BackSpace keys within
	indentation. Mode to avoid autocompletion list cancelling when
	there are no viable matches.
      </li>
      <li>
	ReplaceTarget replaced with two calls ReplaceTarget
	(which is incompatible with previous ReplaceTarget) and
	ReplaceTargetRE. Both of these calls have a count first
	parameter which allows using strings containing nulls.
	SearchInTarget and SetSearchFlags functions allow
	specifying a search in several simple steps which helps
	some clients which can not create structs or pointers easily.
      </li>
      <li>
	Asian language input through an Input Method Editor works
	on Windows 2000.
      </li>
      <li>
	On Windows, control characters can be entered through use of
	the numeric keypad in conjunction with the Alt key.
      </li>
      <li>
	Document memory allocation changed to grow exponentially
	which reduced time to load a 30 Megabyte file from
	1000 seconds to 25. Change means more memory may be used.
      </li>
      <li>
	Word part movement keys now handled in Scintilla rather than
	SciTE.
      </li>
      <li>
	Regular expression '^' and '$' work more often allowing insertion
	of text at start or end of line with a replace command.
	Backslash quoted control characters \a, \b, \f, \t, and \v
	recognized within sets.
      </li>
      <li>
	Session files for SciTE.
      </li>
      <li>
	Export as PDF command hidden in SciTE as it often failed.
	Code still present so can be turned on by those willing to cope.
      </li>
      <li>
	Bug fixed in HTML lexer handling % before &gt; as end ASP
	even when no start ASP encountered.
        Bug fixed when scripts ended with a quoted string and
        end tag was not seen.
      </li>
      <li>
	Bug fixed on Windows where context menu key caused menu to
	appear in corner of screen rather than within window.
      </li>
      <li>
	Bug fixed in SciTE's Replace All command not processing
	whole file when replace string longer than search string.
      </li>
      <li>
	Bug fixed in SciTE's MRU list repeating entries if Ctrl+Tab
	used when all entries filled.
      </li>
      <li>
	ConvertEOLs call documentation fixed.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite137.zip?download">Release 1.37</a>
    </h3>
    <ul>
      <li>
	Released on 17 April 2001.
      </li>
      <li>
	Bug fixed with scroll bars being invisible on GTK+ 1.2.9.
      </li>
      <li>
	Scintilla and SciTE support find and replace using simple regular
	expressions with tagged expressions. SciTE supports C '\' escapes
	in the Find and Replace dialogs.
	Replace in Selection available in SciTE.
      </li>
      <li>
	Scintilla has a 'target' feature for replacing code rapidly without
	causing display updates.
      </li>
      <li>
	Scintilla and SciTE on GTK+ support file dropping from file managers
	such as Nautilus and gmc. Files or other URIs dropped on Scintilla
	result in a URIDropped notification.
      </li>
      <li>
	Lexers may have separate Lex and Fold functions.
      </li>
      <li>
	Lexer infrastructure improved to allow for plug in lexers and for referring
	to lexers by name rather than by ID.
      </li>
      <li>
	Ada lexer and support added.
      </li>
      <li>
	Option in both Scintilla and SciTE to treat both left and right margin
	as equally important when repositioning visible area in response to
	caret movement. Default is to prefer visible area positioning which
	minimizes the horizontal scroll position thus favouring the left margin.
      </li>
      <li>
	Caret line highlighting.
      </li>
      <li>
	Commands to delete from the caret to the end of line and
	from the caret to the beginning of line.
      </li>
      <li>
	SciTE has commands for inserting and removing block comments and
	for inserting stream comments.
      </li>
      <li>
	SciTE Director interface uses C++ '\' escapes to send control characters.
      </li>
      <li>
	SciTE Director interface adds more commands including support for macros.
      </li>
      <li>
	SciTE has menu options for recording and playing macros which are visible
	when used with a companion program that supports these features.
      </li>
      <li>
	SciTE has an Expand Abbreviation command.
	Abbreviations are stored in a global abbrev.properties file.
      </li>
      <li>
	SciTE has a Full Screen command to switch between a normal window
	size and using the full screen. On Windows, the menu bar can be turned
	off when in full screen mode.
      </li>
      <li>
	SciTE has a Use monospaced font command to switch between the normal
	set of fonts and one size of a particular fixed width font.
      </li>
      <li>
	SciTE's use of tabs can be controlled for particular file names
	as well as globally.
      </li>
      <li>
	The contents of SciTE's status bar can be defined by a property and
	include variables. On Windows, several status bar definitions can be active
	with a click on the status bar cycling through them.
      </li>
      <li>
	Copy as RTF command in SciTE on Windows to allow pasting
	styled text into word processors.
      </li>
      <li>
	SciTE can allow the use of non-alphabetic characters in
	Complete Symbol lists and can automatically display this autocompletion
	list when a trigger character such as '.' is typed.
	Complete word can be set to pop up when the user is typing a word and
	there is only one matching word in the document.
      </li>
      <li>
	SciTE lists the imported properties files on a menu to allow rapid
	access to them.
      </li>
      <li>
	SciTE on GTK+ improvements to handling accelerator keys and focus
	in dialogs. Message boxes respond to key presses without the Alt key as
	they have no text entries to accept normal keystrokes.
      </li>
      <li>
	SciTE on GTK+ sets the application icon.
      </li>
      <li>
	SciTE allows setting the colours used to indicate the current
	error line.
      </li>
      <li>
	Variables within PHP strings have own style. Keyword list updated.
      </li>
      <li>
	Keyword list for Lua updated for Lua 4.0.
      </li>
      <li>
	Bug fixed in rectangular selection where rectangle still appeared
	selected after using cursor keys to move caret.
      </li>
      <li>
	Bug fixed in C++ lexer when deleting a '{' controlling a folded range
	led to that range becoming permanently invisible.
      </li>
      <li>
	Bug fixed in Batch lexer where comments were not recognized.
      </li>
      <li>
	Bug fixed with undo actions coalescing into steps incorrectly.
      </li>
      <li>
	Bug fixed with Scintilla on GTK+ positioning scroll bars 1 pixel
	over the Scintilla window leading to their sides being chopped off.
      </li>
      <li>
	Bugs fixed in SciTE when doing some actions led to the start
	or end of the file being displayed rather than the current location.
      </li>
      <li>
	Appearance of calltips fixed to look like document text including
	any zoom factor. Positioned to be outside current line even when
	multiple fonts and sizes used.
      </li>
      <li>
	Bug fixed in Scintilla macro support where typing Enter caused both a newline
	command and newline character insertion to be recorded.
      </li>
      <li>
	Bug fixed in SciTE on GTK+ where focus was moving
	between widgets incorrectly.
      </li>
      <li>
	Bug fixed with fold symbols sometimes not updating when
	the text changed.
      </li>
      <li>
	Bugs fixed in SciTE's handling of folding commands.
      </li>
      <li>
	Deprecated undo collection enumeration removed from API.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite136.zip?download">Release 1.36</a>
    </h3>
    <ul>
      <li>
	Released on 1 March 2001.
      </li>
      <li>
	Scintilla supports GTK+ on Win32.
      </li>
      <li>
	Some untested work on making Scintilla and SciTE 64 bit compatible.
	For users on GTK+ this requires including Scintilla.h before
	ScintillaWidget.h.
      </li>
      <li>
	HTML lexer allows folding HTML.
      </li>
      <li>
	New lexer for Avenue files which are used in the ESRI ArcView GIS.
      </li>
      <li>
	DOS Batch file lexer has states for '@', external commands, variables and
	operators.
      </li>
      <li>
	C++ lexer can fold comments of /* .. */ form.
      </li>
      <li>
	Better disabling of pop up menu items in Scintilla when in read-only mode.
      </li>
      <li>
	Starting to move to Doxygen compatible commenting.
      </li>
      <li>
	Director interface on Windows enables another application to control SciTE.
      </li>
      <li>
	Opening SciTE on Windows 9x sped up greatly for some cases.
      </li>
      <li>
	The command.build.directory property allows SciTE to run the build
	command in a different directory to the source files.
      </li>
      <li>
	SciTE on Windows allows setting foreground and background colours
	for printed headers and footers.
      </li>
      <li>
	Bug fixed in finding calltips in SciTE which led to no calltips for some identifiers.
      </li>
      <li>
	Documentation added for lexers and for the extension and director interfaces.
      </li>
      <li>
	SciTE menus rearranged with new View menu taking over some of the items that
	were under the Options menu. Clear All Bookmarks command added.
      </li>
      <li>
	Clear Output command in SciTE.
      </li>
      <li>
	SciTE on Windows gains an Always On Top command.
      </li>
      <li>
	Bug fixed in SciTE with attempts to define properties recursively.
      </li>
      <li>
	Bug fixed in SciTE properties where only one level of substitution was done.
      </li>
      <li>
	Bug fixed in SciTE properties where extensions were not being
	matched in a case insensitive manner.
      </li>
      <li>
	Bug fixed in SciTE on Windows where the Go to dialog displays the correct
	line number.
      </li>
      <li>
	In SciTE, if fold.on.open set then switching buffers also performs fold.
      </li>
      <li>
	Bug fixed in Scintilla where ensuring a line was visible in the presence of folding
	operated on the document line instead of the visible line.
      </li>
      <li>
	SciTE command line processing modified to operate on arguments in order and in
	two phases. First any arguments before the first file name are processed, then the
	UI is opened, then the remaining arguments are processed. Actions defined for the
	Director interface (currently only "open") may also be used on the command line.
	For example, "SciTE -open:x.txt" will start SciTE and open x.txt.
      </li>
      <li>
	Numbered menu items SciTE's Buffers menu and the Most Recently Used portion
	of the File menu go from 1..0 rather than 0..9.
      </li>
      <li>
	The tab bar in SciTE for Windows has numbers.
	The tab.hide.one option hides the tab bar until there is more than one buffer open.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite135.zip?download">Release 1.35</a>
    </h3>
    <ul>
      <li>
        Released on 29 January 2001.
      </li>
      <li>
        Rewritten and simplified widget code for the GTK+ version to enhance
        solidity and make more fully compliant with platform norms. This includes more
        normal handling of keystrokes so they are forwarded to containers correctly.
      </li>
      <li>
        User defined lists can be shown.
      </li>
      <li>
        Many fixes to the Perl lexer.
      </li>
      <li>
        Pascal lexer handles comments more correctly.
      </li>
      <li>
        C/C++/Java/JavaScipt lexer has a state for line doc comments.
      </li>
      <li>
        Error output lexer understands Sun CC messages.
      </li>
      <li>
        Make file lexer has variable, preprocessor, and operator states.
      </li>
      <li>
        Wider area given to an italics character that is at the end of a line to prevent it
	being cut off.
      </li>
      <li>
        Call to move the caret inside the currently visible area.
      </li>
      <li>
        Paste Rectangular will space fill on the left hand side of the pasted text as
	needed to ensure it is kept rectangular.
      </li>
      <li>
        Cut and Paste Rectangular does nothing in read-only mode.
      </li>
      <li>
        Undo batching changed so that a paste followed by typing creates two undo actions..
      </li>
      <li>
        A "visibility policy" setting for Scintilla determines which range of lines are displayed
	when a particular line is moved to. Also exposed as a property in SciTE.
      </li>
      <li>
        SciTE command line allows property settings.
      </li>
      <li>
        SciTE has a View Output command to hide or show the output pane.
      </li>
      <li>
        SciTE's Edit menu has been split in two with searching commands moved to a
	new Search menu. Find Previous and Previous Bookmark are in the Search menu.
      </li>
      <li>
        SciTE on Windows has options for setting print margins, headers and footers.
      </li>
      <li>
        SciTE on Windows has tooltips for toolbar.
      </li>
      <li>
        SciTE on GTK+ has properties for setting size of file selector.
      </li>
      <li>
        Visual and audio cues in SciTE on Windows enhanced.
      </li>
      <li>
        Fixed performance problem in SciTE for GTK+ by dropping the extra 3D
        effect on the content windows.
      </li>
      <li>
        Fixed problem in SciTE where choosing a specific lexer then meant
        that no lexer was chosen when files opened.
      </li>
      <li>
        Default selection colour changed to be visible on low colour displays.
      </li>
      <li>
        Fixed problems with automatically reloading changed documents in SciTE on
        Windows.
      </li>
      <li>
        Fixed problem with uppercase file extensions in SciTE.
      </li>
      <li>
        Fixed some problems when using characters >= 128, some of which were being
        incorrectly treated as spaces.
      </li>
      <li>
        Fixed handling multiple line tags, non-inline scripts, and XML end tags /&gt; in HTML/XML lexer.
      </li>
      <li>
        Bookmarks in SciTE no longer disappear when switching between buffers.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite134.zip?download">Release 1.34</a>
    </h3>
    <ul>
      <li>
        Released on 28 November 2000.
      </li>
      <li>
        Pascal lexer.
      </li>
      <li>
        Export as PDF in SciTE.
      </li>
      <li>
        Support for the OpenVMS operating system in SciTE.
      </li>
      <li>
        SciTE for GTK+ can check for another instance of SciTE
	editing a file and switch to it rather than open a second instance
	on one file.
      </li>
      <li>
        Fixes to quoting and here documents in the Perl lexer.
      </li>
      <li>
        SciTE on Windows can give extra visual and audio cues when a
	warning is shown or find restarts from beginning of file.
      </li>
      <li>
        Open Selected Filename command in SciTE. Also understands some
	warning message formats.
      </li>
      <li>
        Wider area for line numbers when printing.
      </li>
      <li>
        Better scrolling performance on GTK+.
      </li>
      <li>
        Fixed problem where rectangles with negative coordinates were
	invalidated leading to trouble with platforms that use
	unsigned coordinates.
      </li>
      <li>
        GTK+ Scintilla uses more compliant signalling code so that keyboard
	events should propagate to containers.
      </li>
      <li>
        Bug fixed with opening full or partial paths.
      </li>
      <li>
        Improved handling of paths in error messages in SciTE.
      </li>
      <li>
        Better handling of F6 in SciTE.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite133.zip?download">Release 1.33</a>
    </h3>
    <ul>
      <li>
        Released on 6 November 2000.
      </li>
      <li>
        XIM support for the GTK+ version of Scintilla ensures that more non-English
        characters can be typed.
      </li>
      <li>
        Caret may be 1, 2, or 3 pixels wide.
      </li>
      <li>
        Cursor may be switched to wait image during lengthy processing.
      </li>
      <li>
        Scintilla's internal focus flag is exposed for clients where focus is handled in
        complex ways.
      </li>
      <li>
        Error status defined for Scintilla to hold indication that an operation failed and the reason
        for that failure. No detection yet implemented but clients may start using the interface
        so as to be ready for when it does.
      </li>
      <li>
        Context sensitive help in SciTE.
      </li>
      <li>
        CurrentWord property available in SciTE holding the value of the word the
        caret is within or near.
      </li>
      <li>
        Apache CONF file lexer.
      </li>
      <li>
        Changes to Python lexer to allow 'as' as a context sensitive keyword and the
        string forms starting with u, r, and ur to be recognized.
      </li>
      <li>
        SCN_POSCHANGED notification now working and SCN_PAINTED notification added.
      </li>
      <li>
        Word part movement commands for cursoring between the parts of reallyLongCamelIdentifiers and
        other_ways_of_making_words.
      </li>
      <li>
        When text on only one line is selected, Shift+Tab moves to the previous tab stop.
      </li>
      <li>
        Tab control available for Windows version of SciTE listing all the buffers
        and making it easy to switch between them.
      </li>
      <li>
        SciTE can be set to automatically determine the line ending type from the contents of a
        file when it is opened.
      </li>
      <li>
        Dialogs in GTK+ version of SciTE made more modal and have accelerator keys.
      </li>
      <li>
        Find in Files command in GTK+ version of SciTE allows choice of directory.
      </li>
      <li>
        On Windows, multiple files can be opened at once.
      </li>
      <li>
        SciTE source broken up into more files.
      </li>
      <li>
        Scintilla headers made safe for C language, not just C++.
      </li>
      <li>
        New printing modes - force background to white and force default background to white.
      </li>
      <li>
        Automatic unfolding not occurring when Enter pressed at end of line bug fixed.
      </li>
      <li>
        Bugs fixed in line selection.
      </li>
      <li>
        Bug fixed with escapes in PHP strings in the HTML lexer.
      </li>
      <li>
        Bug fixed in SciTE for GTK+ opening files when given full paths.
      </li>
      <li>
        Bug fixed in autocompletion where user backspaces into existing text.
      </li>
      <li>
        Bugs fixed in opening files and ensuring they are saved before running.
        A case bug also fixed here.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite132.zip?download">Release 1.32</a>
    </h3>
    <ul>
      <li>
        Released on 8 September 2000.
      </li>
      <li>
        Fixes bugs in complete word and related code. Protection against a bug when
	receiving a bad argument.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite131.zip?download">Release 1.31</a>
    </h3>
    <ul>
      <li>
        Released on 6 September 2000.
      </li>
      <li>
        Scintilla is available as a COM control from the scintillactrl module in CVS.
      </li>
      <li>
        Style setting to underline text. Exposed in SciTE as "underlined".
      </li>
      <li>
        Style setting to make text invisible.
      </li>
      <li>
        SciTE has an extensibility interface that can be used to implement features such as
        a scripting language or remote control. An example use of this is the extlua module
        available from CVS which allows SciTE to be scripted in Lua.
      </li>
      <li>
        Many minor fixes to all of the lexers.
      </li>
      <li>
        New lexer for diff and patch files.
      </li>
      <li>
        Error message lexer understands Perl error messages.
      </li>
      <li>
        C/C++/Java lexer now supports C#, specifically verbatim strings and
	@ quoting of identifiers that are the same as keywords. SciTE has
	a set of keywords for C# and a build command set up for C#.
      </li>
      <li>
        Scintilla property to see whether in overtype or insert state.
      </li>
      <li>
         PosChanged notification fired when caret moved.
      </li>
      <li>
        Comboboxes in dialogs in SciTE on Windows can be horizontally scrolled.
      </li>
      <li>
        Autocompletion and calltips can treat the document as case sensitive or
        case insensitive.
      </li>
      <li>
        Autocompletion can be set to automatically choose the only
	element in a single element list.
      </li>
      <li>
        Set of characters that automatically complete an autocompletion list
	can be set.
      </li>
      <li>
        SciTE command to display calltip - useful when dropped because of
	editing.
      </li>
      <li>
        SciTE has a Revert command to go back to the last saved version.
      </li>
      <li>
        SciTE has an Export as RTF command. Save as HTML is renamed
	to Export as HTML and is located on the Export sub menu.
      </li>
      <li>
        SciTE command "Complete Word" searches document for any
	words starting with characters before caret.
      </li>
      <li>
        SciTE options for changing aspects of the formatting of files exported
	as HTML or RTF.
      </li>
      <li>
        SciTE "character.set" option for choosing the character
	set for all fonts.
      </li>
      <li>
        SciTE has a "Toggle all folds" command.
      </li>
      <li>
        The makefiles have changed. The makefile_vc and
	makefile_bor files in scintilla/win32 and scite/win32 have been
	merged into scintilla/win32/scintilla.mak and scite/win32/scite.mak.
	DEBUG may be defined for all make files and this will turn on
	assertions and for some make files will choose other debugging
	options.
      </li>
      <li>
         To make debugging easier and allow good use of BoundsChecker
	 there is a Visual C++ project file in scite/boundscheck that builds
	 all of Scintilla and SciTE into one executable.
      </li>
      <li>
         The size of the SciTE output window can be set with the
	 output.horizontal.size and output.vertical.size settings.
      </li>
      <li>
         SciTE status bar indicator for insert or overwrite mode.
      </li>
      <li>
        Performance improvements to autocompletion and calltips.
      </li>
      <li>
        A caret redraw problem when undoing is fixed.
      </li>
      <li>
        Crash with long lines fixed.
      </li>
      <li>
        Bug fixed with merging markers when lines merged.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite130.zip?download">Release 1.30</a>
    </h3>
    <ul>
      <li>
        Released on 26 July 2000.
      </li>
      <li>
        Much better support for PHP which is now an integral part of the HTML support.
      </li>
      <li>
        Start replacement of Windows-specific APIs with cross platform APIs.
        In 1.30, the new APIs are introduced but the old APIs are still available.
        For the GTK+ version, may have to include "WinDefs.h" explicitly to
        use the old APIs.
      </li>
      <li>
        "if" and "import" statements in SciTE properties files allows modularization into
        language-specific properties files and choices based upon platform.
        This means that SciTE is delivered with 9 language-specific properties files
        as well as the standard SciTEGlobal.properties file.
      </li>
      <li>
        Much lower resource usage on Windows 9x.
      </li>
      <li>
        "/p" option in SciTE on Windows for printing a file and then exiting.
      </li>
      <li>
        Options for printing with inverted brightness (when the screen is set to use
        a dark background) and to force black on white printing.
      </li>
      <li>
        Option for printing magnified or miniaturized from screen settings.
      </li>
      <li>
        In SciTE, Ctrl+F3 and Ctrl+Shift+F3 find the selection in the forwards and backwards
        directions respectively.
      </li>
      <li>
        Auto-completion lists may be set to cancel when the cursor goes before
        its start position or before the start of string being completed.
      </li>
      <li>
        Auto-completion lists automatically size more sensibly.
      </li>
      <li>
        SCI_CLEARDOCUMENTSTYLE zeroes all style bytes, ensures all
        lines are shown and deletes all folding information.
      </li>
      <li>
        On Windows, auto-completion lists are visually outdented rather than indented.
      </li>
      <li>
        Close all command in SciTE.
      </li>
      <li>
        On Windows multiple files can be dragged into SciTE.
      </li>
      <li>
        When saving a file, the SciTE option save.deletes.first deletes it before doing the save.
        This allows saving with a different capitalization on Windows.
      </li>
      <li>
        When use tabs option is off pressing the tab key inserts spaces.
      </li>
      <li>
        Bug in indicators leading to extra line drawn fixed.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite128.zip?download">Release 1.28</a>
    </h3>
    <ul>
      <li>
        Released on 27 June 2000.
      </li>
      <li>
         Fixes crash in indentation guides when indent size set to 0.
      </li>
      <li>
         Fixes to installation on GTK+/Linux. User properties file on GTK+ has a dot at front of name:
         .SciTEUser.properties. Global properties file location configurable at compile time
         defaulting to $prefix/share/scite. $prefix determined from Gnome if present else its
         /usr/local and can be overridden by installer. Gnome menu integration performed in
         make install if Gnome present.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite127.zip?download">Release 1.27</a>
    </h3>
    <ul>
      <li>
        Released on 23 June 2000.
      </li>
      <li>
         Indentation guides. View whitespace mode may be set to not display whitespace
	 in indentation.
      </li>
      <li>
        Set methods have corresponding gets for UndoCollection, BufferedDraw,
	CodePage, UsePalette, ReadOnly, CaretFore, and ModEventMask.
      </li>
      <li>
        Caret is continuously on rather than blinking while typing or holding down
	delete or backspace. And is now always shown if non blinking when focused on GTK+.
      </li>
      <li>
        Bug fixed in SciTE with file extension comparison now done in case insensitive way.
      </li>
      <li>
        Bugs fixed in SciTE's file path handling on Windows.
      </li>
      <li>
        Bug fixed with preprocessor '#' last visible character causing hang.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite126.zip?download">Release 1.26</a>
    </h3>
    <ul>
      <li>
        Released on 13 June 2000.
      </li>
      <li>
         Support for the Lua language in both Scintilla and SciTE.
      </li>
      <li>
        Multiple buffers may be open in SciTE.
      </li>
      <li>
        Each style may have a character set configured. This may determine
	the characters that are displayed by the style.
      </li>
      <li>
         In the C++ lexer, lexing of preprocessor source may either treat it all as being in
	 the preprocessor class or only the initial # and preprocessor command word as
	 being in the preprocessor class.
      </li>
      <li>
        Scintilla provides SCI_CREATEDOCUMENT, SCI_ADDREFDOCUMENT, and
	SCI_RELEASEDOCUMENT to make it easier for a container to deal with multiple
	documents.
      </li>
      <li>
        GTK+ specific definitions in Scintilla.h were removed to ScintillaWidget.h. All GTK+ clients will need to
	#include "ScintillaWidget.h".
      </li>
      <li>
        For GTK+, tools can be executed in the background by setting subsystem to 2.
      </li>
      <li>
        Keys in the properties files are now case sensitive. This leads to a performance increase.
      </li>
      <li>
        Menu to choose which lexer to use on a file.
      </li>
      <li>
        Tab size dialog on Windows.
      </li>
      <li>
        File dialogs enlarged on GTK+.
      </li>
      <li>
         Match Brace command bound to Ctrl+E on both platforms with Ctrl+] a synonym on Windows.
         Ctrl+Shift+E is select to matching brace. Brace matching tries to match to either the inside or the
         outside, depending on whether the cursor is inside or outside the braces initially.
	View End of Line bound to Ctrl+Shift+O.
      </li>
      <li>
        The Home key may be bound to move the caret to either the start of the line or the start of the
        text on the line.
      </li>
      <li>
        Visual C++ project file for SciTE.
      </li>
      <li>
        Bug fixed with current x location after Tab key.
      </li>
      <li>
        Bug fixed with hiding fold margin by setting fold.margin.width to 0.
      </li>
      <li>
        Bugs fixed with file name confusion on Windows when long and short names used, or different capitalizations,
	or relative paths.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite125.zip?download">Release 1.25</a>
    </h3>
    <ul>
      <li>
        Released on 9 May 2000.
      </li>
      <li>
        Some Unicode support on Windows. Treats buffer and API as UTF-8 and displays
	through UCS-2 of Windows.
      </li>
      <li>
        Automatic indentation. Indentation size can be different to tab size.
      </li>
      <li>
        Tool bar.
      </li>
      <li>
        Status bar now on Windows as well as GTK+.
      </li>
      <li>
        Input fields in Find and Replace dialogs now have history on both Windows and
	GTK+.
      </li>
      <li>
        Auto completion list items may be separated by a chosen character to allow spaces
	in items. The selected item may be changed through the API.
      </li>
      <li>
        Horizontal scrollbar can be turned off.
      </li>
      <li>
        Property to remove trailing spaces when saving file.
      </li>
      <li>
        On Windows, changed font size calculation to be more compatible with
	other applications.
      </li>
      <li>
        On GTK+, SciTE's global properties files are looked for in the directory specified in the
	SCITE_HOME environment variable if it is set. This allows hiding in a dot directory.
      </li>
      <li>
        Keyword lists in SciTE updated for JavaScript to include those destined to be used in
	the future. IDL includes XPIDL keywords as well as MSIDL keywords.
      </li>
      <li>
        Zoom level can be set and queried through API.
      </li>
      <li>
        New notification sent before insertions and deletions.
      </li>
      <li>
        LaTeX lexer.
      </li>
      <li>
        Fixes to folding including when deletions and additions are performed.
      </li>
      <li>
        Fix for crash with very long lines.
      </li>
      <li>
        Fix to affect all of rectangular selections with deletion and case changing.
      </li>
      <li>
        Removed non-working messages that had been included only for Richedit compatibility.
      </li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/scite124.zip">Release 1.24</a>
    </h3>
    <ul>
      <li>
        Released on 29 March 2000.
      </li>
      <li>
        Added lexing of IDL based on C++ lexer with extra UUID lexical class.
      </li>
      <li>
        Functions and associated keys for Line Delete, Line Cut, Line Transpose,
	Selection Lower Case and Selection Upper Case.
      </li>
      <li>
        Property setting for SciTE, eol.mode, chooses initial state of line end characters.
      </li>
      <li>
        Fixed bugs in undo history with small almost-contiguous changes being incorrectly coalesced.
      </li>
      <li>
        Fixed bugs with incorrect expansion of ContractionState data structures causing crash.
      </li>
      <li>
        Fixed bugs relating to null fonts.
      </li>
      <li>
        Fixed bugs where recolourization was not done sometimes when required.
      </li>
      <li>
        Fixed compilation problems with SVector.h.
      </li>
      <li>
        Fixed bad setting of fold points in Python.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/scite123.zip?download">Release 1.23</a>
    </h3>
    <ul>
      <li>
        Released on 21 March 2000.
      </li>
      <li>
        Directory structure to separate on basis of product (Scintilla, SciTE, DMApp)
	and environment (Cross-platform, Win32, GTK+).
      </li>
      <li>
        Download packaging to allow download of the source or platform dependent executables.
      </li>
      <li>
        Source code now available from CVS at SourceForge.
      </li>
      <li>
        Very simple Windows-only demonstration application DMApp is available from cvs as dmapp.
      </li>
      <li>
        Lexing functionality may optionally be included in Scintilla rather than be provided by
        the container.
      </li>
      <li>
        Set of lexers included is determined at link time by defining which of the Lex* object files
	are linked in.
      </li>
      <li>
        On Windows, the SciLexer.DLL extends Scintilla.DLL with the standard lexers.
      </li>
      <li>
        Enhanced HTML lexer styles embedded VBScript and Python.
	ASP segments are styled and ASP scripts in JavaScript, VBScript and Python are styled.
      </li>
      <li>
        PLSQL and PHP supported.
      </li>
      <li>
        Maximum number of lexical states extended to 128.
      </li>
      <li>
        Lexers may store per line parse state for multiple line features such as ASP script language choice.
      </li>
      <li>
        Lexing API simplified.
      </li>
      <li>
        Project file for Visual C++.
      </li>
      <li>
        Can now cycle through all recent files with Ctrl+Tab in SciTE.
      </li>
      <li>
        Bookmarks in SciTE.
      </li>
      <li>
        Drag and drop copy works when dragging to the edge of the selection.
      </li>
      <li>
        Fixed bug with value sizes in properties file.
      </li>
      <li>
        Fixed bug with last line in properties file not being used.
      </li>
      <li>
        Bug with multiple views of one document fixed.
      </li>
      <li>
        Keypad now works on GTK+.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/SciTE122.zip?download">Release 1.22</a>
    </h3>
    <ul>
      <li>
        Released on 27 February 2000.
      </li>
      <li>
        wxWindows platform defined.
	Implementation for wxWindows will be available separately
	from main Scintilla distribution.
      </li>
      <li>
        Line folding in Scintilla.
      </li>
      <li>
        SciTE performs syntax directed folding for C/C++/Java/JavaScript and for Python.
      </li>
      <li>
        Optional macro recording support.
      </li>
      <li>
        User properties file (SciTEUser.properties) allows for customization by the user
	that is not overwritten with each installation of SciTE.
      </li>
      <li>
        Python lexer detects and highlights inconsistent indentation.
      </li>
      <li>
        Margin API made more orthogonal. SCI_SETMARGINWIDTH and SCI_SETLINENUMBERWIDTH
        are deprecated in favour of this new API.
      </li>
      <li>
        Margins may be made sensitive to forward mouse click events to container.
      </li>
      <li>
        SQL lexer and styles included.
      </li>
      <li>
        Perl lexer handles regular expressions better.
      </li>
      <li>
        Caret policy determines how closely caret is tracked by visible area.
      </li>
      <li>
        New marker shapes: arrow pointing down, plus and minus.
      </li>
      <li>
        Optionally display full path in title rather than just file name.
      </li>
      <li>
        Container is notified when Scintilla gains or loses focus.
      </li>
      <li>
        SciTE handles focus in a more standard way and applies the main
	edit commands to the focused pane.
      </li>
      <li>
        Container is notified when Scintilla determines that a line needs to be made visible.
      </li>
      <li>
        Document watchers receive notification when document about to be deleted.
      </li>
      <li>
        Document interface allows access to list of watchers.
      </li>
      <li>
        Line end determined correctly for lines ending with only a '\n'.
      </li>
      <li>
        Search variant that searches form current selection and sets selection.
      </li>
      <li>
        SciTE understands format of diagnostic messages from WScript.
      </li>
      <li>
        SciTE remembers top line of window for each file in MRU list so switching to a recent file
	is more likely to show the same text as when the file was previously visible.
      </li>
      <li>
        Document reference count now initialized correctly.
      </li>
      <li>
        Setting a null document pointer creates an empty document.
      </li>
      <li>
        WM_GETTEXT can no longer overrun buffer.
      </li>
      <li>
        Polygon drawing bug fixed on GTK+.
      </li>
      <li>
        Java and JavaScript lexers merged into C++ lexer.
      </li>
      <li>
        C++ lexer indicates unterminated strings by colouring the end of the line
	rather than changing the rest of the file to string style. This is less
	obtrusive and helps the folding.
      </li>
    </ul>
    <h3>
       <a href="https://prdownloads.sourceforge.net/scintilla/SciTE121.zip?download">Release 1.21</a>
    </h3>
    <ul>
      <li>
        Released on 2 February 2000.
      </li>
      <li>
        Blank margins on left and right side of text.
      </li>
      <li>
        SCN_CHECKBRACE renamed SCN_UPDATEUI and made more efficient.
      </li>
      <li>
        SciTE source code refactored into platform independent and platform specific classes.
      </li>
      <li>
        XML and Perl subset lexers in SciTE.
      </li>
      <li>
        Large improvement to lexing speed.
      </li>
      <li>
        A new subsystem, 2, allows use of ShellExec on Windows.
      </li>
      <li>
        Borland compatible makefile.
      </li>
      <li>
        Status bar showing caret position in GTK+ version of SciTE.
      </li>
      <li>
        Bug fixes to selection drawing when part of selection outside window, mouse release over
        scroll bars, and scroll positioning after deletion.
      </li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/SciTE120.zip">Release 1.2</a>
    </h3>
    <ul>
      <li>
        Released on 21 January 2000.
      </li>
      <li>
        Multiple views of one document.
      </li>
      <li>
        Rectangular selection, cut, copy, paste, drag and drop.
      </li>
      <li>
        Long line indication.
      </li>
      <li>
        Reverse searching
      </li>
      <li>
        Line end conversion.
      </li>
      <li>
        Generic autocompletion and calltips in SciTE.
      </li>
      <li>
        Call tip background colour can be set.
      </li>
      <li>
        SCI_MARKERPREV for moving to a previous marker.
      </li>
      <li>
        Caret kept more within window where possible.
      </li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/SciTE115.zip">Release 1.15</a>
    </h3>
    <ul>
      <li>
        Released on 15 December 1999.
      </li>
      <li>
        Brace highlighting and badlighting (for mismatched braces).
      </li>
      <li>
        Visible line ends.
      </li>
      <li>
        Multiple line call tips.
      </li>
      <li>
        Printing now works from SciTE on Windows.
      </li>
      <li>
        SciTE has a global "*" lexer style that is used as the basis for all the lexers' styles.
      </li>
      <li>
        Fixes some warnings on GTK+ 1.2.6.
      </li>
      <li>
        Better handling of modal dialogs on GTK+.
      </li>
      <li>
        Resize handle drawn on pane splitter in SciTE on GTK+ so it looks more like a regular GTK+
        *paned widget.
      </li>
      <li>
        SciTE does not place window origin offscreen if no properties file found on GTK+.
      </li>
      <li>
        File open filter remembered in SciTE on Windows.
      </li>
      <li>
        New mechanism using style numbers 32 to 36 standardizes the setting of styles for brace
        highlighting, brace badlighting, line numbers, control characters and the default style.
      </li>
      <li>
        Old messages SCI_SETFORE .. SCI_SETFONT have been replaced by the default style 32. The old
        messages are deprecated and will disappear in a future version.
      </li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/SciTE114.zip">Release 1.14</a>
    </h3>
    <ul>
      <li>
        Released on 20 November 1999.
      </li>
      <li>
        Fixes a scrolling bug reported on GTK+.
      </li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/SciTE113.zip">Release 1.13</a>
    </h3>
    <ul>
      <li>
        Released on 18 November 1999.
      </li>
      <li>
        Fixes compilation problems with the mingw32 GCC 2.95.2 on Windows.
      </li>
      <li>
        Control characters are now visible.
      </li>
      <li>
        Performance has improved, particularly for scrolling.
      </li>
      <li>
        Windows RichEdit emulation is more accurate. This may break client code that uses these
        messages: EM_GETLINE, EM_GETLINECOUNT, EM_EXGETSEL, EM_EXSETSEL, EM_EXLINEFROMCHAR,
        EM_LINELENGTH, EM_LINEINDEX, EM_CHARFROMPOS, EM_POSFROMCHAR, and EM_GETTEXTRANGE.
      </li>
      <li>
        Menus rearranged and accelerator keys set for all static items.
      </li>
      <li>
        Placement of space indicators in view whitespace mode is more accurate with some fonts.
      </li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/SciTE112.zip">Release 1.12</a>
    </h3>
    <ul>
      <li>
        Released on 9 November 1999.
      </li>
      <li>
        Packaging error in 1.11 meant that the compilation error was not fixed in that release.
        Linux/GTK+ should compile with GCC 2.95 this time.
      </li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/SciTE111.zip">Release 1.11</a>
    </h3>
    <ul>
      <li>
        Released on 7 November 1999.
      </li>
      <li>
        Fixed a compilation bug in ScintillaGTK.cxx.
      </li>
      <li>
        Added a README file to explain how to build.
      </li>
      <li>
        GTK+/Linux downloads now include documentation.
      </li>
      <li>
        Binary only Sc1.EXE one file download for Windows.
      </li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/SciTE110.zip">Release 1.1</a>
    </h3>
    <ul>
      <li>
        Released on 6 November 1999.
      </li>
      <li>
        Major restructuring for better modularity and platform independence.
      </li>
      <li>
        Inter-application drag and drop.
      </li>
      <li>
        Printing support in Scintilla on Windows.
      </li>
      <li>
        Styles can select colouring to end of line. This can be used when a file contains more than
        one language to differentiate between the areas in each language. An example is the HTML +
        JavaScript styling in SciTE.
      </li>
      <li>
        Actions can be grouped in the undo stack, so they will be undone together. This grouping is
        hierarchical so higher level actions such as replace all can be undone in one go. Call to
        discover whether there are any actions to redo.
      </li>
      <li>
        The set of characters that define words can be changed.
      </li>
      <li>
        Markers now have identifiers and can be found and deleted by their identifier. The empty
        marker type can be used to make a marker that is invisible and which is only used to trace
        where a particular line moves to.
      </li>
      <li>
        Double click notification.
      </li>
      <li>
        HTML styling in SciTE also styles embedded JavaScript.
      </li>
      <li>
        Additional tool commands can be added to SciTE.
      </li>
      <li>
        SciTE option to allow reloading if changed upon application activation and saving on
        application deactivation. Not yet working on GTK+ version.
      </li>
      <li>
        Entry fields in search dialogs remember last 10 user entries. Not working in all cases in
        Windows version.
      </li>
      <li>
        SciTE can save a styled copy of the current file in HTML format. As SciTE does not yet
        support printing, this can be used to print a file by then using a browser to print the
        HTML file.
      </li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/SciTE102.zip">Release 1.02</a>
    </h3>
    <ul>
      <li>
        Released on 1 October 1999.
      </li>
      <li>
        GTK+ version compiles with GCC 2.95.
      </li>
      <li>
        Properly deleting objects when window destroyed under GTK+.
      </li>
      <li>
        If the selection is not empty backspace deletes the selection.
      </li>
      <li>
        Some X style middle mouse button handling for copying the primary selection to and from
        Scintilla. Does not work in all cases.
      </li>
      <li>
        HTML styling in SciTE.
      </li>
      <li>
        Stopped dirty flag being set in SciTE when results pane modified.
      </li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/SciTE101.zip">Release 1.01</a>
    </h3>
    <ul>
      <li>
        Released on 28 September 1999.
      </li>
      <li>
        Better DBCS support on Windows including IME.
      </li>
      <li>
        Wheel mouse support for scrolling and zooming on Windows. Zooming with Ctrl+KeypadPlus and
        Ctrl+KeypadMinus.
      </li>
      <li>
        Performance improvements especially on GTK+.
      </li>
      <li>
        Caret blinking and settable colour on both GTK+ and Windows.
      </li>
      <li>
        Drag and drop within a Scintilla window. On Windows, files can be dragged into SciTE.
      </li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/SciTE100.zip">Release 1.0</a>
    </h3>
    <ul>
      <li>
        Released on 17 May 1999.
      </li>
      <li>
        Changed name of "Tide" to "SciTE" to avoid clash with a TCL based IDE. "SciTE" is a
        SCIntilla based Text Editor and is Latin meaning something like "understanding in a neat
        way" and is also an Old English version of the word "shit".
      </li>
      <li>
        There is a SCI_AUTOCSTOPS message for defining a string of characters that will stop
        autocompletion mode. Autocompletion mode is cancelled when any cursor movement occurs apart
        from backspace.
      </li>
      <li>
        GTK+ version now splits horizontally as well as vertically and all dialogs cancel when the
        escape key is pressed.
      </li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/Tide92.zip">Beta release 0.93</a>
    </h3>
    <ul>
      <li>
        Released on 12 May 1999.
      </li>
      <li>
        A bit more robust than 0.92 and supports SCI_MARKERNEXT message.
      </li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/Tide92.zip">Beta release 0.92</a>
    </h3>
    <ul>
      <li>
        Released on 11 May 1999.
      </li>
      <li>
        GTK+ version now contains all features of Windows version with some very small differences.
        Executing programs works much better now.
      </li>
      <li>
        New palette code to allow more colours to be displayed in 256 colour screen modes. A line
        number column can be displayed to the left of the selection margin.
      </li>
      <li>
        The code that maps from line numbers to text positions and back has been completely
        rewritten to be faster, and to allow markers to move with the text.
      </li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/Tide91.zip">Beta release 0.91</a>
    </h3>
    <ul>
      <li>
        Released on 30 April 1999, containing fixes to text measuring to make Scintilla work better
        with bitmap fonts. Also some small fixes to make compiling work with Visual C++.
      </li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/Tide90.zip">Beta release 0.90</a>
    </h3>
    <ul>
      <li>
        Released on 29 April 1999, containing working GTK+/Linux version.
      </li>
      <li>
        The Java, C++ and Python lexers recognize operators as distinct from default allowing them
        to be highlighted.
      </li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/Tide82.zip">Beta release 0.82</a>
    </h3>
    <ul>
      <li>
        Released on 1 April 1999, to fix a problem with handling the Enter key in PythonWin. Also
        fixes some problems with cmd key mapping.
      </li>
    </ul>
    <h3>
       <a href="https://www.scintilla.org/Tide81.zip">Beta release 0.81</a>
    </h3>
    <ul>
      <li>
        Released on 30th March 1999, containing bug fixes and a few more features.
      </li>
      <li>
        Static linking supported and Tidy.EXE, a statically linked version of Tide.EXE. Changes to
        compiler flags in the makefiles to optimize for size.
      </li>
      <li>
        Scintilla supports a 'savepoint' in the undo stack which can be set by the container when
        the document is saved. Notifications are sent to the container when the savepoint is
        entered or left, allowing the container to to display a dirty indicator and change its
        menus.
      </li>
      <li>
        When Scintilla is set to read-only mode, a notification is sent to the container should the
        user try to edit the document. This can be used to check the document out of a version
        control system.
      </li>
      <li>
        There is an API for setting the appearance of indicators.
      </li>
      <li>
        The keyboard mapping can be redefined or removed so it can be implemented completely by the
        container. All of the keyboard commands are now commands which can be sent by the
        container.
      </li>
      <li>
        A home command like Visual C++ with one hit going to the start of the text on the line and
        the next going to the left margin is available. I do not personally like this but my
        fingers have become trained to it by much repetition.
      </li>
      <li>
        SCI_MARKERDELETEALL has an argument in wParam which is the number of the type marker to
        delete with -1 performing the old action of removing all marker types.
      </li>
      <li>
        Tide now understands both the file name and line numbers in error messages in most cases.
      </li>
      <li>
        Tide remembers the current lines of files in the recently used list.
      </li>
      <li>
        Tide has a Find in Files command.
      </li>
    </ul>
    <h3>
       Beta release 0.80
    </h3>
    <ul>
      <li>
        This was the first public release on 14th March 1999, containing a mostly working Win32
        Scintilla DLL and Tide EXE.
      </li>
    </ul>
    <h3>
       Beta releases of SciTE were called Tide
    </h3>
  </body>
</html>
