/*! \mainpage QScintilla - a Port to Qt v5 and Qt v6 of Scintilla

<h2>Introduction</h2>

<a href="http://www.riverbankcomputing.com/software/qscintilla/">QScintilla</a>
is a port to Qt of the <a href="http://www.scintilla.org/">Scintilla</a>
editing component.

As well as features found in standard text editing components, Scintilla
includes features especially useful when editing and debugging source code:

<ul>
<li>syntax styling with support for over 70 languages
<li>error indicators
<li>code completion
<li>call tips
<li>code folding
<li>margins can contain markers like those used in debuggers to indicate
    breakpoints and the current line.
<li>recordable macros
<li>multiple views
<li>printing.
</ul>

QScintilla is a port or Scintilla to the Qt GUI toolkit from
<a href="http://www.qt.io">The Qt Company</a> and runs on any operating system
supported by Qt (eg. Windows, Linux, macOS, iOS and Android).  QScintilla works
with Qt v5 and v6.

QScintilla also includes language bindings for
<a href="https://www.python.org">Python</a>.  These require that
<a href="https://www.riverbankcomputing.com/software/pyqt/">PyQt</a> v5 or v6
is also installed.

This version of QScintilla is based on Scintilla v3.10.1.


<h2>Licensing</h2>

QScintilla is available under the
<a href="http://www.gnu.org/licenses/gpl.html">GNU General Public License v3</a>
and the Riverbank Commercial License.

The commercial license allows closed source applications using QScintilla to be
developed and distributed.  At the moment the commercial version of QScintilla
is bundled with, but packaged separately from, the commercial version of
<a href="http://www.riverbankcomputing.com/software/pyqt/">PyQt</a>.

The Scintilla code within QScintilla is released under the following license:

<pre>
 License for Scintilla and SciTE

 Copyright 1998-2003 by Neil Hodgson <<EMAIL>>

 All Rights Reserved

 Permission to use, copy, modify, and distribute this software and its
 documentation for any purpose and without fee is hereby granted,
 provided that the above copyright notice appear in all copies and that
 both that copyright notice and this permission notice appear in
 supporting documentation.

 NEIL HODGSON DISCLAIMS ALL WARRANTIES WITH REGARD TO THIS
 SOFTWARE, INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
 AND FITNESS, IN NO EVENT SHALL NEIL HODGSON BE LIABLE FOR ANY
 SPECIAL, INDIRECT OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
 WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR OTHER
 TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE
 OR PERFORMANCE OF THIS SOFTWARE.
</pre>


<h2>Installation</h2>

As supplied QScintilla will be built as a shared library/DLL and installed in
the same directories as the Qt libraries and include files.

If you wish to build a static version of the library then pass
<tt>CONFIG+=staticlib</tt> on the <tt>qmake</tt> command line.

On macOS, if you wish to build a dynamic version of the library that supports
both <tt>x86_64</tt> and <tt>arm64</tt> architectures then edit the file
<tt>qscintilla.pro</tt> in the <tt>src</tt> directory and comment in the
definition of <tt>QMAKE_APPLE_DEVICE_ARCHS</tt>.  Similar changes can be made
to the <tt>.pro</tt> files for the Designer plugin and the example application.

If you want to make more significant changes to the configuration then edit the
file <tt>qscintilla.pro</tt> in the <tt>src</tt> directory.

If you do make changes, specifically to the names of the installation
directories or the name of the library, then you may also need to update the
<tt>src/features/qscintilla2.prf</tt> file.

See your <tt>qmake</tt> documentation for more details.

To build and install QScintilla, run:

<pre>
    cd src
    qmake
    make
    make install
</pre>

If you have multiple versions of Qt installed then make sure you use the
correct version of <tt>qmake</tt>.

The underlying Scintilla code may support additional compile-time options.
These can be configured by passing appropriate arguments to <tt>qmake</tt>.
For example, if you have an old C++ compiler that does not have a working
<tt>std::regex</tt> then invoke <tt>qmake</tt> as follows:

<pre>
    qmake DEFINES+=NO_CXX11_REGEX=1
</pre>


<h3>Installation on Windows</h3>

Before compiling QScintilla on Windows you should remove the <tt>Qsci</tt>
directory containing the QScintilla header files from any previous
installation.  This is because the <tt>Makefile</tt> generated by
<tt>qmake</tt> will find these older header files instead of the new ones.

Depending on the compiler you are using you may need to run <tt>nmake</tt>
rather than <tt>make</tt>.

If you have built a Windows DLL then you probably also want to run:

<pre>
    copy %%QTDIR%\\lib\\qscintilla2.dll %%QTDIR%\\bin
</pre>


<h2>Integration with <tt>qmake</tt></h2>

To configure <tt>qmake</tt> to find your QScintilla installation, add the
following line to your application's <tt>.pro</tt> file:

<pre>
    CONFIG += qscintilla2
</pre>


<h2>Qt Designer Plugin</h2>

QScintilla includes an optional plugin for Qt Designer that allows QScintilla
instances to be included in GUI designs just like any other Qt widget.

To build the plugin on all platforms, make sure QScintilla is installed and
then run (as root or administrator):

<pre>
    cd designer
    qmake
    make
    make install
</pre>

On Windows (and depending on the compiler you are using) you may need to run
<tt>nmake</tt> rather than <tt>make</tt>.


<h2>Example Application</h2>

The example application provided is a port of the standard Qt
<tt>application</tt> example with the QsciScintilla class being used instead of
Qt's QTextEdit class.

The example does not demonstrate all of the extra features of QScintilla.

To build the example, run:

<pre>
    cd example
    qmake
    make
</pre>

On Windows (and depending on the compiler you are using) you may need to run
<tt>nmake</tt> rather than <tt>make</tt>.


<h2>Python Bindings</h2>

The Python bindings are in the <tt>Python</tt> sub-directory.  You must have
either PyQt5 or PyQt6 already installed and PyQt-builder.  QScintilla must also
already be built and installed.

The <tt>Python</tt> sub-directory contains a <tt>pyproject-qt5.toml</tt> file
and a <tt>pyproject-qt6.toml</tt> file.  If you are building for PyQt5 and Qt
v5 then you must copy the <tt>pyproject-qt5.toml</tt> file to
<tt>pyproject.toml</tt>.  If instead you are building for PyQt6 and Qt v6 then
you must copy the <tt>pyproject-qt6.toml</tt> file to <tt>pyproject.toml</tt>.

To build and install the bindings, run:

<pre>
    cd Python
    sip-install
</pre>
*/
