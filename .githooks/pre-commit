#!/bin/bash
# 预提交钩子：检查并格式化C++代码

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 获取暂存的文件列表（只处理C++文件）
files=$(git diff --cached --name-only --diff-filter=ACMR | grep -E '\.(cpp|hpp|c|h|cc|cxx)$')

if [ -z "$files" ]; then
    exit 0  # 没有C++文件需要检查
fi

echo -e "${CYAN}🔍 正在检查代码格式...${NC}"

# 检查clang-format是否可用
if ! command -v clang-format &> /dev/null; then
    echo -e "${RED}❌ 错误：未找到clang-format命令${NC}"
    echo -e "${YELLOW}💡 请安装clang-format或确保其在PATH中${NC}"
    exit 1
fi

# 检查是否存在.clang-format配置文件
if [ ! -f ".clang-format" ]; then
    echo -e "${YELLOW}⚠️  警告：未找到.clang-format配置文件${NC}"
fi

format_needed=false
error_files=()

# 检查每个文件的格式
for file in $files; do
    if [ -f "$file" ]; then
        # 检查文件是否需要格式化
        if ! clang-format --dry-run -Werror "$file" &> /dev/null; then
            echo -e "${RED}❌ 格式错误：${BLUE}$file${NC}"
            format_needed=true
            error_files+=("$file")
        fi
    fi
done

if [ "$format_needed" = true ]; then
    echo ""
    echo -e "${YELLOW}⚠️  发现 ${#error_files[@]} 个文件存在格式问题！${NC}"
    echo ""
    echo -e "${CYAN}📝 手动修复方法：${NC}"
    echo -e "   ${BLUE}clang-format -i $files${NC}"
    echo ""
    echo -e "${CYAN}🤖 或者选择自动修复：${NC}"
    echo -ne "${GREEN}❓ 是否自动修复格式问题？ [y/N]: ${NC}"
    read -n 1 -r < /dev/tty
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${CYAN}🔧 正在自动修复格式问题...${NC}"
        clang-format -i $files
        
        # 重新添加修改后的文件到暂存区
        git add $files
        echo -e "${GREEN}✅ 格式修复完成，文件已重新暂存${NC}"
    else
        echo ""
        echo -e "${RED}❌ 提交被取消${NC}"
        echo -e "${YELLOW}💡 请手动修复格式问题后重新提交${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}✅ 代码格式检查通过${NC}"
exit 0 