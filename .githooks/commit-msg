#!/bin/sh

# 颜色定义（兼容sh）
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 读取提交信息第一行
first_line=$(head -n1 "$1")

# 使用通用 POSIX 正则进行检查：以 task_数字 或 bug_数字 开头
# 注意：有些版本的 Bash/sh 不支持 [[ ]]，用 POSIX 兼容的 grep 方式
echo "$first_line" | grep -Eqi '^task_[0-9]+|^bug_[0-9]+' || {
	printf "${RED}❌ 提交信息格式错误${NC}\n"
	printf "\n"
	printf "${YELLOW}📋 格式要求：${NC}\n"
	printf "   第一行必须以 ${CYAN}'task_数字'${NC} 或 ${CYAN}'bug_数字'${NC} 开头\n"
	printf "\n"
	printf "${GREEN}✅ 正确示例：${NC}\n"
	printf "   ${BLUE}task_188，激活MCS坐标系${NC}\n"
	printf "   ${BLUE}bug_42，修复界面显示问题${NC}\n"
	printf "\n"
	printf "${RED}❌ 当前提交信息：${NC}\n"
	printf "   ${YELLOW}$first_line${NC}\n"
	exit 1
}