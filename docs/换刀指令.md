📚 本文说明换刀相关G代码指令，如何围绕OpenCnc来实现、并满足业务功能。

# 🔧 1. 换刀指令

## ⚙️ 1.1. T指令

OpenCnc译码模块不支持车床中常用的**T0502**这种同时完成2个功能(下面)的G指令：
* 🔢 05指定换刀的编号。
* 🔧 02指定刀长补偿的编号。

但也可以实现这项功能，在她的ParaDef.h中share memory有如下结构体：

```cpp
//注意：实际使用刀具数为 TOOL_NUM， 最大刀具数为 TOOL_NUM_MAX：100个
struct TOOLS_PARA_PTR
{
    TOOL_PARA_PTR m_ToolPara[TOOL_NUM_MAX];
    TOOL_PARA_PTR_EX m_ToolParaEx[TOOL_NUM_MAX];
};

//刀具参数
struct TOOL_PARA_PTR // 40
{
    double m_ToolR; //刀具半径
    double m_ToolDComp; //刀具半径补偿值
    double m_ToolLen[8]; //刀具长度：XY+6Z
    double m_ToolHComp[8]; //刀具长度补偿值：XY+6Z

    // 省略
};
```

TOOLS_PARA_PTR.m_ToolPara表示最大支持100个参数不同的刀具TOOL_PARA_PTR，每一个TOOL_PARA_PTR都有自己的刀长(8个轴)和补偿，半径和补偿数据。

于是刀库管理中结合这些结构体，设计如下的刀具数据格式：

| 🆔 刀具编号(m_ToolPara[i]) | 🔢 刀号 | 🔧 刀沿号 | 📏 刀长X轴(m_ToolLen[0]) | 📏 刀长Z轴(m_ToolLen[2]) | ⭕ 刀半径(m_ToolR) | 🔧 刀长X轴-补偿(m_ToolHComp[0]) | 🔧 刀长Z轴-补偿(m_ToolHComp[2]) | ⭕ 刀半径-补偿(m_ToolDComp) |
|---------|-------------|----------|----------|----------|----------|----------|----------|----------|
| 1       | 1   |  1    |   11   | 21   | 1.11   | 1   |  2   | 0.11   |
| 2       | 1   |  2    |   21   | 31   | 2.11   | 2   |  3   | 1.11   |
| 3       | 2   |  1    |   11   | 21   | 1.11   | 1   |  2   | 0.11   |
| 4       | 2   |  2    |   21   | 31   | 2.11   | 2   |  3   | 1.11   |
| 5       | 3   |  1    |   11   | 21   | 1.11   | 1   |  2   | 0.11   |
| 6       | 4   |  1    |   11   | 21   | 1.11   | 1   |  2   | 0.11   |
| 7       | 5   |  1    |   21   | 31   | 2.11   | 2   |  3   | 1.11   |
| 8       | 5   |  2    |   11   | 21   | 1.11   | 1   |  2   | 0.11   |

那么对于**T0502**，需要二次开发者使用宏程序将**T0502**转换为下面G代码：

```gcode
T05
G43
H08
```

* 🔢 **T05**表示选择刀号是5。**02**表示选择刀号是5 + 刀沿是02的刀具，对应刀具编号是8。
* ⚙️ **G43**表示启用刀的长度补偿。
* 🔧 **H08**表示最后选择的是刀具编号是8的刀具。

## 🔧 1.2. D指令

**D08**表示开启刀具的半径补偿，**08**对应上述表格中的**刀具编号**。但是你可以执行**T0502 D02**，这时候OpenCnc会使用**刀具编号8**的刀长补偿数据，和**刀具编号2**的刀半径补偿数据。

⚠️ 这里要小心。

把这里弄清楚了才能进入下面。

# 🎯 2. 仿真译码

综上所述，换刀涉及到3个指令：
* 🔢 T指令：选择刀具。
* ⭕ D指令：开启刀具的半径补偿。
* 🔧 H指令：选择刀具的刀长补偿。

当使用OpenCnc的仿真译码功能时，每一条G代码都会被译码为NC_MOTION结构体，其中TNum、HNum、DNum分别对应T指令、H指令、D指令的参数。当用户点击**半径预览**功能时，我们可以把当前G代码中所有G指令对应的D指令对应的半径补偿数值，显示成为1张表格，运行用户来修改每一条数值，然后点击**应用**按钮，再把修改后的数值应用到改刀具编号的半径补偿参数中，同时保留当前G代码的预览图，并再次仿真译码、绘制新的图形，从而让用户直观看到旧的刀具半径补偿数值和新的刀具半径补偿数值的差异。

当然，当用户退出预览功能时，我们要把修改的半径参数改回之前的数值。