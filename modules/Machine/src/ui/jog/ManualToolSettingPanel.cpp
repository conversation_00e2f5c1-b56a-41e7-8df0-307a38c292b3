#include "ManualToolSettingPanel.h"

#include <QGridLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QVBoxLayout>

#include "StyledComboBox.h"
#include "StyledLineEdit.h"
#include "ToolAnimationWidget.h"
#include "core/AppLogger.h"
#include "core/CncManager.h"
#include "core/ToolSelectionDialog.h"
#include "public/IServiceBus.h"

// 创建参数名称标签的辅助函数
static QLabel* createParamNameLabel(const QString& text) {
    QLabel* label = new QLabel(text);
    label->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    return label;
}

// 创建参数值标签的辅助函数
static QLabel* createParamValueLabel(const QString& text = "---") {
    QLabel* label = new QLabel(text);
    label->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    label->setMinimumWidth(80);  // 保证有足够空间显示数值
    return label;
}

ManualToolSettingPanel::ManualToolSettingPanel(IServiceBus* serviceBus, QWidget* parent)
    : FocusablePanel(parent),
      m_toolAnimationWidget(nullptr),
      m_rightPanelLayout(nullptr),
      // 初始化标签
      m_labelT(nullptr),
      m_selectedToolNameValueLabel(nullptr),
      m_labelD(nullptr),
      m_selectedToolDValueLabel(nullptr),
      m_labelST(nullptr),
      m_selectedToolSTValueLabel(nullptr),
      m_referencePointLabel(nullptr),
      m_referencePointComboBox(nullptr),
      m_activeParamNameLabel(nullptr),
      m_activeParamValueEdit(nullptr),
      m_toolGeomTitleLabel(nullptr),
      m_toolGeomXNameLabel(nullptr),
      m_toolGeomXValueLabel(nullptr),
      m_toolGeomZNameLabel(nullptr),
      m_toolGeomZValueLabel(nullptr),
      m_toolGeomRNameLabel(nullptr),
      m_toolGeomRValueLabel(nullptr),
      m_savedPositionTitleLabel(nullptr),
      m_savedPositionXNameLabel(nullptr),
      m_savedPositionXValueLabel(nullptr),
      m_savedPositionZNameLabel(nullptr),
      m_savedPositionZValueLabel(nullptr),
      m_serviceBus(serviceBus),
      m_currentMode(MeasurementMode::NONE) {
    setObjectName("ManualToolSettingPanel");
    setTitleText(tr("手动测量"));
    setStatusBarVisible(false);
    setupContent();
    processSelectedTool(ToolInfo());
    updateDisplayForMode(MeasurementMode::NONE);

    // 尝试为整个 ManualToolSettingPanel 请求更大的尺寸
    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
}

ManualToolSettingPanel::~ManualToolSettingPanel() {}

QString ManualToolSettingPanel::getToolImagePath(const ToolInfo& tool) const {
    if (tool.name.empty()) {
        return QString();  // 没有刀具时不显示图像
    }
    // 示例：刀具名 "ROUGHING_T80 A" -> 资源 ":/machinemodule/ToolSetting/roughing_t80_a.png"
    // 要求 qrc 中图片命名规范，并放在 'machinemodule/ToolSetting' 前缀下。
    // QString resourceName = tool.toolName.toLower().replace(" ", "_");
    // return QString(":/machinemodule/ToolSetting/%1.png").arg(resourceName);
    QString resourceName = ":/machinemodule/ToolSetting/ToolMeasure_X_TurningTool_1";
    return resourceName;
}

void ManualToolSettingPanel::setupContent() {
    QHBoxLayout* mainPageLayout = new QHBoxLayout(contentArea());

    // --- 左侧面板 (动画) ---
    m_toolAnimationWidget = new ToolAnimationWidget(this);
    m_toolAnimationWidget->setMinimumSize(320, 240);
    mainPageLayout->addWidget(m_toolAnimationWidget, 4);  // 拉伸因子 4

    // --- 右侧面板 (参数) ---
    QWidget* rightPanelContainer = new QWidget();
    m_rightPanelLayout = new QGridLayout(rightPanelContainer);  // 右侧布局
    m_rightPanelLayout->setSpacing(10);

    // 第0行: T [ToolName] D [D_Value]
    m_labelT = createParamNameLabel("T");
    m_selectedToolNameValueLabel = new StyledLineEdit();
    m_selectedToolNameValueLabel->setAttribute(Qt::WA_StyledBackground, true);
    m_selectedToolNameValueLabel->setText("");
    m_labelD = createParamNameLabel("D");
    m_selectedToolDValueLabel = new StyledComboBox();
    m_selectedToolDValueLabel->setAttribute(Qt::WA_StyledBackground, true);
    m_selectedToolDValueLabel->setFixedWidth(20);
    // 添加D值选项 1-9
    for (int i = 1; i <= 9; ++i) {
        m_selectedToolDValueLabel->addItem(QString::number(i));
    }

    QHBoxLayout* toolNameLayout = new QHBoxLayout();
    toolNameLayout->addWidget(m_selectedToolNameValueLabel);
    toolNameLayout->addWidget(m_labelD);
    toolNameLayout->addWidget(m_selectedToolDValueLabel);

    m_rightPanelLayout->addWidget(m_labelT, 0, 0);
    m_rightPanelLayout->addLayout(toolNameLayout, 0, 1, 1, 1);

    // 第1行: ST [ST_Value]
    m_labelST = createParamNameLabel("ST");
    m_selectedToolSTValueLabel = new StyledComboBox();
    m_selectedToolSTValueLabel->setAttribute(Qt::WA_StyledBackground, true);
    m_selectedToolSTValueLabel->setFixedWidth(20);
    // 添加ST值选项 1-12
    for (int i = 1; i <= 12; ++i) {
        m_selectedToolSTValueLabel->addItem(QString::number(i));
    }
    m_rightPanelLayout->addWidget(m_labelST, 1, 0);
    m_rightPanelLayout->addWidget(m_selectedToolSTValueLabel, 1, 1, Qt::AlignLeft);  // 靠左

    // 第2行: 参考点（仅在Z操作时显示）
    QHBoxLayout* referencePointLayout = new QHBoxLayout();
    m_referencePointLabel = createParamNameLabel("参考点");
    m_referencePointComboBox = new StyledComboBox();
    m_referencePointComboBox->setAttribute(Qt::WA_StyledBackground, true);
    // 添加参考点选项
    m_referencePointComboBox->addItem("工件边沿");
    m_referencePointComboBox->addItem("主主轴卡盘");
    referencePointLayout->addWidget(m_referencePointLabel);
    referencePointLayout->addWidget(m_referencePointComboBox);
    m_rightPanelLayout->addLayout(referencePointLayout, 2, 0, 1, 2, Qt::AlignLeft);
    // 初始隐藏参考点行
    m_referencePointLabel->setVisible(false);
    m_referencePointComboBox->setVisible(false);

    // 第3行: 当前参数 [ParamName] [ParamValueEdit]
    m_activeParamNameLabel = createParamNameLabel("X0/Z0");  // 例如 X0
    m_activeParamValueEdit = new StyledLineEdit();
    m_activeParamValueEdit->setAttribute(Qt::WA_StyledBackground, true);
    // 设置为数字输入模式，允许正负数和小数
    m_activeParamValueEdit->setInputMode(StyledLineEdit::Double);
    m_activeParamValueEdit->setDecimals(3);                   // 3位小数精度
    m_activeParamValueEdit->setRealTimeValidation(true);      // 启用实时验证
    m_activeParamValueEdit->setPlaceholderText(tr("0.000"));  // 提示数字格式
    m_activeParamValueEdit->setText("0.000");                 // 设置默认值

    // 设置状态相关对齐方式：非编辑状态右对齐，编辑状态左对齐
    m_activeParamValueEdit->setAlignmentForState(StyledLineEdit::Normal, Qt::AlignRight | Qt::AlignVCenter);
    m_activeParamValueEdit->setAlignmentForState(StyledLineEdit::Selected, Qt::AlignRight | Qt::AlignVCenter);
    m_activeParamValueEdit->setAlignmentForState(StyledLineEdit::Editing, Qt::AlignLeft | Qt::AlignVCenter);

    m_rightPanelLayout->addWidget(m_activeParamNameLabel, 3, 0);
    m_rightPanelLayout->addWidget(m_activeParamValueEdit, 3, 1, 1, 1, Qt::AlignLeft);  // 输入框占1列

    // 刀具几何数据（第2、3列）
    m_toolGeomTitleLabel = createParamNameLabel(tr("刀具数据"));
    m_rightPanelLayout->addWidget(m_toolGeomTitleLabel, 0, 2, 1, 2, Qt::AlignLeft);  // 跨2列

    m_toolGeomXNameLabel = createParamNameLabel("X");
    m_toolGeomXValueLabel = createParamValueLabel();
    m_rightPanelLayout->addWidget(m_toolGeomXNameLabel, 1, 2, Qt::AlignRight);
    m_rightPanelLayout->addWidget(m_toolGeomXValueLabel, 1, 3, Qt::AlignRight);

    m_toolGeomZNameLabel = createParamNameLabel("Z");
    m_toolGeomZValueLabel = createParamValueLabel();
    m_rightPanelLayout->addWidget(m_toolGeomZNameLabel, 2, 2, Qt::AlignRight);
    m_rightPanelLayout->addWidget(m_toolGeomZValueLabel, 2, 3, Qt::AlignRight);

    m_toolGeomRNameLabel = createParamNameLabel("R");
    m_toolGeomRValueLabel = createParamValueLabel();
    m_rightPanelLayout->addWidget(m_toolGeomRNameLabel, 3, 2, Qt::AlignRight);
    m_rightPanelLayout->addWidget(m_toolGeomRValueLabel, 3, 3, Qt::AlignRight);

    // 已保存位置数据（第2、3列，刀具几何下方）
    m_savedPositionTitleLabel = createParamNameLabel(tr("保存的位置"));
    m_rightPanelLayout->addWidget(m_savedPositionTitleLabel, 5, 2, 1, 2, Qt::AlignLeft);

    m_savedPositionXNameLabel = createParamNameLabel("X");
    m_savedPositionXValueLabel = createParamValueLabel("---");
    m_rightPanelLayout->addWidget(m_savedPositionXNameLabel, 6, 2, Qt::AlignRight);
    m_rightPanelLayout->addWidget(m_savedPositionXValueLabel, 6, 3, Qt::AlignRight);

    m_savedPositionZNameLabel = createParamNameLabel("Z");
    m_savedPositionZValueLabel = createParamValueLabel("---");
    m_rightPanelLayout->addWidget(m_savedPositionZNameLabel, 7, 2, Qt::AlignRight);
    m_rightPanelLayout->addWidget(m_savedPositionZValueLabel, 7, 3, Qt::AlignRight);

    // 拉伸因子与占位符
    m_rightPanelLayout->setColumnStretch(1, 1);  // 输入框列拉伸
    m_rightPanelLayout->setColumnStretch(3, 0);  // 数值列不拉伸
    m_rightPanelLayout->setRowStretch(8, 1);     // 右侧底部留空
    m_rightPanelLayout->setRowMinimumHeight(2, 22);
    m_rightPanelLayout->addItem(new QSpacerItem(1, 1, QSizePolicy::Minimum, QSizePolicy::Expanding), 8, 0, 1, 4);

    rightPanelContainer->setObjectName("manualToolSettingPanel");
    rightPanelContainer->setLayout(m_rightPanelLayout);
    mainPageLayout->addWidget(rightPanelContainer, 3);  // 右侧拉伸因子3

    contentArea()->setLayout(mainPageLayout);
}

void ManualToolSettingPanel::updateDisplayForMode(MeasurementMode mode) {
    m_currentMode = mode;
    QString paramNameText;
    bool showReferencePoint = false;                                       // 是否显示参考点行
    QString chuckImagePath = ":/machinemodule/Chuck/Chuck_outline_Z.png";  // 默认Z轴轮廓
    bool startAnim = false;

    QPointF chuckRefPoint(0, 0);
    ToolAnimationWidget::AnimationDirection animationDirection = ToolAnimationWidget::AnimationDirection::RightToLeft;

    if (m_toolAnimationWidget) {
        m_toolAnimationWidget->stopAnimation();
    }

    switch (mode) {
        case MeasurementMode::NONE:
            paramNameText = tr("Z0/X0");
            showReferencePoint = false;
            break;
        case MeasurementMode::SET_Z_REFERENCE:  // Z 操作
            paramNameText = tr("Z0");
            chuckImagePath = ":/machinemodule/Chuck/Chuck_outline_Z.png";
            chuckRefPoint = QPointF(159, 66);
            animationDirection = ToolAnimationWidget::AnimationDirection::RightToLeft;
            startAnim = true;
            showReferencePoint = true;  // Z操作时显示参考点
            break;
        case MeasurementMode::SET_X_WORKPIECE:  // X 操作
            paramNameText = tr("X0");
            chuckImagePath = ":/machinemodule/Chuck/Chuck_outline_X.png";  // X模式切换卡盘图片
            chuckRefPoint = QPointF(127, 40);
            animationDirection = ToolAnimationWidget::AnimationDirection::TopToBottom;
            startAnim = true;
            showReferencePoint = false;
            break;
    }

    // 控制参考点行的显示/隐藏
    if (m_referencePointLabel && m_referencePointComboBox) {
        m_referencePointLabel->setVisible(showReferencePoint);
        m_referencePointComboBox->setVisible(showReferencePoint);
    }

    if (m_toolAnimationWidget) {
        // 加载卡盘图像并指定参考点
        // 直接使用在 switch 语句中为当前模式确定的 chuckRefPoint
        m_toolAnimationWidget->loadChuckImage(chuckImagePath, chuckRefPoint);
    }

    m_activeParamNameLabel->setText(paramNameText);

    if (startAnim && m_toolAnimationWidget && m_toolAnimationWidget->isToolLoaded()) {
        LOG_DEBUG("准备启动动画。模式:{} 方向:{}", static_cast<int>(mode),
                  (animationDirection == ToolAnimationWidget::AnimationDirection::RightToLeft ? "RightToLeft"
                                                                                              : "TopToBottom"));
        m_toolAnimationWidget->startApproachAnimation(1500, animationDirection, -1, 1000);
    } else if (startAnim) {
        LOG_WARN("无法启动动画，ToolAnimationWidget为空或刀具未加载。");
    }
}

void ManualToolSettingPanel::actionSelectTool() {
    LOG_INFO("调用 actionSelectTool() 以选择刀具");

    int presetToolNumber = -1;
    if (m_currentSelectedTool.isValid) {
        presetToolNumber = m_currentSelectedTool.number;
    }

    ToolSelectionDialog::show(
        m_serviceBus, this,
        [this](const ToolInfo& selectedTool) {
            LOG_INFO("从刀具选择对话框中选择了刀具：T{}", selectedTool.number);
            processSelectedTool(selectedTool);
        },
        presetToolNumber);
}

void ManualToolSettingPanel::actionSavePosition() {
    LOG_INFO("调用 actionSavePosition()");

    // 获取CNC管理器
    auto* cncManager = CncManager::getInstance();
    if (!cncManager || !cncManager->isInitialized()) {
        LOG_ERROR("保存位置：CNC系统未初始化");
        return;
    }

    // 获取当前工件坐标位置
    PointXD currentPosition;
    int channelId = 0;  // 假设使用通道0，实际可能需要从配置获取
    ErrorCode result = cncManager->getWorkPosition(channelId, currentPosition);
    if (result != ErrorCode::Success) {
        LOG_ERROR("保存位置：获取当前WCS坐标失败");
        return;
    }

    // 根据当前模式保存对应轴的位置
    bool positionSaved = false;
    if (m_currentMode == MeasurementMode::SET_X_WORKPIECE) {
        // 保存X轴位置
        if (currentPosition.coordinates.find(0) != currentPosition.coordinates.end()) {
            double xPosition = currentPosition.coordinates[0];
            m_savedPositionXValueLabel->setText(QString::number(xPosition, 'f', 3));
            m_savedPositionZValueLabel->setText("---");
            LOG_INFO("已保存 X0 工件坐标位置: {} mm", xPosition);
            positionSaved = true;
        } else {
            LOG_WARN("无法获取X轴坐标");
        }
    } else if (m_currentMode == MeasurementMode::SET_Z_REFERENCE) {
        // 保存Z轴位置
        if (currentPosition.coordinates.find(2) != currentPosition.coordinates.end()) {
            double zPosition = currentPosition.coordinates[2];
            m_savedPositionZValueLabel->setText(QString::number(zPosition, 'f', 3));
            m_savedPositionXValueLabel->setText("---");
            LOG_INFO("已保存 Z0 参考坐标位置: {} mm", zPosition);
            positionSaved = true;
        } else {
            LOG_WARN("无法获取Z轴坐标");
        }
    } else {
        LOG_WARN("保存位置：当前不在有效的测量模式中");
        return;
    }

    if (positionSaved) {
        // 保存成功后，清空输入框并退出当前模式
        LOG_INFO("位置保存成功，已退出测量模式");
    }
}

void ManualToolSettingPanel::actionSetX() { updateDisplayForMode(MeasurementMode::SET_X_WORKPIECE); }

void ManualToolSettingPanel::actionSetZ() { updateDisplayForMode(MeasurementMode::SET_Z_REFERENCE); }

void ManualToolSettingPanel::actionSetLength() {
    LOG_INFO("调用 actionSetLength()");

    // 检查是否选择了刀具
    if (!m_currentSelectedTool.isValid) {
        LOG_UI("未选择刀具");
        return;
    }

    // 检查选择的刀具是否当前刀具
    auto* cncManager = CncManager::getInstance();
    if (!cncManager || !cncManager->isInitialized()) {
        LOG_UI("CNC系统未初始化");
        return;
    }
    ToolInfo currentTool;
    if (cncManager->getCurrentToolInfo(m_channelId, currentTool) != ErrorCode::Success) {
        LOG_UI("获取当前刀具信息失败");
        return;
    }
    if (currentTool.number != m_currentSelectedTool.number) {
        LOG_UI("请按[启动]按键换刀");
        return;
    }

    // 获取零偏数据管理器实例
    auto* offsetManager = OffsetDataManager::getInstance();
    if (!offsetManager) {
        LOG_UI("零偏数据管理器未初始化");
        return;
    }

    // 检查是否有保存的位置数据
    bool hasXPosition = (m_savedPositionXValueLabel->text() != "---" && !m_savedPositionXValueLabel->text().isEmpty());
    bool hasZPosition = (m_savedPositionZValueLabel->text() != "---" && !m_savedPositionZValueLabel->text().isEmpty());

    // 如果没有保存的位置数据，先自动保存当前位置
    if (!hasXPosition && !hasZPosition) {
        LOG_INFO("设置长度：没有保存位置数据，自动保存当前位置");

        // 获取当前工件坐标位置
        PointXD currentPosition;
        ErrorCode result = cncManager->getWorkPosition(m_channelId, currentPosition);
        if (result != ErrorCode::Success) {
            LOG_UI("获取当前WCS坐标失败，无法自动保存位置");
            return;
        }

        // 根据当前测量模式自动保存对应轴的位置
        if (m_currentMode == MeasurementMode::SET_X_WORKPIECE) {
            if (currentPosition.coordinates.find(0) != currentPosition.coordinates.end()) {
                double xPosition = currentPosition.coordinates[0];
                m_savedPositionXValueLabel->setText(QString::number(xPosition, 'f', 3));
                m_savedPositionZValueLabel->setText("---");
                hasXPosition = true;
                LOG_INFO("自动保存 X0 工件坐标位置: {} mm", xPosition);
            }
        } else if (m_currentMode == MeasurementMode::SET_Z_REFERENCE) {
            if (currentPosition.coordinates.find(2) != currentPosition.coordinates.end()) {
                double zPosition = currentPosition.coordinates[2];
                m_savedPositionZValueLabel->setText(QString::number(zPosition, 'f', 3));
                m_savedPositionXValueLabel->setText("---");
                hasZPosition = true;
                LOG_INFO("自动保存 Z0 参考坐标位置: {} mm", zPosition);
            }
        } else {
            LOG_UI("当前不在有效的测量模式中，无法确定保存哪个轴的位置");
            return;
        }

        // 重新检查是否成功保存了位置
        if (!hasXPosition && !hasZPosition) {
            LOG_UI("自动保存位置失败");
            return;
        }
    }

    // 构造期望的工件坐标位置（基于用户输入的值）
    PointXD desiredWorkPosition;

    // 获取用户输入的期望工件坐标值
    bool ok;
    double userInputValue = m_activeParamValueEdit->text().toDouble(&ok);
    if (!ok) {
        LOG_WARN("设置长度：无法解析用户输入的坐标值: {}", m_activeParamValueEdit->text().toStdString());
        return;
    }

    // 根据当前测量模式和保存的位置确定要设置的轴
    if (hasXPosition && (m_currentMode == MeasurementMode::SET_X_WORKPIECE || m_currentMode == MeasurementMode::NONE)) {
        desiredWorkPosition.coordinates[0] = userInputValue;  // X轴索引为0
        LOG_DEBUG("期望X轴工件坐标: {} mm", userInputValue);
    }

    if (hasZPosition && (m_currentMode == MeasurementMode::SET_Z_REFERENCE || m_currentMode == MeasurementMode::NONE)) {
        desiredWorkPosition.coordinates[2] = userInputValue;  // Z轴索引为2
        LOG_DEBUG("期望Z轴工件坐标: {} mm", userInputValue);
    }

    // 检查是否有有效的期望坐标
    if (desiredWorkPosition.coordinates.empty()) {
        LOG_WARN("设置长度：没有有效的期望工件坐标数据");
        return;
    }

    // 使用OffsetDataManager计算刀具长度
    PointXD calculatedToolOffset;
    ValidationResult result =
        offsetManager->calculateRequiredToolLengthAuto(m_channelId, desiredWorkPosition, calculatedToolOffset);

    if (!result.isValid) {
        LOG_UI("设置长度：{}", result.errorMessage.toStdString());
        return;
    }

    // 输出警告信息（如果有）
    for (const QString& warning : result.warnings) {
        LOG_WARN("刀具长度计算警告: {}", warning.toStdString());
    }

    // 复制当前选中的刀具信息用于修改
    ToolInfo toolToUpdate = m_currentSelectedTool;
    bool hasUpdates = false;

    // 将计算结果应用到刀具几何参数
    auto xIt = calculatedToolOffset.coordinates.find(0);
    auto zIt = calculatedToolOffset.coordinates.find(2);

    if (xIt != calculatedToolOffset.coordinates.end()) {
        toolToUpdate.geometryLengthX = xIt->second;
        hasUpdates = true;
        LOG_INFO("计算得到的X轴刀具长度: {} mm", xIt->second);
    }

    if (zIt != calculatedToolOffset.coordinates.end()) {
        toolToUpdate.geometryLengthZ = zIt->second;
        hasUpdates = true;
        LOG_INFO("计算得到的Z轴刀具长度: {} mm", zIt->second);
    }

    // 如果有更新，保存到CNC系统
    if (hasUpdates) {
        ErrorCode setResult = cncManager->setToolParameters(toolToUpdate);
        if (setResult == ErrorCode::Success) {
            // 更新界面显示的刀具几何数据
            m_toolGeomXValueLabel->setText(QString::number(toolToUpdate.geometryLengthX, 'f', 3));
            m_toolGeomZValueLabel->setText(QString::number(toolToUpdate.geometryLengthZ, 'f', 3));

            // 更新本地选中的刀具信息
            m_currentSelectedTool = toolToUpdate;

            // BUG-225
            ToolListManager::instance()->updateTool(m_currentSelectedTool);

            LOG_INFO("刀具T{} 长度参数设置成功：X={}, Z={} (使用OffsetDataManager计算)", toolToUpdate.number,
                     toolToUpdate.geometryLengthX, toolToUpdate.geometryLengthZ);
        } else {
            LOG_ERROR("设置刀具T{} 参数失败", toolToUpdate.number);
        }
    } else {
        LOG_WARN("设置长度：OffsetDataManager计算结果中没有有效的轴数据");
    }
}

void ManualToolSettingPanel::processSelectedTool(const ToolInfo& tool) {
    m_currentSelectedTool = tool;
    if (m_toolAnimationWidget) {
        QString toolImagePath = getToolImagePath(tool);
        if (!toolImagePath.isEmpty()) {
            // 为刀具图像指定一个参考点，这里示例为左上角(0,0)
            // 实际应用中，应根据图像内容和期望的对齐行为调整此参考点 (例如刀尖)
            QPointF toolRefPoint = QPointF(0, 195);
            m_toolAnimationWidget->loadToolImage(toolImagePath, toolRefPoint);
            LOG_INFO("加载刀具图像{} 参考点:({}, {})", toolImagePath.toStdString(), toolRefPoint.x(), toolRefPoint.y());
        } else {
            m_toolAnimationWidget->loadToolImage(QString(), QPointF());  // 清除刀具图像
            LOG_INFO("清除刀具图像，因路径为空。");
        }
    }

    if (!tool.name.empty()) {
        m_selectedToolNameValueLabel->setText(QString::fromStdString(tool.name));
        // 设置D值下拉框的选中项
        int dIndex = m_selectedToolDValueLabel->findText(QString::number(tool.dNumber));
        if (dIndex >= 0) {
            m_selectedToolDValueLabel->setCurrentIndex(dIndex);
        }
        // 设置ST值下拉框的选中项
        int stIndex = m_selectedToolSTValueLabel->findText(QString::number(tool.sisterToolNumber));
        if (stIndex >= 0) {
            m_selectedToolSTValueLabel->setCurrentIndex(stIndex);
        }

        m_toolGeomXValueLabel->setText(QString::number(tool.geometryLengthX, 'f', 3));
        m_toolGeomZValueLabel->setText(QString::number(tool.geometryLengthZ, 'f', 3));
        m_toolGeomRValueLabel->setText(QString::number(tool.geometryRadius, 'f', 3));
    } else {
        m_selectedToolNameValueLabel->setText(tr(""));
        m_selectedToolDValueLabel->setCurrentIndex(0);   // 设置为第一项
        m_selectedToolSTValueLabel->setCurrentIndex(0);  // 设置为第一项

        m_toolGeomXValueLabel->setText("---");
        m_toolGeomZValueLabel->setText("---");
        m_toolGeomRValueLabel->setText("---");
    }
    // 切换刀具或取消选择时清空保存位置
    m_savedPositionXValueLabel->setText("---");
    m_savedPositionZValueLabel->setText("---");  // 初始化保存Z
    updateDisplayForMode(m_currentMode);
}

void ManualToolSettingPanel::handleMenuAction(const QString& actionId, const QVariantMap& states) {
    if (actionId == "machine.measureTool.manual.selectTool") {
        actionSelectTool();
    } else if (actionId == "machine.measureTool.manual.setX") {
        actionSetX();
    } else if (actionId == "machine.measureTool.manual.setZ") {
        actionSetZ();
    } else if (actionId == "machine.measureTool.manual.setLength") {
        actionSetLength();
    } else if (actionId == "machine.measureTool.manual.savePosition") {
        actionSavePosition();
    }
}

void ManualToolSettingPanel::onCycleStartPressed() {
    LOG_INFO("调用 onCycleStartPressed()");

    // 检查是否选择了刀具
    if (!m_currentSelectedTool.isValid) {
        LOG_UI("未选择刀具");
        return;
    }

    // 检查选择的刀具是否当前刀具
    auto* cncManager = CncManager::getInstance();
    if (!cncManager || !cncManager->isInitialized()) {
        LOG_UI("CNC系统未初始化");
        return;
    }
    ToolInfo currentTool;
    if (cncManager->getCurrentToolInfo(m_channelId, currentTool) == ErrorCode::Success &&
        currentTool.number == m_currentSelectedTool.number) {
        LOG_UI("已经是当前刀具");
        return;
    }

    // 生成换刀G代码，形式 Txxyy M06
    // xx - 刀号
    // yy - 刀沿
    // M06 - 换刀指令
    QString gCode = QString::asprintf("T%02d%02d M06", m_currentSelectedTool.number, m_currentSelectedTool.dNumber);
    LOG_DEBUG("生成的G代码: {}", gCode.toStdString());

    // 直接执行MDI命令
    ErrorCode result = cncManager->executeMacro(m_channelId, gCode.toStdString());
    if (result == ErrorCode::Success) {
        LOG_UI("正在换刀...");
    } else {
        LOG_UI("换刀命令执行失败");
    }
}