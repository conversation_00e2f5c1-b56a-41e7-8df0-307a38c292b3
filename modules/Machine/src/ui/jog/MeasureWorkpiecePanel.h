#ifndef MEASUREWORKPIECEPANEL_H
#define MEASUREWORKPIECEPANEL_H

#include "FocusablePanel.h"
#include "IServiceBus.h"
#include "core/CncManager.h"
#include "core/ToolListManager.h"
#include "public/ICncInterface.h"

// 前向声明
class QLabel;
class QGridLayout;
class StyledLineEdit;
class StyledComboBox;
class ToolAnimationWidget;

class MeasureWorkpiecePanel : public FocusablePanel {
    Q_OBJECT

   public:
    explicit MeasureWorkpiecePanel(IServiceBus* serviceBus, QWidget* parent = nullptr);
    ~MeasureWorkpiecePanel() override;

    void handleMenuAction(const QString& actionId, const QVariantMap& states) override;
    void onCycleStartPressed();
    void reset();

   private slots:
    // 菜单动作
    void actionSaveZeroOffset();

    // 控件信号
    void onOffsetModeChanged(int index);
    void onOffsetCommandChanged(const QString& text);

    // 参考点值编辑完成
    void onReferencePointValueEditingFinished();

   private:
    enum class MeasurementMode { NONE, MEASURE_Z };

    enum class OffsetValueState {
        HIDDEN = 0,     // 隐藏
        NO_RESULT = 1,  // 无结果
        HAS_RESULT = 2  // 获得结果
    };

    void setupContent();
    void setupConnections();
    void updateDisplayForMode(MeasurementMode mode);
    void processSelectedWorkpieceZeroOffset(const QString& workpieceZeroOffsetName = "");
    QString getWorkpieceZeroOffsetImagePath(const QString& workpieceZeroOffsetName) const;
    void updateMeasurementResultState(OffsetValueState state);
    void updateMeasurementControlsVisibility(bool showControls);
    void updateOffsetValueState(OffsetValueState state);

    int getAxisIndex(const std::string& axisName) const;

    double getAxisWorkOffsetValue(const std::string& axisName, const std::string& workOffsetName) const;

    // UI组件
    ToolAnimationWidget* m_toolAnimationWidget = nullptr;
    QGridLayout* m_rightPanelLayout = nullptr;

    // 右侧 - 测量参数
    StyledComboBox* m_offsetModeComboBox = nullptr;     // 测量零偏模式
    StyledComboBox* m_offsetCommandComboBox = nullptr;  // 零偏指令
    QWidget* m_offsetCommandPlaceholder = nullptr;      // 零偏指令占位符

    // 右侧 - 测量参数控件
    QLabel* m_referencePointValueLabel = nullptr;
    StyledLineEdit* m_referencePointValue = nullptr;

    // 右侧 - 测量结果显示控件
    QLabel* m_resultTitleLabel = nullptr;

    QLabel* m_offsetValueLabel = nullptr;
    QLabel* m_offsetValue = nullptr;

    // 右侧 - 测量结果显示控件
    QLabel* m_measurePointValueLabel = nullptr;
    QLabel* m_measurePointValueZ0Label = nullptr;
    QLabel* m_measurePointValue = nullptr;
    QWidget* m_measurePointValueEditPlaceholder = nullptr;  // 测量结果编辑框占位符

    // 服务
    IServiceBus* m_serviceBus = nullptr;
    MeasurementMode m_currentMode = MeasurementMode::NONE;

    int m_channelId = 0;
    CncManager* m_cncManager = nullptr;

    // 数据
    int m_axisIndex = 0;
};

#endif  // MEASUREWORKPIECEPANEL_H