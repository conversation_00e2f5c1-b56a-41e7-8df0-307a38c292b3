#include "MeasureWorkpiecePanel.h"

#include <qlabel.h>

#include <QGridLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QVBoxLayout>

#include "ICncInterface.h"
#include "StyledComboBox.h"
#include "StyledLineEdit.h"
#include "ToolAnimationWidget.h"
#include "core/AppLogger.h"
#include "core/CncManager.h"
#include "core/OffsetDataManager.h"
#include "core/StyledMessageBox.h"
#include "core/ToolSelectionDialog.h"
#include "core/ZeroOffsetSelectionDialog.h"
#include "public/ErrorCode.h"
#include "public/IServiceBus.h"

// 创建参数名称标签的辅助函数
static QLabel* createParamNameLabel(const QString& text) {
    QLabel* label = new QLabel(text);
    label->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    return label;
}

// 创建参数值标签的辅助函数
static QLabel* createParamValueLabel(const QString& text = "0.000") {
    QLabel* label = new QLabel(text);
    label->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    label->setMinimumWidth(80);  // 保证有足够空间显示数值
    return label;
}

MeasureWorkpiecePanel::MeasureWorkpiecePanel(IServiceBus* serviceBus, QWidget* parent)
    : FocusablePanel(parent),
      m_toolAnimationWidget(nullptr),
      m_rightPanelLayout(nullptr),
      // 初始化标签
      m_offsetModeComboBox(nullptr),
      m_offsetCommandComboBox(nullptr),
      m_referencePointValueLabel(nullptr),
      m_referencePointValue(nullptr),
      m_measurePointValueLabel(nullptr),
      m_measurePointValueZ0Label(nullptr),
      m_measurePointValue(nullptr),
      m_measurePointValueEditPlaceholder(nullptr),
      m_resultTitleLabel(nullptr),  // 零偏值
      m_offsetValueLabel(nullptr),
      m_offsetValue(nullptr),
      m_serviceBus(serviceBus),
      m_currentMode(MeasurementMode::MEASURE_Z),
      m_cncManager(CncManager::getInstance()) {
    m_axisIndex = getAxisIndex("Z");

    setObjectName("MeasureWorkpiecePanel");
    setTitleText(tr("测量工件"));
    setStatusBarVisible(false);

    setupContent();
    setupConnections();

    processSelectedWorkpieceZeroOffset("G54");
    updateDisplayForMode(MeasurementMode::MEASURE_Z);
    updateMeasurementResultState(OffsetValueState::NO_RESULT);  // 初始状态：未获得结果
    updateMeasurementControlsVisibility(true);                  // 初始状态：显示测量控件
    updateOffsetValueState(OffsetValueState::NO_RESULT);        // 初始状态：无结果状态

    // 初始化占位符状态（零偏模式，所以占位符隐藏）
    if (m_offsetCommandPlaceholder) {
        m_offsetCommandPlaceholder->setVisible(false);
    }

    if (m_measurePointValueEditPlaceholder) {
        m_measurePointValueEditPlaceholder->setVisible(false);
    }

    // 尝试为整个 MeasureWorkpiecePanel 请求更大的尺寸
    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
}

MeasureWorkpiecePanel::~MeasureWorkpiecePanel() = default;

QString MeasureWorkpiecePanel::getWorkpieceZeroOffsetImagePath(const QString& workpieceZeroOffsetName) const {
    if (workpieceZeroOffsetName.isEmpty()) {
        return QString();  // 没有刀具时不显示图像
    }
    // 示例：刀具名 "ROUGHING_T80 A" -> 资源 ":/machinemodule/ToolSetting/roughing_t80_a.png"
    // 要求 qrc 中图片命名规范，并放在 'machinemodule/ToolSetting' 前缀下。
    QString resourceName = ":/machinemodule/ToolSetting/ToolMeasure_X_TurningTool_1";
    return resourceName;
}

int MeasureWorkpiecePanel::getAxisIndex(const std::string& axisName) const {
    SystemConfig systemConfig = m_cncManager->getSystemConfig();
    for (int i = 0; i < systemConfig.axesConfigs.size(); i++) {
        if (systemConfig.axesConfigs[i].name == axisName) {
            return i;
        }
    }
    return -1;
}

double MeasureWorkpiecePanel::getAxisWorkOffsetValue(const std::string& axisName,
                                                     const std::string& workOffsetName) const {
    double offsetValue = 0.000;
    int axisIndex = getAxisIndex(axisName);  // 获取轴索引
    if (axisIndex == -1) {
        return offsetValue;
    }

    if (m_cncManager && m_cncManager->isInitialized()) {
        PointXD offsetValue;
        m_cncManager->getWorkOffsetValue(0, workOffsetName, offsetValue);
        if (offsetValue.coordinates.find(axisIndex) != offsetValue.coordinates.end()) {
            return offsetValue.coordinates[axisIndex];
        }
    }

    return offsetValue;
}

void MeasureWorkpiecePanel::setupConnections() {
    connect(m_offsetModeComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), this,
            &MeasureWorkpiecePanel::onOffsetModeChanged);
    connect(m_offsetCommandComboBox, QOverload<const QString&>::of(&QComboBox::currentTextChanged), this,
            &MeasureWorkpiecePanel::onOffsetCommandChanged);
    connect(m_referencePointValue, &StyledLineEdit::editingFinished, this,
            &MeasureWorkpiecePanel::onReferencePointValueEditingFinished);
}

void MeasureWorkpiecePanel::setupContent() {
    QHBoxLayout* mainPageLayout = new QHBoxLayout(contentArea());

    // --- 左侧面板 (动画) ---
    m_toolAnimationWidget = new ToolAnimationWidget(this);
    m_toolAnimationWidget->setMinimumSize(320, 240);
    mainPageLayout->addWidget(m_toolAnimationWidget, 4);  // 拉伸因子 4

    // --- 右侧面板 (参数) ---
    QWidget* rightPanelContainer = new QWidget();
    rightPanelContainer->setAttribute(Qt::WA_StyledBackground, true);  // 启用样式背景
    m_rightPanelLayout = new QGridLayout(rightPanelContainer);         // 右侧布局
    m_rightPanelLayout->setSpacing(10);

    // 第0行: 测量零偏模式和零偏指令（同一行）
    m_offsetModeComboBox = new StyledComboBox();
    m_offsetModeComboBox->setAttribute(Qt::WA_StyledBackground, true);
    m_offsetModeComboBox->addItem(tr("仅测量"));
    m_offsetModeComboBox->addItem(tr("零偏"));
    //设置零偏模式为零偏
    m_offsetModeComboBox->setCurrentIndex(1);
    // 固定宽度
    m_offsetModeComboBox->setFixedWidth(200);

    // 零偏指令下拉框
    m_offsetCommandComboBox = new StyledComboBox();
    m_offsetCommandComboBox->setAttribute(Qt::WA_StyledBackground, true);
    m_offsetCommandComboBox->addItems({"G54", "G55", "G56", "G57", "G58", "G59"});

    // 获取当前工件零偏名称
    std::string currentWorkOffsetName;
    if (m_cncManager && m_cncManager->isInitialized()) {
        m_cncManager->getCurrentWorkOffsetName(0, currentWorkOffsetName);
        LOG_INFO("当前工件零偏名称: {}", currentWorkOffsetName);
    }
    // 如果当前工件零偏名称为空，则设置为G54
    if (currentWorkOffsetName.empty()) {
        currentWorkOffsetName = "G54";
    }
    // 设置零偏指令为当前工件零偏名称
    m_offsetCommandComboBox->setCurrentText(QString::fromStdString(currentWorkOffsetName));

    // 固定宽度
    m_offsetCommandComboBox->setFixedWidth(100);
    int comboHeight = m_offsetCommandComboBox->sizeHint().height();

    // 创建占位符
    m_offsetCommandPlaceholder = new QWidget();
    m_offsetCommandPlaceholder->setFixedSize(100, comboHeight);
    m_offsetCommandPlaceholder->setVisible(false);
    m_offsetCommandPlaceholder->setStyleSheet("background-color: transparent;");  // 透明背景

    // 创建水平布局来放置控件
    QHBoxLayout* offsetLayout = new QHBoxLayout();
    offsetLayout->addWidget(m_offsetModeComboBox);        // 模式框 - 固定位置
    offsetLayout->addWidget(m_offsetCommandComboBox);     // 指令框 - 可变显示
    offsetLayout->addWidget(m_offsetCommandPlaceholder);  // 指令框占位符 - 可变显示
    offsetLayout->setSpacing(10);
    offsetLayout->setContentsMargins(0, 0, 0, 0);

    // 设置拉伸因子，确保模式框位置固定
    offsetLayout->setStretch(0, 0);  // 模式框不拉伸，固定位置
    offsetLayout->setStretch(1, 0);  // 指令框不拉伸
    offsetLayout->setStretch(2, 0);  // 占位符不拉伸

    m_rightPanelLayout->addLayout(offsetLayout, 0, 1, Qt::AlignLeft);

    // 保证列宽度 (只保证模式框200px的最小空间)
    m_rightPanelLayout->setColumnMinimumWidth(1, 200);

    // 第1行: 测量值输入 （第2、3列）
    m_referencePointValueLabel = createParamNameLabel(tr("Z0"));
    m_referencePointValue = new StyledLineEdit();
    m_referencePointValue->setAttribute(Qt::WA_StyledBackground, true);
    // 设置为数字输入模式，允许正负数和小数
    m_referencePointValue->setInputMode(StyledLineEdit::Double);
    m_referencePointValue->setDecimals(3);                   // 3位小数精度
    m_referencePointValue->setRealTimeValidation(true);      // 启用实时验证
    m_referencePointValue->setPlaceholderText(tr("0.000"));  // 提示数字格式
    m_referencePointValue->setText("0.000");                 // 设置默认值

    // 设置状态相关对齐方式：非编辑状态右对齐，编辑状态左对齐
    m_referencePointValue->setAlignmentForState(StyledLineEdit::Normal, Qt::AlignRight | Qt::AlignVCenter);
    m_referencePointValue->setAlignmentForState(StyledLineEdit::Selected, Qt::AlignRight | Qt::AlignVCenter);
    m_referencePointValue->setAlignmentForState(StyledLineEdit::Editing, Qt::AlignLeft | Qt::AlignVCenter);

    // 创建水平布局来放置两个下拉框
    QHBoxLayout* measurementValueLayout = new QHBoxLayout();
    measurementValueLayout->addWidget(m_referencePointValueLabel, 1);  // 拉伸因子1，占1/3宽度
    measurementValueLayout->addWidget(m_referencePointValue, 3);       // 拉伸因子2，占3/4宽度
    measurementValueLayout->setSpacing(10);                            // 设置间距

    m_rightPanelLayout->addLayout(measurementValueLayout, 1, 1, Qt::AlignLeft);

    // 右边第0行 测量结果显示（第0、1列） （第2、3列）
    m_resultTitleLabel = createParamNameLabel(tr("零偏值"));
    m_rightPanelLayout->addWidget(m_resultTitleLabel, 0, 2, 1, 1, Qt::AlignLeft);  // 跨2

    // 右边第1行
    //第2行 （第2、3列）
    m_offsetValueLabel = createParamNameLabel(tr("Z"));
    m_offsetValue = createParamValueLabel("0.000");

    double offsetValue = getAxisWorkOffsetValue("Z", currentWorkOffsetName);
    m_offsetValue->setText(QString::number(offsetValue, 'f', 3));

    m_rightPanelLayout->addWidget(m_offsetValueLabel, 1, 2, 1, 1, Qt::AlignLeft);
    m_rightPanelLayout->addWidget(m_offsetValue, 1, 3, 1, 2, Qt::AlignLeft);

    // 右边第0行 测量结果显示（第4、5列） （第2、3列）
    m_measurePointValueLabel = createParamNameLabel(tr("测量值"));
    m_rightPanelLayout->addWidget(m_measurePointValueLabel, 4, 2, 1, 1, Qt::AlignLeft);  // 跨2

    // 右边第1行
    //第5行 （第2、3列）
    m_measurePointValueZ0Label = createParamNameLabel(tr("Z0"));
    m_measurePointValue = createParamValueLabel("0.000");

    // 创建测量结果编辑框占位符
    m_measurePointValueEditPlaceholder = new QWidget();
    m_measurePointValueEditPlaceholder->setFixedSize(m_measurePointValue->sizeHint().width(),
                                                     m_measurePointValue->sizeHint().height());
    m_measurePointValueEditPlaceholder->setVisible(false);
    m_measurePointValueEditPlaceholder->setStyleSheet("background-color: transparent;");  // 透明背景

    m_rightPanelLayout->addWidget(m_measurePointValueZ0Label, 5, 2, 1, 1, Qt::AlignLeft);
    m_rightPanelLayout->addWidget(m_measurePointValue, 5, 3, 1, 2, Qt::AlignLeft);
    m_rightPanelLayout->addWidget(m_measurePointValueEditPlaceholder, 5, 3, 1, 2, Qt::AlignLeft);  // 占位符覆盖同一位置

    // 拉伸因子与占位符
    m_rightPanelLayout->setColumnStretch(1, 1);      // 输入框列拉伸
    m_rightPanelLayout->setColumnStretch(3, 0);      // 数值列不拉伸
    m_rightPanelLayout->setRowStretch(6, 1);         // 右侧底部留空
    m_rightPanelLayout->setRowMinimumHeight(0, 22);  // 设置第0行最小高度
    m_rightPanelLayout->setRowMinimumHeight(1, 22);  // 设置第1行最小高度
    m_rightPanelLayout->setRowMinimumHeight(2, 22);  // 设置第2行最小高度
    m_rightPanelLayout->addItem(new QSpacerItem(1, 1, QSizePolicy::Minimum, QSizePolicy::Expanding), 6, 0, 1, 4);

    rightPanelContainer->setObjectName(
        "manualToolSettingPanel");  // 使用与ManualToolSettingPanel相同的objectName以保持样式一致
    rightPanelContainer->setLayout(m_rightPanelLayout);
    mainPageLayout->addWidget(rightPanelContainer, 3);  // 右侧拉伸因子3

    contentArea()->setLayout(mainPageLayout);
}

void MeasureWorkpiecePanel::updateDisplayForMode(MeasurementMode mode) {
    m_currentMode = mode;
    QString measurementTypeText;
    bool showAnimation = false;
    QString chuckImagePath = ":/machinemodule/Chuck/Chuck_outline_Z.png";  // 默认Z轴轮廓
    QPointF chuckRefPoint(0, 0);
    ToolAnimationWidget::AnimationDirection animationDirection = ToolAnimationWidget::AnimationDirection::RightToLeft;

    if (m_toolAnimationWidget) {
        m_toolAnimationWidget->stopAnimation();
    }

    switch (mode) {
        case MeasurementMode::NONE:
            measurementTypeText = tr("请选择测量类型");
            showAnimation = false;
            break;
        case MeasurementMode::MEASURE_Z:  // Z 测量
            measurementTypeText = tr("Z轴测量");
            chuckImagePath = ":/machinemodule/Chuck/Chuck_outline_Z.png";
            chuckRefPoint = QPointF(159, 66);
            animationDirection = ToolAnimationWidget::AnimationDirection::RightToLeft;
            showAnimation = true;
            break;
    }

    if (m_toolAnimationWidget) {
        // 加载卡盘图像并指定参考点
        m_toolAnimationWidget->loadChuckImage(chuckImagePath, chuckRefPoint);
    }

    if (showAnimation && m_toolAnimationWidget && m_toolAnimationWidget->isToolLoaded()) {
        LOG_DEBUG("准备启动动画。模式:{} 方向:{}", static_cast<int>(mode),
                  (animationDirection == ToolAnimationWidget::AnimationDirection::RightToLeft ? "RightToLeft"
                                                                                              : "TopToBottom"));
        m_toolAnimationWidget->startApproachAnimation(1500, animationDirection, -1, 1000);
    } else if (showAnimation) {
        LOG_WARN("无法启动动画，ToolAnimationWidget为空或刀具未加载。");
    }

    // 切换模式时只隐藏/显示指令框和占位符
    if (mode == MeasurementMode::NONE) {
        m_offsetCommandComboBox->setVisible(false);
        m_offsetCommandPlaceholder->setVisible(true);
    } else {
        m_offsetCommandComboBox->setVisible(true);
        m_offsetCommandPlaceholder->setVisible(false);
    }
}

void MeasureWorkpiecePanel::actionSaveZeroOffset() {
    double offsetValue = 0.0;

    LOG_DEBUG("调用 actionSaveZeroOffset()");

    double desiredWcsValue = m_referencePointValue->text().toDouble();

    // 显示当前工件坐标系位置
    m_measurePointValue->setText(QString::number(desiredWcsValue, 'f', 3));
    updateMeasurementResultState(OffsetValueState::HAS_RESULT);

    // 如果当前在测量模式下，不保存零偏
    if (m_offsetModeComboBox->currentIndex() == 0) {
        LOG_INFO("保存零偏：当前在测量模式下，不保存零偏");
        return;
    }

    // 获取零偏数据管理器实例
    auto* offsetManager = OffsetDataManager::getInstance();
    if (!offsetManager) {
        LOG_ERROR("获取零偏数据管理器失败");
        return;
    }

    // 根据期望的工件坐标系，计算当前零偏值
    bool ret = offsetManager->calculateCurrentWorkOffsetFromWCS(m_channelId, m_axisIndex, desiredWcsValue, offsetValue);
    if (!ret) {
        LOG_ERROR("计算当前零偏值失败");
        return;
    }

    // 显示当前零偏值
    m_offsetValue->setText(QString::number(offsetValue, 'f', 3));

    // 更新为获得结果状态
    updateOffsetValueState(OffsetValueState::HAS_RESULT);

    std::string currentWorkOffsetName;
    ErrorCode result = m_cncManager->getCurrentWorkOffsetName(m_channelId, currentWorkOffsetName);
    if (result != ErrorCode::Success) {
        LOG_ERROR("获取当前工件零偏名称失败");
        return;
    }

    // 如果当前工件零偏名称与当前零偏指令相同，则直接保存零偏
    std::string selectedWorkOffsetName = m_offsetCommandComboBox->currentText().toStdString();
    if (selectedWorkOffsetName == currentWorkOffsetName) {
        offsetManager->updateCurrentWorkOffset(m_channelId, m_axisIndex, offsetValue);
        return;
    }

    // 提示用户是否激活新的零偏指令
    LOG_UI("请按[启动]按钮切换坐标系 {}", selectedWorkOffsetName);
}

void MeasureWorkpiecePanel::processSelectedWorkpieceZeroOffset(const QString& workpieceZeroOffsetName) {
    if (m_toolAnimationWidget) {
        QString workpieceZeroOffsetImagePath = getWorkpieceZeroOffsetImagePath(workpieceZeroOffsetName);
        if (!workpieceZeroOffsetImagePath.isEmpty()) {
            // 为工件零偏图像指定一个参考点，这里示例为左上角(0,0)
            // 实际应用中，应根据图像内容和期望的对齐行为调整此参考点 (例如刀尖)
            QPointF workpieceZeroOffsetRefPoint = QPointF(0, 195);
            m_toolAnimationWidget->loadToolImage(workpieceZeroOffsetImagePath, workpieceZeroOffsetRefPoint);
            LOG_INFO("加载工件零偏图像{} 参考点:({}, {})", workpieceZeroOffsetImagePath.toStdString(),
                     workpieceZeroOffsetRefPoint.x(), workpieceZeroOffsetRefPoint.y());
        } else {
            m_toolAnimationWidget->loadToolImage(QString(), QPointF());  // 清除工件零偏图像
            LOG_INFO("清除工件零偏图像，因路径为空。");
        }
    }

    // 保持用户当前选择的模式，不强制设置为零偏模式
    // m_offsetModeComboBox->setCurrentIndex(1);     // 设置为"零偏"模式
    m_offsetCommandComboBox->setCurrentIndex(0);  // G10

    // 清空测量结果
    m_measurePointValue->setText("0.000");
    updateMeasurementResultState(OffsetValueState::NO_RESULT);  // 重置为未获得结果状态
    updateMeasurementControlsVisibility(true);                  // 重置为显示测量控件
    updateOffsetValueState(OffsetValueState::NO_RESULT);        // 重置为无结果状态

    // 重置占位符状态（零偏模式，所以占位符隐藏）
    if (m_offsetCommandPlaceholder) {
        m_offsetCommandPlaceholder->setVisible(false);
    }
    if (m_measurePointValueEditPlaceholder) {
        m_measurePointValueEditPlaceholder->setVisible(false);
    }

    updateDisplayForMode(m_currentMode);
}

void MeasureWorkpiecePanel::updateMeasurementResultState(OffsetValueState state) {
    switch (state) {
        case OffsetValueState::HIDDEN:  // 隐藏状态
            if (m_measurePointValueLabel) {
                m_measurePointValueLabel->setVisible(false);
            }
            if (m_measurePointValueZ0Label) {
                m_measurePointValueZ0Label->setVisible(false);
            }
            if (m_measurePointValue) {
                m_measurePointValue->setVisible(false);
            }
            if (m_measurePointValueEditPlaceholder) {
                m_measurePointValueEditPlaceholder->setVisible(false);
            }
            break;
        case OffsetValueState::NO_RESULT:  // 无结果状态
            if (m_measurePointValueLabel) {
                m_measurePointValueLabel->setVisible(true);
                m_measurePointValueLabel->setStyleSheet("color: #808080;");  // 灰色文字
            }
            if (m_measurePointValueZ0Label) {
                m_measurePointValueZ0Label->setVisible(true);
                m_measurePointValueZ0Label->setStyleSheet("color: #808080;");  // 灰色文字
            }
            if (m_measurePointValue) {
                m_measurePointValue->setVisible(false);  // 无结果时不显示
            }
            if (m_measurePointValueEditPlaceholder) {
                m_measurePointValueEditPlaceholder->setVisible(true);  // 显示占位符
            }
            break;
        case OffsetValueState::HAS_RESULT:  // 获得结果状态
            if (m_measurePointValueLabel) {
                m_measurePointValueLabel->setVisible(true);
                m_measurePointValueLabel->setStyleSheet("");  // 正常颜色
            }
            if (m_measurePointValueZ0Label) {
                m_measurePointValueZ0Label->setVisible(true);
                m_measurePointValueZ0Label->setStyleSheet("");  // 正常颜色
            }
            if (m_measurePointValue) {
                m_measurePointValue->setVisible(true);
                m_measurePointValue->setStyleSheet("");  // 正常颜色
            }
            if (m_measurePointValueEditPlaceholder) {
                m_measurePointValueEditPlaceholder->setVisible(false);  // 隐藏占位符
            }
            break;
    }
}

void MeasureWorkpiecePanel::updateMeasurementControlsVisibility(bool showControls) {
    if (showControls) {
        // 显示测量控件
        if (m_offsetCommandComboBox) {
            m_offsetCommandComboBox->setVisible(true);
        }
        if (m_referencePointValueLabel) {
            m_referencePointValueLabel->setVisible(true);
        }
        if (m_referencePointValue) {
            m_referencePointValue->setVisible(true);
        }
    } else {
        // 隐藏测量控件
        if (m_offsetCommandComboBox) {
            m_offsetCommandComboBox->setVisible(false);
        }
        if (m_referencePointValueLabel) {
            m_referencePointValueLabel->setVisible(false);
        }
        if (m_referencePointValue) {
            m_referencePointValue->setVisible(false);
        }
    }
}

void MeasureWorkpiecePanel::updateOffsetValueState(OffsetValueState state) {
    switch (state) {
        case OffsetValueState::HIDDEN:  // 隐藏状态
            if (m_resultTitleLabel) {
                m_resultTitleLabel->setVisible(false);
            }
            if (m_offsetValueLabel) {
                m_offsetValueLabel->setVisible(false);
            }
            if (m_offsetValue) {
                m_offsetValue->setVisible(false);
            }
            break;

        case OffsetValueState::NO_RESULT:  // 无结果状态
            if (m_resultTitleLabel) {
                m_resultTitleLabel->setVisible(true);
                m_resultTitleLabel->setStyleSheet("color: #808080;");  // 灰色文字
            }
            if (m_offsetValueLabel) {
                m_offsetValueLabel->setVisible(true);
                m_offsetValueLabel->setStyleSheet("color: #808080;");  // 灰色文字
            }
            if (m_offsetValue) {
                m_offsetValue->setVisible(true);
                m_offsetValue->setStyleSheet("color: #808080;");  // 灰色文字
            }
            break;

        case OffsetValueState::HAS_RESULT:  // 获得结果状态
            if (m_resultTitleLabel) {
                m_resultTitleLabel->setVisible(true);
                m_resultTitleLabel->setStyleSheet("");  // 正常颜色
            }
            if (m_offsetValueLabel) {
                m_offsetValueLabel->setVisible(true);
                m_offsetValueLabel->setStyleSheet("");  // 正常颜色
            }
            if (m_offsetValue) {
                m_offsetValue->setVisible(true);
                m_offsetValue->setStyleSheet("");  // 正常颜色
            }
            break;
    }
}

void MeasureWorkpiecePanel::onOffsetModeChanged(int index) {
    //还需要更新按钮machine.measureWorkpiece.setZeroOffset的显示文字为"计算"
    // 获取按钮
    // 更新本地按钮状态
    QVariantMap states;
    if (index == 0) {
        states["text"] = tr("计算");
    } else {
        states["text"] = tr("设置\n零偏");
    }
    m_serviceBus->updateMenuActionState("machine.measureWorkpiece.setZeroOffset", states);

    if (index == 0) {
        // 仅测量模式：隐藏相关控件
        updateMeasurementControlsVisibility(false);
        updateOffsetValueState(OffsetValueState::HIDDEN);
        updateMeasurementResultState(OffsetValueState::NO_RESULT);

        // 隐藏零偏指令，显示占位符
        if (m_offsetCommandComboBox) {
            m_offsetCommandComboBox->setVisible(false);
        }
        if (m_offsetCommandPlaceholder) {
            m_offsetCommandPlaceholder->setVisible(true);
        }
    } else if (index == 1) {
        // 零偏模式：显示相关控件，无结果状态
        updateMeasurementControlsVisibility(true);
        updateOffsetValueState(OffsetValueState::NO_RESULT);
        updateMeasurementResultState(OffsetValueState::NO_RESULT);

        // 显示零偏指令，隐藏占位符
        if (m_offsetCommandComboBox) {
            m_offsetCommandComboBox->setVisible(true);
        }
        if (m_offsetCommandPlaceholder) {
            m_offsetCommandPlaceholder->setVisible(false);
        }
    }
}

void MeasureWorkpiecePanel::onOffsetCommandChanged(const QString& text) {
    LOG_INFO("零偏指令已更改为: {}", text.toStdString());

    // 更新零偏值显示
    double offsetValue = getAxisWorkOffsetValue("Z", text.toStdString());
    m_offsetValue->setText(QString::number(offsetValue, 'f', 3));

    // 清空测量结果
    m_measurePointValue->setText("0.000");
    updateMeasurementResultState(OffsetValueState::NO_RESULT);
}

void MeasureWorkpiecePanel::reset() {
    LOG_INFO("重置测量工件面板");
    m_offsetModeComboBox->setCurrentIndex(1);
    m_offsetCommandComboBox->setCurrentIndex(0);
    m_offsetValue->setText("0.000");
    m_measurePointValue->setText("0.000");
    updateMeasurementResultState(OffsetValueState::NO_RESULT);
    updateMeasurementControlsVisibility(true);
    updateOffsetValueState(OffsetValueState::NO_RESULT);
}

void MeasureWorkpiecePanel::onReferencePointValueEditingFinished() {
    // 更新测量结果状态
    updateMeasurementResultState(OffsetValueState::NO_RESULT);
    // 更新零偏值状态
    updateOffsetValueState(OffsetValueState::NO_RESULT);
}

void MeasureWorkpiecePanel::handleMenuAction(const QString& actionId, const QVariantMap& states) {
    if (actionId == "machine.measureWorkpiece.chooseZeroOffset") {
        ZeroOffsetSelectionDialog::show(m_serviceBus, nullptr, m_offsetCommandComboBox->currentText(),
                                        [this](const QString& displayName) {
                                            LOG_INFO("用户选择了零偏类型: {}", displayName.toStdString());
                                            for (int i = 0; i < m_offsetCommandComboBox->count(); i++) {
                                                if (m_offsetCommandComboBox->itemText(i) == displayName) {
                                                    m_offsetCommandComboBox->setCurrentIndex(i);
                                                    break;
                                                }
                                            }
                                        });
    } else if (actionId == "machine.measureWorkpiece.setZeroOffset") {
        actionSaveZeroOffset();
    }
}

void MeasureWorkpiecePanel::onCycleStartPressed() {
    LOG_DEBUG("收到循环启动按键事件");

    // 如果当前在测量模式下，不需要切换
    if (m_offsetModeComboBox->currentIndex() == 0) {
        return;
    }

    // 比较当前选择的工件坐标系
    std::string currentWorkOffsetName;
    ErrorCode result = m_cncManager->getCurrentWorkOffsetName(m_channelId, currentWorkOffsetName);
    if (result != ErrorCode::Success) {
        LOG_UI("获取当前工件坐标系失败");
        return;
    }
    std::string selectedWorkOffsetName = m_offsetCommandComboBox->currentText().toStdString();
    if (selectedWorkOffsetName == currentWorkOffsetName) {
        return;
    }

    // 切换工件坐标系
    result = m_cncManager->executeMacro(m_channelId, selectedWorkOffsetName);
    if (result == ErrorCode::Success) {
        LOG_UI("正在切换到工件坐标系 {}", selectedWorkOffsetName);
    } else {
        LOG_UI("切换工件坐标系 {} 失败", selectedWorkOffsetName);
    }
}