#include "MdaInputPanel.h"

#include <QFileDialog>
#include <QFrame>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QStandardPaths>
#include <QVBoxLayout>

#include "GCodeEditor.h"
#include "IServiceBus.h"
#include "core/AppLogger.h"

MdaInputPanel::MdaInputPanel(IServiceBus* serviceBus, QWidget* parent)
    : FocusablePanel(parent), m_serviceBus(serviceBus) {
    setAttribute(Qt::WA_StyledBackground, true);

    // 获取CNC管理器实例
    m_cncManager = CncManager::getInstance();

    initUI();
    initConnect();
}

MdaInputPanel::~MdaInputPanel() = default;

void MdaInputPanel::setProgramText(const QString& text) {
    if (m_gcodeEditor) {
        m_gcodeEditor->setText(text);
    }
}

QString MdaInputPanel::getProgramText() const {
    if (m_gcodeEditor) {
        return m_gcodeEditor->text();
    }
    return QString();
}

void MdaInputPanel::clearProgram() {
    if (m_gcodeEditor) {
        m_gcodeEditor->clear();
        m_currentFilePath.clear();
        emit clearProgramRequested();
    }
}

void MdaInputPanel::saveProgram(const QString& fileName) {
    if (m_gcodeEditor) {
        m_gcodeEditor->saveGCodeFile(fileName);
        m_currentFilePath = fileName;
        LOG_DEBUG("程序已保存到: {}", fileName.toStdString());
    }
}

void MdaInputPanel::loadProgram(const QString& filePath) {
    if (m_gcodeEditor) {
        m_gcodeEditor->loadGCodeFile(filePath);
        m_currentFilePath = filePath;
        LOG_DEBUG("程序已从文件加载: {}", filePath.toStdString());
    }
}

void MdaInputPanel::runProgram() {
    QString gCode = getProgramText();
    if (gCode.trimmed().isEmpty()) {
        LOG_UI("MDA程序内容为空，无法运行");
        return;
    }

    // 基本的G代码语法检查
    if (!validateGCodeSyntax(gCode)) {
        LOG_UI("MDA程序语法检查失败");
        return;
    }

    // 直接调用CNC管理器加载程序
    if (m_cncManager && m_cncManager->isInitialized()) {
        LOG_INFO("MDA运行程序: {}", gCode.left(100).toStdString());  // 只记录前100个字符
        ErrorCode result = m_cncManager->executeMdi(0, gCode.toStdString());
        if (result == ErrorCode::Success) {
            LOG_UI("MDA程序加载成功");
        } else {
            QString errorMsg = QString("程序加载失败，错误码: %1").arg(static_cast<int>(result));
            LOG_UI("{}", errorMsg.toStdString());
        }
    } else {
        LOG_WARN("CNC管理器未初始化，无法运行MDA程序");
    }
}

void MdaInputPanel::onTextChanged() {
    // 简单记录文本变化，不再需要发射信号
    QString text = getProgramText();
    LOG_DEBUG("MDA程序文本已变化，长度: {}", text.length());
}

void MdaInputPanel::onProgramStatusChanged(int channelId, const ProgramStatus& status) {
    Q_UNUSED(channelId)

    // 根据程序状态更新界面
    LOG_DEBUG("MDA面板程序状态变化: 当前行{} 总行数{} 程序名{} 运行状态{}, 暂停状态{}, 错误状态{}", status.currentBlock,
              status.totalLines, QString::fromStdString(status.programName).toStdString(), status.isRunning,
              status.isPaused, status.isError);

    if (!m_gcodeEditor) {
        LOG_WARN("MDA面板GCodeEditor不存在，无法高亮执行行");
        return;
    }

    // 高亮当前执行行
    if ((status.isRunning || status.isPaused) && status.currentBlock > 0) {
        // 程序运行中，使用GCodeEditor的高亮功能
        m_gcodeEditor->setReadOnly(true);
        m_gcodeEditor->setCaretWidth(0);
        m_gcodeEditor->highlightLine(status.currentBlock - 1, GCodeEditor::ProgramExecution);  // QScintilla行号从0开始
        m_gcodeEditor->setCaretLineVisible(false);
        LOG_DEBUG("高亮当前执行行: {}", status.currentBlock);
        return;
    }

    // 程序未运行或已停止，清除高亮
    m_gcodeEditor->setReadOnly(false);
    m_gcodeEditor->setCaretWidth(1);
    m_gcodeEditor->clearHighlight(GCodeEditor::ProgramExecution);
    m_gcodeEditor->setCaretLineVisible(true);

    if (!status.isRunning && !status.isPaused && !status.isError) {
        // 程序未运行或已停止，清除高亮
        m_gcodeEditor->setCursorPosition(0, 0);
        LOG_DEBUG("程序已复位，高亮第一行");
    } else {
        LOG_DEBUG("程序已停止，清除高亮");
    }
}

void MdaInputPanel::initUI() {
    // 设置面板标题
    setTitleText(tr("MDA"));

    // 获取内容区域并设置布局
    QWidget* contArea = contentArea();
    QVBoxLayout* mainLayout = new QVBoxLayout(contArea);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);

    // G代码编辑器
    m_gcodeEditor = new GCodeEditor(contArea);
    m_gcodeEditor->setObjectName("gcodeEditor");
    mainLayout->addWidget(m_gcodeEditor, 1);  // 占用剩余空间
}

void MdaInputPanel::initConnect() {
    // 连接编辑器文本变化信号
    if (m_gcodeEditor) {
        connect(m_gcodeEditor, &GCodeEditor::textChanged, this, &MdaInputPanel::onTextChanged);
    }
}

bool MdaInputPanel::validateGCodeSyntax(const QString& gcode) {
    // 基本的G代码语法检查
    QStringList lines = gcode.split('\n', Qt::SkipEmptyParts);

    for (const QString& line : lines) {
        QString trimmedLine = line.trimmed();

        // 跳过空行和注释行
        if (trimmedLine.isEmpty() || trimmedLine.startsWith('(') || trimmedLine.startsWith(';')) {
            continue;
        }

        // 检查是否包含基本的G代码格式（字母后跟数字）
        QRegExp gCodePattern("^[GMTSFHDgmtsfhd]\\d+");
        QRegExp macroPattern("^#\\d+\\s*=\\s*-?\\d+(\\.\\d+)?");  // 支持宏变量赋值
        QRegExp macroReadPattern("^#\\d+");                       // 支持宏变量读取
        if (gCodePattern.indexIn(trimmedLine) != 0 && !trimmedLine.startsWith('%')) {
            QRegExp axisPattern("^[XYZIJKABCUVWxyzijkabcuvw]-?\\d+");
            if (axisPattern.indexIn(trimmedLine) != 0 && macroPattern.indexIn(trimmedLine) != 0 &&
                macroReadPattern.indexIn(trimmedLine) != 0) {
                LOG_WARN("可疑的G代码行: {}", trimmedLine.toStdString());
                return false;
            }
        }
    }

    return true;
}

void MdaInputPanel::handleMenuAction(const QString& actionId, const QVariantMap& states) {
    LOG_DEBUG("MdaInputPanel::handleMenuAction: actionId={}", actionId.toStdString());
}