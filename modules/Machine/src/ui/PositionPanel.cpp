#include "PositionPanel.h"

#include <QApplication>
#include <QFrame>
#include <QGridLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QStyle>
#include <QTimer>
#include <QVBoxLayout>
#include <string>

#include "ErrorCode.h"
#include "IServiceBus.h"     // Include actual header
#include "StyledLineEdit.h"  // 添加StyledLineEdit头文件
#include "core/AppLogger.h"
#include "core/CncManager.h"  // 添加CncManager, Axis enum, PointXD, ErrorCode
#include "core/OffsetDataManager.h"

// 辅助函数，格式化double值
QString formatDoublePos(double value, int precision = 3) {  // 重命名以避免冲突，如果另一个formatDouble存在
    return QString::number(value, 'f', precision);
}

// 辅助函数，格式化double值，考虑单位
QString formatAxisValue(double value, int precision = 3) {  // 单位将由 formatAxisUnit 处理
    return QString::number(value, 'f', precision);          // 只返回数字
}

// 新的辅助函数，获取单位字符串
QString formatAxisUnit(AxisType axisType, const SystemConfig& sysConfig) {
    if (axisType == AxisType::Linear) {
        if (sysConfig.linearUnit == DistanceUnit::Millimeter)
            return "mm";
        else if (sysConfig.linearUnit == DistanceUnit::Inch)
            return "in";
    } else if (axisType == AxisType::Rotary) {
        if (sysConfig.angularUnit == AngleUnit::Degree)
            return QString::fromUtf8("°");
        else if (sysConfig.angularUnit == AngleUnit::Radian)
            return "rad";
    }
    return "";  // 未知单位
}

PositionPanel::PositionPanel(IServiceBus* serviceBus, QWidget* parent)
    : FocusablePanel(parent),
      m_titleCol1Label(nullptr),
      m_titleCol2Label(nullptr),
      m_titleCol3Label(nullptr),
      m_titleCol4Label(nullptr),
      m_axisLayout(nullptr),
      m_gcodeLabel(nullptr),
      m_serviceBus(serviceBus),
      m_cncManager(nullptr),
      m_updateTimer(nullptr),
      m_defaultChannelId(0),
      m_useMachineCoordinate(false),
      m_isInEditMode(false) {
    setObjectName("PositionPanel");
    setupUi();
    m_cncManager = CncManager::getInstance();
    if (m_cncManager && m_cncManager->isInitialized()) {
        m_systemConfig = m_cncManager->getSystemConfig();
        populateAxisSpindleDisplay();
        m_updateTimer = new QTimer(this);
        connect(m_updateTimer, &QTimer::timeout, this, &PositionPanel::onUpdateDisplay);
        m_updateTimer->start(50);
        onUpdateDisplay();
    } else {
        clearDynamicRows();
        QLabel* offlineLabel = new QLabel(tr("CNC 控制器离线"), contentArea());
        m_axisLayout->addWidget(offlineLabel, 0, 0, 1, 4);
        setWcsMcsText(tr("N/A"));
        setGCode(tr("离线"));
    }
}

PositionPanel::~PositionPanel() { clearDynamicRows(); }

void PositionPanel::clearDynamicRows() {
    for (auto& rowElements : m_displayRows) {
        // 删除QLabel小部件
        if (rowElements.symbolLabel) delete rowElements.symbolLabel;  // 将被父QGridLayout删除
        if (rowElements.nameLabel) delete rowElements.nameLabel;
        if (rowElements.valueLabel) delete rowElements.valueLabel;
        if (rowElements.unitLabel) delete rowElements.unitLabel;
        if (rowElements.remainingLabel) delete rowElements.remainingLabel;
        if (rowElements.editWidget) delete rowElements.editWidget;  // 清理编辑控件
    }
    m_displayRows.clear();

    // 如果它们直接添加到m_axisLayout中，则删除所有小部件
    // QGridLayout::takeAt将删除并返回项目，然后我们删除它。
    // 如果populateAxisSpindleDisplay直接添加到布局中，这是安全的。
    if (m_axisLayout) {
        QLayoutItem* item;
        while ((item = m_axisLayout->takeAt(0)) != nullptr) {
            delete item->widget();  // 如果存在，则删除小部件
            delete item;            // 删除布局项本身
        }
    }
}

void PositionPanel::setupUi() {
    // 1. 自定义标题栏 (4列)
    QWidget* tbArea = titleBarArea();
    QHBoxLayout* titleLayout = qobject_cast<QHBoxLayout*>(tbArea->layout());
    if (!titleLayout) {
        titleLayout = new QHBoxLayout(tbArea);  // 如果不存在则创建
        tbArea->setLayout(titleLayout);
    }
    // 清理 FocusablePanel 的默认标题内容
    QLayoutItem* item;
    while ((item = titleLayout->takeAt(0)) != nullptr) {
        delete item->widget();  // 删除控件
        delete item;            // 删除布局项
    }
    titleLayout->setContentsMargins(5, 3, 5, 3);
    titleLayout->setSpacing(5);

    m_titleCol1Label = new QLabel("WCS", tbArea);
    m_titleCol2Label = new QLabel(tr("轴"), tbArea);
    m_titleCol3Label = new QLabel(tr("位置"), tbArea);
    m_titleCol4Label = new QLabel(tr(""), tbArea);
    m_titleCol5Label = new QLabel(tr("余程"), tbArea);

    m_titleCol1Label->setObjectName("posTitleCol1");
    m_titleCol2Label->setObjectName("posTitleCol2");
    m_titleCol3Label->setObjectName("posTitleCol3");
    m_titleCol4Label->setObjectName("posTitleCol4");
    m_titleCol5Label->setObjectName("posTitleCol5");

    // 统一设置标题标签的对齐方式，使其内容居中或左对齐
    m_titleCol1Label->setAlignment(Qt::AlignCenter | Qt::AlignVCenter);
    m_titleCol2Label->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_titleCol3Label->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    m_titleCol4Label->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_titleCol5Label->setAlignment(Qt::AlignRight | Qt::AlignVCenter);

    m_titleCol1Label->setFixedWidth(50);
    m_titleCol4Label->setFixedWidth(20);

    titleLayout->addWidget(m_titleCol1Label, 0);  // 拉伸因子0
    titleLayout->addWidget(m_titleCol2Label, 1);  // 拉伸因子1
    titleLayout->addWidget(m_titleCol3Label, 1);  // 拉伸因子1
    titleLayout->addWidget(m_titleCol4Label, 0);  // 拉伸因子0
    titleLayout->addWidget(m_titleCol5Label, 1);  // 拉伸因子1

    // 2. 内容区域 (直接使用GridLayout)
    QWidget* contArea = contentArea();

    m_axisLayout =
        new QGridLayout(contArea);  // 将 contArea 作为父对象传递给布局，或者稍后调用 contArea->setLayout(m_axisLayout)
    contArea->setLayout(m_axisLayout);  // 设置 contArea 的布局为 m_axisLayout

    m_axisLayout->setContentsMargins(5, 5, 5, 5);  // 直接在 gridLayout 上设置边距
    m_axisLayout->setHorizontalSpacing(5);
    m_axisLayout->setVerticalSpacing(5);

    m_axisLayout->setColumnStretch(0, 0);
    m_axisLayout->setColumnStretch(1, 1);
    m_axisLayout->setColumnStretch(2, 1);
    m_axisLayout->setColumnStretch(3, 0);
    m_axisLayout->setColumnStretch(4, 1);

    m_axisLayout->setColumnMinimumWidth(0, 50);
    m_axisLayout->setColumnMinimumWidth(3, 20);

    // 3. 状态栏
    setStatusBarVisible(true);
    QWidget* sbArea = statusBarArea();
    QHBoxLayout* sbLayout = qobject_cast<QHBoxLayout*>(sbArea->layout());
    if (!sbLayout) {
        sbLayout = new QHBoxLayout(sbArea);
        sbArea->setLayout(sbLayout);
    }
    QLayoutItem* sbItem;
    while ((sbItem = sbLayout->takeAt(0)) != nullptr) {
        delete sbItem->widget();
        delete sbItem;
    }
    sbLayout->setContentsMargins(5, 2, 5, 2);
    m_gcodeLabel = new QLabel(sbArea);
    m_gcodeLabel->setObjectName("posGcodeLabel");
    sbLayout->addWidget(m_gcodeLabel);
    sbLayout->addStretch();
}

// --- Update Methods ---
void PositionPanel::setWcsMcsText(const QString& text) {
    if (m_titleCol1Label) m_titleCol1Label->setText(text);
}

void PositionPanel::setGCode(const QString& gcode) {
    if (m_gcodeLabel) m_gcodeLabel->setText(gcode);
}

// 坐标系统切换方法
void PositionPanel::setCoordinateSystem(bool useMachineCoordinate) {
    if (m_useMachineCoordinate != useMachineCoordinate) {
        m_useMachineCoordinate = useMachineCoordinate;

        // 更新标题显示
        if (m_useMachineCoordinate) {
            setWcsMcsText(QStringLiteral("MCS"));
        } else {
            setWcsMcsText(QStringLiteral("WCS"));
        }

        // 更新轴和主轴名称显示
        updateAxisSpindleNames();

        // 立即更新显示
        onUpdateDisplay();
    }
}

bool PositionPanel::isUsingMachineCoordinate() const { return m_useMachineCoordinate; }

void PositionPanel::updateAxisSpindleNames() {
    for (auto& row : m_displayRows) {
        if (row.nameLabel && !row.originalName.isEmpty()) {
            QString displayName = row.originalName;
            if (m_useMachineCoordinate) {
                // MCS模式下，为轴和主轴名称添加"M"前缀
                displayName = "M" + displayName;
            }
            row.nameLabel->setText(displayName);
        }
    }
}

void PositionPanel::onUpdateDisplay() {
    if (!m_cncManager || !m_cncManager->isInitialized()) {
        for (auto& row : m_displayRows) {
            if (row.valueLabel) row.valueLabel->setText(tr("N/A"));
            if (row.remainingLabel) row.remainingLabel->setText(tr("N/A"));
        }
        setWcsMcsText(tr("N/A"));
        setGCode(tr("离线"));
        return;
    }

    // 根据当前坐标系统设置标题
    if (m_useMachineCoordinate) {
        setWcsMcsText(QStringLiteral("MCS"));
    } else {
        setWcsMcsText(QStringLiteral("WCS"));
    }

    std::string wcsNameStr;
    if (m_cncManager && m_cncManager->isInitialized() &&
        m_cncManager->getCurrentWorkOffsetName(m_defaultChannelId, wcsNameStr) == ErrorCode::Success) {
        setGCode(QString::fromStdString(wcsNameStr));
    } else {
        setGCode(tr("---"));
    }

    PointXD currentPos, remainingPos;
    bool currentPosOk = false;
    bool remainPosOk = m_cncManager && m_cncManager->isInitialized() &&
                       m_cncManager->getRemainingPosition(m_defaultChannelId, remainingPos) == ErrorCode::Success;

    // 根据坐标系统选择获取机床坐标或工件坐标
    if (m_cncManager && m_cncManager->isInitialized()) {
        if (m_useMachineCoordinate) {
            currentPosOk = m_cncManager->getMachinePosition(m_defaultChannelId, currentPos) == ErrorCode::Success;
        } else {
            currentPosOk = m_cncManager->getWorkPosition(m_defaultChannelId, currentPos) == ErrorCode::Success;
        }
    }

    for (auto& row : m_displayRows) {
        if (row.isSpindle) {
            // 主轴
            double spindleSpeed = 0.0;
            QString spindleRotatingSymbol = " ";  // Default to space (not rotating)

            if (row.globalIndex != -1 && m_cncManager && m_cncManager->isInitialized() &&
                m_cncManager->getActualSpindleSpeed(m_defaultChannelId, row.globalIndex, spindleSpeed) ==
                    ErrorCode::Success) {
                if (row.valueLabel) {
                    row.valueLabel->setText(QString::number(spindleSpeed, 'f', 0));
                }
                if (spindleSpeed != 0.0) {
                    spindleRotatingSymbol = "S";
                }
            } else {
                if (row.valueLabel) row.valueLabel->setText(tr("N/A"));
            }
            if (row.symbolLabel) {
                row.symbolLabel->setText(spindleRotatingSymbol);
            }
        } else {
            // 轴
            double axisCurrentVal = 0.0;
            QString axisRemainStr = formatAxisValue(0.0);

            if (currentPosOk && row.globalIndex != -1 && currentPos.coordinates.count(row.globalIndex)) {
                axisCurrentVal = currentPos.coordinates.at(row.globalIndex);
            }
            if (row.valueLabel) {
                row.valueLabel->setText(formatAxisValue(axisCurrentVal));
            }
            if (row.unitLabel) {
                row.unitLabel->setText(formatAxisUnit(row.axisType, m_systemConfig));
            }

            if (remainPosOk && row.globalIndex != -1 && remainingPos.coordinates.count(row.globalIndex)) {
                axisRemainStr = formatAxisValue(remainingPos.coordinates.at(row.globalIndex));
            }
            if (row.remainingLabel) {
                row.remainingLabel->setText(axisRemainStr);
            }
        }
    }
}

void PositionPanel::handleMenuAction(const QString& actionId, const QVariantMap& states) {
    LOG_DEBUG("PositionPanel::handleMenuAction: actionId={}", actionId.toStdString());

    if (actionId == "machine.zeroOffset.z0") {
        // 设置Z轴零点 - 将当前Z轴位置设为工件坐标系的零点
        setAxisZeroOffset("Z", 0);
    } else if (actionId == "machine.zeroOffset.x0") {
        // 设置X轴零点 - 将当前X轴位置设为工件坐标系的零点
        setAxisZeroOffset("X", 0);
    } else if (actionId == "machine.zeroOffset.delete") {
        // 删除有效零偏 - 清零当前工件坐标系的所有偏移值
        deleteActiveZeroOffset();
    }

    exitEditMode();
}

void PositionPanel::populateAxisSpindleDisplay() {
    clearDynamicRows();
    QWidget* contArea = contentArea();  // 获取内容区域小部件

    if (m_defaultChannelId < 0 || static_cast<size_t>(m_defaultChannelId) >= m_systemConfig.channelsConfigs.size()) {
        LOG_ERROR("PositionPanel: 无效的通道编号 {} 或通道配置错误.", m_defaultChannelId);
        QLabel* errorLabel = new QLabel(tr("通道配置错误"), contArea);
        m_axisLayout->addWidget(errorLabel, 0, 0, 1, 4);
        return;
    }

    const auto& channelConf = m_systemConfig.channelsConfigs[m_defaultChannelId];
    int currentRow = 0;  // 跟踪QGridLayout中的当前行

    // 显示轴
    for (const auto& axisMapping : channelConf.axes) {
        if (axisMapping.globalAxisIndex < 0 ||
            static_cast<size_t>(axisMapping.globalAxisIndex) >= m_systemConfig.axesConfigs.size()) {
            LOG_ERROR("PositionPanel: 无效的主轴编号 {} 对于通道 {}.", axisMapping.globalAxisIndex, m_defaultChannelId);
            continue;
        }
        const auto& axisConf = m_systemConfig.axesConfigs[axisMapping.globalAxisIndex];
        DisplayRowElements rowElements;
        rowElements.globalIndex = axisMapping.globalAxisIndex;
        rowElements.isSpindle = false;
        rowElements.axisType = axisConf.type;

        // 创建QLabel小部件
        // Symbol可以基于axisConf.type（例如，L用于线性，R用于旋转）
        // 现在，使用一个通用的符号或名称的第一个字母（如果可用）
        QString symbolText = axisConf.name.empty() ? "A" : QString::fromStdString(axisConf.name.substr(0, 1)).toUpper();
        if (axisConf.type == AxisType::Linear)
            symbolText = "";
        else if (axisConf.type == AxisType::Rotary)
            symbolText = "";

        rowElements.symbolLabel = new QLabel(symbolText, contArea);
        rowElements.symbolLabel->setAlignment(Qt::AlignCenter | Qt::AlignVCenter);
        rowElements.symbolLabel->setFixedWidth(50);
        rowElements.symbolLabel->setObjectName("posAxisSymbol");

        QString axisDisplayName = axisMapping.channelLocalName.empty()
                                      ? QString::fromStdString(axisConf.name)
                                      : QString::fromStdString(axisMapping.channelLocalName);
        rowElements.nameLabel = new QLabel(axisDisplayName, contArea);
        rowElements.nameLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
        rowElements.nameLabel->setObjectName("posAxisName");

        // 保存原始名称
        rowElements.originalName = axisDisplayName;

        // 如果当前是MCS模式，添加M前缀
        if (m_useMachineCoordinate) {
            axisDisplayName = "M" + axisDisplayName;
            rowElements.nameLabel->setText(axisDisplayName);
        }

        rowElements.valueLabel = new QLabel(tr("0.000"), contArea);
        rowElements.valueLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
        rowElements.valueLabel->setObjectName("posAxisValue");

        rowElements.unitLabel = new QLabel(formatAxisUnit(axisConf.type, m_systemConfig), contArea);
        rowElements.unitLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
        rowElements.unitLabel->setFixedWidth(20);
        rowElements.unitLabel->setObjectName("posAxisUnit");

        rowElements.remainingLabel = new QLabel(tr("0.000"), contArea);
        rowElements.remainingLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
        rowElements.remainingLabel->setObjectName("posAxisRemaining");

        // 添加到布局
        m_axisLayout->addWidget(rowElements.symbolLabel, currentRow, 0);
        m_axisLayout->addWidget(rowElements.nameLabel, currentRow, 1);
        m_axisLayout->addWidget(rowElements.valueLabel, currentRow, 2);
        m_axisLayout->addWidget(rowElements.unitLabel, currentRow, 3);
        m_axisLayout->addWidget(rowElements.remainingLabel, currentRow, 4);

        m_displayRows.push_back(rowElements);
        currentRow++;
    }

    // 显示主轴
    for (const auto& spindleMapping : channelConf.spindles) {
        if (spindleMapping.globalSpindleIndex < 0 ||
            static_cast<size_t>(spindleMapping.globalSpindleIndex) >= m_systemConfig.spindlesConfigs.size()) {
            LOG_WARN("PositionPanel: 无效的主轴编号 {} 对于通道 {}.", spindleMapping.globalSpindleIndex,
                     m_defaultChannelId);
            continue;
        }
        const auto& spindleConf = m_systemConfig.spindlesConfigs[spindleMapping.globalSpindleIndex];
        DisplayRowElements rowElements;
        rowElements.globalIndex = spindleMapping.globalSpindleIndex;
        rowElements.isSpindle = true;

        rowElements.symbolLabel = new QLabel(" ", contArea);  // Default to space, updated in onUpdateDisplay
        rowElements.symbolLabel->setAlignment(Qt::AlignCenter | Qt::AlignVCenter);
        rowElements.symbolLabel->setFixedWidth(50);
        rowElements.symbolLabel->setObjectName("posSpindleSymbol");

        QString spindleDisplayName = spindleMapping.channelLocalName.empty()
                                         ? QString::fromStdString(spindleConf.name)
                                         : QString::fromStdString(spindleMapping.channelLocalName);
        rowElements.nameLabel = new QLabel(spindleDisplayName, contArea);
        rowElements.nameLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
        rowElements.nameLabel->setObjectName("posSpindleName");

        // 保存原始名称
        rowElements.originalName = spindleDisplayName;

        // 如果当前是MCS模式，添加M前缀
        if (m_useMachineCoordinate) {
            spindleDisplayName = "M" + spindleDisplayName;
            rowElements.nameLabel->setText(spindleDisplayName);
        }

        rowElements.valueLabel = new QLabel(tr("0"), contArea);
        rowElements.valueLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
        rowElements.valueLabel->setObjectName("posSpindleValue");

        rowElements.unitLabel = new QLabel(tr("°"), contArea);
        rowElements.unitLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
        rowElements.unitLabel->setFixedWidth(20);
        rowElements.unitLabel->setObjectName("posSpindleUnit");

        rowElements.remainingLabel = new QLabel(tr("-"), contArea);  // 剩余不适用于主轴
        rowElements.remainingLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
        rowElements.remainingLabel->setObjectName("posSpindleRemaining");

        // 添加到布局
        m_axisLayout->addWidget(rowElements.symbolLabel, currentRow, 0);
        m_axisLayout->addWidget(rowElements.nameLabel, currentRow, 1);
        m_axisLayout->addWidget(rowElements.valueLabel, currentRow, 2);
        m_axisLayout->addWidget(rowElements.unitLabel, currentRow, 3);
        m_axisLayout->addWidget(rowElements.remainingLabel, currentRow, 4);

        m_displayRows.push_back(rowElements);
        currentRow++;
    }

    if (currentRow == 0) {  // 当前通道无轴或主轴配置
        QLabel* noDataLabel = new QLabel(tr("当前通道无轴或主轴配置"), contArea);
        noDataLabel->setAlignment(Qt::AlignCenter);
        m_axisLayout->addWidget(noDataLabel, 0, 0, 1, 4);
        currentRow++;  // 增加以确保拉伸正确
    }

    // 添加拉伸到末尾以推动所有行到顶部
    m_axisLayout->setRowStretch(currentRow, 1);
}

// 编辑状态管理方法
void PositionPanel::enterEditMode() {
    if (m_isInEditMode) {
        return;  // 已经在编辑状态
    }

    m_isInEditMode = true;

    // 停止定时器更新，避免在编辑时被覆盖
    if (m_updateTimer) {
        m_updateTimer->stop();
    }

    QWidget* contArea = contentArea();

    // 为每个轴和主轴的值创建StyledLineEdit
    for (auto& row : m_displayRows) {
        if (row.valueLabel) {
            // 创建编辑控件
            row.editWidget = new StyledLineEdit(contArea);
            row.editWidget->setText(row.valueLabel->text());
            row.editWidget->setAlignment(Qt::AlignRight | Qt::AlignVCenter);

            // 根据轴/主轴类型设置不同的ObjectName
            if (row.isSpindle) {
                row.editWidget->setObjectName("posSpindleEditValue");
            } else {
                row.editWidget->setObjectName("posAxisEditValue");
            }

            // 设置编辑框大小与原Label一致
            row.editWidget->setFixedSize(row.valueLabel->size());
            row.editWidget->setSizePolicy(row.valueLabel->sizePolicy());
            row.editWidget->setMinimumSize(row.valueLabel->minimumSize());
            row.editWidget->setMaximumSize(row.valueLabel->maximumSize());

            // 连接编辑完成信号
            connect(row.editWidget, &StyledLineEdit::editingFinished, this, [this, &row]() { onValueEdited(row); });

            // 隐藏原来的标签，显示编辑控件
            row.valueLabel->hide();

            // 找到valueLabel在布局中的位置
            int rowIndex = -1, colIndex = -1;
            for (int i = 0; i < m_axisLayout->rowCount(); ++i) {
                for (int j = 0; j < m_axisLayout->columnCount(); ++j) {
                    QLayoutItem* item = m_axisLayout->itemAtPosition(i, j);
                    if (item && item->widget() == row.valueLabel) {
                        rowIndex = i;
                        colIndex = j;
                        break;
                    }
                }
                if (rowIndex != -1) break;
            }

            // 将编辑控件添加到相同位置
            if (rowIndex != -1 && colIndex != -1) {
                m_axisLayout->addWidget(row.editWidget, rowIndex, colIndex);
            }
        }
    }

    // 使用单次定时器确保所有控件完全初始化后再激活面板
    QTimer::singleShot(0, this, [this]() { activatePanel(); });

    LOG_DEBUG("PositionPanel 进入编辑状态");
}

void PositionPanel::exitEditMode() {
    if (!m_isInEditMode) {
        return;  // 不在编辑状态
    }

    m_isInEditMode = false;

    // 恢复正常的坐标系统标题显示
    if (m_useMachineCoordinate) {
        setWcsMcsText(QStringLiteral("MCS"));
    } else {
        setWcsMcsText(QStringLiteral("WCS"));
    }

    // 移除编辑控件，恢复标签显示
    for (auto& row : m_displayRows) {
        if (row.editWidget) {
            // 移除编辑控件
            m_axisLayout->removeWidget(row.editWidget);
            delete row.editWidget;
            row.editWidget = nullptr;

            // 恢复原来的标签显示
            if (row.valueLabel) {
                row.valueLabel->show();
            }
        }
    }

    // 重新启动定时器更新
    if (m_updateTimer) {
        m_updateTimer->start(200);
    }

    LOG_DEBUG("PositionPanel 退出编辑状态");
}

bool PositionPanel::isInEditMode() const { return m_isInEditMode; }

void PositionPanel::onValueEdited(const DisplayRowElements& row) {
    if (!row.editWidget) {
        return;
    }

    QString newValue = row.editWidget->text();
    bool ok = false;
    double value = newValue.toDouble(&ok);

    if (!ok) {
        LOG_DEBUG("无效的数值输入: {}", newValue.toStdString());
        // 恢复原来的值
        row.editWidget->setText(row.valueLabel->text());
        return;
    }

    LOG_DEBUG("编辑完成 - 轴索引: {} 新值: {} 是否主轴: {}", row.globalIndex, value, row.isSpindle);

    if (row.isSpindle) {
        // 主轴转速暂时不支持编辑
        LOG_DEBUG("主轴转速暂不支持编辑");
        // 恢复原来的值
        row.editWidget->setText(row.valueLabel->text());
        return;
    } else {
        // 处理轴位置设置（零偏设置）
        if (m_cncManager && m_cncManager->isInitialized() && row.globalIndex != -1) {
            double currentWorkOffset;
            std::string errorMsg;

            // 假设用户修改了第1行、X轴的数值，这意味着该数值是用户期望的WCS坐标，接下来我要调整工作坐标系的零偏来“适应”这个WCS坐标
            // WCS = MCS - totalOffset(基本零偏 + 工作坐标系的零偏 + 刀具零偏 + TOFF)

            // 根据期望的工件坐标系，计算当前零偏值
            auto* offsetManager = OffsetDataManager::getInstance();
            if (!offsetManager) {
                LOG_ERROR("获取零偏数据管理器失败");
                return;
            }

            // 根据期望的WCS，计算当前工作坐标系的零偏值
            bool ret = offsetManager->calculateCurrentWorkOffsetFromWCS(m_defaultChannelId, row.globalIndex, value,
                                                                        currentWorkOffset);
            if (!ret) {
                LOG_ERROR("计算当前零偏值失败");
                return;
            }

            // 设置新的工件坐标系偏移值
            ret = offsetManager->updateCurrentWorkOffset(m_defaultChannelId, row.globalIndex, currentWorkOffset);
            if (!ret) {
                LOG_ERROR("设置轴 {} 工件坐标系 {} 失败", row.globalIndex, currentWorkOffset);
                return;
            }

            LOG_ERROR("设置轴 {} 工件坐标系 {} 成功", row.globalIndex, currentWorkOffset);

            // 更新显示值
            if (row.valueLabel) {
                row.valueLabel->setText(formatAxisValue(value));
            }
        }
    }
}

// 零偏设置相关方法实现
void PositionPanel::setAxisZeroOffset(const QString& axisName, double targetValue) {
    LOG_DEBUG("设置轴零偏: {} = {}", axisName.toStdString(), targetValue);

    if (!m_cncManager || !m_cncManager->isInitialized()) {
        LOG_WARN("CNC管理器未初始化，无法设置零偏");
        return;
    }

    // 查找轴的全局索引
    int axisGlobalIndex = findAxisGlobalIndex(axisName);
    if (axisGlobalIndex == -1) {
        LOG_WARN("未找到轴: {}", axisName.toStdString());
        return;
    }

    try {
        // 获取当前机床坐标
        PointXD currentMachinePos;
        ErrorCode result = m_cncManager->getMachinePosition(m_defaultChannelId, currentMachinePos);
        if (result != ErrorCode::Success) {
            LOG_ERROR("获取机床坐标失败，错误码: {}", static_cast<int>(result));
            return;
        }

        // 检查轴是否存在于当前位置中
        if (currentMachinePos.coordinates.find(axisGlobalIndex) == currentMachinePos.coordinates.end()) {
            LOG_WARN("轴 {} 在当前位置中不存在", axisName.toStdString());
            return;
        }

        // 获取当前轴的机床坐标
        double currentAxisMachinePos = currentMachinePos.coordinates.at(axisGlobalIndex);

        // 获取当前工件坐标系名称
        std::string currentWorkOffsetName;
        result = m_cncManager->getCurrentWorkOffsetName(m_defaultChannelId, currentWorkOffsetName);
        if (result != ErrorCode::Success) {
            LOG_ERROR("获取当前工件坐标系名称失败，错误码: {}", static_cast<int>(result));
            return;
        }

        // 获取当前工件坐标系的偏移值
        PointXD currentWorkOffset;
        result = m_cncManager->getWorkOffsetValue(m_defaultChannelId, currentWorkOffsetName, currentWorkOffset);
        if (result != ErrorCode::Success) {
            LOG_ERROR("获取工件坐标系 {} 偏移值失败，错误码: {}", currentWorkOffsetName, static_cast<int>(result));
            return;
        }

        // 计算新的偏移值：新偏移 = 当前机床坐标 - 目标工件坐标
        // 例如：机床坐标100，目标工件坐标0，则偏移值为100
        double newOffsetValue = currentAxisMachinePos - targetValue;

        // 更新工件坐标系偏移值
        currentWorkOffset.coordinates[axisGlobalIndex] = newOffsetValue;

        // 设置新的工件坐标系偏移值
        result = m_cncManager->setWorkOffsetValue(m_defaultChannelId, currentWorkOffsetName, currentWorkOffset);
        if (result == ErrorCode::Success) {
            LOG_INFO("成功设置轴 {} 零偏: 机床坐标 {} -> 工件坐标 {} (偏移值: {})", axisName.toStdString(),
                     currentAxisMachinePos, targetValue, newOffsetValue);
        } else {
            LOG_ERROR("设置轴 {} 零偏失败，错误码: {}", axisName.toStdString(), static_cast<int>(result));
        }

    } catch (const std::exception& e) {
        LOG_ERROR("设置轴零偏时发生异常: {}", e.what());
    }
}

void PositionPanel::deleteActiveZeroOffset() {
    LOG_DEBUG("删除有效零偏");

    if (!m_cncManager || !m_cncManager->isInitialized()) {
        LOG_WARN("CNC管理器未初始化，无法删除零偏");
        return;
    }

    try {
        // 获取当前工件坐标系名称
        std::string currentWorkOffsetName;
        ErrorCode result = m_cncManager->getCurrentWorkOffsetName(m_defaultChannelId, currentWorkOffsetName);
        if (result != ErrorCode::Success) {
            LOG_ERROR("获取当前工件坐标系名称失败，错误码: {}", static_cast<int>(result));
            return;
        }

        // 创建零偏移值 - 所有轴的偏移值都设为0
        PointXD zeroOffset;

        // 遍历所有轴，将偏移值设为0
        const auto& channelConf = m_systemConfig.channelsConfigs[m_defaultChannelId];
        for (const auto& axisMapping : channelConf.axes) {
            if (axisMapping.globalAxisIndex >= 0) {
                zeroOffset.coordinates[axisMapping.globalAxisIndex] = 0.0;
            }
        }

        // 设置零偏移值
        result = m_cncManager->setWorkOffsetValue(m_defaultChannelId, currentWorkOffsetName, zeroOffset);
        if (result == ErrorCode::Success) {
            LOG_INFO("成功删除工件坐标系 {} 的所有零偏", currentWorkOffsetName);
        } else {
            LOG_ERROR("删除工件坐标系 {} 零偏失败，错误码: {}", currentWorkOffsetName, static_cast<int>(result));
        }

    } catch (const std::exception& e) {
        LOG_ERROR("删除零偏时发生异常: {}", e.what());
    }
}

int PositionPanel::findAxisGlobalIndex(const QString& axisName) const {
    if (m_defaultChannelId < 0 || static_cast<size_t>(m_defaultChannelId) >= m_systemConfig.channelsConfigs.size()) {
        LOG_ERROR("无效的通道编号: {}", m_defaultChannelId);
        return -1;
    }

    const auto& channelConf = m_systemConfig.channelsConfigs[m_defaultChannelId];

    // 遍历轴映射，查找匹配的轴名称
    for (const auto& axisMapping : channelConf.axes) {
        if (axisMapping.globalAxisIndex < 0 ||
            static_cast<size_t>(axisMapping.globalAxisIndex) >= m_systemConfig.axesConfigs.size()) {
            continue;
        }

        const auto& axisConf = m_systemConfig.axesConfigs[axisMapping.globalAxisIndex];

        // 检查轴配置名称
        QString configAxisName = QString::fromStdString(axisConf.name);
        if (configAxisName.compare(axisName, Qt::CaseInsensitive) == 0) {
            return axisMapping.globalAxisIndex;
        }

        // 检查通道本地名称
        if (!axisMapping.channelLocalName.empty()) {
            QString localAxisName = QString::fromStdString(axisMapping.channelLocalName);
            if (localAxisName.compare(axisName, Qt::CaseInsensitive) == 0) {
                return axisMapping.globalAxisIndex;
            }
        }
    }

    LOG_WARN("未找到轴: {}", axisName.toStdString());
    return -1;
}
