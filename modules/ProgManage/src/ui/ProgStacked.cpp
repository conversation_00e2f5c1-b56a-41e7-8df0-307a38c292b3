#include "ProgStacked.h"

#include <QCoreApplication>
#include <QLabel>
#include <QMessageBox>
#include <QTreeWidget>

#include "core/AppLogger.h"
#include "core/CncManager.h"
#include "public/PublicEventConstants.h"

using namespace ProgStackedConstants;

// 程序文件路径常量
static const QString NC_PROGRAM_DIR = "/NcPrg";

ProgStacked::ProgStacked(IServiceBus* serviceBus, QWidget* parent) : QWidget(parent), m_serviceBus(serviceBus) {
    LOG_INFO(LOG_UI_INIT_START.toStdString());

    setupUi();
    logServiceBusStatus();

    // 连接信号和槽,都初始化后才再连接信号和槽确保成功连接
    connectSignalsAndSlots();

    // 设置初始焦点到任务列表面板
    setInitialFocusToTaskPanel();

    LOG_INFO(LOG_UI_INIT_COMPLETE.toStdString());
}

ProgStacked::~ProgStacked() {
    LOG_INFO(LOG_UI_DESTROY_START.toStdString());
    // Qt 会自动处理子控件的删除
    LOG_INFO(LOG_UI_DESTROY_COMPLETE.toStdString());
}

void ProgStacked::logServiceBusStatus() {
    if (m_serviceBus) {
        LOG_INFO(LOG_SERVICE_BUS_SUCCESS.toStdString());

        // 检查 CncManager 状态
        auto cncManager = CncManager::getInstance();
        if (cncManager && cncManager->isInitialized()) {
            LOG_INFO(LOG_CNC_INTERFACE_SUCCESS.toStdString());
        } else {
            LOG_WARN(LOG_CNC_INTERFACE_FAILED.toStdString());
        }
    } else {
        LOG_WARN(LOG_SERVICE_BUS_FAILED.toStdString());
    }
}

void ProgStacked::setupUi() {
    LOG_INFO("开始设置程序管理主界面UI");

    // 创建主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN);
    m_mainLayout->setSpacing(LAYOUT_SPACING);

    // 创建工作区容器
    m_progStackedWidget = new QWidget(this);
    m_progStackedWidget->setObjectName(PROG_STACKED_WIDGET_NAME);
    m_progStackedLayout = new QHBoxLayout(m_progStackedWidget);
    m_progStackedLayout->setContentsMargins(LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN);
    m_progStackedLayout->setSpacing(LAYOUT_SPACING);

    // 创建任务列表面板和内容显示面板
    setupTaskListPanel();
    setupContentDisplayPanel();

    // 添加面板到工作区布局
    m_progStackedLayout->addWidget(m_taskListPanel, TASK_PANEL_STRETCH_FACTOR);
    m_progStackedLayout->addWidget(m_contentDisplayPanel, CONTENT_PANEL_STRETCH_FACTOR);

    // 添加工作区到主布局
    m_mainLayout->addWidget(m_progStackedWidget);
    setLayout(m_mainLayout);  // 设置主布局

    LOG_INFO("程序管理主界面UI设置完成");
}

void ProgStacked::setupTaskListPanel() {
    LOG_INFO("开始设置任务列表面板");

    // 创建任务列表面板
    getCurrentTask();  // 初始化CurrentTask实例

    // 添加到布局
    // m_taskListLayout->addWidget(m_taskTreeWidget);

    LOG_INFO("任务列表面板设置完成");
}

void ProgStacked::connectSignalsAndSlots() {
    LOG_INFO("开始连接信号和槽");

    // 连接CurrentTask的信号

    if (m_taskListPanel) {
        connect(m_taskListPanel, &CurrentTask::taskListItemSelectionChanged, this,
                &ProgStacked::onTaskListItemSelectionChanged);
    } else {
        LOG_INFO("CurrentTask::taskListItemSelectionChanged和ProgStacked::onTaskListItemSelectionChanged连接失败");
    }

    // 连接信号和槽
    if (m_localDataView) {
        connect(m_localDataView, &LocalData::historyFilesReady, this, &ProgStacked::handleHistoryFiles);
    } else {
        LOG_INFO("LocalData::historyFilesReady和ProgStacked::handleHistoryFiles连接失败");
    }

    LOG_INFO("信号和槽连接完成");
}

void ProgStacked::setupContentDisplayPanel() {
    LOG_INFO("开始设置内容显示面板");

    // 创建内容显示面板
    m_contentDisplayPanel = new QFrame(m_progStackedWidget);
    m_contentDisplayPanel->setObjectName(CONTENT_DISPLAY_PANEL_NAME);
    m_contentDisplayLayout = new QVBoxLayout(m_contentDisplayPanel);
    m_contentDisplayLayout->setContentsMargins(LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN);
    m_contentDisplayLayout->setSpacing(LAYOUT_SPACING);

    // 初始化视图组件
    initializeViewComponents();

    // 默认显示本地数据视图
    m_contentDisplayLayout->addWidget(m_localDataView);
    m_currentActiveView = m_localDataView;

    // 初始化本地数据视图
    QString appRootPath = QCoreApplication::applicationDirPath();
    QString ncProgramPath = appRootPath + NC_PROGRAM_DIRECTORY;
    LOG_INFO("应用程序根路径: {}", appRootPath.toStdString());
    LOG_INFO("NC程序路径: {}", ncProgramPath.toStdString());

    m_localDataView->setLocalPath(ncProgramPath);  // 设置路径
    // m_localDataView->showEvent();                  //显示界面

    LOG_INFO("内容显示面板设置完成");
}

void ProgStacked::initializeViewComponents() {
    LOG_INFO("开始初始化视图组件");

    // 创建各种视图组件

    getLocalData();  // 创建本地视图组件
    m_localDataView->setObjectName(LOCAL_DATA_VIEW_NAME);

    m_usbDataView = new UsbDiskData(m_serviceBus);
    m_usbDataView->setObjectName(USB_DATA_VIEW_NAME);
    // 连接USB事件信号
    connect(m_usbDataView, &UsbDiskData::usbInserted, this, &ProgStacked::onUsbInserted);
    connect(m_usbDataView, &UsbDiskData::usbRemoved, this, &ProgStacked::onUsbRemoved);
    // 启动USB监控（指定监控目录）
    if (!m_usbDataView->startMonitoring("/mnt/usb")) {
        LOG_INFO("无法启动USB监控");
    }

    m_netDataView = new NetData();
    m_netDataView->setObjectName(NET_DATA_VIEW_NAME);

    m_textEditingView = new TextEditing();
    m_textEditingView->setObjectName(TEXT_EDITING_VIEW_NAME);
    // 连接文本编辑器信号
    connect(m_textEditingView, &TextEditing::editorSelectionBegun, this, &ProgStacked::onTextEditSelectionBegun);
    connect(m_textEditingView, &TextEditing::editorSelectionCleared, this, &ProgStacked::onTextEditSelectionCleared);
    connect(m_textEditingView, &TextEditing::editorUndoAvailabilityChanged, this,
            &ProgStacked::onTextEditUndoAvailabilityChanged);
    connect(m_textEditingView, &TextEditing::editorRedoAvailabilityChanged, this,
            &ProgStacked::onTextEditRedoAvailabilityChanged);
    // 禁用文本编辑器取消选区和撤销重做菜单项
    onTextEditSelectionCleared();
    onTextEditUndoAvailabilityChanged(false);
    onTextEditRedoAvailabilityChanged(false);

    LOG_INFO("视图组件初始化完成");
}

// 键盘事件处理
void ProgStacked::keyPressEvent(QKeyEvent* event) {
    LOG_INFO("处理ProgStacked的键盘事件");
    // 先处理自己的按键事件，再交给基类
    if (event->key() == Qt::Key_Tab) {
        switchFocusBetweenPanels();
        return;
    }

    // 未处理的事件交给父类处理
    QWidget::keyPressEvent(event);
}

void ProgStacked::onProgramSelected() {
    QString filePath = "";
    m_taskListPanel->transferProcessingPath(filePath);
    LOG_INFO("程序已选择: {}", filePath.toStdString());
    processSelectedProgram(filePath);
}

QString ProgStacked::getCurrentViewName() const {
    if (!m_currentActiveView) {
        return "null";
    }
    return m_currentActiveView->objectName();
}

void ProgStacked::switchToView(QWidget* targetView) {
    if (!targetView || targetView == m_currentActiveView) {
        return;
    }

    QString currentViewName = getCurrentViewName();
    QString targetViewName = targetView->objectName();

    LOG_INFO("切换视图: 从{} 到 {}", currentViewName.toStdString(), targetViewName.toStdString());

    // 隐藏当前视图并清除焦点
    if (m_currentActiveView) {
        m_contentDisplayLayout->removeWidget(m_currentActiveView);
        m_currentActiveView->hide();
        if (m_focusedView == m_currentActiveView) {
            m_focusedView = nullptr;
        }
    }

    // 显示目标视图并设置焦点
    m_contentDisplayLayout->addWidget(targetView);
    targetView->show();
    m_currentActiveView = targetView;
    setFocusToView(targetView);

    LOG_INFO("视图切换完成");
}

// 视图切换方法实现
void ProgStacked::showLocalDataView() {
    LOG_INFO("请求显示本地数据视图");
    switchToView(m_localDataView);
}

void ProgStacked::showUsbDiskDataView() {
    LOG_INFO("请求显示U盘数据视图");
    switchToView(m_usbDataView);

    // 优先使用传入的路径，无路径时才扫描
    if (!m_usbPath.isEmpty()) {
        m_usbDataView->setUsbPath(m_usbPath);
        LOG_INFO("使用传入的U盘路径: {}", m_usbPath.toStdString());
    } else {
        m_usbDataView->setUsbPath("000");
        LOG_WARN("未检测到U盘, 不显示路径");
    }
}

bool ProgStacked::updateUsbInterface() { return m_usbDataView->updateUsbInterface(); }

void ProgStacked::showNetDataView() {
    LOG_INFO("请求显示网络数据视图");
    switchToView(m_netDataView);
    QString appRootPath = QCoreApplication::applicationDirPath();
    m_netDataView->slotShow(QDir(appRootPath));
}

void ProgStacked::showTextEditingView() {
    LOG_INFO("请求显示文本编辑视图");
    switchToView(m_textEditingView);
}

void ProgStacked::triggerProgramSelectionInLocalView() {
    LOG_INFO("开始触发本地视图中的程序选择");

    if (!m_localDataView) {
        LOG_WARN("本地数据视图未初始化");
        return;
    }

    // 获取本地数据视图选中的文件列表
    QFileInfoList selectedFiles;
    m_localDataView->getLocalSelectedFiles(selectedFiles);

    if (selectedFiles.isEmpty()) {
        LOG_INFO("未选择任何程序文件或文件夹");
        return;
    }

    // 分离文件和文件夹
    QFileInfoList files;
    QFileInfoList folders;
    for (const QFileInfo& info : selectedFiles) {
        if (info.isFile()) {
            files.append(info);
        } else if (info.isDir()) {
            folders.append(info);
        }
    }

    // 处理不同选择情况
    if (!files.isEmpty() && !folders.isEmpty()) {
        // 同时选择了文件和文件夹，不允许添加
        LOG_WARN("不能同时选择文件和文件夹");
        // QMessageBox::warning(this, tr("警告"), tr("不能同时选择文件和文件夹"));
        return;
    } else if (!folders.isEmpty()) {
        // 只选择了文件夹
        if (folders.count() > 1) {
            // 选择了多个文件夹，不允许添加
            LOG_WARN("只能选择单个文件夹");
            // QMessageBox::warning(this, tr("警告"), tr("只能选择单个文件夹"));
            return;
        } else {
            // 选择了单个文件夹，获取其中的NC文件
            QFileInfo folder = folders.first();
            LOG_INFO("选择了文件夹: {}", folder.fileName().toStdString());

            // 递归获取文件夹中的NC文件
            LOG_INFO("选择了文件夹路径为: {}", folder.absoluteFilePath().toStdString());
            QFileInfoList ncFiles = m_localDataView->getNCFilesInFolder(folder.absoluteFilePath());
            if (ncFiles.isEmpty()) {
                LOG_INFO("文件夹中没有NC文件");
                // QMessageBox::information(this, tr("提示"), tr("该文件夹中没有NC程序文件"));
                return;
            }

            LOG_INFO("从文件夹中找到{}个NC文件", ncFiles.count());
            addProgramsToTaskList(ncFiles);
            return;
        }
    } else {
        // 只选择了文件
        LOG_INFO("选中了{}个程序文件", files.count());
        addProgramsToTaskList(files);
    }

    LOG_INFO("程序选择操作完成");
}

void ProgStacked::handleHistoryFiles(const QFileInfoList& files) {
    LOG_INFO("处理历史记录文件，数量: {}", files.size());

    if (!files.isEmpty()) {
        addProgramsToTaskList(files);  // 调用现有函数
    } else {
        LOG_WARN("历史记录文件列表为空");
    }
}

void ProgStacked::addProgramsToTaskList(const QFileInfoList& fileList) {
    if (m_taskListPanel) {
        m_taskListPanel->addProgramsToTaskList(fileList);
    } else {
        LOG_WARN("任务列表面板未初始化");
    }

    LOG_INFO("程序添加到任务列表完成");
}

void ProgStacked::validateFileInfo(const QFileInfo& fileInfo) const {
    if (!fileInfo.exists()) {
        LOG_WARN("文件不存在:{}", fileInfo.filePath().toStdString());
    }

    if (!fileInfo.isFile()) {
        LOG_WARN("不是有效的文件:{}", fileInfo.filePath().toStdString());
    }

    if (!fileInfo.isReadable()) {
        LOG_WARN("文件不可读:{}", fileInfo.filePath().toStdString());
    }
}

QFileInfo ProgStacked::getFileInfoFromCurrentItem() const {
    if (m_taskListPanel) {
        return m_taskListPanel->getFileInfoFromCurrentItem();
    }
    return QFileInfo();
}

bool ProgStacked::isChannelRootSelected() const {
    if (m_taskListPanel) {
        return m_taskListPanel->isChannelRootSelected();
    }
    return true;
}

void ProgStacked::onTaskListItemSelectionChanged() {
    if (isChannelRootSelected()) {
        LOG_INFO("选中了通道根节点，清空文本编辑器");
        clearTextEditor();
        return;
    }

    QFileInfo fileInfo = getFileInfoFromCurrentItem();
    if (!fileInfo.exists()) {
        LOG_WARN("选中项对应的文件不存在或无效");
        clearTextEditor();
        return;
    }

    QString filePath = fileInfo.filePath();
    LOG_INFO("任务列表选择变化，文件路径:{}", filePath.toStdString());

    // 在文本编辑视图中打开文件
    if (m_textEditingView) {
        m_textEditingView->openProgramFile(filePath);
        LOG_INFO("在文本编辑器中打开文件:{}", fileInfo.fileName().toStdString());

        // 设置文本编辑视图获得焦点
        // setFocusToView(m_textEditingView);
    } else {
        LOG_WARN("文本编辑视图未初始化");
    }
}

void ProgStacked::saveCurrentText() { m_textEditingView->saveCurrentText(); }

void ProgStacked::textEditingBeginSelection() { m_textEditingView->beginSelection(); }

void ProgStacked::textEditingClearSelection() { m_textEditingView->clearSelection(); }

void ProgStacked::textEditingToggleSelectionSide() { m_textEditingView->toggleSelectionSide(); }

void ProgStacked::textEditingCopySelection() { m_textEditingView->copySelection(); }

void ProgStacked::textEditingCutSelection() { m_textEditingView->cutSelection(); }

void ProgStacked::textEditingPasteToCursor() { m_textEditingView->pasteToCursor(); }

void ProgStacked::textEditingSelectAllText() { m_textEditingView->selectAllText(); }

void ProgStacked::textEditingPerformUndo() { m_textEditingView->performUndo(); }

void ProgStacked::textEditingPerformRedo() { m_textEditingView->performRedo(); }

void ProgStacked::textEditingFindPrev() { m_textEditingView->findPrevInEditor(); }

void ProgStacked::textEditingFindNext() { m_textEditingView->findNextInEditor(); }

void ProgStacked::clearFocusFromAllViews() { setFocusToView(nullptr); }

void ProgStacked::setFocusToView(QWidget* view) {
    LOG_INFO("焦点视图: {}", m_focusedView ? m_focusedView->objectName().toStdString() : "无");
    LOG_INFO("要切换视图: {}", view ? view->objectName().toStdString() : "无");

    // 清除之前拥有焦点的视图的焦点
    if (m_focusedView && m_focusedView != view) {
        LOG_INFO("进入if (m_focusedView && m_focusedView != view)");
        if (m_focusedView == m_localDataView && m_localDataView) {
            LOG_INFO("执行m_localDataView->clearSelectionOnFocusLost();");
            m_localDataView->clearSelectionOnFocusLost();
        }
        m_focusedView->setFocusPolicy(Qt::NoFocus);
    }

    // 设置新的焦点
    if (view) {
        m_focusedView = view;

        // 特殊处理LocalData
        if (view == m_localDataView && m_localDataView) {
            m_localDataView->setSelectionOnFocus();
        }
        // 特殊处理任务树控件（不再需要额外操作，由CurrentTask自身监控焦点）
        else if (view == m_taskListPanel->getTaskTreeWidget()) {
            view->setFocusPolicy(Qt::StrongFocus);
            view->setFocus();
            m_taskListPanel->setSelectionOnFocus();
        } else {
            view->setFocusPolicy(Qt::StrongFocus);
            view->setFocus();
        }
    } else {
        m_focusedView = nullptr;
    }

    LOG_INFO("焦点已切换到视图: {}", view ? view->objectName().toStdString() : "无");
}

void ProgStacked::processSelectedProgram(const QString& filePath) {
    if (m_serviceBus) {
        LOG_INFO("开始准备跳转到加工处理");

        m_serviceBus->navigateToContext("MACHINE", "AUTO");

        LOG_INFO("跳转完成");
    } else {
        LOG_ERROR("m_serviceBus没有初始化，无法跳转");
    }
}

// 创建历史记录对话框
CreateHistoryDialog* ProgStacked::getLocalCreateHistoryDialog() {
    if (m_createHistoryDialog == nullptr) {
        m_createHistoryDialog = new CreateHistoryDialog(m_serviceBus, getLocalData());

        // 在对话框销毁时自动重置指针
        connect(m_createHistoryDialog, &QObject::destroyed, this, [this]() {
            m_createHistoryDialog = nullptr;
            LOG_INFO("历史记录对话框 已销毁");
        });

        // 建立信号连接，使用单次连接方式
        connect(m_createHistoryDialog, &CreateHistoryDialog::historyRecordSelected, this,
                &ProgStacked::handleHistoryRecordSelection, Qt::UniqueConnection);
        LOG_INFO("已连接CreateHistoryDialog信号");
    }
    // 更新历史记录对话框列表
    m_createHistoryDialog->loadHistoryRecords();

    // 创建弹出对话框时，不清除焦点
    if (m_localDataView) {
        m_localDataView->setKeepSelection(true);
    }

    return m_createHistoryDialog;
}

// 创建删除对话框
CreateDeleteDialog* ProgStacked::getLocalCreateDeleteDialog() {
    if (m_createDeleteDialog == nullptr) {
        m_createDeleteDialog = new CreateDeleteDialog(m_serviceBus, getLocalData(), getCurrentTask());

        // 接收destroyed信号
        connect(m_createDeleteDialog, &QObject::destroyed, this, [this]() {
            m_createDeleteDialog = nullptr;
            LOG_INFO("CreateDeleteDialog 已销毁");
        });
    }

    if (m_focusedView == m_localDataView) {
        m_createDeleteDialog->setDeletionType(CreateDeleteDialog::LocalFiles);
        // 创建弹出对话框时，不清除焦点
        if (m_localDataView) {
            m_localDataView->setKeepSelection(true);
        }
    } else if (m_focusedView == m_taskListPanel->getTaskTreeWidget()) {
        m_createDeleteDialog->setDeletionType(CreateDeleteDialog::TaskItems);
    }

    return m_createDeleteDialog;
}

// 创建新建 文件/文件夹 对话框
CreateNewItemDialog* ProgStacked::getLocalCreateNewItemDialog() {
    if (m_createNewItemDialog == nullptr) {
        m_createNewItemDialog = new CreateNewItemDialog(m_serviceBus, getLocalData());

        //  设置对话框初始状态（默认选中“新建NC文件”）
        m_createNewItemDialog->setCurrentOperationType(CreateNewItemDialog::CreateFile);

        // 接收destroyed信号
        connect(m_createNewItemDialog, &QObject::destroyed, this, [this]() {
            m_createNewItemDialog = nullptr;
            LOG_INFO("CreateNewItemDialog 已销毁");
        });
    }
    return m_createNewItemDialog;
}

// 创建重命名对话框
CreateRenameDialog* ProgStacked::getLocalCreateRenameDialog() {
    if (m_createRenameDialog == nullptr) {
        m_createRenameDialog = new CreateRenameDialog(m_serviceBus, getLocalData());

        // 接收destroyed信号
        connect(m_createRenameDialog, &QObject::destroyed, this, [this]() {
            m_createRenameDialog = nullptr;
            LOG_INFO("CreateRenameDialog 已销毁");
        });
    }

    // 创建弹出对话框时，不清除焦点
    if (m_localDataView) {
        m_localDataView->setKeepSelection(true);
    }

    return m_createRenameDialog;
}

LocalData* ProgStacked::getLocalData() {
    if (m_localDataView == nullptr) {
        m_localDataView = new LocalData(m_serviceBus, this);

        // 接收destroyed信号
        connect(m_localDataView, &QObject::destroyed, this, [this]() {
            m_localDataView = nullptr;
            LOG_INFO("LocalData destroyed");
        });

        // 连接所有需要的信号
    }
    return m_localDataView;
}

CurrentTask* ProgStacked::getCurrentTask() {
    if (m_taskListPanel == nullptr) {
        m_taskListPanel = new CurrentTask(m_serviceBus, this);

        // 接收destroyed信号
        connect(m_taskListPanel, &QObject::destroyed, this, [this]() {
            m_taskListPanel = nullptr;
            LOG_INFO("CurrentTask destroyed");
        });

        // 连接所有需要的信号
    }
    return m_taskListPanel;
}

void ProgStacked::onUsbInserted(int subdirCount, const QStringList& subdirPaths) {
    LOG_INFO("USB设备插入，子目录数量:{}", subdirCount);

    // 输出所有子目录路径
    for (const QString& qStr : subdirPaths) {
        LOG_INFO("子目录路径:{}", qStr.toStdString());
    }

    // 获取第一个USB路径并传递给视图
    if (!subdirPaths.isEmpty()) {
        QString firstUsbPath = subdirPaths.first();  // 取第一个路径
        LOG_INFO("第一个U盘路径: {}", firstUsbPath.toStdString());

        // 切换到U盘视图并设置第一个路径
        m_usbPath = firstUsbPath;
    } else {
        LOG_WARN("USB插入但未检测到有效子目录路径");
    }

    if (subdirCount > 0) {
        QVariantMap states;
        states["enabled"] = true;
        if (m_serviceBus) {
            m_serviceBus->updateMenuActionState("ProgManage.usbDiskDataView", states);
        }
    }
    // 可以在这里添加处理USB设备的逻辑
    // 例如：自动读取设备上的文件
}

void ProgStacked::onUsbRemoved(int remainingCount, const QStringList& remainingPaths) {
    for (const QString& qStr : remainingPaths) {
        LOG_INFO("USB设备移除，剩余子目录数量:{}", qStr.toStdString());
    }

    if (remainingCount == 0) {
        if (m_serviceBus) {
            QVariantMap states;
            states["enabled"] = false;
            m_serviceBus->updateMenuActionState("ProgManage.usbDiskDataView", states);
        }
    }
    // 可以在这里添加清理逻辑
}

void ProgStacked::onTextEditSelectionBegun() {
    QVariantMap states;
    states["enabled"] = false;
    m_serviceBus->updateMenuActionState("ProgManage.textEditingBeginSelection", states);
    states["enabled"] = true;
    m_serviceBus->updateMenuActionState("ProgManage.textEditingClearSelection", states);
    m_serviceBus->updateMenuActionState("ProgManage.textEditingToggleSelectionSide", states);
    m_serviceBus->updateMenuActionState("ProgManage.textEditingCopySelection", states);
    m_serviceBus->updateMenuActionState("ProgManage.textEditingCutSelection", states);
}

void ProgStacked::onTextEditSelectionCleared() {
    QVariantMap states;
    states["enabled"] = true;
    m_serviceBus->updateMenuActionState("ProgManage.textEditingBeginSelection", states);
    states["enabled"] = false;
    m_serviceBus->updateMenuActionState("ProgManage.textEditingClearSelection", states);
    m_serviceBus->updateMenuActionState("ProgManage.textEditingToggleSelectionSide", states);
    m_serviceBus->updateMenuActionState("ProgManage.textEditingCopySelection", states);
    m_serviceBus->updateMenuActionState("ProgManage.textEditingCutSelection", states);
}

void ProgStacked::onTextEditUndoAvailabilityChanged(const bool available) {
    QVariantMap states;
    states["enabled"] = available;
    m_serviceBus->updateMenuActionState("ProgManage.textEditingPerformUndo", states);
}

void ProgStacked::onTextEditRedoAvailabilityChanged(const bool available) {
    QVariantMap states;
    states["enabled"] = available;
    m_serviceBus->updateMenuActionState("ProgManage.textEditingPerformRedo", states);
}

// 处理历史记录选中事件
void ProgStacked::handleHistoryRecordSelection(const QFileInfoList& fileInfoList) {
    LOG_INFO("接收到历史记录选中信号，文件数量: {}", fileInfoList.size());
    addProgramsToTaskList(fileInfoList);  // 调用现有函数添加到任务列表
}

void ProgStacked::selectAllProgramsInLocalView() {
    LOG_INFO("请求选择本地视图中的所有程序");
    if (m_localDataView) {
        m_localDataView->selectAllFiles();
        LOG_INFO("本地视图全选操作完成");
    } else {
        LOG_WARN("本地数据视图未初始化");
    }
}

void ProgStacked::selectInvertProgramsInLocalView() {
    LOG_INFO("请求反选本地视图中的程序");
    if (m_localDataView) {
        m_localDataView->invertSelectFiles();
        LOG_INFO("本地视图反选操作完成");
    } else {
        LOG_WARN("本地数据视图未初始化");
    }
}

void ProgStacked::copySelectedFilesInLocalView() {
    LOG_INFO("请求在本地视图中复制所选文件");
    if (m_localDataView) {
        m_localDataView->copySelectedFiles();
        LOG_INFO("本地视图复制文件操作完成");
    } else {
        LOG_WARN("本地数据视图未初始化");
    }
}

void ProgStacked::cutSelectedFilesInLocalView() {
    LOG_INFO("请求在本地视图中剪贴所选文件");
    if (m_localDataView) {
        m_localDataView->cutSelectedFiles();
        LOG_INFO("本地视图剪贴文件操作完成");
    } else {
        LOG_WARN("本地数据视图未初始化");
    }
}

void ProgStacked::pasteFilesInLocalView() {
    LOG_INFO("请求在本地视图中粘贴所选文件");
    if (m_localDataView) {
        m_localDataView->pasteFiles();
        LOG_INFO("本地视图粘贴文件操作完成");
    } else {
        LOG_WARN("本地数据视图未初始化");
    }
}

void ProgStacked::deselectAllProgramsInLocalView() {
    LOG_INFO("请求清除选择本地视图中的所有程序");
    if (m_localDataView) {
        m_localDataView->deselectAllFiles();
        LOG_INFO("本地视图清除操作完成");
    } else {
        LOG_WARN("本地数据视图未初始化");
    }
}

void ProgStacked::removeCurrentTaskFromList() {
    LOG_INFO("开始从任务列表中移除当前项");

    if (m_taskListPanel) {
        m_taskListPanel->removeCurrentTaskFromList();
    } else {
        LOG_WARN("任务列表面板未初始化");
    }

    LOG_INFO("任务列表中移除当前项完成");
}

void ProgStacked::clearAllTasksFromList() {
    LOG_INFO("开始清空所有任务列表");

    if (m_taskListPanel) {
        m_taskListPanel->clearAllTasksFromList();
    } else {
        LOG_WARN("任务列表面板未初始化");
    }

    LOG_INFO("所有任务列表清空完成");
}

void ProgStacked::setDefaultFocusToTaskPanel() {
    LOG_INFO("设置默认焦点到任务面板");
    if (m_taskListPanel) {
        m_taskListPanel->setFocusToTaskTree();
        // 清除其他视图的焦点
        clearFocusFromAllViews();
        m_focusedView = m_taskListPanel;
        LOG_INFO("任务面板焦点设置完成");
    } else {
        LOG_WARN("任务列表面板未初始化");
    }
}

void ProgStacked::switchFocusBetweenPanels() {
    LOG_INFO("开始在面板间切换焦点");

    if (!m_currentActiveView) {
        LOG_WARN("当前没有激活的视图，无法切换焦点");
        return;
    }

    if (m_focusedView == m_currentActiveView) {
        // 焦点在当前视图，切换到任务面板
        LOG_INFO("焦点从内容面板切换到任务面板");
        if (m_taskListPanel) {
            setFocusToView(m_taskListPanel->getTaskTreeWidget());
        } else {
            LOG_WARN("任务列表面板未初始化");
        }
    } else {
        // 焦点在任务面板，切换到当前视图
        LOG_INFO("焦点从任务面板切换到内容面板");
        setFocusToView(m_currentActiveView);
    }

    LOG_INFO("面板焦点切换完成");
}

void ProgStacked::clearProgramSelectionIndicator() {
    LOG_INFO("清除程序选择标记");
    if (m_localDataView) {
        m_localDataView->clearSelectedFiles();
        LOG_INFO("程序选择标记清除完成");
    } else {
        LOG_WARN("本地数据视图未初始化");
    }
}

void ProgStacked::clearTextEditor() {
    LOG_INFO("清空文本编辑器");
    if (m_textEditingView) {
        m_textEditingView->clearEditorContent();
        LOG_INFO("文本编辑器清空完成");
    } else {
        LOG_WARN("文本编辑视图未初始化");
    }
}

void ProgStacked::TestUsb() {
    // 测试USB功能的实现
    LOG_INFO("开始测试USB功能");
    showUsbDiskDataView();
}

void ProgStacked::setInitialFocusToTaskPanel() {
    LOG_INFO("设置初始焦点到任务面板");
    if (m_taskListPanel) {
        // 使用延迟设置焦点，确保界面完全显示后再设置焦点
        QTimer::singleShot(0, this, [this]() {
            if (m_taskListPanel) {
                m_taskListPanel->setFocusToTaskTree();
                // 清除其他视图的焦点
                clearFocusFromAllViews();
                m_focusedView = m_taskListPanel;
                LOG_INFO("初始焦点已设置到任务面板");
            }
        });
    } else {
        LOG_WARN("任务列表面板未初始化，无法设置初始焦点");
    }
}
