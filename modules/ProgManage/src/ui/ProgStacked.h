#ifndef PROGSTACKED_H
#define PROGSTACKED_H

#include <QFileInfo>
#include <QFileInfoList>
#include <QFrame>
#include <QGridLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QModelIndex>
#include <QString>
#include <QTimer>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QVBoxLayout>
#include <QWidget>
#include <string>

#include "./dialogBox/CreateDeleteDialog.h"
#include "./dialogBox/CreateHistoryDialog.h"
#include "./dialogBox/CreateNewItemDialog.h"
#include "./dialogBox/CreateRenameDialog.h"
#include "CurrentTask.h"
#include "ICncInterface.h"
#include "IServiceBus.h"
#include "LocalData.h"
#include "NCCreation.h"
#include "NetData.h"
#include "ProgStackedConstants.h"
#include "TextEditing.h"
#include "UsbDiskData.h"

/**
 * @brief 程序管理主界面类
 *
 * 负责管理程序文件的显示、编辑和任务列表操作
 * 包含任务列表面板和内容显示面板的切换管理
 *
 * 主要功能：
 * - 多种数据源视图的切换（本地、U盘、网络）
 * - 程序文件的任务列表管理
 * - 文本编辑和图形预览
 * - 焦点管理和用户交互
 *
 * 架构特点：
 * - 使用QTreeWidgetItem::setData()直接存储文件信息，避免并行列表同步问题
 * - 统一的视图切换机制，支持多种数据源
 * - 完善的错误处理和日志记录
 * - 遵循中文注释和日志规范
 *
 * @version 2.0.0 (重构版本)
 * @date 2024
 * @note 本版本已完全重构，移除了并行列表依赖，提高了代码健壮性
 */
class ProgStacked : public QWidget {
    Q_OBJECT

   public:
    explicit ProgStacked(IServiceBus* serviceBus, QWidget* parent = nullptr);
    ~ProgStacked();

    // 视图切换方法
    void showLocalDataView();    // 显示本地数据视图
    void showNetDataView();      // 显示网络数据视图
    void showUsbDiskDataView();  // 显示U盘数据视图
    void showTextEditingView();  // 显示文本编辑视图

    // 程序文件操作方法
    void triggerProgramSelectionInLocalView();             // 触发本地视图中的程序选择
    void processSelectedProgram(const QString& filePath);  // 准备当前程序用于加工
    void selectAllProgramsInLocalView();                   // 选择本地视图中的所有程序
    void selectInvertProgramsInLocalView();                // 反选本地视图中的程序
    void deselectAllProgramsInLocalView();                 // 清除选择本地视图中的所有程序
    void copySelectedFilesInLocalView();                   // 在本地视图中复制所选文件
    void cutSelectedFilesInLocalView();                    // 在本地视图中剪贴所选文件
    void pasteFilesInLocalView();                          // 在本地视图中粘贴所选文件
    void saveCurrentText();

    // 程序文件编辑方法
    void textEditingBeginSelection();       // 文本编辑器开始选区
    void textEditingClearSelection();       // 文本编辑器结束选区
    void textEditingToggleSelectionSide();  // 文本编辑器切换选区首尾
    void textEditingCopySelection();        // 文本编辑器复制选区
    void textEditingCutSelection();         // 文本编辑器
    void textEditingPasteToCursor();        // 文本编辑器粘贴至光标
    void textEditingSelectAllText();        // 文本编辑器全选文本
    void textEditingPerformUndo();          // 文本编辑器进行撤销操作
    void textEditingPerformRedo();          // 文本编辑器进行重做操作
    void textEditingFindPrev();             // 文本编辑器查找上一个
    void textEditingFindNext();             // 文本编辑器查找下一个

    // 任务列表管理方法
    void removeCurrentTaskFromList();  // 从任务列表中移除当前项
    void clearAllTasksFromList();      // 清空任务列表

    // 界面控制方法
    void clearProgramSelectionIndicator();  // 清除程序选择标记
    void clearTextEditor();                 // 清空文本编辑器

    // 焦点管理方法
    void setFocusToView(QWidget* view);  // 设置焦点到指定视图
    void clearFocusFromAllViews();       // 清除所有视图的焦点
    void switchFocusBetweenPanels();     // 开始在面板间切换焦点
    void setDefaultFocusToTaskPanel();   // 设置默认焦点到任务面板
    void setInitialFocusToTaskPanel();   // 设置初始焦点到任务面板

    // 弹窗相关方法
    CreateHistoryDialog* getLocalCreateHistoryDialog();  // 历史记录对话框
    CreateDeleteDialog* getLocalCreateDeleteDialog();    // 删除对话框
    CreateNewItemDialog* getLocalCreateNewItemDialog();  // 新建文本/文件夹对话框
    CreateRenameDialog* getLocalCreateRenameDialog();    // 重命名对话框
    LocalData* getLocalData();
    CurrentTask* getCurrentTask();

    void handleHistoryFiles(const QFileInfoList& files);  //

    bool updateUsbInterface();

    void TestUsb();
    void onProgramSelected();

   private slots:
    void onTaskListItemSelectionChanged();                                 // 任务列表项选择变化时的响应
    void handleHistoryRecordSelection(const QFileInfoList& fileInfoList);  //

    void onUsbInserted(int subdirCount, const QStringList& subdirPaths);       // USB设备插入事件处理
    void onUsbRemoved(int remainingCount, const QStringList& remainingPaths);  // USB设备移除事件处理

    void onTextEditSelectionBegun();                              // 文本编辑选区开始响应
    void onTextEditSelectionCleared();                            // 文本编辑选区清除响应
    void onTextEditUndoAvailabilityChanged(const bool avalable);  // 文本编辑撤销可用性改变
    void onTextEditRedoAvailabilityChanged(const bool avalable);  // 文本编辑重做可用性改变

   protected:
    void keyPressEvent(QKeyEvent* event) override;  // 键盘事件处理

   private:
    // UI设置方法
    void setupUi();                   // 设置界面布局和控件
    void setupTaskListPanel();        // 设置任务列表面板
    void setupContentDisplayPanel();  // 设置内容显示面板
    void initializeViewComponents();  // 初始化视图组件
    void connectSignalsAndSlots();    // 连接信号和槽

    // 视图管理方法
    void switchToView(QWidget* targetView);  // 统一的视图切换方法
    QString getCurrentViewName() const;      // 获取当前视图名称

    // 任务列表管理方法
    void addProgramsToTaskList(const QFileInfoList& fileList);  // 添加程序到任务列表
    QFileInfo getFileInfoFromCurrentItem() const;               // 从当前选中项获取文件信息
    bool isChannelRootSelected() const;                         // 判断是否选中了通道根节点

    // 辅助方法
    void logServiceBusStatus();                              // 记录服务总线状态
    void validateFileInfo(const QFileInfo& fileInfo) const;  // 验证文件信息

    // 主布局
    QVBoxLayout* m_mainLayout = nullptr;

    // 工作区布局
    QWidget* m_progStackedWidget = nullptr;
    QHBoxLayout* m_progStackedLayout = nullptr;

    // 任务列表面板
    // QFrame* m_taskListPanel = nullptr;        // 任务列表面板
    // QVBoxLayout* m_taskListLayout = nullptr;  // 任务列表布局
    // QTreeWidget* m_taskTreeWidget = nullptr;  // 任务树控件

    // 内容显示面板
    QFrame* m_contentDisplayPanel = nullptr;        // 内容显示面板
    QVBoxLayout* m_contentDisplayLayout = nullptr;  // 内容显示布局

    // 各种数据视图组件
    CurrentTask* m_taskListPanel = nullptr;    // 任务面板
    LocalData* m_localDataView = nullptr;      // 本地数据视图
    NetData* m_netDataView = nullptr;          // 网络数据视图
    UsbDiskData* m_usbDataView = nullptr;      // U盘数据视图
    TextEditing* m_textEditingView = nullptr;  // 文本编辑视图

    // 弹窗视图组件
    CreateHistoryDialog* m_createHistoryDialog = nullptr;  // 添加本地历史记录弹窗成员变量
    CreateDeleteDialog* m_createDeleteDialog = nullptr;    // 添加本地删除弹窗成员变量
    CreateNewItemDialog* m_createNewItemDialog = nullptr;  // 添加本地创建文件弹窗成员变量
    CreateRenameDialog* m_createRenameDialog = nullptr;    // 添加本地重命名文件弹窗成员变量

    // 当前状态管理
    QWidget* m_currentActiveView = nullptr;        // 当前激活的视图
    QTreeWidgetItem* m_channelRootItem = nullptr;  // 通道根节点

    // 文件数据管理（已废弃，保留用于兼容性）
    QFileInfoList m_selectedFileList;        // 选中的文件列表（已废弃）
    QFileInfoList m_programFilesForChannel;  // 通道的程序文件列表（已废弃）

    // 服务接口
    IServiceBus* m_serviceBus = nullptr;  // 服务总线接口

    // 状态标志
    bool m_isFocusOnTaskPanel = false;  // 焦点是否在任务面板上

    // 弹出框存储
    NCCreation* m_popup = nullptr;  // 存储弹出框指针

    QWidget* m_focusedView = nullptr;  // 当前拥有焦点的视图

    QString m_usbPath = "";
};

#endif  // PROGSTACKED_H
