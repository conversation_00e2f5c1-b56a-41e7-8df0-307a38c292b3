#include "TextEditing.h"

#include "FocusManager.h"
#include "core/AppLogger.h"

TextEditing::TextEditing(QWidget* parent) : FocusablePanel(parent) {
    setObjectName("TextEditing");
    LOG_INFO("文本编辑界面开始初始化");
    setupUi();
}

TextEditing::~TextEditing() {
    // Qt 会自动处理子控件的删除
    if (m_gCodeEditor) {
        delete m_gCodeEditor;
        m_gCodeEditor = NULL;
    }
}

void TextEditing::setupUi() {
    LOG_INFO("开始设置文本编辑界面UI");

    // 创建主布局
    m_mainLayout = new QVBoxLayout(contentArea());
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);

    // 创建编辑器框架
    m_editorFrame = new QFrame(this);
    m_editorFrame->setObjectName("editorFrame");
    m_editorLayout = new QVBoxLayout(m_editorFrame);
    m_editorLayout->setContentsMargins(0, 0, 0, 0);
    m_editorLayout->setSpacing(0);

    // 设置搜索区域的Layout
    m_searchLayout = new QHBoxLayout(this);
    m_searchLayout->setContentsMargins(6, 3, 25, 3);  // 设置边距
    m_searchLayout->setSpacing(6);                    // 设置间距
    m_statusLabel = new QLabel("【未选择文件】", this);
    m_searchLayout->addWidget(m_statusLabel);
    m_searchLayout->addStretch();
    m_hintLabel = new QLabel(tr("查找"), this);
    m_searchLayout->addWidget(m_hintLabel);
    m_searchBox = new QLineEdit();
    m_searchBox->setObjectName("searchBox");
    m_searchBox->setMinimumSize(QSize(120, 30));
    m_searchBox->setMaximumSize(QSize(120, 30));
    m_searchLayout->addWidget(m_searchBox);

    // 设置G代码编辑器
    setupGCodeEditor();

    // 连接信号和槽
    connectSignalsAndSlots();

    // 添加到布局
    m_editorLayout->addWidget(m_gCodeEditor);
    m_editorLayout->addLayout(m_searchLayout);
    m_mainLayout->addWidget(m_editorFrame);
    contentArea()->setLayout(m_mainLayout);  // 设置主布局
    // 不需要标题栏，标题栏隐藏
    titleBarArea()->setVisible(false);

    // 注册到焦点管理器
    auto focusManager = FocusManager::instance();
    focusManager->registerWidget(m_gCodeEditor);
    focusManager->registerWidget(m_searchBox);
}

void TextEditing::handleMenuAction(const QString& actionId, const QVariantMap& states) {
    LOG_DEBUG("TextEditing::handleMenuAction: actionId={}", actionId.toStdString());
    // 这里可以根据需要添加具体的处理逻辑

    Q_UNUSED(states)
}

void TextEditing::saveCurrentText() {
    LOG_INFO("开始保存当前文本");
    saveProgramFile(m_currentFilePath);
}

void TextEditing::beginSelection() {
    LOG_INFO("开始选区");

    if (!m_gCodeEditor) {
        LOG_ERROR("G代码编辑器未初始化");
        return;
    }

    m_gCodeEditor->beginSelection();
}

void TextEditing::clearSelection() {
    LOG_INFO("结束选区");

    if (!m_gCodeEditor) {
        LOG_ERROR("G代码编辑器未初始化");
        return;
    }

    m_gCodeEditor->clearSelection();
}

void TextEditing::toggleSelectionSide() {
    LOG_INFO("切换选区首尾");

    if (!m_gCodeEditor) {
        LOG_ERROR("G代码编辑器未初始化");
        return;
    }

    m_gCodeEditor->toggleSelectionSide();
}

void TextEditing::copySelection() {
    LOG_INFO("复制选区");

    if (!m_gCodeEditor) {
        LOG_ERROR("G代码编辑器未初始化");
        return;
    }

    m_gCodeEditor->copySelection();
}

void TextEditing::cutSelection() {
    LOG_INFO("剪切选区");

    if (!m_gCodeEditor) {
        LOG_ERROR("G代码编辑器未初始化");
        return;
    }

    m_gCodeEditor->cutSelection();
}

void TextEditing::pasteToCursor() {
    LOG_INFO("粘贴至光标");

    if (!m_gCodeEditor) {
        LOG_ERROR("G代码编辑器未初始化");
        return;
    }

    m_gCodeEditor->pasteToCursor();
}

void TextEditing::selectAllText() {
    LOG_INFO("全选文本");

    if (!m_gCodeEditor) {
        LOG_ERROR("G代码编辑器未初始化");
        return;
    }

    m_gCodeEditor->selectAllText();
}

void TextEditing::performUndo() {
    LOG_INFO("撤销");

    if (!m_gCodeEditor) {
        LOG_ERROR("G代码编辑器未初始化");
        return;
    }

    m_gCodeEditor->performUndo();
}

void TextEditing::performRedo() {
    LOG_INFO("重做");

    if (!m_gCodeEditor) {
        LOG_ERROR("G代码编辑器未初始化");
        return;
    }

    m_gCodeEditor->performRedo();
}

void TextEditing::findPrevInEditor() {
    LOG_INFO("查找上一个");

    if (!m_gCodeEditor) {
        LOG_ERROR("G代码编辑器未初始化");
        return;
    }

    if (m_searchBox->text().isEmpty()) {
        m_hintLabel->setStyleSheet("QLabel { color : red; }");
        m_hintLabel->setText(tr("查找内容为空"));

        return;
    }

    bool found = m_gCodeEditor->jumpToPrev(m_searchBox->text());

    if (found) {
        m_gCodeEditor->setFocus();
        m_hintLabel->setStyleSheet("QLabel { color : black; }");
        m_hintLabel->setText(tr("查找"));
    } else {
        m_hintLabel->setStyleSheet("QLabel { color : red; }");
        m_hintLabel->setText(tr("找不到上一个"));
    }
}

void TextEditing::findNextInEditor() {
    LOG_INFO("查找下一个");

    if (!m_gCodeEditor) {
        LOG_ERROR("G代码编辑器未初始化");
        return;
    }

    if (m_searchBox->text().isEmpty()) {
        m_hintLabel->setStyleSheet("QLabel { color : red; }");
        m_hintLabel->setText(tr("查找内容为空"));

        return;
    }

    bool found = m_gCodeEditor->jumpToNext(m_searchBox->text());

    if (found) {
        m_gCodeEditor->setFocus();
        m_hintLabel->setStyleSheet("QLabel { color : black; }");
        m_hintLabel->setText(tr("查找"));
    } else {
        m_hintLabel->setStyleSheet("QLabel { color : red; }");
        m_hintLabel->setText(tr("找不到下一个"));
    }
}

void TextEditing::setupGCodeEditor() {
    LOG_INFO("开始设置G代码编辑器");

    m_gCodeEditor = new GCodeEditor(m_editorFrame);
    if (!m_gCodeEditor) {
        LOG_ERROR("G代码编辑器创建失败");
        return;
    }
}

void TextEditing::connectSignalsAndSlots() {
    LOG_INFO("开始连接文本编辑器信号和槽");

    if (m_gCodeEditor) {
        // 连接文本变化信号
        connect(m_gCodeEditor, &QsciScintilla::textChanged, this, &TextEditing::onEditorContentChanged);
        connect(m_gCodeEditor, &GCodeEditor::selectionBegun, this, &TextEditing::onEditorSelectionBegun);
        connect(m_gCodeEditor, &GCodeEditor::selectionCleared, this, &TextEditing::onEditorSelectionCleared);
        connect(m_gCodeEditor, &GCodeEditor::undoAvailabilityChanged, this, &TextEditing::onUndoAvailabilityChanged);
        connect(m_gCodeEditor, &GCodeEditor::redoAvailabilityChanged, this, &TextEditing::onRedoAvailabilityChanged);
        LOG_INFO("文本编辑器信号和槽连接完成");
    } else {
        LOG_WARN("G代码编辑器未初始化，无法连接信号");
    }
}

void TextEditing::openProgramFile(const QString& filePath) {
    LOG_INFO("开始打开程序文件:{}", filePath.toStdString());

    if (filePath.isEmpty()) {
        LOG_WARN("文件路径为空");
        return;
    }

    if (!m_gCodeEditor) {
        LOG_ERROR("G代码编辑器未初始化");
        return;
    }

    try {
        m_gCodeEditor->loadGCodeFile(filePath);
        m_currentFilePath = filePath;
        m_hasUnsavedChanges = false;
        LOG_INFO("程序文件打开成功:{}", filePath.toStdString());

        m_statusLabel->setText("【文件已打开】");
    } catch (const std::exception& e) {
        LOG_ERROR("打开程序文件失败:{}", e.what());
    }
}

void TextEditing::clearEditorContent() {
    LOG_INFO("开始清空编辑器内容");

    if (!m_gCodeEditor) {
        LOG_WARN("G代码编辑器未初始化");
        return;
    }

    m_gCodeEditor->clear();
    m_currentFilePath.clear();
    m_hasUnsavedChanges = false;

    LOG_INFO("编辑器内容清空完成");
}

void TextEditing::saveProgramFile(const QString& filePath) {
    LOG_INFO("开始保存程序文件:{}", filePath.toStdString());

    if (!m_gCodeEditor) {
        LOG_ERROR("G代码编辑器未初始化");
        return;
    }

    QString saveFilePath = filePath.isEmpty() ? m_currentFilePath : filePath;

    if (saveFilePath.isEmpty()) {
        LOG_WARN("保存路径为空");
        return;
    }

    try {
        m_gCodeEditor->saveGCodeFile(saveFilePath);
        m_currentFilePath = saveFilePath;
        m_hasUnsavedChanges = false;
        LOG_INFO("程序文件保存成功:{}", saveFilePath.toStdString());

        m_statusLabel->setText("【文件已保存】");
    } catch (const std::exception& e) {
        LOG_ERROR("保存程序文件失败:{}", e.what());
    }
}

GCodeEditor* TextEditing::getGCodeEditor() const { return m_gCodeEditor; }

bool TextEditing::hasUnsavedChanges() const { return m_hasUnsavedChanges; }

QString TextEditing::getCurrentFilePath() const { return m_currentFilePath; }

void TextEditing::onEditorContentChanged() {
    if (!m_hasUnsavedChanges) {
        m_hasUnsavedChanges = true;
        LOG_INFO("编辑器内容已修改，标记为未保存状态");

        m_statusLabel->setText("【文件已修改】");
    }
}

void TextEditing::onEditorSelectionBegun() {
    LOG_INFO("编辑器开始选区");

    emit editorSelectionBegun();
}

void TextEditing::onEditorSelectionCleared() {
    LOG_INFO("编辑器清除选区");

    emit editorSelectionCleared();
}

void TextEditing::onUndoAvailabilityChanged(const bool available) { emit editorUndoAvailabilityChanged(available); }

void TextEditing::onRedoAvailabilityChanged(const bool available) { emit editorRedoAvailabilityChanged(available); }
