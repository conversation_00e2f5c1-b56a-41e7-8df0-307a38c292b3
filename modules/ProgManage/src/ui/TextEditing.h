#ifndef TEXTEDITING_H
#define TEXTEDITING_H

#include <QFrame>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QVBoxLayout>
#include <QWidget>

#include "FocusablePanel.h"
#include "core/GCodeEditor.h"

/**
 * @brief 文本编辑界面类
 *
 * 负责G代码程序文件的文本编辑功能
 * 使用专业的GCodeEditor提供语法高亮、自动补全等功能
 *
 * 主要功能：
 * - G代码文件的打开和显示
 * - 语法高亮和代码补全
 * - 文本编辑和保存
 * - 清空编辑器内容
 */
class TextEditing : public FocusablePanel {
    Q_OBJECT

   public:
    explicit TextEditing(QWidget* parent = nullptr);
    ~TextEditing();

    // 文件操作方法
    void openProgramFile(const QString& filePath);  // 打开程序文件（原OpenProg）
    void clearEditorContent();                      // 清空编辑器内容（原ClearText）
    void saveProgramFile(const QString& filePath);  // 保存程序文件
    void saveCurrentText();

    // 文件编辑方法
    void beginSelection();       // 开始选区
    void clearSelection();       // 清除选区
    void toggleSelectionSide();  // 切换选区首尾
    void copySelection();        // 复制选区
    void cutSelection();         // 剪切选区
    void pasteToCursor();        // 粘贴至光标
    void selectAllText();        // 全选文本
    void performUndo();          // 进行撤销操作
    void performRedo();          // 进行重做操作
    void findPrevInEditor();     // 查找上一个
    void findNextInEditor();     // 查找下一个

    // 编辑器访问方法
    GCodeEditor* getGCodeEditor() const;  // 获取G代码编辑器实例
    bool hasUnsavedChanges() const;       // 检查是否有未保存的更改
    QString getCurrentFilePath() const;   // 获取当前文件路径

   signals:
    void editorSelectionBegun();                              // 编辑器选区开始信号
    void editorSelectionCleared();                            // 编辑器选区结束信号
    void editorUndoAvailabilityChanged(const bool avalable);  // 编辑器撤销可用性变化信号
    void editorRedoAvailabilityChanged(const bool avalable);  // 编辑器重做可用性变化信号

   private slots:
    void onEditorContentChanged();                        // 编辑器内容变化时的响应
    void onEditorSelectionBegun();                        // 编辑器开始选区时的响应
    void onEditorSelectionCleared();                      // 编辑器结束选区时的响应
    void onUndoAvailabilityChanged(const bool avalable);  // 编辑器撤销可用性改变时的响应
    void onRedoAvailabilityChanged(const bool avalable);  // 编辑器重做可用性改变时的响应

   protected:
    void handleMenuAction(const QString& actionId, const QVariantMap& states) override;

   private:
    void setupUi();                 // 设置界面布局和控件
    void setupGCodeEditor();        // 设置G代码编辑器
    void connectSignalsAndSlots();  // 连接信号和槽

    // 主布局
    QVBoxLayout* m_mainLayout = nullptr;

    // 工作区
    QFrame* m_editorFrame = nullptr;        // 编辑器框架（原m_textEditingWidget）
    QVBoxLayout* m_editorLayout = nullptr;  // 编辑器布局（原m_textEditingLayout）

    // G代码编辑器
    GCodeEditor* m_gCodeEditor = nullptr;   // G代码编辑器（替代m_qsciTestEdit）
    QHBoxLayout* m_searchLayout = nullptr;  // 搜索区域的Layout
    QLabel* m_statusLabel = nullptr;        // 状态标签
    QLabel* m_hintLabel = nullptr;          // 提示标签
    QLineEdit* m_searchBox = nullptr;       // 搜索框

    // 状态管理
    QString m_currentFilePath;         // 当前文件路径
    bool m_hasUnsavedChanges = false;  // 是否有未保存的更改
};

#endif  // TEXTEDITING_H