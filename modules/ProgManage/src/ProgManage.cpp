#include "ProgManage.h"

#include <QApplication>
#include <QFile>
#include <QFileSystemModel>
#include <QJsonDocument>
#include <QLabel>
#include <QTextStream>
#include <QTreeView>
#include <QVBoxLayout>
#include <QWidget>

#include "core/AppLogger.h"
#include "public/PublicEventConstants.h"

ProgManage::ProgManage(QObject* parent) : IModule() { LOG_INFO("程序管理模块实例已创建"); }

ProgManage::~ProgManage() {
    // 停止计时器
    m_timerEvent->stop();
    LOG_INFO("程序管理模块实例已销毁");
}

QString ProgManage::moduleId() const { return "ProgManage"; }

QString ProgManage::moduleDisplayName() const { return tr("程序管理"); }

bool ProgManage::initialize(IServiceBus* serviceBus) {
    LOG_INFO("正在初始化程序管理模块...");
    m_serviceBus = serviceBus;

    connect(m_serviceBus, &IServiceBus::globalEvent, this, &ProgManage::onGlobalEvent);

    // 加载菜单描述文件
    QFile file(":/progmanage/menu.json");
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        LOG_WARN("无法打开菜单描述文件: {}", file.fileName().toStdString());
        return false;
    }

    QByteArray jsonData = file.readAll();
    file.close();

    QJsonDocument doc = QJsonDocument::fromJson(jsonData);
    if (doc.isNull() || !doc.isObject()) {
        LOG_ERROR("解析菜单JSON文件失败");
        return false;
    }
    m_menuCache = doc.object();

    // 初始化动作处理器映射表
    initializeActionHandlers();

    // 创建计时器
    m_timerEvent = new QTimer(this);
    // 连接信号和槽
    connect(m_timerEvent, &QTimer::timeout, this, &ProgManage::updateEvent);
    // 启动计时器
    // m_timerEvent->start(1000);  // 每秒更新一次时钟

    LOG_INFO("程序管理模块初始化完成");
    return true;
}

void ProgManage::shutdown() {
    LOG_INFO("正在关闭程序管理模块...");
    m_serviceBus = nullptr;
    m_menuCache = QJsonObject();
    m_actionHandlers.clear();
    LOG_INFO("程序管理模块已关闭");
}

QJsonObject ProgManage::menuDescriptor() const { return m_menuCache; }

void ProgManage::initializeActionHandlers() {
    // 视图切换动作
    m_actionHandlers["ProgManage.localDataView"] = [this]() {
        if (m_progStacked) {
            m_progStacked->showLocalDataView();
            LOG_INFO("切换到本地数据视图");
        }
    };

    m_actionHandlers["ProgManage.usbDiskDataView"] = [this]() {
        if (m_progStacked) {
            m_progStacked->showUsbDiskDataView();
            LOG_INFO("切换到U盘数据视图");
        }
    };

    m_actionHandlers["ProgManage.netDataView"] = [this]() {
        if (m_progStacked) {
            m_progStacked->showNetDataView();
            LOG_INFO("切换到网络数据视图");
        }
    };

    m_actionHandlers["ProgManage.textEditingView"] = [this]() {
        if (m_progStacked) {
            m_progStacked->showTextEditingView();
            LOG_INFO("切换到文本编辑视图");
        }
    };

    // 程序操作动作
    m_actionHandlers["ProgManage.localOpenProg"] = [this]() {
        if (m_progStacked) {
            m_progStacked->triggerProgramSelectionInLocalView();
            m_progStacked->clearProgramSelectionIndicator();
            m_progStacked->setDefaultFocusToTaskPanel();
            LOG_INFO("执行本地程序选择操作");
        }
    };

    m_actionHandlers["ProgManage.processView"] = [this]() {
        if (m_progStacked) {
            m_progStacked->onProgramSelected();
            LOG_INFO("准备当前程序用于加工处理");
        }
    };

    m_actionHandlers["ProgManage.localAllProg"] = [this]() {
        if (m_progStacked) {
            m_progStacked->selectAllProgramsInLocalView();
            LOG_INFO("选择本地视图中的所有程序");
        }
    };

    m_actionHandlers["ProgManage.localInvertProg"] = [this]() {
        if (m_progStacked) {
            m_progStacked->selectInvertProgramsInLocalView();
            LOG_INFO("反选本地视图中的程序");
        }
    };

    m_actionHandlers["ProgManage.localCopyFile"] = [this]() {
        if (m_progStacked) {
            m_progStacked->copySelectedFilesInLocalView();
            LOG_INFO("本地视图中复制功能");
        }
    };

    m_actionHandlers["ProgManage.localCutFile"] = [this]() {
        if (m_progStacked) {
            m_progStacked->cutSelectedFilesInLocalView();
            LOG_INFO("本地视图中剪贴功能");
        }
    };

    m_actionHandlers["ProgManage.localPasteFile"] = [this]() {
        if (m_progStacked) {
            m_progStacked->pasteFilesInLocalView();
            LOG_INFO("本地视图中粘贴功能");
        }
    };

    m_actionHandlers["ProgManage.localRenameProg"] = [this]() {
        if (m_progStacked) {
            m_progStacked->deselectAllProgramsInLocalView();
            LOG_INFO("清除选择本地视图中的所有程序");
        }
    };

    m_actionHandlers["ProgManage.localSelectClear"] = [this]() {
        if (m_progStacked) {
            m_progStacked->removeCurrentTaskFromList();
            LOG_INFO("从任务列表中移除当前项");
        }
    };

    m_actionHandlers["ProgManage.localAllClear"] = [this]() {
        if (m_progStacked) {
            m_progStacked->clearAllTasksFromList();
            LOG_INFO("清空所有任务列表");
        }
    };

    m_actionHandlers["ProgManage.localPasteProg"] = [this]() {
        if (m_progStacked) {
            m_progStacked->switchFocusBetweenPanels();
            LOG_INFO("在界面面板间切换焦点");
        }
    };

    // 图形预览
    m_actionHandlers["ProgManage.graphicVertical"] = [this]() {
        if (m_graphicPreviewView) {
            m_graphicPreviewView->showVertical();
            LOG_INFO("俯视图");
        }
    };

    m_actionHandlers["ProgManage.graphicFront"] = [this]() {
        if (m_graphicPreviewView) {
            m_graphicPreviewView->showFront();
            LOG_INFO("前视图");
        }
    };

    m_actionHandlers["ProgManage.graphicRight"] = [this]() {
        if (m_graphicPreviewView) {
            m_graphicPreviewView->showRight();
            LOG_INFO("右视图");
        }
    };

    m_actionHandlers["ProgManage.graphicBottom"] = [this]() {
        if (m_graphicPreviewView) {
            m_graphicPreviewView->showBottom();
            LOG_INFO("仰视图");
        }
    };

    m_actionHandlers["ProgManage.graphicScaleUp"] = [this]() {
        if (m_graphicPreviewView) {
            m_graphicPreviewView->showScaleUp();
            LOG_INFO("放大");
        }
    };

    m_actionHandlers["ProgManage.graphicScaleDown"] = [this]() {
        if (m_graphicPreviewView) {
            m_graphicPreviewView->showScaleDown();
            LOG_INFO("缩小");
        }
    };

    // 文本编辑
    m_actionHandlers["ProgManage.textSave"] = [this]() {
        if (m_progStacked) {
            m_progStacked->saveCurrentText();
            LOG_INFO("保存文本编辑当前文本");
        }
    };

    m_actionHandlers["ProgManage.textEditingBeginSelection"] = [this]() {
        if (m_progStacked) {
            m_progStacked->textEditingBeginSelection();
            LOG_INFO("文本编辑开始选区");
        }
    };

    m_actionHandlers["ProgManage.textEditingClearSelection"] = [this]() {
        if (m_progStacked) {
            m_progStacked->textEditingClearSelection();
            LOG_INFO("文本编辑结束选区");
        }
    };

    m_actionHandlers["ProgManage.textEditingToggleSelectionSide"] = [this]() {
        if (m_progStacked) {
            m_progStacked->textEditingToggleSelectionSide();
            LOG_INFO("文本编辑切换选区首尾");
        }
    };

    m_actionHandlers["ProgManage.textEditingCopySelection"] = [this]() {
        if (m_progStacked) {
            m_progStacked->textEditingCopySelection();
            LOG_INFO("文本编辑复制选区");
        }
    };

    m_actionHandlers["ProgManage.textEditingCutSelection"] = [this]() {
        if (m_progStacked) {
            m_progStacked->textEditingCutSelection();
            LOG_INFO("文本编辑剪切选区");
        }
    };

    m_actionHandlers["ProgManage.textEditingPasteToCursor"] = [this]() {
        if (m_progStacked) {
            m_progStacked->textEditingPasteToCursor();
            LOG_INFO("文本编辑粘贴");
        }
    };

    m_actionHandlers["ProgManage.textEditingSelectAllText"] = [this]() {
        if (m_progStacked) {
            m_progStacked->textEditingSelectAllText();
            LOG_INFO("文本编辑全选文本");
        }
    };

    m_actionHandlers["ProgManage.textEditingPerformUndo"] = [this]() {
        if (m_progStacked) {
            m_progStacked->textEditingPerformUndo();
            LOG_INFO("文本编辑撤销");
        }
    };

    m_actionHandlers["ProgManage.textEditingPerformRedo"] = [this]() {
        if (m_progStacked) {
            m_progStacked->textEditingPerformRedo();
            LOG_INFO("文本编辑重做");
        }
    };

    m_actionHandlers["ProgManage.textEditingFindPrev"] = [this]() {
        if (m_progStacked) {
            m_progStacked->textEditingFindPrev();
            LOG_INFO("文本编辑查找上一个");
        }
    };

    m_actionHandlers["ProgManage.textEditingFindNext"] = [this]() {
        if (m_progStacked) {
            m_progStacked->textEditingFindNext();
            LOG_INFO("文本编辑查找下一个");
        }
    };

    LOG_INFO("动作处理器映射表初始化完成，共注册 {} 个动作处理器", m_actionHandlers.size());
}

void ProgManage::handleMenuAction(const QString& actionId, const QVariantMap& states) {
    // 记录状态信息用于调试
    QString statesStr;
    QMapIterator<QString, QVariant> i(states);
    while (i.hasNext()) {
        i.next();
        statesStr += i.key() + ":" + i.value().toString() + " ";
    }
    LOG_INFO("收到菜单动作: {} 状态参数: {}", actionId.toStdString(), statesStr.toStdString());

    // 查找并执行对应的动作处理器
    auto it = m_actionHandlers.find(actionId);
    if (it != m_actionHandlers.end()) {
        try {
            it.value()();  // 执行动作处理器
        } catch (const std::exception& e) {
            LOG_ERROR("执行动作处理器时发生异常: {} 错误信息: {}", actionId.toStdString(), e.what());
        }
    } else {
        LOG_WARN("未找到对应的动作处理器: {}", actionId.toStdString());
    }
}

QWidget* ProgManage::createWidgetForComponent(const QString& componentId) {
    LOG_INFO("程序管理模块请求创建组件: {}", componentId.toStdString());

    if (componentId == "ProgManage.localDataView") {
        // 创建程序管理主界面
        if (!m_progStacked) {
            m_progStacked = new ProgStacked(m_serviceBus);
            m_progStacked->setObjectName("m_progStacked");

            LOG_INFO("程序管理主界面组件已创建");
        }
        return m_progStacked;
    } else if (componentId == "ProgManage.paraProgView") {
        // 创建参数编程界面
        if (!m_paraProgram) {
            m_paraProgram = new ParaProgram();
            m_paraProgram->setObjectName("m_paraProgram");
            LOG_INFO("参数编程界面组件已创建");
        }
        return m_paraProgram;
    } else if (componentId == "local.localHistoricalFile") {
        // 创建历史记录对话框
        if (m_progStacked) {
            LOG_INFO("成功创建历史记录对话框");
            return m_progStacked->getLocalCreateHistoryDialog();
        } else {
            LOG_WARN("ProgStacked未创建，无法创建历史记录对话框");
        }
    } else if (componentId == "local.localDeleteFile") {
        // 创建删除对话框
        if (m_progStacked) {
            LOG_INFO("成功创建删除对话框");
            return m_progStacked->getLocalCreateDeleteDialog();
        } else {
            LOG_WARN("ProgStacked未创建，无法创建删除对话框");
        }
    } else if (componentId == "local.localNewItem") {
        // 创建新建对话框
        if (m_progStacked) {
            LOG_INFO("成功创建新建对话框");
            return m_progStacked->getLocalCreateNewItemDialog();
        } else {
            LOG_WARN("ProgStacked未创建，无法创建新建对话框");
        }
    } else if (componentId == "local.CreateRenameView") {
        // 创建重命名对话框
        if (m_progStacked) {
            LOG_INFO("成功创建重命名对话框");
            return m_progStacked->getLocalCreateRenameDialog();
        } else {
            LOG_WARN("ProgStacked未创建，无法创建重命名对话框");
        }
    } else if (componentId == "ProgManage.GraphicPreview") {
        // 创建图形预览
        if (!m_graphicPreviewView) {
            m_graphicPreviewView = new GraphicPreview();
            m_graphicPreviewView->setObjectName("graphicPreviewView");
        }
        return m_graphicPreviewView;
    }
    LOG_WARN("未知的组件ID: {}", componentId.toStdString());
    return nullptr;
}

QString ProgManage::loadModuleStyleSheet() {
    // 返回本模块的样式表
    return QString();
}

void ProgManage::onGlobalEvent(const QString& eventType, const QVariantMap& params) {
    if (!m_serviceBus) {
        LOG_WARN("onGlobalEvent 调用时服务总线为空，忽略事件：{}", eventType.toStdString());
        return;
    }
#if 0
    if (eventType == G_INTER_MODULE_REQUEST) {
        QString requestType = params.value(KEY_REQUEST_TYPE).toString();
        if (requestType == REQ_TYPE_EDIT_NC_FILE) {
            QVariantMap payload = params.value(KEY_PAYLOAD_DATA).toMap();
            QString filePath = payload.value("filePath").toString();
            if (filePath.isEmpty()) {
                LOG_WARN("REQ_TYPE_EDIT_NC_FILE 请求中的 filePath 为空。");
                return;
            }

            // 请求导航到该分类下
            QVariantMap navParams;
            navParams[KEY_TARGET_CATEGORY] = "程序管理";
            //// BUG172
            //navParams[KEY_TARGET_SECTION] = "本地数据";
            m_serviceBus->publishEvent(G_SYSTEM_NAVIGATE_TO_CONTEXT, navParams);
            if (m_serviceBus != nullptr) {
                m_serviceBus->navigateToAction("ProgManage.localDataView");
            }
        }
    }
#endif
}

void ProgManage::updateEvent() {
    QString id = "ProgManage.usbDiskDataView";
    if (m_progStacked) {
        if (m_progStacked->updateUsbInterface()) {
            // if (m_menuStatus) {
            m_serviceBus->updateMenuActionEnabled(id, true);
            // m_serviceBus->updateMenuActionChecked(id, true);
            m_menuStatus = true;
            // }
        } else {
            // if (!m_menuStatus) {
            m_serviceBus->updateMenuActionEnabled(id, false);
            // m_serviceBus->updateMenuActionChecked(id, false);
            m_menuStatus = false;
            //}
        }
    }
}
