#include "DiagMaint.h"

#include <QApplication>
#include <QFile>
#include <QFileSystemModel>
#include <QJsonDocument>
#include <QLabel>
#include <QTextStream>
#include <QTreeView>
#include <QVBoxLayout>
#include <QWidget>
#include <iostream>
#include <string>

#include "StyledSystemSettingDialog.h"
#include "core/AppLogger.h"
#include "core/CncManager.h"

DiagMaint::DiagMaint(QObject* parent) : IModule() { LOG_INFO("DiagMaint实例已创建"); }

DiagMaint::~DiagMaint() { LOG_INFO("DiagMaint实例已销毁"); }

QString DiagMaint::moduleId() const { return "DiagMaint"; }

QString DiagMaint::moduleDisplayName() const { return tr("诊断/维护"); }

bool DiagMaint::initialize(IServiceBus* serviceBus) {
    LOG_INFO("初始化DiagMaint...");
    m_serviceBus = serviceBus;

    QFile file(":/diagmaint/menu.json");
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        LOG_WARN("无法打开菜单描述文件: {}", file.fileName().toStdString());
        return false;
    }

    QByteArray jsonData = file.readAll();
    file.close();

    QJsonDocument doc = QJsonDocument::fromJson(jsonData);
    if (doc.isNull() || !doc.isObject()) {
        LOG_ERROR("解析菜单JSON失败");
        return false;
    }
    m_menuCache = doc.object();

    std::vector<ConfigCategory> rootCategories;
    CncManager::getInstance()->getConfiguration(rootCategories);
    for (auto index = 0; index < rootCategories.size(); index++) {
        const auto& category = rootCategories[index];
        BaseDataSource* dataSource = new BaseDataSource(QString("No.%1-").arg(index + 1),
                                                        QString::fromStdString(category.categoryName), category);
        m_dataSourceList.append(dataSource);
    }

    m_diDataSource = new DIDataSource(nullptr);
    m_diDataSource->initialize(QString("No.%1-").arg(rootCategories.size() + 1), "PLC DI参数");
    m_doDataSource = new DODataSource(nullptr);
    m_doDataSource->initialize(QString("No.%1-").arg(rootCategories.size() + 2), "PLC DO参数");

    //获取轴的名称，并创建对应的ScrewPitchTableDataSource

    SystemConfig systemConfig = CncManager::getInstance()->getSystemConfig();
    int lastRowIndex = 1;
    for (auto index = 0; index < systemConfig.totalNumberOfAxes; index++) {
        const auto& axisName = systemConfig.axesConfigs[index].name;
        CncManager::getInstance()->enablePitchCompensation(0, index, true, true);
        ScrewPitchTableDataSource* dataSource = new ScrewPitchTableDataSource(nullptr, 0, index, lastRowIndex);
        dataSource->initialize(QString("No.%1-").arg(rootCategories.size() + 3),
                               QString::fromStdString(axisName) + tr("轴"));
        lastRowIndex += dataSource->rowCount();
        m_screwPitchTableDataSources.append(dataSource);
    }

#if 0  //主轴不支持螺距补偿
    for (auto index = 0; index < systemConfig.totalNumberOfSpindles; index++) {
        const auto& axisName = systemConfig.spindlesConfigs[index].name;
        int axisIndex = systemConfig.totalNumberOfAxes + index;
        CncManager::getInstance()->enablePitchCompensation(0, index, true, true);
        ScrewPitchTableDataSource* dataSource = new ScrewPitchTableDataSource(nullptr, 0, index, lastRowIndex);
        dataSource->initialize(QString("No.%1-").arg(rootCategories.size() + 3),
                               QString::fromStdString(axisName) + tr("轴"));
        lastRowIndex += dataSource->rowCount();
        m_screwPitchTableDataSources.append(dataSource);
    }
#endif

    return true;
}

void DiagMaint::shutdown() {
    LOG_INFO("关闭DiagMaint...");
    m_serviceBus = nullptr;
    m_menuCache = QJsonObject();
    for (auto dataSource : m_dataSourceList) {
        delete dataSource;
    }
    m_dataSourceList.clear();

    delete m_diDataSource;
    m_diDataSource = nullptr;
    delete m_doDataSource;
    m_doDataSource = nullptr;

    for (auto dataSource : m_screwPitchTableDataSources) {
        delete dataSource;
    }
    m_screwPitchTableDataSources.clear();
}

QJsonObject DiagMaint::menuDescriptor() const { return m_menuCache; }

void DiagMaint::handleMenuAction(const QString& actionId, const QVariantMap& states) {
    QString statesStr;
    QMapIterator<QString, QVariant> i(states);
    while (i.hasNext()) {
        i.next();
        statesStr += i.key() + ":" + i.value().toString() + " ";
    }
    LOG_INFO("收到动作: {} states: {}", actionId.toStdString(), statesStr.toStdString());
    // TODO: 实现具体的动作处理逻辑
    if (actionId == "DiagMaint.showLadderView") {
        // 显示梯形图界面
        m_signalListView->showLadderView();
    } else if (actionId == "DiagMaint.ladderViewFindPrev") {
        // 梯形图界面查找上一个
        m_signalListView->findPrevInLadderView();
    } else if (actionId == "DiagMaint.ladderViewFindNext") {
        // 梯形图界面查找下一个
        m_signalListView->findNextInLadderView();
    } else if (actionId == "DiagMaint.parameterManagerView") {
        StyledSystemSettingDialog::show(m_serviceBus, this);
    } else if (actionId == "DiagMaint.alarmNowView.details") {
        // 底部一半显示报警详情
        LOG_DEBUG("进入当前报警显示报警详情");
        if (m_alarmManager) {
            LOG_DEBUG("开始显示报警详情");
            m_alarmManager->showCurrentAlarmBottomHalf();
            LOG_DEBUG("显示报警详情成功");
        } else {
            LOG_WARN("m_alarmManager showCurrentAlarmBottomHalf");
        }
    } else if (actionId == "DiagMaint.alarmNowView.fullScreen") {
        // 全屏显示报警详情
        LOG_DEBUG("当前报警全屏显示报警详情");
        if (m_alarmManager) {
            m_alarmManager->showCurrentAlarmFullScreen();
        } else {
            LOG_WARN("m_alarmManager showCurrentAlarmFullScreen");
        }
    } else if (actionId == "DiagMaint.alarmNowView.quit") {
        // 退出详情模式，全屏显示报警列表
        LOG_DEBUG("当前报警退出详情模式，全屏显示报警列表");
        if (m_alarmManager) {
            m_alarmManager->exitCurrentAlarmDetail();
        } else {
            LOG_WARN("m_alarmManager exitCurrentAlarmDetail");
        }
    } else if (actionId == "DiagMaint.alarmHistoryView.details") {
        // 底部一半显示报警详情
        LOG_DEBUG("进入历史报警显示报警详情");
        if (m_alarmManager) {
            LOG_DEBUG("开始显示报警详情");
            m_alarmManager->showHistoryAlarmBottomHalf();
            LOG_DEBUG("显示报警详情成功");
        } else {
            LOG_WARN("m_alarmManager showCurrentAlarmBottomHalf");
        }
    } else if (actionId == "DiagMaint.alarmHistoryView.fullScreen") {
        // 全屏显示报警详情
        LOG_DEBUG("历史报警全屏显示报警详情");
        if (m_alarmManager) {
            m_alarmManager->showHistoryAlarmFullScreen();
        } else {
            LOG_WARN("m_alarmManager showCurrentAlarmFullScreen");
        }
    } else if (actionId == "DiagMaint.alarmHistoryView.quit") {
        // 退出详情模式，全屏显示报警列表
        LOG_DEBUG("历史报警退出详情模式，全屏显示报警列表");
        if (m_alarmManager) {
            m_alarmManager->exitHistoryAlarmDetail();
        } else {
            LOG_WARN("m_alarmManager未初始化，无法调用 exitCurrentAlarmDetail");
        }
        // 操作日志测试
    } else if (actionId == "DiagMaint.allLog.quit") {
        LOG_DEBUG("全部日志");
        if (m_operationLogManager) {
            m_operationLogManager->showAllLogs();
        } else {
            LOG_WARN("m_operationLogManager未初始化，无法调用 exitCurrentAlarmDetail");
        }
    } else if (actionId == "DiagMaint.debugLog.quit") {
        LOG_DEBUG("调试日志");
        if (m_operationLogManager) {
            m_operationLogManager->showDebugLogs();
        } else {
            LOG_WARN("m_operationLogManager未初始化，无法调用 exitCurrentAlarmDetail");
        }
    } else if (actionId == "DiagMaint.uiLog.quit") {
        LOG_DEBUG("UI日志");
        if (m_operationLogManager) {
            m_operationLogManager->showUiLogs();
        } else {
            LOG_WARN("m_operationLogManager未初始化，无法调用 exitCurrentAlarmDetail");
        }
    } else if (actionId == "DiagMaint.userLog.quit") {
        LOG_DEBUG("用户日志");
        if (m_operationLogManager) {
            m_operationLogManager->showUserLogs();
        } else {
            LOG_WARN("m_operationLogManager未初始化，无法调用 exitCurrentAlarmDetail");
        }
    } else if (actionId == "DiagMaint.hardwareLog.quit") {
        LOG_DEBUG("hardware日志");
        if (m_operationLogManager) {
            m_operationLogManager->showHardwareLogs();
        } else {
            LOG_WARN("m_operationLogManager未初始化，无法调用 exitCurrentAlarmDetail");
        }
    }
}

QWidget* DiagMaint::createWidgetForComponent(const QString& componentId) {
    LOG_INFO("DiagMaint 请求创建组件: {}", componentId.toStdString());

    // 检查 CncManager 状态，但暂时不获取报警（组件自己会处理）
    auto cncManager = CncManager::getInstance();
    if (!cncManager || !cncManager->isInitialized()) {
        LOG_WARN("CncManager 未初始化");
    }

    if (componentId == "DiagMaint.alarmNowView") {
        // 获取报警系统单例
        if (!m_alarmManager) {
            m_alarmManager = &AlarmManager::getInstance(m_serviceBus);
            m_alarmManager->setObjectName("m_alarmManager");
            LOG_INFO("报警系统已初始化");
        }

        return m_alarmManager->getCurrentAlarmUI();

    } else if (componentId == "DiagMaint.alarmHistoryView") {
        // 获取报警系统单例
        if (!m_alarmManager) {
            m_alarmManager = &AlarmManager::getInstance(m_serviceBus);
            m_alarmManager->setObjectName("m_alarmManager");
            LOG_INFO("报警系统界面已初始化");
        }

        return m_alarmManager->getHistoryAlarmUI();
    }
    if (componentId == "DiagMaint.operateLogView") {
        // 操作日志界面
        if (!m_operationLogManager) {
            m_operationLogManager = &OperationLogManager::getInstance(m_serviceBus);
            m_operationLogManager->setObjectName("m_operationLogManager");
            LOG_INFO("操作日志界面已初始化");
        }

        return m_operationLogManager->getOperationLogUI();
    } else if (componentId == "DiagMaint.SignalListView") {
        // 显示PLC,DI,DO等界面
        if (!m_signalListView) {
            m_signalListView = new SignalListView();
            m_signalListView->setObjectName("signalListView");
        }
        return m_signalListView;
    } else if (componentId == "DiagMaint.deviceView") {
        // 设备部件界面
        if (!m_devicePart) {
            m_devicePart = new DevicePart();
            m_devicePart->setObjectName("DiagMaint.deviceView");
        }
        return m_devicePart;
    } else if (componentId == "DiagMaint.debugView") {
        // 调试界面
        if (!m_debugGer) {
            m_debugGer = new DebugGer();
            m_debugGer->setObjectName("debugGer");
        }
        return m_debugGer;
    } else if (componentId == "DiagMaint.coordinateAndmanual") {
#if 0
        // 坐标系与手动界面
        if (!m_coordinateAndManual) {
            m_coordinateAndManual = new CoordinateAndManual(m_serviceBus);
            m_coordinateAndManual->setObjectName("coordinateAndManual");
        }
        return m_coordinateAndManual;
#endif
    }

    return nullptr;
}

QString DiagMaint::loadModuleStyleSheet() {
    LOG_INFO("DiagMaint模块被请求提供样式...");

    // 1. 尝试从资源文件加载样式
    QFile resourceStyleFile(":/diagmaint/styles/diagmaint.qss");
    QString styleContent;

    if (resourceStyleFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QTextStream stream(&resourceStyleFile);
        styleContent = stream.readAll();
        resourceStyleFile.close();
        LOG_INFO("从资源文件加载DiagMaint样式成功，长度: {} 字符", styleContent.length());
        return styleContent;
    }

    // 2. 如果资源文件不可用，尝试从文件系统加载
    QString execPath = QApplication::applicationDirPath();
    QString moduleStylePath = execPath + "/modules/DiagMaint/styles/diagmaint.qss";

    QFile fileSystemStyleFile(moduleStylePath);
    if (fileSystemStyleFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QTextStream stream(&fileSystemStyleFile);
        styleContent = stream.readAll();
        fileSystemStyleFile.close();
        LOG_INFO("从文件系统加载DiagMaint样式成功: {}，长度: {} 字符", moduleStylePath.toStdString(),
                 styleContent.length());
        return styleContent;
    }

    // 3. 两种方式都失败了
    LOG_INFO("DiagMaint模块没有找到样式文件，跳过样式加载");

    return QString();  // 返回空字符串表示没有样式
}

QList<BaseDataSource*> DiagMaint::getDataSourceList() { return m_dataSourceList; }

DIDataSource* DiagMaint::getDIDataSource() { return m_diDataSource; }

DODataSource* DiagMaint::getDODataSource() { return m_doDataSource; }

QList<ScrewPitchTableDataSource*> DiagMaint::getScrewPitchTableDataSources() { return m_screwPitchTableDataSources; }