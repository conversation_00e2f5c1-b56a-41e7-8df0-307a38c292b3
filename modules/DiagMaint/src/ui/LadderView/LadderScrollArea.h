#pragma once

#include <QFontMetrics>
#include <QHash>
#include <QKeyEvent>
#include <QMap>
#include <QMouseEvent>
#include <QPainter>
#include <QPen>
#include <QRect>
#include <QScrollArea>
#include <QScrollBar>
#include <QVector>
#include <QWheelEvent>

#include "CncManager.h"
#include "CommandListParser.h"
#include "LDGridParser.h"
#include "LadderDrawer.h"
#include "PlcManager.h"

class LadderScrollArea : public QScrollArea {
    Q_OBJECT
   public:
    explicit LadderScrollArea(QWidget *parent = nullptr);
    ~LadderScrollArea();

   public:
    // 设置总列数
    void setColCount(const int colCount);

    // 设置总行数
    void setRowCount(const int rowCount);

    // 获取总列数
    int colCount() const;

    // 获取总行数
    int rowCount() const;

    // 设置列宽数组
    void setColWidths(const QVector<int> &colWidths);

    // 设置行高
    void setRowHeight(const int rowHeight);

    // 跳转到上一个目标（变量或命令名），返回是否找到目标
    bool jumpToPrev(const QString &target);

    // 跳转到下一个目标（变量或命令名），返回是否找到目标
    bool jumpToNext(const QString &target);

    // 从 ladder.LD 文件读取网格数据
    void loadFromLadderLD(const QString &path, const char *encoding);

    // 从 commandlist 文件读取网格数据
    void loadFromCommandList(const QString &path);

   signals:
    void cellDataLoaded(int rowCount);

   private:
    // 跳转到指定单元格
    void jumpToCell(const QPoint cellPos);

    // 加载变量描述
    void loadVariableDesc(PlcManager *plcManager, CncManager *cncManager);

    // 更新行变量
    void updateVisibleRowVariables();

    // 从空间本地坐标获取对应的单元格坐标
    QPoint getGridCell(const QPointF &mousePoint);

    // 滚动指定的行数（不触发重绘）
    void scrollByLine(const int delta);

   protected:
    void resizeEvent(QResizeEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void mouseDoubleClickEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;

   private:
    // 将PLC变量值转换为字符串
    QString stringifyPlcValue(const PlcValue &value, const bool hex, const int precision);

    // 更新指定PLC变量缓存
    void updatePlcValue(QString variable, PlcVariableType varType, int address, const PlcValue &newValue);

    // 获取指定PLC变量类型的前缀字符
    char getPlcPrefixChar(const PlcVariableType type);

    // 获取指定PLC变量前缀的类型
    PlcVariableType getPlcVariableType(const char prefix);

    // 获取给定变量的值，在绘制变量值时由绘制者调用（可能更新缓存的变量值）
    QString getVariableDisplayValue(QString &variable);

    // 绘制背景
    void drawBackground(QPainter &painter) const;

    // 绘制母线
    void drawRails(QPainter &painter) const;

    // 绘制指定单元格选框
    void drawCellSelection(QPainter &painter, const int cellX, const int cellY) const;

    // 绘制指定单元格垂直线
    void drawCellVertLine(QPainter &painter, const int cellX, const int cellY) const;

    // 绘制指定单元格内容和垂直线（可能更新缓存的变量值）
    void drawCellContent(QPainter &painter, const int cellX, const int cellY);

   protected:
    void paintEvent(QPaintEvent *event) override;

    void focusInEvent(QFocusEvent *event) override;

    void focusOutEvent(QFocusEvent *event) override;

   protected slots:
    void onScrollBarMoved(int value);

    void onPlcVariableChanged(int address, PlcVariableType varType, const PlcValue &newValue);

   private:
    int m_totalWidth;           // 总宽度
    QVector<int> m_colWidths;   // 存储每列宽度的数组
    QVector<int> m_colLeftPos;  // 存储每列的起始位置
    int m_rowHeight;            // 行高
    int m_rowCount;             // 行数
    int m_colCount;             // 列数
    int m_startRow;             // 起始行（当前显示范围内首行行号）
    int m_maxStartRow;          // 最大起始行（滚动条拉到底部时的起始行）
    int m_visibleRowCount;      // 滚动区域中可见的行数

    QVector<LadderCellData> m_cellData;                      // 梯形图网格数据
    QVector<QString> m_programNums;                          // 梯形图左侧程序号
    QVector<QString> m_programNote;                          // 梯形图右侧行注释
    QHash<QString, QString> m_varDesc;                       // 变量注释/描述
    QHash<QString, QString> m_varValue;                      // 变量显示值
    MacroVariableRange m_macroRange;                         // 宏变量索引范围
    QMap<QPair<PlcVariableType, int>, QString> m_plcVarIds;  // PLC变量名表

    bool m_mouseIsPressed;    // 当前鼠标是否按下
    QPoint m_mousePressCell;  // 鼠标按下时点击的单元格坐标
    QPoint m_mouseHoverCell;  // 鼠标拖拽时悬停的单元格坐标

    QFont *m_ladderFont;       // 用于绘制梯形图文本
    QFont *m_coilFont;         // 用于绘制线圈
    QPen *m_rowNumPen;         // 用于绘制行号
    QPen *m_programNumPen;     // 用于绘制程序号+行号
    QPen *m_rungPen;           // 用于绘制母线、水平线和元件
    QPen *m_notePen;           // 用于绘制元件注释
    QPen *m_valuePen;          // 用于绘制变量值
    QPen *m_selectionPen;      // 用于绘制选中框
    QBrush *m_selectionBrush;  // 用于绘制选中框
    QPen *m_unfocusedPen;      // 用于绘制非焦点选中框
    QBrush *m_unfocusedBrush;  // 用于绘制非焦点选中框
    QBrush *m_conditionBrush;  // 用于绘制条件框
};
