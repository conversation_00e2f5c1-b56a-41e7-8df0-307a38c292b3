#pragma once

#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QVBoxLayout>

#include "LadderScrollArea.h"
#include "TextEncrypt.h"

class LadderWidget : public QWidget {
    Q_OBJECT
   public:
    explicit LadderWidget(QWidget *parent = nullptr);

    void findPrev();  // 查找上一个
    void findNext();  // 查找下一个

   private:
    void setupUi();       // 用于设置界面布局和控件
    void setupSignals();  // 用于设置槽函数

    QVBoxLayout *m_outerVLayout = nullptr;  // 整个梯形图的Layout，包括滚动区域和下方搜索面板
    QHBoxLayout *m_scrollLayout = nullptr;  // 滚动区域的Layout
    QHBoxLayout *m_searchLayout = nullptr;  // 搜索区域的Layout

    LadderScrollArea *m_scroll = nullptr;  // 滚动视图
    QLabel *m_rowCountLabel = nullptr;     // 总行数标签
    QLabel *m_modeLabel = nullptr;         // 模式标签
    QLabel *m_hintLabel = nullptr;         // 提示标签
    QLineEdit *m_searchBox = nullptr;      // 搜索框

   protected:
    void paintEvent(QPaintEvent *event) override;
};