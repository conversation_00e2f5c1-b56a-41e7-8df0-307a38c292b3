#pragma once

#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QWidget>

#include "FocusablePanel.h"
#include "LadderWidget.h"

class NewLadderView : public FocusablePanel {
    Q_OBJECT

   public:
    explicit NewLadderView(QWidget* parent = nullptr);
    ~NewLadderView();

    void findPrevInWidget();

    void findNextInWidget();

   private slots:
    // 可以添加槽函数来响应用户交互或更新状态

   protected:
    void handleMenuAction(const QString& actionId, const QVariantMap& states) override;

   private:
    void setupUi();  // 用于设置界面布局和控件

    // 主布局
    QVBoxLayout* m_mainLayout = nullptr;

    // 工作区
    LadderWidget* m_ladderWidget = nullptr;
};
