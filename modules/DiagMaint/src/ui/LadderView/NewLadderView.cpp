#include "NewLadderView.h"

#include "core/AppLogger.h"

NewLadderView::NewLadderView(QWidget* parent) : FocusablePanel(parent) {
    setObjectName("NewLadderView");
    setupUi();
}

NewLadderView::~NewLadderView() {
    // Qt 会自动处理子控件的删除
}

void NewLadderView::findPrevInWidget() { m_ladderWidget->findPrev(); }

void NewLadderView::findNextInWidget() { m_ladderWidget->findNext(); }

void NewLadderView::setupUi() {
    m_mainLayout = new QVBoxLayout(contentArea());
    m_mainLayout->setContentsMargins(0, 0, 0, 0);  // 去除边距
    m_mainLayout->setSpacing(0);                   // 去除间距

    // 工作区
    m_ladderWidget = new LadderWidget(this);
    m_ladderWidget->setObjectName("newLadderWidget");

    m_mainLayout->addWidget(m_ladderWidget);

    contentArea()->setLayout(m_mainLayout);  // 设置主布局
    // 不需要标题栏，标题栏隐藏
    titleBarArea()->setVisible(false);
}

void NewLadderView::handleMenuAction(const QString& actionId, const QVariantMap& states) {
    LOG_DEBUG("handleMenuAction::handleMenuAction: actionId={}", actionId.toStdString());
    // 这里可以根据需要添加具体的处理逻辑
    Q_UNUSED(states)
}