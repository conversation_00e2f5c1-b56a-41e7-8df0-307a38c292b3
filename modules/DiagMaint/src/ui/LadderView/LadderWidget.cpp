#include "LadderWidget.h"

#include <QCoreApplication>
#include <QDir>

#include "FocusManager.h"
#include "TextEncrypt.h"

LadderWidget::LadderWidget(QWidget *parent) : QWidget(parent) {
    setupUi();
    setupSignals();

    // 确定配置路径
    QDir baseDir(QCoreApplication::applicationDirPath());
    QString configPath = baseDir.absolutePath();

    // 从 commandlist 读取数据
    // const QString path1 = QString("%1/PLC/commandlist").arg(configPath);
    // m_scroll->loadFromCommandList(path1);

    // 从 ladder.LD 读取数据
    TextEncrypt te;
    const QString path3 = QString("%1/PLC/ladder.LD").arg(configPath);
    const QString path2 = QString("%1/PLC/ladder.LD.txt").arg(configPath);
    const char *encoding = "GBK";  // 明文使用 GBK 编码
    if (te.Decrypt(path3, path2)) {
        m_scroll->loadFromLadderLD(path2, encoding);

        // 读取完成后删除明文文件
        QFile::remove(path2);
    } else {
        LOG_ERROR("LD文件解密失败");
    }
}

void LadderWidget::setupUi() {
    // 设置整体的Layout
    m_outerVLayout = new QVBoxLayout(this);
    setLayout(m_outerVLayout);
    m_outerVLayout->setContentsMargins(0, 0, 0, 0);  // 去除边距
    m_outerVLayout->setSpacing(0);                   // 去除间距

    // 设置滚动区域的Layout
    m_scrollLayout = new QHBoxLayout(this);
    m_scrollLayout->setContentsMargins(0, 0, 0, 0);  // 去除边距
    m_scrollLayout->setSpacing(0);                   // 去除间距
    m_scroll = new LadderScrollArea(this);
    m_scrollLayout->addWidget(m_scroll);

    // 设置搜索区域的Layout
    m_searchLayout = new QHBoxLayout(this);
    m_searchLayout->setContentsMargins(6, 3, 25, 3);  // 设置边距
    m_searchLayout->setSpacing(6);                    // 设置间距
    m_rowCountLabel = new QLabel("LNUM ???", this);
    m_searchLayout->addWidget(m_rowCountLabel);
    m_modeLabel = new QLabel("【运行模式】", this);
    m_searchLayout->addWidget(m_modeLabel);
    m_searchLayout->addStretch();
    m_hintLabel = new QLabel(tr("查找"), this);
    m_searchLayout->addWidget(m_hintLabel);
    m_searchBox = new QLineEdit();
    m_searchBox->setObjectName("searchBox");
    m_searchBox->setMinimumSize(QSize(120, 30));
    m_searchBox->setMaximumSize(QSize(120, 30));
    m_searchLayout->addWidget(m_searchBox);

    m_outerVLayout->addLayout(m_scrollLayout);
    m_outerVLayout->addLayout(m_searchLayout);

    // 注册到焦点管理器
    auto focusManager = FocusManager::instance();
    focusManager->registerWidget(m_scroll);
    focusManager->registerWidget(m_searchBox);
}

void LadderWidget::setupSignals() {
    // 更新总行数标签文本
    connect(m_scroll, &LadderScrollArea::cellDataLoaded, [this](int rowCount) {
        QString labelText = QString("LNUM %1").arg(rowCount);

        m_rowCountLabel->setText(labelText);
    });
}

// 查找上一个
void LadderWidget::findPrev() {
    if (m_searchBox->text().isEmpty()) {
        m_hintLabel->setStyleSheet("QLabel { color : red; }");
        m_hintLabel->setText(tr("查找内容为空"));

        return;
    }

    bool found = m_scroll->jumpToPrev(m_searchBox->text());

    if (found) {
        m_scroll->setFocus();
        m_hintLabel->setStyleSheet("QLabel { color : black; }");
        m_hintLabel->setText(tr("查找"));
    } else {
        m_hintLabel->setStyleSheet("QLabel { color : red; }");
        m_hintLabel->setText(tr("找不到上一个"));
    }
}

// 查找下一个
void LadderWidget::findNext() {
    if (m_searchBox->text().isEmpty()) {
        m_hintLabel->setStyleSheet("QLabel { color : red; }");
        m_hintLabel->setText(tr("查找内容为空"));

        return;
    }

    bool found = m_scroll->jumpToNext(m_searchBox->text());

    if (found) {
        m_scroll->setFocus();
        m_hintLabel->setStyleSheet("QLabel { color : black; }");
        m_hintLabel->setText(tr("查找"));
    } else {
        m_hintLabel->setStyleSheet("QLabel { color : red; }");
        m_hintLabel->setText(tr("找不到下一个"));
    }
}

void LadderWidget::paintEvent(QPaintEvent *event) {
    Q_UNUSED(event)
    QPainter painter(this);

    // 绘制搜索区域背景
    painter.fillRect(rect(), QColor("#7A8899"));
}
