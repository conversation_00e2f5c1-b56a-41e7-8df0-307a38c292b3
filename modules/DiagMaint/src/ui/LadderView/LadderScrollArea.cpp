#include "LadderScrollArea.h"

LadderScrollArea::LadderScrollArea(QWidget *parent) : QScrollArea(parent), m_rowHeight(70) {
    setObjectName("LadderScrollArea");
    setFocusPolicy(Qt::ClickFocus);
    setStyleSheet("border: none;");

    // 重置选中的单元格
    m_mousePressCell = QPoint(-1, -1);

    // 初始化至第一行
    m_startRow = 0;

    // 获取 PlcManager 实例
    PlcManager *plcManager = PlcManager::getInstance();

    // 获取 CncManager 实例
    CncManager *cncManager = CncManager::getInstance();

    // 读取变量描述
    loadVariableDesc(plcManager, cncManager);

    // 设置槽函数
    connect(verticalScrollBar(), &QScrollBar::sliderMoved, this, &LadderScrollArea::onScrollBarMoved);
    connect(plcManager, &PlcManager::plcVariableChanged, this, &LadderScrollArea::onPlcVariableChanged);

    // 创建绘图资源
    m_ladderFont = new QFont();
    m_ladderFont->setPixelSize(12);
    m_coilFont = new QFont();
    m_coilFont->setPixelSize(15);
    m_rowNumPen = new QPen(QColor(0, 168, 0));
    m_programNumPen = new QPen(QColor(0, 0, 255));
    m_rungPen = new QPen(QColor(0, 0, 0));
    m_notePen = new QPen(QColor(0, 0, 88));
    m_valuePen = new QPen(QColor(173, 0, 173));
    m_selectionPen = new QPen(QColor(255, 1, 1));
    m_selectionBrush = new QBrush(QColor(255, 136, 122));
    m_unfocusedPen = new QPen(QColor(102, 102, 255));
    m_unfocusedBrush = new QBrush(QColor(168, 168, 180));
    m_conditionBrush = new QBrush(QColor(255, 255, 255, 0));

    // 自己处理方向按键
    setProperty("state", "editing");
}

LadderScrollArea::~LadderScrollArea() {
    // Qt 会自动处理子控件的删除

    // 释放绘图资源
    delete m_ladderFont;
    delete m_coilFont;
    delete m_rowNumPen;
    delete m_programNumPen;
    delete m_rungPen;
    delete m_notePen;
    delete m_valuePen;
    delete m_selectionPen;
    delete m_selectionBrush;
    delete m_unfocusedPen;
    delete m_unfocusedBrush;
    delete m_conditionBrush;

    // 清空容器
    m_colWidths.clear();
    m_colLeftPos.clear();
    m_cellData.clear();
    m_programNums.clear();
    m_programNote.clear();
    m_varDesc.clear();
    m_plcVarIds.clear();
}

void LadderScrollArea::setColCount(const int colCount) { m_colCount = colCount; }

void LadderScrollArea::setRowCount(const int rowCount) {
    m_rowCount = rowCount;

    // 总行数改变后，更新可见行变量
    updateVisibleRowVariables();
}

int LadderScrollArea::colCount() const { return m_colCount; }

int LadderScrollArea::rowCount() const { return m_rowCount; }

void LadderScrollArea::setColWidths(const QVector<int> &colWidths) {
    if (colWidths.size() != m_colCount) {  // 检查列数是否一致
        LOG_ERROR("列数与列宽个数不一致！列数：{} 列宽个数：{}", m_colCount, colWidths.size());
        return;
    }

    m_colWidths.clear();
    m_colLeftPos.clear();
    m_colWidths = colWidths;
    m_totalWidth = 0;

    for (int width : m_colWidths) {
        m_colLeftPos.append(m_totalWidth);
        m_totalWidth += width;
    }

    update();  // 触发重绘
}

void LadderScrollArea::setRowHeight(const int rowHeight) {
    m_rowHeight = rowHeight;

    // 行高改变后，更新可见行变量
    updateVisibleRowVariables();
}

bool LadderScrollArea::jumpToPrev(const QString &target) {
    // 内容网格列数去掉左右各一列
    const int contentColCount = m_colCount - 2;
    int curIndex = m_mouseHoverCell.y() * contentColCount + m_mouseHoverCell.x() - 1;
    bool found = false;

    curIndex--;
    while (curIndex >= 0) {
        if (m_cellData[curIndex].containsTarget(target)) {
            found = true;
            break;
        }
        curIndex--;
    }

    if (found) {
        LOG_INFO("找到目标：{}", curIndex);
        jumpToCell(QPoint(curIndex % contentColCount + 1, curIndex / contentColCount));
    } else {
        LOG_INFO("未找到目标");
    }

    return found;
}

bool LadderScrollArea::jumpToNext(const QString &target) {
    // 内容网格列数去掉左右各一列
    const int contentColCount = m_colCount - 2;
    int curIndex = m_mouseHoverCell.y() * contentColCount + m_mouseHoverCell.x() - 1;
    bool found = false;

    curIndex++;
    while (curIndex < m_cellData.size()) {
        if (m_cellData[curIndex].containsTarget(target)) {
            found = true;
            break;
        }
        curIndex++;
    }

    if (found) {
        LOG_INFO("找到目标：{}", curIndex);
        jumpToCell(QPoint(curIndex % contentColCount + 1, curIndex / contentColCount));
    } else {
        LOG_INFO("未找到目标");
    }

    return found;
}

void LadderScrollArea::loadFromLadderLD(const QString &path, const char *encoding) {
    auto programList = LDGridParser::parseSymbolList(path, encoding);

    QVector<QPair<int, QString>> programNumsList;
    QVector<QPair<int, QString>> programNoteList;

    int totalRowCount = 0;
    int programStartRow = 0;

    // 设置列布局
    setColCount(9);
    setColWidths({80, 70, 70, 70, 70, 70, 70, 300, 113});
    // 内容网格列数去掉左右各一列
    const int contentColCount = m_colCount - 2;

    // 先计算出显示所有程序所需网格的总大小
    for (auto &program : programList) {
        int progRowCount = program.m_rowCount;

        // 添加每个程序起始行的程序号
        if (totalRowCount > 0) {
            programNumsList.append(qMakePair(totalRowCount, program.m_name));
        }

        // 添加每个程序中的行注释
        for (auto it = program.m_notes.keyValueBegin(); it != program.m_notes.keyValueEnd(); ++it) {
            programNoteList.append(qMakePair(totalRowCount + it->first, it->second));
        }

        totalRowCount += progRowCount;
    }

    // 把程序号填入数组
    m_programNums.clear();
    m_programNums.resize(totalRowCount);
    for (auto &pair : programNumsList) {
        m_programNums[pair.first] = pair.second;
    }
    // 把行注释填入数组
    m_programNote.clear();
    m_programNote.resize(totalRowCount);
    for (auto &pair : programNoteList) {
        m_programNote[pair.first] = pair.second;
    }

    LOG_INFO("程序总行数：{}", totalRowCount);

    // 设置行布局
    setRowCount(totalRowCount);
    setRowHeight(70);

    m_cellData.clear();
    m_cellData.resize(contentColCount * m_rowCount);

    int curRow = 0;

    for (auto &program : programList) {
        int progRowCount = program.m_rowCount;

        for (auto &symbol : program.m_symbols) {
            if (symbol.m_x < contentColCount) {
                const auto index = (symbol.m_y + programStartRow) * contentColCount + symbol.m_x;

                if (m_cellData[index].m_cellType == LadderCellType::Dummy) {
                    continue;
                }

                m_cellData[index] = symbol.m_cellData;
                if (symbol.m_cellData.m_cellType == LadderCellType::Condition && symbol.m_x < contentColCount - 1) {
                    // 把右边一格设为 Dummy 类型
                    m_cellData[index + 1] = LadderCellData(LadderCellType::Dummy, QString());
                }
            }
        }

        for (auto &vertLine : program.m_vertLines) {
            if (vertLine.x() < contentColCount) {
                const auto index = (vertLine.y() + programStartRow) * contentColCount + vertLine.x();

                m_cellData[index].m_leftVertLine = true;
            }
        }

        programStartRow += progRowCount;
    }

    LOG_DEBUG("网格数据填充完毕");

    emit cellDataLoaded(totalRowCount);
}

void LadderScrollArea::loadFromCommandList(const QString &path) {
    auto programList = CommandListParser::parseInstructionList(path);

    QVector<LadderProgram> validProgramList;
    QVector<QPair<int, QString>> programNumsList;

    int totalRowCount = 0;
    int levelStartRow = 0;

    // 设置列布局
    setColCount(9);
    setColWidths({80, 70, 70, 70, 70, 70, 70, 300, 113});
    // 内容网格列数去掉左右各一列
    const int contentColCount = m_colCount - 2;

    // 先计算出显示所有程序所需网格的总大小
    for (auto &program : programList) {
        int progColCount = 0;
        int progRowCount = 0;

        for (auto &level : program.m_levels) {
            const auto inputSize = level.first.getCellSize();
            const auto outputSize = level.second.getCellSize();

            progRowCount += qMax(inputSize.height(), outputSize.height());
            progColCount = qMax(progColCount, inputSize.width() + outputSize.width());
        }

        if (progColCount > contentColCount) {
            LOG_WARN("{}包含{}列，无法完整显示，已跳过", program.m_name.toStdString(), progColCount);
            program.printContent();

            continue;
        }

        validProgramList.append(program);
        // 添加每个程序起始行的程序号
        if (totalRowCount > 0) {
            programNumsList.append(qMakePair(totalRowCount, program.m_name));
        }

        totalRowCount += progRowCount;
    }

    // 把程序号填入数组
    m_programNums.clear();
    m_programNums.resize(totalRowCount);
    for (auto &pair : programNumsList) {
        m_programNums[pair.first] = pair.second;
    }
    // 清空行注释（在 commandlist 中不包含行注释）
    m_programNote.clear();

    LOG_INFO("程序总行数：{}", totalRowCount);

    // 设置行布局
    setRowCount(totalRowCount);
    setRowHeight(70);

    m_cellData.clear();
    m_cellData.resize(contentColCount * m_rowCount);

    int curRow = 0;

    // 填充网格数据
    for (auto &program : validProgramList) {
        for (auto &level : program.m_levels) {
            auto inputFragment = level.first;
            auto outputFragment = level.second;

            // 填充输入部分网格数据
            auto inputSize = inputFragment.fillCellDataLeftAligned(m_cellData, contentColCount, 0, curRow);

            // 填充输出部分网格数据
            auto outputSize =
                outputFragment.fillCellDataRightAligned(m_cellData, contentColCount, contentColCount, curRow);

            // 补齐此层首行的横线
            for (int rx = inputSize.width(); rx < contentColCount - outputSize.width(); rx++) {
                m_cellData[contentColCount * curRow + rx] = LadderCellData(LadderCellType::Rung, QString());
            }

            // 下移光标准备填充下一层
            curRow += qMax(inputSize.height(), outputSize.height());
        }
    }

    LOG_DEBUG("网格数据填充完毕");

    emit cellDataLoaded(totalRowCount);
}

void LadderScrollArea::jumpToCell(const QPoint cellPos) {
    // 更新选中框位置
    m_mousePressCell = cellPos;
    m_mouseHoverCell = cellPos;

    // 滚动窗口，确保目标单元格在显示范围内
    if (cellPos.y() < m_startRow) {
        m_startRow = cellPos.y();
    }
    if (cellPos.y() >= m_startRow + m_visibleRowCount - 1) {
        // 若目标单元格显示在最下面一行，多滚一行避免遮挡
        m_startRow = cellPos.y() - m_visibleRowCount + 2;
    }

    m_startRow = qMax(0, m_startRow);
    m_startRow = qMin(m_startRow, m_maxStartRow);

    // 更新滚动条位置
    verticalScrollBar()->setValue(m_startRow * m_rowHeight);

    update();  // 触发重绘
}

void LadderScrollArea::loadVariableDesc(PlcManager *plcManager, CncManager *cncManager) {
    m_varDesc.clear();

    const auto &ioConfigList = plcManager->getIoPointConfigs();
    for (auto config : ioConfigList) {
        auto name = QString::fromStdString(config.name);
        auto desc = QString::fromStdString(config.description);

        m_varDesc[name] = desc;
    }

    const auto &varConfigList = plcManager->getPlcVariableConfigs();
    char prefix;

    for (auto config : varConfigList) {
        auto name = QString::fromStdString(config.name);
        auto desc = QString::fromStdString(config.description);

        m_varDesc[name] = desc;

        // 将变量的标识符存入查找表
        char prefix = getPlcPrefixChar(config.type);
        if (prefix == '\0') {
            // 自定义变量，跳过
            continue;
        }

        for (int addr = 0; addr < config.count; addr++) {
            auto varId = QString("%1%2").arg(prefix).arg(addr + config.startAddress, 3, 10, QLatin1Char('0'));
            m_plcVarIds[qMakePair(config.type, addr + config.startAddress)] = varId;
        }
    }

    // 获取用户定义宏变量范围
    const auto &config = cncManager->getSystemConfig();
    m_macroRange = config.macroVariableRange;
}

void LadderScrollArea::updateVisibleRowVariables() {
    // 动态计算可见行数（覆盖整个高度）
    m_visibleRowCount = rect().height() / m_rowHeight + 1;
    // 下方多留出一个空行，确保最后一行不被搜索区域遮挡
    m_maxStartRow = qMax(0, m_rowCount - m_visibleRowCount + 1);

    // 更新滚动条最大值
    verticalScrollBar()->setMaximum(m_maxStartRow * m_rowHeight);
}

QPoint LadderScrollArea::getGridCell(const QPointF &mousePoint) {
    // 计算鼠标点击的网格
    int x = 0, col = 0;
    for (; col < m_colCount; col++) {
        x += m_colWidths[col];
        if (mousePoint.x() <= x) {  // 已到达鼠标所指列
            break;
        }
    }

    int y = 0, row = 0;
    for (; row <= m_visibleRowCount; row++) {
        y += m_rowHeight;
        if (mousePoint.y() <= y) {  // 已到达鼠标所指行
            break;
        }
    }

    return QPoint(col, row + m_startRow);
}

void LadderScrollArea::scrollByLine(const int delta) {
    m_startRow += delta;

    m_startRow = qMax(0, m_startRow);
    m_startRow = qMin(m_startRow, m_maxStartRow);

    // 应用自定义滚动
    verticalScrollBar()->setValue(m_startRow * m_rowHeight);

    // LOG_DEBUG("梯形图滚动：{}行，当前起始行：{}/{}", delta, m_startRow, m_maxStartRow);
}

void LadderScrollArea::resizeEvent(QResizeEvent *event) {
    LOG_DEBUG("梯形图控件大小改变：{}x{} -> {}x{}", event->oldSize().width(), event->oldSize().height(),
              event->size().width(), event->size().height());

    // 重新设置各列列宽
    setColWidths({80, 70, 70, 70, 70, 70, 70, 300, event->size().width() - 800});

    // 视口高度改变后，更新可见行变量
    updateVisibleRowVariables();
}

void LadderScrollArea::wheelEvent(QWheelEvent *event) {
    QPoint numSteps = event->angleDelta() / 120;  // 标准滚轮步数（通常每步120度）
    int delta = numSteps.y() * -3;                // 计算实际滚动距离

    scrollByLine(delta);
    event->accept();

    update();  // 触发重绘
}

void LadderScrollArea::mousePressEvent(QMouseEvent *event) {
    QPoint cell = getGridCell(event->localPos());
    m_mousePressCell = cell;
    m_mouseHoverCell = cell;
    m_mouseIsPressed = true;

    update();  // 触发重绘

    // LOG_DEBUG("鼠标按下：{}, {} / 单元格：{}, {}", event->x(), event->y(), cell.x(), cell.y());
}

void LadderScrollArea::mouseReleaseEvent(QMouseEvent *event) {
    m_mouseIsPressed = false;

    update();  // 触发重绘

    // LOG_DEBUG("鼠标抬起：{}, {} / 单元格：{}, {}", event->x(), event->y());
}

void LadderScrollArea::mouseDoubleClickEvent(QMouseEvent *event) {
    QPoint cell = getGridCell(event->localPos());
    m_mousePressCell = cell;
    m_mouseHoverCell = cell;

    // 不是输出/命令列，不做处理
    if (cell.x() != m_colCount - 2) {
        return;
    }

    // 检查是否是调用命令
    int index = cell.y() * (m_colCount - 2) + cell.x() - 1;
    if (index >= 0 && index < m_cellData.size() && m_cellData[index].m_cellType == LadderCellType::Command) {
        QStringList texts = m_cellData[index].m_name.split(' ', Qt::SkipEmptyParts);

        if (texts.size() == 2 && texts[0] == "CALL") {
            // 跳转到对应子程序的首行行号处
            for (int i = 0; i < m_programNums.size(); i++) {
                if (m_programNums[i] == texts[1]) {
                    jumpToCell(QPoint(0, i));
                }
            }
        }
    }

    // LOG_DEBUG("鼠标双击：{}, {} / 单元格：{}, {}", event->x(), event->y(), cell.x(), cell.y());
}

void LadderScrollArea::mouseMoveEvent(QMouseEvent *event) {
    if (!m_mouseIsPressed) return;  // 若不是拖拽状态则不处理

    int yPos = event->localPos().y();
    if (yPos < 0) {  // 向上滚动
        scrollByLine(-1);
    } else if (yPos > (m_visibleRowCount - 1) * m_rowHeight) {  // 向下滚动
        scrollByLine(1);
    }

    QPoint cell = getGridCell(event->localPos());

    if (m_mouseHoverCell != cell) {  // 鼠标拖拽单元格发生改变
        m_mouseHoverCell = cell;

        update();  // 触发重绘
    }

    // LOG_DEBUG("鼠标移动：{}, {}", event->x(), event->y());
}

void LadderScrollArea::keyPressEvent(QKeyEvent *event) {
    LOG_DEBUG("按键按下：{}", event->key());

    auto curCell = m_mouseHoverCell;
    bool moveCell = false;

    if (event->key() == Qt::Key_Up && curCell.y() > 0) {
        moveCell = true;
        curCell.setY(curCell.y() - 1);
    }

    if (event->key() == Qt::Key_Down && curCell.y() < m_rowCount - 1) {
        moveCell = true;
        curCell.setY(curCell.y() + 1);
    }

    if (event->key() == Qt::Key_Left && curCell.x() > 0) {
        moveCell = true;
        curCell.setX(curCell.x() - 1);
    }

    if (event->key() == Qt::Key_Right && curCell.x() < m_colCount - 1) {
        moveCell = true;
        curCell.setX(curCell.x() + 1);
    }

    if (event->key() == Qt::Key_PageUp && curCell.y() > 0) {
        moveCell = true;
        curCell.setY(qMax(0, curCell.y() - m_visibleRowCount + 1));
    }

    if (event->key() == Qt::Key_PageDown && curCell.y() < m_rowCount - 1) {
        moveCell = true;
        curCell.setY(qMin(m_rowCount - 1, curCell.y() + m_visibleRowCount - 1));
    }

    if (moveCell) {
        // 跳转到单元格
        jumpToCell(curCell);
    } else {
        // 交给父级 FocusablePanel 处理
        event->ignore();
    }
}

QString LadderScrollArea::stringifyPlcValue(const PlcValue &value, const bool hex, const int precision) {
    if (bool const *pval = std::get_if<bool>(&value)) return QString(*pval ? "True" : "False");

    if (int16_t const *pval = std::get_if<int16_t>(&value)) {
        return hex             ? QString("0x%1").arg(QString::number(*pval, 16))
               : precision > 0 ? QString::number(*pval, 'f', precision)
                               : QString::number(*pval);
    }

    if (int32_t const *pval = std::get_if<int32_t>(&value)) {
        return hex             ? QString("0x%1").arg(QString::number(*pval, 16))
               : precision > 0 ? QString::number(*pval, 'f', precision)
                               : QString::number(*pval);
    }

    if (uint16_t const *pval = std::get_if<uint16_t>(&value)) {
        return hex             ? QString("0x%1").arg(QString::number(*pval, 16))
               : precision > 0 ? QString::number(*pval, 'f', precision)
                               : QString::number(*pval);
    }

    if (uint32_t const *pval = std::get_if<uint32_t>(&value)) {
        return hex             ? QString("0x%1").arg(QString::number(*pval, 16))
               : precision > 0 ? QString::number(*pval, 'f', precision)
                               : QString::number(*pval);
    }

    if (float const *pval = std::get_if<float>(&value)) {
        return QString::number(*pval, 'f', precision);
    }

    if (double const *pval = std::get_if<double>(&value)) {
        return QString::number(*pval, 'f', precision);
    }

    return QString("???");
}

void LadderScrollArea::updatePlcValue(QString variable, PlcVariableType varType, int address,
                                      const PlcValue &newValue) {
    QString value;

    if (varType == PlcVariableType::T_TIMER) {
        value = stringifyPlcValue(newValue, false, 3) + 's';
    } else if (varType == PlcVariableType::D_REGISTER) {
        // 参照变量地址使用相应格式
        if (address < 2000) {  // 使用十进制整型格式
            value = stringifyPlcValue(newValue, false, 0);
        } else if (address < 4000) {  // 使用十六进制整型格式
            value = stringifyPlcValue(newValue, true, 0);
        } else {  // 使用浮点数格式，保留三位小数
            value = stringifyPlcValue(newValue, false, 3);
        }
    } else {  // 使用十六进制整型格式
        value = stringifyPlcValue(newValue, true, 0);
    }

    m_varValue[variable] = value;

    LOG_DEBUG("PLC变量{}的值更新为{}", variable.toStdString(), value.toStdString());
}

char LadderScrollArea::getPlcPrefixChar(const PlcVariableType type) {
    switch (type) {
        case PlcVariableType::D_REGISTER:
            return 'D';
        case PlcVariableType::R_REGISTER:
            return 'R';
        case PlcVariableType::M_RELAY:
            return 'M';
        case PlcVariableType::T_TIMER:
            return 'T';
        case PlcVariableType::C_COUNTER:
            return 'C';
        case PlcVariableType::K_CONSTANT:
            return 'K';
        case PlcVariableType::X_INPUT:
            return 'X';
        case PlcVariableType::Y_OUTPUT:
            return 'Y';
        case PlcVariableType::B_REGISTER:
            return 'B';
        case PlcVariableType::W_REGISTER:
            return 'W';
        case PlcVariableType::F_REGISTER:
            return 'F';
        case PlcVariableType::G_REGISTER:
            return 'G';
        default:
            return '\0';
    }
}

PlcVariableType LadderScrollArea::getPlcVariableType(const char prefix) {
    switch (prefix) {
        case 'D':
            return PlcVariableType::D_REGISTER;
        case 'R':
            return PlcVariableType::R_REGISTER;
        case 'M':
            return PlcVariableType::M_RELAY;
        case 'T':
            return PlcVariableType::T_TIMER;
        case 'C':
            return PlcVariableType::C_COUNTER;
        case 'K':
            return PlcVariableType::K_CONSTANT;
        case 'X':
            return PlcVariableType::X_INPUT;
        case 'Y':
            return PlcVariableType::Y_OUTPUT;
        case 'B':
            return PlcVariableType::B_REGISTER;
        case 'W':
            return PlcVariableType::W_REGISTER;
        case 'F':
            return PlcVariableType::F_REGISTER;
        case 'G':
            return PlcVariableType::G_REGISTER;
        default:
            return PlcVariableType::CUSTOM;
    }
}

QString LadderScrollArea::getVariableDisplayValue(QString &variable) {
    if (variable.isEmpty()) {
        // 确保后续过程中变量名非空
        return QString();
    }

    // 检查是否使用变量位
    int pointIndex = variable.indexOf(".");
    if (pointIndex >= 0) {
        variable = variable.left(pointIndex);
    }

    if (m_varValue.contains(variable)) {
        // 已经缓存，直接返回
        return m_varValue[variable];
    } else {
        // 尚未缓存，从 PlcManager 或 CncManager 查询
        const char prefix = variable[0].toLatin1();

        if (prefix == '#') {  // 宏变量
            double rawValue = 0.0f;

            int channelId = 0;                    // 通道号 TODO: 确认
            int index = variable.mid(1).toInt();  // 索引号

            if (index < m_macroRange.minUserVarIndex || index > m_macroRange.maxUserVarIndex) {
                LOG_WARN("宏变量索引号超出范围：{}", index);
                return QString();
            }

            auto errorCode = CncManager::getInstance()->getMacroVariable(channelId, index, rawValue);
            if (errorCode != ErrorCode::Success) {
                LOG_WARN("宏变量{}的值更新失败！", variable.toStdString());
                m_varValue[variable] = QString("ERROR %1").arg(static_cast<int>(errorCode));
            } else {
                m_varValue[variable] = QString::number(rawValue, 'f', 3);
                LOG_DEBUG("宏变量{}的值更新为{}", variable.toStdString(), m_varValue[variable].toStdString());
            }
        } else {  // PLC变量
            PlcValue rawValue = 0.0f;
            int address = variable.mid(1).toInt();
            // 根据前缀确定变量类型
            auto varType = getPlcVariableType(prefix);

            auto errorCode = PlcManager::getInstance()->getPlcVariable(varType, address, rawValue);
            if (errorCode != ErrorCode::Success) {
                LOG_WARN("PLC变量{}的值获取失败！", variable.toStdString());
                m_varValue[variable] = QString("ERROR %1").arg(static_cast<int>(errorCode));
            } else {
                updatePlcValue(variable, varType, address, rawValue);
            }
        }

        return m_varValue[variable];
    }
}

void LadderScrollArea::drawBackground(QPainter &painter) const {
    // 绘制白色背景
    painter.fillRect(rect(), Qt::white);
}

void LadderScrollArea::drawRails(QPainter &painter) const {
    // 绘制行号（程序号）和行注释
    painter.setFont(*m_ladderFont);

    int y = 0;
    for (int row = 0; row <= m_visibleRowCount; row++) {
        y += m_rowHeight;
        int rowNum = row + m_startRow;

        if (rowNum >= m_rowCount) {
            break;  // 末尾补齐的空行，结束
        }

        // 行号（程序号）这一格的rect
        auto rect = QRect(0, y - m_rowHeight, m_colWidths[0] - 6, m_rowHeight);

        if (rowNum >= 0 && rowNum < m_programNums.size() && !m_programNums[rowNum].isEmpty()) {
            painter.setPen(*m_programNumPen);  // 蓝色
            auto numString = QString("%1 %2").arg(m_programNums[rowNum]).arg(rowNum, 3, 10, QLatin1Char('0'));
            // 绘制在rect中垂直居中水平靠右的位置
            painter.drawText(rect, Qt::AlignRight | Qt::AlignVCenter, numString);
        } else {
            painter.setPen(*m_rowNumPen);  // 绿色
            auto numString = QString("%1").arg(rowNum, 3, 10, QLatin1Char('0'));
            // 绘制在rect中垂直居中水平靠右的位置
            painter.drawText(rect, Qt::AlignRight | Qt::AlignVCenter, numString);
        }

        painter.setPen(*m_notePen);  // 深蓝色
        if (rowNum >= 0 && rowNum < m_programNote.size() && !m_programNote[rowNum].isEmpty()) {
            // 注释这一格的rect
            rect = QRect(m_colLeftPos[m_colCount - 1] + 8, y - m_rowHeight, m_colWidths[m_colCount - 1], m_rowHeight);
            // 绘制在rect中垂直居中水平靠左的位置
            painter.drawText(rect, Qt::AlignLeft | Qt::AlignVCenter, m_programNote[rowNum]);
        }
    }

    // 绘制左母线
    painter.setPen(*m_rungPen);  //  黑色
    int x = m_colWidths[0];
    painter.drawLine(x - 1, 0, x - 1, m_visibleRowCount * m_rowHeight);  //  左偏1px

    // 绘制右母线
    x = m_colLeftPos[m_colCount - 1];
    painter.drawLine(x + 1, 0, x + 1, m_visibleRowCount * m_rowHeight);  //  右偏1px
}

void LadderScrollArea::drawCellSelection(QPainter &painter, const int cellX, const int cellY) const {
    bool focused = hasFocus();

    painter.setPen(focused ? *m_selectionPen : *m_unfocusedPen);
    painter.setBrush(focused ? *m_selectionBrush : *m_unfocusedBrush);

    if (cellX >= 0 && cellX < m_colCount && cellY >= m_startRow &&
        cellY < m_startRow + m_visibleRowCount & cellY < m_rowCount) {
        int x = m_colLeftPos[cellX];
        int y = (cellY - m_startRow) * m_rowHeight;
        int w = m_colWidths[cellX];
        int h = m_rowHeight;

        // 检查单元格内容类型来确定单元格大小
        if (cellX >= 1 && cellX < m_colCount - 1) {
            // 内容网格列数去掉左右各一列
            const int contentColCount = m_colCount - 2;
            auto cellType = m_cellData[cellY * contentColCount + (cellX - 1)].m_cellType;

            if (cellType == LadderCellType::Dummy) {
                // 此单元格被其他元件占据，绘制该元件的选中框
                if (cellX >= 2) {  // 左边还有元件，往左边找
                    drawCellSelection(painter, cellX - 1, cellY);
                }
                return;
            }

            if (cellType == LadderCellType::Condition && cellX < m_colCount - 1) {
                // 占用右边格子的空间
                w += m_colWidths[cellX + 1];
            }
        }

        // 绘制矩形
        QRect cellRect = QRect(x, y, w - 1, h - 1);
        painter.drawRect(cellRect);
    }
}

void LadderScrollArea::drawCellVertLine(QPainter &painter, const int cellX, const int cellY) const {
    if (cellX >= 1 && cellX < m_colCount - 1 && cellY >= m_startRow - 1 && cellY >= 0 &&
        cellY < m_startRow + m_visibleRowCount & cellY < m_rowCount) {
        // 内容网格列数去掉左右各一列
        const int contentColCount = m_colCount - 2;
        int x = m_colLeftPos[cellX];
        int y = (cellY - m_startRow) * m_rowHeight;
        int w = m_colWidths[cellX];
        int h = m_rowHeight;

        auto cellData = m_cellData[cellY * contentColCount + (cellX - 1)];

        // 绘制左侧垂直线
        if (cellData.m_leftVertLine) {
            LadderDrawer::drawVerticalLine(painter, *m_rungPen, x, y + h / 2, h);
        }

        // 绘制右侧垂直线
        if (cellData.m_rightVertLine) {
            LadderDrawer::drawVerticalLine(painter, *m_rungPen, x + w, y + h / 2, h);
        }
    }
}

void LadderScrollArea::drawCellContent(QPainter &painter, const int cellX, const int cellY) {
    if (cellX >= 1 && cellX < m_colCount - 1 && cellY >= m_startRow &&
        cellY < m_startRow + m_visibleRowCount & cellY < m_rowCount) {
        // 内容网格列数去掉左右各一列
        const int contentColCount = m_colCount - 2;
        int x = m_colLeftPos[cellX];
        int y = (cellY - m_startRow) * m_rowHeight;
        int w = m_colWidths[cellX];
        int h = m_rowHeight;

        auto cellData = m_cellData[cellY * contentColCount + (cellX - 1)];

        // 从查找表找变量对应的名称/注释
        auto note = cellData.getCellNoteKey();
        if (!note.isEmpty()) {
            note = m_varDesc.contains(note) ? m_varDesc[note] : QString();
        }

        // 绘制单元格图形
        switch (cellData.m_cellType) {
            case LadderCellType::NO:
                LadderDrawer::drawContact(painter, *m_rungPen, *m_notePen, x, y, w, h, cellData.m_name, note,
                                          *m_ladderFont);
                break;
            case LadderCellType::NC:
                LadderDrawer::drawContact(painter, *m_rungPen, *m_notePen, x, y, w, h, cellData.m_name, note,
                                          *m_ladderFont);
                LadderDrawer::drawMarkNC(painter, *m_rungPen, x, y, w, h);
                break;
            case LadderCellType::RisePulse:
                LadderDrawer::drawContact(painter, *m_rungPen, *m_notePen, x, y, w, h, cellData.m_name, note,
                                          *m_ladderFont);
                LadderDrawer::drawMarkRisePulse(painter, *m_rungPen, x, y, w, h);
                break;
            case LadderCellType::FallPulse:
                LadderDrawer::drawContact(painter, *m_rungPen, *m_notePen, x, y, w, h, cellData.m_name, note,
                                          *m_ladderFont);
                LadderDrawer::drawMarkFallPulse(painter, *m_rungPen, x, y, w, h);
                break;
            case LadderCellType::Rung:
                LadderDrawer::drawRung(painter, *m_rungPen, x, y, w, h);
                break;
            case LadderCellType::Invert:
                LadderDrawer::drawRung(painter, *m_rungPen, x, y, w, h);
                LadderDrawer::drawMarkNC(painter, *m_rungPen, x, y, w, h);
                break;
            case LadderCellType::Coil:
                LadderDrawer::drawCoil(painter, *m_rungPen, *m_notePen, *m_valuePen, x, y, w, h, cellData.m_name, note,
                                       *m_coilFont, *m_ladderFont, [this](QString &varName) -> QString {
                                           return this->getVariableDisplayValue(varName);
                                       });
                break;
            case LadderCellType::Command:
                LadderDrawer::drawCommand(
                    painter, *m_rungPen, *m_notePen, *m_valuePen, x, y, w, h, cellData.m_name, note, *m_ladderFont,
                    [this](QString &varName) -> QString { return this->getVariableDisplayValue(varName); });
                break;
            case LadderCellType::Condition:
                if (cellX < m_colCount - 1) {
                    // 占用右边格子的空间
                    w += m_colWidths[cellX + 1];
                }

                LadderDrawer::drawCondition(painter, *m_rungPen, *m_conditionBrush, *m_notePen, x, y, w, h,
                                            cellData.m_name, note, *m_ladderFont);
                break;
        }

        // 绘制左侧垂直线
        if (cellData.m_leftVertLine) {
            LadderDrawer::drawVerticalLine(painter, *m_rungPen, x, y + h / 2, h);
        }

        // 绘制右侧垂直线
        if (cellData.m_rightVertLine) {
            LadderDrawer::drawVerticalLine(painter, *m_rungPen, x + w, y + h / 2, h);
        }
    }
}

void LadderScrollArea::paintEvent(QPaintEvent *event) {
    Q_UNUSED(event)
    // 对于 QAbstractScrollArea 的子类，应使用其视口对应的 painter 来绘制
    QPainter painter(this->viewport());

    // 绘制背景
    drawBackground(painter);

    // 绘制当前选中框
    int minCellX = qMin(m_mousePressCell.x(), m_mouseHoverCell.x());
    int maxCellX = qMax(m_mousePressCell.x(), m_mouseHoverCell.x());
    int minCellY = qMin(m_mousePressCell.y(), m_mouseHoverCell.y());
    int maxCellY = qMax(m_mousePressCell.y(), m_mouseHoverCell.y());

    for (int col = minCellX; col <= maxCellX; col++) {
        for (int row = minCellY; row <= maxCellY; row++) {
            drawCellSelection(painter, col, row);
        }
    }

    // 绘制母线和行号
    drawRails(painter);

    // 竖直线包含当前行的下半行和下一行的上半行，所以需要绘制第 m_startRow - 1 行的竖直线
    if (m_startRow > 0) {
        for (int col = 1; col < m_colCount - 1; col++) {  // 忽略最左和最右列
            drawCellVertLine(painter, col, m_startRow - 1);
        }
    }

    // 绘制网格内容
    for (int col = 1; col < m_colCount - 1; col++) {  // 忽略最左和最右列
        for (int row = 0; row <= m_visibleRowCount; row++) {
            drawCellContent(painter, col, m_startRow + row);
        }
    }
}

void LadderScrollArea::focusInEvent(QFocusEvent *event) {
    Q_UNUSED(event)
    LOG_DEBUG("梯形图滚动区域获得焦点");
    update();  // 触发重绘
}

void LadderScrollArea::focusOutEvent(QFocusEvent *event) {
    Q_UNUSED(event)
    LOG_DEBUG("梯形图滚动区域失去焦点");
    update();  // 触发重绘
}

void LadderScrollArea::onScrollBarMoved(int value) {
    float scrollPos = (float)value / verticalScrollBar()->maximum();

    if (m_maxStartRow > 0) {
        m_startRow = (int)(m_maxStartRow * scrollPos);

        // LOG_DEBUG("梯形图滚动条拖动，当前起始行：{}/{}", m_startRow, m_maxStartRow);
    }

    update();  // 触发重绘
}

void LadderScrollArea::onPlcVariableChanged(int address, PlcVariableType varType, const PlcValue &newValue) {
    const QPair varKey = qMakePair(varType, address);
    if (!m_plcVarIds.contains(varKey)) {
        LOG_WARN("未找到PLC变量的变量名！类型：{}，地址：{}", static_cast<uint8_t>(varType), address);
        return;
    }

    // 更新缓存
    const QString variable = m_plcVarIds[varKey];
    updatePlcValue(variable, varType, address, newValue);

    // 检查更改的变量是否触发重绘
    bool shouldUpdate = false;
    const int startIndex = m_startRow * (m_colCount - 2);
    const int endIndex = qMin(m_startRow + m_visibleRowCount, m_rowCount) * (m_colCount - 2);

    for (int index = startIndex; index < endIndex; index++) {
        if (m_cellData[index].containsTarget(variable)) {
            // 可考虑改为局部重绘提高性能
            shouldUpdate = true;
        }
    }

    if (shouldUpdate) {
        update();  // 触发重绘
    }
};
