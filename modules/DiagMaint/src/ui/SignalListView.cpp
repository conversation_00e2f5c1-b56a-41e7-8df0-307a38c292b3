#include "SignalListView.h"

#include "core/AppLogger.h"

SignalListView::SignalListView(QWidget* parent) : FocusablePanel(parent) {
    setObjectName("SignalListView");
    setupUi();
}

SignalListView::~SignalListView() {
    // Qt 会自动处理子控件的删除
}

void SignalListView::setupUi() {
    m_mainLayout = new QVBoxLayout(contentArea());
    m_mainLayout->setContentsMargins(0, 0, 0, 0);  // 去除边距
    m_mainLayout->setSpacing(0);                   // 去除间距

    // 工作区
    m_SignalListViewWidget = new QWidget(this);
    m_SignalListViewWidget->setObjectName("SignalListViewWidget");
    m_SignalListViewLayout = new QVBoxLayout(m_SignalListViewWidget);
    m_SignalListViewLayout->setContentsMargins(0, 0, 0, 0);  // 可以设置边距
    m_SignalListViewLayout->setSpacing(0);

    m_ladderView = new NewLadderView();  //  梯形图界面
    m_SignalListViewLayout->addWidget(m_ladderView);

    m_currtUI = m_ladderView;

    // 工作区添加到主布局
    m_mainLayout->addWidget(m_SignalListViewWidget);

    contentArea()->setLayout(m_mainLayout);  // 设置主布局
    // 不需要标题栏，标题栏隐藏
    titleBarArea()->setVisible(false);
}

void SignalListView::handleMenuAction(const QString& actionId, const QVariantMap& states) {
    LOG_DEBUG("SignalListView::handleMenuAction: actionId={}", actionId.toStdString());
    // 这里可以根据需要添加具体的处理逻辑
    Q_UNUSED(states)
}

// 显示梯形图界面
void SignalListView::showLadderView() {
    m_SignalListViewLayout->removeWidget(m_currtUI);
    m_currtUI->hide();
    m_SignalListViewLayout->addWidget(m_ladderView);
    m_ladderView->show();
    m_currtUI = m_ladderView;
}

void SignalListView::findPrevInLadderView() {
    if (m_ladderView) {
        m_ladderView->findPrevInWidget();
    }
}

void SignalListView::findNextInLadderView() {
    if (m_ladderView) {
        m_ladderView->findNextInWidget();
    }
}
