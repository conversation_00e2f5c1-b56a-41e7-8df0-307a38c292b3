

#ifndef SignalListView_H
#define SignalListView_H

#include <QVBoxLayout>
#include <QWidget>

#include "FocusablePanel.h"
#include "LadderView/NewLadderView.h"
#include "LadderView/OldLadderView.h"

class SignalListView : public FocusablePanel {
    Q_OBJECT

   public:
    explicit SignalListView(QWidget* parent = nullptr);
    ~SignalListView();

    // 显示梯形图
    void showLadderView();

    // 梯形图查找上一个
    void findPrevInLadderView();

    // 梯形图查找下一个
    void findNextInLadderView();

   private slots:
    // 可以添加槽函数来响应用户交互或更新状态

   protected:
    void handleMenuAction(const QString& actionId, const QVariantMap& states) override;

   private:
    void setupUi();  // 用于设置界面布局和控件

    // 主布局
    QVBoxLayout* m_mainLayout = nullptr;

    // 工作区
    QWidget* m_SignalListViewWidget = nullptr;
    QVBoxLayout* m_SignalListViewLayout = nullptr;

    NewLadderView* m_ladderView = nullptr;

    QWidget* m_currtUI = nullptr;
};

#endif  // SignalListView_H