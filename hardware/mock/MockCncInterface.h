#pragma once

#include <atomic>
#include <chrono>
#include <condition_variable>
#include <map>
#include <mutex>
#include <string>
#include <thread>
#include <vector>

#include "ICncEventSystem.h"  // 包含事件系统头文件
#include "ICncInterface.h"    // ICncInterface.h 应该在正确的包含路径下
#include "ParaDef.h"

typedef struct MachineParameter {
    MachineParameter() {
        m_type = 0;
        memset(m_name, 0, sizeof(m_name));
        memset(m_id, 0, sizeof(m_id));
        memset(m_systemName, 0, sizeof(m_systemName));
        memset(m_sysVersion, 0, sizeof(m_sysVersion));
    }

    int32_t m_type;
    char m_name[32];
    char m_id[32];
    char m_systemName[32];
    char m_sysVersion[32];
} MachineParameter;

// 一个用于模拟和调试的 ICncInterface 实现
class MockCncInterface : public ICncInterface {
   public:
    MockCncInterface();
    virtual ~MockCncInterface() override;

    // --- 生命周期管理 ---
    ErrorCode initialize(const CncInitializationParams& params) override;
    ErrorCode shutdown() override;
    bool isInitialized() const override;

    // --- 配置参数读取 ---
    ErrorCode getSystemConfig(SystemConfig& config) override;

    // --- 系统状态与模式 ---
    ErrorCode getSystemState(CncSystemState& state) override;
    ErrorCode getOperatingMode(int channelId, OperatingMode& mode) override;
    ErrorCode setOperatingMode(int channelId, OperatingMode mode) override;
    ErrorCode getLastError(std::string& errorMsg) override;
    ErrorCode getActiveGFunctionList(int channelId, std::vector<ActiveGFunction>& functions) override;

    // --- 坐标系与位置 ---
    ErrorCode getWorkPosition(int channelId, PointXD& position, bool isReadFromCNC = false) override;
    ErrorCode getMachinePosition(int channelId, PointXD& position) override;
    ErrorCode getRelativePosition(int channelId, PointXD& position) override;
    ErrorCode getRemainingPosition(int channelId, PointXD& position) override;
    ErrorCode getAxisWorkPosition(int channelId, int axisIndex, double& position) override;
    ErrorCode getAxisMachinePosition(int channelId, int axisIndex, double& position) override;
    ErrorCode getCurrentWorkOffsetName(int channelId, std::string& name) override;
    ErrorCode getCurrentWorkOffsetValue(int channelId, PointXD& offset) override;
    ErrorCode getWorkOffsetValue(int channelId, const std::string& workOffsetName, PointXD& offset) override;
    ErrorCode setWorkOffsetValue(int channelId, const std::string& workOffsetName, const PointXD& offset) override;

    // --- 进给与主轴 ---
    ErrorCode getCommandFeedrate(int channelId, double& feedrate) override;
    ErrorCode getActualFeedrate(int channelId, double& feedrate) override;
    ErrorCode getFeedOverride(int channelId, double& overridePercent) override;
    ErrorCode setFeedOverride(int channelId, double overridePercent) override;
    ErrorCode getRapidOverride(int channelId, double& overridePercent) override;
    ErrorCode setRapidOverride(int channelId, double overridePercent) override;
    ErrorCode getCommandSpindleSpeed(int channelId, int spindleIndex, double& speedRpm) override;
    ErrorCode getActualSpindleSpeed(int channelId, int spindleIndex, double& speedRpm) override;
    ErrorCode getSpindleOverride(int channelId, double& overridePercent) override;
    ErrorCode setSpindleOverride(int channelId, double overridePercent) override;
    ErrorCode isSpindleOn(int channelId, int spindleIndex, bool& isOn) override;

    // --- 刀库管理 (高级接口) ---
    ErrorCode getToolInfoInPocket(int toolChangerId, int pocketNumber, ToolInfo& toolInfo) override;
    ErrorCode loadToolIntoPocket(int toolChangerId, int pocketNumber, int toolNumber) override;
    ErrorCode unloadToolFromPocket(int toolChangerId, int pocketNumber, int& unloadedToolNumber) override;
    ErrorCode exchangeToolsInPockets(int toolChangerId, int pocketNumber1, int pocketNumber2) override;
    ErrorCode moveToolFromPocketToSpindle(int channelId, int toolChangerId, int pocketNumber,
                                          int spindleIndex) override;
    ErrorCode moveToolFromSpindleToPocket(int channelId, int spindleIndex, int toolChangerId, int pocketNumber,
                                          int* movedToPocketToolNumber) override;
    ErrorCode getMagazineStatus(int toolChangerId, std::vector<PocketStatus>& pocketStatuses) override;
    ErrorCode getCurrentToolInfo(int channelId, ToolInfo& toolInfo) override;

    // --- 基于 UUID 的刀具管理接口 ---
    ErrorCode getToolParameters(const std::string& uuid, ToolInfo& toolInfo) override;
    ErrorCode setToolParameters(const ToolInfo& toolInfo) override;
    ErrorCode deleteTool(const std::string& uuid) override;
    ErrorCode getAllToolParameters(std::map<std::string, ToolInfo>& allToolsInfo) override;
    ErrorCode getToolsByMagazine(int toolChangerId, std::vector<ToolInfo>& toolsInMagazine) override;

    // --- 程序执行 ---
    ErrorCode loadProgram(int channelId, const std::string& filePath) override;
    ErrorCode compileProgram(int channelId, const std::string filePath, std::vector<GMotion>& gMotions) override;
    ErrorCode startProgram(int channelId) override;
    ErrorCode pauseProgram(int channelId) override;
    ErrorCode resumeProgram(int channelId) override;
    ErrorCode stopProgram(int channelId) override;
    ErrorCode resetProgram(int channelId) override;
    ErrorCode getProgramStatus(int channelId, ProgramStatus& status) override;
    ErrorCode executeMdi(int channelId, const std::string& mdiString) override;
    ErrorCode setBlockSkip(int channelId, bool enable) override;
    ErrorCode setOptionalStop(int channelId, bool enable) override;
    ErrorCode setSingleStepMode(int channelId, SingleBlockModeType mode) override;
    ErrorCode setProgramTestMode(int channelId, bool enable) override;
    ErrorCode setDryRunMode(int channelId, bool enable) override;
    ErrorCode setHandwheelMode(int channelId, bool enable) override;
    ErrorCode getMacroVariable(int channelId, int index, double& value) override;
    ErrorCode setMacroVariable(int channelId, int index, double value) override;

    // --- 手动操作 ---
    ErrorCode startJog(int channelId, int axisIndex, double speed, double distance = 0.0) override;
    ErrorCode stopJog(int channelId, int axisIndex) override;
    ErrorCode setMpgControl(int channelId, int axisIndex, double incrementPerPulse, bool active) override;
    ErrorCode getMpgControlStatus(int channelId, int& controlledAxisIndex, double& currentIncrementPerPulse,
                                  bool& isAnyAxisMpgControlled) override;

    // --- 报警处理 ---
    ErrorCode getActiveAlarms(std::vector<AlarmInfo>& alarms) override;
    ErrorCode clearAlarms(int channelId = -1) override;

    // --- 回零操作 ---
    ErrorCode startHoming(int channelId, const std::vector<int>& axisIndices) override;
    ErrorCode isAxisHomed(int channelId, int axisIndex, bool& isHomed) override;
    ErrorCode areAllAxesHomed(int channelId, bool& allHomed) override;

    // --- 螺距补偿管理 ---
    ErrorCode getPitchCompensation(int channelId, int axisIndex, AxisPitchCompensation& compensation) override;
    ErrorCode setPitchCompensation(int channelId, int axisIndex, const AxisPitchCompensation& compensation) override;
    ErrorCode enablePitchCompensation(int channelId, int axisIndex, bool enable, bool isCyclicMode = false) override;
    ErrorCode getPitchCompensationStatus(int channelId, int axisIndex, bool& isEnabled, bool& isCyclicMode,
                                         double& currentCompensation) override;
    ErrorCode getAllPitchCompensations(int channelId, std::vector<AxisPitchCompensation>& allCompensations) override;
    ErrorCode setAllPitchCompensations(int channelId, const std::vector<AxisPitchCompensation>& compensations) override;

    // --- 系统通用配置管理 ---
    ErrorCode getConfiguration(std::vector<ConfigCategory>& rootCategories) override;
    ErrorCode setConfiguration(const std::vector<ConfigCategory>& rootCategoriesToUpdate) override;
    ErrorCode getConfigurationImpl(std::vector<ConfigCategory>& rootCategories);                // 自动生成
    ErrorCode setConfigurationImpl(const std::vector<ConfigCategory>& rootCategoriesToUpdate);  // 自动生成

    // --- 事件处理 ---
    ErrorCode registerEventListener(ICncEventListener* listener) override;
    ErrorCode unregisterEventListener(ICncEventListener* listener) override;

    // --- Mock 特定控制方法 (用于测试和调试) ---
    void mock_setInitialPositionForChannel(int channelId, int axisIndex, double machinePos, double workOffsetPos);
    void mock_setSystemState(CncSystemState state);  // 强制设置整体系统状态
    void mock_forceSetOperatingModeForChannel(int channelId, OperatingMode mode);
    void mock_addSimulatedAlarm(const AlarmInfo& alarm);
    void mock_clearSimulatedAlarms();
    void mock_setSimulatedToolForChannel(int channelId, const ToolInfo& tool);
    void mock_setSimulatedGlobalToolParameters(int toolNumber, const ToolInfo& tool);

    void mock_setSimulatedMacroVariableForChannel(int channelId, int index, double value);
    void mock_setSimulatedHomeStateForChannelAxis(int channelId, int axisIndex, bool isHomed);
    void mock_simulateError(ErrorCode error, const std::string& message);

   private:
    // 模拟内部状态
    std::atomic<bool> m_initialized{false};  // 是否已初始化
    IAppLogger* m_logger = nullptr;          // 日志接口指针
    CncSystemState m_overallSystemState{
        CncSystemState::Offline};  // 模拟的系统总体状态 (atomic for direct read, mutex for complex changes)
    std::string m_lastError{"无错误"};                       // 最后一个错误信息
    std::string m_sdkVersion{"MockSDK v2.0-multi-channel"};  // 模拟的 SDK 版本
    std::string m_writablePath;                              // 可写目录路径，用于保存配置文件
    std::string m_configPath;                                // 配置文件路径，用于读取默认刀具等

    SystemConfig system_config_;  // 存储所有静态配置信息

    // 每通道状态变量
    std::vector<OperatingMode> channel_operating_modes_;
    std::vector<PointXD> channel_machine_positions_;  // MCS, key: globalAxisIndex
    std::vector<std::map<std::string, PointXD>>
        channel_work_offset_values_;  // WCS offset values, key: workOffsetName -> PointXD(globalAxisIndex -> value)
    std::vector<PointXD> channel_remaining_positions_;  // 余程位置, key: globalAxisIndex
    std::vector<std::string> channel_current_work_offset_names_;
    std::vector<double> channel_command_feedrates_;
    std::vector<double> channel_actual_feedrates_;
    std::vector<double> channel_feed_overrides_;
    std::vector<double> channel_rapid_overrides_;
    std::vector<double> channel_spindle_overrides_;  // 通道级主轴倍率

    // 每通道，每个主轴的状态 (map key 是 globalSpindleIndex)
    std::vector<std::map<int, double>> channel_command_spindle_speeds_rpm_;
    std::vector<std::map<int, double>> channel_actual_spindle_speeds_rpm_;
    std::vector<std::map<int, bool>> channel_spindle_on_states_;

    std::vector<ToolInfo> channel_current_tool_info_;  // 当前在通道主轴上的刀具
    std::vector<ProgramStatus> channel_program_status_;
    std::vector<std::vector<ActiveGFunction>> channel_active_g_functions_;
    std::vector<std::map<int, double>> channel_macro_variables_;  // key: macro index

    // 程序执行模拟相关 (每通道)
    std::vector<std::vector<std::string>> channel_program_lines_;  // 每个通道加载的程序行
    std::vector<std::string> channel_loaded_program_paths_;        // 每个通道加载的程序文件路径

    // 程序执行控制状态 (每通道)
    enum class ProgramExecutionState {
        Idle,     // 空闲状态，没有加载程序或程序已停止
        Loaded,   // 程序已加载但未启动
        Running,  // 程序正在运行
        Paused,   // 程序已暂停
        Stopped   // 程序已停止但未重置
    };
    std::vector<ProgramExecutionState> channel_program_execution_states_;

    // 程序执行同步原语 (每通道)
    std::vector<std::unique_ptr<std::condition_variable>> channel_program_execution_cvs_;
    std::vector<std::unique_ptr<std::mutex>> channel_program_execution_mutexes_;

    // 每通道，每个轴的回零状态 (map key 是 globalAxisIndex)
    std::vector<std::map<int, bool>> channel_axis_homed_status_;

    // MPG 相关状态 (每个通道可能有一个轴被控制)
    struct MpgControlState {
        int controlledAxisIndex = -1;
        double incrementPerPulse = 0.001;
        bool isActive = false;
    };
    std::vector<MpgControlState> channel_mpg_control_status_;

    // 全局状态 (非通道特定)
    // === 刀具存储结构 ===
    std::map<std::string, ToolInfo> m_toolParametersByUuid;  // 主要存储：按 UUID 索引的刀具参数库
    std::multimap<int, std::string> m_toolNumberToUuidIndex;  // 辅助索引：刀号到 UUID 的映射 (支持多个刀沿)
    std::vector<AlarmInfo> m_activeAlarms;  // 模拟的活动报警列表 (AlarmInfo 内部有 channelId)

    // === 螺距补偿存储结构 ===
    struct PitchCompensationState {
        AxisPitchCompensation config;      // 轴的螺距补偿配置
        bool isEnabled = false;            // 是否启用螺距补偿
        bool isCyclicMode = false;         // 是否为循环补偿模式
        double currentCompensation = 0.0;  // 当前生效的补偿值 (0.1μm)
    };
    std::map<int, PitchCompensationState> m_pitchCompensationStates;  // 按轴索引存储螺距补偿状态
    // ConfigCategory related members for get/setConfiguration if needed
    std::vector<ConfigCategory> m_configCategories;

    // 事件监听器列表
    std::vector<ICncEventListener*> m_eventListeners;

    // 内部辅助
    std::mutex m_mutex;  // 用于保护非原子成员的访问

    // 点动模拟相关 (需要适配多通道和 axisIndex)
    struct JoggingAxisInfo {
        std::thread jogThread;
        std::atomic<bool> stopFlag{false};
        int channelId;
        int axisIndex;
        double speed;
        double distance;
        JoggingAxisInfo(std::thread&& t, int ch, int ax, double spd, double dist)
            : jogThread(std::move(t)), stopFlag(false), channelId(ch), axisIndex(ax), speed(spd), distance(dist) {}
        JoggingAxisInfo() = default;
    };
    std::map<std::pair<int, int>, JoggingAxisInfo> m_joggingAxes;  // key: {channelId, axisIndex}

    void simulateJogMovement(int channelId, int axisIndex, double speed, double distance);
    void updatePositionForJog(int channelId, int axisIndex, double delta);

    // 辅助函数 (将在 MockCncInterface.cpp 中实现)
    void populateDefaultSystemConfig(int numChannels, int numAxesPerChannel, int numSpindlesPerChannel);
    void initializeChannelSpecificStates();
    void initializeToolSimulationData();  // 初始化刀具模拟数据
    ErrorCode validateChannel(int channelId, const std::string& functionName);
    ErrorCode validateChannelAndAxis(int channelId, int axisIndex, const std::string& functionName);
    ErrorCode validateChannelAndSpindle(int channelId, int spindleIndex, const std::string& funcName);

    // 程序执行模拟私有方法
    void executeProgramForChannel(int channelId);                           // 每个通道的程序执行主循环
    void parseAndExecuteGCodeLine(int channelId, const std::string& line);  // 解析并执行单行G代码
    void simulateMovement(int channelId, const PointXD& targetPosition, double feedrate);  // 模拟运动
    bool shouldSkipLine(int channelId, const std::string& line);  // 检查是否应跳过此行

    // 事件发送方法
    void sendProgramStatusChangedEvent(int channelId, const ProgramStatus& status,
                                       ChannelProgramStatusEventPayload::ChangeReason reason);

    // ------------------------------------------------------------
    // ONLY FOR TEST
    // ------------------------------------------------------------

    MachineParameter m_machineParameter;

    // Googol 参数指针 (共享内存)
    NC_OUT_PTR m_ncOutPtr;                 // NC 输出参数
    SYS_PARA_PTR m_sysParamPtr;            // 系统参数
    TSystemAxesConfig m_axesCfgPtr;        // 轴配置参数 (来自GTC_InitShareMemParamPtr)
    TWORKCOORD_PARA_PTR m_coordParamPtr;   // 坐标系参数 (来自GTC_InitShareMemParamPtr)
    NC_CHANNEL_OUT_PTR m_ncChannelOutPtr;  // NC 通道输出区 (来自GTC_InitShareMemParamPtr)
    TPLC_PARA m_plcPara;                   // PLC 参数 (应为 TPLC_PARA，定义在 ParaDef.h 中)
    PLC_OUT_PTR m_plcOutPtr;               // PLC 输出参数 (来自GTC_InitShareMemParamPtr)
    HMI_OUT_PTR m_hmiOutPtr;               // HMI 输出区 (可选, 来自GTC_InitShareMemParamPtr)

    // SDK 参数结构体指针数组 (通过 GTC_InitShareMemParamPtr 初始化各项)
    std::array<AXIS_PARA_PTR, AXIS_NUM> m_axisParamPtr;                    // 各轴参数指针数组
    std::array<TAXIS_COMPENSATION_PTR, AXIS_NUM> m_axisScrewPitchCompPtr;  // 各轴螺距补偿参数指针数组

    static constexpr short MAX_CHANNEL_NUM = 4;  // 根据预期的最大通道数定义 (例如4通道)
    std::array<MOTION_PARA_PTR, MAX_CHANNEL_NUM> m_motionParamPtr;               // 运动参数 (每通道)
    std::array<GCODE_PARA_PTR, MAX_CHANNEL_NUM> m_gcodeParamPtr;                 // G 代码参数 (每通道)
    std::array<TG5_1_PARA_PTR, MAX_CHANNEL_NUM> m_tg51ParamPtr;                  // G5.1 参数 (每通道)
    std::array<TTOOLS_PARA_PTR, MAX_CHANNEL_NUM> m_toolsParamPtr;                // 刀具参数 (每通道)
    std::array<TMACRO_PUBLIC_PARA_PTR, MAX_CHANNEL_NUM> m_macroPublicParamPtr;   // 宏公共参数 (每通道)
    std::array<MACRO_SETTING_PARA_PTR, MAX_CHANNEL_NUM> m_macroSettingParamPtr;  // 宏设置参数 (每通道)
    std::array<NC_CHANNEL_OUT_PTR, MAX_CHANNEL_NUM> m_ncChannelOutPtrArr;        // NC 通道输出参数 (每通道)

    // 初始化配置设置器
    void initConfigurationSetters();

    // 配置文件管理私有方法
    ErrorCode loadMachineParameterFromFile();
    ErrorCode saveMachineParameterToFile();
    ErrorCode loadConfigurationFromFile(std::vector<ConfigCategory>& rootCategories);
    ErrorCode saveConfigurationToFile(const std::vector<ConfigCategory>& rootCategories);
    std::string getConfigurationFilePath() const;

    // 刀具数据文件管理私有方法
    ErrorCode loadToolDataFromFile();
    ErrorCode saveToolDataToFile();
    std::string getToolDataFilePath() const;
    std::string getDefaultToolsFilePath() const;
    ErrorCode loadDefaultToolsFromConfigFile();

    // 不获取锁的私有版本（用于已持有锁的方法调用）
    ErrorCode loadToolDataFromFileNoLock();
    ErrorCode saveToolDataToFileNoLock();
    ErrorCode getToolParametersNoLock(int toolNumber, ToolInfo& toolInfo);

    // === 内部辅助方法 ===
    std::string generateUuid();                               // 生成新的 UUID
    void updateToolNumberIndex(const ToolInfo& toolInfo);     // 更新刀号索引
    void removeFromToolNumberIndex(const std::string& uuid);  // 从刀号索引中移除
    void createMinimalDefaultTools();                         // 创建简单默认刀具

    // ------------------------------------------------------------
};
