cmake_minimum_required(VERSION 3.16)
set(CMAKE_SKIP_BUILD_RPATH FALSE) # 确保为构建树生成 RPATH

# HardwareLib 项目，不直接指定project，因为它由主项目通过 add_subdirectory 调用

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_INCLUDE_CURRENT_DIR ON) # 允许 #include "HardwareFactory.h"

# 设置 Qt 路径
if(UNIX AND NOT APPLE)
    set(Qt5_DIR "/opt/Qt5.15.1")
endif()

# 允许手动指定Qt5目录 (如: -DQt5_DIR=/path/to/qt/lib/cmake/Qt5)
if(NOT DEFINED Qt5_DIR AND DEFINED QT_DIR)
    set(Qt5_DIR ${QT_DIR}/lib/cmake/Qt5)
endif()
# 查找所需的 Qt 包
find_package(Qt5 REQUIRED COMPONENTS Core Gui Widgets)

# 定义平台相关的宏
if(WIN32)
    add_definitions(-DWIN32_PLATFORM)
elseif(APPLE)
    add_definitions(-DMACOS_PLATFORM)
elseif(UNIX AND NOT APPLE)
    add_definitions(-DLINUX_PLATFORM)
endif()

# --- 源文件列表 ---
set(FACTORY_SOURCES
    HardwareFactory.cpp
    HardwareFactory.h
)

# --- 根据平台选择实现 ---
if(UNIX AND NOT APPLE)
    message(STATUS "HardwareLib: 编译 Googol 实现（Linux）")

    find_package(Python3 COMPONENTS Interpreter REQUIRED)
    if(Python3_FOUND)
        message(STATUS "Python 3 解释器已找到: ${Python3_EXECUTABLE}")
    else()
        message(WARNING "未能找到 Python 3 解释器。自动配置代码生成将无法进行。")
    endif()

    set(GOOGOL_CONFIG_AUTO_GENERATED_CPP_STEM ${CMAKE_CURRENT_BINARY_DIR}/googol/GoogolCncInterface_SysConfigAuto)
    set(GOOGOL_CONFIG_AUTO_GENERATED_CPP_FILE ${GOOGOL_CONFIG_AUTO_GENERATED_CPP_STEM}.cpp)
    set(GOOGOL_GENERATED_JSON_FILE ${CMAKE_CURRENT_BINARY_DIR}/googol/googol_config_auto.json)
    set(GOOGOL_PARAMS_EXCEL_FILE ${CMAKE_SOURCE_DIR}/configs/googol_parameters.xlsx)
    set(PARAMS_PARSER_SCRIPT_FILE ${CMAKE_SOURCE_DIR}/tools/params_parser.py)

    # 自定义命令，用于从 Excel 生成 C++ 和 JSON 配置文件
    add_custom_command(
        OUTPUT ${GOOGOL_CONFIG_AUTO_GENERATED_CPP_FILE} ${GOOGOL_GENERATED_JSON_FILE}
        COMMAND ${Python3_EXECUTABLE} ${PARAMS_PARSER_SCRIPT_FILE}
                ${GOOGOL_PARAMS_EXCEL_FILE}
                --output_cpp_stem "${GOOGOL_CONFIG_AUTO_GENERATED_CPP_STEM}"
                --output_json "${GOOGOL_GENERATED_JSON_FILE}"
                --cpp_class_name "GoogolCncInterface"
                --quiet # 静默模式，减少构建过程中的输出
        DEPENDS ${GOOGOL_PARAMS_EXCEL_FILE} ${PARAMS_PARSER_SCRIPT_FILE}
        COMMENT "为 GoogolCncInterface 生成自动配置代码 C++ 和 JSON (${GOOGOL_CONFIG_AUTO_GENERATED_CPP_FILE}, ${GOOGOL_GENERATED_JSON_FILE})"
        WORKING_DIRECTORY ${CMAKE_SOURCE_DIR} # 设置脚本执行的工作目录为项目根目录
        VERBATIM # 确保命令中的参数按原样传递
    )

    set(HARDWARE_IMPL_SOURCES
        ${GOOGOL_CONFIG_AUTO_GENERATED_CPP_FILE} # 添加自动生成的配置文件
        googol/GoogolCncInterface.cpp
        googol/GoogolCncInterface.h
        googol/GoogolCncInterface_Alarms.cpp
        googol/GoogolCncInterface_Config.cpp
        googol/GoogolCncInterface_Coords.cpp
        googol/GoogolCncInterface_FeedsSpindle.cpp
        googol/GoogolCncInterface_Homing.cpp
        googol/GoogolCncInterface_Manual.cpp
        googol/GoogolCncInterface_Program.cpp
        googol/GoogolCncInterface_RuntimeVars.cpp
        googol/GoogolCncInterface_Status.cpp
        googol/GoogolCncInterface_Tools.cpp
        googol/GoogolCncInterface_PlcKey.cpp
        googol/GoogolCncInterface_DefaultParams.cpp
        googol/GoogolCncInterface_PitchCompensation.cpp
        googol/CncWorkerThread.cpp
        googol/CncWorkerThread.h
        googol/CompilerWorkerThread.cpp
        googol/CompilerWorkerThread.h
        googol/GoogolCncInterface_func.cpp
        googol/GoogolPlcInterface.cpp
        googol/GoogolPlcInterface.h
        googol/tools/GoogolToolManager.cpp
        googol/tools/GoogolToolManager.h
        googol/plc/GoogolPlcSdk.cpp
        googol/plc/GoogolPlcMonitor.cpp
        googol/plc/GoogolPlcData.cpp
        googol/plc/GoogolPlcCore.cpp
        googol/plc/GoogolPlcConfig.cpp
        googol/plc/GoogolPlcImpl.h
        googol/plc/GoogolPlcImpl.cpp
        googol/plc/GoogolPlcPrivate.h
    )

    set(HARDWARE_IMPL_INCLUDE_DIRS
        ${CMAKE_CURRENT_SOURCE_DIR}/googol # 允许 #include "GoogleCncInterface.h"
        ${CMAKE_SOURCE_DIR}/3rd/googol/include
        ${CMAKE_SOURCE_DIR}/3rd/googol/IO       # IO 头文件 (如果存在)
        ${CMAKE_SOURCE_DIR}/3rd/googol/PLC       # PLC 头文件 (如果存在)
        ${CMAKE_SOURCE_DIR}/3rd/googol/CardLib/linux64 # CardLib 头文件 (如果存在)
        ${CMAKE_SOURCE_DIR}/3rd/spdlog-1.15.3/include
    )

    # Googol 库的路径变量
    set(GOOGOL_LIB_DIR ${CMAKE_SOURCE_DIR}/3rd/googol/lib)
    set(GOOGOL_IO_DIR ${CMAKE_SOURCE_DIR}/3rd/googol/IO)
    set(GOOGOL_PLC_DIR ${CMAKE_SOURCE_DIR}/3rd/googol/PLC)
    set(GOOGOL_CARDLIB_DIR ${CMAKE_SOURCE_DIR}/3rd/googol/CardLib/linux64)

    # 使用完整路径指定Googol库
    set(GOOGOL_LINK_LIBS
        ${GOOGOL_LIB_DIR}/libGTS_NC.so
        ${GOOGOL_LIB_DIR}/libGTS_PLC.so
        ${GOOGOL_LIB_DIR}/libCncCodeCompile.so
        ${GOOGOL_LIB_DIR}/libCncParamRWDll.so
        ${GOOGOL_LIB_DIR}/libCncGlobalParam.so
        ${GOOGOL_LIB_DIR}/libWarningDll.so
        ${GOOGOL_LIB_DIR}/libControlCard.so
        ${GOOGOL_LIB_DIR}/libGTC_GetMotorPos.so
        ${GOOGOL_LIB_DIR}/libDllGlobalFunction.so
        ${GOOGOL_LIB_DIR}/libPathOptimization.so
        ${GOOGOL_LIB_DIR}/libReadNcFileCom.so
        ${GOOGOL_LIB_DIR}/libHmiWatchData.so
        ${GOOGOL_LIB_DIR}/libSocketDll.so
        ${GOOGOL_LIB_DIR}/libDllOpenUserCom.so
        ${GOOGOL_LIB_DIR}/libglobalData.so
        ${GOOGOL_LIB_DIR}/libPLCUser.so
        ${GOOGOL_IO_DIR}/libIOUI.so
        ${GOOGOL_PLC_DIR}/libPlcUI.so
    )

    # 为 Googol 实现添加特定的安装规则
    install(FILES ${GOOGOL_LINK_LIBS}
            DESTINATION lib
            COMPONENT RuntimeLibraries)

    # 特殊处理 libgxn_x64.so，将其也安装到可执行文件通常所在的目录 (CMAKE_INSTALL_BINDIR)
    # 以确保主程序 LatheCNC 能够直接找到此库
    set(LIBGXN_SO_FILE_PATH "${GOOGOL_CARDLIB_DIR}/libgxn_x64.so")
    if(EXISTS "${LIBGXN_SO_FILE_PATH}")
        install(FILES "${LIBGXN_SO_FILE_PATH}"
                DESTINATION .
                COMPONENT RuntimeLibraries
                RENAME "libgxn_x64.so") # 确保目标文件名正确
        message(STATUS "已添加安装规则，将 ${LIBGXN_SO_FILE_PATH} 复制到 ${CMAKE_INSTALL_PREFIX}/libgxn_x64.so")
    else()
        message(WARNING "特殊库文件 ${LIBGXN_SO_FILE_PATH} 未找到，无法为其添加安装规则到合适目录。")
    endif()

    # 安装后修改第三方库的RPATH
    find_program(PATCHELF_EXECUTABLE patchelf)
    if(PATCHELF_EXECUTABLE)
        foreach(GOOGOL_LIB_ITEM ${GOOGOL_LINK_LIBS})
            get_filename_component(LIB_NAME ${GOOGOL_LIB_ITEM} NAME)
            install(CODE "
                # 临时安装目录的正确路径是 $ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/LIB_NAME
                set(LIB_STAGED_PATH \"\$ENV{DESTDIR}\${CMAKE_INSTALL_PREFIX}/lib/${LIB_NAME}\")
                
                message(STATUS \"CPack: 正在修复 ${LIB_NAME} 的 RPATH 路径为 '\${LIB_STAGED_PATH}'\")

                execute_process(
                    COMMAND ${PATCHELF_EXECUTABLE} --force-rpath --set-rpath \"\\\$ORIGIN\" \"\${LIB_STAGED_PATH}\"
                    RESULT_VARIABLE patchelf_result
                    OUTPUT_VARIABLE patchelf_output
                    ERROR_VARIABLE patchelf_error_output
                )
                if(NOT patchelf_result EQUAL 0)
                    message(WARNING \"CPack: 修复 ${LIB_NAME} 的 RPATH 路径失败. 路径: '\${LIB_STAGED_PATH}'. 结果: \${patchelf_result}. 标准输出: '\${patchelf_output}'. 标准错误: '\${patchelf_error_output}'.\")
                else()
                    message(STATUS \"CPack: 成功修复 ${LIB_NAME} 的 RPATH 路径为 '\${LIB_STAGED_PATH}'\")
                endif()
            ")
        endforeach()
    else()
        message(WARNING "patchelf 命令未找到。将跳过修复第三方库的RPATH。可能会导致运行时链接错误。请安装 patchelf。")
    endif()

else()
    message(STATUS "HardwareLib: 编译 Mock 实现")

    find_package(Python3 COMPONENTS Interpreter REQUIRED)
    if(Python3_FOUND)
        message(STATUS "Python 3 解释器已找到: ${Python3_EXECUTABLE}")
    else()
        message(WARNING "未能找到 Python 3 解释器。自动配置代码生成将无法进行。")
    endif()

    # 为 Mock 实现设置自动代码生成
    set(MOCK_CONFIG_AUTO_GENERATED_CPP_STEM ${CMAKE_CURRENT_BINARY_DIR}/mock/MockCncInterface_SysConfigAuto)
    set(MOCK_CONFIG_AUTO_GENERATED_CPP_FILE ${MOCK_CONFIG_AUTO_GENERATED_CPP_STEM}.cpp)
    set(MOCK_GENERATED_JSON_FILE ${CMAKE_CURRENT_BINARY_DIR}/mock/mock_config_auto.json)
    set(MOCK_PARAMS_EXCEL_FILE ${CMAKE_SOURCE_DIR}/configs/googol_parameters.xlsx) # Mock 使用 googol 的 Excel 文件名
    set(PARAMS_PARSER_SCRIPT_FILE ${CMAKE_SOURCE_DIR}/tools/params_parser.py)

    add_custom_command(
        OUTPUT ${MOCK_CONFIG_AUTO_GENERATED_CPP_FILE} ${MOCK_GENERATED_JSON_FILE}
        COMMAND ${Python3_EXECUTABLE} ${PARAMS_PARSER_SCRIPT_FILE}
                ${MOCK_PARAMS_EXCEL_FILE}
                --output_cpp_stem "${MOCK_CONFIG_AUTO_GENERATED_CPP_STEM}"
                --output_json "${MOCK_GENERATED_JSON_FILE}"
                --cpp_class_name "MockCncInterface" # Mock 实现的类名
                --quiet
        DEPENDS ${MOCK_PARAMS_EXCEL_FILE} ${PARAMS_PARSER_SCRIPT_FILE}
        COMMENT "为 MockCncInterface 生成自动配置代码 C++ 和 JSON (${MOCK_CONFIG_AUTO_GENERATED_CPP_FILE}, ${MOCK_GENERATED_JSON_FILE})"
        WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
        VERBATIM
    )

    # Mock 实现的源文件
    set(HARDWARE_IMPL_SOURCES
        ${MOCK_CONFIG_AUTO_GENERATED_CPP_FILE} # 添加自动生成的配置文件
        mock/MockCncInterface.cpp
        mock/MockCncInterface_AlarmHoming.cpp
        mock/MockCncInterface_Config.cpp
        mock/MockCncInterface_FeedSpindle.cpp
        mock/MockCncInterface_Manual.cpp
        mock/MockCncInterface_Position.cpp
        mock/MockCncInterface_Program.cpp
        mock/MockCncInterface_StateMode.cpp
        mock/MockCncInterface_Tool.cpp
        mock/MockCncInterface.h
        mock/MockPlcInterface.cpp
        mock/MockPlcInterface.h
    )
    set(HARDWARE_IMPL_INCLUDE_DIRS
        ${CMAKE_CURRENT_SOURCE_DIR}/mock # 允许 #include "MockCncInterface.h"
    )
    # Mock 实现通常没有额外的外部库依赖
    set(GOOGOL_LINK_LIBS "")
endif()

# --- 创建 HardwareLib 共享库 ---
add_library(HardwareLib SHARED ${FACTORY_SOURCES} ${HARDWARE_IMPL_SOURCES})

# --- 包含目录 ---
target_include_directories(HardwareLib PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR} # 允许外部 #include "HardwareFactory.h"
)
target_include_directories(HardwareLib PRIVATE
    ${HARDWARE_IMPL_INCLUDE_DIRS}
    ${CMAKE_SOURCE_DIR}/src/public # ICncInterface.h 接口在主项目的 src/public
    ${CMAKE_SOURCE_DIR}/3rd/googol/include # for ParDef.h test
)

# --- 链接库 ---
target_link_libraries(HardwareLib PUBLIC 
    nlohmann_json::nlohmann_json
)
if((UNIX AND NOT APPLE) AND GOOGOL_LINK_LIBS)
    target_link_libraries(HardwareLib PUBLIC 
        ${GOOGOL_LINK_LIBS}
        Qt5::Core
        Qt5::Gui
        Qt5::Widgets
    )
endif()

# --- RPATH 设置 ---
# 当 HardwareLib 安装后，在其自身目录中查找依赖项
set_target_properties(HardwareLib PROPERTIES
    INSTALL_RPATH "$ORIGIN"
    SKIP_BUILD_RPATH FALSE
    BUILD_WITH_INSTALL_RPATH TRUE
)

# 在构建树中，也帮助 HardwareLib 找到其依赖项
if(UNIX AND NOT APPLE)
    set_target_properties(HardwareLib PROPERTIES
        BUILD_RPATH "$ORIGIN:${GOOGOL_LIB_DIR}:${GOOGOL_IO_DIR}:${GOOGOL_PLC_DIR}:${GOOGOL_CARDLIB_DIR}"
    )

    # --- 构建后自定义命令：将 Googol 依赖库复制到 HardwareLib 构建目录旁并修改其RPATH (用于本地运行) ---
    if(GOOGOL_LINK_LIBS)
        # 首先检查 patchelf 是否可用
        find_program(PATCHELF_EXECUTABLE patchelf)
        if(NOT PATCHELF_EXECUTABLE)
            message(WARNING "patchelf 命令未找到。将跳过修复第三方库的RPATH。可能会导致运行时链接错误。请安装 patchelf。")
        endif()

        foreach(GOOGOL_LIB_ITEM ${GOOGOL_LINK_LIBS})
            get_filename_component(LIB_NAME ${GOOGOL_LIB_ITEM} NAME)
            # $<TARGET_FILE_DIR:HardwareLib> 会在生成阶段被解析为实际路径
            set(COPIED_LIB_PATH "$<TARGET_FILE_DIR:HardwareLib>/${LIB_NAME}")

            add_custom_command(TARGET HardwareLib POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy_if_different
                        "${GOOGOL_LIB_ITEM}" # 源文件
                        "${COPIED_LIB_PATH}" # 目标文件 (注意：这里COPIED_LIB_PATH是字符串，CMake生成时会处理$<...>
                COMMENT "复制: ${LIB_NAME} 到 $<TARGET_FILE_DIR:HardwareLib>/"
                DEPENDS "${GOOGOL_LIB_ITEM}"
                VERBATIM
            )

            if(PATCHELF_EXECUTABLE) # 仅当 patchelf 可用时才执行
                # 注意：COPIED_LIB_PATH 在这里作为 add_custom_command 的参数，
                # 它内部的 $<TARGET_FILE_DIR:HardwareLib> 会在构建生成阶段被正确解析。
                add_custom_command(TARGET HardwareLib POST_BUILD
                    COMMAND ${PATCHELF_EXECUTABLE} --force-rpath --set-rpath "\$ORIGIN" "${COPIED_LIB_PATH}"
                    COMMENT "修复RPATH: ${LIB_NAME} in $<TARGET_FILE_DIR:HardwareLib>/"
                    VERBATIM
                )
            endif()
        endforeach()
    endif()
endif()

# --- 安装规则 ---
# 安装 HardwareLib 动态库本身
install(TARGETS HardwareLib 
    LIBRARY DESTINATION lib COMPONENT RuntimeLibraries
    ARCHIVE DESTINATION lib COMPONENT Development
    RUNTIME DESTINATION bin COMPONENT RuntimeExecutables # Windows DLLs might go to bin
)

message(STATUS "HardwareLib 配置完成") 

# 打开测试
option(BUILD_TESTING "Build tests" ON)

# --- 测试配置 ---
if(BUILD_TESTING)
    enable_testing()

    # --- 使用本地的 Google Test 源码 --- 
    # 确保 GTest 源码存在于 3rd/googletest-1.17.0
    set(GTEST_SOURCE_DIR ${CMAKE_SOURCE_DIR}/3rd/googletest-1.17.0)
    set(GTEST_BINARY_DIR ${CMAKE_BINARY_DIR}/googletest-build) # 指定构建目录

    if(EXISTS ${GTEST_SOURCE_DIR}/CMakeLists.txt)
        message(STATUS "使用本地 Google Test 源码: ${GTEST_SOURCE_DIR}")
        # 将 Google Test 添加为一个子项目，这将定义 gtest, gmock, gtest_main 目标
        # EXCLUDE_FROM_ALL 避免在默认构建中构建 gtest 目标，除非有目标链接到它
        add_subdirectory(${GTEST_SOURCE_DIR} ${GTEST_BINARY_DIR} EXCLUDE_FROM_ALL)
    else()
        message(FATAL_ERROR "Google Test 源码目录不存在: ${GTEST_SOURCE_DIR}")
    endif()

    set(CNC_TEST_SOURCES
        tests/test_cnc_interface.cpp
        tests/test_plc_interface.cpp
    )

    add_executable(cnc_interface_tests ${CNC_TEST_SOURCES})

    set_target_properties(cnc_interface_tests PROPERTIES
        CXX_STANDARD 17
        CXX_STANDARD_REQUIRED ON
    )
    target_compile_options(cnc_interface_tests PRIVATE "-frtti")

    target_include_directories(cnc_interface_tests PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/tests
        ${CMAKE_SOURCE_DIR}/src/public
    )

    # 链接到本地构建的 GTest 目标 (由 add_subdirectory 定义)
    target_link_libraries(cnc_interface_tests PRIVATE gtest gmock gtest_main HardwareLib)

    # 禁用自动测试发现，避免构建时运行测试导致找不到库文件的问题
    # 用户可以手动运行测试脚本 run_tests.sh
    # 注释掉 gtest_discover_tests，防止CMake尝试在构建过程中运行测试
    # include(GoogleTest)
    # gtest_discover_tests(cnc_interface_tests)

    message(STATUS "HardwareLib tests 配置完成 (使用本地 GTest: ${GTEST_SOURCE_DIR})")
    
    # 添加 Googol PLC 测试
    # if(UNIX AND NOT APPLE)
    #     add_subdirectory(googol/tests)
    # endif()
endif() 
