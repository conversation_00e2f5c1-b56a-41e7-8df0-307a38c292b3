#include <QDateTime>
#include <QDir>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonValue>
#include <chrono>
#include <cstring>
#include <map>
#include <random>
#include <string>

#include "CncGlobalParamDll.h"
#include "GoogolCncInterface.h"
#include "ParaDef.h"
#include "tools/GoogolToolManager.h"

// --- 刀具管理相关方法 ---

ErrorCode GoogolCncInterface::getCurrentToolInfo(int channelId, ToolInfo& toolInfo) {
    return m_toolManager->getCurrentToolInfo(channelId, toolInfo);
}

ErrorCode GoogolCncInterface::getToolParameters(const std::string& uuid, ToolInfo& toolInfo) {
    return m_toolManager->getToolParameters(uuid, toolInfo);
}

ErrorCode GoogolCncInterface::setToolParameters(const ToolInfo& toolInfo) {
    return m_toolManager->setToolParameters(toolInfo);
}

ErrorCode GoogolCncInterface::getToolInfoInPocket(int toolChangerId, int pocketNumber, ToolInfo& toolInfo) {
    return m_toolManager->getToolInfoInPocket(toolChangerId, pocketNumber, toolInfo);
}

ErrorCode GoogolCncInterface::loadToolIntoPocket(int toolChangerId, int pocketNumber, int toolNumber) {
    return m_toolManager->loadToolIntoPocket(toolChangerId, pocketNumber, toolNumber);
}

ErrorCode GoogolCncInterface::unloadToolFromPocket(int toolChangerId, int pocketNumber, int& unloadedToolNumber) {
    return m_toolManager->unloadToolFromPocket(toolChangerId, pocketNumber, unloadedToolNumber);
}

ErrorCode GoogolCncInterface::exchangeToolsInPockets(int toolChangerId, int pocketNumber1, int pocketNumber2) {
    return m_toolManager->exchangeToolsInPockets(toolChangerId, pocketNumber1, pocketNumber2);
}

ErrorCode GoogolCncInterface::moveToolFromPocketToSpindle(int channelId, int toolChangerId, int pocketNumber,
                                                          int spindleIndex) {
    return m_toolManager->moveToolFromPocketToSpindle(channelId, toolChangerId, pocketNumber, spindleIndex);
}

ErrorCode GoogolCncInterface::moveToolFromSpindleToPocket(int channelId, int spindleIndex, int toolChangerId,
                                                          int pocketNumber, int* movedToPocketToolNumber) {
    return m_toolManager->moveToolFromSpindleToPocket(channelId, spindleIndex, toolChangerId, pocketNumber,
                                                      movedToPocketToolNumber);
}

ErrorCode GoogolCncInterface::getMagazineStatus(int toolChangerId, std::vector<PocketStatus>& pocketStatuses) {
    return m_toolManager->getMagazineStatus(toolChangerId, pocketStatuses);
}

ErrorCode GoogolCncInterface::getAllToolParameters(std::map<std::string, ToolInfo>& allToolsInfo) {
    return m_toolManager->getAllToolParameters(allToolsInfo);
}

ErrorCode GoogolCncInterface::getToolsByMagazine(int toolChangerId, std::vector<ToolInfo>& toolsInMagazine) {
    return m_toolManager->getToolsByMagazine(toolChangerId, toolsInMagazine);
}

ErrorCode GoogolCncInterface::deleteTool(const std::string& uuid) { return m_toolManager->deleteTool(uuid); }
