#pragma once

#include <QCoreApplication>
#include <QFile>
#include <QIODevice>
#include <QMutex>
#include <QString>
#include <QStringList>
#include <QVector>

// Forward declarations for Qt JSON classes
class QJsonObject;
#include <array>
#include <cwchar>  // 用于 wcslen, wcsncpy_s 等宽字符函数
#include <map>
#include <mutex>
#include <optional>
#include <shared_mutex>
#include <stdexcept>
#include <string>
#include <unordered_map>
#include <vector>

#include "ICncEventSystem.h"  // 新增: 包含事件系统定义
#include "ICncInterface.h"    // 包含上层目录的抽象接口

// 首先包含 SDK 的 Linux 全局定义，用于 DWORD, WCHAR 等类型
#include "GlobalDef_Linux.h"

// 平台相关的 API 宏覆盖 (Linux)
// 必须在 GlobalDef_Linux.h 之后，且在其他使用这些宏的 SDK 头文件之前
#if !defined(_WIN32)
// 确保 __declspec 在 Linux 上是空操作，以防 GlobalDef_Linux.h 未处理或 SDK 头文件无保护地重定义它
#if !defined(__declspec)  // 再次检查，因为 GlobalDef_Linux.h 可能未定义它
#define __declspec(x)     /* 在 Linux 上将 __declspec 定义为空操作 */
#endif
#endif  // !defined(_WIN32)

// SDK 头文件，应在此处包含以确保宏定义优先
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <nlohmann/json.hpp>

#include "CncCodeCompileEx.h"
#include "CncGlobalParamDll.h"     // 包含获取共享内存指针的函数
#include "CncWorkerThread.h"       // CNC 后台工作线程
#include "CompilerWorkerThread.h"  // G代码编译器后台工作线程
#include "DllGlobalFunction.h"
#include "DllVersion.h"
#include "NCDll.h"       // 包含宏变量读写等函数
#include "ParaDef.h"     // 包含 Googol 参数定义
#include "WarningDll.h"  // 包含报警处理函数
#include "globalPlcParaDef.h"

// 辅助函数声明
std::string WcharToStdString(const wchar_t* wstr);  // 将 wchar_t* 转换为 std::string

class ICncInterface;
class GoogolToolManager;

enum class MachineType : int32_t {
    LacheXZS = 0,           ///< 3轴车床XZS
    GeneralMachineXZS = 1,  ///< 通用3轴车床XZS
};

typedef struct MachineParameter {
    MachineParameter() {
        m_type = static_cast<int32_t>(MachineType::LacheXZS);
        memset(m_name, 0, sizeof(m_name));
        memset(m_id, 0, sizeof(m_id));
        memset(m_systemName, 0, sizeof(m_systemName));
        memset(m_sysVersion, 0, sizeof(m_sysVersion));
    }

    int32_t m_type;  // 机器类型:0： 3轴车床XZS
    char m_name[32];
    char m_id[32];
    char m_systemName[32];
    char m_sysVersion[32];
} MachineParameter;

// Googol CNC 接口的实现
class GoogolCncInterface : public ICncInterface {
   public:
    GoogolCncInterface();
    ~GoogolCncInterface() override;

    // --- 生命周期管理 ---
    ErrorCode initialize(const CncInitializationParams& params) override;
    ErrorCode shutdown() override;
    bool isInitialized() const override;

    // --- 配置参数读取 ---
    ErrorCode getSystemConfig(SystemConfig& config) override;  // 未实现

    // --- 系统状态与模式 ---
    ErrorCode getSystemState(CncSystemState& state) override;
    ErrorCode getOperatingMode(int channelId, OperatingMode& mode) override;
    ErrorCode setOperatingMode(int channelId, OperatingMode mode) override;  // 云川  西门子需要切换模式
    ErrorCode getLastError(std::string& errorMsg) override;                  // 未实现
    ErrorCode getActiveGFunctionList(int channelId, std::vector<ActiveGFunction>& functions) override;

    // --- 坐标系与位置 --- (坐标单位由 SystemConfig.linearUnit/angularUnit 决定)
    ErrorCode getWorkPosition(int channelId, PointXD& position, bool isReadFromCNC = false) override;
    ErrorCode getMachinePosition(int channelId, PointXD& position) override;
    ErrorCode getRelativePosition(int channelId, PointXD& position) override;
    ErrorCode getRemainingPosition(int channelId, PointXD& position) override;
    ErrorCode getAxisWorkPosition(int channelId, int axisIndex, double& position) override;
    ErrorCode getAxisMachinePosition(int channelId, int axisIndex, double& position) override;
    ErrorCode getAxisTotalOffset(int channelId, int axisIndex, double& totalOffset) override;
    ErrorCode getCurrentWorkOffsetName(int channelId, std::string& name) override;
    ErrorCode getCurrentWorkOffsetValue(int channelId, PointXD& offset) override;  // 获取当前使用的工件坐标系的值？
    ErrorCode getWorkOffsetValue(int channelId, const std::string& workOffsetName,
                                 PointXD& offset) override;  // 获取指定工件坐标系的值
    // 设置工作坐标系G54。。。的接口；  是否缺少工件坐标系外部偏置接口
    ErrorCode setWorkOffsetValue(int channelId, const std::string& workOffsetName, const PointXD& offset) override;
    // 外部偏置

    // --- 进给与主轴 --- (均针对指定的 channelId)
    ErrorCode getCommandFeedrate(int channelId, double& feedrate) override;
    ErrorCode getActualFeedrate(int channelId, double& feedrate) override;
    ErrorCode getFeedOverride(int channelId, double& overridePercent) override;
    ErrorCode setFeedOverride(int channelId, double overridePercent) override;
    ErrorCode getRapidOverride(int channelId,
                               double& overridePercent) override;  // 获取快移倍率 需要在PLC中查找D变量，等待许子龙补充
    ErrorCode setRapidOverride(
        int channelId, double overridePercent) override;  // 云川保留         面板设置倍率 PLC中进行处理，等待许子龙补充
    ErrorCode getCommandSpindleSpeed(int channelId, int spindleIndex, double& speedRpm) override;
    ErrorCode getActualSpindleSpeed(int channelId, int spindleIndex, double& speedRpm) override;
    ErrorCode getSpindleOverride(int channelId, double& overridePercent) override;
    ErrorCode setSpindleOverride(int channelId, double overridePercent) override;
    ErrorCode isSpindleOn(int channelId, int spindleIndex, bool& isOn) override;

    // --- 刀具管理 ---
    ErrorCode getCurrentToolInfo(int channelId, ToolInfo& toolInfo) override;

    // --- 基于 UUID 的刀具管理接口 ---
    ErrorCode getToolParameters(const std::string& uuid, ToolInfo& toolInfo) override;
    ErrorCode setToolParameters(const ToolInfo& toolInfo) override;
    ErrorCode deleteTool(const std::string& uuid) override;
    ErrorCode getAllToolParameters(std::map<std::string, ToolInfo>& allToolsInfo) override;
    ErrorCode getToolsByMagazine(int toolChangerId, std::vector<ToolInfo>& toolsInMagazine) override;

    // --- 刀库管理 (高级接口) ---
    ErrorCode getToolInfoInPocket(int toolChangerId, int pocketNumber, ToolInfo& toolInfo) override;
    ErrorCode loadToolIntoPocket(int toolChangerId, int pocketNumber, int toolNumber) override;
    ErrorCode unloadToolFromPocket(int toolChangerId, int pocketNumber, int& unloadedToolNumber) override;
    ErrorCode exchangeToolsInPockets(int toolChangerId, int pocketNumber1, int pocketNumber2) override;
    ErrorCode moveToolFromPocketToSpindle(int channelId, int toolChangerId, int pocketNumber,
                                          int spindleIndex) override;
    ErrorCode moveToolFromSpindleToPocket(int channelId, int spindleIndex, int toolChangerId, int pocketNumber,
                                          int* movedToPocketToolNumber) override;
    ErrorCode getMagazineStatus(int toolChangerId, std::vector<PocketStatus>& pocketStatuses) override;

    // --- 程序执行 --- (均针对指定的 channelId)
    // 涉及通道的参数，需要根据变量划分区间来对应其通道参数，需要许子龙进行梳理
    ErrorCode loadProgram(int channelId, const std::string& filePath) override;
    ErrorCode compileProgram(int channelId, const std::string filePath, std::vector<GMotion>& gMotions) override;
    ErrorCode startProgram(int channelId) override;
    ErrorCode pauseProgram(int channelId) override;
    ErrorCode resumeProgram(int channelId) override;
    ErrorCode stopProgram(int channelId) override;
    ErrorCode resetProgram(int channelId) override;
    ErrorCode getProgramStatus(int channelId, ProgramStatus& status) override;  // 存在参数问题待解决，和刘工沟通
    ErrorCode executeMdi(int channelId, const std::string& mdiString) override;
    ErrorCode executeMacro(int channelId, const std::string& macro) override;
    ErrorCode setBlockSkip(int channelId, bool enable) override;
    ErrorCode setOptionalStop(int channelId, bool enable) override;
    ErrorCode setSingleStepMode(int channelId, SingleBlockModeType mode) override;
    ErrorCode setProgramTestMode(int channelId, bool enable) override;  // 未实现    不理解
    ErrorCode setDryRunMode(int channelId, bool enable) override;       // 未实现    空运行干切不支持
    ErrorCode setHandwheelMode(int channelId, bool enable) override;

    // --- 手动操作 --- (均针对指定的 channelId)
    ErrorCode startJog(int channelId, int axisIndex, double speed, double distance = 0.0) override;
    ErrorCode stopJog(int channelId, int axisIndex) override;
    ErrorCode setMpgControl(int channelId, int axisIndex, double incrementPerPulse, bool active) override;
    ErrorCode getMpgControlStatus(int channelId, int& controlledAxisIndex, double& currentIncrementPerPulse,
                                  bool& isAnyAxisMpgControlled) override;

    // --- 报警处理 ---
    ErrorCode getActiveAlarms(std::vector<AlarmInfo>& alarms) override;
    ErrorCode clearAlarms(int channelId = -1) override;

    // --- 回零操作 --- (均针对指定的 channelId)
    ErrorCode startHoming(int channelId, const std::vector<int>& axisIndices) override;
    ErrorCode isAxisHomed(int channelId, int axisIndex,
                          bool& isHomed) override;  // 未实现    需要PLC中的G变量，回零完成标志位，许子龙补充
    ErrorCode areAllAxesHomed(int channelId,
                              bool& allHomed) override;  // 未实现    需要PLC中的G变量，回零完成标志位，许子龙补充

    // --- 螺距补偿管理 ---
    ErrorCode getPitchCompensation(int channelId, int axisIndex, AxisPitchCompensation& compensation) override;
    ErrorCode setPitchCompensation(int channelId, int axisIndex, const AxisPitchCompensation& compensation) override;
    ErrorCode enablePitchCompensation(int channelId, int axisIndex, bool enable, bool isCyclicMode = false) override;
    ErrorCode getPitchCompensationStatus(int channelId, int axisIndex, bool& isEnabled, bool& isCyclicMode,
                                         double& currentCompensation) override;
    ErrorCode getAllPitchCompensations(int channelId, std::vector<AxisPitchCompensation>& allCompensations) override;
    ErrorCode setAllPitchCompensations(int channelId, const std::vector<AxisPitchCompensation>& compensations) override;

    // --- 参数与变量读写 ---
    ErrorCode getMacroVariable(int channelId, int index, double& value) override;
    ErrorCode setMacroVariable(int channelId, int index, double value) override;

    // --- 系统通用配置管理（上层应用通常不关心其具体意义，主要用于备份/恢复/诊断） ---
    ErrorCode getConfiguration(std::vector<ConfigCategory>& rootCategories) override;
    ErrorCode setConfiguration(const std::vector<ConfigCategory>& rootCategoriesToUpdate) override;  // 未实现
    ErrorCode getConfigurationImpl(std::vector<ConfigCategory>& rootCategories);                     // 自动生成
    ErrorCode setConfigurationImpl(const std::vector<ConfigCategory>& rootCategoriesToUpdate);       // 自动生成

    // --- 事件处理 ---
    ErrorCode registerEventListener(ICncEventListener* listener) override;    // 未实现
    ErrorCode unregisterEventListener(ICncEventListener* listener) override;  // 未实现

    // --- UI模态模式通知 ---
    ErrorCode enterModalMode(UiModalType modalType, const std::string& modalId = "") override;
    ErrorCode exitModalMode(UiModalType modalType, const std::string& modalId = "") override;

   private:
    friend class GoogolToolManager;

    // --- 成员变量 ---
    IAppLogger* m_logger = nullptr;      // 日志接口指针
    bool m_isInitialized = false;        // 接口是否已初始化标志
    bool m_isConfigInitialized = false;  // 配置是否已初始化标志
    bool m_isPowerOffing = false;        // 是否正在关机
    std::string m_writablePath;          // 可写路径
    std::string m_configPath;            // 配置文件路径
    mutable std::string m_lastError;     // 保存最后一次操作的错误信息
    SystemConfig m_systemConfig;         // 系统配置，包含刀库配置

    // 加载的轴、主轴和通道数量
    int m_loadedAxesCount = 0;
    int m_loadedSpindlesCount = 0;
    int m_loadedChannelsCount = 0;

    MachineParameter m_machineParameter;

    NC_OUT_PTR m_ncOutPtr;                // NC 输出:公共参数
    SYS_PARA_PTR m_sysParamPtr;           // 系统参数
    TSystemAxesConfig m_axesCfgPtr;       // 轴配置参数 (来自GTC_InitShareMemParamPtr)
    TWORKCOORD_PARA_PTR m_coordParamPtr;  // 坐标系参数 (来自GTC_InitShareMemParamPtr)

    TPLC_PARA m_plcPara;        // PLC 参数 (应为 TPLC_PARA，定义在 ParaDef.h 中)
    PLC_OUT_PTR m_plcOutPtr;    // PLC 输出参数 (来自GTC_InitShareMemParamPtr)
    HMI_OUT_PTR m_hmiOutPtr;    // HMI 输出区 (可选, 来自GTC_InitShareMemParamPtr)
    NC2PLC_PARA_F* m_plcFPara;  // PLC：F变量参数
    PLC2NC_PARA_G* m_plcGPara;  // PLC：G变量参数

    // SDK 参数结构体指针数组 (通过 GTC_InitShareMemParamPtr 初始化各项)
    std::array<AXIS_PARA_PTR, AXIS_NUM> m_axisParamPtr;                    // 各轴参数指针数组
    std::array<TAXIS_COMPENSATION_PTR, AXIS_NUM> m_axisScrewPitchCompPtr;  // 各轴螺距补偿参数指针数组

    static constexpr short MAX_CHANNEL_NUM = 4;  // 根据预期的最大通道数定义 (例如4通道)
    std::array<MOTION_PARA_PTR, MAX_CHANNEL_NUM> m_motionParamPtr;               // 运动参数 (每通道)
    std::array<GCODE_PARA_PTR, MAX_CHANNEL_NUM> m_gcodeParamPtr;                 // G 代码参数 (每通道)
    std::array<TG5_1_PARA_PTR, MAX_CHANNEL_NUM> m_tg51ParamPtr;                  // G5.1 参数 (每通道)
    std::array<TTOOLS_PARA_PTR, MAX_CHANNEL_NUM> m_toolsParamPtr;                // 刀具参数 (每通道)
    std::array<TMACRO_PUBLIC_PARA_PTR, MAX_CHANNEL_NUM> m_macroPublicParamPtr;   // 宏公共参数 (每通道)
    std::array<MACRO_SETTING_PARA_PTR, MAX_CHANNEL_NUM> m_macroSettingParamPtr;  // 宏设置参数 (每通道)
    std::array<NC_CHANNEL_OUT_PTR, MAX_CHANNEL_NUM> m_ncChannelOutPtrArr;        // NC 通道输出参数 (每通道)

    // 后台线程 (使用指针，延迟创建)
    CncWorkerThread* m_cncThread = nullptr;
    CompilerWorkerThread* m_compilerThread = nullptr;

    // 事件监听器
    std::vector<ICncEventListener*> m_eventListeners;
    std::mutex m_eventListenerMutex;  // 用于保护 m_eventListeners 的互斥锁

    // 定义的一些辅助变量和辅助函数  刘盼 2025.5.12
    TGWorkMode m_preWorkMode;  // 记录系统前一个工作模式

    QString m_sLoadNcFile;     // 系统Auto加载文件
    QString m_sLoadMDINcFile;  // 系统MDI加载文件
    QString m_sLoadMacroFile;  // 用户宏文件
    QMutex m_mutex;            // 线程锁

    mutable std::shared_mutex m_alarmMapMutex;  // 读写锁
    std::unordered_map<int32_t, AlarmInfo> m_alarmMap;
    std::unordered_map<PanelKeyType, PanelKeyAction> m_keyActionMap;

    std::thread m_eventThread;
    std::atomic<bool> m_eventStopFlag;
    std::atomic<bool> m_eventPausedFlag;                   // 用于模拟 isPaused()
    CncSystemState m_iSysStatus = CncSystemState::Idle;    // 记录系统状态
    CncSystemState m_preSysStatus = CncSystemState::Idle;  // 记录系统状态
    OperatingMode m_opMode = OperatingMode::Auto;

    std::array<CncSystemState, MAX_CHANNEL_NUM> m_currentSysStatusArr = {CncSystemState::Idle};  // 记录系统状态
    std::array<ProgramStatus, MAX_CHANNEL_NUM> m_chanProgramStatusArr;

    // --- 刀具管理数据成员 ---
    std::unique_ptr<GoogolToolManager> m_toolManager;  // 刀具管理器

    // --- 内部辅助方法 ---
    // 私有静态路径准备函数
    static std::string preparePath(const std::string& base, const std::string& leaf);

    // 初始化逻辑的私有辅助函数
    ErrorCode initSdkPaths(const std::string& configPath, std::string& outErrorCfgPath, std::string& outPlcPath,
                           std::string& outMacroPath);
    ErrorCode initDebugModuleInternal(const std::string& errorCfgFilePath);
    ErrorCode initAndLoadSharedMemoryInternal(const std::string& errorCfgFilePath);
    ErrorCode initNcInternal();
    ErrorCode initPlcInternal(const std::string& plcPath);
    ErrorCode initCompilerInternal(const std::string& macroPath);
    ErrorCode startWorkerThreadsInternal();
    ErrorCode initMDI();
    ErrorCode initMacroFile();
    ErrorCode initPlcKeyMap();

    ErrorCode initCncSystem(const std::string& errorCfgFilePath);
    ErrorCode initSystemConfig(SystemConfig& config);

    ErrorCode checkInitializedAndPointers();  // 检查接口是否已初始化及关键指针是否有效
    ErrorCode handleGoogolReturnCode(short googolRetCode,
                                     const std::string& operationContext);  // 处理 Googol SDK 返回码

    OperatingMode mapGoogolWorkModeToEnum(unsigned short googolMode);  // 将固高工作模式代码映射到 OperatingMode 枚举
    unsigned short mapEnumToGoogolWorkMode(OperatingMode mode);  // 将 OperatingMode 枚举映射到固高工作模式代码

    void notifyEventListeners(const CncEvent& event);  // 通知所有注册的监听器

    // --- 辅助方法 ---
    bool setPlcDataXBit(unsigned int iNo, unsigned int bit, unsigned short value);
    bool getPlcDataXBit(unsigned int iNo, unsigned int bit, unsigned short& value);
    bool setDPara(unsigned int iNo, unsigned int count, void* pValue);
    bool getDPara(unsigned int iNo, unsigned int count, void* pValue);
    bool getPlcDataX(unsigned int iNo, unsigned int count, unsigned short* pValue);
    bool setPlcDataX(unsigned int iNo, unsigned int count, unsigned short* pValue);
    bool getPlcDataY(unsigned int iNo, unsigned int count, unsigned short* pValue);
    bool setPlcDataY(unsigned int iNo, unsigned int count, unsigned short* pValue);
    bool getPlcDataYBit(unsigned int iNo, unsigned int bit, unsigned short& value);
    bool setPlcDataYBit(unsigned int iNo, unsigned int bit, unsigned short value);
    bool getFPara(unsigned int iNo, unsigned int count, unsigned short* pValue);
    bool getFBit(unsigned int iNo, unsigned int bit, unsigned short& value);
    bool getGPara(unsigned int iNo, unsigned int count, unsigned short* pValue);
    bool getGBit(unsigned int iNo, unsigned int bit, unsigned short& value);

    long GetAcitveChannel();  // 获取当前激活的通道
    bool Nc_LoadNcFile(QString strNcFileName, short iChannel, short compileType = WORK_COMPILE);  // 加载NC文件
    bool setAutoPlcMode();  // 设置为自动模式
    bool setRefMode();      // 设置为回零模式
    bool setJogMode();      // 设置为JOG模式
    bool setMpgMode();      // 设置为手轮模式
    bool setPichMode();     // 设置为寸动模式
    bool setMDIMode();      // 设置为DMI模式

    QVector<QStringList> getAlarm();  // 获取系统报警
    bool getWarningList();
    void filtrateAlarm();  // 筛选报警

    bool setMacroPublicVar(unsigned int iChannel, unsigned int iNo, unsigned int count, double* pValue);  // 设置宏变量
    bool getMacroPublicVar(unsigned int iChannel, unsigned int iNo, unsigned int count, double* pValue);  // 获取宏变量

    // 初始化配置设置器，在初始化时调用
    void initConfigurationSetters();

    void eventThreadLoop();
    void startEventThread();
    void stopEventThread();
    void waitEventThread();
    void pauseEventThread();
    void resumeEventThread();
    bool isEventThreadPaused() const;
    void initEventFlags();

    void checkAlarmOccurredEvent();
    void checkAlarmClearedEvent();
    void checkSystemStateEvent();
    void checkOperatingModeEvent();
    void checkConnectionStatusEvent();
    void checkProgramStatusChangedEvent();
    void checkMdiResultEvent();
    void checkFeedOverrideChangedEvent();
    void checkRapidOverrideChangedEvent();
    void checkSpindleOverrideChangedEvent();
    void checkToolInSpindleChangedEvent();
    void checkActiveWorkOffsetChangedEvent();
    void checkHomingProcessUpdateEvent();
    void checkAxisHomedStateChangedEvent();
    void checkPlcKeyEvent();
    void checkPowerOffEvent();
    void checkMsgAndLog();

    // --- 辅助方法 ---
    ErrorCode validateChannel(int channelId, const std::string& operation) const;  // 验证通道ID
    ErrorCode validateChannelAndSpindle(int channelId, int spindleIndex,
                                        const std::string& operation) const;  // 验证通道和主轴

    // --- JSON转换和文件操作方法 ---
    bool ensureDirectoryExists(const std::string& path);
    std::string extractDirectoryFromPath(const std::string& filepath);
    std::string escapeJsonString(const std::string& str);
    std::string configValueToJsonString(const ConfigValue& value);
    std::string configRangeToJsonString(const ConfigRange& range);
    std::string configNodeToJsonString(const ConfigNode& node);
    std::string configCategoryToJsonString(const ConfigCategory& category);
    std::string configCategoriesToJson(const std::vector<ConfigCategory>& rootCategories);
    QJsonArray configCategoriesToQJsonArray(const std::vector<ConfigCategory>& rootCategories);
    QJsonObject configCategoryToQJsonObject(const ConfigCategory& category);
    QJsonObject configNodeToQJsonObject(const ConfigNode& node);
    QJsonValue configValueToQJsonValue(const ConfigValue& value);
    QJsonObject configRangeToQJsonObject(const ConfigRange& range);
    std::string calculateChecksum(const std::string& data);
    bool saveConfigToFile(const std::string& filename, const std::string& jsonContent);
    bool loadConfigFromFile(const std::string& filename, std::string& jsonContent);
    bool parseJsonToConfigCategories(const std::string& jsonArrayStr, std::vector<ConfigCategory>& rootCategories);
    bool parseJsonAndValidateChecksum(const std::string& jsonContent, std::vector<ConfigCategory>& rootCategories);

    // JSON parsing helper functions
    bool parseConfigCategoryFromJson(const nlohmann::json& categoryObj, ConfigCategory& category);
    bool parseConfigNodeFromJson(const nlohmann::json& nodeObj, ConfigNode& node);
    ConfigValue parseConfigValueFromJson(const nlohmann::json& jsonValue);
    ConfigRange parseConfigRangeFromJson(const nlohmann::json& rangeObj);

    void printConfigCategory(const ConfigCategory& cat) const;

    // 新增：递归同步配置的辅助函数声明
    void updateConfigCategories(const std::vector<ConfigCategory>& from, std::vector<ConfigCategory>& to);

    // --- 螺距补偿相关辅助方法 ---
    ErrorCode convertGoogolToPitchCompensation(const COMPENSATION_PTR* googolComp, AxisPitchCompensation& compensation,
                                               int axisIndex);
    ErrorCode convertPitchCompensationToGoogol(const AxisPitchCompensation& compensation, COMPENSATION_PTR* googolComp);
    ErrorCode validateAxisIndex(int axisIndex, const std::string& operation) const;
    ErrorCode getPitchCompensationPointer(int axisIndex, TAXIS_COMPENSATION_PTR& compPtr);
    ErrorCode loadPitchCompensationFromFile(int axisIndex, TAXIS_COMPENSATION_PTR& compPtr);
    ErrorCode savePitchCompensationToFile(int axisIndex, TAXIS_COMPENSATION_PTR& compPtr);
    bool isPitchCompensationEnabled(int axisIndex);
    ErrorCode getCurrentPitchCompensationValue(int axisIndex, double& currentValue);

    ErrorCode initGoogolSystemParam();
    ErrorCode initGoogolAxisConfig();
    ErrorCode initGoogolMotionParam(int totalChannelCount);
    ErrorCode initGoogolAxisParam(int totalAxesCount);
    ErrorCode initGoogolGCodeParam(int totalChannelCount);
    ErrorCode initGoogolG5_1Param(int totalChannelCount);
    ErrorCode initGoogolToolParam(int totalChannelCount);
    ErrorCode initGoogolMacroSettingParam(int totalChannelCount);
    ErrorCode initGoogolMacroPublicParam(int totalChannelCount);
    ErrorCode initGoogolScrewPitchCompParam(int totalAxesCount);
    ErrorCode initGoogolWorkCoordParam(void);
    int setDefaultScrewPitchCompParam(int axisIndex);
    void setAxis0ScrewPitchCompData(COMPENSATION_PTR* compPtr);
    void setAxis1ScrewPitchCompData(COMPENSATION_PTR* compPtr);
    void setAxis2ScrewPitchCompData(COMPENSATION_PTR* compPtr);
    void setAxis3ScrewPitchCompData(COMPENSATION_PTR* compPtr);
    void setAxis4ScrewPitchCompData(COMPENSATION_PTR* compPtr);
    void setDefaultGCodeParam(int channelIndex);
    void setDefaultG5_1Param(int channelIndex);
    void setDefaultToolParam(int channelIndex);
    void setDefaultMacroSettingParam(int channelIndex);
    void setDefaultMacroPublicParam(int channelIndex);
    void setDefaultAxisParam(int i);

    // 轴特定参数设置函数
    void setAxis1SpecificParams(int i);
    void setAxis2SpecificParams(int i);
    void setAxis3SpecificParams(int i);

    MachineType getMachineType();

    // 调试函数
    void debugMacroBindings();
};