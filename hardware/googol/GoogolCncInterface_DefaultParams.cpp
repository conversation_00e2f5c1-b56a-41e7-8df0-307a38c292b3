#include "BaseCtrl.h"  // 包含底层状态、点动、回零功能
#include "CncCodeCompileEx.h"
#include "CncGlobalParamDll.h"     // 用于 GTC_... 访问共享内存的函数
#include "CncParamRWDll.h"         // 用于 GTCR_... 访问参数的函数
#include "CncWorkerThread.h"       // 添加CNC工作线程头文件
#include "CompilerWorkerThread.h"  // 添加编译线程头文件
#include "ErrorCode.h"
#include "GoogolCncInterface.h"
#include "IAppLogger.h"
#include "ICncEventSystem.h"
#include "NCDll.h"
#include "PLCDLL.h"
#include "ParaDef.h"     // Googol 核心数据结构
#include "WarningDll.h"  // 包含报警功能
#include "gxn.h"

#define GOOGOL_DISABLE_FROM_XML 1

ErrorCode GoogolCncInterface::initGoogolSystemParam() {
    if (m_sysParamPtr.m_shm8Ptr == nullptr || m_sysParamPtr.m_shm32Ptr == nullptr) {
        return ErrorCode::InvalidParam;
    }
#if GOOGOL_DISABLE_FROM_XML
    if (getMachineType() == MachineType::LacheXZS) {
        m_sysParamPtr.m_shm32Ptr->m_SysChannelNum = 1;  // 通道数量
        m_sysParamPtr.m_shm32Ptr->m_SysSpindleNum = 1;  // 主轴数量
        m_sysParamPtr.m_shm32Ptr->m_SysAxisNum = 2;     // 轴数量,不包括主轴
    } else if (getMachineType() == MachineType::GeneralMachineXZS) {
        m_sysParamPtr.m_shm32Ptr->m_SysChannelNum = 1;  // 通道数量
        m_sysParamPtr.m_shm32Ptr->m_SysSpindleNum = 1;  // 主轴数量
        m_sysParamPtr.m_shm32Ptr->m_SysAxisNum = 2;     // 轴数量,不包括主轴
    } else {
        HW_LOG_ERROR(m_logger, "initGoogolSystemParam 机器类型错误");
        return ErrorCode::InvalidParam;
    }

    // 修改为与SysConfig.xml一致的值
    m_sysParamPtr.m_shm32Ptr->m_nMpgFilterTime = 1;  // 从100改为1
    // 配置错误可能导致手轮不能动
    m_sysParamPtr.m_shm32Ptr->m_nWheelAxis = 1;    // 从4改为1
    m_sysParamPtr.m_shm32Ptr->m_AutoMpgMEven = 4;  // 从1改为4
    m_sysParamPtr.m_shm32Ptr->m_AutoMpgSEven = 1;
    m_sysParamPtr.m_shm32Ptr->m_AutoMpgFilterTime = 20;
    m_sysParamPtr.m_shm32Ptr->m_nUserVarShareMinNo = 500;
    m_sysParamPtr.m_shm32Ptr->m_nUserVarShareMaxNo = 29999;

    m_sysParamPtr.m_shm32Ptr->m_nFollowTableSize = 16;
    m_sysParamPtr.m_shm32Ptr->m_nUserVarShareMinNo = 100;

    SYS_PARA_8* sysPara = m_sysParamPtr.m_shm8Ptr;
    sysPara->m_nCardType = CARD_R688;

    sysPara->m_nDecimalNum = 4;
    sysPara->m_nDisDecimalNum = 4;
    sysPara->m_nLogSaveDate = 3;  // 从30改为3
    sysPara->m_bLogDubug = true;
    sysPara->m_bLogTip = true;
    sysPara->m_bMpgDir = true;
    sysPara->m_bProbeSns = true;

    sysPara->m_nExtIOType = 1;
    sysPara->m_bProtectUserMacroInSim = true;
    sysPara->m_nMultSysPosSyncIO = 1;  // 从false改为1
    sysPara->m_nMultSysPosSyncIOlevel = 1;

    sysPara->m_nCloudLoadType = 1;
    sysPara->m_nDisAxisPosType = 1;
    sysPara->m_bUpdataAbsPosAfterEmg = true;  //测试过后，无效，总是报警
    sysPara->m_bContiueStartBufCmd = true;
    sysPara->m_bEnableRemoteRead = true;
    sysPara->m_bDisableGantrol = true;
    sysPara->m_nStartLineMode = 1;

    sysPara->m_bCheckPowerOff = true;
    sysPara->m_nVelRatioIndexManual = true;
#else
    short ret = GTCR_LoadSysPara(m_sysParamPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTCR_LoadSysPara 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTCR_LoadSysPara");
    }
#endif

    return ErrorCode::Success;
}

ErrorCode GoogolCncInterface::initGoogolAxisConfig() {
#if GOOGOL_DISABLE_FROM_XML
    if (m_axesCfgPtr.m_shmPtr == nullptr) {
        return ErrorCode::InvalidParam;
    }

    int axisCount = m_sysParamPtr.m_shm32Ptr->m_SysAxisNum + m_sysParamPtr.m_shm32Ptr->m_SysSpindleNum;
    for (int i = 0; i < axisCount; i++) {
        m_axesCfgPtr.m_shmPtr->axisConfig[i].bAxisEnable = true;

        // 设置轴名称：XYZABCUVW...
        char axisName;
        if (i < 3) {
            axisName = 'X' + i;
        } else if (i < 6) {
            axisName = 'A' + i - 3;
        } else if (i < 9) {
            axisName = 'U' + i - 6;
        } else {
            axisName = 'D' + i - 9;
        }

        if (getMachineType() == MachineType::LacheXZS || getMachineType() == MachineType::GeneralMachineXZS) {
            if (i == 1) {
                axisName = 'Z';
            }
        }

        strncpy(m_axesCfgPtr.m_shmPtr->axisConfig[i].ncAxisName, &axisName, 1);
        m_axesCfgPtr.m_shmPtr->axisConfig[i].ncAxisName[1] = '\0';
        if (i < m_sysParamPtr.m_shm32Ptr->m_SysAxisNum) {
            m_axesCfgPtr.m_shmPtr->axisConfig[i].nAxisType = _AXIS_TYPE_CRD;
            strncpy(m_axesCfgPtr.m_shmPtr->axisConfig[i].disAxisName, &axisName, 1);
            m_axesCfgPtr.m_shmPtr->axisConfig[i].disAxisName[1] = '\0';
        } else {
            m_axesCfgPtr.m_shmPtr->axisConfig[i].nAxisType = _AXIS_TYPE_SPINDLE;
            std::string axisName;
            if (m_sysParamPtr.m_shm32Ptr->m_SysSpindleNum > 1) {
                axisName = "S" + std::to_string(i);
            } else {
                axisName = "S";
            }
            strncpy(m_axesCfgPtr.m_shmPtr->axisConfig[i].disAxisName, axisName.c_str(), axisName.length());
            m_axesCfgPtr.m_shmPtr->axisConfig[i].disAxisName[axisName.length()] = '\0';
        }
    }
#else
    short ret = GTCR_LoadAxesConfig(m_axesCfgPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTCR_LoadAxesConfig 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTCR_LoadAxesConfig");
    }
#endif

    return ErrorCode::Success;
}

ErrorCode GoogolCncInterface::initGoogolMotionParam(int totalChannelCount) {
#if GOOGOL_DISABLE_FROM_XML
    for (int i = 0; i < totalChannelCount; i++) {
        if (m_motionParamPtr[i].m_shm8Ptr == nullptr || m_motionParamPtr[i].m_shm32Ptr == nullptr) continue;

        m_motionParamPtr[i].m_shm8Ptr->m_nMachineType = NC_SYSTEM_CNC;

        if (getMachineType() == MachineType::LacheXZS || getMachineType() == MachineType::GeneralMachineXZS) {
            m_motionParamPtr[i].m_shm8Ptr->m_b5AxesXDir = 1;
            m_motionParamPtr[i].m_shm8Ptr->m_b5AxesZDir = 1;
        }
        // 根据轴数量设置，否则：AN0027 通道1插补轴数与通道配置不匹配
        m_motionParamPtr[i].m_shm32Ptr->m_ControlAxisNum = m_sysParamPtr.m_shm32Ptr->m_SysAxisNum;
        m_motionParamPtr[i].m_shm32Ptr->m_crdDimension = m_sysParamPtr.m_shm32Ptr->m_SysAxisNum;
        m_motionParamPtr[i].m_shm32Ptr->m_crdAxisNum = m_sysParamPtr.m_shm32Ptr->m_SysAxisNum;

        // 设置重要的8位参数
        m_motionParamPtr[i].m_shm8Ptr->m_bOpenMPGGuide = 1;  // 开启MPG引导
        m_motionParamPtr[i].m_shm8Ptr->m_bExtendSubFile = 1;
        m_motionParamPtr[i].m_shm8Ptr->m_UsePathOpt = 1;
        m_motionParamPtr[i].m_shm8Ptr->m_bUseToolLen = 1;
        m_motionParamPtr[i].m_shm8Ptr->m_bUseOptToolComp = 1;
        m_motionParamPtr[i].m_shm8Ptr->m_nToolLenSel = 1;
        m_motionParamPtr[i].m_shm8Ptr->m_nVelRatioIndexG00 = 1;
        m_motionParamPtr[i].m_shm8Ptr->m_nVelRatioIndexG01 = 2;
        m_motionParamPtr[i].m_shm8Ptr->m_nVelRatioIndexSP = 1;
        m_motionParamPtr[i].m_shm8Ptr->m_nJumpStartMacroPrgSel = 4;
        m_motionParamPtr[i].m_shm8Ptr->m_nDvMaxLmtType = 1;
        m_motionParamPtr[i].m_shm8Ptr->m_nDvMaxLmtOption = 1;
        m_motionParamPtr[i].m_shm8Ptr->m_nGSCommandMode = 1;
        m_motionParamPtr[i].m_shm8Ptr->m_nServoGSSlaveStationID = 4;
        m_motionParamPtr[i].m_shm8Ptr->m_laPrmType = 1;
        m_motionParamPtr[i].m_shm8Ptr->m_nG0EndVelMode = 1;

        m_motionParamPtr[i].m_shm32Ptr->m_lookparaSelect = 1;
        m_motionParamPtr[i].m_shm32Ptr->m_OutlineCompNumAddr = 10002;
        m_motionParamPtr[i].m_shm32Ptr->m_OutlineCompTableAddr = 10003;

        m_motionParamPtr[i].m_shm32Ptr->m_5AxesMachineType = 0;
        m_motionParamPtr[i].m_shm32Ptr->m_VelSmoothModeLa = 1;

        // 添加其他重要的32位参数
        m_motionParamPtr[i].m_shm32Ptr->m_PathOptBlendingMode = 0;
        m_motionParamPtr[i].m_shm32Ptr->m_5AxesVelSettingDef = 0;
        m_motionParamPtr[i].m_shm32Ptr->m_AxisGCodeType = 0;
        m_motionParamPtr[i].m_shm32Ptr->m_ncCodeType = 0;

        m_motionParamPtr[i].m_shm32Ptr->m_lookAheadNum = 400;
        m_motionParamPtr[i].m_shm32Ptr->m_startPushBufCmdNum = 2400;
        m_motionParamPtr[i].m_shm32Ptr->m_runPushBufCmdNum = 400;

        m_motionParamPtr[i].m_shm64Ptr->m_synVel = 15000.00f;  //加工速度
        m_motionParamPtr[i].m_shm64Ptr->m_TMGVel = 12000.00f;  //换刀速度

#if 0
        m_motionParamPtr[i].m_shm64Ptr->m_g0Vel = 60000.00f;
        m_motionParamPtr[i].m_shm64Ptr->m_G0MaxVel = 60000.00f;
        m_motionParamPtr[i].m_shm64Ptr->m_G0Acc = 7200000.00f;
#else
        m_motionParamPtr[i].m_shm64Ptr->m_g0Vel = 15000.00f;     //快进速度
        m_motionParamPtr[i].m_shm64Ptr->m_G0MaxVel = 30000.00f;  //
        m_motionParamPtr[i].m_shm64Ptr->m_G0Acc = 7200000.00f;
#endif

        m_motionParamPtr[i].m_shm64Ptr->m_synMaxVel = 15000.00f;

        m_motionParamPtr[i].m_shm64Ptr->m_synAcc = 2520000.00f;
        m_motionParamPtr[i].m_shm64Ptr->m_stpAcc = 2520000.00f;
        m_motionParamPtr[i].m_shm64Ptr->m_EvenTime = 50.00f;

        m_motionParamPtr[i].m_shm64Ptr->m_JerkMax = 2000.00f;
        m_motionParamPtr[i].m_shm64Ptr->m_EStopAcc = 9997200.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_PathTolerance = 0.005f;
        m_motionParamPtr[i].m_shm64Ptr->m_LookaheadRatio = 1.00f;
        m_motionParamPtr[i].m_shm64Ptr->m_blendingMinError = 0.005f;
        m_motionParamPtr[i].m_shm64Ptr->m_blendingMaxAngle = 150.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_5mmArcVel = 3000.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_arcRMinErr = 0.001f;
        m_motionParamPtr[i].m_shm64Ptr->m_arcRMaxErr = 0.010f;
        m_motionParamPtr[i].m_shm64Ptr->m_MinDccAngle = 150.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_ManualFeed = 1000.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_ManualSpeed = 3000.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_VelVerifyMaxR = 5.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_VelVerifyMinR = 0.100f;
        m_motionParamPtr[i].m_shm64Ptr->m_VelVerifyVref = 2000.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_MaxOverrideLa = 1.500f;

        for (int j = 0; j < 20; j++) {
            m_motionParamPtr[i].m_shm64Ptr->m_lookparaAcc[j] = 0.360f;
        }

        for (int j = 0; j < 20; j++) {
            m_motionParamPtr[i].m_shm64Ptr->m_lookparaT[j] = 0.010f;
        }

        m_motionParamPtr[i].m_shm64Ptr->m_dArcDisptolerance = 0.001f;
        m_motionParamPtr[i].m_shm64Ptr->m_dNolinearError = 0.100f;

        for (int j = 0; j < 3; j++) {
            m_motionParamPtr[i].m_shm64Ptr->m_5AxesPrimaryAxisPoint[j] = -0.310f;
        }

        for (int j = 0; j < 3; j++) {
            m_motionParamPtr[i].m_shm64Ptr->m_5AxesSlaveAxisPoint[j] = -1.500f;
        }

        m_motionParamPtr[i].m_shm64Ptr->m_GrpPrfMode = 41.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_GrpPrfAccTime = 26.500f;
        m_motionParamPtr[i].m_shm64Ptr->m_GrpPrfValue = 100.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_GrpPrfBegPercent = 10.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_GrpPrfEndPercent = 10.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_dPowerOffAxisMoveBack = 5.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_dOriMotionRatio = 1.000f;

        // 添加其他重要的64位参数
        m_motionParamPtr[i].m_shm64Ptr->m_ZUpPos = 0.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_GrpPrfK = 0.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_dOriVelMax = 1000.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_dOriAccMax = 10000.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_dOriDecMax = 10000.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_dOriJerkMax = 10000.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_laCurvatureErr = 0.010f;
        m_motionParamPtr[i].m_shm64Ptr->m_5AxesStandartToolLength = 0.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_ToolBaseLength = 0.000f;
        m_motionParamPtr[i].m_shm64Ptr->m_G37BaseValue = 0.000f;

        // 设置5轴工具坐标和工作坐标中心
        for (int j = 0; j < 3; j++) {
            m_motionParamPtr[i].m_shm64Ptr->m_5AxesToolCoordPoint[j] = 0.000f;
        }

        for (int j = 0; j < 3; j++) {
            m_motionParamPtr[i].m_shm64Ptr->m_5AxesWorkCoordCenter[j] = 0.000f;
        }

        // 设置TmHD2轴相关参数
        for (int j = 0; j < 3; j++) {
            m_motionParamPtr[i].m_shm64Ptr->m_TmHD2AxisPoint[j] = 0.000f;
        }

        for (int j = 0; j < 3; j++) {
            m_motionParamPtr[i].m_shm64Ptr->m_TmHD2AxisDirVector[j] = 0.000f;
        }
    }
#else
    for (int i = 0; i < totalChannelCount; i++) {
        short ret = GTCR_LoadMotionPara(m_motionParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTCR_LoadMotionPara 失败，返回码: %d", ret);
            return handleGoogolReturnCode(ret, "GTCR_LoadMotionPara");
        }
    }
#endif
    return ErrorCode::Success;
}

// 公共默认值设置函数
void GoogolCncInterface::setDefaultAxisParam(int axisIndex) {
    // 基础轴配置
    // 和硬件轴ID保持一致
    if (axisIndex == 0) {
        m_axisParamPtr[axisIndex].m_shm8Ptr->m_axisID = 1;
    } else if (axisIndex == 1) {
        m_axisParamPtr[axisIndex].m_shm8Ptr->m_axisID = 3;
    } else if (axisIndex == 2) {
        m_axisParamPtr[axisIndex].m_shm8Ptr->m_axisID = 4;
    } else {
        m_axisParamPtr[axisIndex].m_shm8Ptr->m_axisID = axisIndex + 1;
    }

    // 默认为轴号（轴ID，否则：AN0034 轴3的规划输出通道配置错误
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nAxisPrfIndex = m_axisParamPtr[axisIndex].m_shm8Ptr->m_axisID;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nAxisEncIndex = m_axisParamPtr[axisIndex].m_shm8Ptr->m_axisID;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nAxisStepIndex = m_axisParamPtr[axisIndex].m_shm8Ptr->m_axisID;

    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEnAlarm = true;  //伺服报警

    m_axisParamPtr[axisIndex].m_shm8Ptr->m_axisType = 0;  // 直线轴
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nCtrlMode = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEnable = 1;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEnLmtPosi = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEnLmtNega = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEnLmtSoft = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEnScrewComp = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEnBacklash = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nCompDir = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEnBand = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEnInputShaping = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEnPrfPosFilter = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEnPrfVelFilter = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEnWheelCtrl = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bLaMaxVelLmt = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bLaMaxAccLmt = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bLaMaxDVLmt = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bPosiLmtSns = 1;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bHomeSns = 1;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEncoderSns = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nEncoderType = 0;

    m_axisParamPtr[axisIndex].m_shm8Ptr->m_homeType = 5;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_homeDir = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_homeTrig = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_indexTrig = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nGPIHomeIndex = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bSynEncoderToPrf = 1;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nAbsEncDataType = 1;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nCycleScrewComp = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nAxisPrfIndexEx = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nPosiLmtType = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nNegaLmtType = 1;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nPosiLmtIndex = axisIndex + 1;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nNegaLmtIndex = axisIndex + 1;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_GSDir = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nCrossCompMode = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nCrossMasterId = 1;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_iSysIndex = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nWheelAxis = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bMpgDir = 0;

    //不能设置：否则：AN0032 第1车削主轴必须为C类型轴
    //通过：m_axesCfgPtr.m_shmPtr->axisConfig[axisIndex].nAxisType 设置
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nSpindleType = 0;

    // 根据轴索引设置5维轴ID，依赖：
    // m_motionParamPtr[axisIndex].m_shm8Ptr->m_b5AxesXDir = 1;
    // m_motionParamPtr[axisIndex].m_shm8Ptr->m_b5AxesZDir = 1;
    // m_motionParamPtr[axisIndex].m_shm8Ptr->m_b5AxesYDir = 0;
    // m_motionParamPtr[axisIndex].m_shm8Ptr->m_b5AxesPDir = 0;
    // m_motionParamPtr[axisIndex].m_shm8Ptr->m_b5AxesSDir = 0;
    // 否则：AN0037 轴2的模型ID配置错误
    if (axisIndex == 0) {
        m_axisParamPtr[axisIndex].m_shm8Ptr->m_n5DimensionAxisID = 1;  // AxisPara_0.xml
    } else if (axisIndex == 1) {
        m_axisParamPtr[axisIndex].m_shm8Ptr->m_n5DimensionAxisID = 3;  // AxisPara_1.xml
    } else if (axisIndex == 2) {
        m_axisParamPtr[axisIndex].m_shm8Ptr->m_n5DimensionAxisID = 0;  // AxisPara_2.xml (修正为0， 无效轴)
    } else {
        m_axisParamPtr[axisIndex].m_shm8Ptr->m_n5DimensionAxisID = axisIndex + 1;  // 其他轴默认
    }

    m_axisParamPtr[axisIndex].m_shm8Ptr->m_reverseLimitMode = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nAuAbsEncoderIndex = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEnCompleteSynEncToPrf = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nSpindleCtrlType = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nAdditionCtrlType = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_n5RotateAxisType = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bTSpindleDir = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEnEStopDi = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nEStopDiType = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nEStopDiIndex = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEStopDiSns = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nMPGSource = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_nTempSensorType = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bAxleLoad = 1;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bCheckGlinkServoSts = 1;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEnAxisSecondName = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_iCaptureSignalType = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_iCaptureSignalIndex = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_iCaptureSignalValue = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_iAxisScaleUpdateMode = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bSpPosErrCheck = 0;
    m_axisParamPtr[axisIndex].m_shm8Ptr->m_bEnComp2D = 0;

    // 32位参数
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_screwPointNum = 31;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_encoderBits = 17;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_nAbsoluteEncDir = 1;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_maxEncoderPulses = 10550000;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_minEncoderPulses = -50000;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_prfAlpha = 1;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_prfBeta = 1;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_encAlpha = 1;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_encBeta = 1;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_MotorBrand = 14;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_port = 1;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_baud = 19200;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_dataBit = 8;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_parity = 0;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_stopbit = 1;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_AbsoluteEncID = axisIndex + 1;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_PrfVelSmoothType = -1;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_PrfVelSmoothK = 0;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_bandTime = 10;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_homeLastWaitTime = 1000;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_MaxOutput = 1000;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_MinOutput = 0;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_ulCrossPointNum = 10;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_TorqueLatchType = 0;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_TorqueProbeType = 0;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_TorqueCurrentValue = 0;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_TorqueContinueTime = 0;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_TorqueWindowOnly = 0;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_TorqueFirstPosition = 0;
    m_axisParamPtr[axisIndex].m_shm32Ptr->m_TorqueLastPosition = 0;

    // 64位参数
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_kp = 1.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_ki = 0.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_kd = 0.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_kvff = 0.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_kaff = 0.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_gantryKp = 0.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_mtrBias = 0.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_iLimt = 1000.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_outCmdLmt = 10.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_nPulse = 120000.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_nPitch = 12.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_backlash = 0.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_mapCount = 0.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_lScrewReverseVel = 5.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_FlowerrMax = 1.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_maxAcc = 9997200.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_maxVel = 24000.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_maxDVel = 5000.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_StopDec = 7999200.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_EStopDec = 11998800.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_jogfeed = 1100.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_jogacc = 7999200.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_jogdec = 7999200.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_jogsmooth = 5.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_bufMovefeed = 20000.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_bufMoveacc = 2998800.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_bufMovedec = 7999200.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_bufMovesmooth = 5.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_HomeVel = 12000.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_HomeLowVel = 100.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_NegOffset = 5.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_SchIndexOffSet = 20.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_HomeOffset = 0.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_IndexOffset = 0.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_refPos = 0.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_Band = 0.05f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_accurateBand = 0.001f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_roughStopBand = 0.01f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_wheelScale = 1.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_WheelSlope = 0.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_rPreV = 1000.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_BackCompValue = 0.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_BackCompLowPassTimeK = 10.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_BackCompDecTime = 10.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_SmoothTime = 60.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_SmoothK = 10.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_MaxJerk = 100.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_SpindleDelay = 0.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_scale = 10000.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_velScale = 0.1666666667f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_accScale = 0.0000027778f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_shortVel = 15000.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_shortAcc = 7999200.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_shortDec = 7999200.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_shortSmoothtime = 5.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_shortDistance = 5.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_GantryErrLmt = 2.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_HomeIndexMinErr = 1.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_dAdjustmentAccuracy = 0.001f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_GantryAdjVel = 50.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_HomeLmtLeaveDis = 10.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_EcatHomeAcc = 10000.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_AbsEncoderPulse = 120000.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_EnCompleteVelBand = 0.01f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_minPos2 = -5.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_maxPos2 = 1055.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_VelRefRatio = 10.0f;
    m_axisParamPtr[axisIndex].m_shm64Ptr->m_EncDataScale = 10000.0f;

    // 2D补偿参数 - 注释掉不存在的成员
    // m_axisParamPtr[axisIndex].m_shm64Ptr->m_2DCompPosBegin_0 = 0.0f;
    // m_axisParamPtr[axisIndex].m_shm64Ptr->m_2DCompPosBegin_1 = 0.0f;
    // m_axisParamPtr[axisIndex].m_shm64Ptr->m_2DCompStep_0 = 0.0f;
    // m_axisParamPtr[axisIndex].m_shm64Ptr->m_2DCompStep_1 = 0.0f;
    // m_axisParamPtr[axisIndex].m_shm64Ptr->m_2DCompAngle = 0.0f;
}

ErrorCode GoogolCncInterface::initGoogolAxisParam(int totalAxesCount) {
#if GOOGOL_DISABLE_FROM_XML
    for (int i = 0; i < totalAxesCount; i++) {
        if (m_axisParamPtr[i].m_shm8Ptr == nullptr || m_axisParamPtr[i].m_shm32Ptr == nullptr) {
            continue;
        }

        // 设置基础默认参数
        setDefaultAxisParam(i);

        // 根据轴索引设置特定参数
        switch (i) {
            case 0:  // Axis 1 (X轴)
                setAxis1SpecificParams(i);
                break;
            case 1:  // Axis 2 (Z轴)
                setAxis2SpecificParams(i);
                break;
            case 2:  // Axis 3 (S轴)
                setAxis3SpecificParams(i);
                break;
            default:
                // 其他轴保持默认配置
                break;
        }

        // 主轴类型设置
        if (m_axesCfgPtr.m_shmPtr->axisConfig[i].nAxisType == _AXIS_TYPE_SPINDLE) {
            m_axisParamPtr[i].m_shm8Ptr->m_axisType = 1;  // 旋转轴
            m_axisParamPtr[i].m_shm8Ptr->m_bEnAlarm = 0;  //关闭伺服报警功能，没有伺服报警信息，卡库出错
        }
    }
#else

    for (int i = 0; i < totalAxesCount; ++i) {
        // 轴控制参数
        int ret = GTCR_LoadAxisPara(m_axisParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTCR_LoadAxisPara 加载轴/主轴索引 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTCR_LoadAxisPara");
        }
    }
#endif
    return ErrorCode::Success;
}

// 设置轴1特定参数 (X轴)
void GoogolCncInterface::setAxis1SpecificParams(int i) {
    // 8位参数
    m_axisParamPtr[i].m_shm8Ptr->m_bNegaLmtSns = 1;
    m_axisParamPtr[i].m_shm8Ptr->m_homeType = 5;
    m_axisParamPtr[i].m_shm8Ptr->m_nWheelAxis = 1;
    m_axisParamPtr[i].m_shm8Ptr->m_bMpgDir = 1;

    // 32位参数
    m_axisParamPtr[i].m_shm32Ptr->m_screwPointNum = 53;
    m_axisParamPtr[i].m_shm32Ptr->m_nAbsoluteEncDir = 1;
    m_axisParamPtr[i].m_shm32Ptr->m_maxEncoderPulses = 10550000;
    m_axisParamPtr[i].m_shm32Ptr->m_minEncoderPulses = -50000;
    m_axisParamPtr[i].m_shm32Ptr->m_AbsoluteEncID = 1;

    // 64位参数
    m_axisParamPtr[i].m_shm64Ptr->m_minPos = -5.0f;
    m_axisParamPtr[i].m_shm64Ptr->m_maxPos = 1055.0f;
    m_axisParamPtr[i].m_shm64Ptr->m_AbsoluteEncZero = -7771566.0f;
    m_axisParamPtr[i].m_shm64Ptr->m_EnCompleteVelBand = 0.01f;
    m_axisParamPtr[i].m_shm64Ptr->m_minPos2 = -5.0f;
    m_axisParamPtr[i].m_shm64Ptr->m_maxPos2 = 1055.0f;
    m_axisParamPtr[i].m_shm64Ptr->m_VelRefRatio = 10.0f;
}

// 设置轴2特定参数 (Z轴)
void GoogolCncInterface::setAxis2SpecificParams(int i) {
    // 8位参数
    m_axisParamPtr[i].m_shm8Ptr->m_bNegaLmtSns = 1;
    m_axisParamPtr[i].m_shm8Ptr->m_homeType = 5;
    m_axisParamPtr[i].m_shm8Ptr->m_homeTrig = 1;
    m_axisParamPtr[i].m_shm8Ptr->m_indexTrig = 1;

    // 32位参数
    m_axisParamPtr[i].m_shm32Ptr->m_screwPointNum = 31;
    m_axisParamPtr[i].m_shm32Ptr->m_nAbsoluteEncDir = 1;
    m_axisParamPtr[i].m_shm32Ptr->m_maxEncoderPulses = 15000;
    m_axisParamPtr[i].m_shm32Ptr->m_minEncoderPulses = -6015000;
    m_axisParamPtr[i].m_shm32Ptr->m_AbsoluteEncID = 2;

    // 64位参数
    m_axisParamPtr[i].m_shm64Ptr->m_minPos = -601.5f;
    m_axisParamPtr[i].m_shm64Ptr->m_maxPos = 1.5f;
    m_axisParamPtr[i].m_shm64Ptr->m_AbsoluteEncZero = 3979610.0f;
    m_axisParamPtr[i].m_shm64Ptr->m_EnCompleteVelBand = 0.0f;
    m_axisParamPtr[i].m_shm64Ptr->m_minPos2 = -601.5f;
    m_axisParamPtr[i].m_shm64Ptr->m_maxPos2 = 1.5f;
    m_axisParamPtr[i].m_shm64Ptr->m_VelRefRatio = 10.0f;
}

// 设置轴3特定参数 (S轴)
void GoogolCncInterface::setAxis3SpecificParams(int i) {
    // 8位参数 - 根据AxisPara_2.xml修正
    m_axisParamPtr[i].m_shm8Ptr->m_axisType = 1;  // 旋转轴
    m_axisParamPtr[i].m_shm8Ptr->m_bNegaLmtSns = 0;
    m_axisParamPtr[i].m_shm8Ptr->m_bEncoderSns = 1;
    m_axisParamPtr[i].m_shm8Ptr->m_homeType = 5;
    m_axisParamPtr[i].m_shm8Ptr->m_homeDir = 1;
    m_axisParamPtr[i].m_shm8Ptr->m_homeTrig = 1;
    m_axisParamPtr[i].m_shm8Ptr->m_indexTrig = 1;
    m_axisParamPtr[i].m_shm8Ptr->m_GSDir = 1;

    // 32位参数
    m_axisParamPtr[i].m_shm32Ptr->m_screwPointNum = 31;
    m_axisParamPtr[i].m_shm32Ptr->m_maxEncoderPulses = 50000;
    m_axisParamPtr[i].m_shm32Ptr->m_minEncoderPulses = -6015000;
    m_axisParamPtr[i].m_shm32Ptr->m_AbsoluteEncID = 3;

    // 64位参数
    m_axisParamPtr[i].m_shm64Ptr->m_minPos = -601.5f;
    m_axisParamPtr[i].m_shm64Ptr->m_maxPos = 5.0f;
    m_axisParamPtr[i].m_shm64Ptr->m_AbsoluteEncZero = 4137029.0f;
    m_axisParamPtr[i].m_shm64Ptr->m_EnCompleteVelBand = 0.0f;
    m_axisParamPtr[i].m_shm64Ptr->m_minPos2 = -15.0f;
    m_axisParamPtr[i].m_shm64Ptr->m_maxPos2 = 1.0f;
    m_axisParamPtr[i].m_shm64Ptr->m_VelRefRatio = 10.0f;
}

MachineType GoogolCncInterface::getMachineType() { return static_cast<MachineType>(m_machineParameter.m_type); }

ErrorCode GoogolCncInterface::initGoogolGCodeParam(int totalChannelCount) {
    for (int i = 0; i < totalChannelCount; ++i) {
        if (m_gcodeParamPtr[i].m_shm8Ptr == nullptr || m_gcodeParamPtr[i].m_shm32Ptr == nullptr ||
            m_gcodeParamPtr[i].m_shm64Ptr == nullptr) {
            continue;
        }

#if GOOGOL_DISABLE_FROM_XML
        // 根据GCodePara_0.xml文件硬编码设置G代码参数
        setDefaultGCodeParam(i);
#else
        // 工艺参数
        int ret = GTCR_LoadGCodePara(m_gcodeParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTCR_LoadGCodePara 加载通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTCR_LoadGCodePara");
        }
#endif
    }
    return ErrorCode::Success;
}

// 根据XML文件硬编码设置G代码参数的辅助函数
void GoogolCncInterface::setDefaultGCodeParam(int channelIndex) {
    // 8位参数 - 根据GCodePara_0.xml设置
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iInitGroupSet_1 = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iInitGroupSet_2 = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iInitGroupSet_3 = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iInitGroupSet_5 = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iInitGroupSet_6 = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iInitGroupG10_9 = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->bG43_4RotateAxisZeroType = 1;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->bG43_4UpPcsZOffset = 1;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->bG49UpPcsZOffset = 1;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iScrewAccLenSet = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iCAxisDirG1_1 = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->G276_Pm = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iTmSpindleStopType = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->G75_IsRetract = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->G75_RetractDir = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->M00_IsStopTSpindle = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->M202_Vir5AxisID = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->G43_AxisPosType = 1;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->G130_NearPosSel = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->bG43_4PosCoordType = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->M30_IsStopMSpindle = 1;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->M19_IsPlcMotion = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iMSpindleStopType = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->bG43_4_G68_2_RotateAxisPosType = 1;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->G07_1_RotateAxisPosType = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iCaptureStopPosType = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->M19_AutoServoOn = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iG52CancelMode = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->G0_AxisMotionConstraintMode = 1;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->TID_DigitsNum = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iTSpindleControlType = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iG271PathCompType = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->TComp_DigitsNum = 2;  // ！！！重要：这里表示需要刀具补偿号！！！
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iG53MotionType = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iTSpindleMotionMode = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iGLoopJBPara = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iG75RtnDir = 0;
    m_gcodeParamPtr[channelIndex].m_shm8Ptr->iG74Mode = 0;

    // 探测信号配置数组 - 根据XML设置
    for (int j = 0; j < 8; j++) {
        m_gcodeParamPtr[channelIndex].m_shm8Ptr->iCaptureStopSignalType[j] = 0;
        m_gcodeParamPtr[channelIndex].m_shm8Ptr->iCaptureStopSignalIndex[j] = 0;
        m_gcodeParamPtr[channelIndex].m_shm8Ptr->iCaptureStopSignalValue[j] = 0;
        m_gcodeParamPtr[channelIndex].m_shm8Ptr->iCaptureStopSignalCondition[j] = 0;
        m_gcodeParamPtr[channelIndex].m_shm8Ptr->G31_SignalSel[j] = 0;
        m_gcodeParamPtr[channelIndex].m_shm8Ptr->G31_SignalLogic[j] = 0;
    }

    // 64位参数 - 根据GCodePara_0.xml设置
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->G53_3_ForwardVel = 0.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->G53_3_BackwardVel = 0.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->screwAccLen = 0.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->screwDecLen = 0.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->G292_chamferLen = 0.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->G292_chamferAngle = 0.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->G276_Pa = 0.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->G276_Rd = 0.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->dTmSpindleStopPos = 0.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->M19_SpindleStopPos = 0.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->G276_Mind = 0.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->axisVelZeroValue = 0.100000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->axisVelArriveValue = 1.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->G271_Ud = 1.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->G271_Re = 1.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->G273_Ui = 1.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->G273_Wk = 1.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->G273_Rd = 1.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->G274_Re = 0.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->G73_D = 0.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->G83_D = 0.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->G74_D = 0.000000;
    m_gcodeParamPtr[channelIndex].m_shm64Ptr->G84_2RtnRadio = 0.0000000000;

    // G31探测信号数组 - 根据XML设置
    for (int j = 0; j < 8; j++) {
        m_gcodeParamPtr[channelIndex].m_shm64Ptr->G31_CaptureStopMoveFeed[j] = 0.000000;
    }

    // G28参考点数组 - 根据XML设置，全部初始化为0
    for (int group = 0; group < 4; group++) {
        for (int index = 0; index < 40; index++) {
            m_gcodeParamPtr[channelIndex].m_shm64Ptr->G28_refPos[group][index] = 0.000000;
        }
    }
}

ErrorCode GoogolCncInterface::initGoogolG5_1Param(int totalChannelCount) {
    for (int i = 0; i < totalChannelCount; ++i) {
        if (m_tg51ParamPtr[i].m_shmPtr == nullptr) {
            continue;
        }

#if GOOGOL_DISABLE_FROM_XML
        // 根据G5_1Para_0.xml文件硬编码设置G5.1高速高精参数
        setDefaultG5_1Param(i);
#else
        int ret = GTCR_LoadG5_1Para(m_tg51ParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTCR_LoadG5_1Para 加载通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTCR_LoadG5_1Para");
        }
#endif
    }
    return ErrorCode::Success;
}

// 根据XML文件硬编码设置G5.1高速高精参数的辅助函数
void GoogolCncInterface::setDefaultG5_1Param(int channelIndex) {
    // 根据G5_1Para_0.xml文件设置参数

    // 坐标系1参数
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfMode[0] = 41.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfAccTime[0] = 26.500000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfK[0] = 0.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfValue[0] = 100.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfBegPercent[0] = 10.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfEndPercent[0] = 10.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_JerkMax[0] = 2000.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_5mmArcVel[0] = 3000.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_MaxOverrideLa[0] = 1.500000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_lookparaT[0] = 0.010000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_laCurvatureErr[0] = 0.010000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_G0Acc[0] = 3000000.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_synAcc[0] = 3000000.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_stpAcc[0] = 3000000.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_laPrmType[0] = 1;

    // 坐标系2参数
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfMode[1] = 41.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfAccTime[1] = 50.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfK[1] = 0.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfValue[1] = 100.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfBegPercent[1] = 10.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfEndPercent[1] = 10.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_JerkMax[1] = 1000.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_5mmArcVel[1] = 3000.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_MaxOverrideLa[1] = 1.500000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_lookparaT[1] = 0.005000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_laCurvatureErr[1] = 0.010000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_G0Acc[1] = 3000000.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_synAcc[1] = 3000000.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_stpAcc[1] = 3000000.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_laPrmType[1] = 0;

    // 坐标系3参数
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfMode[2] = 41.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfAccTime[2] = 50.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfK[2] = 0.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfValue[2] = 100.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfBegPercent[2] = 10.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_GrpPrfEndPercent[2] = 10.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_JerkMax[2] = 1000.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_5mmArcVel[2] = 3000.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_MaxOverrideLa[2] = 1.500000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_lookparaT[2] = 0.005000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_laCurvatureErr[2] = 0.010000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_G0Acc[2] = 3000000.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_synAcc[2] = 3000000.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_stpAcc[2] = 3000000.000000;
    m_tg51ParamPtr[channelIndex].m_shmPtr->m_laPrmType[2] = 0;

    // 设置轴平滑参数 - 坐标系1
    for (int i = 0; i < 40; i++) {
        if (i < 3) {
            // 前3个轴使用不同的参数
            m_tg51ParamPtr[channelIndex].m_shmPtr->m_SmoothTime[0][i] = 45.000000;
            m_tg51ParamPtr[channelIndex].m_shmPtr->m_SmoothK[0][i] = 16.000000;
            m_tg51ParamPtr[channelIndex].m_shmPtr->m_MaxJerk[0][i] = 100000.000000;
            m_tg51ParamPtr[channelIndex].m_shmPtr->m_maxDVel[0][i] = 180.000000;
            m_tg51ParamPtr[channelIndex].m_shmPtr->m_maxVel[0][i] = 60000.000000;
        } else {
            // 其他轴使用统一的参数
            m_tg51ParamPtr[channelIndex].m_shmPtr->m_SmoothTime[0][i] = 55.000000;
            m_tg51ParamPtr[channelIndex].m_shmPtr->m_SmoothK[0][i] = 15.000000;
            m_tg51ParamPtr[channelIndex].m_shmPtr->m_MaxJerk[0][i] = 1000.000000;
            m_tg51ParamPtr[channelIndex].m_shmPtr->m_maxDVel[0][i] = 200.000000;
            m_tg51ParamPtr[channelIndex].m_shmPtr->m_maxVel[0][i] = 10000.000000;
        }
    }

    // 设置轴平滑参数 - 坐标系2和3
    for (int coord = 1; coord < 3; coord++) {
        for (int i = 0; i < 40; i++) {
            m_tg51ParamPtr[channelIndex].m_shmPtr->m_SmoothTime[coord][i] = 55.000000;
            m_tg51ParamPtr[channelIndex].m_shmPtr->m_SmoothK[coord][i] = 15.000000;
            m_tg51ParamPtr[channelIndex].m_shmPtr->m_MaxJerk[coord][i] = 1000.000000;
            m_tg51ParamPtr[channelIndex].m_shmPtr->m_maxDVel[coord][i] = 200.000000;
            m_tg51ParamPtr[channelIndex].m_shmPtr->m_maxVel[coord][i] = 10000.000000;
        }
    }
}

ErrorCode GoogolCncInterface::initGoogolToolParam(int totalChannelCount) {
    for (int i = 0; i < totalChannelCount; ++i) {
        if (m_toolsParamPtr[i].m_shmPtr == nullptr) {
            continue;
        }

#if GOOGOL_DISABLE_FROM_XML
        // 根据ToolPara_0.xml文件硬编码设置刀具参数
        setDefaultToolParam(i);
#else
        int ret = GTCR_LoadToolPara(m_toolsParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTCR_LoadToolPara 加载通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTCR_LoadToolPara");
        }
#endif
    }
    return ErrorCode::Success;
}

// 根据XML文件硬编码设置刀具参数的辅助函数
void GoogolCncInterface::setDefaultToolParam(int channelIndex) {
    // 根据ToolPara_0.xml文件设置刀具参数

    // 刀具0 - 根据XML设置
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolR = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolDComp = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolLen[0] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolLen[1] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolLen[2] = -575.4112000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolLen[3] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolLen[4] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolLen[5] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolLen[6] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolLen[7] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolHComp[0] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolHComp[1] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolHComp[2] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolHComp[3] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolHComp[4] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolHComp[5] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolHComp[6] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ToolHComp[7] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_FeedRate = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_SpindleRate = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_ArcFeedRate = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_dToolType = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_dToolTimeType = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_dToolLock = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_dToolCurTime = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_dToolTime1 = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_dToolTime2 = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_dToolTimeTip = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_dBreakPos = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_dResToolNum = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_dMaxRpm = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[0].m_dToolLocal = 0.0000000000;

    // 刀具1 - 根据XML设置
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolR = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolDComp = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolLen[0] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolLen[1] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolLen[2] = -580.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolLen[3] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolLen[4] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolLen[5] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolLen[6] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolLen[7] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolHComp[0] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolHComp[1] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolHComp[2] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolHComp[3] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolHComp[4] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolHComp[5] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolHComp[6] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ToolHComp[7] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_FeedRate = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_SpindleRate = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_ArcFeedRate = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_dToolType = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_dToolTimeType = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_dToolLock = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_dToolCurTime = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_dToolTime1 = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_dToolTime2 = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_dToolTimeTip = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_dBreakPos = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_dResToolNum = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_dMaxRpm = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[1].m_dToolLocal = 0.0000000000;

    // 刀具2 - 根据XML设置
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolR = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolDComp = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolLen[0] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolLen[1] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolLen[2] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolLen[3] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolLen[4] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolLen[5] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolLen[6] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolLen[7] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolHComp[0] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolHComp[1] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolHComp[2] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolHComp[3] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolHComp[4] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolHComp[5] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolHComp[6] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ToolHComp[7] = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_FeedRate = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_SpindleRate = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_ArcFeedRate = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_dToolType = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_dToolTimeType = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_dToolLock = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_dToolCurTime = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_dToolTime1 = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_dToolTime2 = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_dToolTimeTip = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_dBreakPos = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_dResToolNum = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_dMaxRpm = 0.0000000000;
    m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[2].m_dToolLocal = 0.0000000000;

    // 设置刀具扩展参数 - 所有刀具的扩展参数都初始化为0
    for (int toolIndex = 0; toolIndex < TOOL_NUM_MAX; toolIndex++) {
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolParaEx[toolIndex].m_ToolSel = 0;
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolParaEx[toolIndex].m_FeedRateSel = 0;
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolParaEx[toolIndex].m_SpindleRateSel = 0;
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolParaEx[toolIndex].m_ArcFeedSel = 0;
        for (int i = 0; i < 4; i++) {
            m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolParaEx[toolIndex].null[i] = 0;
        }
    }

    // 初始化其他刀具参数为0（刀具3到TOOL_NUM_MAX-1）
    for (int toolIndex = 3; toolIndex < TOOL_NUM_MAX; toolIndex++) {
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_ToolR = 0.0000000000;
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_ToolDComp = 0.0000000000;
        for (int i = 0; i < 8; i++) {
            m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_ToolLen[i] = 0.0000000000;
            m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_ToolHComp[i] = 0.0000000000;
        }
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_FeedRate = 0.0000000000;
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_SpindleRate = 0.0000000000;
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_ArcFeedRate = 0.0000000000;
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_dToolType = 0.0000000000;
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_dToolTimeType = 0.0000000000;
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_dToolLock = 0.0000000000;
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_dToolCurTime = 0.0000000000;
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_dToolTime1 = 0.0000000000;
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_dToolTime2 = 0.0000000000;
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_dToolTimeTip = 0.0000000000;
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_dBreakPos = 0.0000000000;
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_dResToolNum = 0.0000000000;
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_dMaxRpm = 0.0000000000;
        m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].m_dToolLocal = 0.0000000000;
        for (int i = 0; i < 8; i++) {
            m_toolsParamPtr[channelIndex].m_shmPtr->m_ToolPara[toolIndex].null[i] = 0.0000000000;
        }
    }
}

ErrorCode GoogolCncInterface::initGoogolMacroSettingParam(int totalChannelCount) {
    for (int i = 0; i < totalChannelCount; ++i) {
        if (m_macroSettingParamPtr[i].m_shm8Ptr == nullptr || m_macroSettingParamPtr[i].m_shm32Ptr == nullptr) {
            continue;
        }

#if GOOGOL_DISABLE_FROM_XML
        // 根据MacroSettingPara_0.xml文件硬编码设置宏设置参数
        setDefaultMacroSettingParam(i);
#else
        int ret = GTCR_LoadMacroSettingPara(m_macroSettingParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTCR_LoadMacroSettingPara 加载通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTCR_LoadMacroSettingPara");
        }
#endif
    }
    return ErrorCode::Success;
}

// 根据XML文件硬编码设置宏设置参数的辅助函数
void GoogolCncInterface::setDefaultMacroSettingParam(int channelIndex) {
    // 根据MacroSettingPara_0.xml文件设置宏设置参数

    // 设置M代码宏名称和参数
    // M代码1-16
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[0].cName, "M06.NCC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[1].cName, "M179.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[2].cName, "M19.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[3].cName, "M170.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[4].cName, "M171.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[5].cName, "M172.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[6].cName, "");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[7].cName, "");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[8].cName, "");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[9].cName, "M71.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[10].cName, "M72.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[11].cName, "M68.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[12].cName, "M69.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[13].cName, "M18.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[14].cName, "M75.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[15].cName, "M03.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[16].cName, "M04.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[17].cName, "M05.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[18].cName, "");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[19].cName, "");

    // 设置M代码参数
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[0].nNcNum = 6;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[0].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[0].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[0].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[1].nNcNum = 179;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[1].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[1].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[1].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[2].nNcNum = 19;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[2].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[2].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[2].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[3].nNcNum = 170;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[3].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[3].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[3].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[4].nNcNum = 171;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[4].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[4].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[4].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[5].nNcNum = 172;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[5].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[5].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[5].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[6].nNcNum = 7;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[6].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[6].nGrpNo = 2;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[6].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[7].nNcNum = 8;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[7].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[7].nGrpNo = 2;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[7].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[8].nNcNum = 9;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[8].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[8].nGrpNo = 2;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[8].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[9].nNcNum = 71;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[9].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[9].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[9].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[10].nNcNum = 72;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[10].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[10].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[10].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[11].nNcNum = 68;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[11].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[11].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[11].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[12].nNcNum = 69;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[12].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[12].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[12].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[13].nNcNum = 18;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[13].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[13].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[13].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[14].nNcNum = 75;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[14].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[14].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[14].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[15].nNcNum = 3;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[15].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[15].nGrpNo = 1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[15].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[16].nNcNum = 4;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[16].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[16].nGrpNo = 1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[16].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[17].nNcNum = 5;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[17].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[17].nGrpNo = 1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[17].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[18].nNcNum = 17;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[18].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[18].nGrpNo = 2;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[18].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[19].nNcNum = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[19].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[19].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[19].null = 0;

    // 设置G代码宏名称和参数
    // G代码1-17
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[0].cName, "G73.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[1].cName, "G74.NCC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[2].cName, "G75.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[3].cName, "G76.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[4].cName, "G77.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[5].cName, "G78.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[6].cName, "G79.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[7].cName, "G80.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[8].cName, "G81.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[9].cName, "G82.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[10].cName, "G83.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[11].cName, "G84.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[12].cName, "G85.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[13].cName, "G86.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[14].cName, "G480.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[15].cName, "G590.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[16].cName, "G591.NC");
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[17].cName, "");

    // 设置G代码参数
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[0].nNcNum = 73;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[0].nType = 1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[0].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[0].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[1].nNcNum = 74;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[1].nType = 1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[1].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[1].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[2].nNcNum = 75;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[2].nType = 1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[2].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[2].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[3].nNcNum = 76;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[3].nType = 1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[3].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[3].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[4].nNcNum = 77;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[4].nType = 1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[4].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[4].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[5].nNcNum = 78;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[5].nType = 1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[5].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[5].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[6].nNcNum = 79;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[6].nType = 1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[6].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[6].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[7].nNcNum = 80;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[7].nType = 1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[7].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[7].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[8].nNcNum = 81;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[8].nType = 1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[8].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[8].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[9].nNcNum = 82;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[9].nType = 1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[9].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[9].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[10].nNcNum = 83;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[10].nType = 1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[10].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[10].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[11].nNcNum = 84;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[11].nType = 1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[11].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[11].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[12].nNcNum = 85;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[12].nType = 1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[12].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[12].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[13].nNcNum = 86;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[13].nType = 1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[13].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[13].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[14].nNcNum = 480;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[14].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[14].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[14].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[15].nNcNum = 590;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[15].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[15].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[15].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[16].nNcNum = 591;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[16].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[16].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[16].null = 0;

    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[17].nNcNum = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[17].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[17].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[17].null = 0;

    // 设置T代码宏名称和参数
    strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroTname[0].cName, "T.NCC");
    for (int i = 1; i < MACRO_GMT_N; i++) {
        strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroTname[i].cName, "");
    }

    // 设置T代码参数
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroTcode[0].nNcNum = -1;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroTcode[0].nType = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroTcode[0].nGrpNo = 0;
    m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroTcode[0].null = 0;

    for (int i = 1; i < MACRO_GMT_N; i++) {
        m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroTcode[i].nNcNum = 0;
        m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroTcode[i].nType = 0;
        m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroTcode[i].nGrpNo = 0;
        m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroTcode[i].null = 0;
    }

    // 初始化其他M代码和G代码为0
    for (int i = 20; i < MACRO_GMT_N; i++) {
        strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroMname[i].cName, "");
        m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[i].nNcNum = 0;
        m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[i].nType = 0;
        m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[i].nGrpNo = 0;
        m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroMcode[i].null = 0;
    }

    for (int i = 18; i < MACRO_GMT_N; i++) {
        strcpy(m_macroSettingParamPtr[channelIndex].m_shm8Ptr->m_nMacroGname[i].cName, "");
        m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[i].nNcNum = 0;
        m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[i].nType = 0;
        m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[i].nGrpNo = 0;
        m_macroSettingParamPtr[channelIndex].m_shm32Ptr->m_nMacroGcode[i].null = 0;
    }
}

// PublicDataConfig_0.xml
ErrorCode GoogolCncInterface::initGoogolMacroPublicParam(int totalChannelCount) {
    for (int i = 0; i < totalChannelCount; ++i) {
        if (m_macroPublicParamPtr[i].m_shmPtr == nullptr) {
            continue;
        }

#if GOOGOL_DISABLE_FROM_XML
        // 根据PublicDataConfig_0.xml文件硬编码设置宏公共参数
        setDefaultMacroPublicParam(i);
#else
        int ret = GTCR_LoadMacroPublicPara(m_macroPublicParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTCR_LoadMacroPublicPara 加载通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTCR_LoadMacroPublicPara");
        }
#endif
    }
    return ErrorCode::Success;
}

// 根据XML文件硬编码设置宏公共参数的辅助函数
void GoogolCncInterface::setDefaultMacroPublicParam(int channelIndex) {
    // 根据PublicDataConfig_0.xml文件设置宏公共参数
    // 初始化所有用户宏变量为0
    for (int i = 0; i < _PUBLIC_USER_VAR_NUM_; i++) {
        m_macroPublicParamPtr[channelIndex].m_shmPtr->m_dPublicUserVar[i] = 0.0;
    }

    // 根据PublicDataConfig_0.xml文件设置特定的宏变量值
    // XML文件中的D19000-D29999对应#19000-#29999
    // 但m_dPublicUserVar数组对应#500-#29999，所以需要调整索引

    // 设置D19000-D29999对应的宏变量（#19000-#29999）
    // 注意：m_dPublicUserVar[0]对应#500，所以#19000对应索引18500
    for (int i = 18500; i < _PUBLIC_USER_VAR_NUM_; i++) {
        // 根据PublicDataConfig_0.xml文件，所有D19000-D29999的值都是0.000000
        m_macroPublicParamPtr[channelIndex].m_shmPtr->m_dPublicUserVar[i] = 0.0;
    }

    // 如果需要设置其他特定的宏变量值，可以在这里添加
    // 例如：
    // m_macroPublicParamPtr[channelIndex].m_shmPtr->m_dPublicUserVar[0] = 100.0;  // #500
    // m_macroPublicParamPtr[channelIndex].m_shmPtr->m_dPublicUserVar[1] = 200.0;  // #501
    // 等等...
}

ErrorCode GoogolCncInterface::initGoogolScrewPitchCompParam(int totalAxesCount) {
    int ret = 0;
    for (int i = 0; i < totalAxesCount; ++i) {
#if GOOGOL_DISABLE_FROM_XML
        // 硬编码设置螺距补偿参数
        ret = setDefaultScrewPitchCompParam(i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "setDefaultScrewPitchCompParam 设置轴/主轴索引 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "setDefaultScrewPitchCompParam");
        }
#else
        // 螺距补偿参数
        ret = GTCR_LoadScrewPitchComp(m_axisScrewPitchCompPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTCR_LoadScrewPitchComp 加载轴/主轴索引 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTCR_LoadScrewPitchComp");
        }
#endif
    }
    return ErrorCode::Success;
}

int GoogolCncInterface::setDefaultScrewPitchCompParam(int axisIndex) {
    // 获取螺距补偿参数指针
    TAXIS_COMPENSATION_PTR compPtr;
    int ret = GTC_GetPtrScrewPitchComp(compPtr, static_cast<short>(axisIndex));
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_GetPtrScrewPitchComp 获取轴 %d 螺距补偿指针失败，返回码: %d", axisIndex, ret);
        return ret;
    }

    // 检查共享内存指针是否有效
    if (!compPtr.m_shmPtr) {
        HW_LOG_ERROR(m_logger, "轴 %d 螺距补偿共享内存指针无效", axisIndex);
        return -1;
    }

    // 根据XML文件硬编码设置螺距补偿参数
    // 清空所有补偿点
    memset(compPtr.m_shmPtr->m_ComPara, 0, sizeof(compPtr.m_shmPtr->m_ComPara));

    // 根据轴索引设置不同的硬编码补偿数据
    switch (axisIndex) {
        case 0:  // X轴
            setAxis0ScrewPitchCompData(compPtr.m_shmPtr);
            break;
        case 1:  // Y轴
            setAxis1ScrewPitchCompData(compPtr.m_shmPtr);
            break;
        case 2:  // Z轴
            setAxis2ScrewPitchCompData(compPtr.m_shmPtr);
            break;
        case 3:  // A轴
            setAxis3ScrewPitchCompData(compPtr.m_shmPtr);
            break;
        case 4:  // B轴
            setAxis4ScrewPitchCompData(compPtr.m_shmPtr);
            break;
        default:
            // 对于其他轴，设置为默认值（全零）
            HW_LOG_INFO(m_logger, "轴 %d 使用默认螺距补偿参数（全零）", axisIndex);
            break;
    }

    return 0;
}

// 设置轴0（X轴）的螺距补偿数据
void GoogolCncInterface::setAxis0ScrewPitchCompData(COMPENSATION_PTR* compPtr) {
    // 根据AxisScrewPitchComp_0.xml文件硬编码设置X轴的补偿数据
    // 前21个点有实际数据，其余为0

    // Pitch_0
    compPtr->m_ComPara[0].comp[0] = -6.745;    // position
    compPtr->m_ComPara[0].comp[1] = -304.600;  // poserror
    compPtr->m_ComPara[0].comp[2] = -295.333;  // negerror

    // Pitch_1
    compPtr->m_ComPara[1].comp[0] = 18.255;
    compPtr->m_ComPara[1].comp[1] = -303.467;
    compPtr->m_ComPara[1].comp[2] = -285.600;

    // Pitch_2
    compPtr->m_ComPara[2].comp[0] = 43.255;
    compPtr->m_ComPara[2].comp[1] = -287.867;
    compPtr->m_ComPara[2].comp[2] = -283.800;

    // Pitch_3
    compPtr->m_ComPara[3].comp[0] = 68.255;
    compPtr->m_ComPara[3].comp[1] = -286.600;
    compPtr->m_ComPara[3].comp[2] = -271.133;

    // Pitch_4
    compPtr->m_ComPara[4].comp[0] = 93.255;
    compPtr->m_ComPara[4].comp[1] = -278.733;
    compPtr->m_ComPara[4].comp[2] = -263.800;

    // Pitch_5
    compPtr->m_ComPara[5].comp[0] = 118.255;
    compPtr->m_ComPara[5].comp[1] = -264.333;
    compPtr->m_ComPara[5].comp[2] = -259.600;

    // Pitch_6
    compPtr->m_ComPara[6].comp[0] = 143.255;
    compPtr->m_ComPara[6].comp[1] = -249.333;
    compPtr->m_ComPara[6].comp[2] = -232.867;

    // Pitch_7
    compPtr->m_ComPara[7].comp[0] = 168.255;
    compPtr->m_ComPara[7].comp[1] = -218.800;
    compPtr->m_ComPara[7].comp[2] = -214.067;

    // Pitch_8
    compPtr->m_ComPara[8].comp[0] = 193.255;
    compPtr->m_ComPara[8].comp[1] = -195.467;
    compPtr->m_ComPara[8].comp[2] = -181.933;

    // Pitch_9
    compPtr->m_ComPara[9].comp[0] = 218.255;
    compPtr->m_ComPara[9].comp[1] = -168.200;
    compPtr->m_ComPara[9].comp[2] = -150.733;

    // Pitch_10
    compPtr->m_ComPara[10].comp[0] = 243.255;
    compPtr->m_ComPara[10].comp[1] = -145.200;
    compPtr->m_ComPara[10].comp[2] = -136.267;

    // Pitch_11
    compPtr->m_ComPara[11].comp[0] = 268.255;
    compPtr->m_ComPara[11].comp[1] = -136.467;
    compPtr->m_ComPara[11].comp[2] = -117.200;

    // Pitch_12
    compPtr->m_ComPara[12].comp[0] = 293.255;
    compPtr->m_ComPara[12].comp[1] = -111.667;
    compPtr->m_ComPara[12].comp[2] = -89.533;

    // Pitch_13
    compPtr->m_ComPara[13].comp[0] = 318.255;
    compPtr->m_ComPara[13].comp[1] = -93.200;
    compPtr->m_ComPara[13].comp[2] = -73.067;

    // Pitch_14
    compPtr->m_ComPara[14].comp[0] = 343.255;
    compPtr->m_ComPara[14].comp[1] = -74.733;
    compPtr->m_ComPara[14].comp[2] = -55.933;

    // Pitch_15
    compPtr->m_ComPara[15].comp[0] = 368.255;
    compPtr->m_ComPara[15].comp[1] = -55.533;
    compPtr->m_ComPara[15].comp[2] = -43.400;

    // Pitch_16
    compPtr->m_ComPara[16].comp[0] = 393.255;
    compPtr->m_ComPara[16].comp[1] = -44.133;
    compPtr->m_ComPara[16].comp[2] = -23.600;

    // Pitch_17
    compPtr->m_ComPara[17].comp[0] = 418.255;
    compPtr->m_ComPara[17].comp[1] = -28.733;
    compPtr->m_ComPara[17].comp[2] = -19.267;

    // Pitch_18
    compPtr->m_ComPara[18].comp[0] = 443.255;
    compPtr->m_ComPara[18].comp[1] = -25.267;
    compPtr->m_ComPara[18].comp[2] = -6.667;

    // Pitch_19
    compPtr->m_ComPara[19].comp[0] = 468.255;
    compPtr->m_ComPara[19].comp[1] = -8.933;
    compPtr->m_ComPara[19].comp[2] = 3.200;

    // Pitch_20
    compPtr->m_ComPara[20].comp[0] = 493.255;
    compPtr->m_ComPara[20].comp[1] = -0.000;
    compPtr->m_ComPara[20].comp[2] = 10.933;

    // 其余点设置为0（Pitch_21到Pitch_511）
    for (int i = 21; i < SCREWPITCH_COMP_NUM; i++) {
        compPtr->m_ComPara[i].comp[0] = 0.000;  // position
        compPtr->m_ComPara[i].comp[1] = 0.000;  // poserror
        compPtr->m_ComPara[i].comp[2] = 0.000;  // negerror
    }
}

// 设置轴1（Y轴）的螺距补偿数据
void GoogolCncInterface::setAxis1ScrewPitchCompData(COMPENSATION_PTR* compPtr) {
    // 根据AxisScrewPitchComp_1.xml文件硬编码设置Y轴的补偿数据

    // Pitch_0
    compPtr->m_ComPara[0].comp[0] = -92.590;
    compPtr->m_ComPara[0].comp[1] = -0.000;
    compPtr->m_ComPara[0].comp[2] = -1.667;

    // Pitch_1
    compPtr->m_ComPara[1].comp[0] = -82.590;
    compPtr->m_ComPara[1].comp[1] = -14.000;
    compPtr->m_ComPara[1].comp[2] = -6.000;

    // Pitch_2
    compPtr->m_ComPara[2].comp[0] = -72.590;
    compPtr->m_ComPara[2].comp[1] = -16.667;
    compPtr->m_ComPara[2].comp[2] = -8.000;

    // Pitch_3
    compPtr->m_ComPara[3].comp[0] = -62.590;
    compPtr->m_ComPara[3].comp[1] = -18.667;
    compPtr->m_ComPara[3].comp[2] = -9.000;

    // Pitch_4
    compPtr->m_ComPara[4].comp[0] = -52.590;
    compPtr->m_ComPara[4].comp[1] = -20.333;
    compPtr->m_ComPara[4].comp[2] = -11.000;

    // Pitch_5
    compPtr->m_ComPara[5].comp[0] = -42.590;
    compPtr->m_ComPara[5].comp[1] = -22.667;
    compPtr->m_ComPara[5].comp[2] = -7.667;

    // Pitch_6
    compPtr->m_ComPara[6].comp[0] = -32.590;
    compPtr->m_ComPara[6].comp[1] = -26.667;
    compPtr->m_ComPara[6].comp[2] = -11.333;

    // Pitch_7
    compPtr->m_ComPara[7].comp[0] = -22.590;
    compPtr->m_ComPara[7].comp[1] = -31.333;
    compPtr->m_ComPara[7].comp[2] = -11.667;

    // Pitch_8
    compPtr->m_ComPara[8].comp[0] = -12.590;
    compPtr->m_ComPara[8].comp[1] = -35.333;
    compPtr->m_ComPara[8].comp[2] = -29.000;

    // Pitch_9
    compPtr->m_ComPara[9].comp[0] = -2.590;
    compPtr->m_ComPara[9].comp[1] = -38.667;
    compPtr->m_ComPara[9].comp[2] = -30.333;

    // Pitch_10
    compPtr->m_ComPara[10].comp[0] = 7.410;
    compPtr->m_ComPara[10].comp[1] = -35.333;
    compPtr->m_ComPara[10].comp[2] = -23.333;

    // Pitch_11
    compPtr->m_ComPara[11].comp[0] = 17.410;
    compPtr->m_ComPara[11].comp[1] = -31.667;
    compPtr->m_ComPara[11].comp[2] = -25.667;

    // Pitch_12
    compPtr->m_ComPara[12].comp[0] = 27.410;
    compPtr->m_ComPara[12].comp[1] = -28.000;
    compPtr->m_ComPara[12].comp[2] = -21.667;

    // Pitch_13
    compPtr->m_ComPara[13].comp[0] = 37.410;
    compPtr->m_ComPara[13].comp[1] = -23.333;
    compPtr->m_ComPara[13].comp[2] = -16.667;

    // Pitch_14
    compPtr->m_ComPara[14].comp[0] = 47.410;
    compPtr->m_ComPara[14].comp[1] = -18.000;
    compPtr->m_ComPara[14].comp[2] = -12.000;

    // Pitch_15
    compPtr->m_ComPara[15].comp[0] = 57.410;
    compPtr->m_ComPara[15].comp[1] = -13.000;
    compPtr->m_ComPara[15].comp[2] = -8.000;

    // Pitch_16
    compPtr->m_ComPara[16].comp[0] = 67.410;
    compPtr->m_ComPara[16].comp[1] = -7.333;
    compPtr->m_ComPara[16].comp[2] = -4.667;

    // Pitch_17
    compPtr->m_ComPara[17].comp[0] = 77.410;
    compPtr->m_ComPara[17].comp[1] = -2.000;
    compPtr->m_ComPara[17].comp[2] = 1.000;

    // Pitch_18
    compPtr->m_ComPara[18].comp[0] = 87.410;
    compPtr->m_ComPara[18].comp[1] = 1.333;
    compPtr->m_ComPara[18].comp[2] = 4.667;

    // 其余点设置为0
    for (int i = 19; i < SCREWPITCH_COMP_NUM; i++) {
        compPtr->m_ComPara[i].comp[0] = 0.000;
        compPtr->m_ComPara[i].comp[1] = 0.000;
        compPtr->m_ComPara[i].comp[2] = 0.000;
    }
}

// 设置轴2（Z轴）的螺距补偿数据
void GoogolCncInterface::setAxis2ScrewPitchCompData(COMPENSATION_PTR* compPtr) {
    // 根据AxisScrewPitchComp_2.xml文件硬编码设置Z轴的补偿数据
    // 这里设置为默认值，实际使用时需要根据具体的XML文件数据

    // 所有点设置为0（默认值）
    for (int i = 0; i < SCREWPITCH_COMP_NUM; i++) {
        compPtr->m_ComPara[i].comp[0] = 0.000;  // position
        compPtr->m_ComPara[i].comp[1] = 0.000;  // poserror
        compPtr->m_ComPara[i].comp[2] = 0.000;  // negerror
    }
}

// 设置轴3（A轴）的螺距补偿数据
void GoogolCncInterface::setAxis3ScrewPitchCompData(COMPENSATION_PTR* compPtr) {
    // 根据AxisScrewPitchComp_3.xml文件硬编码设置A轴的补偿数据
    // 这里设置为默认值，实际使用时需要根据具体的XML文件数据

    // 所有点设置为0（默认值）
    for (int i = 0; i < SCREWPITCH_COMP_NUM; i++) {
        compPtr->m_ComPara[i].comp[0] = 0.000;  // position
        compPtr->m_ComPara[i].comp[1] = 0.000;  // poserror
        compPtr->m_ComPara[i].comp[2] = 0.000;  // negerror
    }
}

// 设置轴4（B轴）的螺距补偿数据
void GoogolCncInterface::setAxis4ScrewPitchCompData(COMPENSATION_PTR* compPtr) {
    // 根据AxisScrewPitchComp_4.xml文件硬编码设置B轴的补偿数据
    // 这里设置为默认值，实际使用时需要根据具体的XML文件数据

    // 所有点设置为0（默认值）
    for (int i = 0; i < SCREWPITCH_COMP_NUM; i++) {
        compPtr->m_ComPara[i].comp[0] = 0.000;  // position
        compPtr->m_ComPara[i].comp[1] = 0.000;  // poserror
        compPtr->m_ComPara[i].comp[2] = 0.000;  // negerror
    }
}

ErrorCode GoogolCncInterface::initGoogolWorkCoordParam(void) {
// 根据CoordOffset.xml文件硬编码设置工件坐标系参数
#if GOOGOL_DISABLE_FROM_XML
    // 检查共享内存指针是否有效
    if (!m_coordParamPtr.m_shmPtr) {
        HW_LOG_ERROR(m_logger, "工件坐标系共享内存指针无效");
        return ErrorCode::InternalError;
    }

    // 清空所有坐标数据
    memset(&m_coordParamPtr.m_shmPtr->m_OffSetCoord, 0, sizeof(m_coordParamPtr.m_shmPtr->m_OffSetCoord));
    memset(&m_coordParamPtr.m_shmPtr->m_sysCoord, 0, sizeof(m_coordParamPtr.m_shmPtr->m_sysCoord));
    memset(&m_coordParamPtr.m_shmPtr->m_userCoord, 0, sizeof(m_coordParamPtr.m_shmPtr->m_userCoord));
    memset(&m_coordParamPtr.m_shmPtr->m_appendCoord, 0, sizeof(m_coordParamPtr.m_shmPtr->m_appendCoord));

    // 设置偏移坐标 (m_OffSetCoord)
    m_coordParamPtr.m_shmPtr->m_OffSetCoord.pos[0] = 0.000;
    m_coordParamPtr.m_shmPtr->m_OffSetCoord.pos[1] = 0.000;
    m_coordParamPtr.m_shmPtr->m_OffSetCoord.pos[2] = 200.000;  // Z轴偏移200mm
    for (int i = 3; i < 32; i++) {
        m_coordParamPtr.m_shmPtr->m_OffSetCoord.pos[i] = 0.000;
    }

    // 设置系统坐标 (m_sysCoord) - 根据XML文件数据
    // G54 (index 0)
    m_coordParamPtr.m_shmPtr->m_sysCoord[0].pos[0] = 599.951200;
    m_coordParamPtr.m_shmPtr->m_sysCoord[0].pos[1] = -288.298900;
    m_coordParamPtr.m_shmPtr->m_sysCoord[0].pos[2] = 156.300000;
    for (int i = 3; i < 32; i++) {
        m_coordParamPtr.m_shmPtr->m_sysCoord[0].pos[i] = 0.000;
    }

    // G55 (index 1)
    m_coordParamPtr.m_shmPtr->m_sysCoord[1].pos[0] = 287.197200;
    m_coordParamPtr.m_shmPtr->m_sysCoord[1].pos[1] = -288.224300;
    m_coordParamPtr.m_shmPtr->m_sysCoord[1].pos[2] = -150.516800;
    for (int i = 3; i < 32; i++) {
        m_coordParamPtr.m_shmPtr->m_sysCoord[1].pos[i] = 0.000;
    }

    // G56 (index 2)
    m_coordParamPtr.m_shmPtr->m_sysCoord[2].pos[0] = 509.531100;
    m_coordParamPtr.m_shmPtr->m_sysCoord[2].pos[1] = -205.121800;
    m_coordParamPtr.m_shmPtr->m_sysCoord[2].pos[2] = -29.384000;
    for (int i = 3; i < 32; i++) {
        m_coordParamPtr.m_shmPtr->m_sysCoord[2].pos[i] = 0.000;
    }

    // G57 (index 3) - 全部为0
    for (int i = 0; i < 32; i++) {
        m_coordParamPtr.m_shmPtr->m_sysCoord[3].pos[i] = 0.000;
    }

    // G58 (index 4)
    m_coordParamPtr.m_shmPtr->m_sysCoord[4].pos[0] = 0.000;
    m_coordParamPtr.m_shmPtr->m_sysCoord[4].pos[1] = 0.000;
    m_coordParamPtr.m_shmPtr->m_sysCoord[4].pos[2] = -16.121800;
    for (int i = 3; i < 32; i++) {
        m_coordParamPtr.m_shmPtr->m_sysCoord[4].pos[i] = 0.000;
    }

    // G59 (index 5) - 全部为0
    for (int i = 0; i < 32; i++) {
        m_coordParamPtr.m_shmPtr->m_sysCoord[5].pos[i] = 0.000;
    }

    // 其他系统坐标 (index 6-21) - 全部为0
    for (int coordIndex = 6; coordIndex < 22; coordIndex++) {
        for (int i = 0; i < 32; i++) {
            m_coordParamPtr.m_shmPtr->m_sysCoord[coordIndex].pos[i] = 0.000;
        }
    }

    // 用户坐标 (m_userCoord) - 全部为0
    for (int coordIndex = 0; coordIndex < 10; coordIndex++) {
        for (int i = 0; i < 32; i++) {
            m_coordParamPtr.m_shmPtr->m_userCoord[coordIndex].pos[i] = 0.000;
        }
    }

    // 附加坐标 (m_appendCoord) - 全部为0
    for (int coordIndex = 0; coordIndex < 63; coordIndex++) {
        for (int i = 0; i < 32; i++) {
            m_coordParamPtr.m_shmPtr->m_appendCoord[coordIndex].pos[i] = 0.000;
        }
    }

    HW_LOG_INFO(m_logger, "工件坐标系参数硬编码设置完成");
#else
    short ret = GTCR_LoadCoordPara(m_coordParamPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTCR_LoadCoordPara 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTCR_LoadCoordPara");
    }
#endif

    return ErrorCode::Success;
}

ErrorCode GoogolCncInterface::initCncSystem(const std::string& errorCfgFilePath) {
    short ret = GTC_InitShareMemParamPtr();
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_InitShareMemParamPtr 失败，返回码: %d", ret);
        return ErrorCode::InternalError;
    }

    // 2. 初始化调试模块
    if (initDebugModuleInternal(errorCfgFilePath) != ErrorCode::Success) {
        HW_LOG_ERROR(m_logger, "initDebugModuleInternal 失败");
        return ErrorCode::InternalError;
    }

    // //系统参数
    ret = GTC_GetPtrSysPara(m_sysParamPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_GetPtrSysPara 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_GetPtrSysPara");
    }

    if (initGoogolSystemParam() != ErrorCode::Success) {
        return ErrorCode::InternalError;
    }

    if (m_sysParamPtr.m_shm32Ptr == nullptr) {
        HW_LOG_ERROR(m_logger, "m_sysParamPtr.m_shm32Ptr 在 GTC_GetPtrSysPara 和 GTCR_LoadSysPara 后为空。");
        return ErrorCode::InternalError;
    }
    HW_LOG_INFO(m_logger, "系统参数共享内存指针 (m_shm32Ptr) 有效。");

    // m_loadedAxesCount = m_sysParamPtr.m_shm32Ptr->m_SysAxisNum;         // 直线轴数
    // m_loadedSpindlesCount = m_sysParamPtr.m_shm32Ptr->m_SysSpindleNum;  // 主轴数
    m_loadedChannelsCount = m_sysParamPtr.m_shm32Ptr->m_SysChannelNum;

    int totalAxesCount = m_sysParamPtr.m_shm32Ptr->m_SysAxisNum + m_sysParamPtr.m_shm32Ptr->m_SysSpindleNum;

    HW_LOG_INFO(m_logger, "直线轴数: %d, 主轴数: %d, 通道数: %d", m_sysParamPtr.m_shm32Ptr->m_SysAxisNum,
                m_sysParamPtr.m_shm32Ptr->m_SysSpindleNum, m_loadedChannelsCount);

    // 轴配置
    ret = GTC_GetPtrAxesConfig(m_axesCfgPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_GetPtrAxesConfig 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_GetPtrAxesConfig");
    }

    // 轴控制参数
    for (int i = 0; i < totalAxesCount; i++) {
        ret = GTC_GetPtrAxisPara(m_axisParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrAxisPara 获取轴/主轴索引 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrAxisPara");
        }
    }

    for (int i = 0; i < MAX_CHANNEL_NUM; i++) {
        ret = GTC_GetPtrMotionPara(m_motionParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrMotionPara 获取通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrMotionPara");
        }
    }

    for (int i = 0; i < totalAxesCount; i++) {
        ret = GTC_GetPtrScrewPitchComp(m_axisScrewPitchCompPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrScrewPitchComp 获取轴/主轴索引 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrScrewPitchComp");
        }
    }

    for (int i = 0; i < MAX_CHANNEL_NUM; i++) {
        ret = GTC_GetPtrToolPara(m_toolsParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrToolPara 获取通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrToolPara");
        }
    }

    for (int i = 0; i < m_loadedChannelsCount; ++i) {
        // 运动参数
        ret = GTC_GetPtrMotionPara(m_motionParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrMotionPara 获取通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrMotionPara");
        }
        // ... (repeat for GCode, G5_1, Tool, MacroPublic, MacroSetting parameters) ...
        // //工艺参数
        ret = GTC_GetPtrGCodePara(m_gcodeParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrGCodePara 获取通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrGCodePara");
        }
        // 高速高精参数
        ret = GTC_GetPtrG5_1Para(m_tg51ParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrG5_1Para 获取通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrG5_1Para");
        }
        // 刀具参数
        ret = GTC_GetPtrToolPara(m_toolsParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrToolPara 获取通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrToolPara");
        }
        // 宏变量
        ret = GTC_GetPtrMacroPublicPara(m_macroPublicParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrMacroPublicPara 获取通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrMacroPublicPara");
        }
        // 宏设置参数
        ret = GTC_GetPtrMacroSettingPara(m_macroSettingParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrMacroSettingPara 获取通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrMacroSettingPara");
        }
        // NC 通道输出参数
        ret = GTC_GetPtrNcChannelOut(m_ncChannelOutPtrArr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrNcChannelOut 获取通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrNcChannelOut");
        }
    }

    // 坐标参数
    ret = GTC_GetPtrCoordPara(m_coordParamPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_GetPtrCoordPara 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_GetPtrCoordPara");
    }

    if (initGoogolWorkCoordParam() != ErrorCode::Success) {
        return ErrorCode::InternalError;
    }

    ret = GTC_GetPtrNcOut(m_ncOutPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_GetPtrNcOut 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_GetPtrNcOut");
    }

    // HMI输出参数
    ret = GTC_GetPtrHmiOut(m_hmiOutPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_GetPtrHmiOut 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_GetPtrHmiOut");
    }

    // PLC 输出参数
    ret = GTC_GetPtrPlcOut(m_plcOutPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_GetPtrPlcOut 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_GetPtrPlcOut");
    }

    // PLC:X\Y\F\G\D变量
    ret = GTC_GetPtrPlcPara(m_plcPara);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_GetPtrPlcPara 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_GetPtrPlcPara");
    }
    // PLC：F变量参数
    m_plcFPara = (NC2PLC_PARA_F*)m_plcPara.m_shm16Ptr->F;
    // PLC：G变量参数
    m_plcGPara = (PLC2NC_PARA_G*)m_plcPara.m_shm16Ptr->G;

#if 0
	//WATCH参数定义
	GTC_GetPtrWatchDefine(g_WatchParaDef);
	//g_pWatchParaDefHMI = g_WatchParaDef.m_shmPtr;

	//WATCH参数配置
	GTC_GetPtrWatchCfg(g_WatchParaCfg);
	//g_pWatchParaCfgHMI = g_WatchParaCfg.m_shmPtr;
#endif

    if (initGoogolAxisConfig() != ErrorCode::Success) {
        return ErrorCode::InternalError;
    }

    if (initGoogolMotionParam(m_loadedChannelsCount) != ErrorCode::Success) {
        return ErrorCode::InternalError;
    }

    // 可能会崩溃，如果超出了范围
    if (initGoogolAxisParam(totalAxesCount) != ErrorCode::Success) {
        return ErrorCode::InternalError;
    }

    if (initGoogolGCodeParam(m_loadedChannelsCount) != ErrorCode::Success) {
        return ErrorCode::InternalError;
    }

    if (initGoogolG5_1Param(m_loadedChannelsCount) != ErrorCode::Success) {
        return ErrorCode::InternalError;
    }

    if (initGoogolToolParam(m_loadedChannelsCount) != ErrorCode::Success) {
        return ErrorCode::InternalError;
    }

    if (initGoogolMacroPublicParam(m_loadedChannelsCount) != ErrorCode::Success) {
        return ErrorCode::InternalError;
    }

    if (initGoogolMacroSettingParam(m_loadedChannelsCount) != ErrorCode::Success) {
        return ErrorCode::InternalError;
    }

    if (initGoogolScrewPitchCompParam(totalAxesCount) != ErrorCode::Success) {
        return ErrorCode::InternalError;
    }

    // 初始化配置
    std::vector<ConfigCategory> rootConfigCategories;
    if (getConfiguration(rootConfigCategories) != ErrorCode::Success) {
        HW_LOG_ERROR(m_logger, "getConfiguration 失败");
        return ErrorCode::InternalError;
    }

    // TODO: 临时编写，待谭飞重构
    if (1) {
        for (int i = 0; i < m_loadedChannelsCount; ++i) {
            if (!m_gcodeParamPtr[i].m_shm8Ptr) {
                continue;
            }

            // 0: 半径编程，1：直径编程
            m_gcodeParamPtr[i].m_shm8Ptr->iInitGroupG10_9 = 0;
        }
    }

    return ErrorCode::Success;
}
