#pragma once

#include <map>
#include <mutex>
#include <nlohmann/json.hpp>
#include <string>
#include <vector>

#include "IAppLogger.h"
#include "ICncInterface.h"

// 前向声明
class GoogolCncInterface;

/**
 * @brief 简化的刀具管理器
 *
 * 负责管理所有刀具相关功能，包括：
 * 1. 刀具数据管理（增删改查）
 * 2. H映射表管理
 * 3. 刀具参数同步到SDK
 * 4. 缓存管理优化
 */
class GoogolToolManager {
   public:
    /**
     * @brief 构造函数
     * @param interface GoogolCncInterface的引用
     */
    explicit GoogolToolManager(GoogolCncInterface& interface);

    /**
     * @brief 析构函数
     */
    ~GoogolToolManager() = default;

    // 禁用拷贝构造和赋值
    GoogolToolManager(const GoogolToolManager&) = delete;
    GoogolToolManager& operator=(const GoogolToolManager&) = delete;

    /**
     * @brief 初始化刀具管理器
     * @return ErrorCode 操作结果
     */
    ErrorCode initialize();

    // === 刀具数据管理接口 ===

    /**
     * @brief 获取当前主轴刀具信息
     * @param channelId 通道ID
     * @param toolInfo 输出的刀具信息
     * @return ErrorCode 操作结果
     */
    ErrorCode getCurrentToolInfo(int channelId, ToolInfo& toolInfo);

    /**
     * @brief 根据UUID获取刀具参数
     * @param uuid 刀具UUID
     * @param toolInfo 输出的刀具信息
     * @return ErrorCode 操作结果
     */
    ErrorCode getToolParameters(const std::string& uuid, ToolInfo& toolInfo);

    /**
     * @brief 设置或更新刀具参数。
     * UUID必须由调用者提供：若 UUID 存在则更新现有刀具；若 UUID 不存在则创建新刀具。
     * @param[in] toolInfo 包含刀具参数的结构体，UUID必须由调用者指定，不能为空。
     * @return ErrorCode 指示操作成功或失败。UUID为空时返回 InvalidParam。
     */
    ErrorCode setToolParameters(const ToolInfo& toolInfo);

    /**
     * @brief 删除刀具
     * @param uuid 刀具UUID
     * @return ErrorCode 操作结果
     */
    ErrorCode deleteTool(const std::string& uuid);

    /**
     * @brief 获取所有刀具参数
     * @param allToolsInfo 输出的所有刀具信息
     * @return ErrorCode 操作结果
     */
    ErrorCode getAllToolParameters(std::map<std::string, ToolInfo>& allToolsInfo);

    // === 刀库管理接口 ===

    /**
     * @brief 获取刀位中的刀具信息
     * @param toolChangerId 刀库ID
     * @param pocketNumber 刀位号
     * @param toolInfo 输出的刀具信息
     * @return ErrorCode 操作结果
     */
    ErrorCode getToolInfoInPocket(int toolChangerId, int pocketNumber, ToolInfo& toolInfo);

    /**
     * @brief 将刀具装载到刀位
     * @param toolChangerId 刀库ID
     * @param pocketNumber 刀位号
     * @param toolNumber 刀号
     * @return ErrorCode 操作结果
     */
    ErrorCode loadToolIntoPocket(int toolChangerId, int pocketNumber, int toolNumber);

    /**
     * @brief 从刀位卸载刀具
     * @param toolChangerId 刀库ID
     * @param pocketNumber 刀位号
     * @param unloadedToolNumber 输出卸载的刀号
     * @return ErrorCode 操作结果
     */
    ErrorCode unloadToolFromPocket(int toolChangerId, int pocketNumber, int& unloadedToolNumber);

    /**
     * @brief 交换两个刀位中的刀具
     * @param toolChangerId 刀库ID
     * @param pocketNumber1 刀位号1
     * @param pocketNumber2 刀位号2
     * @return ErrorCode 操作结果
     */
    ErrorCode exchangeToolsInPockets(int toolChangerId, int pocketNumber1, int pocketNumber2);

    /**
     * @brief 获取刀库中的所有刀具
     * @param toolChangerId 刀库ID
     * @param toolsInMagazine 输出的刀具列表
     * @return ErrorCode 操作结果
     */
    ErrorCode getToolsByMagazine(int toolChangerId, std::vector<ToolInfo>& toolsInMagazine);

    /**
     * @brief 将刀具从刀位移动到主轴
     * @param channelId 通道ID
     * @param toolChangerId 刀库ID
     * @param pocketNumber 刀位号
     * @param spindleIndex 主轴索引
     * @return ErrorCode 操作结果
     */
    ErrorCode moveToolFromPocketToSpindle(int channelId, int toolChangerId, int pocketNumber, int spindleIndex);

    /**
     * @brief 将刀具从主轴移动到刀位
     * @param channelId 通道ID
     * @param spindleIndex 主轴索引
     * @param toolChangerId 刀库ID
     * @param pocketNumber 刀位号
     * @param movedToPocketToolNumber 输出移动到刀位的刀号
     * @return ErrorCode 操作结果
     */
    ErrorCode moveToolFromSpindleToPocket(int channelId, int spindleIndex, int toolChangerId, int pocketNumber,
                                          int* movedToPocketToolNumber);

    /**
     * @brief 获取刀库状态
     * @param toolChangerId 刀库ID
     * @param pocketStatuses 输出的刀位状态列表
     * @return ErrorCode 操作结果
     */
    ErrorCode getMagazineStatus(int toolChangerId, std::vector<PocketStatus>& pocketStatuses);

    /**
     * @brief 获取系统刀库配置信息
     * @param toolChangersConfigs 输出的刀库配置列表
     * @return ErrorCode 操作结果
     */
    ErrorCode getToolChangersConfigs(std::vector<ToolChangerConfig>& toolChangersConfigs);

    // === H映射表管理接口 ===

    /**
     * @brief 查找刀具对应的H号
     * @param toolNumber 刀号
     * @param dNumber 刀沿号
     * @return int H号（0表示未找到）
     */
    int findHNumberForTool(int toolNumber, int dNumber);

    /**
     * @brief 根据H号获取刀具信息
     * @param hNumber H号
     * @param toolInfo 输出的刀具信息
     * @return ErrorCode 操作结果
     */
    ErrorCode getToolInfoByHNumber(int hNumber, ToolInfo& toolInfo);

    // === 主轴状态管理接口 ===

    /**
     * @brief 更新主轴刀号状态并同步刀具激活状态
     * @param channelId 通道ID
     * @return ErrorCode 操作结果
     */
    ErrorCode updateSpindleToolStatus(int channelId);

    /**
     * @brief 检查并更新所有通道的主轴刀号状态
     * @return ErrorCode 操作结果
     */
    ErrorCode checkAndUpdateAllSpindleToolStatus();

    // === 配置管理接口 ===

    /**
     * @brief 保存刀具数据到配置文件
     * @return ErrorCode 操作结果
     */
    ErrorCode saveToolDataToConfig();

   private:
    GoogolCncInterface& m_interface;  // CNC接口引用
    mutable std::mutex m_toolMutex;   // 刀具数据访问锁
    bool m_isInitialized;             // 初始化状态

    // 刀具参数存储
    std::map<std::string, ToolInfo> m_toolParametersByUuid;   // 主要存储：按UUID索引的刀具参数
    std::multimap<int, std::string> m_toolNumberToUuidIndex;  // 辅助索引：刀号到UUID的映射

    // H号映射缓存（用空间换时间的优化）
    std::map<int, std::string> m_hNumberToUuidCache;    // H号到刀具UUID的直接映射
    std::map<std::string, int> m_uuidToHNumberCache;    // 刀具UUID到H号的反向映射
    std::map<int, int> m_hNumberToToolNumberAndDCache;  // H号到刀号+刀沿组合值的缓存

    // 刀库基本配置（从配置文件或宏变量读取）
    struct MagazineConfig {
        int id = 0;
        std::string name = "主刀库";
        int capacity = 25;
    };
    std::map<int, MagazineConfig> m_magazineConfigs;  // 刀库基本配置信息

    // 主轴刀号状态跟踪
    std::map<int, int> m_currentSpindleToolNumbers;  // 每个通道的当前主轴刀号

    // === 内部辅助方法 ===

    /**
     * @brief 获取当前主轴刀具信息（无锁版本，供内部调用）
     * @param channelId 通道ID
     * @param toolInfo 输出的刀具信息
     * @return ErrorCode 操作结果
     * @note 调用此方法前必须已经持有 m_toolMutex 锁
     */
    ErrorCode getCurrentToolInfoNoLock(int channelId, ToolInfo& toolInfo);

    /**
     * @brief 查找刀具对应的H号（无锁版本，供内部调用）
     * @param toolNumber 刀号
     * @param dNumber 刀沿号
     * @return int H号（0表示未找到）
     * @note 调用此方法前必须已经持有 m_toolMutex 锁
     */
    int findHNumberForToolNoLock(int toolNumber, int dNumber);

    /**
     * @brief 根据H号获取刀具信息（无锁版本，供内部调用）
     * @param hNumber H号
     * @param toolInfo 输出的刀具信息
     * @return ErrorCode 操作结果
     * @note 调用此方法前必须已经持有 m_toolMutex 锁
     */
    ErrorCode getToolInfoByHNumberNoLock(int hNumber, ToolInfo& toolInfo);

    /**
     * @brief 更新主轴刀号状态并同步刀具激活状态（无锁版本，供内部调用）
     * @param channelId 通道ID
     * @return ErrorCode 操作结果
     * @note 调用此方法前必须已经持有 m_toolMutex 锁
     */
    ErrorCode updateSpindleToolStatusNoLock(int channelId);

    /**
     * @brief 生成新的UUID
     * @return std::string 新的UUID
     */
    std::string generateUuid();

    /**
     * @brief 更新刀号索引
     * @param toolInfo 刀具信息
     */
    void updateToolNumberIndex(const ToolInfo& toolInfo);

    /**
     * @brief 从刀号索引中移除
     * @param uuid 刀具UUID
     */
    void removeFromToolNumberIndex(const std::string& uuid);

    /**
     * @brief 清空H号映射缓存
     */
    void clearHMappingCache();

    /**
     * @brief 打印H映射表用于调试
     */
    void printHMappingTable();

    // === H映射表管理（内部方法） ===

    /**
     * @brief 初始化H映射表
     */
    void initializeHMappingTable();

    /**
     * @brief 更新H映射表
     */
    void updateHMappingTable();

    /**
     * @brief 同步H映射表到宏变量
     * @return ErrorCode 操作结果
     */
    ErrorCode syncHMappingToMacroVars();

    // === 系统初始化（内部方法） ===

    /**
     * @brief 初始化系统刀库
     */
    void initializeSystemMagazine();

    /**
     * @brief 从配置文件加载刀库配置
     * @return ErrorCode 操作结果
     */
    ErrorCode loadMagazineConfigFromFile();

    /**
     * @brief 初始化刀具数据
     */
    void initializeToolData();

    /**
     * @brief 从配置文件加载刀具数据
     * @return ErrorCode 操作结果
     */
    ErrorCode loadToolDataFromConfig();

    /**
     * @brief 从默认配置文件加载刀具数据
     * @return ErrorCode 操作结果
     */
    ErrorCode loadDefaultToolsFromConfigFile();

    /**
     * @brief 按H号索引设置刀补数据到ToolPara数组
     * @param channelId 通道ID
     * @param hIndex H号索引（从0开始）
     * @param toolInfo 刀具信息
     * @return ErrorCode 操作结果
     */
    ErrorCode setToolToDeviceByHIndex(int channelId, int hIndex, const ToolInfo& toolInfo);

    /**
     * @brief 获取刀具数据配置文件路径
     * @return std::string 配置文件路径
     */
    std::string getToolDataConfigPath();

    /**
     * @brief 获取默认刀具配置文件路径
     * @return std::string 配置文件路径
     */
    std::string getDefaultToolsFilePath();

    /**
     * @brief 保存刀具数据到配置文件（不获取锁的版本）
     * @return ErrorCode 操作结果
     */
    ErrorCode saveToolDataToConfigNoLock();

    /**
     * @brief 从JSON对象创建ToolInfo
     * @param jsonObj JSON对象
     * @return ToolInfo 刀具信息
     */
    ToolInfo createToolInfoFromJson(const nlohmann::json& jsonObj);

    /**
     * @brief 将ToolInfo转换为JSON对象
     * @param toolInfo 刀具信息
     * @return nlohmann::json JSON对象
     */
    nlohmann::json createJsonFromToolInfo(const ToolInfo& toolInfo);

    /**
     * @brief 验证刀具信息的有效性
     * @param toolInfo 刀具信息
     * @return bool 是否有效
     */
    bool validateToolInfo(const ToolInfo& toolInfo);

    /**
     * @brief 更新刀具寿命数据
     * @param uuid 刀具UUID
     * @param usageSeconds 使用时间（秒）
     * @param usageCount 使用次数
     */
    void updateToolLifeData(const std::string& uuid, double usageSeconds, int usageCount);
};
