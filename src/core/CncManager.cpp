/**
 * @file CncManager.cpp
 * @brief CNC管理器实现文件
 */

#include "CncManager.h"

#include <QApplication>
#include <QDateTime>
#include <QMutexLocker>
#include <QThread>
#include <QTimer>
#include <sstream>

#include "ErrorCode.h"
#include "core/AppLogger.h"
#include "hardware/HardwareFactory.h"

// 静态成员初始化
CncManager* CncManager::s_instance = nullptr;
QMutex CncManager::s_instanceMutex;

// --- CncManager 实现 ---

CncManager::CncManager()
    : QObject(nullptr), m_cncInterface(nullptr), m_systemState(CncSystemState::Unknown), m_initialized(false) {
    LOG_INFO("CNC管理器已创建");

    // 使用排队连接，确保 processQueuedCncEvent 在 CncManager 的线程中执行
    connect(this, &CncManager::eventReceived, this, &CncManager::processQueuedCncEvent, Qt::QueuedConnection);
}

CncManager::~CncManager() {
    LOG_INFO("CNC管理器正在销毁");

    // 关闭接口
    if (m_initialized) {
        shutdown();
    }
}

CncManager* CncManager::getInstance() {
    QMutexLocker locker(&s_instanceMutex);
    if (!s_instance) {
        s_instance = new CncManager();
    }
    return s_instance;
}

// --- 生命周期管理 ---

ErrorCode CncManager::initialize(const std::string& configPath, const std::string& writablePath) {
    QMutexLocker locker(&m_dataMutex);

    if (m_initialized) {
        LOG_WARN("CNC接口已经初始化，请勿重复调用");
        return ErrorCode::AlreadyInitialized;
    }

    try {
        LOG_INFO("正在创建CNC接口实例...");

        // 创建CNC接口实例
        m_cncInterface = createCncInterface();
        if (!m_cncInterface) {
            LOG_ERROR("无法创建CNC接口实例！");
            return ErrorCode::InternalError;
        }

        LOG_INFO("正在初始化CNC接口...");

        // 创建初始化参数
        CncInitializationParams params(configPath, writablePath, &AppLogger::getInstance());

        // 初始化接口
        ErrorCode result = m_cncInterface->initialize(params);
        if (result != ErrorCode::Success) {
            LOG_WARN("CNC接口初始化失败：{}", static_cast<int>(result));
            m_cncInterface.reset();
            return result;
        }

        // 注册事件监听器
        result = m_cncInterface->registerEventListener(this);
        if (result != ErrorCode::Success) {
            LOG_WARN("注册事件监听器失败：{}", static_cast<int>(result));
            // 即使事件监听器注册失败，接口本身仍可用，继续初始化
        }

        // 更新系统配置
        updateSystemConfig();

        m_initialized = true;
        LOG_INFO("CNC接口初始化成功");

        // 打印系统信息
        printSystemInfo();

        return ErrorCode::Success;

    } catch (const std::exception& e) {
        LOG_ERROR("CNC接口初始化异常：{}", e.what());
        m_cncInterface.reset();
        return ErrorCode::InternalError;
    }
}

ErrorCode CncManager::shutdown() {
    QMutexLocker locker(&m_dataMutex);

    if (!m_initialized) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = ErrorCode::Success;

    if (m_cncInterface) {
        // 注销事件监听器
        m_cncInterface->unregisterEventListener(this);

        // 关闭接口
        result = m_cncInterface->shutdown();
        if (result != ErrorCode::Success) {
            LOG_WARN("CNC接口关闭失败：{}", static_cast<int>(result));
        }
    }

    LOG_INFO("CNC接口销毁");
    // FIXME: 这里reset将会死锁！！
    // m_cncInterface.reset();
    m_initialized = false;
    LOG_INFO("CNC接口已关闭");
    return result;
}

bool CncManager::isInitialized() const {
    QMutexLocker locker(&m_dataMutex);
    return m_initialized;
}

// --- 状态获取 ---
const SystemConfig& CncManager::getSystemConfig() const {
    QMutexLocker locker(&m_dataMutex);
    return m_systemConfig;
}

CncSystemState CncManager::getSystemState() const {
    QMutexLocker locker(&m_dataMutex);
    return m_systemState;
}

OperatingMode CncManager::getOperatingMode(int channelId) const {
    QMutexLocker locker(&m_dataMutex);
    auto it = m_operatingModes.find(channelId);
    return (it != m_operatingModes.end()) ? it->second : OperatingMode::Unknown;
}

ErrorCode CncManager::setOperatingMode(int channelId, OperatingMode mode) {
    QMutexLocker locker(&m_dataMutex);
    return m_cncInterface->setOperatingMode(channelId, mode);
}

ErrorCode CncManager::getLastError(std::string& errorMsg) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getLastError(errorMsg);
}

const ProgramStatus& CncManager::getProgramStatus(int channelId) const {
    QMutexLocker locker(&m_dataMutex);
    auto it = m_programStatuses.find(channelId);
    static ProgramStatus defaultStatus;
    return (it != m_programStatuses.end()) ? it->second : defaultStatus;
}

ErrorCode CncManager::getActiveGFunctionList(int channelId, std::vector<ActiveGFunction>& functions) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getActiveGFunctionList(channelId, functions);
}

// --- 坐标与位置 ---

ErrorCode CncManager::getWorkPosition(int channelId, PointXD& position, bool isReadFromCNC) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getWorkPosition(channelId, position, isReadFromCNC);
}

ErrorCode CncManager::getMachinePosition(int channelId, PointXD& position) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getMachinePosition(channelId, position);
}

// --- 进给与主轴 ---

ErrorCode CncManager::getActualFeedrate(int channelId, double& feedrate) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getActualFeedrate(channelId, feedrate);
}

ErrorCode CncManager::setFeedOverride(int channelId, double overridePercent) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->setFeedOverride(channelId, overridePercent);
    if (result == ErrorCode::Success) {
        LOG_INFO("设置进给倍率成功：通道 {}，倍率 {}%", channelId, overridePercent);
    } else {
        LOG_WARN("设置进给倍率失败：通道 {}，错误码 {}", channelId, static_cast<int>(result));
    }

    return result;
}

ErrorCode CncManager::getActualSpindleSpeed(int channelId, int spindleIndex, double& speedRpm) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getActualSpindleSpeed(channelId, spindleIndex, speedRpm);
}

ErrorCode CncManager::getCommandFeedrate(int channelId, double& feedrate) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getCommandFeedrate(channelId, feedrate);
}

ErrorCode CncManager::getFeedOverride(int channelId, double& overridePercent) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getFeedOverride(channelId, overridePercent);
}

ErrorCode CncManager::getCommandSpindleSpeed(int channelId, int spindleIndex, double& speedRpm) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getCommandSpindleSpeed(channelId, spindleIndex, speedRpm);
}

ErrorCode CncManager::getSpindleOverride(int channelId, double& overridePercent) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getSpindleOverride(channelId, overridePercent);
}

ErrorCode CncManager::isSpindleOn(int channelId, int spindleIndex, bool& isOn) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->isSpindleOn(channelId, spindleIndex, isOn);
}

// --- 程序执行 ---

ErrorCode CncManager::loadProgram(int channelId, const std::string& filePath) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->loadProgram(channelId, filePath);
    if (result == ErrorCode::Success) {
        LOG_INFO("程序加载成功：通道 {}，文件 {}", channelId, filePath);
    } else {
        LOG_WARN("程序加载失败：通道 {}，文件 {}，错误码 {}", channelId, QString::fromStdString(filePath).toStdString(),
                 static_cast<int>(result));
        emitError(result, tr("程序加载失败"));
    }

    return result;
}

ErrorCode CncManager::compileProgram(int channelId, const std::string filePath, std::vector<GMotion>& gMotions) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->compileProgram(channelId, filePath, gMotions);
    if (result == ErrorCode::Success) {
        LOG_INFO("编译启动成功：通道 {}", channelId);
    } else {
        std::string errorMsg;

        this->getLastError(errorMsg);
        LOG_WARN("编译启动失败：通道 {}，错误码 {}", channelId, errorMsg);
        emitError(result, tr("编译启动失败"));
    }

    return result;
}

ErrorCode CncManager::startProgram(int channelId) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->startProgram(channelId);
    if (result == ErrorCode::Success) {
        LOG_INFO("程序启动成功：通道 {}", channelId);
    } else {
        LOG_WARN("程序启动失败：通道 {}，错误码 {}", channelId, static_cast<int>(result));
        emitError(result, tr("程序启动失败"));
    }

    return result;
}

ErrorCode CncManager::pauseProgram(int channelId) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->pauseProgram(channelId);
    if (result == ErrorCode::Success) {
        LOG_INFO("程序暂停成功：通道 {}", channelId);
    } else {
        LOG_WARN("程序暂停失败：通道 {}，错误码 {}", channelId, static_cast<int>(result));
    }

    return result;
}

ErrorCode CncManager::resumeProgram(int channelId) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->resumeProgram(channelId);
    if (result == ErrorCode::Success) {
        LOG_INFO("程序恢复成功：通道 {}", channelId);
    } else {
        LOG_WARN("程序恢复失败：通道 {}，错误码 {}", channelId, static_cast<int>(result));
    }

    return result;
}

ErrorCode CncManager::stopProgram(int channelId) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->stopProgram(channelId);
    if (result == ErrorCode::Success) {
        LOG_INFO("程序停止成功：通道 {}", channelId);
    } else {
        LOG_WARN("程序停止失败：通道 {}，错误码 {}", channelId, static_cast<int>(result));
    }

    return result;
}

ErrorCode CncManager::resetProgram(int channelId) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->resetProgram(channelId);
    if (result == ErrorCode::Success) {
        LOG_INFO("程序复位成功：通道 {}", channelId);
    } else {
        LOG_WARN("程序复位失败：通道 {}，错误码 {}", channelId, static_cast<int>(result));
    }

    return result;
}

// --- 手动操作 ---

ErrorCode CncManager::startJog(int channelId, int axisIndex, double speed, double distance) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->startJog(channelId, axisIndex, speed, distance);
    if (result == ErrorCode::Success) {
        LOG_INFO("轴点动开始：通道 {}，轴 {}，速度 {}，距离 {}", channelId, axisIndex, speed, distance);
    } else {
        LOG_WARN("轴点动开始失败：通道 {}，轴 {}，错误码 {}", channelId, axisIndex, static_cast<int>(result));
    }

    return result;
}

ErrorCode CncManager::stopJog(int channelId, int axisIndex) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->stopJog(channelId, axisIndex);
    if (result == ErrorCode::Success) {
        LOG_INFO("轴点动停止：通道 {}，轴 {}", channelId, axisIndex);
    } else {
        LOG_WARN("轴点动停止失败：通道 {}，轴 {}，错误码 {}", channelId, axisIndex, static_cast<int>(result));
    }

    return result;
}

// --- MDI操作 ---

ErrorCode CncManager::executeMdi(int channelId, const std::string& command) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->executeMdi(channelId, command);
    if (result == ErrorCode::Success) {
        LOG_INFO("MDI指令执行成功：通道 {}，指令 '{}'", channelId, command);
    } else {
        LOG_WARN("MDI指令执行失败：通道 {}，指令 '{}'，错误码 {}", channelId,
                 QString::fromStdString(command).toStdString(), static_cast<int>(result));
        emitError(result, tr("MDI指令执行失败"));
    }

    return result;
}

// --- 宏操作 ---
ErrorCode CncManager::executeMacro(int channelId, const std::string& macro) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->executeMacro(channelId, macro);
    if (result == ErrorCode::Success) {
        LOG_INFO("宏指令执行成功：通道 {}，指令 '{}'", channelId, macro);
    } else {
        LOG_WARN("宏指令执行失败：通道 {}，指令 '{}'，错误码 {}", channelId,
                 QString::fromStdString(macro).toStdString(), static_cast<int>(result));
        emitError(result, tr("宏指令执行失败"));
    }

    return result;
}

// --- 坐标系管理 ---
ErrorCode CncManager::getAxisTotalOffset(int channelId, int axisIndex, double& totalOffset) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getAxisTotalOffset(channelId, axisIndex, totalOffset);
}

ErrorCode CncManager::getCurrentWorkOffsetName(int channelId, std::string& workOffsetName) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getCurrentWorkOffsetName(channelId, workOffsetName);
}

ErrorCode CncManager::getWorkOffsetValue(int channelId, const std::string& workOffsetName, PointXD& offset) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getWorkOffsetValue(channelId, workOffsetName, offset);
}

ErrorCode CncManager::setWorkOffsetValue(int channelId, const std::string& workOffsetName, PointXD& offset) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->setWorkOffsetValue(channelId, workOffsetName, offset);
}

ErrorCode CncManager::getRemainingPosition(int channelId, PointXD& remainingPos) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getRemainingPosition(channelId, remainingPos);
}

// --- 程序控制选项 ---

ErrorCode CncManager::setOptionalStop(int channelId, bool enabled) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->setOptionalStop(channelId, enabled);
    if (result == ErrorCode::Success) {
        LOG_INFO("设置可选停止成功：通道 {}，状态 {}", channelId, enabled ? "启用" : "禁用");
    } else {
        LOG_WARN("设置可选停止失败：通道 {}，错误码 {}", channelId, static_cast<int>(result));
    }

    return result;
}

ErrorCode CncManager::setHandwheelMode(int channelId, bool enabled) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->setHandwheelMode(channelId, enabled);
    if (result == ErrorCode::Success) {
        LOG_INFO("设置手轮模式成功：通道 {}，状态 {}", channelId, enabled ? "启用" : "禁用");
    } else {
        LOG_WARN("设置手轮模式失败：通道 {}，错误码 {}", channelId, static_cast<int>(result));
    }

    return result;
}

ErrorCode CncManager::setBlockSkip(int channelId, bool enabled) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->setBlockSkip(channelId, enabled);
    if (result == ErrorCode::Success) {
        LOG_INFO("设置程序段跳过成功：通道 {}，状态 {}", channelId, enabled ? "启用" : "禁用");
    } else {
        LOG_WARN("设置程序段跳过失败：通道 {}，错误码 {}", channelId, static_cast<int>(result));
    }

    return result;
}

ErrorCode CncManager::setSingleStepMode(int channelId, SingleBlockModeType mode) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->setSingleStepMode(channelId, mode);
    if (result == ErrorCode::Success) {
        LOG_INFO("单步模式设置成功：通道 {}，模式 {}", channelId, static_cast<int>(mode));
    } else {
        LOG_WARN("单步模式设置失败：通道 {}，模式 {}，错误码 {}", channelId, static_cast<int>(mode),
                 static_cast<int>(result));
        emitError(result, tr("单步模式设置失败"));
    }

    return result;
}

// --- 宏变量管理 ---

ErrorCode CncManager::getMacroVariable(int channelId, int index, double& value) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->getMacroVariable(channelId, index, value);
    if (result != ErrorCode::Success) {
        LOG_WARN("读取宏变量失败：通道 {}，索引 {}，错误码 {}", channelId, index, static_cast<int>(result));
    }

    return result;
}

ErrorCode CncManager::setMacroVariable(int channelId, int index, double value) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->setMacroVariable(channelId, index, value);
    if (result == ErrorCode::Success) {
        LOG_INFO("宏变量设置成功：通道 {}，索引 {}，值 {}", channelId, index, value);
    } else {
        LOG_WARN("宏变量设置失败：通道 {}，索引 {}，值 {}，错误码 {}", channelId, index, value,
                 static_cast<int>(result));
    }

    return result;
}

// --- 刀具管理 ---

ErrorCode CncManager::getCurrentToolInfo(int channelId, ToolInfo& toolInfo) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getCurrentToolInfo(channelId, toolInfo);
}

ErrorCode CncManager::getMagazineStatus(int toolChangerId, std::vector<PocketStatus>& pocketStatuses) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getMagazineStatus(toolChangerId, pocketStatuses);
}

// === 新的基于 UUID 的刀具管理接口实现 ===

ErrorCode CncManager::getToolParameters(const std::string& uuid, ToolInfo& toolInfo) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getToolParameters(uuid, toolInfo);
}

ErrorCode CncManager::setToolParameters(const ToolInfo& toolInfo) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->setToolParameters(toolInfo);
}

ErrorCode CncManager::deleteTool(const std::string& uuid) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->deleteTool(uuid);
}

ErrorCode CncManager::getAllToolParameters(std::map<std::string, ToolInfo>& allToolsInfo) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getAllToolParameters(allToolsInfo);
}

// --- 配置管理 ---

ErrorCode CncManager::getConfiguration(std::vector<ConfigCategory>& rootCategories) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getConfiguration(rootCategories);
}

ErrorCode CncManager::setConfiguration(std::vector<ConfigCategory>& rootCategories) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->setConfiguration(rootCategories);
}

// --- 系统信息打印 ---

void CncManager::printSystemInfo() const {
    LOG_INFO("=== CNC 系统信息 ===");

    // 系统版本和控制器信息
    LOG_INFO("【系统基本信息】");
    LOG_INFO("  SDK版本: {}", m_systemConfig.sdkVersion);
    LOG_INFO("  控制器型号: {}", m_systemConfig.controllerModel);
    LOG_INFO("  固件版本: {}", m_systemConfig.controllerFirmwareVersion);
    LOG_INFO("  序列号: {}", m_systemConfig.controllerSerialNumber);
    LOG_INFO("  是否已初始化: {}", (m_initialized ? "是" : "否"));

    // 系统单位和性能参数
    LOG_INFO("【系统性能参数】");
    std::string linearUnitStr = (m_systemConfig.linearUnit == DistanceUnit::Millimeter ? "毫米"
                                 : m_systemConfig.linearUnit == DistanceUnit::Inch     ? "英寸"
                                                                                       : "未知");
    LOG_INFO("  距离单位: {}", linearUnitStr);

    std::string angularUnitStr = (m_systemConfig.angularUnit == AngleUnit::Degree   ? "度"
                                  : m_systemConfig.angularUnit == AngleUnit::Radian ? "弧度"
                                                                                    : "未知");
    LOG_INFO("  角度单位: {}", angularUnitStr);
    LOG_INFO("  最大编程进给速度: {}", m_systemConfig.maxProgrammedFeedrate);
    LOG_INFO("  最大快移速度: {}", m_systemConfig.maxRapidTraverseRate);
    LOG_INFO("  默认加速度: {}", m_systemConfig.defaultAcceleration);

    // 系统组件数量
    LOG_INFO("【系统组件数量】");
    LOG_INFO("  通道数量: {}", m_systemConfig.numberOfChannels);
    LOG_INFO("  轴数量: {}", m_systemConfig.totalNumberOfAxes);
    LOG_INFO("  主轴数量: {}", m_systemConfig.totalNumberOfSpindles);
    LOG_INFO("  刀库数量: {}", m_systemConfig.numberOfToolChangers);

    // 系统限制
    LOG_INFO("【系统限制】");
    LOG_INFO("  最大刀具补偿对数: {}", m_systemConfig.maxToolCompensationPairs);
    LOG_INFO("  最大工件坐标系数: {}", m_systemConfig.maxWorkOffsets);
    LOG_INFO("  最大程序大小(KB): {}", m_systemConfig.maxProgramSizeKb);
    LOG_INFO("  最大块缓冲区大小: {}", m_systemConfig.maxBlockBufferSize);

    // 宏变量配置
    LOG_INFO("【宏变量配置】");
    LOG_INFO("  最小用户变量索引: {}", m_systemConfig.macroVariableRange.minUserVarIndex);
    LOG_INFO("  最大用户变量索引: {}", m_systemConfig.macroVariableRange.maxUserVarIndex);

    // 通道配置
    LOG_INFO("【通道配置】");
    for (size_t i = 0; i < m_systemConfig.channelsConfigs.size(); ++i) {
        const auto& channel = m_systemConfig.channelsConfigs[i];
        LOG_INFO("  通道 {}: {}", i, channel.name);
        LOG_INFO("    关联轴数: {}", channel.axes.size());
        LOG_INFO("    关联主轴数: {}", channel.spindles.size());
    }

    // 轴配置
    LOG_INFO("【轴配置】");
    for (size_t i = 0; i < m_systemConfig.axesConfigs.size(); ++i) {
        const auto& axis = m_systemConfig.axesConfigs[i];
        std::string axisTypeStr = (axis.type == AxisType::Linear   ? "线性"
                                   : axis.type == AxisType::Rotary ? "旋转"
                                                                   : "未知");
        LOG_INFO("  轴 {}: {} (类型: {}) 限位: [{}, {}] 最大速度: {}", i, axis.name, axisTypeStr,
                 axis.softwareLimitNegative, axis.softwareLimitPositive, axis.maxVelocity);
    }

    // 主轴配置
    LOG_INFO("【主轴配置】");
    for (size_t i = 0; i < m_systemConfig.spindlesConfigs.size(); ++i) {
        const auto& spindle = m_systemConfig.spindlesConfigs[i];
        LOG_INFO("  主轴 {}: {} 转速范围: [{}, {}] RPM 档位数: {} 编码器: {}", i, spindle.name, spindle.minSpeedRpm,
                 spindle.maxSpeedRpm, spindle.numberOfGears, (spindle.hasEncoder ? "有" : "无"));
    }

    // 刀库配置
    LOG_INFO("【刀库配置】");
    for (size_t i = 0; i < m_systemConfig.toolChangersConfigs.size(); ++i) {
        const auto& toolChanger = m_systemConfig.toolChangersConfigs[i];
        std::string toolChangerTypeStr;
        switch (toolChanger.type) {
            case ToolChangerType::CAROUSEL:
                toolChangerTypeStr = "盘式";
                break;
            case ToolChangerType::CHAIN:
                toolChangerTypeStr = "链式";
                break;
            case ToolChangerType::MANUAL:
                toolChangerTypeStr = "手动";
                break;
            case ToolChangerType::NONE:
                toolChangerTypeStr = "无";
                break;
            default:
                toolChangerTypeStr = "未知";
                break;
        }
        LOG_INFO("  刀库 {}: {} 类型: {} 容量: {}", i, toolChanger.name, toolChangerTypeStr, toolChanger.capacity);
    }

    LOG_INFO("=== 系统信息结束 ===");
}

void CncManager::printSystemStatus() const {
    LOG_INFO("=== CNC 系统状态 ===");

    // 系统总体状态
    LOG_INFO("【系统总体状态】");
    std::string stateStr;
    switch (m_systemState) {
        case CncSystemState::Idle:
            stateStr = "空闲";
            break;
        case CncSystemState::Offline:
            stateStr = "离线";
            break;
        case CncSystemState::Stopped:
            stateStr = "已停止";
            break;
        case CncSystemState::Running:
            stateStr = "运行中";
            break;
        case CncSystemState::Paused:
            stateStr = "已暂停";
            break;
        case CncSystemState::ManualActive:
            stateStr = "手动模式";
            break;
        case CncSystemState::Homing:
            stateStr = "回零中";
            break;
        case CncSystemState::Simulating:
            stateStr = "仿真中";
            break;
        case CncSystemState::Error:
            stateStr = "错误";
            break;
        default:
            stateStr = "未知";
            break;
    }
    LOG_INFO("  当前状态: {}", stateStr);

    // 各通道状态
    LOG_INFO("【通道状态】");
    for (const auto& pair : m_operatingModes) {
        int channelId = pair.first;
        OperatingMode mode = pair.second;

        std::string modeStr;
        switch (mode) {
            case OperatingMode::Auto:
                modeStr = "自动";
                break;
            case OperatingMode::MDI:
                modeStr = "MDI";
                break;
            case OperatingMode::Manual:
                modeStr = "手动";
                break;
            case OperatingMode::Edit:
                modeStr = "编辑";
                break;
            case OperatingMode::Home:
                modeStr = "回零";
                break;
            default:
                modeStr = "未知";
                break;
        }
        LOG_INFO("  通道 {}:", channelId);
        LOG_INFO("    操作模式: {}", modeStr);

        // 程序状态
        auto progIt = m_programStatuses.find(channelId);
        if (progIt != m_programStatuses.end()) {
            const auto& progStatus = progIt->second;
            LOG_INFO("    程序名称: {}", progStatus.programName);
            LOG_INFO("    当前行号: {}", progStatus.currentLine);

            std::string progStateStr;
            if (progStatus.isRunning)
                progStateStr = "运行中";
            else if (progStatus.isPaused)
                progStateStr = "暂停";
            else if (progStatus.isError)
                progStateStr = "错误";
            else
                progStateStr = "停止";
            LOG_INFO("    程序状态: {}", progStateStr);
        }
    }

    // 活动报警
    LOG_INFO("【当前报警】");
    if (m_activeAlarms.empty()) {
        LOG_INFO("  无活动报警");
    } else {
        for (const auto& alarm : m_activeAlarms) {
            std::string severityStr;
            switch (alarm.severity) {
                case AlarmSeverity::Info:
                    severityStr = "[信息]";
                    break;
                case AlarmSeverity::Warning:
                    severityStr = "[警告]";
                    break;
                case AlarmSeverity::Error:
                    severityStr = "[错误]";
                    break;
                case AlarmSeverity::Critical:
                    severityStr = "[严重]";
                    break;
            }

            if (alarm.channelId >= 0) {
                LOG_INFO("  [{}] {} {} (通道 {})", alarm.code, severityStr, alarm.message, alarm.channelId);
            } else {
                LOG_INFO("  [{}] {} {}", alarm.code, severityStr, alarm.message);
            }
        }
    }

    LOG_INFO("=== 系统状态结束 ===");
}

void CncManager::printAxisInfo(int channelId) const {
    if (!m_initialized || !m_cncInterface) {
        LOG_WARN("系统未初始化，无法获取轴信息");
        return;
    }

    LOG_INFO("=== CNC 轴信息 ===");

    if (channelId == -1) {
        // 打印所有通道的轴信息
        for (size_t i = 0; i < m_systemConfig.channelsConfigs.size(); ++i) {
            printAxisInfoForChannel(static_cast<int>(i));
        }
    } else {
        // 打印指定通道的轴信息
        printAxisInfoForChannel(channelId);
    }

    LOG_INFO("=== 轴信息结束 ===");
}

void CncManager::printToolInfo() const {
    if (!m_initialized || !m_cncInterface) {
        LOG_WARN("系统未初始化，无法获取刀具信息");
        return;
    }

    LOG_INFO("=== CNC 刀具信息 ===");

    // 打印刀库状态
    LOG_INFO("【刀库状态】");
    for (size_t i = 0; i < m_systemConfig.toolChangersConfigs.size(); ++i) {
        const auto& toolChanger = m_systemConfig.toolChangersConfigs[i];
        LOG_INFO("  刀库 {} ({}):", i, toolChanger.name);

        std::vector<PocketStatus> pocketStatuses;
        if (m_cncInterface->getMagazineStatus(static_cast<int>(i), pocketStatuses) == ErrorCode::Success) {
            int occupiedCount = 0;
            for (const auto& pocket : pocketStatuses) {
                if (pocket.isOccupied) occupiedCount++;
            }
            LOG_INFO("    容量: {} / 已占用: {}", toolChanger.capacity, occupiedCount);

            // 显示前几个刀位的状态
            int displayCount = std::min(10, static_cast<int>(pocketStatuses.size()));
            std::ostringstream statusStream;
            statusStream << "    刀位状态 (前" << displayCount << "个): ";
            for (int j = 0; j < displayCount; ++j) {
                const auto& pocket = pocketStatuses[j];
                if (pocket.isOccupied) {
                    statusStream << "T" << pocket.toolNumberInPocket << " ";
                } else {
                    statusStream << "空 ";
                }
            }
            if (pocketStatuses.size() > 10) {
                statusStream << "...";
            }
            LOG_INFO("{}", statusStream.str());
        } else {
            LOG_WARN("    状态获取失败");
        }
    }

    // 打印当前活动刀具
    LOG_INFO("【当前活动刀具】");
    for (size_t i = 0; i < m_systemConfig.channelsConfigs.size(); ++i) {
        ToolInfo currentTool;
        if (m_cncInterface->getCurrentToolInfo(static_cast<int>(i), currentTool) == ErrorCode::Success) {
            if (currentTool.isValid) {
                LOG_INFO("  通道 {}: T{} ({})", i, currentTool.number, currentTool.name);
                LOG_INFO("    几何长度: {}", currentTool.geometryLengthZ);
                LOG_INFO("    几何半径: {}", currentTool.geometryRadius);
                LOG_INFO("    是否激活: {}", (currentTool.isActive ? "是" : "否"));
            } else {
                LOG_INFO("  通道 {}: 无刀具", i);
            }
        } else {
            LOG_WARN("  通道 {}: 获取失败", i);
        }
    }

    LOG_INFO("=== 刀具信息结束 ===");
}

void CncManager::printAxisInfoForChannel(int channelId) const {
    if (channelId < 0 || channelId >= static_cast<int>(m_systemConfig.channelsConfigs.size())) {
        LOG_WARN("无效的通道ID: {}", channelId);
        return;
    }

    const auto& channel = m_systemConfig.channelsConfigs[channelId];
    LOG_INFO("【通道 {} ({}) 轴信息】", channelId, channel.name);

    for (const auto& axisMapping : channel.axes) {
        int axisIndex = axisMapping.globalAxisIndex;
        if (axisIndex >= 0 && axisIndex < static_cast<int>(m_systemConfig.axesConfigs.size())) {
            const auto& axisConfig = m_systemConfig.axesConfigs[axisIndex];

            LOG_INFO("  轴 {} ({}):", axisIndex, axisConfig.name);
            LOG_INFO("    本地名称: {}", axisMapping.channelLocalName);

            std::string axisTypeStr = (axisConfig.type == AxisType::Linear   ? "线性"
                                       : axisConfig.type == AxisType::Rotary ? "旋转"
                                                                             : "未知");
            LOG_INFO("    轴类型: {}", axisTypeStr);
            LOG_INFO("    限位范围: [{}, {}]", axisConfig.softwareLimitNegative, axisConfig.softwareLimitPositive);
            LOG_INFO("    最大速度: {}", axisConfig.maxVelocity);
            LOG_INFO("    最大加速度: {}", axisConfig.maxAcceleration);
            LOG_INFO("    脉冲当量: {}", axisConfig.pulseEquivalent);

            // 获取当前位置信息
            double workPos = 0.0, machinePos = 0.0;
            if (m_cncInterface->getAxisWorkPosition(channelId, axisIndex, workPos) == ErrorCode::Success &&
                m_cncInterface->getAxisMachinePosition(channelId, axisIndex, machinePos) == ErrorCode::Success) {
                LOG_INFO("    当前工件坐标: {:.3f}", workPos);
                LOG_INFO("    当前机床坐标: {:.3f}", machinePos);
            } else {
                LOG_WARN("    当前位置: 获取失败");
            }

            // 检查回零状态
            bool isHomed = false;
            if (m_cncInterface->isAxisHomed(channelId, axisIndex, isHomed) == ErrorCode::Success) {
                LOG_INFO("    回零状态: {}", (isHomed ? "已回零" : "未回零"));
            } else {
                LOG_WARN("    回零状态: 获取失败");
            }
        }
    }
}

// --- 报警处理 ---

const std::vector<AlarmInfo>& CncManager::getActiveAlarms() const {
    QMutexLocker locker(&m_dataMutex);
    return m_activeAlarms;
}

ErrorCode CncManager::clearAlarms(int channelId) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->clearAlarms(channelId);
    if (result == ErrorCode::Success) {
        LOG_INFO("清除报警成功：通道 {}", channelId);
        // 报警清除后，会通过事件系统通知状态变化，不需要手动更新
    } else {
        LOG_WARN("清除报警失败：通道 {}，错误码 {}", channelId, static_cast<int>(result));
    }

    return result;
}

// --- 事件处理 ---

void CncManager::onCncEvent(const CncEvent& event) {
    // 此方法在硬件回调线程中执行，仅发射信号，将处理工作转移到CNC管理器的线程
    emit eventReceived(event);
}

void CncManager::processQueuedCncEvent(const CncEvent& event) {
    LOG_DEBUG("在CNC管理器线程中处理事件，类型：{}", static_cast<int>(event.type));

    // 根据事件类型进行相应处理
    switch (event.type) {
        case CncEventType::SystemStateChanged: {
            if (auto payload = std::get_if<SystemStateEventPayload>(&event.payload)) {
                QMutexLocker locker(&m_dataMutex);
                if (payload->newState != m_systemState) {
                    CncSystemState oldState = m_systemState;
                    m_systemState = payload->newState;
                    emit systemStateChanged(payload->newState);
                    LOG_INFO("系统状态变化：{} -> {}", static_cast<int>(oldState), static_cast<int>(payload->newState));
                }
            }
            break;
        }

        case CncEventType::OperatingModeChanged: {
            if (auto payload = std::get_if<ChannelOperatingModeEventPayload>(&event.payload)) {
                QMutexLocker locker(&m_dataMutex);
                m_operatingModes[payload->channelId] = payload->newMode;
                emit operatingModeChanged(payload->channelId, payload->newMode);
                LOG_INFO("操作模式变化：通道 {}，新模式 {}", payload->channelId, static_cast<int>(payload->newMode));
            }
            break;
        }

        case CncEventType::ProgramStatusChanged: {
            if (auto payload = std::get_if<ChannelProgramStatusEventPayload>(&event.payload)) {
                QMutexLocker locker(&m_dataMutex);
                m_programStatuses[payload->channelId] = payload->newStatus;
                emit programStatusChanged(payload->channelId, payload->newStatus);
                LOG_INFO("程序状态变化：通道 {}", payload->channelId);
            }
            break;
        }

        case CncEventType::AlarmOccurred:
        case CncEventType::AlarmCleared: {
            if (auto payload = std::get_if<AlarmEventPayload>(&event.payload)) {
                // 更新缓存的报警列表
                if (event.type == CncEventType::AlarmOccurred) {
                    // 添加新报警到缓存
                    QMutexLocker locker(&m_dataMutex);
                    m_activeAlarms.push_back(payload->alarm);
                } else {
                    // 从缓存中移除报警
                    QMutexLocker locker(&m_dataMutex);
                    m_activeAlarms.erase(std::remove_if(m_activeAlarms.begin(), m_activeAlarms.end(),
                                                        [&payload](const AlarmInfo& alarm) {
                                                            return alarm.code == payload->alarm.code &&
                                                                   alarm.channelId == payload->alarm.channelId;
                                                        }),
                                         m_activeAlarms.end());
                }
                emit alarmsChanged(m_activeAlarms);
                LOG_INFO("报警事件：代码 {}，类型 {}", payload->alarm.code,
                         event.type == CncEventType::AlarmOccurred ? "发生" : "清除");
            }
            break;
        }

        case CncEventType::ToolInSpindleChanged: {
            if (auto payload = std::get_if<ChannelToolEventPayload>(&event.payload)) {
                emit toolChanged(payload->channelId, payload->currentToolInSpindle);
                LOG_INFO("换刀事件：通道 {}，刀具号 {}", payload->channelId, payload->currentToolInSpindle.number);
            }
            break;
        }

        case CncEventType::FeedOverrideChanged: {
            if (auto payload = std::get_if<ChannelOverrideEventPayload>(&event.payload)) {
                emit feedrateChanged(payload->channelId, payload->newOverridePercent);
                LOG_INFO("进给倍率变化：通道 {}，新倍率 {}%", payload->channelId, payload->newOverridePercent);
            }
            break;
        }

        case CncEventType::SpindleOverrideChanged: {
            if (auto payload = std::get_if<ChannelOverrideEventPayload>(&event.payload)) {
                // 这里可以发射主轴倍率变化信号
                LOG_INFO("主轴倍率变化：通道 {}，新倍率 {}%", payload->channelId, payload->newOverridePercent);
            }
            break;
        }

        case CncEventType::ActiveWorkOffsetChanged: {
            if (auto payload = std::get_if<ChannelWorkOffsetEventPayload>(&event.payload)) {
                emit workOffsetChanged(payload->channelId, QString::fromStdString(payload->workOffsetName),
                                       payload->offsetValue);
                LOG_INFO("工件坐标系变化：通道 {}，坐标系 {}", payload->channelId, payload->workOffsetName);
            }
            break;
        }

        case CncEventType::HomingProcessUpdate: {
            if (auto payload = std::get_if<ChannelHomingEventPayload>(&event.payload)) {
                if (payload->isComplete) {
                    emit homingCompleted(payload->channelId, payload->axisIndices, payload->overallSuccess,
                                         QString::fromStdString(payload->message));
                }
                LOG_INFO("回零过程更新：通道 {}，完成 {}，成功 {}", payload->channelId, payload->isComplete,
                         payload->overallSuccess);
            }
            break;
        }

        case CncEventType::PanelKeyPressed:
        case CncEventType::PanelKeyReleased:
        case CncEventType::PanelKeyLongPress: {
            if (auto payload = std::get_if<PanelKeyEventPayload>(&event.payload)) {
                // 检查是否应该阻止此按键事件
                if (shouldBlockPanelKey(payload->keyType, payload->action)) {
                    LOG_UI("程序执行中，已阻止MCP模式切换");
                    break;  // 阻止事件，不继续处理
                }

                emit panelKeyEvent(payload->keyType, payload->action, payload->channelId, payload->value);

                std::string actionStr;
                switch (payload->action) {
                    case PanelKeyAction::Pressed:
                        actionStr = "按下";
                        break;
                    case PanelKeyAction::Released:
                        actionStr = "释放";
                        break;
                    case PanelKeyAction::LongPressed:
                        actionStr = "长按";
                        break;
                }
                LOG_INFO("面板按键事件：{}，按键类型 {}，通道 {}", actionStr, static_cast<int>(payload->keyType),
                         payload->channelId);
            }
            break;
        }

        default:
            LOG_WARN("未处理的事件类型：{}", static_cast<int>(event.type));
            break;
    }
}

// --- 私有方法 ---

void CncManager::updateSystemConfig() {
    if (!m_cncInterface) {
        return;
    }

    ErrorCode result = m_cncInterface->getSystemConfig(m_systemConfig);
    if (result != ErrorCode::Success) {
        LOG_WARN("获取系统配置失败：{}", static_cast<int>(result));
    }
}

void CncManager::emitError(ErrorCode errorCode, const QString& context) {
    QString errorMessage = QString("%1 (错误码: %2)").arg(context).arg(static_cast<int>(errorCode));
    emit cncError(errorCode, errorMessage);
    LOG_WARN("{}", errorMessage.toStdString());
}

// --- 事件模拟方法实现 ---

void CncManager::simulatePanelKeyEvent(PanelKeyType keyType, PanelKeyAction action, int channelId, double value) {
    PanelKeyEventPayload payload;
    payload.keyType = keyType;
    payload.action = action;
    payload.channelId = channelId;
    payload.value = value;

    CncEventType eventType;
    switch (action) {
        case PanelKeyAction::Pressed:
            eventType = CncEventType::PanelKeyPressed;
            break;
        case PanelKeyAction::Released:
            eventType = CncEventType::PanelKeyReleased;
            break;
        case PanelKeyAction::LongPressed:
            eventType = CncEventType::PanelKeyLongPress;
            break;
    }

    CncEvent event(eventType, payload);
    onCncEvent(event);
}

void CncManager::simulateSystemStateChange(CncSystemState newState) {
    SystemStateEventPayload payload;
    payload.newState = newState;

    CncEvent event(CncEventType::SystemStateChanged, payload);
    onCncEvent(event);
}

void CncManager::simulateAlarmEvent(const AlarmInfo& alarm, bool isOccurred) {
    AlarmEventPayload payload;
    payload.alarm = alarm;

    CncEventType eventType = isOccurred ? CncEventType::AlarmOccurred : CncEventType::AlarmCleared;
    CncEvent event(eventType, payload);
    onCncEvent(event);
}

// --- 程序状态和按键过滤方法实现 ---

bool CncManager::isAnyProgramRunning() const {
    QMutexLocker locker(&m_dataMutex);

    // 检查所有通道的程序状态
    for (const auto& statusPair : m_programStatuses) {
        const ProgramStatus& status = statusPair.second;
        if (status.isRunning && !status.isError) {
            return true;
        }
    }

    return false;
}

bool CncManager::isMcpModeSwitchKey(PanelKeyType keyType) const {
    // 定义MCP模式切换按键类型
    switch (keyType) {
        case PanelKeyType::ModeAuto:           // 自动模式按键
        case PanelKeyType::ModeMdi:            // MDI模式按键
        case PanelKeyType::ModeManual:         // 手动增量模式按键
        case PanelKeyType::ModeHandwheel:      // 手轮模式按键
        case PanelKeyType::ModeContinuousJog:  // 连续模式按键
        case PanelKeyType::ModeEdit:           // 编辑模式按键
            return true;
        default:
            return false;
    }
}

bool CncManager::shouldBlockPanelKey(PanelKeyType keyType, PanelKeyAction action) const {
    // 只处理按键按下事件
    if (action != PanelKeyAction::Pressed) {
        return false;
    }

    // 检查是否为MCP模式切换按键
    if (!isMcpModeSwitchKey(keyType)) {
        return false;
    }

    // 检查是否有程序正在运行
    if (!isAnyProgramRunning()) {
        return false;
    }

    // 程序运行时阻止模式切换按键
    LOG_WARN("程序执行中，阻止MCP模式切换按键：{}", static_cast<int>(keyType));
    return true;
}

ErrorCode CncManager::getPitchCompensation(int channelId, int axisIndex, AxisPitchCompensation& compensation) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getPitchCompensation(channelId, axisIndex, compensation);
}

ErrorCode CncManager::setPitchCompensation(int channelId, int axisIndex, const AxisPitchCompensation& compensation) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->setPitchCompensation(channelId, axisIndex, compensation);
}

ErrorCode CncManager::enablePitchCompensation(int channelId, int axisIndex, bool enable, bool isCyclicMode) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->enablePitchCompensation(channelId, axisIndex, enable, isCyclicMode);
}

ErrorCode CncManager::getPitchCompensationStatus(int channelId, int axisIndex, bool& isEnabled, bool& isCyclicMode,
                                                 double& currentCompensation) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getPitchCompensationStatus(channelId, axisIndex, isEnabled, isCyclicMode,
                                                      currentCompensation);
}

ErrorCode CncManager::getAllPitchCompensations(int channelId, std::vector<AxisPitchCompensation>& allCompensations) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->getAllPitchCompensations(channelId, allCompensations);
}

ErrorCode CncManager::setAllPitchCompensations(int channelId, const std::vector<AxisPitchCompensation>& compensations) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    return m_cncInterface->setAllPitchCompensations(channelId, compensations);
}

// --- UI模态模式通知 ---

ErrorCode CncManager::enterModalMode(UiModalType modalType, const std::string& modalId) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->enterModalMode(modalType, modalId);
    if (result == ErrorCode::Success) {
        LOG_INFO("UI进入模态模式成功：类型 {}，ID '{}'", static_cast<int>(modalType), modalId);
    } else {
        LOG_WARN("UI进入模态模式失败：类型 {}，ID '{}'，错误码 {}", static_cast<int>(modalType), modalId,
                 static_cast<int>(result));
    }

    return result;
}

ErrorCode CncManager::exitModalMode(UiModalType modalType, const std::string& modalId) {
    if (!m_initialized || !m_cncInterface) {
        return ErrorCode::NotInitialized;
    }

    ErrorCode result = m_cncInterface->exitModalMode(modalType, modalId);
    if (result == ErrorCode::Success) {
        LOG_INFO("UI退出模态模式成功：类型 {}，ID '{}'", static_cast<int>(modalType), modalId);
    } else {
        LOG_WARN("UI退出模态模式失败：类型 {}，ID '{}'，错误码 {}", static_cast<int>(modalType), modalId,
                 static_cast<int>(result));
    }

    return result;
}
