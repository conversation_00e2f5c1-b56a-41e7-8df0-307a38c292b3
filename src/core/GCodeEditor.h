#ifndef GCODEEDITOR_H
#define GCODEEDITOR_H

#include <Qsci/qsciscintilla.h>  // 假设 QScintilla 头文件路径已配置

#include <QFileSystemWatcher>

#include "GCodeAPIs.h"
#include "GCodeLexer.h"

class GCodeEditor : public QsciScintilla {
    Q_OBJECT
    Q_PROPERTY(QString state READ getState WRITE setState)

   public:
    // 行标记类型枚举
    enum LineMarkerType {
        ProgramExecution = 1,  // 程序执行行
        Breakpoint = 2,        // 断点（预留）
        Error = 3,             // 错误行（预留）
        Warning = 4            // 警告行（预留）
    };

    // 标记样式配置结构
    struct MarkerStyleConfig {
        MarkerSymbol symbol = Background;
        QColor backgroundColor = QColor(255, 255, 0, 80);
        QColor foregroundColor = QColor(Qt::white);
        bool enabled = true;
    };

   public:
    explicit GCodeEditor(QWidget *parent = nullptr);
    ~GCodeEditor();

    void loadGCodeFile(const QString &filename);
    void saveGCodeFile(const QString &filename);

    // 获取当前文件名
    QString getCurrentFileName() const { return m_currentFileName; }

    // 行标记功能 - 多类型支持
    void highlightLine(int lineNumber, LineMarkerType type = ProgramExecution);  // 高亮指定行
    void clearHighlight(LineMarkerType type);                                    // 清除指定类型的高亮
    void clearAllHighlights();                                                   // 清除所有高亮

    // 获取当前高亮行号
    int getHighlightedLine(LineMarkerType type) const;
    QList<int> getAllHighlightedLines() const;

    // 样式配置
    void setMarkerStyleConfig(LineMarkerType type, const MarkerStyleConfig &config);
    void setMarkerStyleForFocusState(LineMarkerType type, const MarkerStyleConfig &focusedConfig,
                                     const MarkerStyleConfig &unfocusedConfig);
    MarkerStyleConfig getMarkerStyleConfig(LineMarkerType type) const;

    // 焦点状态相关
    bool hasEditorFocus() const { return m_hasFocus; }
    void updateMarkersForFocusState();  // 根据焦点状态更新所有标记

    // 状态管理
    QString getState() const { return m_state; }
    void setState(const QString &state);
    void updateStateForEditability();  // 根据可编辑性更新状态

    // === CaretLine管理功能 ===
    // CaretLine具有最高视觉优先级，始终显示在所有行标记之上，与CurrentSelection标记一致
    void setCaretLineStyle(const QColor &backgroundColor, bool enabled = true);
    void setCaretLineStyleForFocus(const QColor &focusedColor, const QColor &unfocusedColor, bool enabled = true);
    void setCaretLineVisibilityForFocus(bool visibleWhenFocused, bool visibleWhenUnfocused = false);
    void updateCaretLineForFocusState();  // 根据焦点状态更新CaretLine显示

    // 文件编辑操作
    void beginSelection();       // 开始选区
    void clearSelection();       // 清除选区
    void toggleSelectionSide();  // 切换选区首尾
    void copySelection();        // 复制选区
    void cutSelection();         // 剪切选区
    void pasteToCursor();        // 粘贴至光标
    void selectAllText();        // 全选文本
    void performUndo();          // 进行撤销操作
    void performRedo();          // 进行重做操作

    // 跳转到上一个目标（变量或命令名），返回是否找到目标
    bool jumpToPrev(const QString &target);

    // 跳转到下一个目标（变量或命令名），返回是否找到目标
    bool jumpToNext(const QString &target);

   signals:
    void fileChanged(const QString &path);
    void focusStateChanged(bool hasFocus);               // 焦点状态变化信号
    void selectionBegun();                               // 选区开始信号
    void selectionCleared();                             // 选区清除信号
    void undoAvailabilityChanged(const bool available);  // 撤销可用性变化信号
    void redoAvailabilityChanged(const bool available);  // 重做可用性变化信号

   public slots:
    void setReadOnly(bool ro) override;  // 重写只读设置以更新状态

   protected:
    void keyPressEvent(QKeyEvent *event) override;    // 重写键盘事件处理
    void focusInEvent(QFocusEvent *event) override;   // 重写焦点获得事件
    void focusOutEvent(QFocusEvent *event) override;  // 重写焦点失去事件

   private slots:
    void onFileChanged(const QString &path);    // 文件变化槽函数
    void onCursorPosChanged(int row, int col);  // 光标位置变化槽函数

   private:
    void setupEditor();
    void setupAutoCompletion();
    void setupLineMarkers();                                        // 设置行标记样式
    void applyMarkerStyle(LineMarkerType type);                     // 应用标记样式
    void updateMarkerForLine(int lineNumber, LineMarkerType type);  // 更新指定行的标记
    void startWatchingFile(const QString &filename);                // 开始监控文件
    void stopWatchingFile();                                        // 停止监控文件

    void updateUndoAndRedoAvailability();  // 更新撤销重做可用性

    // 稍后可以添加 G 代码词法分析器设置
    GCodeLexer *m_lexer;
    GCodeAPIs *m_apis;

    // 文件监控相关
    QFileSystemWatcher *m_fileWatcher;
    QString m_currentFileName;  // 当前文件名

    // 多类型行标记支持
    QMap<LineMarkerType, int> m_highlightedLines;               // 记录各类型的高亮行
    QMap<LineMarkerType, MarkerStyleConfig> m_focusedStyles;    // 获得焦点时的样式
    QMap<LineMarkerType, MarkerStyleConfig> m_unfocusedStyles;  // 失去焦点时的样式
    bool m_hasFocus = false;                                    // 焦点状态

    // CaretLine管理 - 最高视觉优先级实现，与CurrentSelection标记一致
    // CaretLine通过Scintilla内置机制实现，具有比行标记更高的绘制优先级
    QColor m_caretLineColorFocused = QColor("#FF9800");  // 获得焦点时CaretLine颜色（与CurrentSelection一致）
    QColor m_caretLineColorUnfocused = QColor("#77ABD6");  // 失去焦点时CaretLine颜色（与CurrentSelection一致）
    bool m_caretLineEnabledWhenFocused = true;             // 获得焦点时是否显示CaretLine
    bool m_caretLineEnabledWhenUnfocused = false;          // 失去焦点时是否显示CaretLine

    // 状态管理
    QString m_state = "normal";  // 编辑器状态：normal, editing, readonly

    // 选区管理
    bool m_selecting = false;      // 选区状态
    int m_selectionBeginRow = -1;  // 选区开始位置行
    int m_selectionBeginCol = -1;  // 选区开始位置列
    int m_selectionColRecord = -1;  // 选区光标列记录（实际光标列会受光标所在行字符数限制，此变量不会）

    // 撤销/重做状态管理
    bool m_undoAvailable = false;
    bool m_redoAvailable = false;

    // 查找状态变量
    QString m_lastFindTarget = QString();
    bool m_lastFindWasForward = false;

    // 标记编号分配（避免冲突）
    static const int MARKER_BASE = 10;  // 标记编号基数
    int getMarkerNumber(LineMarkerType type) const { return MARKER_BASE + static_cast<int>(type); }
};

#endif  // GCODEEDITOR_H