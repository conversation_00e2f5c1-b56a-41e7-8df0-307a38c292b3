#include "GCodeEditor.h"

#include <Qsci/qscilexercustom.h>  // 用于自定义词法分析器，如果需要

#include <QApplication>
#include <QColor>
#include <QFile>
#include <QFileInfo>
#include <QFileSystemWatcher>
#include <QFocusEvent>  // 添加焦点事件头文件
#include <QFont>
#include <QKeyEvent>  // 添加键盘事件头文件
#include <QList>
#include <QRegularExpression>
#include <QStyle>  // 添加样式头文件
#include <QTextStream>

#include "core/AppLogger.h"
#include "core/GCodeLexer.h"

// 构造函数
GCodeEditor::GCodeEditor(QWidget *parent)
    : QsciScintilla(parent),
      m_lexer(new GCodeLexer(this)),
      m_apis(new GCodeAPIs(m_lexer)),
      m_fileWatcher(new QFileSystemWatcher(this)) {
    setupEditor();
    setupAutoCompletion();
    setupLineMarkers();

    // 连接文件变化信号
    connect(m_fileWatcher, &QFileSystemWatcher::fileChanged, this, &GCodeEditor::onFileChanged);

    // 连接文本变化信号以更新状态
    connect(this, &QsciScintilla::textChanged, this, &GCodeEditor::updateStateForEditability);
    connect(this, &QsciScintilla::textChanged, this, &GCodeEditor::updateUndoAndRedoAvailability);

    // 连接光标位置变化信号以更新选区
    connect(this, &QsciScintilla::cursorPositionChanged, this, &GCodeEditor::onCursorPosChanged);

    // 设置初始状态
    updateStateForEditability();
}

// 析构函数
GCodeEditor::~GCodeEditor() { stopWatchingFile(); }

// 编辑器基本设置
void GCodeEditor::setupEditor() {
    // --- 通用编辑器设置 ---
    setUtf8(true);                                          // 支持 UTF-8
    QFont editorFont("Siemens AD Mono CH Simplified", 16);  // 设置字体
    setFont(editorFont);
    setPaper(QColor(Qt::white));

    // --- 行号设置 ---
    setMarginsFont(editorFont);                                                           // 行号区域字体
    setMarginWidth(0, QFontMetrics(editorFont).horizontalAdvance(QString("00000")) + 6);  // 行号边栏宽度
    setMarginLineNumbers(0, true);                                                        // 显示行号
    setMarginsBackgroundColor(QColor("#f0f0f0"));
    setMarginsForegroundColor(QColor("#808080"));

    // --- 自动缩进 ---
    setAutoIndent(true);
    setIndentationsUseTabs(false);
    setTabWidth(4);
    setIndentationGuides(true);

    // --- 括号匹配 ---
    setBraceMatching(QsciScintilla::SloppyBraceMatch);
    setMatchedBraceBackgroundColor(QColor("#c0e0ff"));
    setUnmatchedBraceForegroundColor(QColor("red"));

    // --- CaretLine（光标行高亮）配置 ---
    // 设置默认CaretLine样式，与CurrentSelection标记一致，确保最高优先级显示
    setCaretLineVisible(m_caretLineEnabledWhenFocused);
    setCaretLineBackgroundColor(m_caretLineColorFocused);  // 初始使用获得焦点时的颜色

    // 确保CaretLine具有最高的绘制优先级
    // 通过设置适当的alpha值确保CaretLine总是可见
    SendScintilla(QsciScintilla::SCI_SETCARETLINEVISIBLEALWAYS, true);

    // --- G代码通常不需要折行 ---
    setWrapMode(QsciScintilla::WrapWord);

    // --- 其他 ---
    setLexer(m_lexer);
    m_lexer->setAPIs(m_apis);

    // 启用拖放
    setAcceptDrops(true);

    // 强制设置背景色为白色，尝试覆盖外部样式表
    SendScintilla(QsciScintilla::SCI_STYLESETBACK, QsciScintilla::STYLE_DEFAULT, QColor(Qt::white).rgb());

    // 为编辑器起一个名字，方便样式设置
    setObjectName("gcodeEditor");
}

void GCodeEditor::setupAutoCompletion() {
    setAutoCompletionSource(QsciScintilla::AcsAPIs);
    setAutoCompletionCaseSensitivity(false);
    setAutoCompletionReplaceWord(true);
    setAutoCompletionShowSingle(true);
    setAutoCompletionThreshold(1);
    setAutoCompletionFillupsEnabled(true);
}

void GCodeEditor::setupLineMarkers() {
    // 初始化各类型标记的默认样式配置

    // CurrentSelection标记已移除，光标行显示完全由CaretLine负责

    // 程序运行行样式（获得焦点时）
    // 调整透明度确保CaretLine能够透过显示，保持最高优先级
    MarkerStyleConfig programExecutionFocused;
    programExecutionFocused.symbol = Background;
    programExecutionFocused.backgroundColor = QColor("#5F7989");  // 深蓝灰色背景
    programExecutionFocused.foregroundColor = QColor("#FFFFFF");  // 白色前景
    programExecutionFocused.enabled = true;

    // 程序运行行样式（失去焦点时）
    MarkerStyleConfig programExecutionUnfocused;
    programExecutionUnfocused.symbol = Background;
    programExecutionUnfocused.backgroundColor = QColor("#5F7989");  // 深蓝灰色背景
    programExecutionUnfocused.foregroundColor = QColor("#FFFFFF");  // 白色前景
    programExecutionUnfocused.enabled = true;

    // 断点样式（预留，获得焦点时）
    MarkerStyleConfig breakpointFocused;
    breakpointFocused.symbol = Circle;
    breakpointFocused.backgroundColor = QColor(220, 20, 60);  // 深红色
    breakpointFocused.foregroundColor = QColor(Qt::white);
    breakpointFocused.enabled = true;

    // 断点样式（预留，失去焦点时）
    MarkerStyleConfig breakpointUnfocused;
    breakpointUnfocused.symbol = Circle;
    breakpointUnfocused.backgroundColor = QColor(220, 20, 60, 150);  // 深红色，较透明
    breakpointUnfocused.foregroundColor = QColor(Qt::lightGray);
    breakpointUnfocused.enabled = true;

    // 设置默认样式配置
    setMarkerStyleForFocusState(ProgramExecution, programExecutionFocused, programExecutionUnfocused);
    setMarkerStyleForFocusState(Breakpoint, breakpointFocused, breakpointUnfocused);

    // 设置CaretLine颜色配置，永远显示
    setCaretLineStyleForFocus(QColor("#FF9800"),  // 获得焦点时使用橙色
                              QColor("#77ABD6"),  // 失去焦点时使用蓝色
                              true);

    // 设置CaretLine在任何状态下都显示
    setCaretLineVisibilityForFocus(true, true);

    // 应用所有标记样式
    for (int i = ProgramExecution; i <= Warning; ++i) {
        applyMarkerStyle(static_cast<LineMarkerType>(i));
    }

    LOG_DEBUG("GCodeEditor: 已初始化多类型行标记系统，CaretLine永远显示，CurrentSelection已移除");
}

// 从GCodeEditor本身加载GCode文件
void GCodeEditor::loadGCodeFile(const QString &filename) {
    QFile file(filename);
    if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QTextStream in(&file);
        setText(in.readAll());
        file.close();

        // 设置当前文件名并开始监控
        m_currentFileName = filename;
        startWatchingFile(filename);
    }

    // 结束当前选区
    if (m_selecting) {
        clearSelection();
    }

    // 更新撤销重做可用性
    updateUndoAndRedoAvailability();
}

// 保存GCode文件
void GCodeEditor::saveGCodeFile(const QString &filename) {
    QFile file(filename);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        out << text();
        file.close();

        // 如果是保存到新文件，更新当前文件名并重新监控
        if (filename != m_currentFileName) {
            m_currentFileName = filename;
            startWatchingFile(filename);
        }
    }
}

// 重写键盘事件，自动将小写字母转换为大写，并处理选区方向键
void GCodeEditor::keyPressEvent(QKeyEvent *event) {
    // 获取按下的文本
    QString inputText = event->text();

    // 如果输入的是小写字母，转换为大写
    if (!inputText.isEmpty() && inputText.at(0).isLetter() && inputText.at(0).isLower()) {
        // 将小写字母转换为大写
        QString upperText = inputText.toUpper();

        // 构造一个新的QKeyEvent，包含大写字母
        QKeyEvent *upperEvent = new QKeyEvent(event->type(),          // 事件类型
                                              event->key(),           // 保持原始按键码
                                              event->modifiers(),     // 保持修饰键状态
                                              upperText,              // 替换为大写文本
                                              event->isAutoRepeat(),  // 保持自动重复状态
                                              event->count()          // 保持计数
        );

        // 调用父类处理新的大写事件
        QsciScintilla::keyPressEvent(upperEvent);

        // 清理内存
        delete upperEvent;
        return;  // 不处理原始事件
    }

    // 如果正在选区
    if (m_selecting) {
        if (event->key() == Qt::Key_Left || event->key() == Qt::Key_Right || event->key() == Qt::Key_Up ||
            event->key() == Qt::Key_Down || event->key() == Qt::Key_Home || event->key() == Qt::Key_End) {
            // 获取当前光标位置
            int curRow, curCol;
            getCursorPosition(&curRow, &curCol);

            // 获取行数和最后一行的长度，linelength()函数返回的是字节数不是字符数
            int lineCount = lines();
            int lastLineLength = text(lineCount - 1).length();

            if (event->key() == Qt::Key_Up) {  // 上移一行
                if (curRow > 0) {
                    if (curRow == lineCount - 1 && curCol == lastLineLength && m_selectionColRecord < lastLineLength) {
                        // 情况B处理，仍留在最后一行，列位置还原为 m_selectionColRecord
                        curRow++;
                    }
                    // 计算出上一行最右边的列位置。对于非最后一行的所有行，行末有一个换行字符需要减掉
                    int lastIndex = curRow - 1 == lineCount - 1 ? lastLineLength : text(curRow - 1).length() - 1;
                    int targetCol = qBound(0, m_selectionColRecord, lastIndex);
                    setCursorPosition(curRow - 1, targetCol);
                } else if (curCol > 0 && m_selectionColRecord > 0) {  // 已经在第一行，但左边还有内容
                    // 光标移到第一行最开头，不更新 m_selectionColRecord（情况A）
                    setCursorPosition(0, 0);
                }
            } else if (event->key() == Qt::Key_Down) {  // 下移一行
                if (curRow < lineCount - 1) {
                    if (curRow == 0 && curCol == 0 && m_selectionColRecord > 0) {
                        // 情况A处理，仍留在第一行，列位置还原为 m_selectionColRecord
                        curRow--;
                    }
                    // 计算出下一行最右边的列位置。对于非最后一行的所有行，行末有一个换行字符需要减掉
                    int lastIndex = curRow + 1 == lineCount - 1 ? lastLineLength : text(curRow + 1).length() - 1;
                    int targetCol = qBound(0, m_selectionColRecord, lastIndex);
                    setCursorPosition(curRow + 1, targetCol);
                } else if (curCol < lastLineLength &&
                           m_selectionColRecord < lastLineLength) {  // 已经在最后一行，但右边还有内容
                    // 光标移到最后一行最末尾，不更新 m_selectionColRecord（情况B）
                    setCursorPosition(lineCount - 1, lastLineLength);
                }
            } else if (event->key() == Qt::Key_Left) {  // 左移一个字符
                if (curCol > 0) {
                    setCursorPosition(curRow, curCol - 1);
                    m_selectionColRecord = curCol - 1;
                }
            } else if (event->key() == Qt::Key_Right) {  // 右移一个字符
                // 计算出此行最右边的列位置。对于非最后一行的所有行，行末有一个换行字符需要减掉
                int lastIndex = curRow == lineCount - 1 ? lastLineLength : text(curRow).length() - 1;

                if (curCol < lastIndex) {
                    setCursorPosition(curRow, curCol + 1);
                    m_selectionColRecord = curCol + 1;
                }
            } else if (event->key() == Qt::Key_Home) {  // 左移到行首
                if (curCol > 0) {
                    setCursorPosition(curRow, 0);
                    m_selectionColRecord = 0;
                }
            } else {  // 右移到行尾
                // 计算出此行最右边的列位置。对于非最后一行的所有行，行末有一个换行字符需要减掉
                int lastIndex = curRow == lineCount - 1 ? lastLineLength : text(curRow).length() - 1;

                if (curCol < lastIndex) {
                    setCursorPosition(curRow, lastIndex);
                    m_selectionColRecord = lastIndex;
                }
            }

            return;  // 不处理原始事件
        } else if (event->key() == Qt::Key_Backspace || event->key() == Qt::Key_Delete) {
            // 剪切选区内容
            cut();

            // 删除选区内容，并结束选区
            clearSelection();

            return;  // 不处理原始事件（若处理会额外删除光标前面或后面的一个字符）
        } else if (!inputText.isEmpty()) {
            if (event->modifiers() == Qt::NoModifier) {
                // 剪切选区内容
                cut();
            }

            // 输入了内容，结束选区
            clearSelection();

            // 继续交给父类处理（添加输入的文本或执行快捷键操作）
        }
    }

    // 对于其他按键，调用父类处理
    QsciScintilla::keyPressEvent(event);
}

// 开始监控文件
void GCodeEditor::startWatchingFile(const QString &filename) {
    // 先停止之前的监控
    stopWatchingFile();

    // 添加新文件到监控列表
    if (!filename.isEmpty()) {
        m_fileWatcher->addPath(filename);
    }
}

// 停止监控文件
void GCodeEditor::stopWatchingFile() {
    // 移除所有监控的文件
    if (!m_fileWatcher->files().isEmpty()) {
        m_fileWatcher->removePaths(m_fileWatcher->files());
    }
}

// 开始选区
void GCodeEditor::beginSelection() {
    m_selecting = true;

    // 记录此位置
    getCursorPosition(&m_selectionBeginRow, &m_selectionBeginCol);
    m_selectionColRecord = m_selectionBeginCol;

    // 将选区置空（起始位置和终止位置相同即为空）
    setSelection(m_selectionBeginRow, m_selectionBeginCol, m_selectionBeginRow, m_selectionBeginCol);

    emit selectionBegun();

    LOG_DEBUG("开始选区，行：{}，列：{}", m_selectionBeginRow, m_selectionBeginCol);
}

// 清除选区
void GCodeEditor::clearSelection() {
    m_selecting = false;

    // 获取当前光标位置
    int curRow, curCol;
    getCursorPosition(&curRow, &curCol);

    // 将选区置空（起始位置和终止位置相同即为空）
    setSelection(curRow, curCol, curRow, curCol);

    // 重置选区变量
    m_selectionBeginRow = -1;
    m_selectionBeginCol = -1;
    m_selectionColRecord = -1;

    emit selectionCleared();
}

// 切换选区首尾
void GCodeEditor::toggleSelectionSide() {
    if (!m_selecting) {
        return;
    }

    // 获取当前光标位置
    int curRow, curCol;
    getCursorPosition(&curRow, &curCol);
    setCursorPosition(m_selectionBeginRow, m_selectionBeginCol);

    m_selectionColRecord = m_selectionBeginCol;
    m_selectionBeginRow = curRow;
    m_selectionBeginCol = curCol;
}

// 复制选区
void GCodeEditor::copySelection() {
    // 将选区内容复制到剪贴板
    copy();

    clearSelection();
}

// 剪切选区
void GCodeEditor::cutSelection() {
    // 将选区内容剪切到剪贴板
    cut();

    clearSelection();
}

// 粘贴至光标
void GCodeEditor::pasteToCursor() {
    // 从剪贴板粘贴到光标
    paste();
}

// 全选文本
void GCodeEditor::selectAllText() {
    if (!m_selecting) {
        m_selecting = true;

        emit selectionBegun();
    }

    LOG_DEBUG("全选文本");

    m_selectionBeginRow = 0;
    m_selectionBeginCol = 0;

    // 获取行数和最后一行的长度
    int lineCount = lines();
    int lastLineLength = text(lineCount - 1).length();

    // 将光标置于最后一行末尾
    setCursorPosition(lineCount - 1, lastLineLength);
    m_selectionColRecord = lastLineLength;

    setSelection(0, 0, lineCount - 1, lastLineLength);
}

// 进行撤销操作
void GCodeEditor::performUndo() {
    if (isUndoAvailable()) {
        undo();
        // 结束选区状态
        clearSelection();
    } else {
        LOG_WARN("当前没有可用的撤销操作");
    }
}

// 进行重做操作
void GCodeEditor::performRedo() {
    if (isRedoAvailable()) {
        redo();
        // 结束选区状态
        clearSelection();
    } else {
        LOG_WARN("当前没有可用的重做操作");
    }
}

// 跳转到上一个目标，返回是否找到目标
bool GCodeEditor::jumpToPrev(const QString &target) {
    // 获取当前光标位置
    int curRow, curCol;
    getCursorPosition(&curRow, &curCol);

    auto targetLength = target.length();

    // 目标和方向未改变，继续查找
    if (target == m_lastFindTarget && !m_lastFindWasForward) {
        return findNext();
    }

    // 检查当前光标位置是否是匹配，如果是则前移光标
    if (curCol >= targetLength) {
        auto cursorText = text(curRow).mid(curCol - targetLength, targetLength);
        LOG_DEBUG("光标字符串：{}", cursorText.toStdString());

        if (QString::compare(cursorText, target, Qt::CaseInsensitive) == 0) {
            LOG_DEBUG("光标在目标上，向前跳过这个目标");
            curCol -= targetLength;  // 前移到这个目标之前
        }
    }

    m_lastFindTarget = target;
    m_lastFindWasForward = false;

    // 不使用RE，大小写不敏感，不使用整词匹配，不循环查找，向上
    return findFirst(target, false, false, false, false, false, curRow, curCol);
}

// 跳转到下一个目标，返回是否找到目标
bool GCodeEditor::jumpToNext(const QString &target) {
    // 获取当前光标位置
    int curRow, curCol;
    getCursorPosition(&curRow, &curCol);

    // 目标和方向未改变，继续查找
    if (target == m_lastFindTarget && m_lastFindWasForward) {
        return findNext();
    }

    m_lastFindTarget = target;
    m_lastFindWasForward = true;

    // 不使用RE，大小写不敏感，不使用整词匹配，不循环查找，向下
    return findFirst(target, false, false, false, false, true, curRow, curCol);
}

// 文件变化处理槽函数
void GCodeEditor::onFileChanged(const QString &path) {
    LOG_INFO("文件 {} 已更改", path.toStdString());

    if (path == m_currentFileName) {
        // 重新加载文件内容
        QFile file(path);
        if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            QTextStream in(&file);

            QString updatedText = in.readAll();
            if (text() != updatedText) {
                setText(updatedText);
            } else {
                LOG_INFO("文件 {} 内容无实际更改", path.toStdString());
            }

            file.close();

            // 重新添加监控（某些系统在文件修改后会自动移除监控）
            if (!m_fileWatcher->files().contains(path)) {
                m_fileWatcher->addPath(path);
            }
        } else {
            LOG_WARN("无法重新加载文件内容");
        }
    }

    emit fileChanged(path);
}

// 光标位置变化处理槽函数
void GCodeEditor::onCursorPosChanged(int row, int col) {
    LOG_DEBUG("光标移动，行：{}，列：{}", row, col);

    if (m_selecting) {
        // 更新选区范围
        setSelection(m_selectionBeginRow, m_selectionBeginCol, row, col);
    }
}

// === 多类型行标记功能实现 ===

void GCodeEditor::highlightLine(int lineNumber, LineMarkerType type) {
    if (lineNumber < 0) {
        LOG_WARN("GCodeEditor: 无效的行号: {}", lineNumber);
        return;
    }

    // 先清除该类型的之前高亮
    clearHighlight(type);

    // 添加新的标记
    int markerNumber = getMarkerNumber(type);
    markerAdd(lineNumber, markerNumber);
    m_highlightedLines[type] = lineNumber;

    // 将视图滚动到当前行
    ensureLineVisible(lineNumber);

    LOG_DEBUG("GCodeEditor: 已高亮第{}行（类型: {}）", lineNumber + 1, static_cast<int>(type));
}

void GCodeEditor::clearHighlight(LineMarkerType type) {
    if (!m_highlightedLines.contains(type) || m_highlightedLines[type] < 0) {
        return;
    }

    int lineNumber = m_highlightedLines[type];
    int markerNumber = getMarkerNumber(type);

    // 删除标记
    markerDelete(lineNumber, markerNumber);
    m_highlightedLines.remove(type);

    LOG_DEBUG("GCodeEditor: 已清除第{}行的高亮（类型: {}）", lineNumber + 1, static_cast<int>(type));
}

void GCodeEditor::clearAllHighlights() {
    for (auto it = m_highlightedLines.begin(); it != m_highlightedLines.end(); ++it) {
        int lineNumber = it.value();
        int markerNumber = getMarkerNumber(it.key());
        if (lineNumber >= 0) {
            markerDelete(lineNumber, markerNumber);
        }
    }
    m_highlightedLines.clear();
    LOG_DEBUG("GCodeEditor: 已清除所有高亮");
}

int GCodeEditor::getHighlightedLine(LineMarkerType type) const { return m_highlightedLines.value(type, -1); }

QList<int> GCodeEditor::getAllHighlightedLines() const { return m_highlightedLines.values(); }

void GCodeEditor::setMarkerStyleConfig(LineMarkerType type, const MarkerStyleConfig &config) {
    if (m_hasFocus) {
        m_focusedStyles[type] = config;
    } else {
        m_unfocusedStyles[type] = config;
    }
    applyMarkerStyle(type);
}

void GCodeEditor::setMarkerStyleForFocusState(LineMarkerType type, const MarkerStyleConfig &focusedConfig,
                                              const MarkerStyleConfig &unfocusedConfig) {
    m_focusedStyles[type] = focusedConfig;
    m_unfocusedStyles[type] = unfocusedConfig;
    applyMarkerStyle(type);
}

GCodeEditor::MarkerStyleConfig GCodeEditor::getMarkerStyleConfig(LineMarkerType type) const {
    if (m_hasFocus) {
        return m_focusedStyles.value(type);
    } else {
        return m_unfocusedStyles.value(type);
    }
}

void GCodeEditor::applyMarkerStyle(LineMarkerType type) {
    const MarkerStyleConfig &config = getMarkerStyleConfig(type);
    int markerNumber = getMarkerNumber(type);

    if (!config.enabled) {
        return;
    }

    // 定义标记符号
    markerDefine(config.symbol, markerNumber);

    // 设置颜色
    setMarkerBackgroundColor(config.backgroundColor, markerNumber);
    if (config.foregroundColor.isValid()) {
        setMarkerForegroundColor(config.foregroundColor, markerNumber);
    }

    LOG_DEBUG("GCodeEditor: 应用标记样式 - 类型: {}, 符号: {}, 背景: {}, 焦点: {}", static_cast<int>(type),
              static_cast<int>(config.symbol), config.backgroundColor.name().toStdString(), m_hasFocus);
}

void GCodeEditor::updateMarkerForLine(int lineNumber, LineMarkerType type) {
    if (m_highlightedLines.contains(type) && m_highlightedLines[type] == lineNumber) {
        // 重新应用当前行的标记
        clearHighlight(type);
        highlightLine(lineNumber, type);
    }
}

void GCodeEditor::updateMarkersForFocusState() {
    bool oldFocusState = m_hasFocus;
    m_hasFocus = hasFocus();

    if (oldFocusState != m_hasFocus) {
        // 焦点状态改变，重新应用所有标记样式
        for (int i = ProgramExecution; i <= Warning; ++i) {
            LineMarkerType type = static_cast<LineMarkerType>(i);
            applyMarkerStyle(type);

            // 如果该类型有活跃的高亮，重新应用
            if (m_highlightedLines.contains(type)) {
                updateMarkerForLine(m_highlightedLines[type], type);
            }
        }

        emit focusStateChanged(m_hasFocus);
        LOG_DEBUG("GCodeEditor: 焦点状态变化: {}", m_hasFocus ? "获得" : "失去");
    }
}

void GCodeEditor::focusInEvent(QFocusEvent *event) {
    QsciScintilla::focusInEvent(event);
    updateMarkersForFocusState();
    updateCaretLineForFocusState();
    updateStateForEditability();
}

void GCodeEditor::focusOutEvent(QFocusEvent *event) {
    QsciScintilla::focusOutEvent(event);
    updateMarkersForFocusState();
    updateCaretLineForFocusState();
    updateStateForEditability();
}

// === CaretLine管理功能实现 ===

void GCodeEditor::setCaretLineStyle(const QColor &backgroundColor, bool enabled) {
    // 兼容原有接口，但现在使用焦点状态颜色
    m_caretLineColorFocused = backgroundColor;
    m_caretLineColorUnfocused = backgroundColor;  // 如果只提供一个颜色，两种状态使用相同颜色

    // 根据当前焦点状态设置颜色
    QColor currentColor = m_hasFocus ? m_caretLineColorFocused : m_caretLineColorUnfocused;
    setCaretLineBackgroundColor(currentColor);

    if (enabled) {
        // 根据当前焦点状态决定是否显示
        updateCaretLineForFocusState();
        // 确保CaretLine始终具有最高优先级
        SendScintilla(QsciScintilla::SCI_SETCARETLINEVISIBLEALWAYS, true);
    } else {
        // 禁用CaretLine
        setCaretLineVisible(false);
        m_caretLineEnabledWhenFocused = false;
        m_caretLineEnabledWhenUnfocused = false;
        SendScintilla(QsciScintilla::SCI_SETCARETLINEVISIBLEALWAYS, false);
    }

    LOG_DEBUG("GCodeEditor: 设置CaretLine样式 - 颜色: {}, 启用: {}, 最高优先级: {}",
              backgroundColor.name().toStdString(), enabled, enabled);
}

void GCodeEditor::setCaretLineStyleForFocus(const QColor &focusedColor, const QColor &unfocusedColor, bool enabled) {
    m_caretLineColorFocused = focusedColor;
    m_caretLineColorUnfocused = unfocusedColor;

    // 根据当前焦点状态设置颜色
    QColor currentColor = m_hasFocus ? m_caretLineColorFocused : m_caretLineColorUnfocused;
    setCaretLineBackgroundColor(currentColor);

    if (enabled) {
        // 根据当前焦点状态决定是否显示
        updateCaretLineForFocusState();
        // 确保CaretLine始终具有最高优先级
        SendScintilla(QsciScintilla::SCI_SETCARETLINEVISIBLEALWAYS, true);
    } else {
        // 禁用CaretLine
        setCaretLineVisible(false);
        m_caretLineEnabledWhenFocused = false;
        m_caretLineEnabledWhenUnfocused = false;
        SendScintilla(QsciScintilla::SCI_SETCARETLINEVISIBLEALWAYS, false);
    }

    LOG_DEBUG("GCodeEditor: 设置CaretLine焦点状态样式 - 获得焦点: {}, 失去焦点: {}, 启用: {}",
              focusedColor.name().toStdString(), unfocusedColor.name().toStdString(), enabled);
}

void GCodeEditor::setCaretLineVisibilityForFocus(bool visibleWhenFocused, bool visibleWhenUnfocused) {
    m_caretLineEnabledWhenFocused = visibleWhenFocused;
    m_caretLineEnabledWhenUnfocused = visibleWhenUnfocused;

    // 立即应用当前焦点状态的设置
    updateCaretLineForFocusState();

    LOG_DEBUG("GCodeEditor: 设置CaretLine焦点可见性 - 获得焦点: {}, 失去焦点: {}", visibleWhenFocused,
              visibleWhenUnfocused);
}

void GCodeEditor::updateCaretLineForFocusState() {
    bool shouldShow = m_hasFocus ? m_caretLineEnabledWhenFocused : m_caretLineEnabledWhenUnfocused;
    setCaretLineVisible(shouldShow);

    // 确保CaretLine始终保持最高优先级，不被标记遮挡
    if (shouldShow) {
        SendScintilla(QsciScintilla::SCI_SETCARETLINEVISIBLEALWAYS, true);
        // 根据焦点状态设置CaretLine颜色，与CurrentSelection标记一致
        QColor currentColor = m_hasFocus ? m_caretLineColorFocused : m_caretLineColorUnfocused;
        setCaretLineBackgroundColor(currentColor);
    }

    LOG_DEBUG("GCodeEditor: 更新CaretLine可见性 - 焦点: {}, 显示: {}, 颜色: {}, 最高优先级: {}，readOnly: {}",
              m_hasFocus ? "有" : "无", shouldShow ? "是" : "否",
              (m_hasFocus ? m_caretLineColorFocused : m_caretLineColorUnfocused).name().toStdString(), shouldShow,
              isReadOnly());
}

// === 状态管理功能实现 ===

void GCodeEditor::setState(const QString &state) {
    if (m_state != state) {
        m_state = state;

        // 设置样式表属性，使CSS可以根据状态应用不同样式
        setProperty("state", state);

        // 刷新样式
        style()->unpolish(this);
        style()->polish(this);

        LOG_DEBUG("GCodeEditor: 状态已更新为: {}", state.toStdString());
    }
}

void GCodeEditor::updateStateForEditability() {
// BUG169
#if 1
    setState("editing");
#else
    // 检查编辑器是否可编辑
    if (isReadOnly()) {
        setState("readonly");
    } else if (hasFocus() && !text().isEmpty()) {
        setState("editing");
    } else {
        setState("normal");
    }
#endif
}

void GCodeEditor::updateUndoAndRedoAvailability() {
    // 获取当前撤销重做的可用性
    bool undoAvailable = isUndoAvailable();
    bool redoAvailable = isRedoAvailable();

    if (undoAvailable != m_undoAvailable) {
        // 若改变，更新当前值并发出信号
        m_undoAvailable = undoAvailable;

        emit undoAvailabilityChanged(undoAvailable);
    }

    if (redoAvailable != m_redoAvailable) {
        // 若改变，更新当前值并发出信号
        m_redoAvailable = redoAvailable;

        emit redoAvailabilityChanged(redoAvailable);
    }
}

void GCodeEditor::setReadOnly(bool ro) {
    // 调用父类方法
    QsciScintilla::setReadOnly(ro);

    updateStateForEditability();

    LOG_DEBUG("GCodeEditor: 只读模式已设置为: {}, 状态: {}", ro ? "true" : "false", m_state.toStdString());
}