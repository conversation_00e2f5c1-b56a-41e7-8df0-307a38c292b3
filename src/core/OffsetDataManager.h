#pragma once

#include <QDateTime>
#include <QJsonDocument>
#include <QJsonObject>
#include <QList>
#include <QMap>
#include <QObject>
#include <QSet>
#include <QString>
#include <QStringList>
#include <QTimer>
#include <QVariant>
#include <functional>

#include "core/OffsetChannel.h"
#include "core/OffsetDataTypes.h"
#include "public/ICncInterface.h"

/**
 * @brief 零偏数据管理器（单例模式）
 * 负责管理各种零偏数据之间的关系和计算
 *
 * 核心功能：
 * 1. 统一管理所有通道的零偏数据类型
 * 2. 自动计算零偏之间的依赖关系
 * 3. 提供不同显示模式的数据支持
 * 4. 与CNC系统进行数据同步
 *
 * 依赖关系：
 * - 总基本零偏 = 通道专用基本零偏 + 精确值
 * - 当前工件坐标系偏移 = 当前选择的工件坐标系值（如G54、G55等）
 * - 总零偏 = 基本参考 + DRF + 总基本零偏 + 编程的零偏 + 循环参考 + 当前工件坐标系偏移
 * - WCS实际值 = MCS实际值 - 总零偏 - 当前刀具偏移 - TOFF
 */
class OffsetDataManager : public QObject {
    Q_OBJECT

   public:
    // 单例访问接口
    static OffsetDataManager* getInstance();
    static void destroyInstance();

    // 禁止拷贝构造和赋值操作
    OffsetDataManager(const OffsetDataManager&) = delete;
    OffsetDataManager& operator=(const OffsetDataManager&) = delete;

    // 通道管理
    void initializeChannel(int channelId);
    void removeChannel(int channelId);
    QList<int> getActiveChannels() const;

    // 数据管理（需要指定通道ID）
    void initializeOffsetData(int channelId);

    // 初始化零偏数据管理系统
    void initializeOffsetDataManager();
    ValidationResult updateOffsetValue(int channelId, OffsetType type, const PointXD& value,
                                       bool clearPreciseValue = false);
    ValidationResult updatePreciseValue(int channelId, OffsetType type, const PointXD& preciseValue);
    PointXD getOffsetValue(int channelId, OffsetType type) const;
    PointXD getPreciseValue(int channelId, OffsetType type) const;
    PointXD getEffectiveOffsetValue(int channelId, OffsetType type) const;
    OffsetDataItem getOffsetItem(int channelId, OffsetType type) const;

    // 计算和依赖关系
    void recalculateAllDependencies(int channelId);
    void recalculateDependencies(int channelId, OffsetType changedType);
    QList<OffsetType> getDependentTypes(int channelId, OffsetType type) const;
    bool hasCyclicDependency(int channelId, OffsetType type) const;

    // 获取显示数据
    QList<OffsetDataItem> getItemsForMode(int channelId, ZeroOffsetDisplayMode mode) const;

    // 分组相关方法
    QList<OffsetDataItem> getItemsByGroup(int channelId, int groupId) const;
    QMap<int, QList<OffsetDataItem>> getItemsGrouped(int channelId) const;
    QList<int> getAvailableGroupIds(int channelId) const;
    QString getGroupName(int groupId) const;

    // 数据持久化
    bool loadFromCncSystem(int channelId);
    bool saveToCncSystem(int channelId);
    bool saveSpecificOffset(int channelId, OffsetType type);
    bool loadFromFile(int channelId, const QString& filePath);
    bool saveToFile(int channelId, const QString& filePath) const;

    // 批量操作
    bool batchUpdateOffsets(int channelId, const QMap<OffsetType, PointXD>& updates);
    bool batchUpdatePreciseValues(int channelId, const QMap<OffsetType, PointXD>& preciseUpdates);
    bool resetAllOffsets(int channelId);
    bool resetAllPreciseValues(int channelId);
    bool resetOffsetGroup(int channelId, ZeroOffsetDisplayMode mode);

    // 状态查询
    bool hasUnsavedChanges(int channelId) const;
    void markAsSaved(int channelId);
    QStringList getValidationErrors(int channelId) const;
    QStringList getValidationWarnings(int channelId) const;

    // 当前工件坐标系管理
    OffsetType getCurrentWorkOffsetType(int channelId) const;
    bool setCurrentWorkOffsetType(int channelId, OffsetType type);
    QString getCurrentWorkOffsetName(int channelId) const;

    // 当前刀具偏移管理
    bool updateCurrentToolOffset(int channelId);
    bool loadCurrentToolOffsetFromCnc(int channelId, int toolNumber = -1);

    // 动态工件坐标系支持
    void initializeSupportedWorkOffsets();
    QList<QString> getSupportedWorkOffsetNames() const;
    QList<OffsetType> getSupportedWorkOffsetTypes() const;
    QMap<QString, OffsetType> getWorkOffsetNameMapping() const;
    OffsetType getWorkOffsetTypeByName(const QString& name) const;
    QString getWorkOffsetNameByType(OffsetType type) const;

    // 配置管理
    void setConfiguration(const OffsetManagerConfig& config);
    OffsetManagerConfig getConfiguration() const;

    // 自动刷新管理
    void requestAutoRefresh(QObject* subscriber);
    void releaseAutoRefresh(QObject* subscriber);

    // 自动保存和刷新功能
    void startAutoSave();
    void stopAutoSave();
    bool isAutoSaveEnabled() const;
    void triggerAutoSave(int channelId);  // 触发指定通道的自动保存
    void triggerAutoSaveAllChannels();    // 触发所有通道的自动保存
    void startAutoRefresh();
    void stopAutoRefresh();
    bool isAutoRefreshEnabled() const;
    void setAutoSaveInterval(int milliseconds);  // 现在作为延迟时间使用
    void setAutoRefreshInterval(int milliseconds);
    int getAutoSaveInterval() const;
    int getAutoRefreshInterval() const;
    bool loadFromAutoSaveFile(int channelId);
    QString getAutoSaveFilePath(int channelId) const;
    void createBackupFile(int channelId);
    void cleanupOldBackups(int channelId);

    // 数据验证
    ValidationResult validateOffsetValue(int channelId, OffsetType type, const PointXD& value) const;
    ValidationResult validateAllData(int channelId) const;

    // 数据比较和分析
    QMap<OffsetType, PointXD> compareWithCncSystem(int channelId) const;
    double calculateTotalDeviation(int channelId, OffsetType type1, OffsetType type2) const;

    /**
     * @brief 根据期望的工件坐标系，返回匹配的当前工件坐标系
     *
     * @param channelId 通道ID
     * @param axisIndex 轴索引
     * @param wcsValue 期望的工件坐标值
     * @param[out] currentWorkOffset 匹配的当前工件坐标系
     * @return bool 执行结果
     */
    bool calculateCurrentWorkOffsetFromWCS(int channelId, int axisIndex, double desiredWcsValue,
                                           double& currentWorkOffset);

    /**
     * @brief 根据期望的工件坐标系，更新匹配的当前工件坐标系
     *
     * @param channelId 通道ID
     * @param axisIndex 轴索引
     * @param currentWorkOffset 期望的工件坐标值
     * @return bool 执行结果
     */
    bool updateCurrentWorkOffset(int channelId, int axisIndex, double currentWorkOffset);

    // 刀具长度计算功能
    /**
     * @brief 根据期望的工件坐标和当前机器坐标计算所需的刀具长度
     *
     * 计算公式：当前刀具偏移 = MCS实际值 - 总零偏 - WCS期望值 - TOFF
     *
     * @param channelId 通道ID
     * @param currentMachinePosition 当前机器坐标位置
     * @param desiredWorkPosition 期望的工件坐标位置
     * @param[out] calculatedToolOffset 计算得到的刀具偏移值
     * @return ValidationResult 计算结果和验证信息
     */
    ValidationResult calculateRequiredToolLength(int channelId, const PointXD& currentMachinePosition,
                                                 const PointXD& desiredWorkPosition,
                                                 PointXD& calculatedToolOffset) const;

    /**
     * @brief 根据期望工件坐标计算刀具长度（自动获取当前机器坐标）
     *
     * 此方法会自动从CNC系统获取当前机器坐标，然后计算所需的刀具长度
     *
     * @param channelId 通道ID
     * @param desiredWorkPosition 期望的工件坐标位置
     * @param[out] calculatedToolOffset 计算得到的刀具偏移值
     * @return ValidationResult 计算结果和验证信息
     */
    ValidationResult calculateRequiredToolLengthAuto(int channelId, const PointXD& desiredWorkPosition,
                                                     PointXD& calculatedToolOffset) const;

    /**
     * @brief 计算特定轴的刀具长度
     *
     * 针对单个轴计算刀具长度，常用于Z轴刀具长度计算
     *
     * @param channelId 通道ID
     * @param axisIndex 轴索引（通常为Z轴）
     * @param currentMachinePos 当前机器坐标
     * @param desiredWorkPos 期望工件坐标
     * @param[out] calculatedToolLength 计算得到的刀具长度
     * @return ValidationResult 计算结果和验证信息
     */
    ValidationResult calculateAxisToolLength(int channelId, int axisIndex, double currentMachinePos,
                                             double desiredWorkPos, double& calculatedToolLength) const;

    /**
     * @brief 验证计算的刀具长度是否在合理范围内
     *
     * @param channelId 通道ID
     * @param toolOffset 要验证的刀具偏移值
     * @return ValidationResult 验证结果
     */
    ValidationResult validateToolLength(int channelId, const PointXD& toolOffset) const;

    /**
     * @brief 应用计算得到的刀具长度到当前刀具
     *
     * 将计算结果应用到当前激活的刀具偏移中
     *
     * @param channelId 通道ID
     * @param toolOffset 要应用的刀具偏移值
     * @param updateCncSystem 是否立即更新到CNC系统
     * @return ValidationResult 应用结果
     */
    ValidationResult applyCalculatedToolLength(int channelId, const PointXD& toolOffset, bool updateCncSystem = true);

    // 工具方法
    QString offsetTypeToString(OffsetType type) const;
    OffsetType stringToOffsetType(const QString& str) const;
    QJsonObject toJsonObject(int channelId) const;
    bool fromJsonObject(int channelId, const QJsonObject& obj);

   signals:
    // 数据变更信号
    void offsetValueChanged(int channelId, OffsetType type, const PointXD& newValue);
    void preciseValueChanged(int channelId, OffsetType type, const PointXD& newPreciseValue);
    void currentWorkOffsetChanged(int channelId, OffsetType newType);
    void dataValidationFailed(int channelId, const QStringList& errors);
    void syncWithCncCompleted(int channelId, bool success);
    void configurationChanged(const OffsetManagerConfig& config);

    // 文件保存信号
    void autoFileSaveCompleted(int channelId, bool success, const QString& filePath);
    void backupCreated(int channelId, const QString& backupPath);

    // 通道管理信号
    void channelInitialized(int channelId);
    void channelRemoved(int channelId);

   private slots:
    void onSubscriberDestroyed(QObject* obj = nullptr);

   private:
    // 单例构造和析构
    explicit OffsetDataManager(QObject* parent = nullptr);
    ~OffsetDataManager();

    // 内部方法
    void performAutoSave();
    void performAutoRefresh();
    void performDelayedSave(int channelId);  // 执行延迟保存
    void performDelayedSaveAllChannels();    // 执行所有通道的延迟保存
    void ensureAutoSaveDirectory();

    // 成员变量
    static OffsetDataManager* s_instance;  // 单例实例
    QMap<int, OffsetChannel*> m_channels;  // 通道数据映射
    OffsetManagerConfig m_config;          // 全局配置信息

    // 自动保存和刷新定时器
    QTimer* m_delaySaveTimer;                 // 延迟保存定时器（单次触发）
    QTimer* m_autoRefreshTimer;               // 自动刷新定时器
    bool m_autoSaveEnabled;                   // 自动保存启用状态
    bool m_autoRefreshEnabled;                // 自动刷新启用状态
    QSet<int> m_pendingSaveChannels;          // 待保存的通道ID集合
    QSet<QObject*> m_autoRefreshSubscribers;  // 自动刷新订阅者集合

    // 动态工件坐标系支持（全局共享）
    QMap<QString, OffsetType> m_workOffsetNameMapping;  // 工件坐标系名称到类型的映射
    QMap<OffsetType, QString> m_workOffsetTypeMapping;  // 工件坐标系类型到名称的映射
    QList<OffsetType> m_supportedWorkOffsetTypes;       // 系统支持的工件坐标系类型列表
};