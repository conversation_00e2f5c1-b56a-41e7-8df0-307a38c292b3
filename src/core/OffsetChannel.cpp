#include "core/OffsetChannel.h"

#include <QDir>
#include <QFile>
#include <QFileInfo>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonParseError>
#include <functional>

#include "ErrorCode.h"
#include "ICncInterface.h"
#include "core/AppLogger.h"
#include "core/CncManager.h"
#include "core/OffsetDataManager.h"

OffsetChannel::OffsetChannel(int channelId, const OffsetManagerConfig& config, QObject* parent)
    : QObject(parent),
      m_channelId(channelId),
      m_hasUnsavedChanges(false),
      m_syncInProgress(false),
      m_hasUnsavedFileChanges(false),
      m_config(config) {
    m_syncTimer = new QTimer(this);
    connect(m_syncTimer, &QTimer::timeout, this, &OffsetChannel::performSync);

    m_autoSaveTimer = new QTimer(this);
    connect(m_autoSaveTimer, &QTimer::timeout, this, &OffsetChannel::performAutoFileSave);
}

OffsetChannel::~OffsetChannel() {}

void OffsetChannel::initializeOffsetData() {
    auto* manager = OffsetDataManager::getInstance();
    const auto& supportedWorkOffsets = manager->getSupportedWorkOffsetTypes();
    if (!supportedWorkOffsets.isEmpty()) {
        m_currentWorkOffset = supportedWorkOffsets.first();
    } else {
        m_currentWorkOffset = OffsetType::DynamicWorkOffsetStart;
    }

    setupOffsetDefinitions();
    setupDependencyRelations();

    // 尝试从自动保存文件加载数据
    QString autoSaveFile = getAutoSaveFilePath();
    bool loadedFromFile = false;
    if (QFile::exists(autoSaveFile)) {
        loadedFromFile = loadFromFile(autoSaveFile);
        LOG_INFO("通道 {} 从自动保存文件加载数据: {}", m_channelId, loadedFromFile ? "成功" : "失败");

        // 从文件加载成功后，将数据同步到CNC系统
        if (loadedFromFile) {
            bool savedToCnc = saveToCncSystem();
            LOG_INFO("通道 {} 从文件加载后同步到CNC系统: {}", m_channelId, savedToCnc ? "成功" : "失败");
            if (savedToCnc) {
                // 同步成功后，标记为已保存状态
                m_hasUnsavedChanges = false;
            }
        }
    }

    // 如果没有自动保存文件或加载失败，从CNC系统加载
    if (!loadedFromFile) {
        bool loadedFromCnc = loadFromCncSystem();
        LOG_INFO("通道 {} 从CNC系统加载数据: {}", m_channelId, loadedFromCnc ? "成功" : "失败");
    }

    // 重新计算所有依赖关系
    recalculateAllDependencies();
}

ValidationResult OffsetChannel::updateOffsetValue(OffsetType type, const PointXD& value, bool clearPreciseValue) {
    if (!m_offsetData.contains(type)) {
        return {false, "无效的零偏类型"};
    }

    // 验证逻辑
    ValidationResult validation = validateOffsetValue(type, value);
    if (!validation.isValid) {
        LOG_WARN("通道 {} 零偏类型 {} 验证失败: {}", m_channelId, static_cast<int>(type),
                 validation.errorMessage.toStdString());
        return validation;
    }

    // 检查只读状态
    if (m_offsetData[type].isReadOnly) {
        return {false, "该零偏类型为只读，不能修改"};
    }

    m_offsetData[type].value = value;
    if (clearPreciseValue) {
        m_offsetData[type].preciseValue.coordinates.clear();
        emit preciseValueChanged(type, m_offsetData[type].preciseValue);
    }

    // 特殊处理：当修改CurrentWorkOffset时，需要同步到真正的工件坐标系
    if (type == OffsetType::CurrentWorkOffset) {
        updateCurrentWorkOffsetToRealWCS(value, m_offsetData[type].preciseValue);
    }

    emit offsetValueChanged(type, value);
    recalculateDependencies(type);
    m_hasUnsavedChanges = true;
    m_hasUnsavedFileChanges = true;

    return ValidationResult(true);
}

QList<OffsetDataItem> OffsetChannel::getItemsForMode(ZeroOffsetDisplayMode mode) const {
    QList<OffsetDataItem> items;

    switch (mode) {
        case ZeroOffsetDisplayMode::None:
            // 返回空列表
            break;

        case ZeroOffsetDisplayMode::Valid:
            // 有效模式：MCS实际值、当前工件坐标、总零偏、当前刀具偏移、WCS实际值
            if (m_offsetData.contains(OffsetType::MachineCoordinate))
                items.append(m_offsetData[OffsetType::MachineCoordinate]);
            if (m_offsetData.contains(OffsetType::BasicReference))
                items.append(m_offsetData[OffsetType::BasicReference]);
            if (m_offsetData.contains(OffsetType::CurrentWorkOffset))
                items.append(m_offsetData[OffsetType::CurrentWorkOffset]);
            if (m_offsetData.contains(OffsetType::TotalOffset)) items.append(m_offsetData[OffsetType::TotalOffset]);
            if (m_offsetData.contains(OffsetType::CurrentToolOffset))
                items.append(m_offsetData[OffsetType::CurrentToolOffset]);
            if (m_offsetData.contains(OffsetType::TOFF)) items.append(m_offsetData[OffsetType::TOFF]);
            if (m_offsetData.contains(OffsetType::WorkCoordinate))
                items.append(m_offsetData[OffsetType::WorkCoordinate]);
            break;

        case ZeroOffsetDisplayMode::Overview:
            // 概览模式：机床坐标、工件坐标、相对坐标
            if (m_offsetData.contains(OffsetType::MachineCoordinate))
                items.append(m_offsetData[OffsetType::MachineCoordinate]);
            if (m_offsetData.contains(OffsetType::DRF)) items.append(m_offsetData[OffsetType::DRF]);
            if (m_offsetData.contains(OffsetType::BasicReference))
                items.append(m_offsetData[OffsetType::BasicReference]);
            if (m_offsetData.contains(OffsetType::TotalBasicOffset))
                items.append(m_offsetData[OffsetType::TotalBasicOffset]);
            if (m_offsetData.contains(OffsetType::CurrentWorkOffset))
                items.append(m_offsetData[OffsetType::CurrentWorkOffset]);
            if (m_offsetData.contains(OffsetType::CoordTransformRef))
                items.append(m_offsetData[OffsetType::CoordTransformRef]);
            if (m_offsetData.contains(OffsetType::ProgrammedOffset))
                items.append(m_offsetData[OffsetType::ProgrammedOffset]);
            if (m_offsetData.contains(OffsetType::CycleReference))
                items.append(m_offsetData[OffsetType::CycleReference]);
            if (m_offsetData.contains(OffsetType::TotalOffset)) items.append(m_offsetData[OffsetType::TotalOffset]);
            if (m_offsetData.contains(OffsetType::CurrentToolOffset))
                items.append(m_offsetData[OffsetType::CurrentToolOffset]);
            if (m_offsetData.contains(OffsetType::TOFF)) items.append(m_offsetData[OffsetType::TOFF]);
            if (m_offsetData.contains(OffsetType::WorkCoordinate))
                items.append(m_offsetData[OffsetType::WorkCoordinate]);
            break;

        case ZeroOffsetDisplayMode::Basic:
            // 基本模式：通道专用基本零偏
            if (m_offsetData.contains(OffsetType::ChannelBasicOffset))
                items.append(m_offsetData[OffsetType::ChannelBasicOffset]);
            break;

        case ZeroOffsetDisplayMode::WorkCoordinate:
            // 工件坐标系模式：所有工件坐标系
            for (auto it = m_offsetData.begin(); it != m_offsetData.end(); ++it) {
                OffsetType type = it.key();
                // 只显示动态工件坐标系
                if (type >= OffsetType::DynamicWorkOffsetStart) {
                    items.append(it.value());
                }
            }
            break;
    }

    return items;
}

void OffsetChannel::performSync() {
    if (m_syncInProgress) {
        return;
    }

    m_syncInProgress = true;

    bool success = true;

    // 从CNC系统加载数据
    if (!loadFromCncSystem()) {
        LOG_WARN("通道 {} 从CNC系统加载数据失败", m_channelId);
        success = false;
    }

    // 如果有未保存的更改，保存到CNC系统
    if (success && m_hasUnsavedChanges) {
        if (!saveToCncSystem()) {
            LOG_WARN("通道 {} 保存数据到CNC系统失败", m_channelId);
            success = false;
        } else {
            m_hasUnsavedChanges = false;
        }
    }

    m_syncInProgress = false;
    emit syncWithCncCompleted(success);
}

void OffsetChannel::performAutoFileSave() {
    if (!m_hasUnsavedFileChanges) {
        return;
    }

    QString filePath = getAutoSaveFilePath();

    bool success = saveToFile(filePath);
    if (success) {
        m_hasUnsavedFileChanges = false;
        m_lastAutoSaveTime = QDateTime::currentDateTime();
        LOG_INFO("通道 {} 自动保存成功", m_channelId);
    } else {
        LOG_ERROR("通道 {} 自动保存失败", m_channelId);
    }

    emit autoFileSaveCompleted(success, filePath);
}

void OffsetChannel::setupOffsetDefinitions() {
    m_offsetData.clear();
    auto* manager = OffsetDataManager::getInstance();

    m_offsetData[OffsetType::MachineCoordinate] = OffsetDataItem(OffsetType::MachineCoordinate, tr("MCS实际值"), true);
    m_offsetData[OffsetType::WorkCoordinate] = OffsetDataItem(OffsetType::WorkCoordinate, tr("WCS实际值"), true);
    m_offsetData[OffsetType::DRF] = OffsetDataItem(OffsetType::DRF, tr("DRF"), true);
    m_offsetData[OffsetType::BasicReference] = OffsetDataItem(OffsetType::BasicReference, tr("基本参考"), false);
    m_offsetData[OffsetType::ChannelBasicOffset] =
        OffsetDataItem(OffsetType::ChannelBasicOffset, tr("通道专用基本零偏"), false);
    m_offsetData[OffsetType::TotalBasicOffset] = OffsetDataItem(OffsetType::TotalBasicOffset, tr("总基本零偏"), true);
    m_offsetData[OffsetType::CoordTransformRef] =
        OffsetDataItem(OffsetType::CoordTransformRef, tr("坐标转换参考"), true);
    m_offsetData[OffsetType::ProgrammedOffset] = OffsetDataItem(OffsetType::ProgrammedOffset, tr("编程的零偏"), true);
    m_offsetData[OffsetType::CycleReference] = OffsetDataItem(OffsetType::CycleReference, tr("循环参考"), true);
    m_offsetData[OffsetType::CurrentWorkOffset] =
        OffsetDataItem(OffsetType::CurrentWorkOffset, tr("当前工件坐标系"), false);
    m_offsetData[OffsetType::TotalOffset] = OffsetDataItem(OffsetType::TotalOffset, tr("总零偏"), true);
    m_offsetData[OffsetType::CurrentToolOffset] = OffsetDataItem(OffsetType::CurrentToolOffset, tr("当前刀具"), true);
    m_offsetData[OffsetType::TOFF] = OffsetDataItem(OffsetType::TOFF, tr("TOFF"), true);

    const auto& workOffsetMapping = manager->getWorkOffsetNameMapping();
    // It's safer to get the CncManager instance directly when needed.
    if (CncManager::getInstance() && CncManager::getInstance()->isInitialized()) {
        const auto& systemConfig = CncManager::getInstance()->getSystemConfig();
        for (auto it = workOffsetMapping.begin(); it != workOffsetMapping.end(); ++it) {
            const QString& workOffsetName = it.key();
            OffsetType workOffsetType = it.value();
            bool isReadOnly = false;  // Default to writable

            auto cfg_it =
                std::find_if(systemConfig.supportedWorkOffsets.begin(), systemConfig.supportedWorkOffsets.end(),
                             [&](const auto& wcs) { return wcs.name == workOffsetName.toStdString(); });
            if (cfg_it != systemConfig.supportedWorkOffsets.end()) {
                isReadOnly = cfg_it->isReadOnly;
            }
            m_offsetData[workOffsetType] = OffsetDataItem(workOffsetType, workOffsetName, isReadOnly);
        }
    } else {
        for (auto it = workOffsetMapping.begin(); it != workOffsetMapping.end(); ++it) {
            m_offsetData[it.value()] = OffsetDataItem(it.value(), it.key(), false);
        }
    }
}

void OffsetChannel::setupDependencyRelations() {
    m_dependencies.clear();

    // 总基本零偏 = 通道专用基本零偏 + [精确值]
    m_dependencies[OffsetType::ChannelBasicOffset].append(OffsetType::TotalBasicOffset);

    // 总零偏 = 基本参考 + DRF + 总基本零偏 + 编程的零偏 + 循环参考 + 当前工件坐标系偏移
    m_dependencies[OffsetType::BasicReference].append(OffsetType::TotalOffset);
    m_dependencies[OffsetType::DRF].append(OffsetType::TotalOffset);
    m_dependencies[OffsetType::TotalBasicOffset].append(OffsetType::TotalOffset);
    m_dependencies[OffsetType::ProgrammedOffset].append(OffsetType::TotalOffset);
    m_dependencies[OffsetType::CycleReference].append(OffsetType::TotalOffset);
    m_dependencies[OffsetType::CurrentWorkOffset].append(OffsetType::TotalOffset);

    // WCS实际值 = MCS实际值 - 总零偏 - 当前刀具偏移 - TOFF
    m_dependencies[OffsetType::MachineCoordinate].append(OffsetType::WorkCoordinate);
    m_dependencies[OffsetType::TotalOffset].append(OffsetType::WorkCoordinate);
    m_dependencies[OffsetType::CurrentToolOffset].append(OffsetType::WorkCoordinate);
    m_dependencies[OffsetType::TOFF].append(OffsetType::WorkCoordinate);

    // 动态建立工件坐标系依赖关系
    setupWorkOffsetDependencies();
}

void OffsetChannel::setupWorkOffsetDependencies() {
    // 清理旧的工件坐标系依赖关系
    for (auto it = m_dependencies.begin(); it != m_dependencies.end(); ++it) {
        it.value().removeAll(OffsetType::CurrentWorkOffset);
    }

    // 当前选择的工件坐标系值影响CurrentWorkOffset
    if (m_offsetData.contains(m_currentWorkOffset)) {
        m_dependencies[m_currentWorkOffset].append(OffsetType::CurrentWorkOffset);
    }

    // CurrentWorkOffset的变化需要同步到真实的工件坐标系
    // 这个反向依赖在updateDependencyChain中特殊处理
}

void OffsetChannel::recalculateAllDependencies() {
    calculateTotalBasicOffset();
    calculateCurrentWorkOffset();
    calculateTotalOffset();
    calculateWorkCoordinate();
}

void OffsetChannel::recalculateDependencies(OffsetType changedType) {
    QSet<OffsetType> processed;
    updateDependencyChain(changedType, processed);
}

void OffsetChannel::updateDependencyChain(OffsetType changedType, QSet<OffsetType>& processed) {
    if (processed.contains(changedType)) {
        return;
    }
    processed.insert(changedType);

    const QList<OffsetType> dependents = m_dependencies.value(changedType);
    for (const auto depType : dependents) {
        switch (depType) {
            case OffsetType::TotalBasicOffset:
                calculateTotalBasicOffset();
                emit offsetValueChanged(depType, getOffsetValue(depType));
                break;
            case OffsetType::CurrentWorkOffset:
                // 处理工件坐标系变化导致的CurrentWorkOffset更新
                if (changedType >= OffsetType::DynamicWorkOffsetStart && changedType == m_currentWorkOffset) {
                    calculateCurrentWorkOffset();
                    emit offsetValueChanged(depType, getOffsetValue(depType));
                    emit preciseValueChanged(depType, getPreciseValue(depType));

                }
                // 处理CurrentWorkOffset变化需要同步到真实工件坐标系
                else if (changedType == OffsetType::CurrentWorkOffset) {
                    updateCurrentWorkOffsetToRealWCS(getOffsetValue(OffsetType::CurrentWorkOffset),
                                                     getPreciseValue(OffsetType::CurrentWorkOffset));
                }
                break;
            case OffsetType::TotalOffset:
                calculateTotalOffset();
                emit offsetValueChanged(depType, getOffsetValue(depType));
                break;
            case OffsetType::WorkCoordinate:
                calculateWorkCoordinate();
                emit offsetValueChanged(depType, getOffsetValue(depType));
                break;
            default:
                break;
        }
        updateDependencyChain(depType, processed);
    }
}

void OffsetChannel::calculateTotalBasicOffset() {
    const PointXD& channelBasic = m_offsetData[OffsetType::ChannelBasicOffset].getEffectiveValue();
    m_offsetData[OffsetType::TotalBasicOffset].value = channelBasic;
}

void OffsetChannel::calculateCurrentWorkOffset() {
    if (m_offsetData.contains(m_currentWorkOffset)) {
        // 同步主值和精确值
        m_offsetData[OffsetType::CurrentWorkOffset].value = m_offsetData[m_currentWorkOffset].value;
        m_offsetData[OffsetType::CurrentWorkOffset].preciseValue = m_offsetData[m_currentWorkOffset].preciseValue;
        m_offsetData[OffsetType::CurrentWorkOffset].displayName = getWorkOffsetNameByType(m_currentWorkOffset);
    }
}

void OffsetChannel::calculateTotalOffset() {
    PointXD total;

    // 手动实现坐标加法
    auto addPointXD = [&total](const PointXD& point) {
        for (const auto& [axisIndex, value] : point.coordinates) {
            total.coordinates[axisIndex] += value;
        }
    };

    addPointXD(m_offsetData[OffsetType::BasicReference].getEffectiveValue());
    addPointXD(m_offsetData[OffsetType::DRF].getEffectiveValue());
    addPointXD(m_offsetData[OffsetType::TotalBasicOffset].getEffectiveValue());
    addPointXD(m_offsetData[OffsetType::ProgrammedOffset].getEffectiveValue());
    addPointXD(m_offsetData[OffsetType::CycleReference].getEffectiveValue());
    addPointXD(m_offsetData[OffsetType::CurrentWorkOffset].getEffectiveValue());

    m_offsetData[OffsetType::TotalOffset].value = total;
}

void OffsetChannel::calculateWorkCoordinate() {
    // MCS实际值
    PointXD wcs = m_offsetData[OffsetType::MachineCoordinate].getEffectiveValue();

    // 手动实现坐标减法
    auto subtractPointXD = [&wcs](const PointXD& point) {
        for (const auto& [axisIndex, value] : point.coordinates) {
            double newValue = wcs.coordinates[axisIndex] - value;
            wcs.coordinates[axisIndex] = newValue;
        }
    };

    // WCS = MCS - 总零偏(基本零偏 + G54/G55/G56/G57/G58/G59)
    subtractPointXD(m_offsetData[OffsetType::TotalOffset].getEffectiveValue());
    // WCS = MCS - 总零偏(基本零偏 + G54/G55/G56/G57/G58/G59) - 当前刀具偏移
    subtractPointXD(m_offsetData[OffsetType::CurrentToolOffset].getEffectiveValue());
    // WCS = MCS - 总零偏(基本零偏 + G54/G55/G56/G57/G58/G59) - 当前刀具偏移 - TOFF
    subtractPointXD(m_offsetData[OffsetType::TOFF].getEffectiveValue());

    SystemConfig systemConfig = CncManager::getInstance()->getSystemConfig();
    for (const auto& [axisIndex, value] : wcs.coordinates) {
        if (systemConfig.axesConfigs[axisIndex].isDiameterProgramming) {
            // 直径编程的轴WCS = 2.0 * (MCS - 总零偏(基本零偏 + G54/G55/G56/G57/G58/G59) - 当前刀具偏移 - TOFF)
            double newValue = wcs.coordinates[axisIndex] * 2.0;
            // LOG_WARN("axis {} 乘2 oldValue={}, newValue={}", axisIndex, wcs.coordinates[axisIndex], newValue);
            wcs.coordinates[axisIndex] = newValue;
        }
    }

    m_offsetData[OffsetType::WorkCoordinate].value = wcs;
}

bool OffsetChannel::calculateCurrentWorkOffsetFromWCS(int axisIndex, double desiredWcsValue,
                                                      double& currentWorkOffset) {
    bool ret = true;
    PointXD wcsPd, offsetPd;
    double totalOffset;
    std::string currentWorkOffsetName;
    PointXD currentWorkOffsetPd;
    std::string errorMsg;

    auto cncManager = CncManager::getInstance();
    if (!cncManager || !cncManager->isInitialized()) {
        LOG_ERROR("CNC系统未初始化，无法更新当前工件坐标系");
        return false;
    }

    // 1. 获取当前工件坐标
    ErrorCode result = cncManager->getWorkPosition(m_channelId, wcsPd);
    if (result != ErrorCode::Success) {
        cncManager->getLastError(errorMsg);
        LOG_ERROR("获取工件坐标失败，错误码: {}", static_cast<int>(result));
        return false;
    }

    // 2. 计算当前工件坐标与用户输入的差值
    double diff = desiredWcsValue - wcsPd.coordinates[axisIndex];
    LOG_DEBUG("WCS从 {} 改为 {}，变化值: {}", wcsPd.coordinates[axisIndex], desiredWcsValue, diff);

    // 3. 获取当前工件坐标系名称
    result = cncManager->getCurrentWorkOffsetName(m_channelId, currentWorkOffsetName);
    if (result != ErrorCode::Success) {
        cncManager->getLastError(errorMsg);
        LOG_ERROR("获取当前工件坐标系名称失败，错误码: {}", errorMsg);
        return false;
    }

    // 4. 读取工件坐标系偏移值
    result = cncManager->getWorkOffsetValue(m_channelId, currentWorkOffsetName, currentWorkOffsetPd);
    if (result != ErrorCode::Success) {
        cncManager->getLastError(errorMsg);
        LOG_ERROR("获取工件坐标系 {} 偏移值失败，错误码: {}", currentWorkOffsetName, errorMsg);
        return false;
    }

    // 5. 计算新值，根据期望的axisWcsValue
    currentWorkOffset = currentWorkOffsetPd.coordinates[axisIndex] - diff;

    LOG_DEBUG("当前工件坐标系 {} 当前零偏: {}，拟调整值: {}", currentWorkOffsetName,
              currentWorkOffsetPd.coordinates[axisIndex], currentWorkOffset);
    return ret;
}

void OffsetChannel::updateCurrentWorkOffsetToRealWCS(const PointXD& value, const PointXD& preciseValue) {
    // 当用户修改CurrentWorkOffset时，需要将这个值同步到真正的工件坐标系（如G54、G55等）
    if (m_offsetData.contains(m_currentWorkOffset)) {
        // 更新真正的工件坐标系数据（主值和精确值）
        m_offsetData[m_currentWorkOffset].value = value;
        m_offsetData[m_currentWorkOffset].preciseValue = preciseValue;

        // 标记需要保存到CNC系统
        m_hasUnsavedChanges = true;
        m_hasUnsavedFileChanges = true;

        // 发送工件坐标系数据变更信号
        emit offsetValueChanged(m_currentWorkOffset, value);
        emit preciseValueChanged(m_currentWorkOffset, preciseValue);
    } else {
        LOG_WARN("通道 {} 当前工件坐标系 {} 不存在，无法同步", m_channelId, static_cast<int>(m_currentWorkOffset));
    }
}

PointXD OffsetChannel::getOffsetValue(OffsetType type) const {
    if (m_offsetData.contains(type)) {
        return m_offsetData[type].value;
    }
    return {};
}

PointXD OffsetChannel::getPreciseValue(OffsetType type) const {
    if (m_offsetData.contains(type)) {
        return m_offsetData[type].preciseValue;
    }
    return {};
}

PointXD OffsetChannel::getEffectiveOffsetValue(OffsetType type) const {
    if (m_offsetData.contains(type)) {
        return m_offsetData[type].getEffectiveValue();
    }
    return {};
}

OffsetDataItem OffsetChannel::getOffsetItem(OffsetType type) const { return m_offsetData.value(type); }

ValidationResult OffsetChannel::updatePreciseValue(OffsetType type, const PointXD& preciseValue) {
    if (!m_offsetData.contains(type)) {
        return {false, "无效的零偏类型"};
    }

    m_offsetData[type].preciseValue = preciseValue;
    emit preciseValueChanged(type, preciseValue);

    // 重新计算依赖项（因为精确值改变会影响有效值）
    recalculateDependencies(type);

    m_hasUnsavedChanges = true;
    m_hasUnsavedFileChanges = true;
    return ValidationResult(true);
}

OffsetType OffsetChannel::getCurrentWorkOffsetType() const { return m_currentWorkOffset; }

bool OffsetChannel::setCurrentWorkOffsetType(OffsetType type) {
    if (m_currentWorkOffset == type) {
        return true;  // 没有变化
    }

    // 检查新的工件坐标系是否存在
    if (!m_offsetData.contains(type)) {
        LOG_WARN("尝试设置不存在的工件坐标系: {}", static_cast<int>(type));
        return false;
    }

    OffsetType oldType = m_currentWorkOffset;
    m_currentWorkOffset = type;

    // 重新建立工件坐标系依赖关系
    setupWorkOffsetDependencies();

    // 重新计算当前工件坐标系偏移
    calculateCurrentWorkOffset();

    // 重新计算依赖项
    recalculateDependencies(OffsetType::CurrentWorkOffset);

    m_hasUnsavedChanges = true;
    m_hasUnsavedFileChanges = true;

    return true;
}

bool OffsetChannel::loadFromFile(const QString& filePath) {
    QFile file(filePath);
    if (!file.exists()) {
        LOG_WARN("文件不存在: {}", filePath.toStdString());
        return false;
    }

    if (!file.open(QIODevice::ReadOnly)) {
        LOG_ERROR("无法打开文件进行读取: {}", filePath.toStdString());
        return false;
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    if (error.error != QJsonParseError::NoError) {
        LOG_ERROR("JSON解析错误: {}", error.errorString().toStdString());
        return false;
    }

    if (!doc.isObject()) {
        LOG_ERROR("文件格式错误：根元素不是JSON对象");
        return false;
    }

    QJsonObject rootObj = doc.object();
    bool success = fromJsonObject(rootObj);

    if (success) {
        m_hasUnsavedFileChanges = false;
        LOG_INFO("通道 {} 从文件加载数据成功: {}", m_channelId, filePath.toStdString());
        emit fileLoadCompleted(true, filePath);
    } else {
        LOG_ERROR("通道 {} 从文件加载数据失败: {}", m_channelId, filePath.toStdString());
        emit fileLoadCompleted(false, filePath);
    }

    return success;
}

bool OffsetChannel::saveToFile(const QString& filePath) const {
    // 创建备份文件
    if (!createBackupFile(filePath)) {
        LOG_WARN("创建备份文件失败，但继续保存");
    }

    // 序列化数据为JSON
    QJsonObject rootObj = toJsonObject();
    QJsonDocument doc(rootObj);
    QByteArray data = doc.toJson(QJsonDocument::Indented);

    // 确保目录存在
    QFileInfo fileInfo(filePath);
    QDir dir = fileInfo.absoluteDir();
    if (!dir.exists()) {
        dir.mkpath(".");
    }

    // 写入文件
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        LOG_ERROR("无法打开文件进行写入: {}", filePath.toStdString());
        return false;
    }

    qint64 bytesWritten = file.write(data);
    file.close();

    if (bytesWritten != data.size()) {
        LOG_ERROR("文件写入不完整: {} / {} 字节", bytesWritten, data.size());
        return false;
    }

    return true;
}

bool OffsetChannel::loadFromCncSystem() {
    auto* cncManager = CncManager::getInstance();
    if (!cncManager || !cncManager->isInitialized()) {
        LOG_ERROR("CNC系统未初始化，无法加载数据");
        return false;
    }

    bool success = true;
    int loadedCount = 0;

    // 加载机床坐标
    PointXD machinePos;
    if (cncManager->getMachinePosition(m_channelId, machinePos) == ErrorCode::Success) {
        m_offsetData[OffsetType::MachineCoordinate].value = machinePos;
        emit offsetValueChanged(OffsetType::MachineCoordinate, machinePos);
        loadedCount++;
    } else {
        LOG_WARN("通道 {} 加载机床坐标失败", m_channelId);
        success = false;
    }

    // 加载当前工件坐标系偏移
    std::string currentWorkOffsetName;
    if (cncManager->getCurrentWorkOffsetName(m_channelId, currentWorkOffsetName) == ErrorCode::Success) {
        PointXD workOffsetValue;
        if (cncManager->getWorkOffsetValue(m_channelId, currentWorkOffsetName, workOffsetValue) == ErrorCode::Success) {
            m_offsetData[OffsetType::CurrentWorkOffset].value = workOffsetValue;
            emit offsetValueChanged(OffsetType::CurrentWorkOffset, workOffsetValue);
            loadedCount++;

            // 更新当前工件坐标系类型
            QString qWorkOffsetName = QString::fromStdString(currentWorkOffsetName);
            OffsetType workOffsetType = getWorkOffsetTypeByName(qWorkOffsetName);
            if (workOffsetType != m_currentWorkOffset) {
                m_currentWorkOffset = workOffsetType;
                emit currentWorkOffsetChanged(workOffsetType);
            }
        } else {
            std::string errorMsg;
            cncManager->getLastError(errorMsg);
            LOG_WARN("读取工件坐标系 {} 失败:{}", currentWorkOffsetName, errorMsg);
        }
    } else {
        std::string errorMsg;
        cncManager->getLastError(errorMsg);
        LOG_WARN("getCurrentWorkOffsetName {} 失败:{}", currentWorkOffsetName, errorMsg);
    }

    // 加载所有工件坐标系数据
    auto* offsetManager = OffsetDataManager::getInstance();
    const auto& workOffsetMapping = offsetManager->getWorkOffsetNameMapping();
    for (auto it = workOffsetMapping.begin(); it != workOffsetMapping.end(); ++it) {
        const QString& workOffsetName = it.key();
        OffsetType workOffsetType = it.value();

        if (m_offsetData.contains(workOffsetType)) {
            PointXD offsetValue;
            if (cncManager->getWorkOffsetValue(m_channelId, workOffsetName.toStdString(), offsetValue) ==
                ErrorCode::Success) {
                // CNC系统返回的是工件坐标系的有效值（基本值+精确值）
                // 在初始加载时，我们将CNC值作为基本值，精确值保持为0
                // 在后续刷新时，如果有精确值，需要从CNC值中减去精确值得到基本值

                PointXD currentPreciseValue = m_offsetData[workOffsetType].preciseValue;
                bool hasPreciseValue = !currentPreciseValue.coordinates.empty();

                if (hasPreciseValue) {
                    // 如果有精确值，从CNC总值中减去精确值得到基本值
                    PointXD baseValue = offsetValue;
                    for (const auto& [axisIndex, preciseVal] : currentPreciseValue.coordinates) {
                        auto it = baseValue.coordinates.find(axisIndex);
                        if (it != baseValue.coordinates.end()) {
                            it->second -= preciseVal;
                        } else {
                            baseValue.coordinates[axisIndex] = -preciseVal;
                        }

                        LOG_WARN("axis %d=%d", axisIndex, baseValue.coordinates[axisIndex]);
                    }
                    m_offsetData[workOffsetType].value = baseValue;
                    emit offsetValueChanged(workOffsetType, baseValue);
                } else {
                    // 首次加载或没有精确值，直接将CNC值作为基本值
                    m_offsetData[workOffsetType].value = offsetValue;
                    emit offsetValueChanged(workOffsetType, offsetValue);
                }

                loadedCount++;
            } else {
                std::string errorMsg;

                cncManager->getLastError(errorMsg);
                LOG_WARN("读取工件坐标系 {} 失败:{}", workOffsetName.toStdString(), errorMsg);
            }
        }
    }

    const SystemConfig& systemConfig = cncManager->getSystemConfig();
    // 加载当前刀具信息
    ToolInfo toolInfo;
    if (cncManager->getCurrentToolInfo(m_channelId, toolInfo) == ErrorCode::Success) {
        PointXD toolOffset;
        int axisIndex = 0;

// 从ToolInfo的几何参数构建偏移量到PointXD
// TODO: 半径和半径补偿，暂时不用、待王溪确认后再修改
#if 0
        if (toolInfo.geometryRadius != 0.0) {
            double radiusWithWear = toolInfo.geometryRadius + toolInfo.geometryRadiusWear;
            toolOffset.coordinates[0] = radiusWithWear;  // X轴半径补偿
            toolOffset.coordinates[1] = radiusWithWear;  // Y轴半径补偿
        }
#endif

        // 遍历轴
        for (size_t axisIndex = 0; axisIndex < systemConfig.axesConfigs.size(); ++axisIndex) {
            const auto& axisCfg = systemConfig.axesConfigs[axisIndex];
            if (axisCfg.name == "X") {
                toolOffset.coordinates[axisIndex] =
                    axisCfg.getToolZeroOffset(toolInfo.geometryLengthX, toolInfo.geometryLengthXWear);
                LOG_WARN("axisIndex {} X value {} XWear {}", axisIndex, toolInfo.geometryLengthX,
                         toolInfo.geometryLengthXWear);
            } else if (axisCfg.name == "Y") {
                toolOffset.coordinates[axisIndex] =
                    axisCfg.getToolZeroOffset(toolInfo.geometryLengthY, toolInfo.geometryLengthYWear);
                LOG_WARN("axisIndex {} Y value {} YWear {}", axisIndex, toolInfo.geometryLengthY,
                         toolInfo.geometryLengthYWear);
            } else if (axisCfg.name == "Z") {
                toolOffset.coordinates[axisIndex] =
                    axisCfg.getToolZeroOffset(toolInfo.geometryLengthZ, toolInfo.geometryLengthZWear);
                LOG_WARN("axisIndex {} Z value {} YWear {}", axisIndex, toolInfo.geometryLengthZ,
                         toolInfo.geometryLengthZWear);
            }

            LOG_WARN("axisIndex {} {} value {}", axisIndex, axisCfg.name, toolOffset.coordinates[axisIndex]);
        }

        m_offsetData[OffsetType::CurrentToolOffset].value = toolOffset;
        m_offsetData[OffsetType::CurrentToolOffset].displayName =
            tr("当前刀具: %1").arg(QString::fromStdString(toolInfo.name));
        emit offsetValueChanged(OffsetType::CurrentToolOffset, toolOffset);
        // emit offsetValueChanged(OffsetType::TOFF, m_offsetData[OffsetType::TOFF].value);
        loadedCount++;
    }

    if (success) {
        // 重新计算依赖关系
        recalculateAllDependencies();

        // 更新缓存时间
        m_lastCacheUpdate = QDateTime::currentDateTime();

        emit offsetValueChanged(OffsetType::TotalOffset, m_offsetData[OffsetType::TotalOffset].value);
        emit offsetValueChanged(OffsetType::WorkCoordinate, m_offsetData[OffsetType::WorkCoordinate].value);

        emit cncDataLoadCompleted(true);
    } else {
        LOG_ERROR("通道 {} 从CNC系统加载数据部分失败", m_channelId);
        emit cncDataLoadCompleted(false);
    }

    return success;
}

bool OffsetChannel::saveToCncSystem() {
    auto* cncManager = CncManager::getInstance();
    if (!cncManager || !cncManager->isInitialized()) {
        LOG_ERROR("CNC系统未初始化，无法保存数据");
        return false;
    }

    bool success = true;
    int savedCount = 0;

    // 保存所有工件坐标系数据
    auto* offsetManager = OffsetDataManager::getInstance();
    const auto& workOffsetMapping = offsetManager->getWorkOffsetNameMapping();
    for (auto it = workOffsetMapping.begin(); it != workOffsetMapping.end(); ++it) {
        const QString& workOffsetName = it.key();
        OffsetType workOffsetType = it.value();

        if (m_offsetData.contains(workOffsetType) && !m_offsetData[workOffsetType].isReadOnly) {
            PointXD offsetValue = m_offsetData[workOffsetType].getEffectiveValue();
            if (cncManager->setWorkOffsetValue(m_channelId, workOffsetName.toStdString(), offsetValue) ==
                ErrorCode::Success) {
                savedCount++;
            } else {
                std::string errorMsg;

                cncManager->getLastError(errorMsg);
                LOG_WARN("保存工件坐标系 {} 失败:{}", workOffsetName.toStdString(), errorMsg);
                success = false;
            }
        }
    }

    // 保存刀具参数（如果有变化）
    if (m_offsetData.contains(OffsetType::CurrentToolOffset) &&
        !m_offsetData[OffsetType::CurrentToolOffset].isReadOnly) {
        ToolInfo toolInfo;
        if (cncManager->getCurrentToolInfo(m_channelId, toolInfo) == ErrorCode::Success) {
            // 更新刀具几何参数
            const PointXD& toolOffset = m_offsetData[OffsetType::CurrentToolOffset].getEffectiveValue();
            for (const auto& [axisIndex, offsetValue] : toolOffset.coordinates) {
                switch (axisIndex) {
                    case 0:  // X轴
                        toolInfo.geometryLengthX = offsetValue;
                        break;
                    case 1:  // Y轴
                        toolInfo.geometryLengthY = offsetValue;
                        break;
                    case 2:  // Z轴
                        toolInfo.geometryLengthZ = offsetValue;
                        break;
                    default:
                        LOG_WARN("不支持的轴索引 {} 用于刀具偏移", axisIndex);
                        break;
                }
            }

            if (cncManager->setToolParameters(toolInfo) == ErrorCode::Success) {
                savedCount++;
            } else {
                LOG_WARN("保存刀具 {} 参数失败", toolInfo.number);
                success = false;
            }
        }
    }

    if (success) {
        emit cncDataSaveCompleted(true);
    } else {
        LOG_ERROR("通道 {} 保存数据到CNC系统部分失败", m_channelId);
        emit cncDataSaveCompleted(false);
    }

    return success;
}

QList<OffsetType> OffsetChannel::getDependentTypes(OffsetType type) const { return m_dependencies.value(type); }

bool OffsetChannel::hasCyclicDependency(OffsetType type) const {
    QSet<OffsetType> visited;
    QSet<OffsetType> recStack;

    std::function<bool(OffsetType)> hasCycleUtil = [&](OffsetType u) -> bool {
        visited.insert(u);
        recStack.insert(u);

        const QList<OffsetType> dependents = m_dependencies.value(u);
        for (OffsetType v : dependents) {
            if (!visited.contains(v)) {
                if (hasCycleUtil(v)) {
                    return true;
                }
            } else if (recStack.contains(v)) {
                return true;
            }
        }

        recStack.remove(u);
        return false;
    };

    return hasCycleUtil(type);
}

// State management methods
bool OffsetChannel::hasUnsavedChanges() const { return m_hasUnsavedChanges; }

void OffsetChannel::markAsSaved() {
    m_hasUnsavedChanges = false;
    m_hasUnsavedFileChanges = false;
}

QStringList OffsetChannel::getValidationErrors() const { return m_validationErrors; }

QStringList OffsetChannel::getValidationWarnings() const { return m_validationWarnings; }

// Data validation methods
ValidationResult OffsetChannel::validateOffsetValue(OffsetType type, const PointXD& value) const {
    // 检查零偏类型是否存在
    if (!m_offsetData.contains(type)) {
        return ValidationResult(false, "无效的零偏类型");
    }

    const OffsetDataItem& item = m_offsetData[type];

    // 检查是否为只读
    if (item.isReadOnly) {
        return ValidationResult(false, "该零偏类型为只读，不能修改");
    }

    // 检查坐标值范围（根据配置）
    for (const auto& [axisIndex, axisValue] : value.coordinates) {
        if (axisValue > m_config.maxValue) {
            return ValidationResult(
                false, QString("轴 %1 的偏移值 %2 超出最大值 %3").arg(axisIndex).arg(axisValue).arg(m_config.maxValue));
        }

        if (axisValue < m_config.minValue) {
            return ValidationResult(
                false, QString("轴 %1 的偏移值 %2 低于最小值 %3").arg(axisIndex).arg(axisValue).arg(m_config.minValue));
        }
    }

    return ValidationResult(true);
}

ValidationResult OffsetChannel::validateAllData() const {
    QStringList errors;
    QStringList warnings;

    // 验证所有零偏数据
    for (auto it = m_offsetData.begin(); it != m_offsetData.end(); ++it) {
        const OffsetType type = it.key();
        const OffsetDataItem& item = it.value();

        // 验证主值
        ValidationResult result = validateOffsetValue(type, item.value);
        if (!result.isValid) {
            errors.append(QString("零偏 %1: %2").arg(item.displayName).arg(result.errorMessage));
        }

        // 验证精确值
        if (!item.preciseValue.coordinates.empty()) {
            ValidationResult preciseResult = validateOffsetValue(type, item.preciseValue);
            if (!preciseResult.isValid) {
                errors.append(QString("零偏 %1 精确值: %2").arg(item.displayName).arg(preciseResult.errorMessage));
            }
        }

        // 检查是否有循环依赖
        if (hasCyclicDependency(type)) {
            errors.append(QString("零偏 %1 存在循环依赖").arg(item.displayName));
        }
    }

    // 检查当前工件坐标系是否有效
    if (!m_offsetData.contains(m_currentWorkOffset)) {
        errors.append("当前工件坐标系无效");
    }

    if (!errors.isEmpty()) {
        return ValidationResult(false, errors.join("; "));
    }

    return ValidationResult(true);
}

// Batch operations
bool OffsetChannel::batchUpdateOffsets(const QMap<OffsetType, PointXD>& updates) {
    bool allSuccess = true;
    QStringList errors;

    // 先验证所有更新
    for (auto it = updates.begin(); it != updates.end(); ++it) {
        ValidationResult result = validateOffsetValue(it.key(), it.value());
        if (!result.isValid) {
            errors.append(result.errorMessage);
            allSuccess = false;
        }
    }

    if (!allSuccess) {
        LOG_ERROR("批量更新验证失败: {}", errors.join("; ").toStdString());
        return false;
    }

    // 执行批量更新
    for (auto it = updates.begin(); it != updates.end(); ++it) {
        m_offsetData[it.key()].value = it.value();
        emit offsetValueChanged(it.key(), it.value());
    }

    // 重新计算所有依赖关系
    recalculateAllDependencies();

    m_hasUnsavedChanges = true;
    m_hasUnsavedFileChanges = true;

    return true;
}

bool OffsetChannel::batchUpdatePreciseValues(const QMap<OffsetType, PointXD>& preciseUpdates) {
    bool allSuccess = true;
    QStringList errors;

    // 先验证所有更新
    for (auto it = preciseUpdates.begin(); it != preciseUpdates.end(); ++it) {
        if (!m_offsetData.contains(it.key())) {
            errors.append(QString("无效的零偏类型: %1").arg(static_cast<int>(it.key())));
            allSuccess = false;
        }
    }

    if (!allSuccess) {
        LOG_ERROR("批量更新精确值验证失败: {}", errors.join("; ").toStdString());
        return false;
    }

    // 执行批量更新
    for (auto it = preciseUpdates.begin(); it != preciseUpdates.end(); ++it) {
        m_offsetData[it.key()].preciseValue = it.value();
        emit preciseValueChanged(it.key(), it.value());
    }

    // 重新计算所有依赖关系
    recalculateAllDependencies();

    m_hasUnsavedChanges = true;
    m_hasUnsavedFileChanges = true;

    return true;
}

bool OffsetChannel::resetAllOffsets() {
    for (auto it = m_offsetData.begin(); it != m_offsetData.end(); ++it) {
        if (!it.value().isReadOnly) {
            it.value().value.coordinates.clear();
            emit offsetValueChanged(it.key(), it.value().value);
        }
    }

    recalculateAllDependencies();
    m_hasUnsavedChanges = true;
    m_hasUnsavedFileChanges = true;

    return true;
}

bool OffsetChannel::resetAllPreciseValues() {
    for (auto it = m_offsetData.begin(); it != m_offsetData.end(); ++it) {
        if (!it.value().isReadOnly) {
            it.value().preciseValue.coordinates.clear();
            emit preciseValueChanged(it.key(), it.value().preciseValue);
        }
    }

    recalculateAllDependencies();
    m_hasUnsavedChanges = true;
    m_hasUnsavedFileChanges = true;

    return true;
}

bool OffsetChannel::resetOffsetGroup(ZeroOffsetDisplayMode mode) {
    // 根据显示模式确定要重置的零偏类型
    QList<OffsetType> typesToReset;

    switch (mode) {
        case ZeroOffsetDisplayMode::None:
            // 无操作
            break;
        case ZeroOffsetDisplayMode::Basic:
            typesToReset = {OffsetType::BasicReference, OffsetType::ChannelBasicOffset};
            break;
        case ZeroOffsetDisplayMode::WorkCoordinate:
            // 重置所有工件坐标系
            for (auto it = m_offsetData.begin(); it != m_offsetData.end(); ++it) {
                if (static_cast<int>(it.key()) >= static_cast<int>(OffsetType::DynamicWorkOffsetStart)) {
                    typesToReset.append(it.key());
                }
            }
            break;
        case ZeroOffsetDisplayMode::Valid:
        case ZeroOffsetDisplayMode::Overview:
            // 重置所有可编辑的零偏
            for (auto it = m_offsetData.begin(); it != m_offsetData.end(); ++it) {
                if (!it.value().isReadOnly) {
                    typesToReset.append(it.key());
                }
            }
            break;
    }

    // 执行重置
    for (OffsetType type : typesToReset) {
        if (m_offsetData.contains(type) && !m_offsetData[type].isReadOnly) {
            m_offsetData[type].value.coordinates.clear();
            m_offsetData[type].preciseValue.coordinates.clear();
            emit offsetValueChanged(type, m_offsetData[type].value);
            emit preciseValueChanged(type, m_offsetData[type].preciseValue);
        }
    }

    recalculateAllDependencies();
    m_hasUnsavedChanges = true;
    m_hasUnsavedFileChanges = true;

    return true;
}

// JSON serialization
QJsonObject OffsetChannel::toJsonObject() const {
    QJsonObject root;
    root["channelId"] = m_channelId;
    root["currentWorkOffset"] = static_cast<int>(m_currentWorkOffset);
    root["hasUnsavedChanges"] = m_hasUnsavedChanges;

    // 序列化所有零偏数据
    QJsonArray offsetArray;
    for (auto it = m_offsetData.begin(); it != m_offsetData.end(); ++it) {
        QJsonObject offsetObj;
        offsetObj["type"] = static_cast<int>(it.key());
        offsetObj["displayName"] = it.value().displayName;
        offsetObj["isReadOnly"] = it.value().isReadOnly;

        // 序列化主值坐标
        QJsonObject valueObj;
        for (const auto& [axisIndex, value] : it.value().value.coordinates) {
            valueObj[QString::number(axisIndex)] = value;
        }
        offsetObj["value"] = valueObj;

        // 序列化精确值坐标
        QJsonObject preciseObj;
        for (const auto& [axisIndex, value] : it.value().preciseValue.coordinates) {
            preciseObj[QString::number(axisIndex)] = value;
        }
        offsetObj["preciseValue"] = preciseObj;

        offsetArray.append(offsetObj);
    }
    root["offsetData"] = offsetArray;

    // 序列化验证信息
    root["validationErrors"] = QJsonArray::fromStringList(m_validationErrors);
    root["validationWarnings"] = QJsonArray::fromStringList(m_validationWarnings);

    return root;
}

bool OffsetChannel::fromJsonObject(const QJsonObject& obj) {
    if (!obj.contains("channelId") || obj["channelId"].toInt() != m_channelId) {
        LOG_ERROR("JSON数据中的通道ID不匹配");
        return false;
    }

    try {
        // 恢复当前工件坐标系
        if (obj.contains("currentWorkOffset")) {
            m_currentWorkOffset = static_cast<OffsetType>(obj["currentWorkOffset"].toInt());
        }

        // 恢复未保存更改状态
        if (obj.contains("hasUnsavedChanges")) {
            m_hasUnsavedChanges = obj["hasUnsavedChanges"].toBool();
        }

        // 恢复零偏数据
        if (obj.contains("offsetData")) {
            const QJsonArray offsetArray = obj["offsetData"].toArray();
            for (const QJsonValue& value : offsetArray) {
                const QJsonObject offsetObj = value.toObject();

                OffsetType type = static_cast<OffsetType>(offsetObj["type"].toInt());
                if (!m_offsetData.contains(type)) {
                    continue;  // 跳过不存在的零偏类型
                }

                // 恢复主值坐标
                if (offsetObj.contains("value")) {
                    const QJsonObject valueObj = offsetObj["value"].toObject();
                    PointXD point;
                    for (auto it = valueObj.begin(); it != valueObj.end(); ++it) {
                        int axisIndex = it.key().toInt();
                        double axisValue = it.value().toDouble();
                        point.coordinates[axisIndex] = axisValue;
                    }
                    m_offsetData[type].value = point;
                }

                // 恢复精确值坐标
                if (offsetObj.contains("preciseValue")) {
                    const QJsonObject preciseObj = offsetObj["preciseValue"].toObject();
                    PointXD point;
                    for (auto it = preciseObj.begin(); it != preciseObj.end(); ++it) {
                        int axisIndex = it.key().toInt();
                        double axisValue = it.value().toDouble();
                        point.coordinates[axisIndex] = axisValue;
                    }
                    m_offsetData[type].preciseValue = point;
                }
            }
        }

        // 恢复验证信息
        if (obj.contains("validationErrors")) {
            const QJsonArray errorsArray = obj["validationErrors"].toArray();
            m_validationErrors.clear();
            for (const QJsonValue& value : errorsArray) {
                m_validationErrors.append(value.toString());
            }
        }

        if (obj.contains("validationWarnings")) {
            const QJsonArray warningsArray = obj["validationWarnings"].toArray();
            m_validationWarnings.clear();
            for (const QJsonValue& value : warningsArray) {
                m_validationWarnings.append(value.toString());
            }
        }

        // 重新计算依赖关系
        recalculateAllDependencies();

        LOG_INFO("通道 {} 从JSON数据恢复成功", m_channelId);
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR("通道 {} 从JSON恢复时发生异常: {}", m_channelId, e.what());
        return false;
    }
}

// CNC system comparison
QMap<OffsetType, PointXD> OffsetChannel::compareWithCncSystem() const {
    QMap<OffsetType, PointXD> differences;

    auto* cncManager = CncManager::getInstance();
    if (!cncManager || !cncManager->isInitialized()) {
        LOG_ERROR("CNC系统未初始化，无法进行比较");
        return differences;
    }

    // 比较机床坐标
    PointXD cncMachinePos;
    if (cncManager->getMachinePosition(m_channelId, cncMachinePos) == ErrorCode::Success) {
        if (m_offsetData.contains(OffsetType::MachineCoordinate)) {
            const PointXD& localMachinePos = m_offsetData[OffsetType::MachineCoordinate].getEffectiveValue();
            PointXD diff = calculateDifference(localMachinePos, cncMachinePos);
            if (!isZeroPoint(diff)) {
                differences[OffsetType::MachineCoordinate] = diff;
            }
        }
    }

    // 比较工件坐标
    PointXD cncWorkPos;
    if (cncManager->getWorkPosition(m_channelId, cncWorkPos) == ErrorCode::Success) {
        if (m_offsetData.contains(OffsetType::WorkCoordinate)) {
            const PointXD& localWorkPos = m_offsetData[OffsetType::WorkCoordinate].getEffectiveValue();
            PointXD diff = calculateDifference(localWorkPos, cncWorkPos);
            if (!isZeroPoint(diff)) {
                differences[OffsetType::WorkCoordinate] = diff;
            }
        }
    }

    // 比较所有工件坐标系数据
    auto* offsetManager = OffsetDataManager::getInstance();
    const auto& workOffsetMapping = offsetManager->getWorkOffsetNameMapping();
    for (auto it = workOffsetMapping.begin(); it != workOffsetMapping.end(); ++it) {
        const QString& workOffsetName = it.key();
        OffsetType workOffsetType = it.value();

        if (m_offsetData.contains(workOffsetType)) {
            PointXD cncOffsetValue;
            if (cncManager->getWorkOffsetValue(m_channelId, workOffsetName.toStdString(), cncOffsetValue) ==
                ErrorCode::Success) {
                const PointXD& localOffsetValue = m_offsetData[workOffsetType].getEffectiveValue();
                PointXD diff = calculateDifference(localOffsetValue, cncOffsetValue);
                if (!isZeroPoint(diff)) {
                    differences[workOffsetType] = diff;
                }
            }
        }
    }

    // 比较当前刀具偏移
    ToolInfo cncToolInfo;
    if (cncManager->getCurrentToolInfo(m_channelId, cncToolInfo) == ErrorCode::Success) {
        if (m_offsetData.contains(OffsetType::CurrentToolOffset)) {
            PointXD cncToolOffset;
            // 从ToolInfo的几何参数构建偏移量
            if (cncToolInfo.geometryLengthZ != 0.0) {
                cncToolOffset.coordinates[2] = cncToolInfo.geometryLengthZ + cncToolInfo.geometryLengthZWear;
            }
            if (cncToolInfo.geometryRadius != 0.0) {
                double radiusWithWear = cncToolInfo.geometryRadius + cncToolInfo.geometryRadiusWear;
                cncToolOffset.coordinates[0] = radiusWithWear;
                cncToolOffset.coordinates[1] = radiusWithWear;
            }
            if (cncToolInfo.geometryLengthX != 0.0) {
                cncToolOffset.coordinates[0] = cncToolInfo.geometryLengthX + cncToolInfo.geometryLengthXWear;
            }
            if (cncToolInfo.geometryLengthY != 0.0) {
                cncToolOffset.coordinates[1] = cncToolInfo.geometryLengthY + cncToolInfo.geometryLengthYWear;
            }

            const PointXD& localToolOffset = m_offsetData[OffsetType::CurrentToolOffset].getEffectiveValue();
            PointXD diff = calculateDifference(localToolOffset, cncToolOffset);
            if (!isZeroPoint(diff)) {
                differences[OffsetType::CurrentToolOffset] = diff;
            }
        }
    }

    return differences;
}

PointXD OffsetChannel::calculateDifference(const PointXD& local, const PointXD& cnc) const {
    PointXD diff;

    // 获取所有轴的索引
    QSet<int> allAxes;
    for (const auto& [axis, value] : local.coordinates) {
        allAxes.insert(axis);
    }
    for (const auto& [axis, value] : cnc.coordinates) {
        allAxes.insert(axis);
    }

    // 计算每个轴的差异
    for (int axis : allAxes) {
        auto localIt = local.coordinates.find(axis);
        auto cncIt = cnc.coordinates.find(axis);

        double localValue = (localIt != local.coordinates.end()) ? localIt->second : 0.0;
        double cncValue = (cncIt != cnc.coordinates.end()) ? cncIt->second : 0.0;

        double difference = localValue - cncValue;
        if (std::abs(difference) > 1e-6) {  // 忽略微小差异
            diff.coordinates[axis] = difference;
        }
    }

    return diff;
}

bool OffsetChannel::isZeroPoint(const PointXD& point) const {
    for (const auto& [axis, value] : point.coordinates) {
        if (std::abs(value) > 1e-6) {
            return false;
        }
    }
    return true;
}

double OffsetChannel::calculateTotalDeviation(OffsetType type1, OffsetType type2) const {
    if (!m_offsetData.contains(type1) || !m_offsetData.contains(type2)) {
        return 0.0;
    }

    const PointXD& point1 = m_offsetData[type1].getEffectiveValue();
    const PointXD& point2 = m_offsetData[type2].getEffectiveValue();

    double totalDeviation = 0.0;

    // 计算所有轴的偏差平方和
    QSet<int> allAxes;
    for (const auto& [axis, value] : point1.coordinates) {
        allAxes.insert(axis);
    }
    for (const auto& [axis, value] : point2.coordinates) {
        allAxes.insert(axis);
    }

    for (int axis : allAxes) {
        auto it1 = point1.coordinates.find(axis);
        auto it2 = point2.coordinates.find(axis);
        double value1 = (it1 != point1.coordinates.end()) ? it1->second : 0.0;
        double value2 = (it2 != point2.coordinates.end()) ? it2->second : 0.0;
        double deviation = value1 - value2;
        totalDeviation += deviation * deviation;
    }

    return std::sqrt(totalDeviation);
}

// Tool offset management
bool OffsetChannel::updateCurrentToolOffset() {
    auto* cncManager = CncManager::getInstance();
    if (!cncManager || !cncManager->isInitialized()) {
        LOG_ERROR("CNC系统未初始化，无法更新刀具偏移");
        return false;
    }

    ToolInfo toolInfo;
    if (cncManager->getCurrentToolInfo(m_channelId, toolInfo) != ErrorCode::Success) {
        LOG_ERROR("获取当前刀具信息失败");
        return false;
    }

    // 将刀具偏移转换为PointXD
    PointXD toolOffset;
    // 从ToolInfo的几何参数构建偏移量
    if (toolInfo.geometryLengthZ != 0.0) {
        toolOffset.coordinates[2] = toolInfo.geometryLengthZ + toolInfo.geometryLengthZWear;
    }
    if (toolInfo.geometryRadius != 0.0) {
        double radiusWithWear = toolInfo.geometryRadius + toolInfo.geometryRadiusWear;
        toolOffset.coordinates[0] = radiusWithWear;
        toolOffset.coordinates[1] = radiusWithWear;
    }
    if (toolInfo.geometryLengthX != 0.0) {
        toolOffset.coordinates[0] = toolInfo.geometryLengthX + toolInfo.geometryLengthXWear;
    }
    if (toolInfo.geometryLengthY != 0.0) {
        toolOffset.coordinates[1] = toolInfo.geometryLengthY + toolInfo.geometryLengthYWear;
    }

    // 更新本地数据
    if (m_offsetData.contains(OffsetType::CurrentToolOffset)) {
        m_offsetData[OffsetType::CurrentToolOffset].value = toolOffset;
        m_hasUnsavedChanges = true;
        m_hasUnsavedFileChanges = true;

        emit offsetValueChanged(OffsetType::CurrentToolOffset, toolOffset);

        // 重新计算依赖关系
        recalculateDependencies(OffsetType::CurrentToolOffset);
        return true;
    } else {
        LOG_ERROR("当前刀具偏移数据项不存在");
        return false;
    }
}

bool OffsetChannel::loadCurrentToolOffsetFromCnc(int toolNumber) {
    auto* cncManager = CncManager::getInstance();
    if (!cncManager || !cncManager->isInitialized()) {
        LOG_ERROR("CNC系统未初始化，无法加载刀具偏移");
        return false;
    }

    ToolInfo toolInfo;
    ErrorCode result;

    if (toolNumber == -1) {
        // 加载当前刀具
        result = cncManager->getCurrentToolInfo(m_channelId, toolInfo);
    } else {
        // 加载指定刀具 - 使用getAllToolParameters并按刀号筛选
        std::map<std::string, ToolInfo> allTools;
        result = cncManager->getAllToolParameters(allTools);
        if (result == ErrorCode::Success) {
            bool foundTool = false;
            for (const auto& [uuid, tool] : allTools) {
                if (tool.number == toolNumber && tool.isValid && tool.dNumber == 1) {
                    toolInfo = tool;
                    foundTool = true;
                    break;
                }
            }
            if (!foundTool) {
                result = ErrorCode::OperationFailed;  // 没有找到该刀号的刀具
            }
        }
    }

    if (result != ErrorCode::Success) {
        LOG_ERROR("从CNC加载刀具 {} 信息失败", toolNumber);
        return false;
    }

    // 将刀具偏移转换为PointXD
    PointXD toolOffset;
    // 从ToolInfo的几何参数构建偏移量
    if (toolInfo.geometryLengthZ != 0.0) {
        toolOffset.coordinates[2] = toolInfo.geometryLengthZ + toolInfo.geometryLengthZWear;
    }
    if (toolInfo.geometryRadius != 0.0) {
        double radiusWithWear = toolInfo.geometryRadius + toolInfo.geometryRadiusWear;
        toolOffset.coordinates[0] = radiusWithWear;
        toolOffset.coordinates[1] = radiusWithWear;
    }
    if (toolInfo.geometryLengthX != 0.0) {
        toolOffset.coordinates[0] = toolInfo.geometryLengthX + toolInfo.geometryLengthXWear;
    }
    if (toolInfo.geometryLengthY != 0.0) {
        toolOffset.coordinates[1] = toolInfo.geometryLengthY + toolInfo.geometryLengthYWear;
    }

    // 更新本地数据
    if (m_offsetData.contains(OffsetType::CurrentToolOffset)) {
        m_offsetData[OffsetType::CurrentToolOffset].value = toolOffset;
        m_hasUnsavedChanges = true;
        m_hasUnsavedFileChanges = true;

        emit offsetValueChanged(OffsetType::CurrentToolOffset, toolOffset);

        // 重新计算依赖关系
        recalculateDependencies(OffsetType::CurrentToolOffset);

        return true;
    } else {
        LOG_ERROR("当前刀具偏移数据项不存在");
        return false;
    }
}

bool OffsetChannel::saveSpecificOffset(OffsetType type) {
    if (!m_offsetData.contains(type)) {
        LOG_ERROR("尝试保存不存在的零偏类型: {}", static_cast<int>(type));
        return false;
    }

    // 调用现有的保存方法
    bool success = saveOffsetToCnc(type);

    if (!success) {
        LOG_ERROR("保存通道 {} 的零偏类型 {} 到CNC系统失败", m_channelId, static_cast<int>(type));
    }

    return success;
}

// 分组管理方法
QList<OffsetDataItem> OffsetChannel::getItemsByGroup(int groupId) const {
    QList<OffsetDataItem> items;

    for (auto it = m_offsetData.begin(); it != m_offsetData.end(); ++it) {
        if (OffsetUtils::getGroupIdForOffsetType(it.key()) == groupId) {
            items.append(it.value());
        }
    }

    return items;
}

QMap<int, QList<OffsetDataItem>> OffsetChannel::getItemsGrouped() const {
    QMap<int, QList<OffsetDataItem>> groupedItems;

    for (auto it = m_offsetData.begin(); it != m_offsetData.end(); ++it) {
        int groupId = OffsetUtils::getGroupIdForOffsetType(it.key());
        groupedItems[groupId].append(it.value());
    }

    return groupedItems;
}

QList<int> OffsetChannel::getAvailableGroupIds() const {
    QSet<int> groupIds;

    for (auto it = m_offsetData.begin(); it != m_offsetData.end(); ++it) {
        groupIds.insert(OffsetUtils::getGroupIdForOffsetType(it.key()));
    }

    return groupIds.values();
}

QString OffsetChannel::getGroupName(int groupId) const { return OffsetUtils::getGroupName(groupId); }

// 工件坐标系名称支持
QString OffsetChannel::getCurrentWorkOffsetName() const { return getWorkOffsetNameByType(m_currentWorkOffset); }

void OffsetChannel::setWorkOffsetAccessors(std::function<QString(OffsetType)> getNameFunc,
                                           std::function<OffsetType(const QString&)> getTypeFunc) {
    m_getWorkOffsetName = getNameFunc;
    m_getWorkOffsetType = getTypeFunc;
}

QString OffsetChannel::getWorkOffsetNameByType(OffsetType type) const {
    if (m_getWorkOffsetName) {
        return m_getWorkOffsetName(type);
    }

    // 如果没有设置访问器，返回默认名称
    if (type >= OffsetType::DynamicWorkOffsetStart) {
        int index = static_cast<int>(type) - static_cast<int>(OffsetType::DynamicWorkOffsetStart);
        return QString("G%1").arg(54 + index);
    }

    return QString("Unknown_%1").arg(static_cast<int>(type));
}

OffsetType OffsetChannel::getWorkOffsetTypeByName(const QString& name) const {
    if (m_getWorkOffsetType) {
        return m_getWorkOffsetType(name);
    }

    // 如果没有设置访问器，尝试解析G代码格式
    if (name.startsWith("G") && name.length() > 1) {
        bool ok;
        int gCode = name.mid(1).toInt(&ok);
        if (ok && gCode >= 54 && gCode <= 59) {
            int index = gCode - 54;
            return static_cast<OffsetType>(static_cast<int>(OffsetType::DynamicWorkOffsetStart) + index);
        }
    }

    return OffsetType::DynamicWorkOffsetStart;  // 默认值
}

// 自动同步和保存控制
void OffsetChannel::startAutoSync() {
    if (m_config.autoSync && m_config.syncInterval > 0) {
        m_syncTimer->start(m_config.syncInterval);
    }
}

void OffsetChannel::stopAutoSync() { m_syncTimer->stop(); }

void OffsetChannel::startAutoFileSave() {
    if (m_config.autoSaveToFile && m_config.autoSaveInterval > 0) {
        m_autoSaveTimer->start(m_config.autoSaveInterval);
    }
}

void OffsetChannel::stopAutoFileSave() { m_autoSaveTimer->stop(); }

// 配置管理
void OffsetChannel::updateConfiguration(const OffsetManagerConfig& config) {
    bool syncIntervalChanged = (m_config.syncInterval != config.syncInterval);
    bool fileSaveIntervalChanged = (m_config.autoSaveInterval != config.autoSaveInterval);

    m_config = config;

    // 如果同步间隔改变了，重新启动定时器
    if (syncIntervalChanged && m_syncTimer->isActive()) {
        stopAutoSync();
        startAutoSync();
    }

    // 如果文件保存间隔改变了，重新启动定时器
    if (fileSaveIntervalChanged && m_autoSaveTimer->isActive()) {
        stopAutoFileSave();
        startAutoFileSave();
    }
}

OffsetManagerConfig OffsetChannel::getConfiguration() const { return m_config; }

// 验证辅助方法
bool OffsetChannel::isValidOffsetType(OffsetType type) const { return m_offsetData.contains(type); }

bool OffsetChannel::isReadOnlyOffsetType(OffsetType type) const {
    if (!m_offsetData.contains(type)) {
        return true;  // 不存在的类型视为只读
    }
    return m_offsetData[type].isReadOnly;
}

bool OffsetChannel::isWithinValidRange(OffsetType type, const PointXD& value) const {
    // 检查每个轴的值是否在有效范围内
    for (const auto& [axisIndex, axisValue] : value.coordinates) {
        if (axisValue < m_config.minValue || axisValue > m_config.maxValue) {
            return false;
        }
    }
    return true;
}

// CNC系统接口辅助方法
bool OffsetChannel::loadOffsetFromCnc(OffsetType type) {
    auto* cncManager = CncManager::getInstance();
    if (!cncManager || !cncManager->isInitialized()) {
        LOG_ERROR("CNC系统未初始化，无法加载零偏");
        return false;
    }

    bool success = false;

    switch (type) {
        case OffsetType::MachineCoordinate: {
            PointXD machinePos;
            if (cncManager->getMachinePosition(m_channelId, machinePos) == ErrorCode::Success) {
                m_offsetData[type].value = machinePos;
                success = true;
            }
            break;
        }
        case OffsetType::WorkCoordinate: {
            PointXD workPos;
            if (cncManager->getWorkPosition(m_channelId, workPos) == ErrorCode::Success) {
                m_offsetData[type].value = workPos;
                success = true;
            }
            break;
        }
        case OffsetType::CurrentToolOffset: {
            ToolInfo toolInfo;
            if (cncManager->getCurrentToolInfo(m_channelId, toolInfo) == ErrorCode::Success) {
                PointXD toolOffset;
                // 从ToolInfo的几何参数构建偏移量
                if (toolInfo.geometryLengthZ != 0.0) {
                    toolOffset.coordinates[2] = toolInfo.geometryLengthZ + toolInfo.geometryLengthZWear;
                }
                if (toolInfo.geometryRadius != 0.0) {
                    double radiusWithWear = toolInfo.geometryRadius + toolInfo.geometryRadiusWear;
                    toolOffset.coordinates[0] = radiusWithWear;
                    toolOffset.coordinates[1] = radiusWithWear;
                }
                if (toolInfo.geometryLengthX != 0.0) {
                    toolOffset.coordinates[0] = toolInfo.geometryLengthX + toolInfo.geometryLengthXWear;
                }
                if (toolInfo.geometryLengthY != 0.0) {
                    toolOffset.coordinates[1] = toolInfo.geometryLengthY + toolInfo.geometryLengthYWear;
                }
                m_offsetData[type].value = toolOffset;
                success = true;
            }
            break;
        }
        default: {
            // 检查是否是动态工件坐标系
            if (type >= OffsetType::DynamicWorkOffsetStart) {
                QString workOffsetName = getWorkOffsetNameByType(type);
                PointXD offsetValue;
                if (cncManager->getWorkOffsetValue(m_channelId, workOffsetName.toStdString(), offsetValue) ==
                    ErrorCode::Success) {
                    m_offsetData[type].value = offsetValue;
                    success = true;
                }
            }
            break;
        }
    }

    if (success) {
        emit offsetValueChanged(type, m_offsetData[type].value);
        recalculateDependencies(type);
    } else {
        LOG_WARN("从CNC系统加载通道 {} 的零偏类型 {} 失败", m_channelId, static_cast<int>(type));
    }

    return success;
}

bool OffsetChannel::saveOffsetToCnc(OffsetType type) {
    auto* cncManager = CncManager::getInstance();
    if (!cncManager || !cncManager->isInitialized()) {
        LOG_ERROR("CNC系统未初始化，无法保存零偏");
        return false;
    }

    if (!m_offsetData.contains(type)) {
        LOG_ERROR("零偏类型 {} 不存在", static_cast<int>(type));
        return false;
    }

    if (m_offsetData[type].isReadOnly) {
        LOG_WARN("零偏类型 {} 为只读，不能保存到CNC系统", static_cast<int>(type));
        return false;
    }

    bool success = false;
    PointXD effectiveValue = m_offsetData[type].getEffectiveValue();

    switch (type) {
        case OffsetType::CurrentToolOffset: {
            ToolInfo toolInfo;
            if (cncManager->getCurrentToolInfo(m_channelId, toolInfo) == ErrorCode::Success) {
                // 更新刀具几何参数
                for (const auto& [axisIndex, offsetValue] : effectiveValue.coordinates) {
                    switch (axisIndex) {
                        case 0:  // X轴
                            toolInfo.geometryLengthX = offsetValue;
                            break;
                        case 1:  // Y轴
                            toolInfo.geometryLengthY = offsetValue;
                            break;
                        case 2:  // Z轴
                            toolInfo.geometryLengthZ = offsetValue;
                            break;
                        default:
                            LOG_WARN("不支持的轴索引 {} 用于刀具偏移", axisIndex);
                            break;
                    }
                }

                if (cncManager->setToolParameters(toolInfo) == ErrorCode::Success) {
                    success = true;
                }
            }
            break;
        }
        default: {
            // 检查是否是动态工件坐标系
            if (type >= OffsetType::DynamicWorkOffsetStart) {
                QString workOffsetName = getWorkOffsetNameByType(type);
                if (cncManager->setWorkOffsetValue(m_channelId, workOffsetName.toStdString(), effectiveValue) ==
                    ErrorCode::Success) {
                    success = true;
                }
            } else {
                LOG_WARN("零偏类型 {} 不支持保存到CNC系统", static_cast<int>(type));
            }
            break;
        }
    }

    if (success) {
    } else {
        LOG_ERROR("保存通道 {} 的零偏类型 {} 到CNC系统失败", m_channelId, static_cast<int>(type));
    }

    return success;
}

void OffsetChannel::updateCncSystemCache() {
    auto* cncManager = CncManager::getInstance();
    if (!cncManager || !cncManager->isInitialized()) {
        LOG_ERROR("CNC系统未初始化，无法更新缓存");
        return;
    }

    m_cncSystemCache.clear();

    // 缓存机床坐标
    PointXD machinePos;
    if (cncManager->getMachinePosition(m_channelId, machinePos) == ErrorCode::Success) {
        m_cncSystemCache[OffsetType::MachineCoordinate] = machinePos;
    }

    // 缓存工件坐标
    PointXD workPos;
    if (cncManager->getWorkPosition(m_channelId, workPos) == ErrorCode::Success) {
        m_cncSystemCache[OffsetType::WorkCoordinate] = workPos;
    }

    // 缓存所有工件坐标系数据
    auto* offsetManager = OffsetDataManager::getInstance();
    const auto& workOffsetMapping = offsetManager->getWorkOffsetNameMapping();
    for (auto it = workOffsetMapping.begin(); it != workOffsetMapping.end(); ++it) {
        const QString& workOffsetName = it.key();
        OffsetType workOffsetType = it.value();

        PointXD offsetValue;
        if (cncManager->getWorkOffsetValue(m_channelId, workOffsetName.toStdString(), offsetValue) ==
            ErrorCode::Success) {
            m_cncSystemCache[workOffsetType] = offsetValue;
        }
    }

    // 缓存当前刀具偏移
    ToolInfo toolInfo;
    if (cncManager->getCurrentToolInfo(m_channelId, toolInfo) == ErrorCode::Success) {
        PointXD toolOffset;
        // 从ToolInfo的几何参数构建偏移量
        if (toolInfo.geometryLengthZ != 0.0) {
            toolOffset.coordinates[2] = toolInfo.geometryLengthZ + toolInfo.geometryLengthZWear;
        }
        if (toolInfo.geometryRadius != 0.0) {
            double radiusWithWear = toolInfo.geometryRadius + toolInfo.geometryRadiusWear;
            toolOffset.coordinates[0] = radiusWithWear;
            toolOffset.coordinates[1] = radiusWithWear;
        }
        if (toolInfo.geometryLengthX != 0.0) {
            toolOffset.coordinates[0] = toolInfo.geometryLengthX + toolInfo.geometryLengthXWear;
        }
        if (toolInfo.geometryLengthY != 0.0) {
            toolOffset.coordinates[1] = toolInfo.geometryLengthY + toolInfo.geometryLengthYWear;
        }
        m_cncSystemCache[OffsetType::CurrentToolOffset] = toolOffset;
    }

    m_lastCacheUpdate = QDateTime::currentDateTime();
}

bool OffsetChannel::isCacheValid() const {
    if (m_lastCacheUpdate.isNull()) {
        return false;
    }

    // 缓存有效期为5分钟
    const int cacheValiditySeconds = 300;
    return m_lastCacheUpdate.secsTo(QDateTime::currentDateTime()) < cacheValiditySeconds;
}

// 文件操作辅助方法
QString OffsetChannel::getAutoSaveFilePath() const {
    // 使用配置中的自动保存目录
    QString baseDir = m_config.autoSaveDir;
    if (baseDir.isEmpty()) {
        baseDir = "offset_data";
    }

    // 确保目录存在
    QDir dir;
    if (!dir.exists(baseDir)) {
        dir.mkpath(baseDir);
    }

    // 生成文件名：offset_channel_<channelId>.json
    QString fileName = QString("offset_channel_%1.json").arg(m_channelId);
    QString fullPath = QDir(baseDir).absoluteFilePath(fileName);

    return fullPath;
}

bool OffsetChannel::createBackupFile(const QString& originalPath) const {
    if (!m_config.keepBackups || !QFile::exists(originalPath)) {
        return true;  // 如果不需要备份或原文件不存在，直接返回成功
    }

    QFileInfo fileInfo(originalPath);
    QString baseName = fileInfo.completeBaseName();
    QString suffix = fileInfo.suffix();
    QString dirPath = fileInfo.absolutePath();

    // 生成备份文件名：原文件名_YYYYMMDD_HHMMSS.后缀
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    QString backupFileName = QString("%1_%2.%3").arg(baseName, timestamp, suffix);
    QString backupPath = QDir(dirPath).absoluteFilePath(backupFileName);

    // 复制文件
    if (!QFile::copy(originalPath, backupPath)) {
        LOG_ERROR("创建备份文件失败: {} -> {}", originalPath.toStdString(), backupPath.toStdString());
        return false;
    }

    // 清理旧备份文件
    cleanupOldBackups(dirPath, baseName, suffix);

    return true;
}

void OffsetChannel::cleanupOldBackups(const QString& dirPath, const QString& baseName, const QString& suffix) const {
    QDir dir(dirPath);
    QString pattern = QString("%1_*.%2").arg(baseName, suffix);
    QStringList backupFiles = dir.entryList(QStringList() << pattern, QDir::Files, QDir::Time | QDir::Reversed);

    // 保留最新的maxBackupCount个备份文件
    int maxBackups = m_config.maxBackupCount;
    if (backupFiles.size() > maxBackups) {
        for (int i = maxBackups; i < backupFiles.size(); ++i) {
            QString oldBackupPath = dir.absoluteFilePath(backupFiles[i]);
            if (QFile::remove(oldBackupPath)) {
            }
        }
    }
}
