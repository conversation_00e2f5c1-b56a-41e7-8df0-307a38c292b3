#include "core/OffsetDataManager.h"

#include <QApplication>
#include <QDir>
#include <QFile>
#include <QFileInfo>
#include <QJsonArray>
#include <QThread>
#include <QTimer>

#include "core/AppConfig.h"
#include "core/AppLogger.h"
#include "core/CncManager.h"
#include "core/OffsetChannel.h"
#include "public/ICncInterface.h"

// 静态成员变量定义
OffsetDataManager* OffsetDataManager::s_instance = nullptr;

OffsetDataManager::OffsetDataManager(QObject* parent) : QObject(parent) {
    // 初始化定时器
    m_delaySaveTimer = new QTimer(this);
    m_autoRefreshTimer = new QTimer(this);
    m_autoSaveEnabled = false;
    m_autoRefreshEnabled = false;

    // 配置延迟保存定时器（单次触发）
    m_delaySaveTimer->setSingleShot(true);

    // 连接定时器信号
    connect(m_delaySaveTimer, &QTimer::timeout, this, &OffsetDataManager::performDelayedSaveAllChannels);
    connect(m_autoRefreshTimer, &QTimer::timeout, this, &OffsetDataManager::performAutoRefresh);

    initializeSupportedWorkOffsets();
}

OffsetDataManager::~OffsetDataManager() {
    qDeleteAll(m_channels);
    m_channels.clear();
}

OffsetDataManager* OffsetDataManager::getInstance() {
    if (!s_instance) {
        s_instance = new OffsetDataManager();
    }
    return s_instance;
}

void OffsetDataManager::destroyInstance() {
    if (s_instance) {
        delete s_instance;
        s_instance = nullptr;
    }
}

void OffsetDataManager::initializeChannel(int channelId) {
    if (m_channels.contains(channelId)) {
        return;
    }

    auto* channel = new OffsetChannel(channelId, m_config, this);
    m_channels.insert(channelId, channel);

    // 连接通道信号到管理器信号
    connect(channel, &OffsetChannel::offsetValueChanged, this,
            [this, channelId](OffsetType type, const PointXD& newValue) {
                emit offsetValueChanged(channelId, type, newValue);
            });
    connect(channel, &OffsetChannel::preciseValueChanged, this,
            [this, channelId](OffsetType type, const PointXD& newPreciseValue) {
                emit preciseValueChanged(channelId, type, newPreciseValue);
            });
    connect(channel, &OffsetChannel::dataValidationFailed, this,
            [this, channelId](const QStringList& errors) { emit dataValidationFailed(channelId, errors); });

    channel->initializeOffsetData();

    // 尝试从自动保存文件加载数据
    if (m_config.autoSaveToFile && loadFromAutoSaveFile(channelId)) {
        // 同步到CNC系统
        saveToCncSystem(channelId);
    } else {
        // 如果未启用自动保存，直接从CNC系统加载
        loadFromCncSystem(channelId);
    }

    emit channelInitialized(channelId);
}

void OffsetDataManager::removeChannel(int channelId) {
    if (!m_channels.contains(channelId)) {
        return;
    }

    OffsetChannel* channel = m_channels.take(channelId);
    if (channel) {
        delete channel;
    }

    emit channelRemoved(channelId);
}

QList<int> OffsetDataManager::getActiveChannels() const { return m_channels.keys(); }

void OffsetDataManager::initializeOffsetData(int channelId) {
    if (auto* channel = m_channels.value(channelId)) {
        channel->initializeOffsetData();
    }
}

void OffsetDataManager::initializeOffsetDataManager() {
    // 根据配置启动自动功能
    if (m_config.autoSaveToFile) {
        startAutoSave();
    }
}

ValidationResult OffsetDataManager::updateOffsetValue(int channelId, OffsetType type, const PointXD& value,
                                                      bool clearPreciseValue) {
    if (auto* channel = m_channels.value(channelId)) {
        ValidationResult result = channel->updateOffsetValue(type, value, clearPreciseValue);
        if (result.isValid) {
            // 如果启用立即保存到CNC，立即保存
            if (m_config.immediateSaveToCnc) {
                bool cncSuccess = saveToCncSystem(channelId);
                if (!cncSuccess) {
                    LOG_WARN("通道 {} 立即保存到CNC系统失败，零偏类型: {}", channelId, static_cast<int>(type));
                }
            }

            // 数据修改成功，触发延迟文件保存
            triggerAutoSave(channelId);
        }
        return result;
    }
    return {false, "无效的通道ID"};
}

ValidationResult OffsetDataManager::updatePreciseValue(int channelId, OffsetType type, const PointXD& preciseValue) {
    if (auto* channel = m_channels.value(channelId)) {
        ValidationResult result = channel->updatePreciseValue(type, preciseValue);
        if (result.isValid) {
            // 如果启用立即保存到CNC，立即保存
            if (m_config.immediateSaveToCnc) {
                bool cncSuccess = saveToCncSystem(channelId);
                if (!cncSuccess) {
                    LOG_WARN("通道 {} 精确值立即保存到CNC系统失败，零偏类型: {}", channelId, static_cast<int>(type));
                }
            }

            // 数据修改成功，触发延迟文件保存
            triggerAutoSave(channelId);
        }
        return result;
    }
    return {false, "无效的通道ID"};
}

PointXD OffsetDataManager::getOffsetValue(int channelId, OffsetType type) const {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->getOffsetValue(type);
    }
    return {};
}
PointXD OffsetDataManager::getPreciseValue(int channelId, OffsetType type) const {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->getPreciseValue(type);
    }
    return {};
}

PointXD OffsetDataManager::getEffectiveOffsetValue(int channelId, OffsetType type) const {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->getEffectiveOffsetValue(type);
    }
    return {};
}

OffsetDataItem OffsetDataManager::getOffsetItem(int channelId, OffsetType type) const {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->getOffsetItem(type);
    }
    return {};
}

QList<OffsetDataItem> OffsetDataManager::getItemsForMode(int channelId, ZeroOffsetDisplayMode mode) const {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->getItemsForMode(mode);
    }
    return {};
}

bool OffsetDataManager::loadFromCncSystem(int channelId) {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->loadFromCncSystem();
    }
    return false;
}

bool OffsetDataManager::saveToCncSystem(int channelId) {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->saveToCncSystem();
    }
    return false;
}

bool OffsetDataManager::loadFromFile(int channelId, const QString& filePath) {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->loadFromFile(filePath);
    }
    return false;
}

bool OffsetDataManager::saveToFile(int channelId, const QString& filePath) const {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->saveToFile(filePath);
    }
    return false;
}

OffsetType OffsetDataManager::getCurrentWorkOffsetType(int channelId) const {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->getCurrentWorkOffsetType();
    }
    return OffsetType::DynamicWorkOffsetStart;
}

bool OffsetDataManager::setCurrentWorkOffsetType(int channelId, OffsetType type) {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->setCurrentWorkOffsetType(type);
    }
    return false;
}

void OffsetDataManager::initializeSupportedWorkOffsets() {
    m_workOffsetNameMapping.clear();
    m_workOffsetTypeMapping.clear();
    m_supportedWorkOffsetTypes.clear();

    CncManager* cncMgr = CncManager::getInstance();
    if (cncMgr && cncMgr->isInitialized()) {
        const SystemConfig& systemConfig = cncMgr->getSystemConfig();
        if (!systemConfig.supportedWorkOffsets.empty()) {
            int dynamicOffsetCounter = 0;
            for (const auto& workOffsetInfo : systemConfig.supportedWorkOffsets) {
                QString name = QString::fromStdString(workOffsetInfo.name);
                OffsetType type = static_cast<OffsetType>(static_cast<int>(OffsetType::DynamicWorkOffsetStart) +
                                                          dynamicOffsetCounter);

                if (!m_workOffsetNameMapping.contains(name)) {
                    m_workOffsetNameMapping[name] = type;
                    m_workOffsetTypeMapping[type] = name;
                    m_supportedWorkOffsetTypes.append(type);
                    dynamicOffsetCounter++;
                }
            }
            LOG_INFO("从CNC系统配置加载了{}个工件坐标系", systemConfig.supportedWorkOffsets.size());
            return;
        } else {
            LOG_ERROR("CNC系统配置中没有工件坐标系信息");
            return;
        }
    } else {
        LOG_WARN("CNC系统未初始化，使用默认工件坐标系配置");

        // 只有在CNC系统未初始化时才使用默认的G54-G59配置
        for (int i = 0; i < 6; ++i) {
            QString name = QString("G%1").arg(54 + i);
            OffsetType type = static_cast<OffsetType>(static_cast<int>(OffsetType::DynamicWorkOffsetStart) + i);

            m_workOffsetNameMapping[name] = type;
            m_workOffsetTypeMapping[type] = name;
            m_supportedWorkOffsetTypes.append(type);
        }
    }
}

void OffsetDataManager::setConfiguration(const OffsetManagerConfig& config) {
    m_config = config;

    // 根据新配置启动或停止自动功能
    if (m_config.autoSaveToFile) {
        startAutoSave();
    } else {
        stopAutoSave();
    }

    if (!m_config.autoSync) {
        stopAutoRefresh();
    }

    emit configurationChanged(m_config);
}

OffsetManagerConfig OffsetDataManager::getConfiguration() const { return m_config; }

QList<QString> OffsetDataManager::getSupportedWorkOffsetNames() const { return m_workOffsetNameMapping.keys(); }

QList<OffsetType> OffsetDataManager::getSupportedWorkOffsetTypes() const { return m_supportedWorkOffsetTypes; }

QMap<QString, OffsetType> OffsetDataManager::getWorkOffsetNameMapping() const { return m_workOffsetNameMapping; }

OffsetType OffsetDataManager::getWorkOffsetTypeByName(const QString& name) const {
    // 首先检查映射表中是否存在
    if (m_workOffsetNameMapping.contains(name)) {
        return m_workOffsetNameMapping.value(name);
    }

    // 如果映射表中没有，使用 stringToOffsetType 方法解析
    OffsetType type = stringToOffsetType(name);

    // 如果解析结果是工件坐标系类型，返回解析结果
    if (static_cast<int>(type) >= static_cast<int>(OffsetType::DynamicWorkOffsetStart)) {
        return type;
    }

    // 否则返回默认的工件坐标系 (G54)
    return OffsetType::DynamicWorkOffsetStart;
}

QString OffsetDataManager::getWorkOffsetNameByType(OffsetType type) const {
    // 首先检查映射表中是否存在
    if (m_workOffsetTypeMapping.contains(type)) {
        return m_workOffsetTypeMapping.value(type);
    }

    // 如果映射表中没有，使用 offsetTypeToString 方法生成名称
    return offsetTypeToString(type);
}

void OffsetDataManager::recalculateAllDependencies(int channelId) {
    if (auto* channel = m_channels.value(channelId)) {
        channel->recalculateAllDependencies();
    }
}

void OffsetDataManager::recalculateDependencies(int channelId, OffsetType changedType) {
    if (auto* channel = m_channels.value(channelId)) {
        channel->recalculateDependencies(changedType);
    }
}

QList<OffsetType> OffsetDataManager::getDependentTypes(int channelId, OffsetType type) const {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->getDependentTypes(type);
    }
    return {};
}

bool OffsetDataManager::hasCyclicDependency(int channelId, OffsetType type) const {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->hasCyclicDependency(type);
    }
    return false;
}

QString OffsetDataManager::offsetTypeToString(OffsetType type) const {
    // 将枚举值转换为字符串表示
    switch (type) {
        case OffsetType::BasicReference:
            return "基本参考";
        case OffsetType::DRF:
            return "DRF";
        case OffsetType::ChannelBasicOffset:
            return "通道基本偏移";
        case OffsetType::TotalBasicOffset:
            return "总基本偏移";
        case OffsetType::ProgrammedOffset:
            return "编程偏移";
        case OffsetType::CycleReference:
            return "循环参考";
        case OffsetType::CurrentWorkOffset:
            return "当前工件坐标系";
        case OffsetType::TotalOffset:
            return "总偏移";
        case OffsetType::CurrentToolOffset:
            return "当前刀具";
        case OffsetType::TOFF:
            return "TOFF";
        case OffsetType::MachineCoordinate:
            return "机床坐标系";
        case OffsetType::WorkCoordinate:
            return "工件坐标系";
        default:
            // 处理动态工件坐标系
            if (static_cast<int>(type) >= static_cast<int>(OffsetType::DynamicWorkOffsetStart)) {
                // 从类型映射中查找名称
                auto it = m_workOffsetTypeMapping.find(type);
                if (it != m_workOffsetTypeMapping.end()) {
                    return it.value();
                }

                // 如果映射中没有找到，使用默认命名
                int index = static_cast<int>(type) - static_cast<int>(OffsetType::DynamicWorkOffsetStart);
                return QString("DynamicWorkOffset%1").arg(index);
            }
            return "Unknown";
    }
}

OffsetType OffsetDataManager::stringToOffsetType(const QString& str) const {
    // 将字符串转换为枚举值
    if (str == "基本参考") return OffsetType::BasicReference;
    if (str == "DRF") return OffsetType::DRF;
    if (str == "通道基本偏移") return OffsetType::ChannelBasicOffset;
    if (str == "总基本偏移") return OffsetType::TotalBasicOffset;
    if (str == "编程偏移") return OffsetType::ProgrammedOffset;
    if (str == "循环参考") return OffsetType::CycleReference;
    if (str == "当前工件坐标系") return OffsetType::CurrentWorkOffset;
    if (str == "总偏移") return OffsetType::TotalOffset;
    if (str == "当前刀具") return OffsetType::CurrentToolOffset;
    if (str == "TOFF") return OffsetType::TOFF;
    if (str == "机床坐标系") return OffsetType::MachineCoordinate;
    if (str == "工件坐标系") return OffsetType::WorkCoordinate;

    // 处理工件坐标系名称
    auto it = m_workOffsetNameMapping.find(str);
    if (it != m_workOffsetNameMapping.end()) {
        return it.value();
    }

    // 处理动态工件坐标系
    if (str.startsWith("DynamicWorkOffset")) {
        bool ok;
        int index = str.mid(17).toInt(&ok);  // "DynamicWorkOffset".length() = 17
        if (ok) {
            return static_cast<OffsetType>(static_cast<int>(OffsetType::DynamicWorkOffsetStart) + index);
        }
    }

    return OffsetType::BasicReference;  // 默认值
}

ValidationResult OffsetDataManager::validateOffsetValue(int channelId, OffsetType type, const PointXD& value) const {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->validateOffsetValue(type, value);
    }
    return ValidationResult(false, "通道不存在");
}

ValidationResult OffsetDataManager::validateAllData(int channelId) const {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->validateAllData();
    }
    return ValidationResult(false, "通道不存在");
}

QMap<OffsetType, PointXD> OffsetDataManager::compareWithCncSystem(int channelId) const {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->compareWithCncSystem();
    }
    return {};
}

double OffsetDataManager::calculateTotalDeviation(int channelId, OffsetType type1, OffsetType type2) const {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->calculateTotalDeviation(type1, type2);
    }
    return 0.0;
}

bool OffsetDataManager::calculateCurrentWorkOffsetFromWCS(int channelId, int axisIndex, double desiredWcsValue,
                                                          double& currentWorkOffset) {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->calculateCurrentWorkOffsetFromWCS(axisIndex, desiredWcsValue, currentWorkOffset);
    }
    return false;
}

bool OffsetDataManager::updateCurrentWorkOffset(int channelId, int axisIndex, double currentWorkOffset) {
    if (auto* channel = m_channels.value(channelId)) {
        PointXD currentWorkOffsetPd = channel->getOffsetValue(OffsetType::CurrentWorkOffset);

        currentWorkOffsetPd.coordinates[axisIndex] = currentWorkOffset;

        ValidationResult result =
            this->updateOffsetValue(channelId, OffsetType::CurrentWorkOffset, currentWorkOffsetPd, true);
        if (result.isValid) {
            return true;
        }
    }
    return false;
}

// ========== 刀具长度计算功能实现 ==========

ValidationResult OffsetDataManager::calculateRequiredToolLength(int channelId, const PointXD& currentMachinePosition,
                                                                const PointXD& desiredWorkPosition,
                                                                PointXD& calculatedToolOffset) const {
    LOG_DEBUG("开始计算刀具长度，通道ID: {}", channelId);

    // 验证通道有效性
    if (!m_channels.contains(channelId)) {
        QString error = QString("无效的通道ID: %1").arg(channelId);
        LOG_ERROR("刀具长度计算失败: {}", error.toStdString());
        return ValidationResult(false, error);
    }

    auto* channel = m_channels.value(channelId);

    try {
        // 获取当前的总零偏值
        PointXD totalOffset = channel->getEffectiveOffsetValue(OffsetType::TotalOffset);

        // 获取TOFF值
        PointXD toffValue = channel->getEffectiveOffsetValue(OffsetType::TOFF);

        // 计算公式：当前刀具偏移 = MCS实际值 - 总零偏 - WCS期望值 - TOFF
        calculatedToolOffset.coordinates.clear();

        // 遍历所有轴，计算每个轴的刀具偏移
        QSet<int> allAxes;
        for (const auto& pair : currentMachinePosition.coordinates) {
            allAxes.insert(pair.first);
        }
        for (const auto& pair : desiredWorkPosition.coordinates) {
            allAxes.insert(pair.first);
        }

        for (int axisIndex : allAxes) {
            auto machineIt = currentMachinePosition.coordinates.find(axisIndex);
            auto workIt = desiredWorkPosition.coordinates.find(axisIndex);
            auto totalOffsetIt = totalOffset.coordinates.find(axisIndex);
            auto toffIt = toffValue.coordinates.find(axisIndex);

            double machinePos = (machineIt != currentMachinePosition.coordinates.end()) ? machineIt->second : 0.0;
            double workPos = (workIt != desiredWorkPosition.coordinates.end()) ? workIt->second : 0.0;
            double totalOffsetVal = (totalOffsetIt != totalOffset.coordinates.end()) ? totalOffsetIt->second : 0.0;
            double toffVal = (toffIt != toffValue.coordinates.end()) ? toffIt->second : 0.0;

            // 计算刀具偏移
            double toolOffset = machinePos - totalOffsetVal - workPos - toffVal;
            calculatedToolOffset.coordinates[axisIndex] = toolOffset;

            LOG_DEBUG("轴 {} 刀具长度计算: MCS={:.4f}, WCS={:.4f}, 总零偏={:.4f}, TOFF={:.4f}, 刀具偏移={:.4f}",
                      axisIndex, machinePos, workPos, totalOffsetVal, toffVal, toolOffset);
        }

        // 验证计算结果
        ValidationResult validation = validateToolLength(channelId, calculatedToolOffset);
        if (!validation.isValid) {
            return validation;
        }

        LOG_INFO("刀具长度计算成功，通道ID: {}", channelId);
        return ValidationResult(true);

    } catch (const std::exception& e) {
        QString error = QString("刀具长度计算异常: %1").arg(e.what());
        LOG_ERROR("刀具长度计算失败: {}", error.toStdString());
        return ValidationResult(false, error);
    }
}

ValidationResult OffsetDataManager::calculateRequiredToolLengthAuto(int channelId, const PointXD& desiredWorkPosition,
                                                                    PointXD& calculatedToolOffset) const {
    LOG_DEBUG("开始自动获取机器坐标并计算刀具长度，通道ID: {}", channelId);

    // 验证通道有效性
    if (!m_channels.contains(channelId)) {
        QString error = QString("无效的通道ID: %1").arg(channelId);
        LOG_ERROR("自动刀具长度计算失败: {}", error.toStdString());
        return ValidationResult(false, error);
    }

    try {
        // 从CNC系统获取当前机器坐标
        auto cncManager = CncManager::getInstance();
        if (!cncManager || !cncManager->isInitialized()) {
            QString error = "CNC系统未初始化，无法获取当前机器坐标";
            LOG_ERROR("自动刀具长度计算失败: {}", error.toStdString());
            return ValidationResult(false, error);
        }

        PointXD currentMachinePosition;
        ErrorCode result = cncManager->getMachinePosition(channelId, currentMachinePosition);
        if (result != ErrorCode::Success) {
            QString error = QString("获取机器坐标失败，错误代码: %1").arg(static_cast<int>(result));
            LOG_ERROR("自动刀具长度计算失败: {}", error.toStdString());
            return ValidationResult(false, error);
        }

        LOG_DEBUG("成功获取当前机器坐标，轴数量: {}", currentMachinePosition.coordinates.size());

        // 调用主计算方法
        return calculateRequiredToolLength(channelId, currentMachinePosition, desiredWorkPosition,
                                           calculatedToolOffset);

    } catch (const std::exception& e) {
        QString error = QString("自动刀具长度计算异常: %1").arg(e.what());
        LOG_ERROR("自动刀具长度计算失败: {}", error.toStdString());
        return ValidationResult(false, error);
    }
}

ValidationResult OffsetDataManager::calculateAxisToolLength(int channelId, int axisIndex, double currentMachinePos,
                                                            double desiredWorkPos, double& calculatedToolLength) const {
    LOG_DEBUG("开始计算单轴刀具长度，通道ID: {}, 轴索引: {}", channelId, axisIndex);

    // 验证通道有效性
    if (!m_channels.contains(channelId)) {
        QString error = QString("无效的通道ID: %1").arg(channelId);
        LOG_ERROR("单轴刀具长度计算失败: {}", error.toStdString());
        return ValidationResult(false, error);
    }

    auto* channel = m_channels.value(channelId);

    try {
        // 获取当前的总零偏值
        PointXD totalOffset = channel->getEffectiveOffsetValue(OffsetType::TotalOffset);

        // 获取TOFF值
        PointXD toffValue = channel->getEffectiveOffsetValue(OffsetType::TOFF);

        // 获取指定轴的零偏值
        auto totalOffsetIt = totalOffset.coordinates.find(axisIndex);
        auto toffIt = toffValue.coordinates.find(axisIndex);
        double totalOffsetVal = (totalOffsetIt != totalOffset.coordinates.end()) ? totalOffsetIt->second : 0.0;
        double toffVal = (toffIt != toffValue.coordinates.end()) ? toffIt->second : 0.0;

        // 计算刀具长度: 刀具偏移 = MCS实际值 - 总零偏 - WCS期望值 - TOFF
        calculatedToolLength = currentMachinePos - totalOffsetVal - desiredWorkPos - toffVal;

        LOG_DEBUG("轴 {} 刀具长度计算详情: MCS={:.4f}, WCS={:.4f}, 总零偏={:.4f}, TOFF={:.4f}, 计算结果={:.4f}",
                  axisIndex, currentMachinePos, desiredWorkPos, totalOffsetVal, toffVal, calculatedToolLength);

        // 验证计算结果的合理性
        if (std::abs(calculatedToolLength) > m_config.maxValue) {
            QString warning = QString("计算的刀具长度 %1 超出合理范围 [%2, %3]")
                                  .arg(calculatedToolLength)
                                  .arg(-m_config.maxValue)
                                  .arg(m_config.maxValue);
            LOG_WARN("单轴刀具长度计算警告: {}", warning.toStdString());
            ValidationResult result(true);
            result.warnings.append(warning);
            return result;
        }

        LOG_INFO("单轴刀具长度计算成功，轴 {}: {:.4f}", axisIndex, calculatedToolLength);
        return ValidationResult(true);

    } catch (const std::exception& e) {
        QString error = QString("单轴刀具长度计算异常: %1").arg(e.what());
        LOG_ERROR("单轴刀具长度计算失败: {}", error.toStdString());
        return ValidationResult(false, error);
    }
}

ValidationResult OffsetDataManager::validateToolLength(int channelId, const PointXD& toolOffset) const {
    LOG_DEBUG("开始验证刀具长度，通道ID: {}", channelId);

    QStringList warnings;

    // 检查每个轴的刀具偏移值是否在合理范围内
    for (const auto& pair : toolOffset.coordinates) {
        int axisIndex = pair.first;
        double offset = pair.second;

        // 检查绝对值是否超出最大限制
        if (std::abs(offset) > m_config.maxValue) {
            QString error =
                QString("轴 %1 的刀具偏移 %2 超出最大允许值 %3").arg(axisIndex).arg(offset).arg(m_config.maxValue);
            LOG_ERROR("刀具长度验证失败: {}", error.toStdString());
            return ValidationResult(false, error);
        }

        // 检查是否小于最小限制
        if (offset < m_config.minValue) {
            QString error =
                QString("轴 %1 的刀具偏移 %2 小于最小允许值 %3").arg(axisIndex).arg(offset).arg(m_config.minValue);
            LOG_ERROR("刀具长度验证失败: {}", error.toStdString());
            return ValidationResult(false, error);
        }

        // 检查是否过大（可能的计算错误）
        if (std::abs(offset) > m_config.maxValue * 0.8) {
            QString warning = QString("轴 %1 的刀具偏移 %2 较大，请检查计算参数").arg(axisIndex).arg(offset);
            warnings.append(warning);
            LOG_WARN("刀具长度验证警告: {}", warning.toStdString());
        }
    }

    // 检查是否所有轴都有合理的值
    if (toolOffset.coordinates.empty()) {
        QString error = "计算的刀具偏移为空";
        LOG_ERROR("刀具长度验证失败: {}", error.toStdString());
        return ValidationResult(false, error);
    }

    LOG_DEBUG("刀具长度验证通过，警告数量: {}", warnings.size());
    ValidationResult result(true);
    result.warnings = warnings;
    return result;
}

ValidationResult OffsetDataManager::applyCalculatedToolLength(int channelId, const PointXD& toolOffset,
                                                              bool updateCncSystem) {
    LOG_DEBUG("开始应用计算的刀具长度，通道ID: {}, 更新CNC: {}", channelId, updateCncSystem);

    // 验证通道有效性
    if (!m_channels.contains(channelId)) {
        QString error = QString("无效的通道ID: %1").arg(channelId);
        LOG_ERROR("应用刀具长度失败: {}", error.toStdString());
        return ValidationResult(false, error);
    }

    try {
        // 首先验证刀具长度
        ValidationResult validation = validateToolLength(channelId, toolOffset);
        if (!validation.isValid) {
            return validation;
        }

        // 更新当前刀具偏移值
        ValidationResult updateResult = updateOffsetValue(channelId, OffsetType::CurrentToolOffset, toolOffset, false);
        if (!updateResult.isValid) {
            QString error = QString("更新当前刀具偏移失败: %1").arg(updateResult.errorMessage);
            LOG_ERROR("应用刀具长度失败: {}", error.toStdString());
            return ValidationResult(false, error);
        }

        // 如果需要，立即同步到CNC系统
        if (updateCncSystem) {
            bool cncSuccess = saveSpecificOffset(channelId, OffsetType::CurrentToolOffset);
            if (!cncSuccess) {
                QString warning = "刀具长度已更新到本地，但同步到CNC系统失败";
                LOG_WARN("应用刀具长度警告: {}", warning.toStdString());
                ValidationResult result(true);
                result.warnings.append(warning);
                return result;
            }
            LOG_DEBUG("刀具长度已成功同步到CNC系统");
        }

        LOG_INFO("刀具长度应用成功，通道ID: {}", channelId);

        // 合并验证警告
        QStringList allWarnings = validation.warnings;
        if (!updateResult.warnings.isEmpty()) {
            allWarnings.append(updateResult.warnings);
        }

        ValidationResult result(true);
        result.warnings = allWarnings;
        return result;

    } catch (const std::exception& e) {
        QString error = QString("应用刀具长度异常: %1").arg(e.what());
        LOG_ERROR("应用刀具长度失败: {}", error.toStdString());
        return ValidationResult(false, error);
    }
}

QJsonObject OffsetDataManager::toJsonObject(int channelId) const {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->toJsonObject();
    }
    return {};
}

bool OffsetDataManager::fromJsonObject(int channelId, const QJsonObject& obj) {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->fromJsonObject(obj);
    }
    return false;
}

// 分组相关方法
QList<OffsetDataItem> OffsetDataManager::getItemsByGroup(int channelId, int groupId) const {
    if (auto* channel = m_channels.value(channelId)) {
        QList<OffsetDataItem> allItems = channel->getItemsForMode(ZeroOffsetDisplayMode::Overview);
        QList<OffsetDataItem> groupItems;

        // 根据组ID过滤数据项
        for (const auto& item : allItems) {
            int itemGroupId = OffsetUtils::getGroupIdForOffsetType(item.type);
            if (itemGroupId == groupId) {
                groupItems.append(item);
            }
        }

        return groupItems;
    }
    return {};
}

QMap<int, QList<OffsetDataItem>> OffsetDataManager::getItemsGrouped(int channelId) const {
    QMap<int, QList<OffsetDataItem>> groupedItems;

    if (auto* channel = m_channels.value(channelId)) {
        QList<OffsetDataItem> allItems = channel->getItemsForMode(ZeroOffsetDisplayMode::Overview);

        // 按组ID分类数据项
        for (const auto& item : allItems) {
            int groupId = OffsetUtils::getGroupIdForOffsetType(item.type);
            groupedItems[groupId].append(item);
        }
    }

    return groupedItems;
}

QList<int> OffsetDataManager::getAvailableGroupIds(int channelId) const {
    QSet<int> groupIds;

    if (auto* channel = m_channels.value(channelId)) {
        QList<OffsetDataItem> allItems = channel->getItemsForMode(ZeroOffsetDisplayMode::Overview);

        // 收集所有存在的组ID
        for (const auto& item : allItems) {
            int groupId = OffsetUtils::getGroupIdForOffsetType(item.type);
            if (groupId > 0) {  // 排除未知组
                groupIds.insert(groupId);
            }
        }
    }

    QList<int> result = groupIds.values();
    std::sort(result.begin(), result.end());
    return result;
}

QString OffsetDataManager::getGroupName(int groupId) const { return OffsetUtils::getGroupName(groupId); }

// 批量操作方法
bool OffsetDataManager::batchUpdateOffsets(int channelId, const QMap<OffsetType, PointXD>& updates) {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->batchUpdateOffsets(updates);
    }
    return false;
}

bool OffsetDataManager::batchUpdatePreciseValues(int channelId, const QMap<OffsetType, PointXD>& preciseUpdates) {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->batchUpdatePreciseValues(preciseUpdates);
    }
    return false;
}

bool OffsetDataManager::resetAllOffsets(int channelId) {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->resetAllOffsets();
    }
    return false;
}

bool OffsetDataManager::resetAllPreciseValues(int channelId) {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->resetAllPreciseValues();
    }
    return false;
}

bool OffsetDataManager::resetOffsetGroup(int channelId, ZeroOffsetDisplayMode mode) {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->resetOffsetGroup(mode);
    }
    return false;
}

// 状态查询方法
bool OffsetDataManager::hasUnsavedChanges(int channelId) const {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->hasUnsavedChanges();
    }
    return false;
}

void OffsetDataManager::markAsSaved(int channelId) {
    if (auto* channel = m_channels.value(channelId)) {
        channel->markAsSaved();
    }
}

QStringList OffsetDataManager::getValidationErrors(int channelId) const {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->getValidationErrors();
    }
    return {};
}

QStringList OffsetDataManager::getValidationWarnings(int channelId) const {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->getValidationWarnings();
    }
    return {};
}

QString OffsetDataManager::getCurrentWorkOffsetName(int channelId) const {
    if (auto* channel = m_channels.value(channelId)) {
        OffsetType currentType = channel->getCurrentWorkOffsetType();
        return getWorkOffsetNameByType(currentType);
    }

    // 默认值：返回映射中的第一个工件坐标系名称，如果没有则返回空字符串
    if (!m_workOffsetNameMapping.isEmpty()) {
        return m_workOffsetNameMapping.firstKey();
    }
    return QString();  // 如果没有配置任何工件坐标系，返回空字符串
}

bool OffsetDataManager::updateCurrentToolOffset(int channelId) {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->updateCurrentToolOffset();
    }
    return false;
}

bool OffsetDataManager::loadCurrentToolOffsetFromCnc(int channelId, int toolNumber) {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->loadCurrentToolOffsetFromCnc(toolNumber);
    }
    return false;
}

bool OffsetDataManager::saveSpecificOffset(int channelId, OffsetType type) {
    if (auto* channel = m_channels.value(channelId)) {
        return channel->saveSpecificOffset(type);
    }
    return false;
}

// ========== 自动保存和刷新功能实现 ==========

void OffsetDataManager::startAutoSave() {
    if (!m_config.autoSaveToFile) {
        LOG_WARN("自动保存未在配置中启用");
        return;
    }

    if (m_autoSaveEnabled) {
        LOG_DEBUG("自动保存已启用，无需重复启动");
        return;
    }

    LOG_INFO("启动自动保存，延迟时间: {} 毫秒", m_config.autoSaveInterval);
    ensureAutoSaveDirectory();
    m_delaySaveTimer->setInterval(m_config.autoSaveInterval);
    m_autoSaveEnabled = true;
}

void OffsetDataManager::stopAutoSave() {
    if (!m_autoSaveEnabled) {
        LOG_DEBUG("自动保存未启用，无需停止");
        return;
    }

    LOG_INFO("停止自动保存");
    m_delaySaveTimer->stop();
    m_pendingSaveChannels.clear();
    m_autoSaveEnabled = false;
}

bool OffsetDataManager::isAutoSaveEnabled() const { return m_autoSaveEnabled; }

void OffsetDataManager::startAutoRefresh() {
    if (!m_config.autoSync) {
        LOG_WARN("自动同步未在配置中启用");
        return;
    }

    if (m_autoRefreshEnabled) {
        LOG_DEBUG("自动刷新已启用，无需重复启动");
        return;
    }

    LOG_INFO("启动自动刷新，间隔: {} 毫秒", m_config.syncInterval);
    m_autoRefreshTimer->start(m_config.syncInterval);
    m_autoRefreshEnabled = true;
}

void OffsetDataManager::stopAutoRefresh() {
    if (!m_autoRefreshEnabled) {
        LOG_DEBUG("自动刷新未启用，无需停止");
        return;
    }

    LOG_INFO("停止自动刷新");
    m_autoRefreshTimer->stop();
    m_autoRefreshEnabled = false;
}

bool OffsetDataManager::isAutoRefreshEnabled() const { return m_autoRefreshEnabled; }

void OffsetDataManager::setAutoSaveInterval(int milliseconds) {
    if (milliseconds < 500) {
        LOG_WARN("自动保存延迟时间不能少于500毫秒，设置为500毫秒");
        milliseconds = 500;
    }

    m_config.autoSaveInterval = milliseconds;
    LOG_INFO("设置自动保存延迟时间为: {} 毫秒", milliseconds);

    if (m_autoSaveEnabled) {
        m_delaySaveTimer->setInterval(milliseconds);
    }
}

void OffsetDataManager::setAutoRefreshInterval(int milliseconds) {
    if (milliseconds < 500) {
        LOG_WARN("自动刷新间隔不能少于500毫秒，设置为500毫秒");
        milliseconds = 500;
    }

    m_config.syncInterval = milliseconds;
    LOG_INFO("设置自动刷新间隔为: {} 毫秒", milliseconds);

    if (m_autoRefreshEnabled) {
        m_autoRefreshTimer->setInterval(milliseconds);
    }
}

int OffsetDataManager::getAutoSaveInterval() const { return m_config.autoSaveInterval; }

int OffsetDataManager::getAutoRefreshInterval() const { return m_config.syncInterval; }

void OffsetDataManager::triggerAutoSave(int channelId) {
    if (!m_autoSaveEnabled || !m_config.autoSaveToFile) {
        return;
    }

    // 添加到待保存列表
    m_pendingSaveChannels.insert(channelId);

    // 重启延迟定时器（防抖动）
    m_delaySaveTimer->stop();
    m_delaySaveTimer->start();

    LOG_DEBUG("触发通道 {} 的延迟自动保存，延迟时间: {} 毫秒", channelId, m_config.autoSaveInterval);
}

void OffsetDataManager::triggerAutoSaveAllChannels() {
    if (!m_autoSaveEnabled || !m_config.autoSaveToFile) {
        return;
    }

    // 添加所有通道到待保存列表
    for (int channelId : m_channels.keys()) {
        m_pendingSaveChannels.insert(channelId);
    }

    // 重启延迟定时器（防抖动）
    m_delaySaveTimer->stop();
    m_delaySaveTimer->start();

    LOG_DEBUG("触发所有通道的延迟自动保存，延迟时间: {} 毫秒", m_config.autoSaveInterval);
}

void OffsetDataManager::performAutoSave() {
    LOG_DEBUG("执行自动保存");

    bool allSaved = true;
    int savedCount = 0;

    for (auto it = m_channels.begin(); it != m_channels.end(); ++it) {
        int channelId = it.key();

        // 只保存有未保存更改的通道
        if (hasUnsavedChanges(channelId)) {
            QString filePath = getAutoSaveFilePath(channelId);

            // 创建备份文件
            if (m_config.keepBackups) {
                createBackupFile(channelId);
            }

            bool success = saveToFile(channelId, filePath);
            if (success) {
                savedCount++;
                markAsSaved(channelId);
                LOG_DEBUG("通道 {} 自动保存成功: {}", channelId, filePath.toStdString());
            } else {
                allSaved = false;
                LOG_ERROR("通道 {} 自动保存失败: {}", channelId, filePath.toStdString());
            }

            emit autoFileSaveCompleted(channelId, success, filePath);
        }
    }

    if (savedCount > 0) {
        LOG_INFO("自动保存完成，成功保存 {} 个通道", savedCount);

        // 清理旧备份文件
        if (m_config.keepBackups) {
            for (int channelId : m_channels.keys()) {
                cleanupOldBackups(channelId);
            }
        }
    }
}

void OffsetDataManager::performAutoRefresh() {
    int refreshedCount = 0;
    int skippedCount = 0;

    for (auto it = m_channels.begin(); it != m_channels.end(); ++it) {
        int channelId = it.key();

        // 检查通道是否有未保存的修改
        if (hasUnsavedChanges(channelId)) {
            skippedCount++;
            continue;
        }

        bool success = loadFromCncSystem(channelId);
        if (success) {
            refreshedCount++;
        } else {
            LOG_WARN("通道 {} 自动刷新失败", channelId);
        }

        emit syncWithCncCompleted(channelId, success);
    }
}

void OffsetDataManager::performDelayedSave(int channelId) {
    if (!m_autoSaveEnabled || !m_config.autoSaveToFile) {
        return;
    }

    // 只保存指定通道且有未保存更改的数据
    if (hasUnsavedChanges(channelId)) {
        QString filePath = getAutoSaveFilePath(channelId);

        // 创建备份文件
        if (m_config.keepBackups) {
            createBackupFile(channelId);
        }

        bool success = saveToFile(channelId, filePath);
        if (success) {
            markAsSaved(channelId);
            LOG_DEBUG("通道 {} 延迟自动保存成功: {}", channelId, filePath.toStdString());
        } else {
            LOG_ERROR("通道 {} 延迟自动保存失败: {}", channelId, filePath.toStdString());
        }

        emit autoFileSaveCompleted(channelId, success, filePath);
    }
}

void OffsetDataManager::performDelayedSaveAllChannels() {
    if (!m_autoSaveEnabled || !m_config.autoSaveToFile) {
        return;
    }

    LOG_DEBUG("执行延迟自动保存，待保存通道数: {}", m_pendingSaveChannels.size());

    int savedCount = 0;
    QSet<int> channelsToSave = m_pendingSaveChannels;  // 复制集合
    m_pendingSaveChannels.clear();                     // 清空待保存列表

    for (int channelId : channelsToSave) {
        // 检查通道是否仍然存在且有未保存更改
        if (m_channels.contains(channelId) && hasUnsavedChanges(channelId)) {
            QString filePath = getAutoSaveFilePath(channelId);

            // 创建备份文件
            if (m_config.keepBackups) {
                createBackupFile(channelId);
            }

            // 根据配置保存到文件和/或CNC系统
            bool fileSuccess = true;
            bool cncSuccess = true;

            if (m_config.autoSaveToFile) {
                fileSuccess = saveToFile(channelId, filePath);
            }

            if (m_config.autoSaveToCnc) {
                cncSuccess = saveToCncSystem(channelId);
            }

            // 只要有一种保存方式成功就认为成功
            bool success = fileSuccess || cncSuccess;
            if (success) {
                savedCount++;
                markAsSaved(channelId);
                LOG_DEBUG("通道 {} 延迟自动保存成功: 文件={}, CNC={}", channelId, fileSuccess, cncSuccess);
            } else {
                LOG_ERROR("通道 {} 延迟自动保存失败: 文件={}, CNC={}", channelId, fileSuccess, cncSuccess);
            }

            // 提供详细的错误信息
            if (m_config.autoSaveToFile && !fileSuccess) {
                LOG_WARN("通道 {} 文件保存失败", channelId);
            }
            if (m_config.autoSaveToCnc && !cncSuccess) {
                LOG_WARN("通道 {} CNC系统保存失败", channelId);
            }

            emit autoFileSaveCompleted(channelId, success, filePath);
        }
    }

    if (savedCount > 0) {
        LOG_INFO("延迟自动保存完成，成功保存 {} 个通道", savedCount);

        // 清理旧备份文件
        if (m_config.keepBackups) {
            for (int channelId : channelsToSave) {
                cleanupOldBackups(channelId);
            }
        }
    }
}

bool OffsetDataManager::loadFromAutoSaveFile(int channelId) {
    QString filePath = getAutoSaveFilePath(channelId);

    if (!QFile::exists(filePath)) {
        LOG_DEBUG("通道 {} 的自动保存文件不存在: {}", channelId, filePath.toStdString());
        return false;
    }

    LOG_INFO("从自动保存文件加载通道 {} 数据: {}", channelId, filePath.toStdString());
    bool success = loadFromFile(channelId, filePath);

    if (success) {
        markAsSaved(channelId);
        LOG_INFO("通道 {} 自动保存文件加载成功", channelId);
    } else {
        LOG_ERROR("通道 {} 自动保存文件加载失败", channelId);
    }

    return success;
}

QString OffsetDataManager::getAutoSaveFilePath(int channelId) const {
    QString appDataDir = AppConfig::instance()->dataPath();
    QString offsetDataDir = QDir(appDataDir).filePath(m_config.autoSaveDir);
    return QDir(offsetDataDir).filePath(QString("offset_channel_%1.json").arg(channelId));
}

void OffsetDataManager::createBackupFile(int channelId) {
    QString originalPath = getAutoSaveFilePath(channelId);

    if (!QFile::exists(originalPath)) {
        return;  // 原文件不存在，无需备份
    }

    QDateTime now = QDateTime::currentDateTime();
    QString timestamp = now.toString("yyyyMMdd_hhmmss");

    QFileInfo fileInfo(originalPath);
    QString backupPath = QDir(fileInfo.absoluteDir())
                             .filePath(QString("offset_channel_%1_backup_%2.json").arg(channelId).arg(timestamp));

    if (QFile::copy(originalPath, backupPath)) {
        LOG_DEBUG("创建备份文件成功: {}", backupPath.toStdString());
        emit backupCreated(channelId, backupPath);
    } else {
        LOG_WARN("创建备份文件失败: {}", backupPath.toStdString());
    }
}

void OffsetDataManager::cleanupOldBackups(int channelId) {
    QDir dir(getAutoSaveFilePath(channelId));
    dir.cdUp();  // 返回到 offset_data 目录

    QStringList nameFilters;
    nameFilters << QString("offset_channel_%1_backup_*.json").arg(channelId);
    QFileInfoList backupFiles = dir.entryInfoList(nameFilters, QDir::Files, QDir::Time);

    // 保留最新的 m_config.maxBackupCount 个备份
    if (backupFiles.size() > m_config.maxBackupCount) {
        for (int i = m_config.maxBackupCount; i < backupFiles.size(); ++i) {
            QFile::remove(backupFiles.at(i).absoluteFilePath());
        }
    }
}

void OffsetDataManager::ensureAutoSaveDirectory() {
    QString appDataDir = AppConfig::instance()->dataPath();
    QString offsetDataDir = QDir(appDataDir).filePath(m_config.autoSaveDir);

    QDir dir;
    if (!dir.exists(offsetDataDir)) {
        if (dir.mkpath(offsetDataDir)) {
            LOG_INFO("创建自动保存目录: {}", offsetDataDir.toStdString());
        } else {
            LOG_ERROR("创建自动保存目录失败: {}", offsetDataDir.toStdString());
        }
    }
}

void OffsetDataManager::requestAutoRefresh(QObject* subscriber) {
    if (!subscriber) {
        return;
    }

    if (m_autoRefreshSubscribers.isEmpty()) {
        LOG_INFO("第一个订阅者 ({}) 请求自动刷新，启动定时器。", subscriber->objectName().toStdString());
        startAutoRefresh();
    }

    if (!m_autoRefreshSubscribers.contains(subscriber)) {
        m_autoRefreshSubscribers.insert(subscriber);
        connect(subscriber, &QObject::destroyed, this, &OffsetDataManager::onSubscriberDestroyed);
        LOG_DEBUG("新增自动刷新订阅者: {} (当前共 {} 个)", subscriber->objectName().toStdString(),
                  m_autoRefreshSubscribers.count());
    }
}

void OffsetDataManager::releaseAutoRefresh(QObject* subscriber) {
    if (!subscriber || !m_autoRefreshSubscribers.contains(subscriber)) {
        return;
    }

    disconnect(subscriber, &QObject::destroyed, this, &OffsetDataManager::onSubscriberDestroyed);
    m_autoRefreshSubscribers.remove(subscriber);
    LOG_DEBUG("释放自动刷新订阅者: {} (剩余 {} 个)", subscriber->objectName().toStdString(),
              m_autoRefreshSubscribers.count());

    if (m_autoRefreshSubscribers.isEmpty()) {
        LOG_INFO("最后一个订阅者已释放，停止自动刷新定时器。");
        stopAutoRefresh();
    }
}

void OffsetDataManager::onSubscriberDestroyed(QObject* obj) {
    if (obj) {
        LOG_WARN("检测到订阅者 {} 已被销毁，自动释放刷新请求。", obj->objectName().toStdString());
        releaseAutoRefresh(obj);
    }
}
