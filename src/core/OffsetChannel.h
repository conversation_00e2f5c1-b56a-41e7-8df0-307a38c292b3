#pragma once

#include <QDateTime>
#include <QJsonObject>
#include <QList>
#include <QMap>
#include <QObject>
#include <QSet>
#include <QString>
#include <QStringList>
#include <QTimer>
#include <functional>

#include "core/OffsetDataTypes.h"

class OffsetChannel : public QObject {
    Q_OBJECT

   public:
    explicit OffsetChannel(int channelId, const OffsetManagerConfig& config, QObject* parent = nullptr);
    ~OffsetChannel() override;

    // 基础数据管理
    void initializeOffsetData();
    ValidationResult updateOffsetValue(OffsetType type, const PointXD& value, bool clearPreciseValue = false);
    ValidationResult updatePreciseValue(OffsetType type, const PointXD& preciseValue);
    int channelId() const { return m_channelId; }

    // 数据获取器
    PointXD getOffsetValue(OffsetType type) const;
    PointXD getPreciseValue(OffsetType type) const;
    PointXD getEffectiveOffsetValue(OffsetType type) const;
    OffsetDataItem getOffsetItem(OffsetType type) const;

    // 显示模式支持
    QList<OffsetDataItem> getItemsForMode(ZeroOffsetDisplayMode mode) const;

    // 分组管理
    QList<OffsetDataItem> getItemsByGroup(int groupId) const;
    QMap<int, QList<OffsetDataItem>> getItemsGrouped() const;
    QList<int> getAvailableGroupIds() const;
    QString getGroupName(int groupId) const;

    // 工件坐标系管理
    OffsetType getCurrentWorkOffsetType() const;
    bool setCurrentWorkOffsetType(OffsetType type);
    QString getCurrentWorkOffsetName() const;

    // 工件坐标系名称支持（通过回调函数获取，避免重复存储）
    void setWorkOffsetAccessors(std::function<QString(OffsetType)> getNameFunc,
                                std::function<OffsetType(const QString&)> getTypeFunc);
    QString getWorkOffsetNameByType(OffsetType type) const;
    OffsetType getWorkOffsetTypeByName(const QString& name) const;

    // 文件操作
    bool loadFromFile(const QString& filePath);
    bool saveToFile(const QString& filePath) const;
    bool loadFromCncSystem();
    bool saveToCncSystem();

    // 依赖关系管理
    void recalculateAllDependencies();
    void recalculateDependencies(OffsetType changedType);
    QList<OffsetType> getDependentTypes(OffsetType type) const;
    bool hasCyclicDependency(OffsetType type) const;

    // 状态管理
    bool hasUnsavedChanges() const;
    void markAsSaved();
    QStringList getValidationErrors() const;
    QStringList getValidationWarnings() const;

    // 数据验证
    ValidationResult validateOffsetValue(OffsetType type, const PointXD& value) const;
    ValidationResult validateAllData() const;

    // 批量操作
    bool batchUpdateOffsets(const QMap<OffsetType, PointXD>& updates);
    bool batchUpdatePreciseValues(const QMap<OffsetType, PointXD>& preciseUpdates);
    bool resetAllOffsets();
    bool resetAllPreciseValues();
    bool resetOffsetGroup(ZeroOffsetDisplayMode mode);

    // JSON序列化
    QJsonObject toJsonObject() const;
    bool fromJsonObject(const QJsonObject& obj);

    // CNC系统比较
    QMap<OffsetType, PointXD> compareWithCncSystem() const;
    double calculateTotalDeviation(OffsetType type1, OffsetType type2) const;

    // 刀具偏移管理
    bool updateCurrentToolOffset();
    bool loadCurrentToolOffsetFromCnc(int toolNumber = -1);
    bool saveSpecificOffset(OffsetType type);

    // 根据期望的工件坐标系，返回匹配的当前工件坐标系
    bool calculateCurrentWorkOffsetFromWCS(int axisIndex, double desiredWcsValue, double& currentWorkOffset);

    // 自动同步和保存控制
    void startAutoSync();
    void stopAutoSync();
    void startAutoFileSave();
    void stopAutoFileSave();

    // 配置管理
    void updateConfiguration(const OffsetManagerConfig& config);
    OffsetManagerConfig getConfiguration() const;

   signals:
    // 数据变更信号
    void offsetValueChanged(OffsetType type, const PointXD& newValue);
    void preciseValueChanged(OffsetType type, const PointXD& newPreciseValue);
    void currentWorkOffsetChanged(OffsetType newType);
    void dataValidationFailed(const QStringList& errors);

    // 文件操作信号
    void fileLoadCompleted(bool success, const QString& filePath);
    void fileSaveCompleted(bool success, const QString& filePath);
    void autoFileSaveCompleted(bool success, const QString& filePath);

    // CNC系统同步信号
    void syncWithCncCompleted(bool success);
    void cncDataLoadCompleted(bool success);
    void cncDataSaveCompleted(bool success);

    // 批量操作信号
    void batchOperationCompleted(const QString& operationType, bool success);

   private slots:
    void performSync();
    void performAutoFileSave();

   private:
    // 初始化方法
    void setupOffsetDefinitions();
    void setupDependencyRelations();
    void setupWorkOffsetDependencies();

    // 计算方法
    void updateDependencyChain(OffsetType changedType, QSet<OffsetType>& processed);
    void calculateTotalBasicOffset();
    void calculateCurrentWorkOffset();
    void calculateTotalOffset();
    void calculateWorkCoordinate();

    // 工件坐标系同步方法
    void updateCurrentWorkOffsetToRealWCS(const PointXD& value, const PointXD& preciseValue);

    // 分组辅助方法
    int getGroupIdForOffsetType(OffsetType type) const;

    // 验证辅助方法
    bool isValidOffsetType(OffsetType type) const;
    bool isReadOnlyOffsetType(OffsetType type) const;
    bool isWithinValidRange(OffsetType type, const PointXD& value) const;

    // CNC系统接口辅助方法
    bool loadOffsetFromCnc(OffsetType type);
    bool saveOffsetToCnc(OffsetType type);
    void updateCncSystemCache();
    bool isCacheValid() const;

    // 文件操作辅助方法
    QString getAutoSaveFilePath() const;
    bool createBackupFile(const QString& originalPath) const;
    void cleanupOldBackups(const QString& dirPath, const QString& baseName, const QString& suffix) const;

    // 数据比较辅助方法
    PointXD calculateDifference(const PointXD& local, const PointXD& cnc) const;
    bool isZeroPoint(const PointXD& point) const;

    // 成员变量
    int m_channelId;                                     // 通道ID
    QMap<OffsetType, OffsetDataItem> m_offsetData;       // 所有零偏数据
    QMap<OffsetType, QList<OffsetType>> m_dependencies;  // 依赖关系映射
    OffsetType m_currentWorkOffset;                      // 当前工件坐标系

    // 状态管理
    bool m_hasUnsavedChanges;          // 是否有未保存的更改
    bool m_hasUnsavedFileChanges;      // 是否有未保存到文件的更改
    QStringList m_validationErrors;    // 验证错误列表
    QStringList m_validationWarnings;  // 验证警告列表

    // 定时器
    QTimer* m_syncTimer;           // 同步定时器
    QTimer* m_autoSaveTimer;       // 自动文件保存定时器
    bool m_syncInProgress;         // 同步进行中标志
    QDateTime m_lastAutoSaveTime;  // 最后自动保存时间

    // CNC系统缓存
    mutable QMap<OffsetType, PointXD> m_cncSystemCache;  // CNC系统缓存
    mutable QDateTime m_lastCacheUpdate;                 // 最后缓存更新时间

    // 工件坐标系名称访问器（通过回调函数避免重复存储）
    std::function<QString(OffsetType)> m_getWorkOffsetName;         // 获取工件坐标系名称的回调
    std::function<OffsetType(const QString&)> m_getWorkOffsetType;  // 获取工件坐标系类型的回调

    // 配置
    OffsetManagerConfig m_config;  // 通道特定的配置副本
};