DisableStep
Googoltech-CNC-SysMacroProgram
%
;程序名称：M06.NC
;程序作用：	三轴机刀库备刀功能(#20000),=0无刀库,=1钻攻太阳刀库，
;			=2加工中心交臂式刀库
;公司名称：	固高科技
;版本日期：	V6.0/2023/3/23
;版本信息：	双通道6.0
;维护人员：	YZJ/QB
N1(====外部变量 #1~#26 由宏程序调用者传入====)

(====局部变量 #1~#99 仅本程序内可使用====)
(====公共变量 #100~#499 全局可见,重启将被清除==)

(====全局变量自动保存变量 #20000~#21000 全局可见====)
;#20000-#20059	;刀库所有使用参数
;#20000-#20014	;刀库公用参数使用
	;#20000 		;刀库类型=0无刀库,=1钻攻太阳刀库,=2加工中心交臂式刀库(运动程序1)
	;#20001      	;刀库总刀数 
	;#20002   		;100~102初始化中
	;#20003 		;主轴当前刀号
	;#20004      	;刀库当前位置 
	;#20005   		;刀库备刀刀号	
	;#20006 		;X/Y轴移动启动	
	;#20007 		;换刀位置X坐标
	;#20008      	;换刀位置Y坐标
	;#20009   		;换刀位置Z坐标
	;#20010 		;换刀快移速度
	;#20011 		;换刀慢移速度
	;#20012 		;急停IO端口号
;#20015-#20059	;按照刀库类型写入参数
	;钻攻刀库(V0.0版本等待合并)#20000=1
		;#20015 	
		;#20016 
		;#20017 
		;#20018 
		;#20019 
		;·······
	;交臂式刀库(V6.0)#20000=2
		;#20015 		;刀库计数IO端口号
		;#20016  		;刀库零点IO端口号
		;#20017  		;刀库正转IO端口号
		;#20018  		;刀库反转IO端口号
		;#20019  		;机械臂零点IO端口号
		;#20020  		;机械臂刹车IO端口号	
		;#20021  		;机械臂正转IO端口号 
		;#20022  		;机械臂反转IO端口号 
		;#20023  		;刀库转动端口号 
		;#20024   		;刀库运动次数		
		;#20025 		;预计更新刀号		
		;#20026   		;预计更新刀位	
		;#20027 		;换刀后安全位置X		
		;#20028 		;换刀后安全位置Y	
		;#20029   		;换刀后安全位置Z	
;#20151-#200179	;刀库操作屏蔽
		;#20151			刀库异常状态：0正常，1刀库异常，2机械臂异常，3屏蔽所有异常
		;(因调试刀具时，存在IO问题，方便调试需要屏蔽，开机时如果=3会直接给0)
		;#20152			屏蔽刀库侧门检测  0：否  1：是
		;#20153			屏蔽刀库门开关指令 0：否  1：是
		;#20154 		开启运行时备刀完成刀套下 0：否  1：是
		;#20155 		屏蔽探针开启检测 0：否  1：是
		;#20179			;ATC刀库运行状态:0空闲
						;1正转备刀,2反转备刀,3备刀完成	
						;10刀库回零，11刀库回零完成,19刀库异常
						;20机械臂扣刀,21主轴松刀,22机械臂交换刀,23主轴夹刀,24机械臂回零,39机械臂异常
;#20200-#20299	;补偿-刀沿映射表
        ;#20200			;映射表大小
		;#20201			;H01对应的刀号+刀沿
		;#20202			;H02对应的刀号+刀沿
		;#20203			;H03对应的刀号+刀沿
		;·······
(====系统参数 #3000~#9999 全局可见====)
;@3563  ;加工T指令模态
;@2549	;加工T指令补偿
;#90	;译码T指令模态(目标刀号)
;#91    ;译码T指令模态(补偿号)
;@4560-@4599  ;机床坐标（轴关联规划器坐标）
;@4522-@4559  ;机床坐标（轴关联编码器坐标）
(@9999:设置宏程序自定义报警号)
;(	;1501-3000:重要报警)
;(	;5501-9999:提示信息)
;(	;其他值非法)
;(;@9998:清除或输出指定的报警)
;(	;0:清除@9998指定的报警)
;(	;1:输出@9998指定的报警)
(====系统扩展 #10000~#10999 全局可见====)
(====用户扩展 #11000~#27499 全局可见====)
(===========由PLC执行的M指令============)
;M05		;主轴停止
;M19		;主轴准停——————暂用M59进行准停功能
;M20		;主轴准停取消
;M88		;夹刀
;M89		;松刀
;M90		;松刀检测
;M91		;夹刀检测


;M150-M159	;交臂式刀库使用指令
;M150:检测刀库运动程序是否完成(检测时间50S)
;M151:刀库备刀参数(#20023,刀库转动端口号;#20024,刀库运动次数)写入
;M152:刀库备刀运动程序启动
;M153:刀库回零参数写入(刀库转动端口号,刀库运动次数,零点读取次数;(PLC内部赋值写入))
;M154:刀库回零运动程序启动
;M155:刀库零位检测
;M156:刀库备刀安全检测
;M157:刀盘下到位并检测
;M158:刀盘上到位并检测

;M179:刀库回零程序(临时配置为M代码，方便使用)
;M180:机械臂零位检测
;M181:机械臂扣刀检测
;M182:机械臂正转启动
;M183:机械臂反转启动
;M184:机械臂正转回零启动(正常使用为机械臂正反转)
;M185:机械臂反转回零启动(现阶段预留)
;M186:机械臂运动程序是否完成
;M187:刀库运动程序解除急停
;M188:刀库PLC备刀逻辑启动
;M189:刀库PLC备刀逻辑检测
;M190:检测侧门开启并等待
;M191:刀库门开启后结束,PLC5S延时检测
;M192:刀库门开启并检测
;M193:刀库门关启后结束,PLC5S延时检测
;M194:刀库门开启关检测
(===========N代码状态============)
;N10		;初始化
;N100		;钻攻机床-太阳刀库
;N200		;1165机床-交臂式刀库

N10(================初始化================)
G49					;取消模态
PAUSEc
#90=@3663			;TnM6是换刀译码@3663
#91=@2449			;TnM6是换刀补偿@2449
#92=#90*100+#91

;查询H映射表中的H号
#93=1
WHILE[#93 <= #20200]
	#94=#93+20200		;得到H号对应的变量
	IF[##94==#92]		;找到对应的H号
		GOTO 20
	END_IF
	IF[#93 >= 100]		;超出最大索引，因为#20200可能取值不对
		GOTO 9991		;刀库不存在此刀
	END_IF
	#93=#93+1
END_WHILE
GOTO 9991				;刀库不存在此刀
N20(================找到H号================)

IF [#90 == @3563] AND [#91 == @2549] ;主轴刀号与译码一致，直接结束
	IF[#20003==#26022 AND #20155<>1]
		M74					;关闭探头
		M73					;开启侧头
	END_IF
	GOTO 9999
END_IF
IF [#20000 <= 0] 	;不存在刀库更新刀库并结束
	@3563=#90		;换刀完成更新设置加工刀号
	@2525=#90		;换刀完成更新设置加工刀号
	#20003= @3563	;更新铁电主轴刀号
	G43 H#93 D#93	;选择H对应的刀补
	GOTO 9999
END_IF

IF [#20999 == 1] ;仿真判断时跳转结束
	GOTO 9999
END_IF
IF [#20000 == 1] 	;跳转钻攻太阳刀库,暂时屏蔽
	;GOTO 100	
END_IF
IF [#20000 == 2] 	;跳转1165交臂式刀库,暂时屏蔽
	GOTO 200	
END_IF
GOTO 9990	

N100(==============钻攻太阳刀库逻辑============)
GOTO 9999(正常结束)

N200(============1165交臂式刀库============)


N210(==============抬Z后移动X/Y===========)
IF[#20003==#26022 AND #20155<>1]	;当测头开启没有屏蔽且当前刀号为测头时候关闭测头
	M74				;关闭侧头
END_IF
IF[#20152<>1]				;屏蔽刀库侧门检测
	M190					;运动前检测侧门开启并等待
END_IF
IF[#20153<>1]				;屏蔽刀库门开关指令
	M191					;刀库门开启后结束,PLC5S延时检测
END_IF
G108K2V4P1
M50								;第一准停位置
M59
G53 Z#20029 F#20010				;抬Z到换刀安全位置
G4P100
PAUSEc
G102.2A1						;设置X轴第二软限位
G102.2A2						;设置Y轴第二软限位
G102.2A3						;设置Z轴第二软限位
IF[ABS[@4802] > 0.2]				;Z移动位置超差报警
	GOTO 9920
END_IF
;X/Y轴移动到位
IF[#20006==1]			
	G53 X#20007 Y#20008 F#20010	;移动XY位置
	PAUSEc
	IF[ABS[@4800] > 1]			
		GOTO 9900
	END_IF
	IF[ABS[@4801] > 1]			
		GOTO 9910
	END_IF
END_IF
G53 Z#20009 F#20010				;抬Z到换刀位置
G4P100
PAUSEc
IF[ABS[@4802] > 0.2]				;Z移动位置超差报警
	GOTO 9920
END_IF
M189					;刀库PLC备刀逻辑检测
IF[#20153<>1]				;屏蔽刀库门开关指令
	M192					;刀库门开启并检测
END_IF
PAUSEc
IF [#90 <> #20005]			;备刀刀号不等于译码刀号
	GOTO 9950
END_IF
IF [#90 == @3563] 
	GOTO 9999					;目标刀号与当前刀号一致，直接结束
END_IF
PAUSEc
G107
N220(=============机械臂换刀动作===========)
M157							;刀盘下到位并检测
G4.1P100
M180							;机械臂零位检测
M182							;机械臂正转启动
M186							;机械臂运动程序是否完成
PAUSEc
#20179=20						;状态记录：20机械臂扣刀
MSG=Tool Lib:Mechanical arm knife is completed
M181							;机械臂扣刀检测
G4.1P100
M89								;松刀
M90								;松刀检测
PAUSEc
#20179=21						;状态记录：21主轴松刀
MSG=Tool Lib:Spindle loose cutter complete
G4.1P100
M182							;机械臂正转启动
M186							;机械臂运动程序是否完成(5S)
PAUSEc
#20179=22						;状态记录：22机械臂交换刀
MSG=Tool Lib:Robotic arm exchange tool
M181							;机械臂扣刀检测
G4.1P100
M88								;夹刀
M91								;夹刀检测
PAUSEc
#20179=23						;状态记录：23主轴夹刀
MSG=Tool Lib:Spindle clamp to tool
G4.1P100
N230(================更新刀号=============)
PAUSEc
#31 = #20004+20030    			
##31=@3563 						;刀库到位刀号等于主轴刀号
#20005=@3563 					;刀库刀号等于主轴刀号

@3563 = #90       				;主轴刀号等于译码刀号
#20003= #90						;记录主轴刀号
@3574 = #20005					;更新备刀刀号
N240(================机械臂回零位动作=============)
M182							;机械臂正转启动(机械臂电机扣紧状态时使用M182)
M186							;检测刀库运动程序是否完成
PAUSEc
#20179=24						;状态记录：24机械臂回零
MSG=Tool Lib:Robotic arm back to zero
M180							;机械臂零位检测
M158							;刀盘拉回到位并检测
IF[#20153<>1]				;屏蔽刀库门开关指令
	M193							;刀库门关启后结束,PLC5S延时检测
END_IF
G53 Z#20029 F#20010				;抬Z到换刀位置
G4P100
PAUSEc
IF[ABS[@4802] > 0.2]				;Z移动位置超差报警
	GOTO 9920
END_IF
;X/Y轴移动到位
IF[#20006==1]			
	G53 X#20027 Y#20028 F#20010	;移动XY位置
	PAUSEc
	IF[ABS[@4800] > 1]			
		GOTO 9900
	END_IF
	IF[ABS[@4801] > 1]			
		GOTO 9910
	END_IF
END_IF
PAUSEc
#20179=0						;状态记录：0刀库空闲
MSG=Tool Lib:The tool change is complete
IF[#20003==#26022 AND #20155<>1]
	M73					;开启侧头
END_IF
G103.2A1				;恢复第一软件限位
G103.2A2				;恢复第一软件限位
G103.2A3				;恢复第一软件限位
IF[#20153<>1]				;屏蔽刀库门开关指令
	M194							;刀库门关启检测
END_IF

GOTO 9999(结束)
N9900(==========Module_90*异常结束========)
@9999 = 2330           			;X位置超差报警
@9998 = 1
GOTO 9999(结束)

N9910
@9999 = 2331           			;Y位置超差报警
@9998 = 1
GOTO 9999(结束)

N9920
@9999 = 2332           			;Z位置超差报警
@9998 = 1
GOTO 9999(结束)

N9921
@9999 = 2333           			;Z位置编码器位置超限报警
@9998 = 1
GOTO 9999(结束)

N9922
@9999 = 2334           			;Z位置规划器位置超限报警
@9998 = 1
GOTO 9999(结束)

N9930
@9999 = 2335           			;刀库刀号和主刀号不同，存在异常，请先回零
@9998 = 1
GOTO 9999(结束)

N9940
@9999 = 2336           			;自动换刀启动是Z轴不安全
@9998 = 1
GOTO 9999(结束)

N9950
@9999 = 2337           			;不能不备刀直接换刀
@9998 = 1
GOTO 9999(结束)

N9990
@9999=2323						;刀库类型错误，请设置正确的刀库类型
@9998=1
GOTO 9999(结束)

N9991
@9999=2320						;刀库不存在此刀
@9998=1
GOTO 9999(结束)

N9999(==========Module_99*正常结束========)
PAUSEc
M20					;准停取消
M99
