DisableStep
Googoltech-CNC-SysMacroProgram
%
;程序名称：	T.NC
;程序作用：	三轴机刀库备刀功能(#20000),=0无刀库,=1钻攻太阳刀库，
;			=2加工中心交臂式刀库
;公司名称：	固高科技
;版本日期：	V6.0/2023/3/23
;版本信息：	双通道6.0
;维护人员：	YZJ/QB
N1(====外部变量 #1~#26 由宏程序调用者传入====)

(====局部变量 #1~#99 仅本程序内可使用====)

;#90				;自动加工时为#90;跳段加工时由外部传入
(====公共变量 #100~#499 全局可见,重启将被清除==)

(====全局变量自动保存变量 #20000~#21000 全局可见====)
;#20000-#20059	;刀库所有使用参数
;#20000-#20014	;刀库公用参数使用
	;#20000 		;刀库类型
	;#20001      	;刀库总刀数 
	;#20002   		;100~102初始化中
	;#20003 		;主轴当前刀号
	;#20004      	;刀库当前位置 
	;#20005   		;刀库备刀刀号	
	;#20006 		;X/Y轴移动启动	
	;#20007 		;换刀位置X坐标
	;#20008      	;换刀位置Y坐标
	;#20009   		;换刀位置Z坐标
	;#20010 		;换刀快移速度
	;#20011 		;换刀慢移速度
	;#20012 		;急停IO端口号
;#20015-#20059	;按照刀库类型写入参数
	;钻攻刀库(V0.0版本等待合并)#20000=1
		;#20015 	
		;#20016 
		;#20017 
		;#20018 
		;#20019 
		;·······
	;交臂式刀库(V6.0)#20000=2
		;#20015 		;刀库计数IO端口号
		;#20016  		;刀库零点IO端口号
		;#20017  		;刀库正转IO端口号
		;#20018  		;刀库反转IO端口号
		;#20019  		;机械臂零点IO端口号
		;#20020  		;机械臂刹车IO端口号	
		;#20021  		;机械臂正转IO端口号 
		;#20022  		;机械臂反转IO端口号 
		;#20023  		;刀库转动端口号 
		;#20024   		;刀库运动次数		
		;#20025 		;预计更新刀号		
		;#20026   		;预计更新刀位	
		;#20027 		;换刀后安全位置X		
		;#20028 		;换刀后安全位置Y	
		;#20029   		;换刀后安全位置Z	
		;#20030   		;刀库位置记录成功状态位置，突然断电时为1		
		;#20031	;1号刀对应刀套号
		;#20032	;2号刀对应刀套号
		;·······
		;#20055	;25号刀对应刀套号
;#20151-#200179	;刀库操作屏蔽
		;#20151			刀库异常状态：0正常，1刀库异常，2机械臂异常，3屏蔽所有异常
		;(因调试刀具时，存在IO问题，方便调试需要屏蔽，开机时如果=3会直接给0)
		;#20152			屏蔽刀库侧门检测  0：否  1：是
		;#20153			屏蔽刀库门开关指令 0：否  1：是
		;#20154 		开启运行时备刀完成刀套下 0：否  1：是
		;#20155 		屏蔽探针开启检测 0：否  1：是
		;#20179			;ATC刀库运行状态:0空闲
						;1正转备刀,2反转备刀,3备刀完成	
						;10刀库回零，11刀库回零完成,19刀库异常
						;20机械臂扣刀,21主轴松刀,22机械臂交换刀,23主轴夹刀,24机械臂回零,39机械臂异常
;#20200-#20299	;补偿-刀沿映射表
        ;#20200			;映射表大小
		;#20201			;H01对应的刀号+刀沿
		;#20202			;H02对应的刀号+刀沿
		;#20203			;H03对应的刀号+刀沿
		;·······
(====系统参数 #3000~#9999 全局可见====)
;@3563  ;译码加工T指令模态
;@3567  ;译码备刀T指令模态，存在T2
;@2450  ;译码备刀T补偿号
;#90	;译码T指令模态(目标刀号)
;#91    ;译码T指令模态(补偿号)
;@4560-@4599  ;机床坐标（轴关联规划器坐标）
;@4522-@4559  ;机床坐标（轴关联编码器坐标）
(@9999:设置宏程序自定义报警号)
;(	;1501-3000:重要报警)
;(	;5501-9999:提示信息)
;(	;其他值非法)
;(;@9998:清除或输出指定的报警)
;(	;0:清除@9998指定的报警)
;(	;1:输出@9998指定的报警)
(====系统扩展 #10000~#10999 全局可见====)
(====用户扩展 #11000~#27499 全局可见====)
(===========由PLC执行的M指令============)
;M05		;主轴停止
;M19		;主轴准停——————暂用M59进行准停功能
;M20		;主轴准停取消
;M88		;夹刀
;M89		;松刀
;M90		;松刀检测
;M91		;夹刀检测


;M150-M159	;交臂式刀库使用指令
;M150:检测刀库运动程序是否完成(检测时间50S)
;M151:刀库备刀参数(#20023,刀库转动端口号;#20024,刀库运动次数)写入
;M152:刀库备刀运动程序启动
;M153:刀库回零参数写入(刀库转动端口号,刀库运动次数,零点读取次数;(PLC内部赋值写入))
;M154:刀库回零运动程序启动
;M155:刀库零位检测
;M156:刀库备刀安全检测
;M157:刀盘下到位并检测
;M158:刀盘上到位并检测

;M179:刀库回零程序(临时配置为M代码，方便使用)
;M180:机械臂零位检测
;M181:机械臂扣刀检测
;M182:机械臂正转启动
;M183:机械臂反转启动
;M184:机械臂正转回零启动(正常使用为机械臂正反转)
;M185:机械臂反转回零启动(现阶段预留)
;M186:机械臂运动程序是否完成(5S)
;M187:刀库运动程序解除急停
;M188:刀库PLC备刀逻辑启动
;M189:刀库PLC备刀逻辑检测
;M190:检测侧门开启并等待
;M191:刀库门开启后结束,PLC5S延时检测
;M192:刀库门开启并检测
;M193:刀库门关启后结束,PLC5S延时检测
;M194:刀库门开启关检测
(===========N代码状态============)
;N10		;初始化
;N100		;钻攻机床-太阳刀库
;N200		;1165机床-交臂式刀库

N10(================初始化================)
PAUSEc
#90=@3667			;Tn是备刀译码@3667
#91=@2450			;Tn备刀补偿号@2450

IF [#20999 == 1] ;仿真判断时跳转结束
	GOTO 9999
END_IF
IF [#90 == @3563] AND [#91 == @2549] ;主轴刀号与备刀译码刀号一致，直接结束
	GOTO 9999	
END_IF
IF [#20000 <= 0] 	;不存在刀库直接结束
	GOTO 99	
END_IF
IF [#20000 == 1] 	;跳转钻攻太阳刀库,暂时屏蔽
	;GOTO 100	
END_IF
IF [#20000 == 2] 	;跳转1165交臂式刀库
	GOTO 200	
END_IF
GOTO 9990	

N99(==============云川默认刀库逻辑============)
;H映射表查询
MSG=Check-Tool-Id:#90
MSG=Check-Tool-Compensation:#91
#92=#90*100+#91
#60=1
WHILE[#60 <= #20200]
	#61=#60+20200			;得到H号对应的变量
	IF[##61==#92]			;判断是否存在
		@3574=#90			;更新备刀译码
		@2526=#90			;更新备刀译码
		IF [#90 == @3563]	;主轴刀号与备刀译码刀号一致不会调用M06宏
			G43 H#60 D#60	;选择H对应的刀补
		END_IF
		GOTO 9999(正常结束)
	END_IF
	IF[#60 >= 100]			;超出最大索引，因为#20200可能取值不对
		GOTO 9910			;刀库不存在此刀
	END_IF
	#60=#60+1
END_WHILE
GOTO 9910(不存在此刀)

N100(==============钻攻太阳刀库逻辑============)
GOTO 9999(正常结束)

N200(==============1165交臂式刀库============)
IF[#20152<>1]				;屏蔽刀库侧门检测
	M190					;运动前检测侧门开启并等待
END_IF
PAUSEc
#35= #20001+1;			;刀库总刀数
IF [ [#90 < 1] OR [#90 > #35] ]		;刀号小于1，刀号大于25
	GOTO 9910
END_IF
M150					;检测刀库运动程序是否完成(检测时间50S)
M189					;检测刀库备刀是否完成
PAUSEc
;查询刀库里面的刀号
MSG=Next-Tool-Id:#90
#60=1
WHILE[#60 <= #20001]
	#61=#60+20030		;得到刀号对应的刀套号
	IF[##61==#90]
		#31=#60			;保存刀套号
		IF [#31<1]				;刀套号异常输出报警
			GOTO 9980
		END_IF
		IF [#31>#20001]			;刀套号异常输出报警
			GOTO 9980
		END_IF	
		GOTO 210
	ELSE
		#60=#60+1	
	END_IF
END_WHILE
;运行到此位置没有查询到刀号，需要报警
GOTO 9980
N210(==============1165交臂式刀库计算运动位置============)
#20024=#31-#20004		;需要移动的次数	
IF[#20024==0]			;不需要移动，检测安全更新备刀刀号结束
	PAUSEc
	#20025=#90						;保存预计更新刀号
    #20026=#31						;保存预计更新到位
	M188                            ;刀库PLC备刀逻辑启动
	MSG=Tool Lib:No need for a Tool
	MSG=Expected-Next-Tool-Id:#90
	MSG=Expected-Next-Tool-Pos:#31	
	GOTO 9999
END_IF
	
PAUSEc
#20023=#20017			;默认正向移动
IF [#20024<0]			;移动距离小于零的话，转换为正向移动
	#20024=#20024+#20001
END_IF
IF [#20024>#20001/2]	;大于12时候转化为反向移动小于12的距离
	#20023=#20018
	#20024=#20001-#20024
END_IF
#20024=#20024*2;
IF[#20024<=0]
	GOTO 9970
END_IF

N220(==============1165交臂式刀库计算启动备刀功能============)
;记录预计更新刀号
PAUSEc
#20025=#90						;保存预计更新刀号
#20026=#31						;保存预计更新到位                            
 
M158					;刀盘上到位并检测
G4P200					;200ms确保拉回到位
M151					;刀库备刀参数(#20023,刀库转动端口号;#20024,刀库运动次数)写入
M152					;刀库备刀运动程序启动
M188					;刀库PLC备刀逻辑启动

;备刀状态记录
PAUSEc
IF[#20023==#20017]
	;正转备刀记录
	#20179=1		;状态记录：1正转备刀记录
	MSG=Tool Lib:Turn the Tool to start
	MSG=Expected-Next-Tool-Id:#90
	MSG=Expected-Next-Tool-Pos:#31
ELSE
	;反转备刀记录
	#20179=2		;状态记录：2反转备刀记录
	MSG=Tool Lib:Reverse the Tool to start	
	MSG=Expected-Next-Tool-Id:#90
	MSG=Expected-Next-Tool-Pos:#31
END_IF
GOTO 9999(正常结束)

N9900(==========Module_90*异常结束========)
N9910
@9999=2320			;刀库不存在此刀，报警输出
@9998=1
GOTO 9999(结束)

N9970
@9999=2321			;刀库位置计算错误
@9998=1
GOTO 9999(结束)

N9980
@9999=2322			;刀库备刀刀套号错误
@9998=1
GOTO 9999(结束)

N9990
@9999=2323			;刀库类型错误，请设置正确的刀库类型
@9998=1
GOTO 9999(结束)

N9999(==========Module_99*正常结束========)
PAUSEc
M99


